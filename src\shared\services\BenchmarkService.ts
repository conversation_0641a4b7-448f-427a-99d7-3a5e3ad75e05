/**
 * 性能基准测试服务
 * 提供性能基准的建立、测试、比较和报告功能
 */

import {
  PerformanceBenchmark,
  ExtendedWebVitals,
  ComponentPerformanceMetrics,
  PerformanceRegression,
} from '@/core/types/performance';
import { performanceLogger } from '../../core/lib/logger';

interface BenchmarkTestResult {
  benchmark: PerformanceBenchmark;
  currentMetrics: {
    webVitals: ExtendedWebVitals;
    componentMetrics: ComponentPerformanceMetrics[];
  };
  comparison: {
    webVitalsComparison?: Record<string, number>;
    componentComparison: Record<string, number>;
    overallScore: number;
    regressions: PerformanceRegression[];
  };
  recommendations: string[];
}

interface BenchmarkConfig {
  iterations: number;
  warmupRuns: number;
  cooldownTime: number; // ms
  includeWebVitals: boolean;
  includeComponentMetrics: boolean;
  environment: {
    device: 'desktop' | 'mobile' | 'tablet';
    network: '4g' | '3g' | 'wifi' | 'offline';
    cpu: 'high' | 'medium' | 'low';
  };
}

class BenchmarkService {
  private static instance: BenchmarkService;
  private benchmarks: Map<string, PerformanceBenchmark> = new Map();
  private isRunning = false;

  static getInstance(): BenchmarkService {
    if (!BenchmarkService.instance) {
      BenchmarkService.instance = new BenchmarkService();
    }
    return BenchmarkService.instance;
  }

  // ==================== 基准管理 ====================

  /**
   * 创建新的性能基准
   */
  async createBenchmark(
    name: string,
    description: string,
    config: BenchmarkConfig
  ): Promise<PerformanceBenchmark> {
    if (this.isRunning) {
      throw new Error('Another benchmark is currently running');
    }

    this.isRunning = true;
    performanceLogger.info('Creating performance benchmark', { name, config });

    try {
      // 运行基准测试
      const baselineMetrics = await this.runBaselineTest(config);

      const benchmark: PerformanceBenchmark = {
        id: `benchmark-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name,
        description,
        baseline: baselineMetrics,
        thresholds: this.calculateThresholds(baselineMetrics),
        environment: config.environment,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      this.benchmarks.set(benchmark.id, benchmark);

      performanceLogger.info('Benchmark created successfully', {
        benchmarkId: benchmark.id,
        baseline: baselineMetrics,
      });

      return benchmark;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 运行基准测试
   */
  async runBenchmarkTest(
    benchmarkId: string,
    config?: Partial<BenchmarkConfig>
  ): Promise<BenchmarkTestResult> {
    const benchmark = this.benchmarks.get(benchmarkId);
    if (!benchmark) {
      throw new Error(`Benchmark with id ${benchmarkId} not found`);
    }

    if (this.isRunning) {
      throw new Error('Another benchmark is currently running');
    }

    this.isRunning = true;
    performanceLogger.info('Running benchmark test', { benchmarkId });

    try {
      const testConfig: BenchmarkConfig = {
        iterations: 5,
        warmupRuns: 2,
        cooldownTime: 1000,
        includeWebVitals: true,
        includeComponentMetrics: true,
        environment: benchmark.environment,
        ...config,
      };

      // 收集当前性能指标
      const currentMetrics = await this.collectCurrentMetrics(testConfig);

      // 与基准进行比较
      const comparison = this.compareWithBaseline(benchmark, currentMetrics);

      // 生成建议
      const recommendations = this.generateRecommendations(comparison);

      const result: BenchmarkTestResult = {
        benchmark,
        currentMetrics,
        comparison,
        recommendations,
      };

      performanceLogger.info('Benchmark test completed', {
        benchmarkId,
        overallScore: comparison.overallScore,
        regressions: comparison.regressions.length,
      });

      return result;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 获取所有基准
   */
  getAllBenchmarks(): PerformanceBenchmark[] {
    return Array.from(this.benchmarks.values());
  }

  /**
   * 获取特定基准
   */
  getBenchmark(id: string): PerformanceBenchmark | undefined {
    return this.benchmarks.get(id);
  }

  /**
   * 删除基准
   */
  deleteBenchmark(id: string): boolean {
    return this.benchmarks.delete(id);
  }

  /**
   * 更新基准
   */
  async updateBenchmark(
    id: string,
    updates: Partial<PerformanceBenchmark>
  ): Promise<PerformanceBenchmark | null> {
    const benchmark = this.benchmarks.get(id);
    if (!benchmark) {
      return null;
    }

    const updatedBenchmark = {
      ...benchmark,
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    this.benchmarks.set(id, updatedBenchmark);
    return updatedBenchmark;
  }

  // ==================== 私有方法 ====================

  /**
   * 运行基线测试
   */
  private async runBaselineTest(
    config: BenchmarkConfig
  ): Promise<PerformanceBenchmark['baseline']> {
    const results = [];

    // 预热运行
    for (let i = 0; i < config.warmupRuns; i++) {
      await this.runSingleTest(config);
      await this.wait(config.cooldownTime);
    }

    // 正式测试
    for (let i = 0; i < config.iterations; i++) {
      const result = await this.runSingleTest(config);
      results.push(result);

      if (i < config.iterations - 1) {
        await this.wait(config.cooldownTime);
      }
    }

    // 计算平均值
    return this.calculateAverageMetrics(results);
  }

  /**
   * 运行单次测试
   */
  private async runSingleTest(_config: BenchmarkConfig): Promise<any> {
    const startTime = performance.now();

    // 模拟页面加载和渲染
    await this.simulatePageLoad();

    const renderTime = performance.now() - startTime;

    // 收集内存使用情况
    let memoryUsage = 0;
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024);
    }

    // 模拟bundle大小检测
    const bundleSize = await this.estimateBundleSize();

    // 模拟加载时间
    const loadTime = renderTime + Math.random() * 500; // 添加一些随机性

    return {
      renderTime,
      memoryUsage,
      bundleSize,
      loadTime,
    };
  }

  /**
   * 模拟页面加载
   */
  private async simulatePageLoad(): Promise<void> {
    return new Promise(resolve => {
      // 使用更安全的方式模拟页面加载，避免DOM操作错误
      try {
        // 创建一个临时的DocumentFragment，避免直接操作DOM
        const fragment = document.createDocumentFragment();
        const div = document.createElement('div');
        div.innerHTML = '<div>'.repeat(50) + 'Test Content' + '</div>'.repeat(50);
        div.style.position = 'absolute';
        div.style.left = '-9999px';
        div.style.visibility = 'hidden';
        div.style.pointerEvents = 'none';

        // 添加到fragment而不是直接添加到body
        fragment.appendChild(div);

        // 使用setTimeout代替requestAnimationFrame，更可靠
        setTimeout(() => {
          try {
            // 清理fragment中的内容
            while (fragment.firstChild) {
              fragment.removeChild(fragment.firstChild);
            }
          } catch (error) {
            // 静默处理清理错误
            console.debug(
              'Fragment cleanup failed:',
              error instanceof Error ? error.message : String(error)
            );
          } finally {
            resolve();
          }
        }, 10); // 短暂延迟模拟渲染时间
      } catch (error) {
        console.debug(
          'simulatePageLoad failed:',
          error instanceof Error ? error.message : String(error)
        );
        resolve();
      }
    });
  }

  /**
   * 估算bundle大小
   */
  private async estimateBundleSize(): Promise<number> {
    // 简化的bundle大小估算
    const scripts = document.querySelectorAll('script[src]');
    let totalSize = 0;

    for (const script of scripts) {
      try {
        const response = await fetch((script as HTMLScriptElement).src, { method: 'HEAD' });
        const contentLength = response.headers.get('content-length');
        if (contentLength) {
          totalSize += parseInt(contentLength, 10);
        }
      } catch {
        // 忽略错误，使用估算值
        totalSize += 50000; // 50KB 估算
      }
    }

    return Math.round(totalSize / 1024); // 返回KB
  }

  /**
   * 计算平均指标
   */
  private calculateAverageMetrics(results: any[]): PerformanceBenchmark['baseline'] {
    const avg = (arr: number[]) => arr.reduce((a, b) => a + b, 0) / arr.length;

    return {
      renderTime: avg(results.map(r => r.renderTime)),
      memoryUsage: avg(results.map(r => r.memoryUsage)),
      bundleSize: avg(results.map(r => r.bundleSize)),
      loadTime: avg(results.map(r => r.loadTime)),
    };
  }

  /**
   * 计算性能阈值
   */
  private calculateThresholds(
    baseline: PerformanceBenchmark['baseline']
  ): PerformanceBenchmark['thresholds'] {
    return {
      excellent: baseline.renderTime * 0.8,
      good: baseline.renderTime,
      needsImprovement: baseline.renderTime * 1.5,
      poor: baseline.renderTime * 2.5,
    };
  }

  /**
   * 收集当前性能指标
   */
  private async collectCurrentMetrics(
    _config: BenchmarkConfig
  ): Promise<BenchmarkTestResult['currentMetrics']> {
    // 这里应该集成实际的性能数据收集
    // 暂时返回模拟数据
    return {
      webVitals: {
        fcp: 1200,
        lcp: 2500,
        fid: 50,
        cls: 0.1,
        inp: 80,
        ttfb: 300,
        fmp: 1500,
        tti: 3000,
        tbt: 200,
        si: 2000,
        domContentLoaded: 1800,
        loadComplete: 3500,
        resourceLoadTime: 1000,
        connectionType: 'wifi',
        effectiveType: '4g',
        downlink: 10,
        rtt: 50,
        timestamp: Date.now(),
        url: window.location.href,
      },
      componentMetrics: [],
    };
  }

  /**
   * 与基准进行比较
   */
  private compareWithBaseline(
    _benchmark: PerformanceBenchmark,
    currentMetrics: BenchmarkTestResult['currentMetrics']
  ): BenchmarkTestResult['comparison'] {
    const webVitalsComparison: Record<string, number> | undefined = {};
    const componentComparison: Record<string, number> = {};
    const regressions: PerformanceRegression[] = [];

    // Web Vitals 比较
    const vitals = currentMetrics.webVitals;
    webVitalsComparison['fcp'] = ((vitals.fcp - 1800) / 1800) * 100; // 假设基准FCP为1800ms
    webVitalsComparison['lcp'] = ((vitals.lcp - 2500) / 2500) * 100;
    webVitalsComparison['fid'] = ((vitals.fid - 100) / 100) * 100;
    webVitalsComparison['cls'] = ((vitals.cls - 0.1) / 0.1) * 100;

    // 检测回归
    Object.entries(webVitalsComparison).forEach(([metric, change]) => {
      if (change > 20) {
        // 超过20%的性能下降
        regressions.push({
          metric,
          baseline: 0, // 需要实际基准值
          current: 0, // 需要实际当前值
          regression: change,
          detectedAt: new Date().toISOString(),
          severity: change > 50 ? 'critical' : change > 30 ? 'major' : 'minor',
          possibleCauses: [
            '代码变更导致性能下降',
            '资源加载时间增加',
            '第三方库更新',
            '网络环境变化',
          ],
        });
      }
    });

    // 计算总体评分
    const scores = Object.values(webVitalsComparison);
    const overallScore = Math.max(
      0,
      100 - scores.reduce((a, b) => a + Math.abs(b), 0) / scores.length
    );

    return {
      webVitalsComparison,
      componentComparison,
      overallScore,
      regressions,
    };
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(comparison: BenchmarkTestResult['comparison']): string[] {
    const recommendations: string[] = [];

    if (comparison.overallScore < 70) {
      recommendations.push('整体性能需要优化，建议进行全面的性能审查');
    }

    if (comparison.webVitalsComparison?.['fcp'] && comparison.webVitalsComparison['fcp'] > 10) {
      recommendations.push('首次内容绘制时间过长，考虑优化关键渲染路径');
    }

    if (comparison.webVitalsComparison?.['lcp'] && comparison.webVitalsComparison['lcp'] > 10) {
      recommendations.push('最大内容绘制时间过长，优化主要内容的加载速度');
    }

    if (comparison.webVitalsComparison?.['fid'] && comparison.webVitalsComparison['fid'] > 10) {
      recommendations.push('首次输入延迟过长，减少主线程阻塞时间');
    }

    if (comparison.webVitalsComparison?.['cls'] && comparison.webVitalsComparison['cls'] > 10) {
      recommendations.push('累积布局偏移过大，确保元素尺寸稳定');
    }

    if (comparison.regressions.length > 0) {
      recommendations.push(`检测到 ${comparison.regressions.length} 个性能回归，需要立即处理`);
    }

    return recommendations;
  }

  /**
   * 等待指定时间
   */
  private wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 全局实例
export const benchmarkService = BenchmarkService.getInstance();

// 便捷方法
export const createPerformanceBenchmark = (
  name: string,
  description: string,
  config: BenchmarkConfig
) => {
  return benchmarkService.createBenchmark(name, description, config);
};

export const runBenchmarkTest = (benchmarkId: string, config?: Partial<BenchmarkConfig>) => {
  return benchmarkService.runBenchmarkTest(benchmarkId, config);
};

export const getAllBenchmarks = () => {
  return benchmarkService.getAllBenchmarks();
};
