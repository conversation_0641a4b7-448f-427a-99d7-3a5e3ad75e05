/**
 * 配比验证器
 * 处理配比数据的验证逻辑
 */

import type {
  RatioModel,
  RatioMaterialModel,
  RatioValidationResult,
  ValidationError,
  ValidationWarning,
} from '@/models/RatioModel';

export interface IRatioValidator {
  // 完整配比验证
  validate(ratio: RatioModel): Promise<RatioValidationResult>;

  // 分项验证
  validateBasicInfo(ratio: RatioModel): ValidationResult;
  validateCalculationParams(ratio: RatioModel): ValidationResult;
  validateMaterials(materials: RatioMaterialModel[]): ValidationResult;
  validateCalculationResults(ratio: RatioModel): ValidationResult;

  // 业务规则验证
  validateBusinessRules(ratio: RatioModel): ValidationResult;
  validateCompatibility(materials: RatioMaterialModel[]): ValidationResult;
  validateStandards(ratio: RatioModel): ValidationResult;

  // 质量验证
  validateQuality(ratio: RatioModel): ValidationResult;
  validateSafety(ratio: RatioModel): ValidationResult;
  validateEnvironmental(ratio: RatioModel): ValidationResult;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  score: number;
  details?: ValidationDetails;
}

export interface ValidationDetails {
  checkedRules: string[];
  passedRules: string[];
  failedRules: string[];
  skippedRules: string[];
  executionTime: number; // ms
}

export interface ValidationRule {
  id: string;
  name: string;
  description: string;
  category: ValidationCategory;
  severity: 'error' | 'warning' | 'info';
  enabled: boolean;
  execute(ratio: RatioModel): ValidationRuleResult;
}

export interface ValidationRuleResult {
  passed: boolean;
  message?: string;
  details?: any;
  suggestion?: string;
  affectedFields?: string[];
}

export type ValidationCategory =
  | 'basic_info'
  | 'calculation_params'
  | 'materials'
  | 'calculation_results'
  | 'business_rules'
  | 'compatibility'
  | 'standards'
  | 'quality'
  | 'safety'
  | 'environmental';

export class RatioValidator implements IRatioValidator {
  private rules: Map<string, ValidationRule> = new Map();

  constructor(private logger: ILogger) {
    this.initializeRules();
  }

  async validate(ratio: RatioModel): Promise<RatioValidationResult> {
    const startTime = Date.now();
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const checkedRules: string[] = [];
    const passedRules: string[] = [];
    const failedRules: string[] = [];

    try {
      // 执行所有验证规则
      for (const [ruleId, rule] of this.rules) {
        if (!rule.enabled) continue;

        checkedRules.push(ruleId);

        try {
          const result = rule.execute(ratio);

          if (result.passed) {
            passedRules.push(ruleId);
          } else {
            failedRules.push(ruleId);

            if (rule.severity === 'error') {
              errors.push({
                field: result.affectedFields?.[0] || '',
                message: result.message || rule.description,
                severity: 'error',
                code: ruleId,
              });
            } else if (rule.severity === 'warning') {
              warnings.push({
                field: result.affectedFields?.[0] || '',
                message: result.message || rule.description,
                suggestion: result.suggestion,
                code: ruleId,
              });
            }
          }
        } catch (error) {
          this.logger.error(`Validation rule ${ruleId} failed`, error as Error, {
            ratioId: ratio.id,
          });
          errors.push({
            field: 'system',
            message: `验证规则 ${rule.name} 执行失败`,
            severity: 'error',
            code: 'RULE_EXECUTION_FAILED',
          });
        }
      }

      // 计算质量评分
      const totalRules = checkedRules.length;
      const passedCount = passedRules.length;
      const score = totalRules > 0 ? Math.round((passedCount / totalRules) * 100) : 0;

      const executionTime = Date.now() - startTime;

      this.logger.info('Ratio validation completed', {
        ratioId: ratio.id,
        score,
        errorsCount: errors.length,
        warningsCount: warnings.length,
        executionTime,
      });

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        score,
      };
    } catch (error) {
      this.logger.error('Ratio validation failed', error as Error, { ratioId: ratio.id });
      throw error;
    }
  }

  validateBasicInfo(ratio: RatioModel): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 验证基本信息
    if (!ratio.taskInfo.taskNumber) {
      errors.push({
        field: 'taskInfo.taskNumber',
        message: '任务编号不能为空',
        severity: 'error',
        code: 'MISSING_TASK_NUMBER',
      });
    }

    if (!ratio.taskInfo.projectName) {
      errors.push({
        field: 'taskInfo.projectName',
        message: '工程名称不能为空',
        severity: 'error',
        code: 'MISSING_PROJECT_NAME',
      });
    }

    if (!ratio.taskInfo.strengthGrade) {
      errors.push({
        field: 'taskInfo.strengthGrade',
        message: '强度等级不能为空',
        severity: 'error',
        code: 'MISSING_STRENGTH_GRADE',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: errors.length === 0 ? 100 : 0,
    };
  }

  validateCalculationParams(ratio: RatioModel): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const params = ratio.calculationParams;

    // 验证目标强度
    if (params.targetStrength < 10 || params.targetStrength > 100) {
      errors.push({
        field: 'calculationParams.targetStrength',
        message: '目标强度应在10-100MPa范围内',
        severity: 'error',
        code: 'INVALID_TARGET_STRENGTH',
      });
    }

    // 验证坍落度
    if (params.slump < 10 || params.slump > 250) {
      errors.push({
        field: 'calculationParams.slump',
        message: '坍落度应在10-250mm范围内',
        severity: 'error',
        code: 'INVALID_SLUMP',
      });
    }

    // 验证水胶比
    if (params.waterCementRatio < 0.2 || params.waterCementRatio > 0.8) {
      errors.push({
        field: 'calculationParams.waterCementRatio',
        message: '水胶比应在0.2-0.8范围内',
        severity: 'error',
        code: 'INVALID_WATER_CEMENT_RATIO',
      });
    }

    // 验证砂率
    if (params.sandRatio < 20 || params.sandRatio > 50) {
      warnings.push({
        field: 'calculationParams.sandRatio',
        message: '砂率建议在20%-50%范围内',
        suggestion: '调整砂率以获得更好的工作性',
        code: 'SAND_RATIO_WARNING',
      });
    }

    // 验证密度
    if (params.density < 2000 || params.density > 2800) {
      warnings.push({
        field: 'calculationParams.density',
        message: '混凝土密度异常',
        suggestion: '检查材料配比是否合理',
        code: 'DENSITY_WARNING',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: errors.length === 0 ? 100 : Math.max(0, 100 - errors.length * 20),
    };
  }

  validateMaterials(materials: RatioMaterialModel[]): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    if (materials.length === 0) {
      errors.push({
        field: 'materials',
        message: '材料列表不能为空',
        severity: 'error',
        code: 'NO_MATERIALS',
      });
      return { isValid: false, errors, warnings, score: 0 };
    }

    // 检查必需材料
    const requiredMaterials = ['水', '水泥'];
    const materialNames = materials.map(m => m.name);

    for (const required of requiredMaterials) {
      if (!materialNames.includes(required)) {
        errors.push({
          field: 'materials',
          message: `缺少必需材料: ${required}`,
          severity: 'error',
          code: 'MISSING_REQUIRED_MATERIAL',
        });
      }
    }

    // 验证材料用量
    for (const material of materials) {
      if (material.theoreticalAmount <= 0) {
        errors.push({
          field: `materials.${material.id}.theoreticalAmount`,
          message: `${material.name}的理论用量必须大于0`,
          severity: 'error',
          code: 'INVALID_MATERIAL_AMOUNT',
        });
      }

      if (material.waterContent < 0 || material.waterContent > 20) {
        warnings.push({
          field: `materials.${material.id}.waterContent`,
          message: `${material.name}的含水率异常`,
          suggestion: '检查含水率是否正确',
          code: 'WATER_CONTENT_WARNING',
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: errors.length === 0 ? 100 : Math.max(0, 100 - errors.length * 15),
    };
  }

  validateCalculationResults(ratio: RatioModel): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const results = ratio.calculationResults;

    if (!results) {
      warnings.push({
        field: 'calculationResults',
        message: '缺少计算结果',
        suggestion: '请先进行配比计算',
        code: 'MISSING_CALCULATION_RESULTS',
      });
      return { isValid: true, errors, warnings, score: 50 };
    }

    // 验证强度预测
    if (results.strengthPrediction < ratio.calculationParams.targetStrength * 0.8) {
      warnings.push({
        field: 'calculationResults.strengthPrediction',
        message: '预测强度低于目标强度',
        suggestion: '调整配比参数以提高强度',
        code: 'LOW_STRENGTH_PREDICTION',
      });
    }

    // 验证质量评分
    if (results.qualityScore < 60) {
      warnings.push({
        field: 'calculationResults.qualityScore',
        message: '配比质量评分较低',
        suggestion: '优化配比以提高质量',
        code: 'LOW_QUALITY_SCORE',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: Math.max(60, 100 - warnings.length * 10),
    };
  }

  validateBusinessRules(ratio: RatioModel): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 验证状态转换
    if (ratio.metadata.status === 'approved' && !ratio.metadata.approvedBy) {
      errors.push({
        field: 'metadata.approvedBy',
        message: '已审核的配比必须有审核人',
        severity: 'error',
        code: 'MISSING_APPROVER',
      });
    }

    // 验证版本号
    if (ratio.version < 1) {
      errors.push({
        field: 'version',
        message: '版本号必须大于等于1',
        severity: 'error',
        code: 'INVALID_VERSION',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: errors.length === 0 ? 100 : 0,
    };
  }

  validateCompatibility(materials: RatioMaterialModel[]): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 检查材料兼容性
    // 这里需要实现具体的兼容性检查逻辑

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: 100,
    };
  }

  validateStandards(ratio: RatioModel): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 检查是否符合相关标准
    // 这里需要实现具体的标准检查逻辑

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: 100,
    };
  }

  validateQuality(ratio: RatioModel): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 质量检查
    if (ratio.calculationResults?.qualityScore && ratio.calculationResults.qualityScore < 70) {
      warnings.push({
        field: 'calculationResults.qualityScore',
        message: '配比质量评分偏低',
        suggestion: '考虑优化配比以提高质量',
        code: 'QUALITY_CONCERN',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: warnings.length === 0 ? 100 : 80,
    };
  }

  validateSafety(ratio: RatioModel): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 安全性检查
    // 这里需要实现具体的安全性检查逻辑

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: 100,
    };
  }

  validateEnvironmental(ratio: RatioModel): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 环保检查
    if (
      ratio.calculationResults?.carbonFootprint &&
      ratio.calculationResults.carbonFootprint > 400
    ) {
      warnings.push({
        field: 'calculationResults.carbonFootprint',
        message: '碳足迹较高',
        suggestion: '考虑使用更环保的材料',
        code: 'HIGH_CARBON_FOOTPRINT',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: warnings.length === 0 ? 100 : 90,
    };
  }

  private initializeRules(): void {
    // 初始化验证规则
    // 这里可以从配置文件或数据库加载规则

    this.rules.set('BASIC_INFO_COMPLETE', {
      id: 'BASIC_INFO_COMPLETE',
      name: '基本信息完整性',
      description: '检查基本信息是否完整',
      category: 'basic_info',
      severity: 'error',
      enabled: true,
      execute: ratio => {
        const missing = [];
        if (!ratio.taskInfo.taskNumber) missing.push('任务编号');
        if (!ratio.taskInfo.projectName) missing.push('工程名称');
        if (!ratio.taskInfo.strengthGrade) missing.push('强度等级');

        return {
          passed: missing.length === 0,
          message: missing.length > 0 ? `缺少: ${missing.join(', ')}` : undefined,
          affectedFields: missing.map(field => `taskInfo.${field}`),
        };
      },
    });

    // 添加更多规则...
  }
}

// 依赖接口
interface ILogger {
  debug(message: string, meta?: any): void;
  info(message: string, meta?: any): void;
  error(message: string, error: Error, meta?: any): void;
}
