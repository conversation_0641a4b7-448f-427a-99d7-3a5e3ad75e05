/**
 * 材料数据模型
 * 统一的材料数据结构，用于数据访问层
 */

export interface MaterialModel {
  id: string;
  name: string;
  category: MaterialCategory;
  type: MaterialType;

  // 基本属性
  specification: string;
  grade?: string;
  brand?: string;
  supplier: SupplierInfo;

  // 物理属性
  physicalProperties: {
    density: number; // kg/m³
    specificGravity: number; // 比重
    absorptionRate?: number; // 吸水率 %
    fineness?: number; // 细度
    moistureContent?: number; // 含水率 %
    bulkDensity?: number; // 堆积密度 kg/m³
    voidRatio?: number; // 空隙率 %
  };

  // 化学成分
  chemicalComposition?: {
    SiO2?: number;
    Al2O3?: number;
    Fe2O3?: number;
    CaO?: number;
    MgO?: number;
    SO3?: number;
    Na2O?: number;
    K2O?: number;
    Cl?: number;
    LOI?: number; // 烧失量
  };

  // 力学性能
  mechanicalProperties?: {
    compressiveStrength?: number; // 抗压强度 MPa
    flexuralStrength?: number; // 抗折强度 MPa
    tensileStrength?: number; // 抗拉强度 MPa
    elasticModulus?: number; // 弹性模量 GPa
    activity?: number; // 活性指数
  };

  // 质量标准
  qualityStandards: {
    standard: string; // 执行标准
    grade: string; // 等级
    certificationDate: string;
    expiryDate?: string;
    testReports: TestReport[];
  };

  // 成本信息
  costInfo: {
    unitPrice: number; // 元/吨
    currency: string;
    priceDate: string;
    minimumOrder?: number;
    deliveryTime?: number; // 天
    paymentTerms?: string;
  };

  // 环保信息
  environmentalInfo: {
    carbonFootprint: number; // kg CO2/吨
    recyclable: boolean;
    hazardous: boolean;
    environmentalGrade?: 'A' | 'B' | 'C' | 'D';
    certifications: string[]; // 环保认证
  };

  // 存储要求
  storageRequirements: {
    temperature: {
      min: number;
      max: number;
    };
    humidity: {
      max: number;
    };
    ventilation: boolean;
    lightProtection: boolean;
    shelfLife?: number; // 天
    specialRequirements?: string[];
  };

  // 兼容性
  compatibility: {
    compatibleMaterials: string[]; // 兼容材料ID
    incompatibleMaterials: string[]; // 不兼容材料ID
    restrictions: string[]; // 使用限制
  };

  // 使用建议
  usageRecommendations: {
    recommendedFor: string[]; // 推荐用途
    notRecommendedFor: string[]; // 不推荐用途
    optimalDosage: {
      min: number;
      max: number;
      unit: string;
    };
    mixingRequirements?: string[];
  };

  // 可用性状态
  availability: {
    status: AvailabilityStatus;
    stockLevel: number;
    unit: string;
    location: string;
    lastUpdated: string;
    reservedAmount?: number;
  };

  // 元数据
  metadata: {
    createdAt: string;
    createdBy: string;
    updatedAt: string;
    updatedBy: string;
    version: number;
    status: 'active' | 'inactive' | 'discontinued';
    tags: string[];
    notes?: string;
  };
}

export interface SiloModel {
  id: string;
  name: string;
  code: string;

  // 基本信息
  mixingStation: string;
  location: string;
  type: SiloType;

  // 容量信息
  capacity: {
    total: number; // 总容量
    usable: number; // 可用容量
    current: number; // 当前存量
    reserved: number; // 预留量
    unit: string;
  };

  // 存储材料
  storedMaterial?: {
    materialId: string;
    materialName: string;
    specification: string;
    batchNumber: string;
    receivedDate: string;
    expiryDate?: string;
    quality: QualityStatus;
  };

  // 设备状态
  equipment: {
    status: EquipmentStatus;
    lastMaintenance: string;
    nextMaintenance: string;
    sensors: SensorInfo[];
    alarms: AlarmInfo[];
  };

  // 操作历史
  operationHistory: OperationRecord[];

  // 质量控制
  qualityControl: {
    lastInspection: string;
    nextInspection: string;
    inspectionResults: InspectionResult[];
    qualityGrade: 'A' | 'B' | 'C' | 'D';
  };

  // 元数据
  metadata: {
    createdAt: string;
    updatedAt: string;
    status: 'active' | 'maintenance' | 'inactive';
    notes?: string;
  };
}

// 枚举类型
export type MaterialCategory =
  | 'cement'
  | 'aggregate'
  | 'admixture'
  | 'additive'
  | 'water'
  | 'supplementary';

export type MaterialType =
  | 'portland_cement'
  | 'blended_cement'
  | 'fine_aggregate'
  | 'coarse_aggregate'
  | 'chemical_admixture'
  | 'mineral_admixture'
  | 'water'
  | 'fiber'
  | 'other';

export type AvailabilityStatus =
  | 'available'
  | 'low_stock'
  | 'out_of_stock'
  | 'on_order'
  | 'discontinued';

export type SiloType = 'cement' | 'sand' | 'stone' | 'water' | 'additive' | 'flyash' | 'other';

export type QualityStatus = 'excellent' | 'good' | 'acceptable' | 'poor' | 'rejected';

export type EquipmentStatus = 'normal' | 'warning' | 'error' | 'maintenance' | 'offline';

// 辅助接口
export interface SupplierInfo {
  id: string;
  name: string;
  contact: string;
  address: string;
  phone: string;
  email: string;
  rating: number;
  certifications: string[];
}

export interface TestReport {
  id: string;
  testDate: string;
  testType: string;
  results: Record<string, any>;
  conclusion: string;
  tester: string;
  laboratory: string;
  certificateNumber: string;
}

export interface SensorInfo {
  id: string;
  type: string;
  value: number;
  unit: string;
  status: 'normal' | 'warning' | 'error';
  lastUpdate: string;
}

export interface AlarmInfo {
  id: string;
  type: string;
  level: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

export interface OperationRecord {
  id: string;
  type: 'fill' | 'discharge' | 'transfer' | 'maintenance';
  amount: number;
  unit: string;
  operator: string;
  timestamp: string;
  notes?: string;
}

export interface InspectionResult {
  id: string;
  inspectionDate: string;
  inspector: string;
  items: {
    item: string;
    result: 'pass' | 'fail' | 'warning';
    value?: number;
    unit?: string;
    notes?: string;
  }[];
  overallResult: 'pass' | 'fail' | 'conditional';
  nextInspectionDate: string;
}
