'use client';

import { useCallback, useEffect, useState } from 'react';
import { useParams } from 'next/navigation';

import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { LoadingSpinner } from '@/shared/components/LoadingSpinner';
import { useToast } from '@/shared/hooks/use-toast';
import { generateMockTasks } from '@/infrastructure/api/mock/mock-data';
import type { Task } from '@/core/types';

/**
 * 原始V1配比页面 - 恢复原有功能和样式
 */
export default function OriginalV1RatioPage() {
  const params = useParams();
  const taskId = params?.['taskId'] as string;
  const { toast } = useToast();

  // 页面状态
  const [task, setTask] = useState<Task | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 配比数据状态
  const [materials, setMaterials] = useState<any[]>([]);
  const [calculationParams, setCalculationParams] = useState<any>({});
  const [calculationResults, setCalculationResults] = useState<any>({});
  const [isDirty, setIsDirty] = useState(false);

  // 加载任务数据
  useEffect(() => {
    const loadTask = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 模拟加载任务数据
        const mockTasks = generateMockTasks();
        const foundTask = mockTasks.find(t => t.id === taskId);

        if (!foundTask) {
          throw new Error(`未找到任务 ID: ${taskId}`);
        }

        setTask(foundTask);

        // 模拟加载配比数据
        await new Promise(resolve => setTimeout(resolve, 500));

        // 初始化配比数据
        setMaterials([
          { id: '1', name: '水泥', type: 'cement', amount: 350, unit: 'kg/m³' },
          { id: '2', name: '砂', type: 'sand', amount: 650, unit: 'kg/m³' },
          { id: '3', name: '石', type: 'stone', amount: 1200, unit: 'kg/m³' },
          { id: '4', name: '水', type: 'water', amount: 175, unit: 'kg/m³' },
        ]);

        setCalculationParams({
          strength: 'C30',
          slump: '120-160mm',
          waterCementRatio: 0.5,
        });

        setCalculationResults({
          totalVolume: 1.0,
          density: 2375,
          cementContent: 350,
          waterContent: 175,
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载失败');
      } finally {
        setIsLoading(false);
      }
    };

    if (taskId) {
      loadTask();
    }
  }, [taskId]);

  // 保存配比
  const handleSave = useCallback(async () => {
    try {
      toast({
        title: '保存成功',
        description: '配比数据已保存',
      });
      setIsDirty(false);
    } catch (error) {
      toast({
        title: '保存失败',
        description: '请稍后重试',
        variant: 'destructive',
      });
    }
  }, [toast]);

  // 计算配比
  const handleCalculate = useCallback(() => {
    // 模拟计算逻辑
    toast({
      title: '计算完成',
      description: '配比计算已完成',
    });
  }, [toast]);

  // 添加材料
  const handleAddMaterial = useCallback(() => {
    const newMaterial = {
      id: Date.now().toString(),
      name: '新材料',
      type: 'other',
      amount: 0,
      unit: 'kg/m³',
    };
    setMaterials(prev => [...prev, newMaterial]);
    setIsDirty(true);
  }, []);

  // 删除材料
  const handleRemoveMaterial = useCallback((materialId: string) => {
    setMaterials(prev => prev.filter(m => m.id !== materialId));
    setIsDirty(true);
  }, []);

  // 更新材料
  const handleUpdateMaterial = useCallback((materialId: string, updates: any) => {
    setMaterials(prev => prev.map(m => (m.id === materialId ? { ...m, ...updates } : m)));
    setIsDirty(true);
  }, []);

  if (isLoading) {
    return (
      <div className='h-screen w-screen flex items-center justify-center bg-gray-50'>
        <div className='text-center space-y-4'>
          <LoadingSpinner size='large' />
          <p className='text-muted-foreground'>正在加载配比页面...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='h-screen w-screen bg-gray-50 p-4 flex items-center justify-center'>
        <Card className='p-6 max-w-md'>
          <CardHeader>
            <CardTitle className='text-destructive'>加载失败</CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-muted-foreground mb-4'>{error}</p>
            <Button onClick={() => window.location.reload()}>重试</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='h-screen w-screen bg-gray-50 flex flex-col'>
      {/* 页面头部 */}
      <div className='bg-white border-b px-6 py-4 flex-shrink-0'>
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-2xl font-bold text-gray-900'>砼配比设计</h1>
            <p className='text-sm text-gray-600'>
              项目: {task?.projectName} | 任务: {task?.taskNumber}
            </p>
          </div>
          <div className='flex items-center gap-3'>
            <Button variant='outline' size='sm'>
              历史记录
            </Button>
            <Button variant='outline' size='sm'>
              模板
            </Button>
            <Button
              onClick={handleSave}
              disabled={!isDirty}
              className='bg-blue-600 hover:bg-blue-700'
            >
              保存配比
            </Button>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className='flex-1 flex overflow-hidden'>
        {/* 左侧：材料配置 */}
        <div className='w-1/3 bg-white border-r flex flex-col'>
          <div className='p-4 border-b'>
            <h2 className='text-lg font-semibold text-gray-900'>材料配置</h2>
          </div>
          <div className='flex-1 overflow-y-auto p-4'>
            <div className='space-y-3'>
              {materials.map(material => (
                <Card key={material.id} className='p-3'>
                  <div className='flex items-center justify-between mb-2'>
                    <input
                      type='text'
                      value={material.name}
                      onChange={e => handleUpdateMaterial(material.id, { name: e.target.value })}
                      className='font-medium text-sm bg-transparent border-none outline-none'
                    />
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => handleRemoveMaterial(material.id)}
                      className='text-red-600 hover:text-red-700'
                    >
                      删除
                    </Button>
                  </div>
                  <div className='flex items-center gap-2'>
                    <input
                      type='number'
                      value={material.amount}
                      onChange={e =>
                        handleUpdateMaterial(material.id, { amount: Number(e.target.value) })
                      }
                      className='flex-1 px-2 py-1 text-sm border rounded'
                    />
                    <span className='text-xs text-gray-500'>{material.unit}</span>
                  </div>
                </Card>
              ))}
            </div>
            <Button onClick={handleAddMaterial} variant='outline' className='w-full mt-4'>
              + 添加材料
            </Button>
          </div>
        </div>

        {/* 中间：计算参数 */}
        <div className='w-1/3 bg-white border-r flex flex-col'>
          <div className='p-4 border-b'>
            <h2 className='text-lg font-semibold text-gray-900'>计算参数</h2>
          </div>
          <div className='flex-1 overflow-y-auto p-4'>
            <div className='space-y-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-1'>强度等级</label>
                <select
                  value={calculationParams.strength}
                  onChange={e =>
                    setCalculationParams((prev: any) => ({ ...prev, strength: e.target.value }))
                  }
                  className='w-full px-3 py-2 border rounded-md'
                >
                  <option value='C20'>C20</option>
                  <option value='C25'>C25</option>
                  <option value='C30'>C30</option>
                  <option value='C35'>C35</option>
                  <option value='C40'>C40</option>
                </select>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-1'>坍落度</label>
                <select
                  value={calculationParams.slump}
                  onChange={e =>
                    setCalculationParams((prev: any) => ({ ...prev, slump: e.target.value }))
                  }
                  className='w-full px-3 py-2 border rounded-md'
                >
                  <option value='30-50mm'>30-50mm</option>
                  <option value='60-90mm'>60-90mm</option>
                  <option value='120-160mm'>120-160mm</option>
                  <option value='180-220mm'>180-220mm</option>
                </select>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-1'>水胶比</label>
                <input
                  type='number'
                  step='0.01'
                  value={calculationParams.waterCementRatio}
                  onChange={e =>
                    setCalculationParams((prev: any) => ({
                      ...prev,
                      waterCementRatio: Number(e.target.value),
                    }))
                  }
                  className='w-full px-3 py-2 border rounded-md'
                />
              </div>

              <Button onClick={handleCalculate} className='w-full bg-green-600 hover:bg-green-700'>
                重新计算
              </Button>
            </div>
          </div>
        </div>

        {/* 右侧：计算结果 */}
        <div className='w-1/3 bg-white flex flex-col'>
          <div className='p-4 border-b'>
            <h2 className='text-lg font-semibold text-gray-900'>计算结果</h2>
          </div>
          <div className='flex-1 overflow-y-auto p-4'>
            <div className='space-y-4'>
              <Card className='p-4'>
                <h3 className='font-medium text-gray-900 mb-3'>基本信息</h3>
                <div className='space-y-2 text-sm'>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>总体积:</span>
                    <span className='font-medium'>{calculationResults.totalVolume} m³</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>密度:</span>
                    <span className='font-medium'>{calculationResults.density} kg/m³</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>胶凝材料:</span>
                    <span className='font-medium'>{calculationResults.cementContent} kg/m³</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>用水量:</span>
                    <span className='font-medium'>{calculationResults.waterContent} kg/m³</span>
                  </div>
                </div>
              </Card>

              <Card className='p-4'>
                <h3 className='font-medium text-gray-900 mb-3'>配比单</h3>
                <div className='space-y-2 text-sm'>
                  {materials.map(material => (
                    <div key={material.id} className='flex justify-between'>
                      <span className='text-gray-600'>{material.name}:</span>
                      <span className='font-medium'>
                        {material.amount} {material.unit}
                      </span>
                    </div>
                  ))}
                </div>
              </Card>

              <div className='flex gap-2'>
                <Button variant='outline' className='flex-1'>
                  预览配比单
                </Button>
                <Button variant='outline' className='flex-1'>
                  打印
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 底部状态栏 */}
      <div className='bg-white border-t px-6 py-3 flex-shrink-0'>
        <div className='flex items-center justify-between'>
          <div className='text-sm text-gray-600'>
            {isDirty ? '有未保存的更改' : '所有更改已保存'}
          </div>
          <div className='flex items-center gap-3'>
            <Button variant='outline' size='sm'>
              审核发送
            </Button>
            <Button variant='outline' size='sm'>
              退出
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
