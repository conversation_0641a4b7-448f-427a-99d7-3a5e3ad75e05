/**
 * V2版本适配器
 * 将核心配比模块适配到V2版本的接口
 */

import { useMemo } from 'react';
import type {
  UseRatioCoreOptions,
  UseRatioCoreReturn,
} from '@/features/ratio-management/hooks/ratio-core/useRatioCore';
import { useRatioCore } from '@/features/ratio-management/hooks/ratio-core/useRatioCore';

import {
  convertV2MaterialToCore,
  convertCoreToV2Material,
  convertCalculationParamsToCore,
  convertCoreCalculationParamsToVersion,
  convertCalculationResultsToCore,
} from '@/core/utils/ratio-core/ratioDataConverter';

import type {
  UnifiedRatioMaterial,
  RatioCalculationParams,
  CalculationResults,
} from '@/core/types/ratio';

/**
 * V2版本的useUnifiedRatioManagement Hook接口
 */
export interface UseUnifiedRatioManagementOptions {
  taskId: string;
  autoLoad?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
  compatibilityMode?: 'legacy' | 'unified';
}

export interface UseUnifiedRatioManagementReturn {
  // 核心数据 - V2格式
  currentRatio: any;
  selectedMaterials: UnifiedRatioMaterial[];
  calculationParams: RatioCalculationParams | null;
  calculationResult: CalculationResults | null;

  // 状态
  isLoading: boolean;
  isCalculating: boolean;
  isSaving: boolean;
  error: string | null;
  isDirty: boolean;
  hasParamsChanged: boolean;

  // 数据操作
  loadRatio: (taskId?: string) => Promise<void>;
  saveRatio: () => Promise<void>;
  clearRatio: () => void;

  // 材料操作
  addMaterial: (material: UnifiedRatioMaterial) => void;
  removeMaterial: (materialId: string) => void;
  updateMaterial: (materialId: string, updates: Partial<UnifiedRatioMaterial>) => void;

  // 计算操作
  updateCalculationParams: (params: Partial<RatioCalculationParams>) => void;
  calculateRatio: () => Promise<void>;
  reverseCalculate: () => Promise<void>;

  // 工具方法
  canSave: () => boolean;
  canCalculate: () => boolean;
  hasUnsavedChanges: () => boolean;

  // V2特有方法
  convertLegacyMaterialsToUnified: (legacyMaterials: any[]) => UnifiedRatioMaterial[];
  convertUnifiedMaterialsToLegacy: (unifiedMaterials: UnifiedRatioMaterial[]) => any[];
  exportToLegacyFormat: () => any;
  importFromLegacyFormat: (legacyData: any) => void;
}

/**
 * V2版本适配器Hook
 * 将核心配比模块适配为V2版本的useUnifiedRatioManagement接口
 */
export function useUnifiedRatioManagement(
  options: UseUnifiedRatioManagementOptions
): UseUnifiedRatioManagementReturn {
  const {
    taskId,
    autoLoad = true,
    autoSave = true,
    autoSaveDelay = 3000,
    compatibilityMode = 'unified',
  } = options;

  // 使用核心Hook
  const coreOptions: UseRatioCoreOptions = {
    taskId,
    version: 'v2',
    autoLoad,
    autoSave,
    autoSaveDelay,
    useMockData: true, // V2版本默认使用Mock数据
    apiEndpoint: '/api/ratio-v2',
    cacheEnabled: true,
    cacheTTL: 5 * 60 * 1000,
  };

  const core = useRatioCore(coreOptions);

  // ==================== 数据转换 ====================

  // 将核心材料转换为V2格式
  const selectedMaterials = useMemo(() => {
    return core.materials.map(material => convertCoreToV2Material(material));
  }, [core.materials]);

  // 将核心计算参数转换为V2格式
  const calculationParams = useMemo(() => {
    if (!core.calculationParams) return null;
    return convertCoreCalculationParamsToVersion(
      core.calculationParams,
      'v2'
    ) as RatioCalculationParams;
  }, [core.calculationParams]);

  // 将核心计算结果转换为V2格式
  const calculationResult = useMemo(() => {
    if (!core.calculationResults) return null;

    // 转换为V2期望的格式
    return {
      totalWeight: core.calculationResults.totalWeight,
      materials: core.calculationResults.materials,
      qualityScore: core.calculationResults.qualityScore,
      strengthPrediction: core.calculationResults.strengthPrediction,
      workabilityScore: core.calculationResults.workabilityScore,
      durabilityScore: core.calculationResults.durabilityScore,
      warnings: core.calculationResults.warnings.map(w => w.message || String(w)),
      suggestions: core.calculationResults.suggestions.map(s => (s as any).message || String(s)),
      costEstimate: core.calculationResults.costEstimate,
      carbonFootprint: core.calculationResults.carbonFootprint,
      calculationTime: core.calculationResults.calculationTime,
      timestamp: core.calculationResults.timestamp,
    } as CalculationResults;
  }, [core.calculationResults]);

  // 当前配比数据（V2格式）
  const currentRatio = useMemo(() => {
    if (!core.currentRatio) return null;

    return {
      id: core.currentRatio.id,
      taskId: core.currentRatio.taskId,
      name: core.currentRatio.name,
      description: core.currentRatio.description,
      materials: selectedMaterials,
      calculationParams,
      calculationResults: calculationResult,
      version: 'v2',
      createdAt: core.currentRatio.createdAt,
      updatedAt: core.currentRatio.updatedAt,
      status: core.currentRatio.status,
    };
  }, [core.currentRatio, selectedMaterials, calculationParams, calculationResult]);

  // ==================== 材料操作适配 ====================

  const addMaterial = (material: UnifiedRatioMaterial) => {
    const coreMaterial = convertV2MaterialToCore(material);
    core.addMaterial(coreMaterial);
  };

  const removeMaterial = (materialId: string) => {
    core.removeMaterial(materialId);
  };

  const updateMaterial = (materialId: string, updates: Partial<UnifiedRatioMaterial>) => {
    // 将V2格式的更新转换为核心格式
    const coreUpdates: any = {};

    if (updates.designValue !== undefined) coreUpdates.designValue = updates.designValue;
    if (updates.actualAmount !== undefined) coreUpdates.actualValue = updates.actualAmount;
    if (updates.unit !== undefined) coreUpdates.unit = updates.unit;
    if (updates.density !== undefined) coreUpdates.density = updates.density;
    // 处理可能不存在的扩展属性
    const extendedUpdates = updates as any;
    if (extendedUpdates.specificGravity !== undefined)
      coreUpdates.specificGravity = extendedUpdates.specificGravity;
    if (extendedUpdates.absorptionRate !== undefined)
      coreUpdates.absorptionRate = extendedUpdates.absorptionRate;
    if ((updates as any).fineness !== undefined)
      (coreUpdates as any).fineness = (updates as any).fineness;
    if ((updates as any).grade !== undefined) (coreUpdates as any).grade = (updates as any).grade;
    if (updates.supplier !== undefined) coreUpdates.supplier = updates.supplier;
    if ((updates as any).batchNumber !== undefined)
      (coreUpdates as any).batchNumber = (updates as any).batchNumber;
    if ((updates as any).ratio !== undefined) (coreUpdates as any).ratio = (updates as any).ratio;
    if ((updates as any).percentage !== undefined)
      (coreUpdates as any).percentage = (updates as any).percentage;

    core.updateMaterial(materialId, coreUpdates);
  };

  // ==================== 计算操作适配 ====================

  const updateCalculationParams = (params: Partial<RatioCalculationParams>) => {
    const coreParams = convertCalculationParamsToCore(params, 'v2');
    core.updateCalculationParams(coreParams);
  };

  const calculateRatio = async () => {
    await core.calculate();
  };

  const reverseCalculate = async () => {
    await core.reverseCalculate();
  };

  // ==================== V2特有方法 ====================

  const convertLegacyMaterialsToUnified = (legacyMaterials: any[]): any[] => {
    return legacyMaterials.map(material => {
      // 将V1格式转换为V2格式
      return {
        id: material.id || `material_${Date.now()}`,
        name: material.name,
        category: material.category,
        designValue: material.amount || material.designValue || 0,
        actualValue: material.actualAmount,
        unit: material.unit || 'kg/m³',
        density: material.density,
        specificGravity: material.specificGravity,
        absorptionRate: material.absorptionRate,
        fineness: material.fineness,
        grade: material.grade,
        supplier: material.supplier,
        batchNumber: material.batchNumber,
        ratio: material.ratio,
        percentage: material.percentage,
      };
    });
  };

  const convertUnifiedMaterialsToLegacy = (unifiedMaterials: UnifiedRatioMaterial[]): any[] => {
    return unifiedMaterials.map(material => {
      // 将V2格式转换为V1格式
      return {
        id: material.id,
        name: material.name,
        category: material.category,
        amount: material.designValue,
        actualAmount: (material as any).actualValue,
        unit: material.unit,
        density: material.density,
        specificGravity: (material as any).specificGravity,
        grade: (material as any).grade,
        supplier: material.supplier,
        ratio: (material as any).ratio,
        percentage: (material as any).percentage,
        designValue: material.designValue,
      };
    });
  };

  const exportToLegacyFormat = () => {
    if (!currentRatio) return null;

    return {
      id: currentRatio.id,
      taskId: currentRatio.taskId,
      name: currentRatio.name,
      description: currentRatio.description,
      materials: convertUnifiedMaterialsToLegacy(selectedMaterials),
      calculationParams: calculationParams,
      calculationResults: calculationResult,
      version: 'v1', // 导出为V1格式
      createdAt: currentRatio.createdAt,
      updatedAt: currentRatio.updatedAt,
      status: currentRatio.status,
    };
  };

  const importFromLegacyFormat = (legacyData: any) => {
    const unifiedMaterials = convertLegacyMaterialsToUnified(legacyData.materials || []);

    // 重建核心格式数据
    const coreData = {
      id: legacyData.id || `ratio_${Date.now()}`,
      taskId: legacyData.taskId || taskId,
      name: legacyData.name || '导入的配比',
      description: legacyData.description,
      materials: unifiedMaterials.map(material => convertV2MaterialToCore(material)),
      calculationParams: convertCalculationParamsToCore(legacyData.calculationParams || {}, 'v2'),
      calculationResults: legacyData.calculationResults
        ? convertCalculationResultsToCore(legacyData.calculationResults, 'v2')
        : undefined,
      version: 'v2' as const,
      createdAt: legacyData.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: legacyData.createdBy,
      status: legacyData.status || ('draft' as const),
      tags: legacyData.tags,
      metadata: {
        importedFrom: 'legacy',
        originalData: legacyData,
      },
    };

    // 使用核心方法导入
    core.importRatio(JSON.stringify(coreData));
  };

  // ==================== 返回适配后的接口 ====================

  return {
    // 核心数据 - V2格式
    currentRatio,
    selectedMaterials,
    calculationParams,
    calculationResult,

    // 状态
    isLoading: core.isLoading,
    isCalculating: core.isCalculating,
    isSaving: core.isSaving,
    error: core.error,
    isDirty: core.isDirty,
    hasParamsChanged: core.hasParamsChanged,

    // 数据操作
    loadRatio: (taskId?: string) => core.loadRatio(taskId || ''),
    saveRatio: core.saveRatio,
    clearRatio: core.clearRatio,

    // 材料操作
    addMaterial,
    removeMaterial,
    updateMaterial,

    // 计算操作
    updateCalculationParams,
    calculateRatio,
    reverseCalculate,

    // 工具方法
    canSave: core.canSave,
    canCalculate: core.canCalculate,
    hasUnsavedChanges: core.hasUnsavedChanges,

    // V2特有方法
    convertLegacyMaterialsToUnified,
    convertUnifiedMaterialsToLegacy,
    exportToLegacyFormat,
    importFromLegacyFormat,
  };
}
