// src/workers/reminderWorker.ts
import type {
  ReminderConfig,
  ReminderMessage,
  ReminderType,
  ReminderWorkerInput,
  ReminderWorkerOutput,
  Task,
  TaskUpdateForWorker,
} from '@/core/types';

// 工作线程内部状态
let configs: ReminderConfig[] = [];
let previousTasks: Task[] = [];
let taskReminders: Map<string, Set<string>> = new Map(); // 用字符串键跟踪已触发的提醒
let messages: ReminderMessage[] = [];
let messageCounter = 0;
let mockMessageInterval: number | null = null; // 模拟消息的定时器ID

// 常量定义
const REMINDER_WINDOW_MINUTES_DEFAULT = 5; // 默认提醒窗口
const MAX_TASKS_PER_BATCH = 100; // 每批处理的最大任务数
const WORKER_RECOVERY_INTERVAL = 60000; // 出错后恢复间隔（毫秒）
const MOCK_MESSAGE_INTERVAL = 10000; // 模拟消息间隔（毫秒）

/**
 * 计算下次发车时间
 */
function calculateNextDispatchTime(task: Task): Date | null {
  // 如果任务已有nextScheduledDispatchTime字段，直接使用
  if (task.nextScheduledDispatchTime) {
    return new Date(task.nextScheduledDispatchTime);
  }

  // 否则使用旧的计算逻辑
  if (!task.lastDispatchTime || !task.dispatchFrequencyMinutes) {
    return null;
  }

  const lastDispatch = new Date(task.lastDispatchTime);
  return new Date(lastDispatch.getTime() + task.dispatchFrequencyMinutes * 60 * 1000);
}

/**
 * 计算任务距离下次发车的分钟数
 */
function calculateMinutesToDispatch(task: Task): number | null {
  // 如果任务没有发车频率，返回null
  if (!task.dispatchFrequencyMinutes) {
    return null;
  }

  const now = new Date();

  // 如果有下次发车时间，计算到该时间的分钟数
  if (task.nextScheduledDispatchTime) {
    const nextDispatchTime = new Date(task.nextScheduledDispatchTime);
    const diffMs = nextDispatchTime.getTime() - now.getTime();
    return Math.ceil(diffMs / (60 * 1000));
  }

  // 如果有上次发车时间，根据频率计算下次发车时间
  if (task.lastDispatchTime) {
    const lastDispatch = new Date(task.lastDispatchTime);
    const elapsedMinutes = (now.getTime() - lastDispatch.getTime()) / (60 * 1000);
    const remainingMinutes =
      task.dispatchFrequencyMinutes - (elapsedMinutes % task.dispatchFrequencyMinutes);
    return Math.ceil(remainingMinutes);
  }

  // 如果都没有，使用任务创建时间作为基准
  if (task.createdAt) {
    const createdTime = new Date(task.createdAt);
    const elapsedMinutes = (now.getTime() - createdTime.getTime()) / (60 * 1000);
    const remainingMinutes =
      task.dispatchFrequencyMinutes - (elapsedMinutes % task.dispatchFrequencyMinutes);
    return Math.ceil(remainingMinutes);
  }

  // 如果没有任何时间信息，返回频率值
  return task.dispatchFrequencyMinutes;
}

/**
 * 生成模拟消息
 */
function generateMockMessage(tasks: Task[]): ReminderMessage | null {
  if (!tasks || tasks.length === 0) return null;

  // 随机选择一个任务
  const randomTaskIndex = Math.floor(Math.random() * tasks.length);
  const randomTask = tasks[randomTaskIndex];

  if (!randomTask) return null;

  // 随机选择一个提醒类型
  const reminderTypes: ReminderType[] = ['dispatchCountdown', 'highlight', 'popup', 'sound'];
  const randomTypeIndex = Math.floor(Math.random() * reminderTypes.length);
  const randomType = reminderTypes[randomTypeIndex] ?? 'dispatchCountdown';

  // 随机选择一个分钟数
  const minutes = [5, 10, 15, 30][Math.floor(Math.random() * 4)] ?? 5;

  // 创建模拟消息
  return {
    id: `mock_reminder_${messageCounter++}`,
    taskId: randomTask.id,
    taskNumber: randomTask.taskNumber || '未知任务编号',
    type: randomType,
    title: '模拟发车提醒',
    description: `任务 ${randomTask.taskNumber || '未知'}（${randomTask.projectName || '未知项目'}）距离下次发车时间还有${minutes}分钟`,
    projectName: randomTask.projectName || '未知项目',
    time: Date.now(),
    read: false,
  };
}

/**
 * 启动模拟消息生成器
 */
function startMockMessageGenerator(tasks: Task[]) {
  // 如果已经有定时器在运行，先停止它
  if (mockMessageInterval !== null) {
    clearInterval(mockMessageInterval);
  }

  // 创建新的定时器，定期生成模拟消息
  mockMessageInterval = setInterval(() => {
    if (tasks.length > 0) {
      const mockMessage = generateMockMessage(tasks);
      if (mockMessage) {
        messages.push(mockMessage);

        // 发送新消息通知
        self.postMessage({
          tasksWithUpdates: [],
          processedTime: Date.now(),
          messages: [mockMessage],
          messageType: 'NEW_MESSAGES',
        } as ReminderWorkerOutput);
      }
    }
  }, MOCK_MESSAGE_INTERVAL) as unknown as number;
}

/**
 * 停止模拟消息生成器
 */
function stopMockMessageGenerator() {
  if (mockMessageInterval !== null) {
    clearInterval(mockMessageInterval);
    mockMessageInterval = null;
  }
}

/**
 * 处理工作线程消息
 */
self.onmessage = (event: MessageEvent) => {
  try {
    const data = event.data;

    // 检查旧格式的消息（兼容性处理）
    if (data.tasks && Array.isArray(data.tasks)) {
      processTasks(data.tasks, data.reminderWindowMinutes || REMINDER_WINDOW_MINUTES_DEFAULT);
      return;
    }

    // 处理新格式的消息
    switch (data.type) {
      case 'UPDATE_CONFIGS':
        configs = data.payload;
        taskReminders.clear(); // 清除之前的提醒记录
        break;

      case 'CLEAR_MESSAGES':
        messages = [];
        break;

      case 'ADD_MESSAGE':
        const message: Partial<ReminderMessage> = {
          ...data.payload,
          id: `reminder_${messageCounter++}`,
          time: Date.now(),
          read: false,
        };
        messages.push(message as ReminderMessage);
        break;

      case 'UPDATE_TASKS':
        processTasks(data.payload, REMINDER_WINDOW_MINUTES_DEFAULT);
        // 启动模拟消息生成器
        startMockMessageGenerator(data.payload);
        break;

      case 'STOP_MOCK_MESSAGES':
        stopMockMessageGenerator();
        break;

      default:
        console.warn('Worker received unknown message type:', data.type);
    }
  } catch (error) {
    console.error('Worker处理消息出错:', error);

    // 发送错误消息给主线程
    self.postMessage({
      tasksWithUpdates: [],
      processedTime: Date.now(),
      error: '处理任务时出错',
      errorDetails: error instanceof Error ? error.stack : String(error),
    } as ReminderWorkerOutput);
  }
};

/**
 * 检查任务并处理提醒
 */
function processTasks(tasks: Task[], reminderWindowMinutes: number) {
  const startTime = Date.now();
  const tasksWithUpdates: TaskUpdateForWorker[] = [];
  const newMessages: ReminderMessage[] = [];

  try {
    // 分批处理任务以避免阻塞
    for (let i = 0; i < tasks.length; i += MAX_TASKS_PER_BATCH) {
      const batch = tasks.slice(i, i + MAX_TASKS_PER_BATCH);
      processTaskBatch(batch, tasksWithUpdates, newMessages, reminderWindowMinutes);
    }

    // 如果有新的提醒消息，将其添加到消息列表
    if (newMessages.length > 0) {
      messages.push(...newMessages);

      // 发送新消息通知
      self.postMessage({
        tasksWithUpdates: [],
        processedTime: Date.now(),
        messages: newMessages,
        messageType: 'NEW_MESSAGES',
      } as ReminderWorkerOutput);
    }

    // 发送任务更新结果
    self.postMessage({
      tasksWithUpdates,
      processedTime: Date.now(),
      stats: {
        taskCount: tasks.length,
        updatedCount: tasksWithUpdates.length,
        processingTimeMs: Date.now() - startTime,
      },
    } as ReminderWorkerOutput);

    // 保存当前任务列表作为下次比较的基准
    previousTasks = [...tasks];
  } catch (error) {
    console.error('处理任务批次时出错:', error);

    // 发送错误消息
    self.postMessage({
      tasksWithUpdates: [],
      processedTime: Date.now(),
      error: '处理任务时出错',
      errorDetails: error instanceof Error ? error.stack : String(error),
    } as ReminderWorkerOutput);

    // 设置一个定时器，在一段时间后尝试恢复
    setTimeout(() => {
      self.postMessage({
        tasksWithUpdates: [],
        processedTime: Date.now(),
        recovery: true,
      } as ReminderWorkerOutput);
    }, WORKER_RECOVERY_INTERVAL);
  }
}

/**
 * 处理任务批次
 */
function processTaskBatch(
  tasks: Task[],
  tasksWithUpdates: TaskUpdateForWorker[],
  newMessages: ReminderMessage[],
  reminderWindowMinutes: number
) {
  const now = new Date();

  for (const task of tasks) {
    // 只处理正在进行中的任务
    if (task.dispatchStatus !== 'InProgress') {
      continue;
    }

    try {
      // 获取任务配置
      const config = configs.find(c => c.taskId === task.id);
      const isTaskConfigured = config && config.enabled;

      // 计算下次发车时间
      const nextDispatchTime = calculateNextDispatchTime(task);
      if (!nextDispatchTime) {
        continue;
      }

      // 计算距离发车的分钟数
      const minutesToDispatch = calculateMinutesToDispatch(task);
      if (minutesToDispatch === null) {
        continue;
      }

      // 更新计算的任务数据
      const taskUpdate: TaskUpdateForWorker = {
        id: task.id,
        nextScheduledDispatchTime: nextDispatchTime.toISOString(),
        minutesToDispatch,
      };

      // 检查是否需要发车提醒
      const isWithinReminderWindow =
        minutesToDispatch <=
        (isTaskConfigured ? config.reminderFrequencyMinutes : reminderWindowMinutes);

      if (isWithinReminderWindow) {
        taskUpdate.isDueForDispatch = true;

        // 如果已配置且当前没有提醒，创建提醒消息
        if (isTaskConfigured) {
          const reminderKey = `${task.id}_standard`;
          const triggeredTypes = taskReminders.get(task.id) || new Set<string>();

          if (!triggeredTypes.has(reminderKey)) {
            triggeredTypes.add(reminderKey);
            taskReminders.set(task.id, triggeredTypes);

            // 检查每个提醒级别
            if (config.reminderLevels && Array.isArray(config.reminderLevels)) {
              for (const level of config.reminderLevels) {
                if (minutesToDispatch <= level.minutes) {
                  const levelKey = `${task.id}_${level.minutes}`;

                  if (!triggeredTypes.has(levelKey)) {
                    triggeredTypes.add(levelKey);

                    // 创建对应级别的提醒消息
                    for (const type of level.types) {
                      newMessages.push({
                        id: `reminder_${messageCounter++}`,
                        taskId: task.id,
                        taskNumber: task.taskNumber,
                        type: type,
                        title: '发车提醒',
                        description: `任务 ${task.taskNumber}（${task.projectName}）距离下次发车时间还有${level.minutes}分钟`,
                        projectName: task.projectName,
                        time: Date.now(),
                        read: false,
                      });
                    }
                  }
                }
              }
            }
          }
        }
      } else {
        taskUpdate.isDueForDispatch = false;
      }

      tasksWithUpdates.push(taskUpdate);
    } catch (error) {
      console.error(`处理任务 ${task.id} 时出错:`, error);
    }
  }
}

// 导出默认对象以满足TypeScript的模块要求
export default {};
