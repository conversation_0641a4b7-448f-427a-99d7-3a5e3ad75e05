/**
 * 车辆网格组件
 * 专门用于车辆调度的4列网格布局
 */

import React, { useMemo, useCallback } from 'react';
import { DispatchableVehicleCard } from './DispatchableVehicleCard';
import type {
  Vehicle,
  VehicleDisplayMode,
  InTaskVehicleCardStyle,
  TaskListDensityMode,
} from '@/core/types';
import { cn } from '../../../core/lib/utils';

interface VehicleGridProps {
  vehicles: Vehicle[];
  listType: 'pending' | 'returned' | 'outbound';
  globalDispatchActive: boolean;
  displayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  density: Exclude<TaskListDensityMode, '' | 'card'>;
  className?: string;
  columns?: 2 | 3 | 4 | 5 | 6;
  emptyMessage?: string;
  onContextMenu: (event: React.MouseEvent, vehicle: Vehicle) => void;
  onVisualMove: (draggedId: string, hoverId: string, statusList: 'pending' | 'returned') => void;
  onCommitReorder: (statusList: 'pending' | 'returned') => void;
}

/**
 * 车辆网格组件
 */
const VehicleGridComponent: React.FC<VehicleGridProps> = ({
  vehicles,
  listType,
  globalDispatchActive,
  displayMode,
  inTaskVehicleCardStyles,
  density,
  className,
  columns = 4,
  emptyMessage = '暂无车辆',
  onContextMenu,
  onVisualMove,
  onCommitReorder,
}) => {
  // 判断是否为可拖拽的列表类型
  const isDraggableList = listType === 'pending' || listType === 'returned';

  // 缓存网格类名计算 - 避免每次渲染重新计算
  const gridColumnsClass = useMemo(() => {
    switch (columns) {
      case 2:
        return 'grid-cols-2';
      case 3:
        return 'grid-cols-2 sm:grid-cols-3';
      case 4:
        return 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4';
      case 5:
        return 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5';
      case 6:
        return 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6';
      default:
        return 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4';
    }
  }, [columns]);

  // 缓存空状态列跨度计算
  const emptyColSpanClass = useMemo(() => {
    switch (columns) {
      case 2:
        return 'col-span-2';
      case 3:
        return 'col-span-2 sm:col-span-3';
      case 4:
        return 'col-span-2 sm:col-span-3 md:col-span-4';
      case 5:
        return 'col-span-2 sm:col-span-3 md:col-span-4 lg:col-span-5';
      case 6:
        return 'col-span-2 sm:col-span-3 md:col-span-4 lg:col-span-5 xl:col-span-6';
      default:
        return 'col-span-2 sm:col-span-3 md:col-span-4';
    }
  }, [columns]);

  // 缓存车辆卡片样式对象 - 避免每次渲染重新创建
  const finalCardStyles = useMemo(
    (): InTaskVehicleCardStyle => ({
      ...inTaskVehicleCardStyles,
      // 保持用户设置的gap值，如果没有设置则使用0
      gap: inTaskVehicleCardStyles.gap ?? 0,
    }),
    [inTaskVehicleCardStyles]
  );

  // 缓存右键菜单事件处理器 - 避免每次渲染重新创建函数
  const handleContextMenu = useCallback(
    (e: React.MouseEvent, vehicle: Vehicle) => {
      e.preventDefault();
      onContextMenu(e, vehicle);
    },
    [onContextMenu]
  );

  return (
    <div className={cn('w-full', className)}>
      {/* 车辆网格 */}
      <div className={cn('grid gap-1 auto-rows-max', gridColumnsClass)}>
        {vehicles.length === 0 ? (
          <div className={cn(emptyColSpanClass, 'text-center py-4 text-gray-400 text-sm')}>
            {emptyMessage}
          </div>
        ) : (
          vehicles.map((vehicle, index) => (
            <div key={vehicle.id} className='flex-shrink-0'>
              {isDraggableList ? (
                <DispatchableVehicleCard
                  vehicle={vehicle}
                  index={index}
                  listType={listType as 'pending' | 'returned'}
                  globalDispatchActive={globalDispatchActive}
                  displayMode={displayMode}
                  inTaskVehicleCardStyles={finalCardStyles}
                  density={density}
                  onContextMenu={onContextMenu}
                  onVisualMove={onVisualMove}
                  onCommitReorder={onCommitReorder}
                />
              ) : (
                // 非拖拽列表（如outbound）使用简单的卡片显示
                <div onContextMenu={e => handleContextMenu(e, vehicle)}>
                  <DispatchableVehicleCard
                    vehicle={vehicle}
                    index={index}
                    listType={listType === 'outbound' ? 'pending' : listType}
                    globalDispatchActive={globalDispatchActive}
                    displayMode={displayMode}
                    inTaskVehicleCardStyles={finalCardStyles}
                    density={density}
                    onContextMenu={onContextMenu}
                    onVisualMove={onVisualMove}
                    onCommitReorder={onCommitReorder}
                  />
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

// 使用 React.memo 优化 - 避免因父组件重渲染导致的不必要重渲染
export const VehicleGrid = React.memo(VehicleGridComponent);

/**
 * 预设的网格配置
 */
export const VEHICLE_GRID_PRESETS = {
  compact: { columns: 6 as const, name: '紧凑 (6列)' },
  standard: { columns: 4 as const, name: '标准 (4列)' },
  comfortable: { columns: 3 as const, name: '舒适 (3列)' },
  spacious: { columns: 2 as const, name: '宽松 (2列)' },
} as const;

export type VehicleGridPreset = keyof typeof VEHICLE_GRID_PRESETS;
