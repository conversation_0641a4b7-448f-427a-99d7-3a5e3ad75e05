import React, { useState } from 'react';

import {
  BarChart3,
  ChevronDown,
  ChevronUp,
  Eye,
  EyeOff,
  Lock,
  <PERSON>lette,
  Settings,
  Unlock,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { Switch } from '@/shared/components/switch';
import { TaskColumnId, TaskGroupConfig } from '@/core/types';
import { getGroupingOptions } from '@/core/utils/task-grouping';

// 从本地配置文件导入任务列配置
import { ALL_TASK_COLUMNS_CONFIG } from '../task-list.config';

interface TaskGroupConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  groupConfig: TaskGroupConfig;
  onUpdateConfig: (config: Partial<TaskGroupConfig>) => void;
}

export function TaskGroupConfigModal({
  isOpen,
  onClose,
  groupConfig,
  onUpdateConfig,
}: TaskGroupConfigModalProps) {
  const [localConfig, setLocalConfig] = useState<TaskGroupConfig>(groupConfig);
  const groupingOptions = getGroupingOptions(localConfig);

  const handleSave = () => {
    onUpdateConfig(localConfig);
    onClose();
  };

  const handleCancel = () => {
    setLocalConfig(groupConfig);
    onClose();
  };

  const updateLocalConfig = (updates: Partial<TaskGroupConfig>) => {
    setLocalConfig(prev => ({ ...prev, ...updates }));
  };

  const updateGroupHeaderStyle = (updates: Partial<TaskGroupConfig['groupHeaderStyle']>) => {
    setLocalConfig(prev => ({
      ...prev,
      groupHeaderStyle: {
        ...prev.groupHeaderStyle,
        ...updates,
      },
    }));
  };

  const backgroundOptions = [
    { value: 'bg-muted/50', label: '浅灰色', preview: 'bg-muted/50' },
    { value: 'bg-blue-50', label: '浅蓝色', preview: 'bg-blue-50' },
    { value: 'bg-green-50', label: '浅绿色', preview: 'bg-green-50' },
    { value: 'bg-yellow-50', label: '浅黄色', preview: 'bg-yellow-50' },
    { value: 'bg-purple-50', label: '浅紫色', preview: 'bg-purple-50' },
    { value: 'bg-transparent', label: '透明', preview: 'bg-transparent border border-dashed' },
  ];

  const fontSizeOptions = [
    { value: 'text-xs', label: '小号' },
    { value: 'text-sm', label: '中号' },
    { value: 'text-base', label: '大号' },
  ];

  const fontWeightOptions = [
    { value: 'font-normal', label: '正常' },
    { value: 'font-medium', label: '中等' },
    { value: 'font-semibold', label: '半粗' },
    { value: 'font-bold', label: '粗体' },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-2xl max-h-[80vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Settings className='h-5 w-5' />
            任务分组配置
          </DialogTitle>
        </DialogHeader>

        <div className='space-y-6'>
          {/* 基础设置 */}
          <Card>
            <CardHeader>
              <CardTitle className='text-base flex items-center gap-2'>
                <Settings className='h-4 w-4' />
                基础设置
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='flex items-center justify-between'>
                <Label htmlFor='enable-grouping' className='flex items-center gap-2'>
                  <Eye className='h-4 w-4' />
                  启用分组功能
                </Label>
                <Switch
                  id='enable-grouping'
                  checked={localConfig.enabled}
                  onCheckedChange={enabled => updateLocalConfig({ enabled })}
                />
              </div>

              <div className='space-y-2'>
                <Label className='flex items-center gap-2'>
                  <BarChart3 className='h-4 w-4' />
                  分组字段
                </Label>
                <Select
                  value={localConfig.groupBy}
                  onValueChange={(groupBy: TaskGroupConfig['groupBy']) =>
                    updateLocalConfig({ groupBy, enabled: groupBy !== 'none' })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {groupingOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className='flex items-center gap-2'>
                          <span>{option.icon}</span>
                          <span>{option.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className='flex items-center justify-between'>
                <Label htmlFor='collapsible' className='flex items-center gap-2'>
                  <ChevronDown className='h-4 w-4' />
                  允许折叠分组
                </Label>
                <Switch
                  id='collapsible'
                  checked={localConfig.collapsible}
                  onCheckedChange={collapsible => updateLocalConfig({ collapsible })}
                />
              </div>

              <div className='flex items-center justify-between'>
                <Label htmlFor='show-stats' className='flex items-center gap-2'>
                  <BarChart3 className='h-4 w-4' />
                  显示分组统计
                </Label>
                <Switch
                  id='show-stats'
                  checked={localConfig.showGroupStats}
                  onCheckedChange={showGroupStats => updateLocalConfig({ showGroupStats })}
                />
              </div>

              <div className='space-y-2'>
                <Label>排序方式</Label>
                <Select
                  value={localConfig.sortOrder}
                  onValueChange={(sortOrder: 'asc' | 'desc') => updateLocalConfig({ sortOrder })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='asc'>
                      <div className='flex items-center gap-2'>
                        <ChevronUp className='h-4 w-4' />
                        <span>升序 (A-Z)</span>
                      </div>
                    </SelectItem>
                    <SelectItem value='desc'>
                      <div className='flex items-center gap-2'>
                        <ChevronDown className='h-4 w-4' />
                        <span>降序 (Z-A)</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* 列分组权限设置 */}
          <Card>
            <CardHeader>
              <CardTitle className='text-base flex items-center gap-2'>
                <Lock className='h-4 w-4' />
                列分组权限
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-3'>
                <div>
                  <Label className='text-sm font-medium text-green-700 flex items-center gap-2 mb-2'>
                    <Unlock className='h-4 w-4' />
                    允许分组的列
                  </Label>
                  <div className='flex flex-wrap gap-2'>
                    {ALL_TASK_COLUMNS_CONFIG.map(column => {
                      const isAllowed =
                        localConfig.allowedGroupColumns?.includes(column.id as TaskColumnId) ||
                        false;
                      const isDisallowed =
                        localConfig.disallowedGroupColumns?.includes(column.id as TaskColumnId) ||
                        false;

                      return (
                        <Badge
                          key={column.id}
                          variant={isAllowed ? 'default' : 'outline'}
                          className={`cursor-pointer transition-colors ${
                            isAllowed
                              ? 'bg-green-100 text-green-800 hover:bg-green-200'
                              : isDisallowed
                                ? 'opacity-50 cursor-not-allowed'
                                : 'hover:bg-green-50'
                          }`}
                          onClick={() => {
                            if (isDisallowed) return;

                            const newAllowed = isAllowed
                              ? localConfig.allowedGroupColumns.filter(id => id !== column.id)
                              : [...localConfig.allowedGroupColumns, column.id as TaskColumnId];

                            updateLocalConfig({ allowedGroupColumns: newAllowed });
                          }}
                        >
                          {column.label}
                        </Badge>
                      );
                    })}
                  </div>
                </div>

                <div>
                  <Label className='text-sm font-medium text-red-700 flex items-center gap-2 mb-2'>
                    <Lock className='h-4 w-4' />
                    禁止分组的列
                  </Label>
                  <div className='flex flex-wrap gap-2'>
                    {ALL_TASK_COLUMNS_CONFIG.map(column => {
                      const isAllowed =
                        localConfig.allowedGroupColumns?.includes(column.id as TaskColumnId) ||
                        false;
                      const isDisallowed =
                        localConfig.disallowedGroupColumns?.includes(column.id as TaskColumnId) ||
                        false;

                      return (
                        <Badge
                          key={column.id}
                          variant={isDisallowed ? 'destructive' : 'outline'}
                          className={`cursor-pointer transition-colors ${
                            isDisallowed
                              ? 'bg-red-100 text-red-800 hover:bg-red-200'
                              : isAllowed
                                ? 'opacity-50 cursor-not-allowed'
                                : 'hover:bg-red-50'
                          }`}
                          onClick={() => {
                            if (isAllowed) return;

                            const newDisallowed = isDisallowed
                              ? localConfig.disallowedGroupColumns.filter(id => id !== column.id)
                              : [...localConfig.disallowedGroupColumns, column.id as TaskColumnId];

                            updateLocalConfig({ disallowedGroupColumns: newDisallowed });
                          }}
                        >
                          {column.label}
                        </Badge>
                      );
                    })}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 样式设置 */}
          <Card>
            <CardHeader>
              <CardTitle className='text-base flex items-center gap-2'>
                <Palette className='h-4 w-4' />
                分组头部样式
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-2'>
                <Label>背景颜色</Label>
                <div className='grid grid-cols-3 gap-2'>
                  {backgroundOptions.map(option => (
                    <button
                      key={option.value}
                      type='button'
                      className={`
                        p-3 rounded-md border-2 transition-all
                        ${option.preview}
                        ${
                          localConfig.groupHeaderStyle?.backgroundColor === option.value
                            ? 'border-primary ring-2 ring-primary/20'
                            : 'border-border hover:border-primary/50'
                        }
                      `}
                      onClick={() => updateGroupHeaderStyle({ backgroundColor: option.value })}
                    >
                      <div className='text-xs font-medium'>{option.label}</div>
                    </button>
                  ))}
                </div>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label>字体大小</Label>
                  <Select
                    value={localConfig.groupHeaderStyle?.fontSize || 'text-base'}
                    onValueChange={(value: string) =>
                      updateGroupHeaderStyle({
                        fontSize: value as 'text-sm' | 'text-base' | 'text-lg',
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {fontSizeOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className='space-y-2'>
                  <Label>字体粗细</Label>
                  <Select
                    value={localConfig.groupHeaderStyle?.fontWeight || 'font-medium'}
                    onValueChange={(
                      fontWeight: 'font-normal' | 'font-medium' | 'font-semibold' | 'font-bold'
                    ) => updateGroupHeaderStyle({ fontWeight })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {fontWeightOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 预览 */}
              <div className='space-y-2'>
                <Label>预览效果</Label>
                <div
                  className={`
                    ${localConfig.groupHeaderStyle?.backgroundColor || 'bg-muted'}
                    ${localConfig.groupHeaderStyle?.textColor || 'text-foreground'}
                    ${localConfig.groupHeaderStyle?.fontSize || 'text-base'}
                    ${localConfig.groupHeaderStyle?.fontWeight || 'font-medium'}
                    ${localConfig.groupHeaderStyle?.padding || 'py-2'}
                    px-4 rounded-md border flex items-center justify-between
                  `}
                >
                  <div className='flex items-center gap-2'>
                    <ChevronDown className='h-4 w-4' />
                    <span>🏗️ 示例项目组</span>
                  </div>
                  {localConfig.showGroupStats && (
                    <div className='flex items-center gap-2'>
                      <Badge variant='secondary' className='text-xs'>
                        总计: 5
                      </Badge>
                      <Badge variant='outline' className='text-xs'>
                        已分派: 3
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleCancel}>
            取消
          </Button>
          <Button onClick={handleSave}>保存设置</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
