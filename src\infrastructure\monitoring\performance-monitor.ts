/**
 * 性能监控工具
 * 收集和分析应用性能指标
 */

// 性能指标接口
export interface PerformanceMetrics {
  // 页面加载性能
  pageLoad: {
    fcp: number; // First Contentful Paint
    lcp: number; // Largest Contentful Paint
    fid: number; // First Input Delay
    cls: number; // Cumulative Layout Shift
    ttfb: number; // Time to First Byte
  };

  // 资源加载性能
  resources: {
    totalSize: number;
    loadTime: number;
    cacheHitRate: number;
    failedRequests: number;
  };

  // 组件性能
  components: {
    renderTime: number;
    mountTime: number;
    updateCount: number;
    memoryUsage: number;
  };

  // 用户交互性能
  interactions: {
    clickResponseTime: number;
    scrollPerformance: number;
    inputLatency: number;
  };

  // 懒加载性能
  lazyLoading: {
    componentLoadTime: Map<string, number>;
    chunkLoadTime: Map<string, number>;
    preloadSuccess: number;
    preloadFailure: number;
  };
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: Map<string, PerformanceObserver> = new Map();
  private startTime: number = performance.now();
  private isEnabled: boolean = true;

  constructor() {
    this.initializeObservers();
    this.collectInitialMetrics();
  }

  /**
   * 初始化性能观察器
   */
  private initializeObservers() {
    if (typeof window === 'undefined') return;

    try {
      // Web Vitals 观察器
      if ('PerformanceObserver' in window) {
        // LCP (Largest Contentful Paint)
        const lcpObserver = new PerformanceObserver(list => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          this.updateMetric('pageLoad.lcp', lastEntry.startTime);
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.set('lcp', lcpObserver);

        // FID (First Input Delay)
        const fidObserver = new PerformanceObserver(list => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.updateMetric('pageLoad.fid', entry.processingStart - entry.startTime);
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.set('fid', fidObserver);

        // CLS (Cumulative Layout Shift)
        const clsObserver = new PerformanceObserver(list => {
          const entries = list.getEntries();
          let clsValue = 0;
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          this.updateMetric('pageLoad.cls', clsValue);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.set('cls', clsObserver);

        // 资源加载观察器
        const resourceObserver = new PerformanceObserver(list => {
          const entries = list.getEntries();
          this.analyzeResourcePerformance(entries);
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.set('resource', resourceObserver);
      }
    } catch (error) {
      console.warn('Performance observers initialization failed:', error);
    }
  }

  /**
   * 收集初始性能指标
   */
  private collectInitialMetrics() {
    if (typeof window === 'undefined') return;

    // 收集导航时间
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      this.updateMetric('pageLoad.ttfb', navigation.responseStart - navigation.requestStart);
      this.updateMetric('pageLoad.fcp', navigation.loadEventEnd - navigation.fetchStart);
    }

    // 收集内存使用情况
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.updateMetric('components.memoryUsage', memory.usedJSHeapSize);
    }
  }

  /**
   * 分析资源加载性能
   */
  private analyzeResourcePerformance(entries: PerformanceEntry[]) {
    let totalSize = 0;
    let totalLoadTime = 0;
    let cacheHits = 0;
    let failedRequests = 0;

    entries.forEach((entry: any) => {
      // 计算资源大小
      if (entry.transferSize !== undefined) {
        totalSize += entry.transferSize;

        // 检查缓存命中 (transferSize为0通常表示缓存命中)
        if (entry.transferSize === 0 && entry.decodedBodySize > 0) {
          cacheHits++;
        }
      }

      // 计算加载时间
      totalLoadTime += entry.responseEnd - entry.startTime;

      // 检查失败的请求
      if (entry.responseStatus >= 400) {
        failedRequests++;
      }
    });

    this.updateMetric('resources.totalSize', totalSize);
    this.updateMetric('resources.loadTime', totalLoadTime);
    this.updateMetric('resources.cacheHitRate', cacheHits / entries.length);
    this.updateMetric('resources.failedRequests', failedRequests);
  }

  /**
   * 更新性能指标
   */
  private updateMetric(path: string, value: number) {
    if (!this.isEnabled) return;

    const keys = path.split('.');
    let current: any = this.metrics;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (key && !current[key]) {
        current[key] = {};
      }
      if (key) {
        current = current[key];
      }
    }

    const lastKey = keys[keys.length - 1];
    if (lastKey) {
      current[lastKey] = value;
    }
  }

  /**
   * 记录组件渲染时间
   */
  recordComponentRender(componentName: string, renderTime: number) {
    if (!this.isEnabled) return;

    console.log(`🎨 组件渲染: ${componentName} - ${renderTime.toFixed(2)}ms`);

    if (!this.metrics.components) {
      this.metrics.components = {} as any;
    }

    this.updateMetric('components.renderTime', renderTime);
  }

  /**
   * 记录懒加载性能
   */
  recordLazyLoadPerformance(componentName: string, loadTime: number, success: boolean = true) {
    if (!this.isEnabled) return;

    if (!this.metrics.lazyLoading) {
      this.metrics.lazyLoading = {
        componentLoadTime: new Map(),
        chunkLoadTime: new Map(),
        preloadSuccess: 0,
        preloadFailure: 0,
      };
    }

    // 检查是否已经记录过这个组件，避免重复计数
    const hasRecorded = this.metrics.lazyLoading.componentLoadTime.has(componentName);

    if (!hasRecorded) {
      console.log(
        `⚡ 懒加载: ${componentName} - ${loadTime.toFixed(2)}ms - ${success ? '成功' : '失败'}`
      );

      this.metrics.lazyLoading.componentLoadTime.set(componentName, loadTime);

      if (success) {
        this.metrics.lazyLoading.preloadSuccess++;
      } else {
        this.metrics.lazyLoading.preloadFailure++;
      }
    }
  }

  /**
   * 记录用户交互性能
   */
  recordInteraction(type: 'click' | 'scroll' | 'input', responseTime: number) {
    if (!this.isEnabled) return;

    if (!this.metrics.interactions) {
      this.metrics.interactions = {} as any;
    }

    switch (type) {
      case 'click':
        this.updateMetric('interactions.clickResponseTime', responseTime);
        break;
      case 'scroll':
        this.updateMetric('interactions.scrollPerformance', responseTime);
        break;
      case 'input':
        this.updateMetric('interactions.inputLatency', responseTime);
        break;
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): PerformanceMetrics {
    return {
      pageLoad: {
        fcp: this.metrics.pageLoad?.fcp || 0,
        lcp: this.metrics.pageLoad?.lcp || 0,
        fid: this.metrics.pageLoad?.fid || 0,
        cls: this.metrics.pageLoad?.cls || 0,
        ttfb: this.metrics.pageLoad?.ttfb || 0,
      },
      resources: {
        totalSize: this.metrics.resources?.totalSize || 0,
        loadTime: this.metrics.resources?.loadTime || 0,
        cacheHitRate: this.metrics.resources?.cacheHitRate || 0,
        failedRequests: this.metrics.resources?.failedRequests || 0,
      },
      components: {
        renderTime: this.metrics.components?.renderTime || 0,
        mountTime: this.metrics.components?.mountTime || 0,
        updateCount: this.metrics.components?.updateCount || 0,
        memoryUsage: this.metrics.components?.memoryUsage || 0,
      },
      interactions: {
        clickResponseTime: this.metrics.interactions?.clickResponseTime || 0,
        scrollPerformance: this.metrics.interactions?.scrollPerformance || 0,
        inputLatency: this.metrics.interactions?.inputLatency || 0,
      },
      lazyLoading: this.metrics.lazyLoading || {
        componentLoadTime: new Map(),
        chunkLoadTime: new Map(),
        preloadSuccess: 0,
        preloadFailure: 0,
      },
    };
  }

  /**
   * 生成性能分析报告
   */
  generateAnalysisReport() {
    const report = this.getPerformanceReport();
    const analysis = {
      overall: 'good' as 'excellent' | 'good' | 'needs-improvement' | 'poor',
      recommendations: [] as string[],
      scores: {
        pageLoad: 0,
        resources: 0,
        interactions: 0,
        lazyLoading: 0,
      },
    };

    // 页面加载性能评分
    let pageLoadScore = 100;
    if (report.pageLoad.lcp > 2500) pageLoadScore -= 30;
    if (report.pageLoad.fid > 100) pageLoadScore -= 20;
    if (report.pageLoad.cls > 0.1) pageLoadScore -= 25;
    if (report.pageLoad.fcp > 1800) pageLoadScore -= 25;
    analysis.scores.pageLoad = Math.max(0, pageLoadScore);

    // 资源加载性能评分
    let resourceScore = 100;
    if (report.resources.cacheHitRate < 0.7) resourceScore -= 30;
    if (report.resources.failedRequests > 0) resourceScore -= 20;
    if (report.resources.totalSize > 2 * 1024 * 1024) resourceScore -= 25; // 2MB
    analysis.scores.resources = Math.max(0, resourceScore);

    // 交互性能评分
    let interactionScore = 100;
    if (report.interactions.clickResponseTime > 100) interactionScore -= 40;
    if (report.interactions.inputLatency > 50) interactionScore -= 30;
    analysis.scores.interactions = Math.max(0, interactionScore);

    // 懒加载性能评分
    const lazyLoadSuccessRate =
      report.lazyLoading.preloadSuccess /
      (report.lazyLoading.preloadSuccess + report.lazyLoading.preloadFailure);
    analysis.scores.lazyLoading = Math.round(lazyLoadSuccessRate * 100);

    // 总体评分
    const overallScore =
      (analysis.scores.pageLoad +
        analysis.scores.resources +
        analysis.scores.interactions +
        analysis.scores.lazyLoading) /
      4;

    if (overallScore >= 90) analysis.overall = 'excellent';
    else if (overallScore >= 75) analysis.overall = 'good';
    else if (overallScore >= 50) analysis.overall = 'needs-improvement';
    else analysis.overall = 'poor';

    // 生成建议
    if (analysis.scores.pageLoad < 80) {
      analysis.recommendations.push('优化页面加载性能：减少首屏资源大小，优化关键渲染路径');
    }
    if (analysis.scores.resources < 80) {
      analysis.recommendations.push('优化资源加载：启用更好的缓存策略，压缩静态资源');
    }
    if (analysis.scores.interactions < 80) {
      analysis.recommendations.push('优化交互响应：减少主线程阻塞，优化事件处理');
    }
    if (analysis.scores.lazyLoading < 80) {
      analysis.recommendations.push('优化懒加载：改进预加载策略，处理加载失败情况');
    }

    return { report, analysis };
  }

  /**
   * 启用/禁用性能监控
   */
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
    if (!enabled) {
      this.observers.forEach(observer => observer.disconnect());
      this.observers.clear();
    } else if (this.observers.size === 0) {
      this.initializeObservers();
    }
  }

  /**
   * 清理资源
   */
  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.metrics = {};
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 性能监控Hook
export function usePerformanceMonitor() {
  return {
    recordComponentRender: performanceMonitor.recordComponentRender.bind(performanceMonitor),
    recordLazyLoadPerformance:
      performanceMonitor.recordLazyLoadPerformance.bind(performanceMonitor),
    recordInteraction: performanceMonitor.recordInteraction.bind(performanceMonitor),
    getReport: performanceMonitor.getPerformanceReport.bind(performanceMonitor),
    generateAnalysis: performanceMonitor.generateAnalysisReport.bind(performanceMonitor),
  };
}
