/**
 * RatioModalManager - 统一的配比页面模态框管理器
 * 管理所有版本共同的模态框组件
 */

import React from 'react';
import type { Task } from '@/core/types';

// 通用模态框组件导入
import { SiloManagementModal } from '@/models/SiloManagementModal';
import { RatioHistoryModal } from '@/models/RatioHistoryModal';
import { MortarRatioModal } from '@/models/MortarRatioModal';
import { RatioSettingsModal } from '@/models/RatioSettingsModal';
import { RatioSelectionModal } from '@/models/RatioSelectionModal';

// V2特有模态框 - TODO: 这些组件需要创建
// import { PerformanceDashboardModal } from '@/components/modals/ratio-v2/PerformanceDashboardModal';
// import { RatioTemplateModal } from '@/components/modals/ratio-v2/RatioTemplateModal';
// import { RatioNotificationModal } from '@/components/modals/ratio-v2/RatioNotificationModal';
// import { SaveBackupRatioModal } from '@/components/modals/ratio-v2/SaveBackupRatioModal';
// import { BackupRatioSelectionModal } from '@/components/modals/ratio-v2/BackupRatioSelectionModal';
// import { AIGenerateModal } from '@/components/modals/ratio-v2/AIGenerateModal';
// import { RatioRecommendationModal } from '@/components/modals/ratio-v2/RatioRecommendationModal';

// 临时占位符组件
const PerformanceDashboardModal = ({ isOpen, onClose }: any) => null;
const RatioTemplateModal = ({ isOpen, onClose }: any) => null;
const RatioNotificationModal = ({ isOpen, onClose }: any) => null;
const SaveBackupRatioModal = ({ isOpen, onClose }: any) => null;
const BackupRatioSelectionModal = ({ isOpen, onClose }: any) => null;
const AIGenerateModal = ({ isOpen, onClose }: any) => null;
const RatioRecommendationModal = ({ isOpen, onClose }: any) => null;

// Mock数据
import {
  generateMockSiloMappings,
  generateMockRatioHistory,
} from '@/infrastructure/api/mock/mock-data';

// 临时mock函数
const generateMockRatioSelectionRecords = () => [];

/**
 * 模态框状态类型
 */
export interface RatioModalState {
  [key: string]: boolean;
}

/**
 * 模态框管理器属性
 */
export interface RatioModalManagerProps {
  modals: RatioModalState;
  task: Task | null;
  taskId: string;
  onClose: (modalName: string) => void;
  version: 'v1' | 'v2';
}

/**
 * 通用模态框配置
 */
interface ModalConfig {
  name: string;
  component: React.ComponentType<any>;
  versions: ('v1' | 'v2')[];
  props?: Record<string, any>;
}

/**
 * RatioModalManager 组件
 *
 * 功能：
 * 1. 统一管理所有版本的模态框
 * 2. 根据版本显示对应的模态框
 * 3. 提供统一的模态框状态管理
 * 4. 处理模态框间的数据传递
 */
export function RatioModalManager({
  modals,
  task,
  taskId,
  onClose,
  version,
}: RatioModalManagerProps) {
  // ==================== 通用模态框配置 ====================

  const commonModals: ModalConfig[] = [
    {
      name: 'silo',
      component: SiloManagementModal,
      versions: ['v1', 'v2'],
      props: {
        siloMappings: generateMockSiloMappings(),
      },
    },
    {
      name: 'history',
      component: RatioHistoryModal,
      versions: ['v1', 'v2'],
      props: {
        task,
        history: task ? generateMockRatioHistory(task.id) : [],
      },
    },
    {
      name: 'mortar',
      component: MortarRatioModal,
      versions: ['v1', 'v2'],
    },
    {
      name: 'settings',
      component: RatioSettingsModal,
      versions: ['v1', 'v2'],
    },
    {
      name: 'ratioSelection',
      component: RatioSelectionModal,
      versions: ['v1', 'v2'],
      props: {
        ratioRecords: generateMockRatioSelectionRecords(),
        currentTaskId: taskId,
        onSelectRatio: (ratio: any) => {
          console.log('应用配比:', ratio);
          onClose('ratioSelection');
        },
      },
    },
  ];

  // ==================== V2特有模态框配置 ====================

  const v2Modals: ModalConfig[] = [
    {
      name: 'performance',
      component: PerformanceDashboardModal,
      versions: ['v2'],
    },
    {
      name: 'template',
      component: RatioTemplateModal,
      versions: ['v2'],
    },
    {
      name: 'ratioNotification',
      component: RatioNotificationModal,
      versions: ['v2'],
      props: {
        onApply: (result: any) => {
          console.log('应用通知单:', result);
          onClose('ratioNotification');
        },
      },
    },
    {
      name: 'saveBackupRatio',
      component: SaveBackupRatioModal,
      versions: ['v2'],
      props: {
        onSave: (name: string) => {
          console.log('保存备选配比:', name);
          onClose('saveBackupRatio');
        },
      },
    },
    {
      name: 'backupRatioSelection',
      component: BackupRatioSelectionModal,
      versions: ['v2'],
      props: {
        onSelect: (ratio: any) => {
          console.log('选择备选配比:', ratio);
          onClose('backupRatioSelection');
        },
      },
    },
    {
      name: 'aiGenerate',
      component: AIGenerateModal,
      versions: ['v2'],
      props: {
        onGenerate: (requirements: any) => {
          console.log('AI生成配比:', requirements);
          onClose('aiGenerate');
        },
      },
    },
    {
      name: 'ratioRecommendation',
      component: RatioRecommendationModal,
      versions: ['v2'],
      props: {
        onSelect: (recommendation: any) => {
          console.log('选择推荐配比:', recommendation);
          onClose('ratioRecommendation');
        },
      },
    },
  ];

  // ==================== V3特有模态框配置 ====================

  // ==================== 合并所有模态框配置 ====================

  const allModals = [...commonModals, ...v2Modals];

  // ==================== 渲染模态框 ====================

  const renderModal = (config: ModalConfig) => {
    const { name, component: Component, versions, props = {} } = config;

    // 检查是否应该在当前版本显示
    if (!versions.includes(version)) {
      return null;
    }

    // 检查模态框是否打开
    if (!modals[name]) {
      return null;
    }

    // 通用属性
    const commonProps = {
      isOpen: true,
      onOpenChange: (isOpen: boolean) => !isOpen && onClose(name),
      onOpenChangeAction: (isOpen: boolean) => !isOpen && onClose(name),
      ...props,
    };

    return <Component key={name} {...commonProps} />;
  };

  // ==================== 返回渲染结果 ====================

  return <>{allModals.map(renderModal)}</>;
}

/**
 * 模态框名称常量
 * 提供类型安全的模态框名称
 */
export const MODAL_NAMES = {
  // 通用模态框
  SILO: 'silo',
  HISTORY: 'history',
  MORTAR: 'mortar',
  SETTINGS: 'settings',
  RATIO_SELECTION: 'ratioSelection',

  // V2特有模态框
  PERFORMANCE: 'performance',
  TEMPLATE: 'template',
  RATIO_NOTIFICATION: 'ratioNotification',
  SAVE_BACKUP_RATIO: 'saveBackupRatio',
  BACKUP_RATIO_SELECTION: 'backupRatioSelection',
  AI_GENERATE: 'aiGenerate',
  RATIO_RECOMMENDATION: 'ratioRecommendation',

  // V3特有模态框
  SCHEME_COMPARISON: 'schemeComparison',
  AI_OPTIMIZATION: 'aiOptimization',
  MATERIAL_LIBRARY: 'materialLibrary',
} as const;

/**
 * 模态框名称类型
 */
export type ModalName = (typeof MODAL_NAMES)[keyof typeof MODAL_NAMES];

/**
 * 获取版本支持的模态框名称
 */
export function getSupportedModals(version: 'v1' | 'v2'): ModalName[] {
  const commonModals = [
    MODAL_NAMES.SILO,
    MODAL_NAMES.HISTORY,
    MODAL_NAMES.MORTAR,
    MODAL_NAMES.SETTINGS,
    MODAL_NAMES.RATIO_SELECTION,
  ];

  switch (version) {
    case 'v1':
      return commonModals;

    case 'v2':
      return [
        ...commonModals,
        MODAL_NAMES.PERFORMANCE,
        MODAL_NAMES.TEMPLATE,
        MODAL_NAMES.RATIO_NOTIFICATION,
        MODAL_NAMES.SAVE_BACKUP_RATIO,
        MODAL_NAMES.BACKUP_RATIO_SELECTION,
        MODAL_NAMES.AI_GENERATE,
        MODAL_NAMES.RATIO_RECOMMENDATION,
      ];

    default:
      return commonModals;
  }
}
