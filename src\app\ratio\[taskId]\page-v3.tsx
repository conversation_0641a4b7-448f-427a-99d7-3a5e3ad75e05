'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'next/navigation';

import { Button } from '@/shared/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/card';
import { LoadingSpinner } from '@/shared/components/LoadingSpinner';
import { useToast } from '@/shared/hooks/use-toast';
import { generateMockTasks } from '@/infrastructure/api/mock/mock-data';
import type { Task } from '@/core/types';

// 基础配比页面组件
import {
  BaseRatioPage,
  V1_CONFIG,
} from '@/features/ratio-management/components/ratio/BaseRatioPage';
import type { BaseRatioPageRenderProps } from '@/features/ratio-management/components/ratio/BaseRatioPage';

// 配比管理Hook
import { useBaseRatioManagement } from '@/features/ratio-management/hooks/ratio/useBaseRatioManagement';

/**
 * V3配比页面头部组件
 */
function V3RatioHeader({
  task,
  onOpenModal,
  onSave,
  canSave,
  isSaving,
}: {
  task: Task | null;
  onOpenModal: (modalName: string) => void;
  onSave: () => void;
  canSave: boolean;
  isSaving: boolean;
}) {
  return (
    <Card className='h-full'>
      <CardHeader className='pb-2'>
        <div className='flex items-center justify-between'>
          <div>
            <CardTitle className='text-lg'>
              配比设计 V3 - {task?.projectName || '未知项目'}
            </CardTitle>
            <CardDescription>任务ID: {task?.id || 'N/A'}</CardDescription>
          </div>
          <div className='flex gap-2'>
            <Button variant='outline' size='sm' onClick={() => onOpenModal('history')}>
              历史记录
            </Button>
            <Button variant='outline' size='sm' onClick={() => onOpenModal('settings')}>
              设置
            </Button>
            <Button onClick={onSave} disabled={!canSave || isSaving} size='sm'>
              {isSaving ? <LoadingSpinner size='small' /> : '保存配比'}
            </Button>
          </div>
        </div>
      </CardHeader>
    </Card>
  );
}

/**
 * V3配比页面主要内容
 */
function V3RatioContent({ renderProps }: { renderProps: BaseRatioPageRenderProps }) {
  const { task, taskId, openModal } = renderProps;
  const { toast } = useToast();

  // 使用基础配比管理Hook
  const ratioManagement = useBaseRatioManagement({
    taskId,
    version: 'v2',
    autoLoad: true,
    autoSave: false, // V3版本手动保存
  });

  const {
    currentRatio,
    materials,
    calculationParams,
    calculationResults,
    isLoading,
    isCalculating,
    isSaving,
    error,
    isDirty,
    loadRatio,
    saveRatio,
    clearRatio,
    addMaterial,
    removeMaterial,
    updateMaterial,
    updateCalculationParams,
    calculate,
    canSave,
    canCalculate,
    hasUnsavedChanges,
  } = ratioManagement;

  // 保存处理
  const handleSave = useCallback(async () => {
    try {
      await saveRatio();
      toast({
        title: '保存成功',
        description: '配比数据已保存',
      });
    } catch (error) {
      toast({
        title: '保存失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      });
    }
  }, [saveRatio, toast]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className='h-screen w-screen bg-muted/20 p-2 flex items-center justify-center'>
        <div className='text-center space-y-4'>
          <LoadingSpinner size='large' />
          <p className='text-muted-foreground'>正在加载配比数据...</p>
        </div>
      </div>
    );
  }

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <div className='h-screen w-screen bg-muted/20 p-2 flex items-center justify-center'>
        <Card className='p-6'>
          <CardHeader>
            <CardTitle className='text-destructive'>配比数据加载失败</CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-muted-foreground mb-4'>{error}</p>
            <Button onClick={() => loadRatio(taskId)}>重试</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='h-screen w-screen bg-muted/20 p-2 flex flex-col gap-2'>
      {/* 页面头部 */}
      <V3RatioHeader
        task={task}
        onOpenModal={openModal}
        onSave={handleSave}
        canSave={canSave()}
        isSaving={isSaving}
      />

      {/* 主要内容区域 - V3版本使用三列四行布局 */}
      <div className='flex-1 grid grid-cols-3 grid-rows-4 gap-2'>
        {/* 第一行：筒仓管理 */}
        <Card className='col-span-1'>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm'>筒仓管理</CardTitle>
          </CardHeader>
          <CardContent className='text-center text-muted-foreground'>
            <p className='text-sm'>筒仓配置</p>
          </CardContent>
        </Card>

        {/* 第一行：计算参数 */}
        <Card className='col-span-1'>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm'>计算参数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-2 text-sm'>
              <div className='flex justify-between'>
                <span>强度等级:</span>
                <span className='font-mono'>C30</span>
              </div>
              <div className='flex justify-between'>
                <span>水胶比:</span>
                <span className='font-mono'>0.45</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 第一行：计算结果 */}
        <Card className='col-span-1'>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm'>计算结果</CardTitle>
          </CardHeader>
          <CardContent className='text-center text-muted-foreground'>
            <p className='text-sm'>计算结果</p>
          </CardContent>
        </Card>

        {/* 第二行：配比设计 */}
        <Card className='col-span-3 row-span-2'>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm'>配比设计</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-4 gap-4 h-full'>
              <div className='space-y-2'>
                <h4 className='font-medium text-sm'>水泥</h4>
                <div className='text-center text-muted-foreground'>
                  <p className='text-sm'>水泥配置</p>
                </div>
              </div>
              <div className='space-y-2'>
                <h4 className='font-medium text-sm'>砂</h4>
                <div className='text-center text-muted-foreground'>
                  <p className='text-sm'>砂配置</p>
                </div>
              </div>
              <div className='space-y-2'>
                <h4 className='font-medium text-sm'>石</h4>
                <div className='text-center text-muted-foreground'>
                  <p className='text-sm'>石配置</p>
                </div>
              </div>
              <div className='space-y-2'>
                <h4 className='font-medium text-sm'>外加剂</h4>
                <div className='text-center text-muted-foreground'>
                  <p className='text-sm'>外加剂配置</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 第四行：操作面板 */}
        <Card className='col-span-3'>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm'>操作面板</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex gap-2'>
              <Button
                size='sm'
                onClick={() => calculate()}
                disabled={!canCalculate() || isCalculating}
              >
                {isCalculating ? <LoadingSpinner size='small' /> : '计算配比'}
              </Button>
              <Button variant='outline' size='sm' onClick={() => openModal('previewRatio')}>
                预览配比单
              </Button>
              <Button variant='outline' size='sm' onClick={() => openModal('checkStandard')}>
                检查标准
              </Button>
              <Button variant='outline' size='sm' onClick={() => openModal('aiGeneration')}>
                AI生成
              </Button>
              <Button variant='outline' size='sm' onClick={() => openModal('saveAsAlternative')}>
                另存为
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

/**
 * V3配比页面 - 重构版本
 */
export default function V3RatioPage() {
  return (
    <BaseRatioPage
      config={V1_CONFIG}
      callbacks={{
        onTaskLoad: task => {
          console.log('V3页面: 任务加载完成', task);
        },
        onTaskLoadError: error => {
          console.error('V3页面: 任务加载失败', error);
        },
        onModalOpen: modalName => {
          console.log('V3页面: 打开模态框', modalName);
        },
        onModalClose: modalName => {
          console.log('V3页面: 关闭模态框', modalName);
        },
        onBeforeUnload: () => {
          return false;
        },
        onNavigateAway: targetPath => {
          console.log('V3页面: 导航到', targetPath);
        },
      }}
    >
      {renderProps => <V3RatioContent renderProps={renderProps} />}
    </BaseRatioPage>
  );
}
