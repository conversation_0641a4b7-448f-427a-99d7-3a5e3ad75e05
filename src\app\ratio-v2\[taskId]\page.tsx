'use client';

/**
 * 重构后的配比页面 - 使用分层架构
 *
 * 架构说明：
 * - 使用容器组件模式，将业务逻辑分离到Hook中
 * - UI组件只负责展示和用户交互
 * - 业务逻辑和数据操作通过专门的Hook封装
 * - 模态框管理通过专门的管理器组件处理
 */

import { ModernRatioPageContainer } from '@/features/ratio-management/components/ratio-v2/ModernRatioPageContainer';

/**
 * 重构后的配比页面主组件
 * 现在只是一个简单的容器，所有业务逻辑都已分离到专门的Hook和组件中
 */
export default function ModernRatioPageRefactored() {
  return <ModernRatioPageContainer />;
}
