'use client';

import React, { useState, useEffect } from 'react';
import { Monitor, Activity, Zap, AlertTriangle } from 'lucide-react';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Badge } from '@/shared/components/badge';

interface PerformanceStats {
  renderCount: number;
  averageRenderTime: number;
  lastRenderTime: number;
  memoryUsage?: {
    used: number;
    total: number;
    percentage: number;
  };
}

interface PerformanceDebugPanelProps {
  isVisible: boolean;
  onToggle: () => void;
  componentName: string;
  getStats?: () => PerformanceStats | null;
  onReset?: () => void;
}

export const PerformanceDebugPanel: React.FC<PerformanceDebugPanelProps> = ({
  isVisible,
  onToggle,
  componentName,
  getStats,
  onReset,
}) => {
  const [stats, setStats] = useState<PerformanceStats | null>(null);
  const [isRecording, setIsRecording] = useState(false);

  // 定期更新统计数据
  useEffect(() => {
    if (!isVisible || !getStats) return;

    const interval = setInterval(() => {
      const newStats = getStats();
      setStats(newStats);
    }, 1000);

    return () => clearInterval(interval);
  }, [isVisible, getStats]);

  // 获取内存使用情况
  useEffect(() => {
    if (!isVisible) return;

    const updateMemoryStats = () => {
      if ('memory' in performance) {
        const memInfo = (performance as any).memory;
        const used = memInfo.usedJSHeapSize / 1024 / 1024;
        const total = memInfo.totalJSHeapSize / 1024 / 1024;

        setStats(prev =>
          prev
            ? {
                ...prev,
                memoryUsage: {
                  used,
                  total,
                  percentage: (used / total) * 100,
                },
              }
            : null
        );
      }
    };

    const interval = setInterval(updateMemoryStats, 2000);
    return () => clearInterval(interval);
  }, [isVisible]);

  const handleReset = () => {
    onReset?.();
    setStats(null);
  };

  const getPerformanceLevel = (renderTime: number) => {
    if (renderTime < 5) return { level: 'excellent', color: 'bg-green-500' };
    if (renderTime < 10) return { level: 'good', color: 'bg-blue-500' };
    if (renderTime < 16) return { level: 'fair', color: 'bg-yellow-500' };
    return { level: 'poor', color: 'bg-red-500' };
  };

  if (!isVisible) {
    return (
      <Button
        onClick={onToggle}
        size='sm'
        variant='outline'
        className='fixed bottom-4 right-4 z-50 shadow-lg'
      >
        <Monitor className='w-4 h-4 mr-1' />
        性能监控
      </Button>
    );
  }

  const performanceLevel = stats ? getPerformanceLevel(stats.averageRenderTime) : null;

  return (
    <Card className='fixed bottom-4 right-4 z-50 w-80 shadow-xl'>
      <CardHeader className='pb-2'>
        <div className='flex items-center justify-between'>
          <CardTitle className='text-sm flex items-center gap-2'>
            <Activity className='w-4 h-4' />
            性能监控 - {componentName}
          </CardTitle>
          <Button onClick={onToggle} size='sm' variant='ghost'>
            ×
          </Button>
        </div>
      </CardHeader>
      <CardContent className='space-y-3'>
        {stats ? (
          <>
            {/* 渲染性能 */}
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <span className='text-xs text-muted-foreground'>渲染次数</span>
                <Badge variant='secondary'>{stats.renderCount}</Badge>
              </div>

              <div className='flex items-center justify-between'>
                <span className='text-xs text-muted-foreground'>平均渲染时间</span>
                <div className='flex items-center gap-1'>
                  {performanceLevel && (
                    <div className={`w-2 h-2 rounded-full ${performanceLevel.color}`} />
                  )}
                  <span className='text-xs font-mono'>{stats.averageRenderTime.toFixed(2)}ms</span>
                </div>
              </div>

              <div className='flex items-center justify-between'>
                <span className='text-xs text-muted-foreground'>最近渲染时间</span>
                <span className='text-xs font-mono'>{stats.lastRenderTime.toFixed(2)}ms</span>
              </div>
            </div>

            {/* 内存使用 */}
            {stats.memoryUsage && (
              <div className='space-y-2 pt-2 border-t'>
                <div className='flex items-center justify-between'>
                  <span className='text-xs text-muted-foreground'>内存使用</span>
                  <div className='flex items-center gap-1'>
                    {stats.memoryUsage.percentage > 80 && (
                      <AlertTriangle className='w-3 h-3 text-red-500' />
                    )}
                    <span className='text-xs font-mono'>
                      {stats.memoryUsage.used.toFixed(1)}MB / {stats.memoryUsage.total.toFixed(1)}MB
                    </span>
                  </div>
                </div>

                <div className='w-full bg-gray-200 rounded-full h-1.5'>
                  <div
                    className={`h-1.5 rounded-full transition-all ${
                      stats.memoryUsage.percentage > 80
                        ? 'bg-red-500'
                        : stats.memoryUsage.percentage > 60
                          ? 'bg-yellow-500'
                          : 'bg-green-500'
                    }`}
                    style={{ width: `${Math.min(stats.memoryUsage.percentage, 100)}%` }}
                  />
                </div>
              </div>
            )}

            {/* 性能建议 */}
            {stats.averageRenderTime > 16 && (
              <div className='pt-2 border-t'>
                <div className='flex items-start gap-2'>
                  <Zap className='w-3 h-3 text-yellow-500 mt-0.5' />
                  <div className='text-xs text-muted-foreground'>
                    <p className='font-medium text-yellow-600'>性能建议:</p>
                    <p>渲染时间超过16ms，可能影响用户体验</p>
                  </div>
                </div>
              </div>
            )}

            {/* 操作按钮 */}
            <div className='flex gap-2 pt-2'>
              <Button onClick={handleReset} size='sm' variant='outline' className='flex-1'>
                重置统计
              </Button>
              <Button
                onClick={() => setIsRecording(!isRecording)}
                size='sm'
                variant={isRecording ? 'destructive' : 'default'}
                className='flex-1'
              >
                {isRecording ? '停止记录' : '开始记录'}
              </Button>
            </div>
          </>
        ) : (
          <div className='text-center text-sm text-muted-foreground py-4'>暂无性能数据</div>
        )}
      </CardContent>
    </Card>
  );
};
