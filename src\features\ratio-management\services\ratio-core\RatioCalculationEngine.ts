/**
 * 配比计算引擎
 * 统一处理所有版本的配比计算逻辑
 */

import {
  RatioCoreMaterial,
  RatioCoreCalculationParams,
  RatioCoreCalculationResults,
  RatioCoreWarning,
  RatioCoreSuggestion,
  RatioCoreApiRequest,
  RatioCoreApiResponse,
  RatioCoreConfig,
  MaterialCategory,
  CalculationMethod,
} from '@/core/types/ratio-core';
import {
  computationCache,
  cacheNumericComputation,
} from '@/infrastructure/storage/cache/computation-cache';

/**
 * 配比计算引擎
 */
export class RatioCalculationEngine {
  private config: RatioCoreConfig;

  constructor(config: RatioCoreConfig) {
    this.config = config;
  }

  // ==================== 主要计算方法 ====================

  /**
   * 正向计算 - 根据参数计算材料用量（带缓存）
   */
  async calculate(
    materials: RatioCoreMaterial[],
    params: RatioCoreCalculationParams
  ): Promise<RatioCoreCalculationResults> {
    // 创建缓存键
    const cacheKey = `ratio-calculate-${JSON.stringify(materials)}-${JSON.stringify(params)}`;

    return computationCache.computeWithCache(
      cacheKey,
      async () => {
        const startTime = Date.now();

        try {
          if (this.config.useMockData) {
            return await this.mockCalculate(materials, params, startTime);
          } else {
            return await this.apiCalculate(materials, params, startTime);
          }
        } catch (error) {
          console.error('配比计算失败:', error);
          throw new Error(`配比计算失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      },
      { ttl: 30 * 60 * 1000 } // 30分钟缓存
    );
  }

  /**
   * 反向计算 - 根据材料用量反推参数
   */
  async reverseCalculate(
    materials: RatioCoreMaterial[],
    targetParams: Partial<RatioCoreCalculationParams>
  ): Promise<RatioCoreCalculationResults> {
    const startTime = Date.now();

    try {
      if (this.config.useMockData) {
        return await this.mockReverseCalculate(materials, targetParams, startTime);
      } else {
        return await this.apiReverseCalculate(materials, targetParams, startTime);
      }
    } catch (error) {
      console.error('反向计算失败:', error);
      throw new Error(`反向计算失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 优化计算 - AI辅助优化配比（带缓存）
   */
  async optimizeRatio(
    materials: RatioCoreMaterial[],
    params: RatioCoreCalculationParams,
    objectives: string[] = ['strength', 'cost', 'workability']
  ): Promise<RatioCoreCalculationResults> {
    // 创建缓存键
    const cacheKey = `ratio-optimize-${JSON.stringify(materials)}-${JSON.stringify(params)}-${objectives.join(',')}`;

    return computationCache.computeWithCache(
      cacheKey,
      async () => {
        const startTime = Date.now();

        try {
          if (this.config.useMockData) {
            return await this.mockOptimizeRatio(materials, params, objectives, startTime);
          } else {
            return await this.apiOptimizeRatio(materials, params, objectives, startTime);
          }
        } catch (error) {
          console.error('优化计算失败:', error);
          throw new Error(`优化计算失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      },
      { ttl: 60 * 60 * 1000 } // 1小时缓存（优化结果相对稳定）
    );
  }

  // ==================== API计算方法 ====================

  /**
   * API正向计算
   */
  private async apiCalculate(
    materials: RatioCoreMaterial[],
    params: RatioCoreCalculationParams,
    startTime: number
  ): Promise<RatioCoreCalculationResults> {
    const request: RatioCoreApiRequest = {
      taskId: 'temp_' + Date.now(),
      materials,
      calculationParams: params,
      calculationMethod: params.calculationMethod,
    };

    const response = await fetch(`${this.config.apiEndpoint}/calculate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.statusText}`);
    }

    const result: RatioCoreApiResponse<RatioCoreCalculationResults> = await response.json();
    if (!result.success) {
      throw new Error(result.message || 'API返回错误');
    }

    return {
      ...result.data!,
      calculationTime: Date.now() - startTime,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * API反向计算
   */
  private async apiReverseCalculate(
    materials: RatioCoreMaterial[],
    targetParams: Partial<RatioCoreCalculationParams>,
    startTime: number
  ): Promise<RatioCoreCalculationResults> {
    const request = {
      materials,
      targetParams,
      calculationMethod: 'reverse',
    };

    const response = await fetch(`${this.config.apiEndpoint}/reverse-calculate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.statusText}`);
    }

    const result: RatioCoreApiResponse<RatioCoreCalculationResults> = await response.json();
    if (!result.success) {
      throw new Error(result.message || 'API返回错误');
    }

    return {
      ...result.data!,
      calculationTime: Date.now() - startTime,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * API优化计算
   */
  private async apiOptimizeRatio(
    materials: RatioCoreMaterial[],
    params: RatioCoreCalculationParams,
    objectives: string[],
    startTime: number
  ): Promise<RatioCoreCalculationResults> {
    const request = {
      materials,
      calculationParams: params,
      objectives,
      calculationMethod: 'optimize',
    };

    const response = await fetch(`${this.config.apiEndpoint}/optimize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.statusText}`);
    }

    const result: RatioCoreApiResponse<RatioCoreCalculationResults> = await response.json();
    if (!result.success) {
      throw new Error(result.message || 'API返回错误');
    }

    return {
      ...result.data!,
      calculationTime: Date.now() - startTime,
      timestamp: new Date().toISOString(),
    };
  }

  // ==================== Mock计算方法 ====================

  /**
   * Mock正向计算
   */
  private async mockCalculate(
    materials: RatioCoreMaterial[],
    params: RatioCoreCalculationParams,
    startTime: number
  ): Promise<RatioCoreCalculationResults> {
    // 模拟计算延迟
    await new Promise(resolve => setTimeout(resolve, 800));

    const calculatedMaterials = this.performMockCalculation(materials, params);
    const totalWeight = Object.values(calculatedMaterials).reduce((sum, value) => sum + value, 0);

    return {
      totalWeight,
      materials: calculatedMaterials,
      strengthPrediction: this.predictStrength(params, calculatedMaterials),
      workabilityScore: this.calculateWorkabilityScore(params, calculatedMaterials),
      durabilityScore: this.calculateDurabilityScore(params, calculatedMaterials),
      qualityScore: this.calculateQualityScore(params, calculatedMaterials),
      warnings: this.generateWarnings(materials, params, calculatedMaterials),
      suggestions: this.generateSuggestions(materials, params, calculatedMaterials),
      costEstimate: this.calculateCost(calculatedMaterials),
      carbonFootprint: this.calculateCarbonFootprint(calculatedMaterials),
      calculationTime: Date.now() - startTime,
      calculationMethod: params.calculationMethod,
      timestamp: new Date().toISOString(),
      version: this.config.version,
    };
  }

  /**
   * Mock反向计算
   */
  private async mockReverseCalculate(
    materials: RatioCoreMaterial[],
    targetParams: Partial<RatioCoreCalculationParams>,
    startTime: number
  ): Promise<RatioCoreCalculationResults> {
    // 模拟计算延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 基于目标参数调整材料用量
    const adjustedMaterials: Record<string, number> = {};
    materials.forEach(material => {
      let adjustedValue = material.designValue;

      // 根据目标强度调整
      if (targetParams.strengthGrade) {
        const strengthFactor = targetParams.strengthGrade / 30; // 基准强度30MPa
        if (material.category === MaterialCategory.CEMENT) {
          adjustedValue *= strengthFactor;
        }
      }

      // 根据目标坍落度调整
      if (targetParams.slump) {
        const slumpFactor = targetParams.slump / 180; // 基准坍落度180mm
        if (material.category === MaterialCategory.WATER) {
          adjustedValue *= slumpFactor;
        }
      }

      adjustedMaterials[material.name] = Math.round(adjustedValue);
    });

    const totalWeight = Object.values(adjustedMaterials).reduce((sum, value) => sum + value, 0);

    return {
      totalWeight,
      materials: adjustedMaterials,
      strengthPrediction: targetParams.strengthGrade || 30,
      workabilityScore: 85,
      durabilityScore: 88,
      qualityScore: 87,
      warnings: [],
      suggestions: [
        {
          id: 'reverse_calc_suggestion',
          type: 'optimization',
          title: '反向计算建议',
          description: '根据目标参数调整了材料用量，建议进行验证试验',
          impact: 'medium',
          effort: 'low',
        },
      ],
      costEstimate: this.calculateCost(adjustedMaterials),
      carbonFootprint: this.calculateCarbonFootprint(adjustedMaterials),
      calculationTime: Date.now() - startTime,
      calculationMethod: CalculationMethod.STANDARD,
      timestamp: new Date().toISOString(),
      version: this.config.version,
    };
  }

  /**
   * Mock优化计算
   */
  private async mockOptimizeRatio(
    materials: RatioCoreMaterial[],
    params: RatioCoreCalculationParams,
    objectives: string[],
    startTime: number
  ): Promise<RatioCoreCalculationResults> {
    // 模拟AI计算延迟
    await new Promise(resolve => setTimeout(resolve, 2000));

    const optimizedMaterials = this.performOptimization(materials, params, objectives);
    const totalWeight = Object.values(optimizedMaterials).reduce((sum, value) => sum + value, 0);

    return {
      totalWeight,
      materials: optimizedMaterials,
      strengthPrediction: params.strengthGrade * 1.05, // 优化后强度提升5%
      workabilityScore: 92,
      durabilityScore: 95,
      qualityScore: 94,
      warnings: [],
      suggestions: [
        {
          id: 'ai_optimization',
          type: 'optimization',
          title: 'AI优化建议',
          description: `基于${objectives.join('、')}目标进行了配比优化`,
          impact: 'high',
          effort: 'medium',
          parameters: optimizedMaterials,
        },
      ],
      costEstimate: this.calculateCost(optimizedMaterials) * 0.95, // 优化后成本降低5%
      carbonFootprint: this.calculateCarbonFootprint(optimizedMaterials) * 0.9, // 碳足迹降低10%
      calculationTime: Date.now() - startTime,
      calculationMethod: CalculationMethod.STANDARD,
      timestamp: new Date().toISOString(),
      version: this.config.version,
    };
  }

  // ==================== 计算辅助方法 ====================

  /**
   * 执行Mock计算
   */
  private performMockCalculation(
    materials: RatioCoreMaterial[],
    params: RatioCoreCalculationParams
  ): Record<string, number> {
    const result: Record<string, number> = {};

    materials.forEach(material => {
      let calculatedValue = material.designValue;

      // 根据计算参数调整材料用量
      switch (material.category) {
        case MaterialCategory.CEMENT:
          calculatedValue = params.cementAmount || material.designValue;
          break;
        case MaterialCategory.WATER:
          calculatedValue = params.waterAmount || material.designValue;
          break;
        case MaterialCategory.SAND:
          // 根据砂率计算砂用量
          const totalAggregate = 1850; // 假设总骨料用量
          calculatedValue = totalAggregate * (params.sandRatio / 100);
          break;
        case MaterialCategory.GRAVEL:
          // 根据砂率计算石子用量
          const totalAgg = 1850;
          calculatedValue = totalAgg * (1 - params.sandRatio / 100);
          break;
        default:
          calculatedValue = material.designValue;
      }

      result[material.name] = Math.round(calculatedValue);
    });

    return result;
  }

  /**
   * 执行优化计算
   */
  private performOptimization(
    materials: RatioCoreMaterial[],
    _params: RatioCoreCalculationParams,
    objectives: string[]
  ): Record<string, number> {
    const result: Record<string, number> = {};

    materials.forEach(material => {
      let optimizedValue = material.designValue;

      // 根据优化目标调整
      if (objectives.includes('cost')) {
        // 成本优化：适当减少水泥用量，增加掺合料
        if (material.category === MaterialCategory.CEMENT) {
          optimizedValue *= 0.95;
        } else if (material.category === MaterialCategory.FLY_ASH) {
          optimizedValue *= 1.2;
        }
      }

      if (objectives.includes('strength')) {
        // 强度优化：增加水泥用量，降低水胶比
        if (material.category === MaterialCategory.CEMENT) {
          optimizedValue *= 1.05;
        } else if (material.category === MaterialCategory.WATER) {
          optimizedValue *= 0.95;
        }
      }

      if (objectives.includes('workability')) {
        // 工作性优化：适当增加用水量和外加剂
        if (material.category === MaterialCategory.WATER) {
          optimizedValue *= 1.02;
        } else if (material.category === MaterialCategory.ADMIXTURE) {
          optimizedValue *= 1.1;
        }
      }

      result[material.name] = Math.round(optimizedValue);
    });

    return result;
  }

  // ==================== 性能评估方法 ====================

  private predictStrength(
    _params: RatioCoreCalculationParams,
    materials: Record<string, number>
  ): number {
    // 简化的强度预测模型
    const cementAmount =
      Object.entries(materials).find(([name]) => name.includes('水泥'))?.[1] || 400;
    const waterAmount = Object.entries(materials).find(([name]) => name.includes('水'))?.[1] || 180;
    const wcRatio = waterAmount / cementAmount;

    // Abrams定律的简化版本
    const baseStrength = 45 / (wcRatio + 0.5);
    return Math.round(baseStrength * 10) / 10;
  }

  private calculateWorkabilityScore(
    params: RatioCoreCalculationParams,
    materials: Record<string, number>
  ): number {
    // 基于坍落度和材料配比的工作性评分
    const slumpScore = Math.min((params.slump / 200) * 100, 100);
    const waterScore = Math.min(
      Object.entries(materials).find(([name]) => name.includes('水'))?.[1] || (0 / 200) * 100,
      100
    );
    return Math.round((slumpScore + waterScore) / 2);
  }

  private calculateDurabilityScore(
    _params: RatioCoreCalculationParams,
    materials: Record<string, number>
  ): number {
    // 基于水胶比和环境等级的耐久性评分
    const cementAmount =
      Object.entries(materials).find(([name]) => name.includes('水泥'))?.[1] || 400;
    const waterAmount = Object.entries(materials).find(([name]) => name.includes('水'))?.[1] || 180;
    const wcRatio = waterAmount / cementAmount;

    let score = 100 - (wcRatio - 0.3) * 200; // 水胶比越低，耐久性越好
    score = Math.max(score, 60); // 最低60分
    return Math.round(score);
  }

  private calculateQualityScore(
    params: RatioCoreCalculationParams,
    materials: Record<string, number>
  ): number {
    const strengthScore = (this.predictStrength(params, materials) / params.strengthGrade) * 100;
    const workabilityScore = this.calculateWorkabilityScore(params, materials);
    const durabilityScore = this.calculateDurabilityScore(params, materials);

    return Math.round((strengthScore + workabilityScore + durabilityScore) / 3);
  }

  private generateWarnings(
    _materials: RatioCoreMaterial[],
    params: RatioCoreCalculationParams,
    calculatedMaterials: Record<string, number>
  ): RatioCoreWarning[] {
    const warnings: RatioCoreWarning[] = [];

    // 检查水胶比
    const cementAmount =
      Object.entries(calculatedMaterials).find(([name]) => name.includes('水泥'))?.[1] || 400;
    const waterAmount =
      Object.entries(calculatedMaterials).find(([name]) => name.includes('水'))?.[1] || 180;
    const wcRatio = waterAmount / cementAmount;

    if (wcRatio > 0.6) {
      warnings.push({
        id: 'high_wc_ratio',
        type: 'warning',
        severity: 'high',
        message: `水胶比过高 (${wcRatio.toFixed(2)})，可能影响强度和耐久性`,
        field: 'waterCementRatio',
        suggestion: '建议降低用水量或增加胶凝材料用量',
      });
    }

    // 检查砂率
    if (params.sandRatio > 45) {
      warnings.push({
        id: 'high_sand_ratio',
        type: 'warning',
        severity: 'medium',
        message: `砂率过高 (${params.sandRatio}%)，可能影响混凝土强度`,
        field: 'sandRatio',
        suggestion: '建议将砂率控制在35-40%之间',
      });
    }

    return warnings;
  }

  private generateSuggestions(
    _materials: RatioCoreMaterial[],
    _params: RatioCoreCalculationParams,
    calculatedMaterials: Record<string, number>
  ): RatioCoreSuggestion[] {
    const suggestions: RatioCoreSuggestion[] = [];

    // 成本优化建议
    const cementAmount =
      Object.entries(calculatedMaterials).find(([name]) => name.includes('水泥'))?.[1] || 400;
    if (cementAmount > 450) {
      suggestions.push({
        id: 'cost_optimization',
        type: 'cost_reduction',
        title: '成本优化建议',
        description: '可考虑使用部分掺合料替代水泥，降低成本',
        impact: 'medium',
        effort: 'low',
        parameters: {
          cementReduction: 50,
          flyAshAddition: 40,
        },
      });
    }

    return suggestions;
  }

  private calculateCost(materials: Record<string, number>): number {
    // 使用缓存的成本计算
    const cacheKey = `cost-${JSON.stringify(materials)}`;
    const cached = computationCache.computeWithCache(
      cacheKey,
      () => {
        // 简化的成本计算（元/m³）
        const prices: Record<string, number> = {
          水泥: 0.45,
          水: 0.003,
          砂: 0.08,
          石: 0.06,
          外加剂: 8.0,
        };

        let totalCost = 0;
        Object.entries(materials).forEach(([name, amount]) => {
          const price = Object.entries(prices).find(([key]) => name.includes(key))?.[1] || 0.1;
          totalCost += amount * price;
        });

        return Math.round(totalCost * 100) / 100;
      },
      { ttl: 24 * 60 * 60 * 1000 } // 24小时缓存
    );

    // 由于这是同步方法，我们需要处理Promise
    if (cached instanceof Promise) {
      // 对于同步调用，返回一个默认值或抛出错误
      console.warn('Cost calculation cache returned Promise in sync context');
      return 0;
    }

    return cached as number;
  }

  private calculateCarbonFootprint(materials: Record<string, number>): number {
    // 使用缓存的碳足迹计算
    const cacheKey = `carbon-${JSON.stringify(materials)}`;
    const cached = computationCache.computeWithCache(
      cacheKey,
      () => {
        // 简化的碳足迹计算（kg CO2/m³）
        const carbonFactors: Record<string, number> = {
          水泥: 0.82,
          水: 0.001,
          砂: 0.005,
          石: 0.003,
          外加剂: 0.5,
        };

        let totalCarbon = 0;
        Object.entries(materials).forEach(([name, amount]) => {
          const factor =
            Object.entries(carbonFactors).find(([key]) => name.includes(key))?.[1] || 0.01;
          totalCarbon += amount * factor;
        });

        return Math.round(totalCarbon * 100) / 100;
      },
      { ttl: 24 * 60 * 60 * 1000 } // 24小时缓存
    );

    // 处理Promise返回值
    if (cached instanceof Promise) {
      console.warn('Carbon footprint calculation cache returned Promise in sync context');
      return 0;
    }

    return cached as number;
  }
}
