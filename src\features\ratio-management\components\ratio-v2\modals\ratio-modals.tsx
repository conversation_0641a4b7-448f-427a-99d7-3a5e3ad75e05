'use client';

import {
  BarChart3,
  Database,
  FileText,
  History,
  Layers,
  Settings,
  TrendingUp,
  Clock,
  Users,
  Activity,
  Target,
  Zap,
  Download,
  Share,
  Eye,
  Printer,
} from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Badge } from '@/shared/components/badge';
import type { Task } from '@/core/types';
import type { RatioMaterial, CalculationResults } from '@/core/types/ratio';

// 性能监控模态框
interface PerformanceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PerformanceModal({ open, onOpenChange }: PerformanceModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-4xl'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <BarChart3 className='h-5 w-5' />
            性能监控
          </DialogTitle>
          <DialogDescription>查看搅拌站实时性能数据和历史趋势</DialogDescription>
        </DialogHeader>
        <div className='grid grid-cols-2 gap-4'>
          <Card>
            <CardHeader>
              <CardTitle className='text-sm'>实时效率</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold text-green-600'>95%</div>
              <p className='text-xs text-muted-foreground'>较昨日提升 2%</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className='text-sm'>产量统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>1,250m³</div>
              <p className='text-xs text-muted-foreground'>今日累计产量</p>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// 砂浆配比模态框
interface MortarRatioModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function MortarRatioModal({ open, onOpenChange }: MortarRatioModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Layers className='h-5 w-5' />
            砂浆配比
          </DialogTitle>
          <DialogDescription>管理和配置砂浆配比方案</DialogDescription>
        </DialogHeader>
        <div className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle className='text-sm'>M5砂浆</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-sm text-muted-foreground'>水泥:砂 = 1:5.23</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className='text-sm'>M7.5砂浆</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-sm text-muted-foreground'>水泥:砂 = 1:4.82</p>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// 历史记录模态框
interface HistoryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function HistoryModal({ open, onOpenChange }: HistoryModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-3xl'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <History className='h-5 w-5' />
            历史记录
          </DialogTitle>
          <DialogDescription>查看配比历史记录和变更日志</DialogDescription>
        </DialogHeader>
        <div className='space-y-2'>
          {[
            { time: '2024-01-15 14:30', action: '创建配比', user: '张工程师' },
            { time: '2024-01-15 15:45', action: '修改水胶比', user: '李技术员' },
            { time: '2024-01-15 16:20', action: '保存配比', user: '王主管' },
          ].map((record, index) => (
            <div key={index} className='flex items-center justify-between p-2 border rounded'>
              <div className='flex items-center gap-2'>
                <Clock className='h-4 w-4 text-muted-foreground' />
                <span className='text-sm'>{record.action}</span>
              </div>
              <div className='text-xs text-muted-foreground'>
                {record.time} - {record.user}
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}

// 配比模板模态框
interface TemplateModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TemplateModal({ open, onOpenChange }: TemplateModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            配比模板
          </DialogTitle>
          <DialogDescription>管理和使用配比模板</DialogDescription>
        </DialogHeader>
        <div className='grid grid-cols-2 gap-4'>
          {['C30标准配比', 'C35高强配比', 'C40特殊配比'].map((template, index) => (
            <Card key={index} className='cursor-pointer hover:bg-gray-50'>
              <CardHeader>
                <CardTitle className='text-sm'>{template}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className='text-xs text-muted-foreground'>点击应用此模板</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}

// 料仓管理模态框
interface SiloManagementModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function SiloManagementModal({ open, onOpenChange }: SiloManagementModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-4xl'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Database className='h-5 w-5' />
            料仓管理
          </DialogTitle>
          <DialogDescription>管理料仓状态和库存信息</DialogDescription>
        </DialogHeader>
        <div className='grid grid-cols-3 gap-4'>
          {[
            { name: '1#水泥仓', status: '正常', level: 75 },
            { name: '2#水泥仓', status: '偏低', level: 40 },
            { name: '1#砂仓', status: '正常', level: 83 },
          ].map((silo, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className='text-sm'>{silo.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='flex items-center justify-between'>
                  <Badge variant={silo.status === '正常' ? 'default' : 'destructive'}>
                    {silo.status}
                  </Badge>
                  <span className='text-sm'>{silo.level}%</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}

// 系统设置模态框
interface SettingsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function SettingsModal({ open, onOpenChange }: SettingsModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Settings className='h-5 w-5' />
            系统设置
          </DialogTitle>
          <DialogDescription>配置系统参数和用户偏好</DialogDescription>
        </DialogHeader>
        <div className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle className='text-sm'>界面设置</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-sm text-muted-foreground'>主题、语言、显示密度等</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className='text-sm'>计算设置</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-sm text-muted-foreground'>精度、单位、默认参数等</p>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// 预览配比单模态框
interface PreviewRatioModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task: Task | null;
  materials: RatioMaterial[];
  calculationResults: CalculationResults | null;
}

export function PreviewRatioModal({
  open,
  onOpenChange,
  task,
  materials,
  calculationResults,
}: PreviewRatioModalProps) {
  const handlePrint = () => {
    window.print();
  };

  const handleExport = () => {
    // 导出功能实现
    console.log('导出配比单');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-4xl max-h-[80vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Eye className='h-5 w-5' />
            预览配比单
          </DialogTitle>
          <DialogDescription>查看和打印配比单</DialogDescription>
        </DialogHeader>

        <div className='space-y-6 print:space-y-4'>
          {/* 配比单头部 */}
          <div className='text-center border-b pb-4'>
            <h1 className='text-2xl font-bold'>混凝土配比单</h1>
            <p className='text-sm text-muted-foreground mt-2'>
              任务编号: {task?.taskNumber || 'N/A'} | 强度等级: {task?.strength || 'N/A'}
            </p>
          </div>

          {/* 基本信息 */}
          <div className='grid grid-cols-2 gap-4'>
            <Card>
              <CardHeader>
                <CardTitle className='text-sm'>工程信息</CardTitle>
              </CardHeader>
              <CardContent className='space-y-2'>
                <div className='flex justify-between'>
                  <span className='text-sm text-muted-foreground'>工程名称:</span>
                  <span className='text-sm'>{task?.projectName || 'N/A'}</span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-sm text-muted-foreground'>施工部位:</span>
                  <span className='text-sm'>{task?.constructionSite || 'N/A'}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className='text-sm'>配比信息</CardTitle>
              </CardHeader>
              <CardContent className='space-y-2'>
                <div className='flex justify-between'>
                  <span className='text-sm text-muted-foreground'>总重量:</span>
                  <span className='text-sm'>{calculationResults?.totalWeight || 0} kg/m³</span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-sm text-muted-foreground'>预测强度:</span>
                  <span className='text-sm'>{calculationResults?.strengthPrediction || 0} MPa</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 材料清单 */}
          <Card>
            <CardHeader>
              <CardTitle className='text-sm'>材料清单</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='overflow-x-auto'>
                <table className='w-full text-sm'>
                  <thead>
                    <tr className='border-b'>
                      <th className='text-left p-2'>材料名称</th>
                      <th className='text-right p-2'>理论量(kg/m³)</th>
                      <th className='text-right p-2'>含水率(%)</th>
                      <th className='text-right p-2'>实际量(kg/m³)</th>
                    </tr>
                  </thead>
                  <tbody>
                    {materials.map((material, index) => (
                      <tr key={index} className='border-b'>
                        <td className='p-2'>{material.name}</td>
                        <td className='text-right p-2'>{material.theoreticalAmount || 0}</td>
                        <td className='text-right p-2'>{material.waterContent || 0}</td>
                        <td className='text-right p-2'>{material.actualAmount || 0}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className='flex justify-end gap-2 print:hidden'>
            <Button variant='outline' onClick={handlePrint}>
              <Printer className='h-4 w-4 mr-2' />
              打印
            </Button>
            <Button variant='outline' onClick={handleExport}>
              <Download className='h-4 w-4 mr-2' />
              导出
            </Button>
            <Button onClick={() => onOpenChange(false)}>关闭</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
