'use client';

import React, { useCallback, useState } from 'react';

import { DndContext, DragEndEvent } from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  AlertTriangle,
  Building2,
  Calendar,
  Clock,
  Droplets,
  Eye,
  EyeOff,
  FileText,
  GripVertical,
  Hash,
  Layout,
  MapPin,
  Move3D,
  Palette,
  Phone,
  Settings2,
  Target,
  Timer,
  User,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Label } from '@/shared/components/label';
import { Switch } from '@/shared/components/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/tabs';
import { cn } from '@/core/lib/utils';

// 字段配置接口
interface FieldConfig {
  id: string;
  label: string;
  visible: boolean;
  order: number;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  weight?: 'normal' | 'medium' | 'bold';
}

// 卡片布局配置
export interface CardLayoutConfig {
  topFields: FieldConfig[]; // 顶部4个字段
  middleFields: FieldConfig[]; // 中间6个字段
  showVehicleArea: boolean; // 是否显示车辆区域
  cardSize: 'small' | 'medium' | 'large';
  spacing: 'tight' | 'normal' | 'loose';
  theme: 'default' | 'modern' | 'glass' | 'gradient';
}

interface CardLayoutConfigModalProps {
  open: boolean;
  onOpenChangeAction: (open: boolean) => void;
  config: CardLayoutConfig;
  onConfigChangeAction: (config: CardLayoutConfig) => void;
  onSaveAction: (config: CardLayoutConfig) => void;
  onCancelAction: () => void;
}

// 可用字段定义
const AVAILABLE_FIELDS: FieldConfig[] = [
  { id: 'taskNumber', label: '任务编号', visible: true, order: 0 },
  { id: 'constructionSite', label: '施工地点', visible: true, order: 1 },
  { id: 'customerName', label: '客户名称', visible: true, order: 2 },
  { id: 'status', label: '任务状态', visible: true, order: 3 },
  { id: 'requiredVolume', label: '需求方量', visible: true, order: 4 },
  { id: 'completedVolume', label: '完成方量', visible: true, order: 5 },
  { id: 'progress', label: '完成进度', visible: true, order: 6 },
  { id: 'scheduledTime', label: '计划时间', visible: true, order: 7 },
  { id: 'estimatedDuration', label: '预计时长', visible: true, order: 8 },
  { id: 'contactPhone', label: '联系电话', visible: false, order: 9 },
  { id: 'notes', label: '备注信息', visible: false, order: 10 },
  { id: 'address', label: '详细地址', visible: false, order: 11 },
  { id: 'createdAt', label: '创建时间', visible: false, order: 12 },
];

// 字段图标映射
const FIELD_ICONS: Record<string, React.ReactNode> = {
  taskNumber: <Hash className='w-4 h-4' />,
  constructionSite: <Building2 className='w-4 h-4' />,
  customerName: <User className='w-4 h-4' />,
  status: <AlertTriangle className='w-4 h-4' />,
  requiredVolume: <Target className='w-4 h-4' />,
  completedVolume: <Droplets className='w-4 h-4' />,
  progress: <Target className='w-4 h-4' />,
  scheduledTime: <Calendar className='w-4 h-4' />,
  estimatedDuration: <Timer className='w-4 h-4' />,
  contactPhone: <Phone className='w-4 h-4' />,
  notes: <FileText className='w-4 h-4' />,
  address: <MapPin className='w-4 h-4' />,
  createdAt: <Clock className='w-4 h-4' />,
};

// Sortable field item component
interface SortableFieldItemProps {
  field: FieldConfig;
  section: 'topFields' | 'middleFields';
  onToggleVisibility: (fieldId: string, section: 'topFields' | 'middleFields') => void;
  onRemove: (fieldId: string, section: 'topFields' | 'middleFields') => void;
}

const SortableFieldItem: React.FC<SortableFieldItemProps> = ({
  field,
  section,
  onToggleVisibility,
  onRemove,
}) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: field.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className={cn(
        'flex items-center gap-3 p-2 bg-background border rounded-md',
        isDragging && 'shadow-lg ring-2 ring-primary/50 opacity-50'
      )}
    >
      <div {...listeners} className='cursor-grab active:cursor-grabbing'>
        <GripVertical className='w-4 h-4 text-muted-foreground' />
      </div>

      <div className='flex-shrink-0'>
        {FIELD_ICONS[field.id] || <FileText className='w-4 h-4' />}
      </div>

      <div className='flex-1 min-w-0'>
        <div className='font-medium text-sm truncate'>{field.label}</div>
        <div className='text-xs text-muted-foreground'>{field.id}</div>
      </div>

      <div className='flex items-center gap-2'>
        <Button
          variant='ghost'
          size='sm'
          onClick={() => onToggleVisibility(field.id, section)}
          className='h-6 w-6 p-0'
        >
          {field.visible ? <Eye className='w-3 h-3' /> : <EyeOff className='w-3 h-3' />}
        </Button>

        <Button
          variant='ghost'
          size='sm'
          onClick={() => onRemove(field.id, section)}
          className='h-6 w-6 p-0 text-destructive hover:text-destructive'
        >
          ×
        </Button>
      </div>
    </div>
  );
};

export const CardLayoutConfigModal: React.FC<CardLayoutConfigModalProps> = ({
  open,
  onOpenChangeAction,
  config,
  onConfigChangeAction,
  onSaveAction,
  onCancelAction,
}) => {
  const [localConfig, setLocalConfig] = useState<CardLayoutConfig>(config);

  // 处理字段拖拽排序
  const handleFieldDragEnd = useCallback(
    (event: DragEndEvent, section: 'topFields' | 'middleFields') => {
      const { active, over } = event;

      if (!over || active.id === over.id) return;

      const items = Array.from(localConfig[section]);
      const oldIndex = items.findIndex(item => item.id === active.id);
      const newIndex = items.findIndex(item => item.id === over.id);

      if (oldIndex === -1 || newIndex === -1) return;

      const reorderedItems = arrayMove(items, oldIndex, newIndex);

      // 更新order
      const updatedItems = reorderedItems.map((item, index) => ({
        ...item,
        order: index,
      }));

      setLocalConfig(prev => ({
        ...prev,
        [section]: updatedItems,
      }));
    },
    [localConfig]
  );

  // 切换字段可见性
  const toggleFieldVisibility = useCallback(
    (fieldId: string, section: 'topFields' | 'middleFields') => {
      setLocalConfig(prev => ({
        ...prev,
        [section]: prev[section].map(field =>
          field.id === fieldId ? { ...field, visible: !field.visible } : field
        ),
      }));
    },
    []
  );

  // 添加字段到区域
  const addFieldToSection = useCallback(
    (fieldId: string, section: 'topFields' | 'middleFields') => {
      const availableField = AVAILABLE_FIELDS.find(f => f.id === fieldId);
      if (!availableField) return;

      const maxOrder = Math.max(...localConfig[section].map(f => f.order), -1);
      const newField: FieldConfig = {
        ...availableField,
        order: maxOrder + 1,
        visible: true,
      };

      setLocalConfig(prev => ({
        ...prev,
        [section]: [...prev[section], newField],
      }));
    },
    [localConfig]
  );

  // 从区域移除字段
  const removeFieldFromSection = useCallback(
    (fieldId: string, section: 'topFields' | 'middleFields') => {
      setLocalConfig(prev => ({
        ...prev,
        [section]: prev[section].filter(field => field.id !== fieldId),
      }));
    },
    []
  );

  // 渲染字段列表
  const renderFieldList = useCallback(
    (fields: FieldConfig[], section: 'topFields' | 'middleFields', maxFields: number) => {
      const fieldIds = fields.map(f => f.id);

      return (
        <DndContext onDragEnd={event => handleFieldDragEnd(event, section)}>
          <SortableContext items={fieldIds} strategy={verticalListSortingStrategy}>
            <div
              className={cn(
                'space-y-2 min-h-[100px] p-3 border-2 border-dashed rounded-lg transition-colors',
                'border-muted-foreground/30'
              )}
            >
              <div className='flex items-center justify-between text-sm text-muted-foreground'>
                <span>
                  {section === 'topFields' ? '顶部字段' : '中间字段'} ({fields.length}/{maxFields})
                </span>
                {fields.length >= maxFields && (
                  <Badge variant='secondary' className='text-xs'>
                    已满
                  </Badge>
                )}
              </div>

              {fields.map(field => (
                <SortableFieldItem
                  key={field.id}
                  field={field}
                  section={section}
                  onToggleVisibility={toggleFieldVisibility}
                  onRemove={removeFieldFromSection}
                />
              ))}

              {fields.length === 0 && (
                <div className='text-center text-sm text-muted-foreground py-4'>
                  从右侧拖拽字段到此处
                </div>
              )}
            </div>
          </SortableContext>
        </DndContext>
      );
    },
    [handleFieldDragEnd, toggleFieldVisibility, removeFieldFromSection]
  );

  // 获取未使用的字段
  const getUnusedFields = useCallback(() => {
    const usedFieldIds = new Set([
      ...localConfig.topFields.map(f => f.id),
      ...localConfig.middleFields.map(f => f.id),
    ]);

    return AVAILABLE_FIELDS.filter(field => !usedFieldIds.has(field.id));
  }, [localConfig]);

  return (
    <Dialog open={open} onOpenChange={onOpenChangeAction}>
      <DialogContent className='max-w-6xl max-h-[90vh] overflow-hidden'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Layout className='w-5 h-5' />
            卡片布局配置
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue='layout' className='flex-1 overflow-hidden'>
          <TabsList className='grid w-full grid-cols-3'>
            <TabsTrigger value='layout' className='flex items-center gap-2'>
              <Move3D className='w-4 h-4' />
              字段布局
            </TabsTrigger>
            <TabsTrigger value='appearance' className='flex items-center gap-2'>
              <Palette className='w-4 h-4' />
              外观设置
            </TabsTrigger>
            <TabsTrigger value='preview' className='flex items-center gap-2'>
              <Eye className='w-4 h-4' />
              预览效果
            </TabsTrigger>
          </TabsList>

          <TabsContent value='layout' className='flex-1 overflow-hidden'>
            <div className='grid grid-cols-3 gap-6 h-full'>
              {/* 顶部字段配置 */}
              <div className='space-y-4'>
                <h3 className='font-semibold text-lg'>顶部字段 (最多4个)</h3>
                {renderFieldList(localConfig.topFields, 'topFields', 4)}
              </div>

              {/* 中间字段配置 */}
              <div className='space-y-4'>
                <h3 className='font-semibold text-lg'>中间字段 (最多6个)</h3>
                {renderFieldList(localConfig.middleFields, 'middleFields', 6)}
              </div>

              {/* 可用字段 */}
              <div className='space-y-4'>
                <h3 className='font-semibold text-lg'>可用字段</h3>
                <div className='space-y-2 max-h-[400px] overflow-y-auto'>
                  {getUnusedFields().map(field => (
                    <div
                      key={field.id}
                      className='flex items-center gap-3 p-2 bg-muted/50 border rounded-md cursor-pointer hover:bg-muted'
                    >
                      <div className='flex-shrink-0'>
                        {FIELD_ICONS[field.id] || <FileText className='w-4 h-4' />}
                      </div>
                      <div className='flex-1 min-w-0'>
                        <div className='font-medium text-sm'>{field.label}</div>
                        <div className='text-xs text-muted-foreground'>{field.id}</div>
                      </div>
                      <div className='flex gap-1'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => addFieldToSection(field.id, 'topFields')}
                          disabled={localConfig.topFields.length >= 4}
                          className='h-6 px-2 text-xs'
                        >
                          顶部
                        </Button>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => addFieldToSection(field.id, 'middleFields')}
                          disabled={localConfig.middleFields.length >= 6}
                          className='h-6 px-2 text-xs'
                        >
                          中间
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value='appearance' className='space-y-6'>
            {/* 外观设置内容 */}
            <div className='grid grid-cols-2 gap-6'>
              <div className='space-y-4'>
                <h3 className='font-semibold'>卡片设置</h3>

                {/* 卡片尺寸 */}
                <div className='space-y-2'>
                  <Label>卡片尺寸</Label>
                  <div className='grid grid-cols-3 gap-2'>
                    {(['small', 'medium', 'large'] as const).map(size => (
                      <Button
                        key={size}
                        variant={localConfig.cardSize === size ? 'default' : 'outline'}
                        size='sm'
                        onClick={() => setLocalConfig(prev => ({ ...prev, cardSize: size }))}
                      >
                        {size === 'small' ? '小' : size === 'medium' ? '中' : '大'}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* 间距设置 */}
                <div className='space-y-2'>
                  <Label>间距设置</Label>
                  <div className='grid grid-cols-3 gap-2'>
                    {(['tight', 'normal', 'loose'] as const).map(spacing => (
                      <Button
                        key={spacing}
                        variant={localConfig.spacing === spacing ? 'default' : 'outline'}
                        size='sm'
                        onClick={() => setLocalConfig(prev => ({ ...prev, spacing }))}
                      >
                        {spacing === 'tight' ? '紧凑' : spacing === 'normal' ? '标准' : '宽松'}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              <div className='space-y-4'>
                <h3 className='font-semibold'>主题设置</h3>

                {/* 主题选择 */}
                <div className='space-y-2'>
                  <Label>卡片主题</Label>
                  <div className='grid grid-cols-2 gap-2'>
                    {(['default', 'modern', 'glass', 'gradient'] as const).map(theme => (
                      <Button
                        key={theme}
                        variant={localConfig.theme === theme ? 'default' : 'outline'}
                        size='sm'
                        onClick={() => setLocalConfig(prev => ({ ...prev, theme }))}
                      >
                        {theme === 'default'
                          ? '默认'
                          : theme === 'modern'
                            ? '现代'
                            : theme === 'glass'
                              ? '玻璃'
                              : '渐变'}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* 车辆区域 */}
                <div className='flex items-center space-x-2'>
                  <Switch
                    id='show-vehicle-area'
                    checked={localConfig.showVehicleArea}
                    onCheckedChange={checked =>
                      setLocalConfig(prev => ({ ...prev, showVehicleArea: checked }))
                    }
                  />
                  <Label htmlFor='show-vehicle-area'>显示车辆区域</Label>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value='preview' className='space-y-4'>
            {/* 预览内容 */}
            <div className='p-4 border rounded-lg bg-muted/20'>
              <h4 className='font-medium mb-3'>配置预览</h4>
              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span>卡片尺寸:</span>
                  <span>
                    {localConfig.cardSize === 'small'
                      ? '小'
                      : localConfig.cardSize === 'medium'
                        ? '中'
                        : '大'}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>间距:</span>
                  <span>
                    {localConfig.spacing === 'tight'
                      ? '紧凑'
                      : localConfig.spacing === 'normal'
                        ? '标准'
                        : '宽松'}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>主题:</span>
                  <span>
                    {localConfig.theme === 'default'
                      ? '默认'
                      : localConfig.theme === 'modern'
                        ? '现代'
                        : localConfig.theme === 'glass'
                          ? '玻璃'
                          : '渐变'}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>车辆区域:</span>
                  <span>{localConfig.showVehicleArea ? '显示' : '隐藏'}</span>
                </div>
                <div className='flex justify-between'>
                  <span>顶部字段:</span>
                  <span>{localConfig.topFields.filter(f => f.visible).length} 个</span>
                </div>
                <div className='flex justify-between'>
                  <span>中间字段:</span>
                  <span>{localConfig.middleFields.filter(f => f.visible).length} 个</span>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* 底部按钮 */}
        <div className='flex items-center justify-between pt-4 border-t'>
          <div className='text-sm text-muted-foreground'>
            顶部: {localConfig.topFields.filter(f => f.visible).length}/4 字段 | 中间:{' '}
            {localConfig.middleFields.filter(f => f.visible).length}/6 字段
          </div>

          <div className='flex gap-2'>
            <Button variant='outline' onClick={onCancelAction}>
              取消
            </Button>
            <Button onClick={() => onSaveAction(localConfig)}>保存配置</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
