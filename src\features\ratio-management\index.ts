/**
 * 配比模块统一入口
 * 将配比相关功能独立打包，实现按需加载
 */

// ==================== 组件导出 ====================

// 配比页面相关 - 暂时注释掉不存在的组件
// export { default as RatioV1Page } from '@/components/pages/ratio/RatioV1Page';
// export { default as RatioV2Page } from '@/components/pages/ratio-v2/RatioV2Page';
// export { default as RatioCalculationEngine } from '@/components/pages/ratio-v2/enhanced-calculation-engine';

// 配比组件 - 暂时注释掉不存在的组件
// export { default as MaterialSelector } from '@/features/ratio-management/components/MaterialSelector';
// export { default as RatioForm } from '@/features/ratio-management/components/RatioForm';
// export { default as CalculationResults } from '@/features/ratio-management/components/CalculationResults';

// ==================== Store导出 ====================

export { useRatioStore } from '@/features/ratio-management/store/ratioStore';
export { useRatioDataStore } from '@/features/ratio-management/store/ratioDataStore';
export { useRatioUIStore } from '@/features/ratio-management/store/ratioUIStore';
export { useRatioV1UIStore } from '@/features/ratio-management/store/ratioV1UIStore';
export { useRatioBusinessStore } from '@/features/ratio-management/store/ratioBusinessStore';

// ==================== Hooks导出 ====================

// 配比计算相关
export { useRatioCalculation } from '@/features/ratio-management/hooks/ratio/useRatioCalculation';
export { useRatioForm } from '@/features/ratio-management/hooks/ratio/useRatioForm';
export { useRatioMaterials } from '@/features/ratio-management/hooks/ratio/useRatioMaterials';
export { useUnifiedRatioManagement } from '@/features/ratio-management/hooks/ratio/useUnifiedRatioManagement';

// 料仓管理相关
export { useSiloManagement } from '@/features/ratio-management/hooks/useSiloManagement';

// ==================== 服务导出 ====================

export { ratioApiService } from '@/features/ratio-management/services/ratio/ratioApiService';
// export { ratioCalculationService } from '@/features/ratio-management/services/ratioCalculationService';
// export { ratioValidationService } from '@/features/ratio-management/services/ratioValidationService';

// ==================== 类型导出 ====================

export type {
  RatioCalculationParams,
  UnifiedRatioMaterial,
  RatioProportions,
  MaterialCategory,
  ExposureClass,
} from '@/core/types/unified-types';

// ==================== 懒加载组件 ====================

import { lazy } from 'react';

// 配比相关模态框 - 懒加载
export const SiloManagementModal = lazy(() => import('@/models/SiloManagementModal'));
export const RatioHistoryModal = lazy(() => import('@/models/RatioHistoryModal'));
export const MortarRatioModal = lazy(() => import('@/models/MortarRatioModal'));
export const RatioSettingsModal = lazy(() => import('@/models/RatioSettingsModal'));
export const RatioSelectionModal = lazy(() => import('@/models/RatioSelectionModal'));
export const RatioBackupModal = lazy(() => import('@/models/RatioBackupModal'));

// 配比相关图表 - 懒加载 (暂时注释掉不存在的组件)
// export const RatioTrendChart = lazy(() => import('@/components/charts/RatioTrendChart'));
// export const MaterialUsageChart = lazy(() => import('@/components/charts/MaterialUsageChart'));
// export const QualityAnalysisChart = lazy(() => import('@/components/charts/QualityAnalysisChart'));
// export const CostAnalysisChart = lazy(() => import('@/components/charts/CostAnalysisChart'));

// 高级功能组件 - 懒加载 (暂时注释掉不存在的组件)
// export const AIRatioGenerator = lazy(() => import('@/components/ratio/AIRatioGenerator'));
// export const RatioOptimizer = lazy(() => import('@/components/ratio/RatioOptimizer'));
// export const QualityPredictor = lazy(() => import('@/components/ratio/QualityPredictor'));

// ==================== 版本切换组件 ====================

// export const RatioVersionSwitcher = lazy(() => import('@/components/ratio/RatioVersionSwitcher'));

// ==================== 模块配置 ====================

export const RATIO_MODULE_CONFIG = {
  name: 'ratio',
  version: '2.0.0',
  description: '混凝土配比设计模块',
  features: [
    'ratio-calculation',
    'material-management',
    'silo-management',
    'ai-generation',
    'quality-analysis',
    'version-switching',
  ],
  dependencies: [
    'recharts',
    '@hookform/resolvers',
    'react-hook-form',
    'zod',
    '@genkit-ai/googleai',
  ],
  versions: {
    v1: {
      description: '经典配比页面',
      features: ['basic-calculation', 'material-selection', 'manual-input'],
    },
    v2: {
      description: '增强配比页面',
      features: ['ai-generation', 'drag-drop', 'real-time-calculation', 'quality-prediction'],
    },
  },
} as const;

// ==================== 模块初始化 ====================

export function initializeRatioModule(version: 'v1' | 'v2' = 'v2') {
  console.log(`🧪 初始化配比模块 ${version}...`);

  if (typeof window !== 'undefined') {
    // 预加载材料数据 (暂时注释掉不存在的方法)
    // import('@/services/ratio/ratioApiService').then(({ ratioApiService }) => {
    //   ratioApiService.preloadMaterials();
    // });

    // 预加载配比模板 (暂时注释掉不存在的服务)
    // import('@/services/ratio/ratioCalculationService').then(({ ratioCalculationService }) => {
    //   ratioCalculationService.preloadTemplates();
    // });

    // 根据版本预加载特定功能
    if (version === 'v2') {
      // V2版本预加载AI功能 (暂时注释掉不存在的模块)
      // import('@/ai/flows/ratio-ai-generation').then(() => {
      //   console.log('✅ AI配比生成功能已预加载');
      // });
      console.log('🧪 配比V2版本初始化完成');
    }
  }
}

// ==================== 性能监控 ====================

export function getRatioModuleMetrics() {
  return {
    moduleSize: '~900KB',
    loadTime: performance.now(),
    componentsLoaded: ['RatioV1Page', 'RatioV2Page', 'MaterialSelector', 'CalculationEngine'],
    lazyComponentsAvailable: [
      'SiloManagementModal',
      'RatioHistoryModal',
      'MortarRatioModal',
      'AIRatioGenerator',
      'RatioTrendChart',
      'QualityAnalysisChart',
    ],
    aiFeatures: ['ratio-generation', 'quality-prediction', 'optimization-suggestions'],
  };
}

// ==================== 工具函数 ====================

export function switchRatioVersion(version: 'v1' | 'v2') {
  // 动态切换配比版本 (暂时注释掉不存在的组件)
  console.warn(`配比版本 ${version} 暂未实现`);
  return Promise.resolve({ default: () => null });
  // return version === 'v1'
  //   ? import('@/components/pages/ratio/RatioV1Page')
  //   : import('@/components/pages/ratio-v2/RatioV2Page');
}

export function preloadRatioFeatures() {
  // 预加载常用功能 (只加载存在的组件)
  const features = [
    import('@/models/SiloManagementModal'),
    import('@/models/RatioHistoryModal'),
    // import('@/components/charts/RatioTrendChart') // 暂时注释掉不存在的组件
  ];

  return Promise.all(features);
}
