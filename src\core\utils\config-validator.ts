/**
 * 配置验证机制
 * 提供配置数据的验证、迁移和修复功能
 */

import type { AppConfig } from '@/shared/components/contexts/ConfigProvider';

/**
 * 配置验证错误类型
 */
export interface ConfigValidationError {
  path: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
  suggestedFix?: any;
}

/**
 * 配置验证结果
 */
export interface ConfigValidationResult {
  isValid: boolean;
  errors: ConfigValidationError[];
  warnings: ConfigValidationError[];
  fixedConfig?: AppConfig;
}

/**
 * 配置字段验证规则
 */
interface ValidationRule {
  path: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'enum';
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  enumValues?: any[];
  validator?: (value: any) => boolean;
  defaultValue?: any;
}

/**
 * 配置验证规则定义
 */
const VALIDATION_RULES: ValidationRule[] = [
  // 主题配置
  {
    path: 'theme.mode',
    type: 'enum',
    enumValues: ['light', 'dark', 'auto'],
    required: true,
    defaultValue: 'light',
  },
  {
    path: 'theme.primaryColor',
    type: 'string',
    pattern: /^#[0-9a-fA-F]{6}$/,
    required: true,
    defaultValue: '#2563eb',
  },
  {
    path: 'theme.accentColor',
    type: 'string',
    pattern: /^#[0-9a-fA-F]{6}$/,
    required: true,
    defaultValue: '#7c3aed',
  },
  { path: 'theme.borderRadius', type: 'number', min: 0, max: 20, required: true, defaultValue: 6 },
  { path: 'theme.fontFamily', type: 'string', required: true, defaultValue: 'system-ui' },

  // 密度配置
  {
    path: 'density.mode',
    type: 'enum',
    enumValues: ['compact', 'comfortable', 'spacious'],
    required: true,
    defaultValue: 'compact',
  },
  { path: 'density.padding', type: 'number', min: 0, max: 32, required: true, defaultValue: 8 },
  { path: 'density.spacing', type: 'number', min: 0, max: 32, required: true, defaultValue: 8 },
  { path: 'density.fontSize', type: 'number', min: 10, max: 24, required: true, defaultValue: 14 },
  { path: 'density.iconSize', type: 'number', min: 12, max: 32, required: true, defaultValue: 16 },

  // 表格配置
  { path: 'table.columnColors', type: 'object', required: true, defaultValue: {} },
  {
    path: 'table.defaultColumnWidth',
    type: 'number',
    min: 80,
    max: 400,
    required: true,
    defaultValue: 140,
  },
  { path: 'table.minFontSize', type: 'number', min: 8, max: 20, required: true, defaultValue: 12 },
  { path: 'table.showBorders', type: 'boolean', required: true, defaultValue: true },
  { path: 'table.alternateRowColors', type: 'boolean', required: true, defaultValue: true },

  // 卡片配置
  {
    path: 'card.vehicleBackgroundColor',
    type: 'string',
    pattern: /^#[0-9a-fA-F]{6}$/,
    required: true,
    defaultValue: '#fce7f3',
  },
  {
    path: 'card.taskCardHeight',
    type: 'number',
    min: 80,
    max: 300,
    required: true,
    defaultValue: 120,
  },
  {
    path: 'card.taskCardWidth',
    type: 'number',
    min: 200,
    max: 500,
    required: true,
    defaultValue: 280,
  },
  { path: 'card.showShadows', type: 'boolean', required: true, defaultValue: true },
  {
    path: 'card.borderStyle',
    type: 'enum',
    enumValues: ['solid', 'dashed', 'dotted'],
    required: true,
    defaultValue: 'solid',
  },

  // 任务列表配置
  {
    path: 'taskList.mode',
    type: 'enum',
    enumValues: ['table', 'card', 'hybrid'],
    required: true,
    defaultValue: 'table',
  },
  { path: 'taskList.enableDragDrop', type: 'boolean', required: true, defaultValue: true },
  { path: 'taskList.autoRefresh', type: 'boolean', required: true, defaultValue: false },
  {
    path: 'taskList.refreshInterval',
    type: 'number',
    min: 5000,
    max: 300000,
    required: true,
    defaultValue: 30000,
  },

  // 配比页面配置
  {
    path: 'ratio.defaultVersion',
    type: 'enum',
    enumValues: ['v1', 'v2'],
    required: true,
    defaultValue: 'v2',
  },
  { path: 'ratio.enableAutoSave', type: 'boolean', required: true, defaultValue: true },
  {
    path: 'ratio.autoSaveDelay',
    type: 'number',
    min: 1000,
    max: 30000,
    required: true,
    defaultValue: 3000,
  },
  { path: 'ratio.showAdvancedFeatures', type: 'boolean', required: true, defaultValue: true },

  // 性能配置
  { path: 'performance.enableVirtualization', type: 'boolean', required: true, defaultValue: true },
  {
    path: 'performance.chunkSize',
    type: 'number',
    min: 10,
    max: 1000,
    required: true,
    defaultValue: 50,
  },
  {
    path: 'performance.debounceDelay',
    type: 'number',
    min: 100,
    max: 2000,
    required: true,
    defaultValue: 300,
  },
  {
    path: 'performance.cacheSize',
    type: 'number',
    min: 10,
    max: 1000,
    required: true,
    defaultValue: 100,
  },
];

/**
 * 获取嵌套对象的值
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * 设置嵌套对象的值
 */
function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.');
  const lastKey = keys.pop()!;
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  target[lastKey] = value;
}

/**
 * 验证单个字段
 */
function validateField(config: any, rule: ValidationRule): ConfigValidationError[] {
  const errors: ConfigValidationError[] = [];
  const value = getNestedValue(config, rule.path);

  // 检查必填字段
  if (rule.required && (value === undefined || value === null)) {
    errors.push({
      path: rule.path,
      message: `必填字段 ${rule.path} 缺失`,
      severity: 'error',
      suggestedFix: rule.defaultValue,
    });
    return errors;
  }

  // 如果值不存在且不是必填，跳过验证
  if (value === undefined || value === null) {
    return errors;
  }

  // 类型验证
  switch (rule.type) {
    case 'string':
      if (typeof value !== 'string') {
        errors.push({
          path: rule.path,
          message: `${rule.path} 应该是字符串类型，当前是 ${typeof value}`,
          severity: 'error',
          suggestedFix: rule.defaultValue || '',
        });
      } else if (rule.pattern && !rule.pattern.test(value)) {
        errors.push({
          path: rule.path,
          message: `${rule.path} 格式不正确`,
          severity: 'error',
          suggestedFix: rule.defaultValue,
        });
      }
      break;

    case 'number':
      if (typeof value !== 'number' || isNaN(value)) {
        errors.push({
          path: rule.path,
          message: `${rule.path} 应该是数字类型`,
          severity: 'error',
          suggestedFix: rule.defaultValue || 0,
        });
      } else {
        if (rule.min !== undefined && value < rule.min) {
          errors.push({
            path: rule.path,
            message: `${rule.path} 不能小于 ${rule.min}`,
            severity: 'error',
            suggestedFix: rule.min,
          });
        }
        if (rule.max !== undefined && value > rule.max) {
          errors.push({
            path: rule.path,
            message: `${rule.path} 不能大于 ${rule.max}`,
            severity: 'error',
            suggestedFix: rule.max,
          });
        }
      }
      break;

    case 'boolean':
      if (typeof value !== 'boolean') {
        errors.push({
          path: rule.path,
          message: `${rule.path} 应该是布尔类型`,
          severity: 'error',
          suggestedFix: rule.defaultValue || false,
        });
      }
      break;

    case 'object':
      if (typeof value !== 'object' || Array.isArray(value)) {
        errors.push({
          path: rule.path,
          message: `${rule.path} 应该是对象类型`,
          severity: 'error',
          suggestedFix: rule.defaultValue || {},
        });
      }
      break;

    case 'array':
      if (!Array.isArray(value)) {
        errors.push({
          path: rule.path,
          message: `${rule.path} 应该是数组类型`,
          severity: 'error',
          suggestedFix: rule.defaultValue || [],
        });
      }
      break;

    case 'enum':
      if (rule.enumValues && !rule.enumValues.includes(value)) {
        errors.push({
          path: rule.path,
          message: `${rule.path} 的值 "${value}" 不在允许的选项中: ${rule.enumValues.join(', ')}`,
          severity: 'error',
          suggestedFix: rule.defaultValue || rule.enumValues[0],
        });
      }
      break;
  }

  // 自定义验证器
  if (rule.validator && !rule.validator(value)) {
    errors.push({
      path: rule.path,
      message: `${rule.path} 未通过自定义验证`,
      severity: 'error',
      suggestedFix: rule.defaultValue,
    });
  }

  return errors;
}

/**
 * 验证完整配置
 */
export function validateConfig(config: any): ConfigValidationResult {
  const errors: ConfigValidationError[] = [];
  const warnings: ConfigValidationError[] = [];

  // 基本结构检查
  if (!config || typeof config !== 'object') {
    return {
      isValid: false,
      errors: [
        {
          path: 'root',
          message: '配置必须是一个对象',
          severity: 'error',
        },
      ],
      warnings: [],
    };
  }

  // 逐个验证字段
  for (const rule of VALIDATION_RULES) {
    const fieldErrors = validateField(config, rule);
    errors.push(...fieldErrors);
  }

  // 检查未知字段（警告）
  const checkUnknownFields = (obj: any, basePath = '') => {
    for (const key in obj) {
      const currentPath = basePath ? `${basePath}.${key}` : key;
      const hasRule = VALIDATION_RULES.some(rule => rule.path.startsWith(currentPath));

      if (!hasRule) {
        warnings.push({
          path: currentPath,
          message: `未知的配置字段: ${currentPath}`,
          severity: 'warning',
        });
      } else if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        checkUnknownFields(obj[key], currentPath);
      }
    }
  };

  checkUnknownFields(config);

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 修复配置
 */
export function fixConfig(config: any): AppConfig {
  const fixedConfig = JSON.parse(JSON.stringify(config)); // 深拷贝

  for (const rule of VALIDATION_RULES) {
    const fieldErrors = validateField(fixedConfig, rule);

    if (fieldErrors.length > 0) {
      const errorWithFix = fieldErrors.find(error => error.suggestedFix !== undefined);
      if (errorWithFix) {
        setNestedValue(fixedConfig, rule.path, errorWithFix.suggestedFix);
      }
    }
  }

  return fixedConfig as AppConfig;
}

/**
 * 配置迁移（版本升级）
 */
export function migrateConfig(config: any, fromVersion: string, toVersion: string): AppConfig {
  let migratedConfig = JSON.parse(JSON.stringify(config));

  // 这里可以添加版本特定的迁移逻辑
  // 例如：从 v1.0 到 v1.1 的迁移
  if (fromVersion === '1.0' && toVersion === '1.1') {
    // 添加新的配置字段
    if (!migratedConfig.performance) {
      migratedConfig.performance = {
        enableVirtualization: true,
        chunkSize: 50,
        debounceDelay: 300,
        cacheSize: 100,
      };
    }
  }

  // 应用修复
  return fixConfig(migratedConfig);
}

/**
 * 配置兼容性检查
 */
export function checkConfigCompatibility(config: any): {
  isCompatible: boolean;
  requiredMigration?: string;
  issues: string[];
} {
  const issues: string[] = [];

  // 检查是否缺少关键字段
  const requiredSections = [
    'theme',
    'density',
    'table',
    'card',
    'taskList',
    'ratio',
    'performance',
  ];
  const missingSections = requiredSections.filter(section => !config[section]);

  if (missingSections.length > 0) {
    issues.push(`缺少配置部分: ${missingSections.join(', ')}`);
  }

  // 检查是否有过时的字段
  const deprecatedFields = ['oldThemeConfig', 'legacyDensity', 'deprecatedTableSettings'];

  const foundDeprecated = deprecatedFields.filter(field => config[field]);
  if (foundDeprecated.length > 0) {
    issues.push(`发现过时的配置字段: ${foundDeprecated.join(', ')}`);
  }

  return {
    isCompatible: issues.length === 0,
    requiredMigration: issues.length > 0 ? 'latest' : undefined,
    issues,
  };
}
