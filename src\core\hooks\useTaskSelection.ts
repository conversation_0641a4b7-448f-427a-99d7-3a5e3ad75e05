// src/core/hooks/useTaskSelection.ts
'use client';

import { useMemo } from 'react';
import { useTaskSelectionState } from '@/shared/components/contexts/TaskSelectionContext';

/**
 * 优化的任务选择 Hook
 * 只在特定任务的选择状态变化时才触发重新渲染
 */
export function useTaskSelection(taskId: string) {
  const { selectedTaskId } = useTaskSelectionState();

  // 使用 useMemo 确保只在当前任务的选择状态变化时才重新计算
  const isSelected = useMemo(() => {
    return selectedTaskId === taskId;
  }, [selectedTaskId, taskId]);

  return {
    isSelected,
    selectedTaskId,
  };
}

/**
 * 检查任务是否被选中的轻量级 Hook
 * 避免不必要的重新渲染
 */
export function useIsTaskSelected(taskId: string): boolean {
  const { selectedTaskId } = useTaskSelectionState();
  return selectedTaskId === taskId;
}
