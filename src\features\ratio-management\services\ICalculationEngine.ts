/**
 * 计算引擎接口
 * 统一的配比计算接口定义
 */

import type { RatioMaterialModel, RatioValidationResult } from '@/models/RatioModel';

export interface ICalculationEngine {
  // 正向计算
  calculate(params: CalculationParams, materials: RatioMaterialModel[]): CalculationResult;

  // 反向计算
  reverseCalculate(
    materials: RatioMaterialModel[],
    targetResults: CalculationResult,
    method: string
  ): CalculationParams;

  // 参数验证
  validateParams(params: CalculationParams): ValidationResult;

  // 材料验证
  validateMaterials(materials: RatioMaterialModel[]): ValidationResult;

  // 获取引擎信息
  getEngineInfo(): EngineInfo;
}

export interface CalculationParams {
  // 基本参数
  targetStrength: number;
  slump: number;
  maxAggregateSize: number;
  exposureClass: string;

  // 环境参数
  ambientTemperature: number;
  relativeHumidity: number;
  cementTemperature: number;
  aggregateTemperature: number;

  // 材料选择
  selectedMaterials: string[];
  cementType: string;
  aggregateType: string;
  waterType: string;

  // 配比参数
  waterCementRatio: number;
  sandRatio: number;
  cementContent: number;
  waterContent: number;

  // 外加剂参数
  additiveRatio: number;
  flyashRatio: number;
  mineralPowderRatio: number;
  silicaFumeRatio: number;
  antifreezeRatio: number;
  expansionRatio: number;
  ultraFineSandRatio: number;
  earlyStrengthRatio: number;
  s105Ratio: number;

  // 数量参数
  cementAmount: number;
  waterAmount: number;
  density: number;
  airContent: number;
  strengthGrade: number;

  // 施工参数
  placementMethod: string;
  finishingRequirement: string;
  cureConditions: {
    temperature: number;
    humidity: number;
    duration: number;
  };
}

export interface CalculationResult {
  // 基本结果
  totalWeight: number;
  materials: Record<string, number>;

  // 性能预测
  strengthPrediction: number;
  durabilityIndex: number;
  workabilityIndex: number;

  // 质量评估
  qualityScore: number;
  warnings: string[];
  suggestions: string[];

  // 成本和环保
  carbonFootprint: number;
  costEstimate: number;
  economyIndex: number;

  // 详细分析
  detailedAnalysis?: {
    strengthAnalysis: StrengthAnalysis;
    durabilityAnalysis: DurabilityAnalysis;
    workabilityAnalysis: WorkabilityAnalysis;
    economicAnalysis: EconomicAnalysis;
    environmentalAnalysis: EnvironmentalAnalysis;
  };

  // 计算元数据
  metadata: {
    engineVersion: string;
    calculationTime: number; // ms
    complexity: 'simple' | 'standard' | 'complex';
    confidence: number; // 0-100
    method: string;
    timestamp: string;
  };
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  score: number;
}

export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
  code: string;
  value?: any;
  expectedRange?: {
    min: number;
    max: number;
  };
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
  code: string;
  value?: any;
  recommendedValue?: any;
}

export interface EngineInfo {
  name: string;
  version: string;
  description: string;
  capabilities: EngineCapability[];
  supportedMethods: string[];
  supportedMaterials: string[];
  accuracy: {
    strengthPrediction: number; // percentage
    costEstimation: number; // percentage
    durabilityPrediction: number; // percentage
  };
  limitations: string[];
}

export interface EngineCapability {
  name: string;
  description: string;
  supported: boolean;
  accuracy?: number; // percentage
}

// 详细分析接口
export interface StrengthAnalysis {
  predictedStrength: {
    day1: number;
    day3: number;
    day7: number;
    day28: number;
    day90?: number;
  };
  strengthDevelopment: {
    early: number; // 1-7天强度发展
    late: number; // 28-90天强度发展
  };
  influencingFactors: {
    factor: string;
    impact: number; // -100 to 100
    description: string;
  }[];
  confidence: number;
  recommendations: string[];
}

export interface DurabilityAnalysis {
  durabilityIndex: number;
  riskAssessment: {
    carbonation: 'low' | 'medium' | 'high';
    chlorideIngress: 'low' | 'medium' | 'high';
    freezeThaw: 'low' | 'medium' | 'high';
    sulfateAttack: 'low' | 'medium' | 'high';
    alkaliSilicaReaction: 'low' | 'medium' | 'high';
  };
  serviceLife: {
    estimated: number; // years
    confidence: number; // percentage
    factors: string[];
  };
  protectiveMeasures: string[];
  recommendations: string[];
}

export interface WorkabilityAnalysis {
  workabilityIndex: number;
  slumpPrediction: {
    initial: number;
    after30min: number;
    after60min: number;
    after90min: number;
  };
  pumpability: {
    rating: 'excellent' | 'good' | 'fair' | 'poor';
    maxDistance: number; // meters
    maxHeight: number; // meters
    pressureRequired: number; // MPa
  };
  finishability: {
    rating: 'excellent' | 'good' | 'fair' | 'poor';
    bleedingRisk: 'low' | 'medium' | 'high';
    segregationRisk: 'low' | 'medium' | 'high';
  };
  recommendations: string[];
}

export interface EconomicAnalysis {
  totalCost: number;
  costBreakdown: {
    materials: number;
    transportation: number;
    labor: number;
    equipment: number;
    overhead: number;
  };
  costPerUnit: {
    perCubicMeter: number;
    perSquareMeter?: number;
    perTon: number;
  };
  costOptimization: {
    potential: number; // percentage
    recommendations: CostOptimizationRecommendation[];
  };
  marketFactors: {
    materialPriceVolatility: 'low' | 'medium' | 'high';
    seasonalFactors: string[];
    regionalFactors: string[];
  };
}

export interface EnvironmentalAnalysis {
  carbonFootprint: {
    total: number; // kg CO2/m³
    breakdown: {
      cement: number;
      aggregates: number;
      admixtures: number;
      transportation: number;
      production: number;
    };
  };
  sustainabilityMetrics: {
    recycledContent: number; // percentage
    localMaterials: number; // percentage
    renewableEnergy: number; // percentage
  };
  environmentalImpact: {
    waterUsage: number; // liters/m³
    energyConsumption: number; // kWh/m³
    wasteGeneration: number; // kg/m³
  };
  certifications: string[];
  recommendations: EnvironmentalRecommendation[];
}

export interface CostOptimizationRecommendation {
  type: 'material_substitution' | 'proportion_adjustment' | 'supplier_change';
  description: string;
  potentialSavings: number; // percentage
  impact: {
    strength: number; // percentage change
    durability: number; // percentage change
    workability: number; // percentage change
  };
  implementation: {
    difficulty: 'easy' | 'medium' | 'hard';
    timeRequired: string;
    prerequisites: string[];
  };
  risks: string[];
}

export interface EnvironmentalRecommendation {
  type: 'material_substitution' | 'process_optimization' | 'waste_reduction';
  description: string;
  carbonReduction: number; // kg CO2/m³
  impact: {
    strength: number; // percentage change
    cost: number; // percentage change
    durability: number; // percentage change
  };
  implementation: {
    difficulty: 'easy' | 'medium' | 'hard';
    timeRequired: string;
    prerequisites: string[];
  };
  certificationBenefits: string[];
}
