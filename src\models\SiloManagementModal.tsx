'use client';

import React, { useState } from 'react';

import { But<PERSON> } from '@/shared/components/button';
import { Checkbox } from '@/shared/components/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { ScrollArea } from '@/shared/components/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/table';
import { mockPlants } from '@/infrastructure/api/mock/mock-data';
import type { SiloMapping } from '@/core/types';

import { MaterialNameManagementModal } from './MaterialNameManagementModal';
import { SpecificationManagementModal } from './SpecificationManagementModal';
import { StorageLocationManagementModal } from './StorageLocationManagementModal';

interface SiloManagementModalProps {
  isOpen?: boolean;
  open?: boolean;
  onOpenChangeAction?: (open: boolean) => void;
  onOpenChange?: (open: boolean) => void;
  siloMappings?: SiloMapping[];
}

export const SiloManagementModal: React.FC<SiloManagementModalProps> = ({
  isOpen = false,
  open = false,
  onOpenChangeAction,
  onOpenChange,
  siloMappings = [],
}) => {
  const [isMaterialModalOpen, setIsMaterialModalOpen] = useState(false);
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false);
  const [isSpecModalOpen, setIsSpecModalOpen] = useState(false);
  const [currentMaterialForSpec, setCurrentMaterialForSpec] = useState<string | undefined>(
    undefined
  );

  const openSpecModal = (materialName?: string) => {
    setCurrentMaterialForSpec(materialName);
    setIsSpecModalOpen(true);
  };

  // 统一处理open状态和回调
  const modalOpen = isOpen || open;
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChangeAction?.(newOpen);
    onOpenChange?.(newOpen);
  };

  return (
    <>
      <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
        <DialogContent className='max-w-6xl max-h-[85vh] flex flex-col'>
          <DialogHeader>
            <DialogTitle>维护罐信息</DialogTitle>
            <DialogDescription>管理搅拌站的料仓、物料及存放关系。</DialogDescription>
          </DialogHeader>
          <div className='flex items-center justify-between py-2 border-b'>
            <div className='flex items-center gap-2'>
              <span className='text-sm font-medium'>搅拌站名称:</span>
              <Select defaultValue='plant1'>
                <SelectTrigger className='w-[180px] h-8 text-sm'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {mockPlants.map(p => (
                    <SelectItem key={p.id} value={p.id}>
                      {p.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className='flex items-center gap-2'>
              <Button variant='outline' size='sm' onClick={() => setIsMaterialModalOpen(true)}>
                材料名称管理
              </Button>
              <Button variant='outline' size='sm' onClick={() => setIsLocationModalOpen(true)}>
                存放地名称管理
              </Button>
              <Button variant='outline' size='sm'>
                操作说明
              </Button>
            </div>
          </div>
          <ScrollArea className='flex-1'>
            <Table>
              <TableHeader className='sticky top-0 bg-background z-10'>
                <TableRow>
                  <TableHead>货仓</TableHead>
                  <TableHead>货物名称1</TableHead>
                  <TableHead>规格1</TableHead>
                  <TableHead>存放仓</TableHead>
                  <TableHead>卸料地</TableHead>
                  <TableHead>最后入库品名、规格</TableHead>
                  <TableHead>状态</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {siloMappings.map(mapping => (
                  <TableRow key={mapping.id}>
                    <TableCell className='font-semibold text-primary'>{mapping.siloType}</TableCell>
                    <TableCell>
                      <Select defaultValue={mapping.materialName}>
                        <SelectTrigger className='h-8 text-xs'>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={mapping.materialName}>
                            {mapping.materialName}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      <Select
                        defaultValue={mapping.spec}
                        onValueChange={value => {
                          if (value === 'manage-specs') {
                            openSpecModal(mapping.materialName);
                          }
                        }}
                      >
                        <SelectTrigger className='h-8 text-xs'>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem
                            value='manage-specs'
                            className='text-blue-600 font-semibold'
                            onSelect={e => e.preventDefault()}
                          >
                            规格维护...
                          </SelectItem>
                          <SelectSeparator />
                          <SelectItem value={mapping.spec}>{mapping.spec}</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>{mapping.storageBin}</TableCell>
                    <TableCell>
                      <Select defaultValue='default'>
                        <SelectTrigger className='h-8 text-xs'>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='default'>-</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>{mapping.lastEntry || '-'}</TableCell>
                    <TableCell>
                      <Checkbox checked={mapping.enabled} />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
          <div className='flex justify-end gap-2 pt-4 border-t'>
            <Button variant='outline' onClick={() => handleOpenChange(false)}>
              退出
            </Button>
            <Button>保存</Button>
          </div>
        </DialogContent>
      </Dialog>
      {/* Modals */}
      <MaterialNameManagementModal
        isOpen={isMaterialModalOpen}
        onOpenChangeAction={setIsMaterialModalOpen}
      />
      <StorageLocationManagementModal
        isOpen={isLocationModalOpen}
        onOpenChangeAction={setIsLocationModalOpen}
      />
      <SpecificationManagementModal
        isOpen={isSpecModalOpen}
        onOpenChangeAction={setIsSpecModalOpen}
        materialName={currentMaterialForSpec}
      />
    </>
  );
};

export default SiloManagementModal;
