/**
 * 系统常量定义
 * 统一管理所有硬编码的常量，避免魔法数字和字符串
 */

// ==================== 数值常量 ====================

/**
 * 配比计算相关常量
 */
export const RATIO_CONSTANTS = {
  // 强度范围
  MIN_STRENGTH: 10, // 最小强度 MPa
  MAX_STRENGTH: 100, // 最大强度 MPa
  DEFAULT_STRENGTH: 30, // 默认强度 MPa

  // 坍落度范围
  MIN_SLUMP: 10, // 最小坍落度 mm
  MAX_SLUMP: 250, // 最大坍落度 mm
  DEFAULT_SLUMP: 180, // 默认坍落度 mm

  // 水胶比范围
  MIN_WATER_CEMENT_RATIO: 0.25, // 最小水胶比
  MAX_WATER_CEMENT_RATIO: 0.8, // 最大水胶比
  DEFAULT_WATER_CEMENT_RATIO: 0.45, // 默认水胶比

  // 砂率范围
  MIN_SAND_RATIO: 25, // 最小砂率 %
  MAX_SAND_RATIO: 50, // 最大砂率 %
  DEFAULT_SAND_RATIO: 35, // 默认砂率 %

  // 密度范围
  MIN_DENSITY: 2000, // 最小密度 kg/m³
  MAX_DENSITY: 2800, // 最大密度 kg/m³
  DEFAULT_DENSITY: 2400, // 默认密度 kg/m³

  // 含气量范围
  MIN_AIR_CONTENT: 1, // 最小含气量 %
  MAX_AIR_CONTENT: 8, // 最大含气量 %
  DEFAULT_AIR_CONTENT: 4.5, // 默认含气量 %

  // 外加剂掺量范围
  MIN_ADMIXTURE_RATIO: 0, // 最小外加剂掺量 %
  MAX_ADMIXTURE_RATIO: 5, // 最大外加剂掺量 %
  DEFAULT_ADMIXTURE_RATIO: 1.2, // 默认外加剂掺量 %

  // 粉煤灰掺量范围
  MIN_FLY_ASH_RATIO: 0, // 最小粉煤灰掺量 %
  MAX_FLY_ASH_RATIO: 30, // 最大粉煤灰掺量 %
  DEFAULT_FLY_ASH_RATIO: 15, // 默认粉煤灰掺量 %

  // 矿粉掺量范围
  MIN_MINERAL_POWDER_RATIO: 0, // 最小矿粉掺量 %
  MAX_MINERAL_POWDER_RATIO: 25, // 最大矿粉掺量 %
  DEFAULT_MINERAL_POWDER_RATIO: 10, // 默认矿粉掺量 %
} as const;

/**
 * 车辆相关常量
 */
export const VEHICLE_CONSTANTS = {
  // 容量范围
  MIN_CAPACITY: 1, // 最小容量 m³
  MAX_CAPACITY: 30, // 最大容量 m³
  DEFAULT_CAPACITY: 10, // 默认容量 m³

  // 调度时间间隔
  MIN_DISPATCH_INTERVAL: 5, // 最小调度间隔 分钟
  MAX_DISPATCH_INTERVAL: 120, // 最大调度间隔 分钟
  DEFAULT_DISPATCH_INTERVAL: 30, // 默认调度间隔 分钟

  // 预计返回时间
  DEFAULT_RETURN_TIME_HOURS: 2, // 默认预计返回时间 小时
  MAX_RETURN_TIME_HOURS: 8, // 最大预计返回时间 小时
} as const;

/**
 * 任务相关常量
 */
export const TASK_CONSTANTS = {
  // 体积范围
  MIN_VOLUME: 0.1, // 最小体积 m³
  MAX_VOLUME: 1000, // 最大体积 m³
  DEFAULT_VOLUME: 50, // 默认体积 m³

  // 持续时间范围
  MIN_DURATION_HOURS: 0.5, // 最小持续时间 小时
  MAX_DURATION_HOURS: 24, // 最大持续时间 小时
  DEFAULT_DURATION_HOURS: 4, // 默认持续时间 小时

  // 调度频率范围
  MIN_DISPATCH_FREQUENCY: 5, // 最小调度频率 分钟
  MAX_DISPATCH_FREQUENCY: 180, // 最大调度频率 分钟
  DEFAULT_DISPATCH_FREQUENCY: 30, // 默认调度频率 分钟
} as const;

/**
 * UI相关常量
 */
export const UI_CONSTANTS = {
  // 分页
  DEFAULT_PAGE_SIZE: 20, // 默认每页条数
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100], // 每页条数选项

  // 表格
  MIN_COLUMN_WIDTH: 80, // 最小列宽 px
  DEFAULT_COLUMN_WIDTH: 120, // 默认列宽 px
  MAX_COLUMN_WIDTH: 300, // 最大列宽 px

  // 卡片
  MIN_CARD_WIDTH: 200, // 最小卡片宽度 px
  DEFAULT_CARD_WIDTH: 280, // 默认卡片宽度 px
  MAX_CARD_WIDTH: 400, // 最大卡片宽度 px

  // 间距
  SPACING_XS: 4, // 极小间距 px
  SPACING_SM: 8, // 小间距 px
  SPACING_MD: 16, // 中等间距 px
  SPACING_LG: 24, // 大间距 px
  SPACING_XL: 32, // 极大间距 px

  // 圆角
  BORDER_RADIUS_SM: 4, // 小圆角 px
  BORDER_RADIUS_MD: 8, // 中等圆角 px
  BORDER_RADIUS_LG: 12, // 大圆角 px

  // 阴影层级
  SHADOW_LEVEL_1: '0 1px 3px rgba(0, 0, 0, 0.12)',
  SHADOW_LEVEL_2: '0 4px 6px rgba(0, 0, 0, 0.1)',
  SHADOW_LEVEL_3: '0 10px 15px rgba(0, 0, 0, 0.1)',
} as const;

// ==================== 字符串常量 ====================

/**
 * 错误消息常量
 */
export const ERROR_MESSAGES = {
  // 通用错误
  UNKNOWN_ERROR: '未知错误',
  NETWORK_ERROR: '网络连接错误',
  SERVER_ERROR: '服务器错误',
  PERMISSION_DENIED: '权限不足',
  DATA_NOT_FOUND: '数据不存在',

  // 验证错误
  REQUIRED_FIELD: '此字段为必填项',
  INVALID_FORMAT: '格式不正确',
  VALUE_OUT_OF_RANGE: '数值超出有效范围',
  DUPLICATE_VALUE: '数值重复',

  // 配比相关错误
  INVALID_STRENGTH: '强度值无效',
  INVALID_SLUMP: '坍落度值无效',
  INVALID_WATER_CEMENT_RATIO: '水胶比无效',
  INVALID_SAND_RATIO: '砂率无效',
  MATERIAL_NOT_SELECTED: '未选择材料',
  CALCULATION_FAILED: '计算失败',

  // 车辆相关错误
  VEHICLE_NOT_AVAILABLE: '车辆不可用',
  VEHICLE_ALREADY_ASSIGNED: '车辆已被分配',
  INVALID_VEHICLE_CAPACITY: '车辆容量无效',

  // 任务相关错误
  TASK_NOT_FOUND: '任务不存在',
  TASK_ALREADY_COMPLETED: '任务已完成',
  TASK_CANNOT_BE_CANCELLED: '任务无法取消',
  INVALID_TASK_VOLUME: '任务体积无效',
} as const;

/**
 * 成功消息常量
 */
export const SUCCESS_MESSAGES = {
  // 通用成功
  OPERATION_SUCCESS: '操作成功',
  SAVE_SUCCESS: '保存成功',
  DELETE_SUCCESS: '删除成功',
  UPDATE_SUCCESS: '更新成功',
  CREATE_SUCCESS: '创建成功',

  // 配比相关成功
  RATIO_CALCULATED: '配比计算完成',
  RATIO_SAVED: '配比保存成功',
  RATIO_APPLIED: '配比应用成功',

  // 车辆相关成功
  VEHICLE_DISPATCHED: '车辆调度成功',
  VEHICLE_RETURNED: '车辆返回成功',
  VEHICLE_ASSIGNED: '车辆分配成功',

  // 任务相关成功
  TASK_CREATED: '任务创建成功',
  TASK_UPDATED: '任务更新成功',
  TASK_COMPLETED: '任务完成',
  TASK_CANCELLED: '任务已取消',
} as const;

/**
 * 状态文本常量
 */
export const STATUS_TEXTS = {
  // 加载状态
  LOADING: '加载中...',
  CALCULATING: '计算中...',
  SAVING: '保存中...',
  PROCESSING: '处理中...',

  // 空状态
  NO_DATA: '暂无数据',
  NO_RESULTS: '无搜索结果',
  NO_TASKS: '暂无任务',
  NO_VEHICLES: '暂无车辆',

  // 操作状态
  PLEASE_SELECT: '请选择',
  PLEASE_INPUT: '请输入',
  OPTIONAL: '可选',
  REQUIRED: '必填',
} as const;

// ==================== 配置常量 ====================

/**
 * 本地存储键名常量
 */
export const STORAGE_KEYS = {
  // 用户设置
  USER_PREFERENCES: 'user_preferences',
  THEME_MODE: 'theme_mode',
  LANGUAGE: 'language',

  // 界面设置
  TABLE_COLUMNS: 'table_columns',
  CARD_LAYOUT: 'card_layout',
  FILTER_SETTINGS: 'filter_settings',

  // 数据缓存
  RATIO_CACHE: 'ratio_cache',
  VEHICLE_CACHE: 'vehicle_cache',
  TASK_CACHE: 'task_cache',

  // 临时数据
  DRAFT_RATIO: 'draft_ratio',
  UNSAVED_CHANGES: 'unsaved_changes',
} as const;

/**
 * API端点常量
 */
export const API_ENDPOINTS = {
  // 配比相关
  RATIO_CALCULATE: '/api/ratio/calculate',
  RATIO_SAVE: '/api/ratio/save',
  RATIO_LIST: '/api/ratio/list',
  RATIO_DELETE: '/api/ratio/delete',

  // 车辆相关
  VEHICLE_LIST: '/api/vehicles',
  VEHICLE_DISPATCH: '/api/vehicles/dispatch',
  VEHICLE_STATUS: '/api/vehicles/status',

  // 任务相关
  TASK_LIST: '/api/tasks',
  TASK_CREATE: '/api/tasks/create',
  TASK_UPDATE: '/api/tasks/update',
  TASK_DELETE: '/api/tasks/delete',

  // 材料相关
  MATERIAL_LIST: '/api/materials',
  MATERIAL_SPECS: '/api/materials/specifications',
  SILO_STATUS: '/api/silos/status',
} as const;

/**
 * 时间格式常量
 */
export const TIME_FORMATS = {
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm',
  DATETIME: 'YYYY-MM-DD HH:mm',
  DATETIME_FULL: 'YYYY-MM-DD HH:mm:ss',
  TIMESTAMP: 'YYYY-MM-DD HH:mm:ss.SSS',
} as const;

/**
 * 正则表达式常量
 */
export const REGEX_PATTERNS = {
  // 基本格式
  PHONE: /^1[3-9]\d{9}$/, // 手机号
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, // 邮箱
  LICENSE_PLATE:
    /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳]$/, // 车牌号

  // 数值格式
  POSITIVE_NUMBER: /^\d+(\.\d+)?$/, // 正数
  INTEGER: /^\d+$/, // 整数
  DECIMAL: /^\d+\.\d+$/, // 小数

  // 特殊格式
  STRENGTH_GRADE: /^C\d{2,3}$/, // 强度等级 (如 C30, C40)
  CONCRETE_GRADE: /^[A-Z]\d{2,3}$/, // 混凝土等级
} as const;
