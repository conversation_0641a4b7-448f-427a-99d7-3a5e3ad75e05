/**
 * 配比页面容器组件测试
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { RatioPageContainer } from '../RatioPageContainer';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useParams: () => ({ taskId: 'test-task-001' }),
}));

// Mock toast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

// Mock all components to avoid import issues
jest.mock('../ProportioningTable', () => ({
  ProportioningTable: () => React.createElement('div', { 'data-testid': 'proportioning-table' }),
}));

jest.mock('../MaterialsSidebar', () => ({
  MaterialsSidebar: () => React.createElement('div', { 'data-testid': 'materials-sidebar' }),
}));

jest.mock('../CalculationPanel', () => ({
  CalculationPanel: () => React.createElement('div', { 'data-testid': 'calculation-panel' }),
}));

jest.mock('../RatioActionBar', () => ({
  RatioActionBar: () => React.createElement('div', { 'data-testid': 'ratio-action-bar' }),
}));

jest.mock('../TaskInfoPanel', () => ({
  TaskInfoPanel: () => React.createElement('div', { 'data-testid': 'task-info-panel' }),
}));

jest.mock('../RatioFooter', () => ({
  RatioFooter: () => React.createElement('div', { 'data-testid': 'ratio-footer' }),
}));

// Mock modal components
// jest.mock('@/components/modals/MortarRatioModal', () => ({
//   MortarRatioModal: () => React.createElement('div', { 'data-testid': 'mortar-ratio-modal' }),
// }));

// jest.mock('@/components/modals/RatioHistoryModal', () => ({
//   RatioHistoryModal: () => React.createElement('div', { 'data-testid': 'ratio-history-modal' }),
// }));

// jest.mock('@/components/modals/RatioSelectionModal', () => ({
//   RatioSelectionModal: () => React.createElement('div', { 'data-testid': 'ratio-selection-modal' }),
// }));

// jest.mock('@/components/modals/RatioSettingsModal', () => ({
//   RatioSettingsModal: () => React.createElement('div', { 'data-testid': 'ratio-settings-modal' }),
// }));

// jest.mock('@/components/modals/SiloManagementModal', () => ({
//   SiloManagementModal: () => React.createElement('div', { 'data-testid': 'silo-management-modal' }),
// }));

// Mock data
jest.mock('@/data/mock-data', () => ({
  generateMockTasks: () => [
    {
      id: 'test-task-001',
      taskNumber: 'T001',
      projectName: '测试项目',
      strength: 'C30',
      constructionSite: '测试部位',
      pouringMethod: '泵送',
    },
  ],
  generateMockSiloMappings: () => [],
  mockMaterials: [
    {
      id: 'mat-001',
      materialType: '水泥',
      spec: 'P.O 42.5',
      storageBin: '1#水泥仓',
      density: 3100,
      category: 'cementitious',
    },
  ],
  mockPlants: [
    {
      id: 'plant1',
      name: '搅拌站1',
    },
    {
      id: 'plant2',
      name: '搅拌站2',
    },
  ],
}));

jest.mock('@/infrastructure/api/mock/ratio-mock-data', () => ({
  generateMockRatioHistory: () => [],
  generateMockRatioSelectionRecords: () => [],
}));

describe('RatioPageContainer', () => {
  it('应该正确渲染配比页面容器', async () => {
    render(<RatioPageContainer taskId='test-task-001' />);

    // 等待组件初始化
    await waitFor(() => {
      // 检查主要组件是否存在（通过 data-testid）
      expect(screen.getByTestId('ratio-action-bar')).toBeInTheDocument();
      expect(screen.getByTestId('task-info-panel')).toBeInTheDocument();
      expect(screen.getByTestId('calculation-panel')).toBeInTheDocument();
      expect(screen.getByTestId('proportioning-table')).toBeInTheDocument();
      expect(screen.getByTestId('materials-sidebar')).toBeInTheDocument();
      expect(screen.getByTestId('ratio-footer')).toBeInTheDocument();
    });
  });

  it('应该显示任务信息', async () => {
    render(<RatioPageContainer taskId='test-task-001' />);

    await waitFor(() => {
      // 检查任务信息面板是否渲染
      expect(screen.getByTestId('task-info-panel')).toBeInTheDocument();
      // 由于我们 mock 了 TaskInfoPanel，它不会显示实际的任务数据
      // 但我们可以验证组件结构是否正确
      expect(screen.getByTestId('calculation-panel')).toBeInTheDocument();
    });
  });

  it('应该处理无效的任务ID', async () => {
    render(<RatioPageContainer taskId='invalid-task' />);

    // 应该显示错误状态
    await waitFor(() => {
      expect(screen.getByText('加载失败')).toBeInTheDocument();
      expect(screen.getByText('任务不存在')).toBeInTheDocument();
      expect(screen.getByText('重新加载')).toBeInTheDocument();
    });
  });
});
