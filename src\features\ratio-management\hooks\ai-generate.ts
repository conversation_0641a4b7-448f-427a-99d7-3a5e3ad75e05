/**
 * AI配比生成API路由
 * POST /api/ratio/ai-generate
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type {
  AIRatioGenerationRequest,
  AIRatioGenerationResponse,
} from '@/features/ratio-management/services/ratio/aiRatioService';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<AIRatioGenerationResponse>
) {
  // 只允许POST请求
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: '只允许POST请求',
      timestamp: new Date().toISOString(),
      error: 'METHOD_NOT_ALLOWED',
    });
  }

  try {
    const request: AIRatioGenerationRequest = req.body;

    // 基本参数验证
    if (!request.targetStrength || !request.environment) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：目标强度和环境条件',
        timestamp: new Date().toISOString(),
        error: 'MISSING_REQUIRED_PARAMS',
      });
    }

    // 检查是否在服务器环境中运行
    if (typeof window === 'undefined') {
      try {
        // 服务器端：尝试使用实际的AI配比生成逻辑
        const { generateConcreteRatio } = await import('@/features/ai/genkit');

        const aiRequest = {
          targetStrength: request.targetStrength,
          environment: request.environment,
          costLevel: request.costLevel,
          specialRequirements: request.specialRequirements,
          selectedMaterials: request.selectedMaterials,
          additionalParams: request.additionalParams,
          availableMaterials: request.availableMaterials,
        };

        const result = await generateConcreteRatio(aiRequest);

        return res.status(200).json({
          success: true,
          data: result,
          message: 'AI配比生成成功',
          timestamp: new Date().toISOString(),
        });
      } catch (aiError) {
        console.warn('AI生成失败，使用备用配比:', aiError);
        // 如果AI生成失败，回退到备用配比
        return res.status(200).json(generateFallbackRatio(request));
      }
    } else {
      // 客户端：直接返回备用配比（避免导入Node.js模块）
      return res.status(200).json(generateFallbackRatio(request));
    }
  } catch (error) {
    console.error('AI配比生成API错误:', error);

    return res.status(500).json({
      success: false,
      message: 'AI配比生成失败',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : '内部服务器错误',
    });
  }
}

/**
 * 生成备用配比响应
 */
function generateFallbackRatio(request: AIRatioGenerationRequest): AIRatioGenerationResponse {
  // 根据目标强度生成基础配比
  const strengthValue = parseInt(request.targetStrength.replace(/[^\d]/g, '')) || 30;

  // 基础材料用量（每立方米）
  const baseCement = Math.max(300, strengthValue * 8);
  const baseWater = baseCement * 0.45; // 水胶比0.45
  const baseSand = 650;
  const baseGravel = 1200;

  // 根据成本等级调整
  const costMultiplier =
    request.costLevel === 'high' ? 1.2 : request.costLevel === 'low' ? 0.8 : 1.0;

  const materials = {
    cement: Math.round(baseCement * costMultiplier),
    water: Math.round(baseWater),
    sand: Math.round(baseSand),
    gravel: Math.round(baseGravel),
    admixture: Math.round(baseCement * 0.01), // 1%外加剂
  };

  const totalWeight = Object.values(materials).reduce((sum, amount) => sum + amount, 0);

  return {
    success: true,
    data: {
      ratioName: `AI生成${request.targetStrength}配比`,
      description: `基于${request.environment}环境的${request.targetStrength}混凝土配比`,
      materials: Object.entries(materials).map(([name, amount]) => ({
        id: `material-${name}`,
        materialId: `material-${name}`,
        name: getMaterialDisplayName(name),
        category: getMaterialType(name),
        type: getMaterialType(name),
        amount,
        actualAmount: amount,
        theoreticalAmount: amount,
        designValue: amount,
        unit: 'kg/m³',
        specification: getDefaultSpecification(name),
        density: getDefaultDensity(name),
        supplier: '默认供应商',
        cost: amount * 0.8,
        waterContent: name === 'water' ? 100 : 0,
        stoneContent: name === 'gravel' ? 100 : 0,
        siloId: `silo-${name}`,
        siloName: `${getMaterialDisplayName(name)}仓`,
      })),
      calculationParams: {
        density: totalWeight,
        waterCementRatio: baseWater / baseCement,
        sandRatio: (baseSand / (baseSand + baseGravel)) * 100,
        targetStrength: strengthValue,
        slump: request.additionalParams?.slump || 120,
        airContent: 4.5,
        maxAggregateSize: 25,
        exposureClass: 'XC1',
        waterContent: baseWater,
        cementContent: baseCement,
        flyAshRatio: 0,
        silicaFumeRatio: 0,
      } as any,
      calculationResults: {
        totalWeight,
        materials,
        strengthPrediction: strengthValue,
        qualityScore: 85 + Math.random() * 10,
        warnings: generateWarnings(request),
        suggestions: generateSuggestions(request),
        carbonFootprint: totalWeight * 0.3,
        costEstimate: totalWeight * costMultiplier * 0.8,
      },
    },
    message: 'AI配比生成成功（备用方案）',
    timestamp: new Date().toISOString(),
  };
}

function getMaterialDisplayName(name: string): string {
  const names: Record<string, string> = {
    cement: '水泥',
    water: '水',
    sand: '细骨料（砂）',
    gravel: '粗骨料（石）',
    admixture: '外加剂',
  };
  return names[name] || name;
}

function getMaterialType(name: string): string {
  const types: Record<string, string> = {
    cement: 'cement',
    water: 'water',
    sand: 'fine-aggregate',
    gravel: 'coarse-aggregate',
    admixture: 'admixture',
  };
  return types[name] || 'other';
}

function getDefaultSpecification(name: string): string {
  const specs: Record<string, string> = {
    cement: 'P.O 42.5',
    water: '自来水',
    sand: '中砂',
    gravel: '5-25mm碎石',
    admixture: '聚羧酸减水剂',
  };
  return specs[name] || '';
}

function getDefaultDensity(name: string): number {
  const densities: Record<string, number> = {
    cement: 3100,
    water: 1000,
    sand: 2650,
    gravel: 2700,
    admixture: 1200,
  };
  return densities[name] || 2500;
}

function generateWarnings(request: AIRatioGenerationRequest): string[] {
  const warnings: string[] = [];

  if (request.additionalParams?.temperature && request.additionalParams.temperature < 5) {
    warnings.push('低温环境，建议添加防冻剂');
  }

  if (request.additionalParams?.temperature && request.additionalParams.temperature > 35) {
    warnings.push('高温环境，建议调整施工时间');
  }

  if (request.costLevel === 'low') {
    warnings.push('低成本配比，请注意质量控制');
  }

  return warnings;
}

function generateSuggestions(request: AIRatioGenerationRequest): string[] {
  const suggestions: string[] = [];

  suggestions.push('建议进行试配验证');
  suggestions.push('根据实际材料调整配比');

  if (request.specialRequirements.includes('高强度')) {
    suggestions.push('可考虑添加硅灰提高强度');
  }

  if (request.specialRequirements.includes('高耐久性')) {
    suggestions.push('建议降低水胶比，增加矿物掺合料');
  }

  return suggestions;
}
