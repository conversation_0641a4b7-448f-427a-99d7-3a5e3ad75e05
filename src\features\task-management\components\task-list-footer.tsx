// src/components/sections/task-list/components/task-list-footer.tsx
'use client';

import React from 'react';

import { Activity, Clock, TrendingUp, Users } from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import type { Task, TaskStatusFilter, Vehicle } from '@/core/types';

export interface TaskListFooterProps {
  // Data
  allTasks: Task[];
  filteredTasks: Task[];
  allVehicles: Vehicle[];

  // Current state
  taskStatusFilter: TaskStatusFilter;
  selectedTaskIds: string[];

  // Statistics
  performanceMetrics?: {
    renderTime: number;
    updateCount: number;
    memoryUsage?: number;
  };

  // Actions
  onClearSelection: () => void;
  onBulkAction: (action: string) => void;
}

export function TaskListFooter({
  allTasks,
  filteredTasks,
  allVehicles,
  taskStatusFilter,
  selectedTaskIds,
  performanceMetrics,
  onClearSelection,
  onBulkAction,
}: TaskListFooterProps) {
  // Calculate statistics
  const stats = React.useMemo(() => {
    const totalTasks = allTasks.length;
    const filteredCount = filteredTasks.length;
    const selectedCount = selectedTaskIds.length;

    // Task status counts
    const statusCounts = allTasks.reduce(
      (acc, task) => {
        acc[task.dispatchStatus] = (acc[task.dispatchStatus] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    // Vehicle status counts
    const vehicleStatusCounts = allVehicles.reduce(
      (acc, vehicle) => {
        acc[vehicle.status] = (acc[vehicle.status] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    // Active tasks (in progress)
    const activeTasks = allTasks.filter(task => task.dispatchStatus === 'InProgress').length;

    // Overdue tasks (simplified calculation)
    const overdueTasks = allTasks.filter(task => {
      if (task.dispatchStatus !== 'InProgress') return false;
      // Add your overdue logic here
      return false;
    }).length;

    return {
      totalTasks,
      filteredCount,
      selectedCount,
      statusCounts,
      vehicleStatusCounts,
      activeTasks,
      overdueTasks,
    };
  }, [allTasks, filteredTasks, selectedTaskIds, allVehicles]);

  return (
    <div className='border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
      {/* Selection bar (shown when tasks are selected) */}
      {stats.selectedCount > 0 && (
        <div className='flex items-center justify-between p-0.5 bg-primary/10 border-b'>
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium'>已选择 {stats.selectedCount} 个任务</span>
            <Button variant='outline' size='sm' onClick={onClearSelection}>
              清除选择
            </Button>
          </div>

          <div className='flex items-center gap-2'>
            <Button variant='outline' size='sm' onClick={() => onBulkAction('pause')}>
              批量暂停
            </Button>
            <Button variant='outline' size='sm' onClick={() => onBulkAction('resume')}>
              批量恢复
            </Button>
            <Button variant='outline' size='sm' onClick={() => onBulkAction('cancel')}>
              批量取消
            </Button>
          </div>
        </div>
      )}

      {/* Main footer content */}
      <div className='flex items-center justify-between p-0.5 text-sm text-muted-foreground'>
        {/* Left side - Task statistics */}
        <div className='flex items-center gap-4'>
          <div className='flex items-center gap-2'>
            <Activity className='h-4 w-4' />
            <span>总计: {stats.totalTasks}</span>
            {stats.filteredCount !== stats.totalTasks && <span>/ 显示: {stats.filteredCount}</span>}
          </div>

          <div className='flex items-center gap-2'>
            <TrendingUp className='h-4 w-4' />
            <span>进行中: {stats.activeTasks}</span>
          </div>

          {stats.overdueTasks > 0 && (
            <div className='flex items-center gap-2 text-destructive'>
              <Clock className='h-4 w-4' />
              <span>超时: {stats.overdueTasks}</span>
            </div>
          )}

          <div className='flex items-center gap-2'>
            <Users className='h-4 w-4' />
            <span>车辆: {allVehicles.length}</span>
          </div>
        </div>

        {/* Center - Status badges */}
        <div className='flex items-center gap-2'>
          {Object.entries(stats.statusCounts).map(([status, count]) => (
            <Badge
              key={status}
              variant={status === 'InProgress' ? 'default' : 'secondary'}
              className='text-xs'
            >
              {getStatusLabel(status)}: {count}
            </Badge>
          ))}
        </div>

        {/* Right side - Performance metrics */}
        <div className='flex items-center gap-4'>
          {performanceMetrics && (
            <div className='flex items-center gap-2'>
              <span>渲染: {performanceMetrics.renderTime}ms</span>
              <span>更新: {performanceMetrics.updateCount}</span>
              {performanceMetrics.memoryUsage && (
                <span>内存: {Math.round(performanceMetrics.memoryUsage / 1024 / 1024)}MB</span>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Helper function to get status labels
function getStatusLabel(status: string): string {
  const statusLabels: Record<string, string> = {
    New: '新建',
    ReadyToProduce: '待生产',
    RatioSet: '已配比',
    InProgress: '进行中',
    Paused: '暂停',
    Completed: '已完成',
    Cancelled: '已取消',
  };

  return statusLabels[status] || status;
}
