/**
 * 路由级代码分割配置
 * 优化包结构和加载策略
 */

import { lazy } from 'react';

// ==================== 路由级分割配置 ====================

export const ROUTE_SPLITTING_CONFIG = {
  // 核心页面 - 立即加载
  core: {
    home: '/',
    dashboard: '/dashboard',
  },

  // 功能模块 - 按需加载
  modules: {
    dispatch: {
      routes: ['/task-list', '/vehicle-dispatch'],
      chunkName: 'dispatch-module',
      priority: 'high',
      preload: true,
    },
    ratio: {
      routes: ['/ratio', '/ratio/v1', '/ratio/v2', '/ratio/v3'],
      chunkName: 'ratio-module',
      priority: 'medium',
      preload: false,
    },
    reports: {
      routes: ['/reports', '/analytics'],
      chunkName: 'reports-module',
      priority: 'low',
      preload: false,
    },
    settings: {
      routes: ['/settings', '/profile'],
      chunkName: 'settings-module',
      priority: 'low',
      preload: false,
    },
  },

  // 演示页面 - 独立分割
  demo: {
    routes: ['/lazy-loading-demo'],
    chunkName: 'demo-module',
    priority: 'low',
    preload: false,
  },
} as const;

// ==================== 组件级分割策略 ====================

export const COMPONENT_SPLITTING_STRATEGY = {
  // 图表组件 - 按功能模块分组
  charts: {
    dispatch: ['TaskProgressChart', 'VehicleDensityChart', 'DispatchEfficiencyChart'],
    ratio: ['RatioTrendChart', 'MaterialUsageChart', 'QualityAnalysisChart'],
    reports: ['PerformanceChart', 'AnalyticsChart', 'ReportChart'],
  },

  // 模态框组件 - 按使用频率分组
  modals: {
    frequent: ['TaskEditModal', 'VehicleDispatchModal'],
    occasional: ['TaskProgressModal', 'SettingsModal'],
    rare: ['ImportModal', 'ExportModal', 'HelpModal'],
  },

  // 表单组件 - 按复杂度分组
  forms: {
    simple: ['SearchForm', 'FilterForm'],
    complex: ['TaskForm', 'VehicleForm', 'RatioForm'],
    advanced: ['BulkEditForm', 'ImportForm'],
  },
} as const;

// ==================== 预加载策略 ====================

export const PRELOAD_STRATEGY = {
  // 立即预加载 - 用户很可能访问的模块
  immediate: ['dispatch-module'],

  // 空闲时预加载 - 用户可能访问的模块
  onIdle: ['ratio-module'],

  // 悬停预加载 - 用户悬停相关链接时预加载
  onHover: ['reports-module', 'settings-module'],

  // 路由变化预加载 - 基于用户行为模式
  onRouteChange: {
    '/': ['dispatch-module'], // 从首页最可能去调度模块
    '/task-list': ['ratio-module'], // 从任务列表可能去配比
    '/ratio': ['reports-module'], // 从配比可能去报表
  },
} as const;

// ==================== 包大小优化配置 ====================

export const BUNDLE_OPTIMIZATION = {
  // 代码分割阈值
  splitThresholds: {
    minSize: 20000, // 20KB 最小分割大小
    maxSize: 500000, // 500KB 最大包大小
    maxAsyncRequests: 30, // 最大异步请求数
    maxInitialRequests: 5, // 最大初始请求数
  },

  // 共享依赖提取
  sharedDependencies: {
    vendor: ['react', 'react-dom', 'react-router-dom'],
    ui: ['@mui/material', '@mui/icons-material', '@mui/x-date-pickers'],
    utils: ['lodash', 'dayjs', 'zustand'],
    charts: ['recharts', 'd3'],
  },

  // 缓存策略
  caching: {
    vendor: 'long-term', // 第三方库长期缓存
    app: 'medium-term', // 应用代码中期缓存
    chunks: 'short-term', // 动态块短期缓存
  },
} as const;

// ==================== 懒加载工厂函数 ====================

/**
 * 创建路由级懒加载组件
 */
export function createRouteLazyComponent(
  importFn: () => Promise<any>,
  options: {
    chunkName?: string;
    preload?: boolean;
    fallback?: React.ComponentType;
  } = {}
) {
  const LazyComponent = lazy(() => {
    const startTime = performance.now();

    return importFn().then(module => {
      const loadTime = performance.now() - startTime;

      // 记录加载性能
      if (typeof window !== 'undefined') {
        console.log(
          `📦 路由组件加载完成: ${options.chunkName || 'unknown'} - ${loadTime.toFixed(2)}ms`
        );
      }

      return module;
    });
  });

  // 预加载逻辑
  if (options.preload && typeof window !== 'undefined') {
    // 空闲时预加载
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        importFn();
      });
    } else {
      // 降级到 setTimeout
      setTimeout(() => {
        importFn();
      }, 100);
    }
  }

  return LazyComponent;
}

/**
 * 创建组件级懒加载组件
 */
export function createComponentLazyComponent(
  importFn: () => Promise<any>,
  options: {
    componentName?: string;
    category?: string;
    priority?: 'high' | 'medium' | 'low';
  } = {}
) {
  return lazy(() => {
    const startTime = performance.now();

    return importFn().then(module => {
      const loadTime = performance.now() - startTime;

      // 记录组件加载性能
      if (typeof window !== 'undefined') {
        console.log(
          `🧩 组件加载完成: ${options.componentName || 'unknown'} (${options.category}) - ${loadTime.toFixed(2)}ms`
        );
      }

      return module;
    });
  });
}

// ==================== 预加载管理器 ====================

export class PreloadManager {
  private static preloadedModules = new Set<string>();
  private static preloadPromises = new Map<string, Promise<any>>();

  /**
   * 预加载指定模块
   */
  static async preloadModule(moduleName: string, importFn: () => Promise<any>) {
    if (this.preloadedModules.has(moduleName)) {
      return this.preloadPromises.get(moduleName);
    }

    const startTime = performance.now();
    const promise = importFn().then(module => {
      const loadTime = performance.now() - startTime;
      console.log(`⚡ 模块预加载完成: ${moduleName} - ${loadTime.toFixed(2)}ms`);
      this.preloadedModules.add(moduleName);
      return module;
    });

    this.preloadPromises.set(moduleName, promise);
    return promise;
  }

  /**
   * 获取预加载状态
   */
  static getPreloadStatus() {
    return {
      preloadedModules: Array.from(this.preloadedModules),
      pendingPreloads: Array.from(this.preloadPromises.keys()).filter(
        key => !this.preloadedModules.has(key)
      ),
    };
  }

  /**
   * 清理预加载缓存
   */
  static clearCache() {
    this.preloadedModules.clear();
    this.preloadPromises.clear();
  }
}

// ==================== 性能监控 ====================

export function getCodeSplittingMetrics() {
  return {
    totalChunks: performance.getEntriesByType('navigation').length,
    preloadStatus: PreloadManager.getPreloadStatus(),
    cacheHitRate: calculateCacheHitRate(),
    bundleSizes: getBundleSizes(),
  };
}

function calculateCacheHitRate() {
  // 简化的缓存命中率计算
  const cached = PreloadManager.getPreloadStatus().preloadedModules.length;
  const total = Object.keys(ROUTE_SPLITTING_CONFIG.modules).length;
  return total > 0 ? ((cached / total) * 100).toFixed(2) + '%' : '0%';
}

function getBundleSizes() {
  // 简化的包大小信息
  return {
    vendor: '~800KB',
    app: '~400KB',
    chunks: '~200KB',
  };
}
