// src/components/sections/task-list/components/group-by-column-select-modal.tsx
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { But<PERSON> } from '@/shared/components/button';
import { Badge } from '@/shared/components/badge';
import { getGroupingOptions } from '@/core/utils/task-grouping';
import type { TaskGroupConfig, CustomColumnDefinition } from '@/core/types';

interface GroupByColumnSelectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (columnId: string) => void;
  groupConfig: TaskGroupConfig;
  availableColumns?: CustomColumnDefinition[];
}

/**
 * 按列分组选择模态框
 *
 * 在卡片模式下，用户点击分组按钮时显示此模态框，
 * 让用户选择要按哪个列进行分组
 */
export function GroupByColumnSelectModal({
  isOpen,
  onClose,
  onConfirm,
  groupConfig,
  availableColumns = [],
}: GroupByColumnSelectModalProps) {
  // 获取可用的分组选项
  const groupingOptions = getGroupingOptions(groupConfig);

  // 过滤掉"不分组"选项，因为这个模态框是用来选择分组的
  const validGroupingOptions = groupingOptions.filter(option => option.value !== 'none');

  const handleColumnSelect = (columnId: string) => {
    onConfirm(columnId);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <DialogTitle>选择分组列</DialogTitle>
          <DialogDescription>
            请选择要按哪个列对任务卡片进行分组显示。
            <br />
            分组后，任务卡片将按照所选列的值进行分组排列。
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-2 max-h-[300px] overflow-auto'>
          {validGroupingOptions.map(option => (
            <Button
              key={option.value}
              variant='outline'
              className='w-full justify-start h-auto p-3'
              onClick={() => handleColumnSelect(option.value)}
            >
              <div className='flex items-center gap-3 w-full'>
                <span className='text-lg'>{option.icon}</span>
                <div className='flex-1 text-left'>
                  <div className='font-medium'>{option.label}</div>
                  <div className='text-sm text-muted-foreground'>
                    按 {option.label.replace('按', '')} 分组
                  </div>
                </div>
                {groupConfig.groupBy === option.value && (
                  <Badge variant='default' className='text-xs'>
                    当前
                  </Badge>
                )}
              </div>
            </Button>
          ))}
        </div>

        {validGroupingOptions.length === 0 && (
          <div className='text-center py-8 text-muted-foreground'>
            <p>没有可用的分组选项</p>
            <p className='text-sm'>请检查分组配置</p>
          </div>
        )}

        <DialogFooter>
          <Button variant='outline' onClick={onClose}>
            取消
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
