// src/hooks/useQRCodeModal.ts
'use client';

import { useCallback, useState } from 'react';

import type { Task } from '@/core/types';

// src/hooks/useQRCodeModal.ts

interface UseQRCodeModalReturn {
  // QR Code Modal state
  isQRCodeModalOpen: boolean;
  selectedTaskForQRCode: Task | null;
  openQRCodeModal: (task: Task) => void;
  closeQRCodeModal: () => void;
}

export function useQRCodeModal(): UseQRCodeModalReturn {
  const [isQRCodeModalOpen, setIsQRCodeModalOpen] = useState(false);
  const [selectedTaskForQRCode, setSelectedTaskForQRCode] = useState<Task | null>(null);

  const openQRCodeModal = useCallback((task: Task) => {
    setSelectedTaskForQRCode(task);
    setIsQRCodeModalOpen(true);
  }, []);

  const closeQRCodeModal = useCallback(() => {
    setIsQRCodeModalOpen(false);
    setSelectedTaskForQRCode(null); // Clear selected task when closing
  }, []);

  return {
    isQRCodeModalOpen,
    selectedTaskForQRCode,
    openQRCodeModal,
    closeQRCodeModal,
  };
}
