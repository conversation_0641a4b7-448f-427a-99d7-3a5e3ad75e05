'use client';

import { But<PERSON> } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { useDragPerformanceMonitor } from '@/shared/hooks/useDragPerformanceMonitor';
import React, { useState } from 'react';
import { DragPerformanceSettings } from './DragPerformanceSettings';

export const DragPerformanceTest: React.FC = () => {
  const [isTestRunning, setIsTestRunning] = useState(false);
  const {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    isPerformanceGood,
    hasFrameDrops,
    hasExcessiveRenders,
  } = useDragPerformanceMonitor({
    enableMonitoring: true,
    logToConsole: true,
    fpsThreshold: 45,
  });

  const runPerformanceTest = () => {
    setIsTestRunning(true);
    startMonitoring();

    // 模拟拖拽操作
    let frameCount = 0;
    const testDuration = 3000; // 3秒测试
    const startTime = performance.now();

    const testFrame = () => {
      frameCount++;
      const elapsed = performance.now() - startTime;

      if (elapsed < testDuration) {
        // 模拟一些计算负载
        for (let i = 0; i < 1000; i++) {
          // 性能测试：执行一些计算
          const result = Math.random() * Math.random();
          // 避免编译器优化掉这个计算
          if (result < 0) console.log('Never happens');
        }
        requestAnimationFrame(testFrame);
      } else {
        stopMonitoring();
        setIsTestRunning(false);
      }
    };

    requestAnimationFrame(testFrame);
  };

  return (
    <div className='space-y-6 p-6'>
      <Card>
        <CardHeader>
          <CardTitle>🧪 拖拽性能测试</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex gap-4'>
            <Button onClick={runPerformanceTest} disabled={isTestRunning || isMonitoring}>
              {isTestRunning ? '测试中...' : '开始性能测试'}
            </Button>

            {isMonitoring && (
              <Button variant='outline' onClick={stopMonitoring}>
                停止监控
              </Button>
            )}
          </div>

          {metrics && (
            <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
              <div className='p-3 border rounded-lg'>
                <div className='text-sm font-medium'>测试时长</div>
                <div className='text-lg font-bold'>
                  {(metrics.totalDragDuration / 1000).toFixed(2)}s
                </div>
              </div>
              <div className='p-3 border rounded-lg'>
                <div className='text-sm font-medium'>平均 FPS</div>
                <div
                  className={`text-lg font-bold ${isPerformanceGood ? 'text-green-600' : 'text-red-600'}`}
                >
                  {metrics.averageFPS.toFixed(1)}
                </div>
              </div>
              <div className='p-3 border rounded-lg'>
                <div className='text-sm font-medium'>掉帧次数</div>
                <div
                  className={`text-lg font-bold ${hasFrameDrops ? 'text-yellow-600' : 'text-green-600'}`}
                >
                  {metrics.frameDrops}
                </div>
              </div>
              <div className='p-3 border rounded-lg'>
                <div className='text-sm font-medium'>渲染次数</div>
                <div
                  className={`text-lg font-bold ${hasExcessiveRenders ? 'text-red-600' : 'text-green-600'}`}
                >
                  {metrics.renderCount}
                </div>
              </div>
            </div>
          )}

          {metrics && (
            <div className='space-y-2'>
              <h4 className='font-medium'>性能评估:</h4>
              <div className='space-y-1 text-sm'>
                <div className={isPerformanceGood ? 'text-green-600' : 'text-red-600'}>
                  • FPS: {isPerformanceGood ? '良好' : '需要优化'}
                </div>
                <div className={!hasFrameDrops ? 'text-green-600' : 'text-yellow-600'}>
                  • 掉帧: {!hasFrameDrops ? '无掉帧' : '有掉帧现象'}
                </div>
                <div className={!hasExcessiveRenders ? 'text-green-600' : 'text-red-600'}>
                  • 渲染: {!hasExcessiveRenders ? '渲染次数正常' : '渲染次数过多'}
                </div>
                <div className={metrics.memoryUsage < 50 ? 'text-green-600' : 'text-yellow-600'}>
                  • 内存: {metrics.memoryUsage.toFixed(1)}MB{' '}
                  {metrics.memoryUsage < 50 ? '(正常)' : '(偏高)'}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <DragPerformanceSettings
        currentMetrics={metrics}
        onConfigChange={config => {
          console.log('性能配置更新:', config);
          // 这里可以将配置保存到 localStorage 或发送到服务器
        }}
      />
    </div>
  );
};
