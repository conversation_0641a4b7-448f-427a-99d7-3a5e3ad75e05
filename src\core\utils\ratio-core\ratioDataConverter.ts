/**
 * 配比数据转换工具
 * 统一处理各版本之间的数据格式转换
 */

import {
  RatioCoreMaterial,
  RatioCoreCalculationParams,
  RatioCoreCalculationResults,
  RatioCoreData,
  MaterialCategory,
  ExposureClass,
  PlacementMethod,
  FinishingRequirement,
  CalculationMethod,
} from '@/core/types/ratio-core';

import type {
  UnifiedRatioMaterial,
  LegacyRatioMaterial,
  RatioCalculationParams,
  CalculationResults,
} from '@/core/types/ratio';

// ==================== V1 版本转换 ====================

/**
 * 将V1版本的材料转换为核心格式
 */
export function convertV1MaterialToCore(v1Material: LegacyRatioMaterial): RatioCoreMaterial {
  // 扩展类型以访问可能存在的额外属性
  const extendedMaterial = v1Material as any;

  return {
    id: v1Material.id || `material_${Date.now()}`,
    name: extendedMaterial.name || v1Material.materialType,
    category: mapV1CategoryToCore(extendedMaterial.category || 'cement'),
    designValue: extendedMaterial.amount || v1Material.designValue || v1Material.theoryAmount || 0,
    actualValue: v1Material.actualAmount,
    unit: extendedMaterial.unit || 'kg/m³',
    density: extendedMaterial.density,
    specificGravity: extendedMaterial.specificGravity,
    grade: extendedMaterial.grade,
    supplier: extendedMaterial.supplier,
    ratio: extendedMaterial.ratio,
    percentage: extendedMaterial.percentage,
    version: 'v1',
    metadata: {
      originalData: v1Material,
      spec: v1Material.spec,
      binName: v1Material.binName,
      waterRatio: v1Material.waterRatio,
      stoneRatio: v1Material.stoneRatio,
    },
  };
}

/**
 * 将核心格式的材料转换为V1版本
 */
export function convertCoreToV1Material(coreMaterial: RatioCoreMaterial): LegacyRatioMaterial {
  // 从 metadata 中恢复原始 V1 属性
  const metadata = coreMaterial.metadata || {};

  return {
    id: coreMaterial.id,
    materialType: coreMaterial.name,
    spec: metadata['spec'] || '',
    theoryAmount: coreMaterial.designValue,
    waterRatio: metadata['waterRatio'] || 0,
    stoneRatio: metadata['stoneRatio'] || 0,
    actualAmount: (coreMaterial as any).actualValue || coreMaterial.designValue,
    designValue: coreMaterial.designValue,
    binName: metadata['binName'] || '',
  };
}

// ==================== V2 版本转换 ====================

/**
 * 将V2版本的材料转换为核心格式
 */
export function convertV2MaterialToCore(v2Material: UnifiedRatioMaterial): RatioCoreMaterial {
  return {
    id: v2Material.id,
    name: v2Material.name,
    category: mapV2CategoryToCore(v2Material.category),
    designValue: v2Material.designValue,
    actualValue: (v2Material as any).actualValue,
    unit: v2Material.unit,
    density: v2Material.density,
    specificGravity: (v2Material as any).specificGravity,
    absorptionRate: (v2Material as any).absorptionRate,
    fineness: (v2Material as any).fineness,
    grade: (v2Material as any).grade,
    supplier: v2Material.supplier,
    batchNumber: (v2Material as any).batchNumber,
    ratio: (v2Material as any).ratio,
    percentage: (v2Material as any).percentage,
    version: 'v2',
    metadata: {
      originalData: v2Material,
    },
  };
}

/**
 * 将核心格式的材料转换为V2版本
 */
export function convertCoreToV2Material(coreMaterial: RatioCoreMaterial): any {
  return {
    id: coreMaterial.id,
    name: coreMaterial.name,
    category: mapCoreToV2Category(coreMaterial.category),
    designValue: coreMaterial.designValue,
    actualValue: (coreMaterial as any).actualValue,
    unit: coreMaterial.unit,
    density: coreMaterial.density ?? 0,
    specificGravity: coreMaterial.specificGravity,
    absorptionRate: coreMaterial.absorptionRate,
    fineness: coreMaterial.fineness,
    grade: coreMaterial.grade,
    supplier: coreMaterial.supplier ?? '',
    batchNumber: coreMaterial.batchNumber,
    ratio: coreMaterial.ratio,
    percentage: coreMaterial.percentage,
  };
}

// ==================== 计算参数转换 ====================

/**
 * 将各版本的计算参数转换为核心格式
 */
export function convertCalculationParamsToCore(
  params: any,
  version: 'v1' | 'v2'
): RatioCoreCalculationParams {
  const baseParams: RatioCoreCalculationParams = {
    strengthGrade: params.strengthGrade || 30,
    slump: params.slump || 180,
    maxAggregateSize: params.maxAggregateSize || 25,
    exposureClass: params.exposureClass || ExposureClass.XC1,
    placementMethod: params.placementMethod || PlacementMethod.PUMP,
    finishingRequirement: params.finishingRequirement || FinishingRequirement.SMOOTH,
    waterCementRatio: params.waterCementRatio || 0.45,
    sandRatio: params.sandRatio || 35,
    cementAmount: params.cementAmount || 400,
    waterAmount: params.waterAmount || 180,
    calculationMethod: params.calculationMethod || CalculationMethod.STANDARD,
    version,
    metadata: {
      originalData: params,
    },
  };

  // 版本特定的参数处理
  if (version === 'v1') {
    // V1版本的特殊处理
    baseParams.density = params.density || 2400;
    baseParams.airContent = params.airContent || 4.5;
  } else {
    // V2版本的特殊处理
    baseParams.density = params.density || 2400;
    baseParams.airContent = params.airContent || 4.5;
    baseParams.ultraFineSandRatio = params.ultraFineSandRatio || 0;
    baseParams.earlyStrengthRatio = params.earlyStrengthRatio || 0;
  }

  return baseParams;
}

/**
 * 将核心格式的计算参数转换为指定版本
 */
export function convertCoreCalculationParamsToVersion(
  coreParams: RatioCoreCalculationParams,
  targetVersion: 'v1' | 'v2'
): any {
  const baseParams = {
    strengthGrade: coreParams.strengthGrade,
    slump: coreParams.slump,
    maxAggregateSize: coreParams.maxAggregateSize,
    exposureClass: coreParams.exposureClass,
    placementMethod: coreParams.placementMethod,
    finishingRequirement: coreParams.finishingRequirement,
    waterCementRatio: coreParams.waterCementRatio,
    sandRatio: coreParams.sandRatio,
    cementAmount: coreParams.cementAmount,
    waterAmount: coreParams.waterAmount,
    density: coreParams.density,
    airContent: coreParams.airContent,
    calculationMethod: coreParams.calculationMethod,
  };

  // 版本特定的参数
  if (targetVersion === 'v2') {
    return {
      ...baseParams,
      ultraFineSandRatio: coreParams.ultraFineSandRatio,
      earlyStrengthRatio: coreParams.earlyStrengthRatio,
    };
  }

  return baseParams;
}

// ==================== 计算结果转换 ====================

/**
 * 将各版本的计算结果转换为核心格式
 */
export function convertCalculationResultsToCore(
  results: any,
  version: 'v1' | 'v2'
): RatioCoreCalculationResults {
  return {
    totalWeight: results.totalWeight || 0,
    materials: results.materials || {},
    strengthPrediction: results.strengthPrediction || 0,
    workabilityScore: results.workabilityScore || results.qualityScore || 0,
    durabilityScore: results.durabilityScore || results.qualityScore || 0,
    qualityScore: results.qualityScore || 0,
    warnings: results.warnings || [],
    suggestions: results.suggestions || [],
    costEstimate: results.costEstimate || 0,
    carbonFootprint: results.carbonFootprint || 0,
    calculationTime: results.calculationTime || 0,
    calculationMethod: results.calculationMethod || CalculationMethod.STANDARD,
    timestamp: results.timestamp || new Date().toISOString(),
    version,
    metadata: {
      originalData: results,
    },
  };
}

// ==================== 类别映射函数 ====================

function mapV1CategoryToCore(v1Category: string): MaterialCategory {
  const mapping: Record<string, MaterialCategory> = {
    cement: MaterialCategory.CEMENT,
    water: MaterialCategory.WATER,
    sand: MaterialCategory.SAND,
    gravel: MaterialCategory.GRAVEL,
    additive: MaterialCategory.ADDITIVE,
    admixture: MaterialCategory.ADMIXTURE,
  };
  return mapping[v1Category] || MaterialCategory.ADDITIVE;
}

function mapCoreToV1Category(coreCategory: MaterialCategory): string {
  const mapping: Record<MaterialCategory, string> = {
    [MaterialCategory.CEMENT]: 'cement',
    [MaterialCategory.WATER]: 'water',
    [MaterialCategory.SAND]: 'sand',
    [MaterialCategory.GRAVEL]: 'gravel',
    [MaterialCategory.ADDITIVE]: 'additive',
    [MaterialCategory.FLY_ASH]: 'additive',
    [MaterialCategory.MINERAL_POWDER]: 'additive',
    [MaterialCategory.ADMIXTURE]: 'admixture',
  };
  return mapping[coreCategory] || 'additive';
}

function mapV2CategoryToCore(v2Category: any): MaterialCategory {
  if (typeof v2Category === 'string') {
    return mapV1CategoryToCore(v2Category);
  }
  return v2Category as MaterialCategory;
}

function mapCoreToV2Category(coreCategory: MaterialCategory): any {
  return coreCategory;
}

// ==================== 批量转换函数 ====================

/**
 * 批量转换材料数组
 */
export function convertMaterialsToCore(
  materials: any[],
  version: 'v1' | 'v2'
): RatioCoreMaterial[] {
  return materials.map(material => {
    switch (version) {
      case 'v1':
        return convertV1MaterialToCore(material);
      case 'v2':
        return convertV2MaterialToCore(material);

      default:
        throw new Error(`Unsupported version: ${version}`);
    }
  });
}

/**
 * 批量转换材料数组到指定版本
 */
export function convertCoreToVersionMaterials(
  coreMaterials: RatioCoreMaterial[],
  targetVersion: 'v1' | 'v2'
): any[] {
  return coreMaterials.map(material => {
    switch (targetVersion) {
      case 'v1':
        return convertCoreToV1Material(material);
      case 'v2':
        return convertCoreToV2Material(material);

      default:
        throw new Error(`Unsupported target version: ${targetVersion}`);
    }
  });
}

/**
 * 完整的配比数据转换
 */
export function convertRatioDataToCore(ratioData: any, version: 'v1' | 'v2'): RatioCoreData {
  return {
    id: ratioData.id || `ratio_${Date.now()}`,
    taskId: ratioData.taskId,
    name: ratioData.name || '未命名配比',
    description: ratioData.description,
    materials: convertMaterialsToCore(ratioData.materials || [], version),
    calculationParams: convertCalculationParamsToCore(ratioData.calculationParams || {}, version),
    calculationResults: ratioData.calculationResults
      ? convertCalculationResultsToCore(ratioData.calculationResults, version)
      : undefined,
    version,
    createdAt: ratioData.createdAt || new Date().toISOString(),
    updatedAt: ratioData.updatedAt || new Date().toISOString(),
    createdBy: ratioData.createdBy,
    status: ratioData.status || 'draft',
    tags: ratioData.tags,
    metadata: {
      originalData: ratioData,
    },
  };
}
