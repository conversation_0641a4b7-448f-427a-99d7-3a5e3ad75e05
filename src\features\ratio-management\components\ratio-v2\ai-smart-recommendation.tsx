'use client';

import React, { useEffect, useState } from 'react';

import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  BarChart3,
  Brain,
  CheckCircle2,
  Clock,
  DollarSign,
  Leaf,
  Lightbulb,
  Shield,
  Sparkles,
  Target,
  Zap,
} from 'lucide-react';

import { Alert, AlertDescription } from '@/shared/components/alert';
import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/card';
import { Progress } from '@/shared/components/progress';
import { Separator } from '@/shared/components/separator';
import { performanceLogger } from '@/core/lib/logger';

import {
  EnhancedCalculationParams,
  ExposureClass,
  FinishingRequirement,
  PlacementMethod,
} from './enhanced-calculation-engine';

interface AIRecommendationRequest {
  projectType: 'residential' | 'commercial' | 'infrastructure' | 'industrial' | 'marine';
  structuralElement: 'foundation' | 'column' | 'beam' | 'slab' | 'wall' | 'pavement';
  targetStrength: number;
  exposureConditions: string[];
  serviceLife: number; // 年
  budgetConstraint?: number; // 元/m³
  sustainabilityPriority: 'low' | 'medium' | 'high';
  performancePriorities: {
    strength: number; // 1-10
    durability: number; // 1-10
    workability: number; // 1-10
    economy: number; // 1-10
    sustainability: number; // 1-10
  };
  localMaterials?: string[]; // 本地可用材料
  specialRequirements?: string[];
}

interface AIRecommendation {
  id: string;
  name: string;
  description: string;
  confidence: number; // 0-100
  overallScore: number; // 0-100

  // 推荐的配比参数
  recommendedParams: EnhancedCalculationParams;

  // 预测性能
  predictedPerformance: {
    strength: number;
    durability: number;
    workability: number;
    economy: number;
    sustainability: number;
  };

  // 优势和劣势
  advantages: string[];
  disadvantages: string[];

  // 风险评估
  risks: {
    level: 'low' | 'medium' | 'high';
    factors: string[];
    mitigations: string[];
  };

  // 成本分析
  costAnalysis: {
    materialCost: number;
    laborCost: number;
    equipmentCost: number;
    totalCost: number;
    costEffectiveness: number;
  };

  // 环保分析
  environmentalImpact: {
    carbonFootprint: number;
    recycledContent: number;
    energyConsumption: number;
    sustainabilityRating: string;
  };
}

interface AISmartRecommendationProps {
  onRecommendationSelect: (recommendation: AIRecommendation) => void;
  currentParams?: Partial<EnhancedCalculationParams>;
}

export function AISmartRecommendation({ onRecommendationSelect }: AISmartRecommendationProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);
  const [selectedRecommendation, setSelectedRecommendation] = useState<AIRecommendation | null>(
    null
  );
  const [request, setRequest] = useState<AIRecommendationRequest>({
    projectType: 'commercial',
    structuralElement: 'slab',
    targetStrength: 30,
    exposureConditions: ['normal'],
    serviceLife: 50,
    sustainabilityPriority: 'medium',
    performancePriorities: {
      strength: 8,
      durability: 7,
      workability: 6,
      economy: 7,
      sustainability: 5,
    },
  });

  // 生成AI推荐
  const generateRecommendations = async () => {
    setIsAnalyzing(true);
    performanceLogger.info('AI recommendation generation started', { request });

    try {
      // 模拟AI分析过程
      await new Promise(resolve => setTimeout(resolve, 2000));

      const recommendations = await generateSmartRecommendations(request);
      setRecommendations(recommendations);

      performanceLogger.info('AI recommendations generated', {
        count: recommendations.length,
        avgConfidence:
          recommendations.reduce((sum, r) => sum + r.confidence, 0) / recommendations.length,
      });
    } catch (error) {
      performanceLogger.error('AI recommendation generation failed', error as Error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 生成智能推荐的核心逻辑
  const generateSmartRecommendations = async (
    req: AIRecommendationRequest
  ): Promise<AIRecommendation[]> => {
    const recommendations: AIRecommendation[] = [];

    // 推荐1: 高性能配比
    recommendations.push({
      id: 'high_performance',
      name: '高性能混凝土配比',
      description: '采用高效减水剂和优质掺合料，实现高强度和优异耐久性',
      confidence: 92,
      overallScore: 88,
      recommendedParams: {
        targetStrength: req.targetStrength * 1.2,
        slump: 180,
        maxAggregateSize: 20,
        exposureClass: ExposureClass.XC3,
        ambientTemperature: 20,
        relativeHumidity: 60,
        cementTemperature: 20,
        aggregateTemperature: 20,
        selectedMaterials: [
          'cement_p_o_42_5',
          'potable_water',
          'river_sand',
          'crushed_stone',
          'superplasticizer_pce',
          'silica_fume',
        ],
        cementType: 'cement_p_o_42_5',
        aggregateType: 'crushed_stone',
        waterType: 'potable_water',
        airContent: 4,
        placementMethod: PlacementMethod.PUMP,
        finishingRequirement: FinishingRequirement.SMOOTH,
        cureConditions: {
          method: 'moist',
          duration: 28,
          temperature: 20,
          humidity: 95,
        },
      },
      predictedPerformance: {
        strength: 95,
        durability: 90,
        workability: 85,
        economy: 70,
        sustainability: 75,
      },
      advantages: ['超高强度和耐久性', '优异的工作性能', '适合复杂结构', '长期性能稳定'],
      disadvantages: ['材料成本较高', '对施工要求严格', '需要专业养护'],
      risks: {
        level: 'low',
        factors: ['材料供应稳定性', '施工技术要求'],
        mitigations: ['提前备料', '技术培训', '质量控制'],
      },
      costAnalysis: {
        materialCost: 420,
        laborCost: 80,
        equipmentCost: 50,
        totalCost: 550,
        costEffectiveness: 0.18,
      },
      environmentalImpact: {
        carbonFootprint: 320,
        recycledContent: 15,
        energyConsumption: 180,
        sustainabilityRating: 'B+',
      },
    });

    // 推荐2: 经济型配比
    recommendations.push({
      id: 'economic',
      name: '经济型混凝土配比',
      description: '优化成本结构，采用本地材料和合理掺合料比例',
      confidence: 88,
      overallScore: 82,
      recommendedParams: {
        targetStrength: req.targetStrength,
        slump: 120,
        maxAggregateSize: 25,
        exposureClass: ExposureClass.XC2,
        ambientTemperature: 20,
        relativeHumidity: 60,
        cementTemperature: 20,
        aggregateTemperature: 20,
        selectedMaterials: [
          'cement_p_o_42_5',
          'potable_water',
          'river_sand',
          'crushed_stone',
          'superplasticizer_pce',
        ],
        cementType: 'cement_p_o_42_5',
        aggregateType: 'crushed_stone',
        waterType: 'potable_water',
        placementMethod: PlacementMethod.MANUAL,
        finishingRequirement: FinishingRequirement.ROUGH,
        cureConditions: {
          method: 'moist',
          duration: 7,
          temperature: 20,
          humidity: 85,
        },
      },
      predictedPerformance: {
        strength: 85,
        durability: 80,
        workability: 75,
        economy: 95,
        sustainability: 70,
      },
      advantages: ['成本控制优秀', '材料易获得', '施工简便', '适合大批量使用'],
      disadvantages: ['性能相对保守', '耐久性一般', '特殊环境适应性有限'],
      risks: {
        level: 'low',
        factors: ['质量控制', '环境适应性'],
        mitigations: ['严格配比', '适当养护', '质量检测'],
      },
      costAnalysis: {
        materialCost: 280,
        laborCost: 60,
        equipmentCost: 30,
        totalCost: 370,
        costEffectiveness: 0.22,
      },
      environmentalImpact: {
        carbonFootprint: 250,
        recycledContent: 25,
        energyConsumption: 120,
        sustainabilityRating: 'B',
      },
    });

    // 推荐3: 绿色环保配比
    recommendations.push({
      id: 'green',
      name: '绿色环保混凝土配比',
      description: '最大化使用再生材料和工业副产品，降低碳足迹',
      confidence: 85,
      overallScore: 86,
      recommendedParams: {
        targetStrength: req.targetStrength,
        slump: 150,
        maxAggregateSize: 20,
        exposureClass: ExposureClass.XC2,
        ambientTemperature: 20,
        relativeHumidity: 60,
        cementTemperature: 20,
        aggregateTemperature: 20,
        selectedMaterials: [
          'cement_p_o_42_5',
          'potable_water',
          'river_sand',
          'recycled_aggregate_coarse',
          'superplasticizer_pce',
        ],
        cementType: 'cement_p_o_42_5',
        aggregateType: 'recycled_aggregate_coarse',
        waterType: 'potable_water',
        placementMethod: PlacementMethod.PUMP,
        finishingRequirement: FinishingRequirement.SMOOTH,
        cureConditions: {
          method: 'moist',
          duration: 14,
          temperature: 20,
          humidity: 90,
        },
      },
      predictedPerformance: {
        strength: 80,
        durability: 85,
        workability: 80,
        economy: 85,
        sustainability: 95,
      },
      advantages: ['环保效益显著', '碳足迹最低', '资源循环利用', '符合绿色建筑标准'],
      disadvantages: ['材料质量变异性大', '需要严格质控', '长期性能数据有限'],
      risks: {
        level: 'medium',
        factors: ['再生材料质量', '长期耐久性', '标准符合性'],
        mitigations: ['材料检测', '试验验证', '分批使用'],
      },
      costAnalysis: {
        materialCost: 320,
        laborCost: 70,
        equipmentCost: 40,
        totalCost: 430,
        costEffectiveness: 0.19,
      },
      environmentalImpact: {
        carbonFootprint: 180,
        recycledContent: 45,
        energyConsumption: 90,
        sustainabilityRating: 'A',
      },
    });

    // 根据用户优先级排序
    return recommendations.sort((a, b) => {
      const scoreA = calculateWeightedScore(a, req.performancePriorities);
      const scoreB = calculateWeightedScore(b, req.performancePriorities);
      return scoreB - scoreA;
    });
  };

  // 计算加权分数
  const calculateWeightedScore = (
    recommendation: AIRecommendation,
    priorities: AIRecommendationRequest['performancePriorities']
  ) => {
    const weights = {
      strength: priorities.strength / 50,
      durability: priorities.durability / 50,
      workability: priorities.workability / 50,
      economy: priorities.economy / 50,
      sustainability: priorities.sustainability / 50,
    };

    return (
      recommendation.predictedPerformance.strength * weights.strength +
      recommendation.predictedPerformance.durability * weights.durability +
      recommendation.predictedPerformance.workability * weights.workability +
      recommendation.predictedPerformance.economy * weights.economy +
      recommendation.predictedPerformance.sustainability * weights.sustainability
    );
  };

  // 选择推荐
  const handleSelectRecommendation = (recommendation: AIRecommendation) => {
    setSelectedRecommendation(recommendation);
    onRecommendationSelect(recommendation);
    performanceLogger.info('AI recommendation selected', {
      recommendationId: recommendation.id,
      confidence: recommendation.confidence,
    });
  };

  useEffect(() => {
    generateRecommendations();
  }, []);

  return (
    <div className='space-y-6'>
      {/* 头部 */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-3'>
          <div className='p-2 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg'>
            <Brain className='w-6 h-6 text-white' />
          </div>
          <div>
            <h3 className='text-xl font-semibold'>AI智能配比推荐</h3>
            <p className='text-sm text-muted-foreground'>基于机器学习算法的智能配比优化建议</p>
          </div>
        </div>
        <Button onClick={generateRecommendations} disabled={isAnalyzing}>
          {isAnalyzing ? (
            <>
              <Sparkles className='w-4 h-4 mr-2 animate-spin' />
              分析中...
            </>
          ) : (
            <>
              <Zap className='w-4 h-4 mr-2' />
              重新分析
            </>
          )}
        </Button>
      </div>

      {/* 分析状态 */}
      {isAnalyzing && (
        <Card>
          <CardContent className='pt-6'>
            <div className='space-y-4'>
              <div className='flex items-center gap-3'>
                <Brain className='w-5 h-5 text-purple-500 animate-pulse' />
                <span className='font-medium'>AI正在分析您的需求...</span>
              </div>
              <Progress value={75} className='h-2' />
              <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                <div className='flex items-center gap-2'>
                  <CheckCircle2 className='w-4 h-4 text-green-500' />
                  <span>材料兼容性分析</span>
                </div>
                <div className='flex items-center gap-2'>
                  <CheckCircle2 className='w-4 h-4 text-green-500' />
                  <span>性能预测建模</span>
                </div>
                <div className='flex items-center gap-2'>
                  <Sparkles className='w-4 h-4 text-yellow-500 animate-spin' />
                  <span>成本效益优化</span>
                </div>
                <div className='flex items-center gap-2'>
                  <Clock className='w-4 h-4 text-gray-400' />
                  <span>风险评估</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 推荐结果 */}
      {recommendations.length > 0 && (
        <div className='grid gap-6 md:grid-cols-1 lg:grid-cols-1'>
          {recommendations.map((recommendation, index) => (
            <Card
              key={recommendation.id}
              className={`cursor-pointer transition-all duration-200 ${
                selectedRecommendation?.id === recommendation.id
                  ? 'ring-2 ring-purple-500 shadow-lg'
                  : 'hover:shadow-md'
              }`}
              onClick={() => handleSelectRecommendation(recommendation)}
            >
              <CardHeader>
                <div className='flex items-start justify-between'>
                  <div className='space-y-2'>
                    <div className='flex items-center gap-3'>
                      <Badge
                        variant='outline'
                        className='bg-gradient-to-r from-purple-50 to-pink-50'
                      >
                        #{index + 1} 推荐
                      </Badge>
                      <Badge
                        variant={recommendation.confidence >= 90 ? 'default' : 'secondary'}
                        className={recommendation.confidence >= 90 ? 'bg-green-500' : ''}
                      >
                        置信度 {recommendation.confidence}%
                      </Badge>
                    </div>
                    <CardTitle className='text-lg'>{recommendation.name}</CardTitle>
                    <CardDescription>{recommendation.description}</CardDescription>
                  </div>
                  <div className='text-right'>
                    <div className='text-2xl font-bold text-purple-600'>
                      {recommendation.overallScore}
                    </div>
                    <div className='text-sm text-muted-foreground'>综合评分</div>
                  </div>
                </div>
              </CardHeader>

              <CardContent className='space-y-4'>
                {/* 性能雷达图 */}
                <div className='grid grid-cols-5 gap-4'>
                  {Object.entries(recommendation.predictedPerformance).map(([key, value]) => (
                    <div key={key} className='text-center'>
                      <div className='relative w-16 h-16 mx-auto mb-2'>
                        <svg className='w-16 h-16 transform -rotate-90'>
                          <circle
                            cx='32'
                            cy='32'
                            r='28'
                            stroke='currentColor'
                            strokeWidth='4'
                            fill='none'
                            className='text-gray-200'
                          />
                          <circle
                            cx='32'
                            cy='32'
                            r='28'
                            stroke='currentColor'
                            strokeWidth='4'
                            fill='none'
                            strokeDasharray={`${(value / 100) * 175.93} 175.93`}
                            className='text-purple-500'
                          />
                        </svg>
                        <div className='absolute inset-0 flex items-center justify-center'>
                          <span className='text-sm font-semibold'>{value}</span>
                        </div>
                      </div>
                      <div className='text-xs text-muted-foreground capitalize'>
                        {key === 'strength'
                          ? '强度'
                          : key === 'durability'
                            ? '耐久性'
                            : key === 'workability'
                              ? '工作性'
                              : key === 'economy'
                                ? '经济性'
                                : '环保性'}
                      </div>
                    </div>
                  ))}
                </div>

                <Separator />

                {/* 优势和风险 */}
                <div className='grid md:grid-cols-2 gap-4'>
                  <div>
                    <h4 className='font-medium text-green-700 mb-2 flex items-center gap-2'>
                      <CheckCircle2 className='w-4 h-4' />
                      主要优势
                    </h4>
                    <ul className='space-y-1 text-sm'>
                      {recommendation.advantages.slice(0, 3).map((advantage, i) => (
                        <li key={i} className='flex items-start gap-2'>
                          <div className='w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0' />
                          {advantage}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className='font-medium text-amber-700 mb-2 flex items-center gap-2'>
                      <AlertTriangle className='w-4 h-4' />
                      注意事项
                    </h4>
                    <ul className='space-y-1 text-sm'>
                      {recommendation.disadvantages.slice(0, 3).map((disadvantage, i) => (
                        <li key={i} className='flex items-start gap-2'>
                          <div className='w-1.5 h-1.5 bg-amber-500 rounded-full mt-2 flex-shrink-0' />
                          {disadvantage}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <Separator />

                {/* 成本和环保指标 */}
                <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
                  <div className='text-center'>
                    <div className='flex items-center justify-center gap-1 mb-1'>
                      <DollarSign className='w-4 h-4 text-green-600' />
                      <span className='text-sm font-medium'>总成本</span>
                    </div>
                    <div className='text-lg font-bold text-green-600'>
                      ¥{recommendation.costAnalysis.totalCost}
                    </div>
                    <div className='text-xs text-muted-foreground'>/m³</div>
                  </div>

                  <div className='text-center'>
                    <div className='flex items-center justify-center gap-1 mb-1'>
                      <BarChart3 className='w-4 h-4 text-blue-600' />
                      <span className='text-sm font-medium'>性价比</span>
                    </div>
                    <div className='text-lg font-bold text-blue-600'>
                      {recommendation.costAnalysis.costEffectiveness.toFixed(2)}
                    </div>
                    <div className='text-xs text-muted-foreground'>MPa/¥</div>
                  </div>

                  <div className='text-center'>
                    <div className='flex items-center justify-center gap-1 mb-1'>
                      <Leaf className='w-4 h-4 text-green-600' />
                      <span className='text-sm font-medium'>碳足迹</span>
                    </div>
                    <div className='text-lg font-bold text-green-600'>
                      {recommendation.environmentalImpact.carbonFootprint}
                    </div>
                    <div className='text-xs text-muted-foreground'>kg CO₂</div>
                  </div>

                  <div className='text-center'>
                    <div className='flex items-center justify-center gap-1 mb-1'>
                      <Shield className='w-4 h-4 text-purple-600' />
                      <span className='text-sm font-medium'>环保等级</span>
                    </div>
                    <div className='text-lg font-bold text-purple-600'>
                      {recommendation.environmentalImpact.sustainabilityRating}
                    </div>
                    <div className='text-xs text-muted-foreground'>评级</div>
                  </div>
                </div>

                {/* 选择按钮 */}
                <Button
                  className='w-full'
                  variant={selectedRecommendation?.id === recommendation.id ? 'default' : 'outline'}
                  onClick={e => {
                    e.stopPropagation();
                    handleSelectRecommendation(recommendation);
                  }}
                >
                  {selectedRecommendation?.id === recommendation.id ? (
                    <>
                      <CheckCircle2 className='w-4 h-4 mr-2' />
                      已选择此方案
                    </>
                  ) : (
                    <>
                      <Target className='w-4 h-4 mr-2' />
                      选择此方案
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 选中推荐的详细信息 */}
      {selectedRecommendation && (
        <Alert>
          <Lightbulb className='h-4 w-4' />
          <AlertDescription>
            已选择 <strong>{selectedRecommendation.name}</strong>。
            系统将根据此推荐自动调整配比参数，您可以在设计面板中进一步微调。
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
