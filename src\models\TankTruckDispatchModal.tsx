'use client';

/**
 * 罐车调度模态框
 * 占位符组件，用于懒加载测试
 */

import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';

interface TankTruckDispatchModalProps {
  open?: boolean;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onOpenChangeAction?: (open: boolean) => void;
  vehicleId?: string;
  taskId?: string;
}

const TankTruckDispatchModal: React.FC<TankTruckDispatchModalProps> = ({
  open = false,
  isOpen = false,
  onOpenChange,
  onOpenChangeAction,
  vehicleId,
  taskId,
}) => {
  const modalOpen = open || isOpen;
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange?.(newOpen);
    onOpenChangeAction?.(newOpen);
  };

  return (
    <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
      <DialogContent className='max-w-4xl'>
        <DialogHeader>
          <DialogTitle>罐车调度</DialogTitle>
        </DialogHeader>
        <div className='p-4'>
          <p>罐车调度功能正在开发中...</p>
          {vehicleId && <p className='text-sm text-gray-500'>车辆ID: {vehicleId}</p>}
          {taskId && <p className='text-sm text-gray-500'>任务ID: {taskId}</p>}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TankTruckDispatchModal;
