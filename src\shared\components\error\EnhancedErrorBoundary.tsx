/**
 * 增强版错误边界组件
 * 集成统一错误管理系统，提供智能错误恢复和用户友好的错误界面
 */

'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, Bug, Home, RefreshCw, Send, Wifi, WifiOff } from 'lucide-react';

import { Button } from '@/shared/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/card';
import { Progress } from '@/shared/components/progress';
import { Textarea } from '@/shared/components/textarea';
import { errorManager } from '@/infrastructure/error-handling/ErrorManager';
import { AppError, ErrorCategory, ErrorSeverity } from '@/core/types/error';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  level?: 'page' | 'section' | 'component';
  feature?: string;
  onError?: (error: AppError) => void;
  showDetails?: boolean;
  enableRecovery?: boolean;
  maxRetries?: number;
}

interface State {
  hasError: boolean;
  error: AppError | null;
  errorInfo: ErrorInfo | null;
  isRecovering: boolean;
  retryCount: number;
  userFeedback: string;
  isOnline: boolean;
  recoveryProgress: number;
}

export class EnhancedErrorBoundary extends Component<Props, State> {
  private retryTimer?: NodeJS.Timeout;
  private recoveryTimer?: NodeJS.Timeout;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isRecovering: false,
      retryCount: 0,
      userFeedback: '',
      isOnline: navigator.onLine,
      recoveryProgress: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 将原生Error转换为AppError
    const appError =
      error instanceof AppError
        ? error
        : AppError.component(`组件渲染错误: ${error.message}`, {
            originalError: error,
            stack: error.stack,
          });

    return {
      hasError: true,
      error: appError,
    };
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const appError = this.state.error || AppError.component(error.message);

    // 使用错误管理器处理错误
    errorManager.handleError(appError, {
      component: this.props.feature || 'UnknownComponent',
      action: 'render',
      metadata: {
        componentStack: errorInfo.componentStack,
        level: this.props.level || 'component',
        retryCount: this.state.retryCount,
      },
    });

    this.setState({
      errorInfo,
    });

    // 调用外部错误处理器
    if (this.props.onError) {
      this.props.onError(appError);
    }

    // 如果启用了自动恢复，开始恢复流程
    if (this.props.enableRecovery && this.shouldAttemptRecovery(appError)) {
      this.startRecovery();
    }
  }

  override componentDidMount() {
    // 监听网络状态变化
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
  }

  override componentWillUnmount() {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);

    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
    }
    if (this.recoveryTimer) {
      clearTimeout(this.recoveryTimer);
    }
  }

  handleOnline = () => {
    this.setState({ isOnline: true });

    // 如果是网络错误且现在重新联网，自动重试
    if (this.state.error?.category === ErrorCategory.NETWORK) {
      this.handleRetry();
    }
  };

  handleOffline = () => {
    this.setState({ isOnline: false });
  };

  shouldAttemptRecovery = (error: AppError): boolean => {
    const maxRetries = this.props.maxRetries || 3;
    return (
      this.state.retryCount < maxRetries &&
      (error.category === ErrorCategory.NETWORK ||
        error.category === ErrorCategory.API ||
        error.severity !== ErrorSeverity.CRITICAL)
    );
  };

  startRecovery = () => {
    this.setState({ isRecovering: true, recoveryProgress: 0 });

    // 模拟恢复进度
    let progress = 0;
    this.recoveryTimer = setInterval(() => {
      progress += 20;
      this.setState({ recoveryProgress: progress });

      if (progress >= 100) {
        clearInterval(this.recoveryTimer!);
        this.handleRetry();
      }
    }, 500);
  };

  handleRetry = () => {
    const newRetryCount = this.state.retryCount + 1;

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      isRecovering: false,
      retryCount: newRetryCount,
      recoveryProgress: 0,
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleSendFeedback = async () => {
    if (!this.state.error || !this.state.userFeedback.trim()) {
      return;
    }

    try {
      // 这里可以发送用户反馈到服务器
      console.log('User feedback sent:', {
        errorId: this.state.error.id,
        feedback: this.state.userFeedback,
      });

      // 显示成功消息
      alert('感谢您的反馈！我们会尽快处理这个问题。');
      this.setState({ userFeedback: '' });
    } catch (error) {
      console.error('Failed to send feedback:', error);
      alert('反馈发送失败，请稍后重试。');
    }
  };

  getErrorIcon = () => {
    if (!this.state.error) return <AlertTriangle className='w-6 h-6 text-destructive' />;

    switch (this.state.error.category) {
      case ErrorCategory.NETWORK:
        return this.state.isOnline ? (
          <Wifi className='w-6 h-6 text-orange-500' />
        ) : (
          <WifiOff className='w-6 h-6 text-red-500' />
        );
      case ErrorCategory.API:
        return <Bug className='w-6 h-6 text-blue-500' />;
      case ErrorCategory.COMPONENT:
        return <AlertTriangle className='w-6 h-6 text-purple-500' />;
      default:
        return <AlertTriangle className='w-6 h-6 text-destructive' />;
    }
  };

  getErrorTitle = () => {
    if (!this.state.error) return '出现了一个错误';

    switch (this.state.error.category) {
      case ErrorCategory.NETWORK:
        return this.state.isOnline ? '网络连接异常' : '网络连接已断开';
      case ErrorCategory.API:
        return '服务暂时不可用';
      case ErrorCategory.COMPONENT:
        return '页面显示异常';
      case ErrorCategory.VALIDATION:
        return '数据验证失败';
      default:
        return '出现了一个错误';
    }
  };

  getErrorDescription = () => {
    if (!this.state.error) return '很抱歉，应用程序遇到了意外错误。';

    switch (this.state.error.category) {
      case ErrorCategory.NETWORK:
        return this.state.isOnline
          ? '网络连接不稳定，请检查您的网络设置。'
          : '请检查您的网络连接，然后重试。';
      case ErrorCategory.API:
        return '服务器暂时无法响应，我们正在努力修复。';
      case ErrorCategory.COMPONENT:
        return '页面组件加载失败，请尝试刷新页面。';
      case ErrorCategory.VALIDATION:
        return '输入的数据格式不正确，请检查后重试。';
      default:
        return '很抱歉，应用程序遇到了意外错误。';
    }
  };

  override render() {
    if (this.state.hasError) {
      // 如果提供了自定义 fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error } = this.state;
      const maxRetries = this.props.maxRetries || 3;
      const canRetry = this.state.retryCount < maxRetries;

      // 恢复中的界面
      if (this.state.isRecovering) {
        return (
          <div className='min-h-[200px] flex items-center justify-center p-4'>
            <Card className='w-full max-w-md'>
              <CardContent className='p-6 text-center'>
                <RefreshCw className='w-8 h-8 mx-auto mb-4 animate-spin text-blue-500' />
                <h3 className='text-lg font-semibold mb-2'>正在恢复...</h3>
                <p className='text-sm text-muted-foreground mb-4'>系统正在尝试自动修复问题</p>
                <Progress value={this.state.recoveryProgress} className='w-full' />
                <p className='text-xs text-muted-foreground mt-2'>{this.state.recoveryProgress}%</p>
              </CardContent>
            </Card>
          </div>
        );
      }

      // 错误界面
      return (
        <div className='min-h-[400px] flex items-center justify-center p-4 bg-background'>
          <Card className='w-full max-w-2xl'>
            <CardHeader className='text-center'>
              <div className='mx-auto mb-4 w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center'>
                {this.getErrorIcon()}
              </div>
              <CardTitle className='text-2xl'>{this.getErrorTitle()}</CardTitle>
              <CardDescription>{this.getErrorDescription()}</CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              {/* 网络状态指示器 */}
              {error?.category === ErrorCategory.NETWORK && (
                <div
                  className={`flex items-center gap-2 p-3 rounded-lg ${
                    this.state.isOnline ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                  }`}
                >
                  {this.state.isOnline ? (
                    <Wifi className='w-4 h-4' />
                  ) : (
                    <WifiOff className='w-4 h-4' />
                  )}
                  <span className='text-sm'>
                    {this.state.isOnline ? '网络已连接' : '网络已断开'}
                  </span>
                </div>
              )}

              {/* 重试信息 */}
              {this.state.retryCount > 0 && (
                <div className='text-center text-sm text-muted-foreground'>
                  已重试 {this.state.retryCount} 次{canRetry && ` (最多 ${maxRetries} 次)`}
                </div>
              )}

              {/* 操作按钮 */}
              <div className='flex flex-wrap gap-2 justify-center'>
                {canRetry && (
                  <Button onClick={this.handleRetry} className='flex items-center gap-2'>
                    <RefreshCw className='w-4 h-4' />
                    重试
                  </Button>
                )}

                <Button
                  variant='outline'
                  onClick={this.handleReload}
                  className='flex items-center gap-2'
                >
                  <RefreshCw className='w-4 h-4' />
                  刷新页面
                </Button>

                <Button
                  variant='outline'
                  onClick={this.handleGoHome}
                  className='flex items-center gap-2'
                >
                  <Home className='w-4 h-4' />
                  返回首页
                </Button>
              </div>

              {/* 用户反馈 */}
              <div className='space-y-3'>
                <h4 className='text-sm font-medium'>遇到问题？告诉我们发生了什么：</h4>
                <Textarea
                  placeholder='请描述您遇到的问题或您当时正在做什么...'
                  value={this.state.userFeedback}
                  onChange={e => this.setState({ userFeedback: e.target.value })}
                  className='min-h-[80px]'
                />
                <Button
                  variant='outline'
                  size='sm'
                  onClick={this.handleSendFeedback}
                  disabled={!this.state.userFeedback.trim()}
                  className='flex items-center gap-2'
                >
                  <Send className='w-4 h-4' />
                  发送反馈
                </Button>
              </div>

              {/* 错误详情 (仅在开发环境或明确要求时显示) */}
              {(process.env.NODE_ENV === 'development' || this.props.showDetails) && error && (
                <details className='mt-6'>
                  <summary className='cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground'>
                    查看技术详情
                  </summary>
                  <div className='mt-2 p-4 bg-muted rounded-lg'>
                    <div className='space-y-2 text-sm'>
                      <div>
                        <strong>错误ID:</strong> {error.id}
                      </div>
                      <div>
                        <strong>错误代码:</strong> {error.code}
                      </div>
                      <div>
                        <strong>类别:</strong> {error.category}
                      </div>
                      <div>
                        <strong>严重程度:</strong> {error.severity}
                      </div>
                      <div>
                        <strong>时间:</strong> {new Date(error.timestamp).toLocaleString()}
                      </div>
                      <div>
                        <strong>消息:</strong> {error.message}
                      </div>
                      {error.stack && (
                        <div>
                          <strong>堆栈:</strong>
                          <pre className='mt-1 text-xs bg-background p-2 rounded overflow-auto max-h-40'>
                            {error.stack}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                </details>
              )}

              {/* 帮助信息 */}
              <div className='text-center text-sm text-muted-foreground'>
                <p>如果问题持续存在，请联系技术支持。</p>
                {error && <p className='mt-1'>错误ID: {error.id}</p>}
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// 高阶组件，用于包装其他组件
export function withEnhancedErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const EnhancedComponent = (props: P) => (
    <EnhancedErrorBoundary {...errorBoundaryProps}>
      {React.createElement(WrappedComponent, props)}
    </EnhancedErrorBoundary>
  );

  EnhancedComponent.displayName = `withEnhancedErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;
  return EnhancedComponent;
}
