/**
 * ConfigProvider - 统一配置管理提供者
 * 整合分散的样式、密度、主题配置，提供统一的配置管理系统
 */

'use client';

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  ReactNode,
} from 'react';

/**
 * 应用配置接口
 */
export interface AppConfig {
  // 主题配置
  theme: {
    mode: 'light' | 'dark' | 'auto';
    primaryColor: string;
    accentColor: string;
    borderRadius: number;
    fontFamily: string;
  };

  // 密度配置
  density: {
    mode: 'compact' | 'comfortable' | 'spacious';
    padding: number;
    spacing: number;
    fontSize: number;
    iconSize: number;
  };

  // 表格配置
  table: {
    columnColors: Record<string, string>;
    defaultColumnWidth: number;
    minFontSize: number;
    showBorders: boolean;
    alternateRowColors: boolean;
  };

  // 卡片配置
  card: {
    vehicleBackgroundColor: string;
    taskCardHeight: number;
    taskCardWidth: number;
    showShadows: boolean;
    borderStyle: 'solid' | 'dashed' | 'dotted';
  };

  // 任务列表配置
  taskList: {
    mode: 'table' | 'card' | 'hybrid';
    enableDragDrop: boolean;
    autoRefresh: boolean;
    refreshInterval: number;
  };

  // 配比页面配置
  ratio: {
    defaultVersion: 'v1' | 'v2';
    enableAutoSave: boolean;
    autoSaveDelay: number;
    showAdvancedFeatures: boolean;
  };

  // 性能配置
  performance: {
    enableVirtualization: boolean;
    chunkSize: number;
    debounceDelay: number;
    cacheSize: number;
  };
}

/**
 * 默认配置
 */
export const DEFAULT_CONFIG: AppConfig = {
  theme: {
    mode: 'light',
    primaryColor: '#2563eb',
    accentColor: '#7c3aed',
    borderRadius: 6,
    fontFamily: 'system-ui',
  },
  density: {
    mode: 'compact',
    padding: 8,
    spacing: 8,
    fontSize: 14,
    iconSize: 16,
  },
  table: {
    columnColors: {
      taskNumber: '#f3f4f6',
      vehicleInfo: '#fef3c7',
      dispatchInfo: '#dbeafe',
      status: '#f0fdf4',
    },
    defaultColumnWidth: 140,
    minFontSize: 12,
    showBorders: true,
    alternateRowColors: true,
  },
  card: {
    vehicleBackgroundColor: '#fce7f3',
    taskCardHeight: 120,
    taskCardWidth: 280,
    showShadows: true,
    borderStyle: 'solid',
  },
  taskList: {
    mode: 'table',
    enableDragDrop: true,
    autoRefresh: false,
    refreshInterval: 30000,
  },
  ratio: {
    defaultVersion: 'v2',
    enableAutoSave: true,
    autoSaveDelay: 3000,
    showAdvancedFeatures: true,
  },
  performance: {
    enableVirtualization: true,
    chunkSize: 50,
    debounceDelay: 300,
    cacheSize: 100,
  },
};

/**
 * 配置上下文接口
 */
interface ConfigContextValue {
  config: AppConfig;
  updateConfig: (updates: Partial<AppConfig>) => void;
  resetConfig: () => void;
  exportConfig: () => string;
  importConfig: (configJson: string) => boolean;
  isLoading: boolean;
  error: string | null;
}

/**
 * 配置上下文
 */
const ConfigContext = createContext<ConfigContextValue | null>(null);

/**
 * 配置存储键
 */
const CONFIG_STORAGE_KEY = 'tmh-app-config';
const CONFIG_FILE_PATH = '/config/app-config.json';

/**
 * ConfigProvider 组件
 */
export function ConfigProvider({ children }: { children: ReactNode }) {
  const [config, setConfig] = useState<AppConfig>(DEFAULT_CONFIG);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // ==================== 配置加载 ====================

  const loadConfig = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 1. 优先从 localStorage 加载
      const localConfig = localStorage.getItem(CONFIG_STORAGE_KEY);
      if (localConfig) {
        try {
          const parsedConfig = JSON.parse(localConfig);
          const mergedConfig = { ...DEFAULT_CONFIG, ...parsedConfig };
          setConfig(mergedConfig);
          console.log('从 localStorage 加载配置成功');
          return;
        } catch (parseError) {
          console.warn('localStorage 配置解析失败，尝试从文件加载');
        }
      }

      // 2. 从配置文件加载（fallback）
      try {
        const response = await fetch(CONFIG_FILE_PATH);
        if (response.ok) {
          const fileConfig = await response.json();
          const mergedConfig = { ...DEFAULT_CONFIG, ...fileConfig };
          setConfig(mergedConfig);

          // 同步到 localStorage
          localStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(mergedConfig));
          console.log('从配置文件加载配置成功');
          return;
        }
      } catch (fileError) {
        console.warn('配置文件加载失败，使用默认配置');
      }

      // 3. 使用默认配置
      setConfig(DEFAULT_CONFIG);
      localStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(DEFAULT_CONFIG));
      console.log('使用默认配置');
    } catch (error) {
      console.error('配置加载失败:', error);
      setError(error instanceof Error ? error.message : '配置加载失败');
      setConfig(DEFAULT_CONFIG);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // ==================== 配置更新 ====================

  const updateConfig = useCallback((updates: Partial<AppConfig>) => {
    setConfig(prevConfig => {
      const newConfig = { ...prevConfig, ...updates };

      // 深度合并嵌套对象
      Object.keys(updates).forEach(key => {
        if (
          typeof updates[key as keyof AppConfig] === 'object' &&
          updates[key as keyof AppConfig] !== null
        ) {
          newConfig[key as keyof AppConfig] = {
            ...prevConfig[key as keyof AppConfig],
            ...updates[key as keyof AppConfig],
          } as any;
        }
      });

      // 保存到 localStorage
      try {
        localStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(newConfig));
        console.log('配置更新并保存成功');
      } catch (error) {
        console.error('配置保存失败:', error);
        setError('配置保存失败');
      }

      return newConfig;
    });
  }, []);

  // ==================== 配置重置 ====================

  const resetConfig = useCallback(() => {
    setConfig(DEFAULT_CONFIG);
    localStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(DEFAULT_CONFIG));
    console.log('配置已重置为默认值');
  }, []);

  // ==================== 配置导入导出 ====================

  const exportConfig = useCallback(() => {
    return JSON.stringify(config, null, 2);
  }, [config]);

  const importConfig = useCallback((configJson: string) => {
    try {
      const importedConfig = JSON.parse(configJson);
      const mergedConfig = { ...DEFAULT_CONFIG, ...importedConfig };

      // 验证配置结构
      if (!mergedConfig || typeof mergedConfig !== 'object') {
        setError('导入的配置格式无效');
        return false;
      }

      setConfig(mergedConfig);
      localStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(mergedConfig));
      console.log('配置导入成功');
      return true;
    } catch (error) {
      console.error('配置导入失败:', error);
      setError('配置导入失败: ' + (error instanceof Error ? error.message : '未知错误'));
      return false;
    }
  }, []);

  // ==================== 配置验证 ====================

  const isValidConfig = useCallback((config: any): config is AppConfig => {
    return (
      config &&
      typeof config === 'object' &&
      config.theme &&
      config.density &&
      config.table &&
      config.card &&
      config.taskList &&
      config.ratio &&
      config.performance
    );
  }, []);

  // ==================== 初始化 ====================

  useEffect(() => {
    loadConfig();
  }, [loadConfig]);

  // ==================== CSS 变量更新 ====================

  useEffect(() => {
    // 更新 CSS 自定义属性以实现实时样式更新
    const root = document.documentElement;

    // 主题变量
    root.style.setProperty('--primary-color', config.theme.primaryColor);
    root.style.setProperty('--accent-color', config.theme.accentColor);
    root.style.setProperty('--border-radius', `${config.theme.borderRadius}px`);
    root.style.setProperty('--font-family', config.theme.fontFamily);

    // 密度变量
    root.style.setProperty('--density-padding', `${config.density.padding}px`);
    root.style.setProperty('--density-spacing', `${config.density.spacing}px`);
    root.style.setProperty('--density-font-size', `${config.density.fontSize}px`);
    root.style.setProperty('--density-icon-size', `${config.density.iconSize}px`);

    // 表格变量
    root.style.setProperty('--table-column-width', `${config.table.defaultColumnWidth}px`);
    root.style.setProperty('--table-min-font-size', `${config.table.minFontSize}px`);

    // 卡片变量
    root.style.setProperty('--vehicle-bg-color', config.card.vehicleBackgroundColor);
    root.style.setProperty('--task-card-height', `${config.card.taskCardHeight}px`);
    root.style.setProperty('--task-card-width', `${config.card.taskCardWidth}px`);

    console.log('CSS 变量已更新');
  }, [config]);

  // ==================== 上下文值 ====================

  const contextValue: ConfigContextValue = {
    config,
    updateConfig,
    resetConfig,
    exportConfig,
    importConfig,
    isLoading,
    error,
  };

  return <ConfigContext.Provider value={contextValue}>{children}</ConfigContext.Provider>;
}

/**
 * 使用配置的 Hook
 */
export function useConfig() {
  const context = useContext(ConfigContext);
  if (!context) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
}

/**
 * 使用特定配置部分的 Hook
 */
export function useThemeConfig() {
  const { config, updateConfig } = useConfig();
  return {
    theme: config.theme,
    updateTheme: (updates: Partial<AppConfig['theme']>) =>
      updateConfig({ theme: { ...config.theme, ...updates } }),
  };
}

export function useDensityConfig() {
  const { config, updateConfig } = useConfig();
  return {
    density: config.density,
    updateDensity: (updates: Partial<AppConfig['density']>) =>
      updateConfig({ density: { ...config.density, ...updates } }),
  };
}

export function useTableConfig() {
  const { config, updateConfig } = useConfig();
  return {
    table: config.table,
    updateTable: (updates: Partial<AppConfig['table']>) =>
      updateConfig({ table: { ...config.table, ...updates } }),
  };
}

export function useCardConfig() {
  const { config, updateConfig } = useConfig();
  return {
    card: config.card,
    updateCard: (updates: Partial<AppConfig['card']>) =>
      updateConfig({ card: { ...config.card, ...updates } }),
  };
}

export function useTaskListConfig() {
  const { config, updateConfig } = useConfig();
  return {
    taskList: config.taskList,
    updateTaskList: (updates: Partial<AppConfig['taskList']>) =>
      updateConfig({ taskList: { ...config.taskList, ...updates } }),
  };
}

export function useRatioConfig() {
  const { config, updateConfig } = useConfig();
  return {
    ratio: config.ratio,
    updateRatio: (updates: Partial<AppConfig['ratio']>) =>
      updateConfig({ ratio: { ...config.ratio, ...updates } }),
  };
}

export function usePerformanceConfig() {
  const { config, updateConfig } = useConfig();
  return {
    performance: config.performance,
    updatePerformance: (updates: Partial<AppConfig['performance']>) =>
      updateConfig({ performance: { ...config.performance, ...updates } }),
  };
}
