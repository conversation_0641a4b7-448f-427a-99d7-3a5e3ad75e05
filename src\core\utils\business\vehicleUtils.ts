/**
 * 车辆相关业务逻辑工具函数
 * 集中管理车辆处理的通用逻辑
 */

import type { Vehicle } from '@/core/types';
import { VehicleStatus } from '@/core/types/unified/core';

/**
 * 获取车辆状态的中文显示
 */
export function getVehicleStatusText(status: VehicleStatus): string {
  const statusMap: Record<VehicleStatus, string> = {
    pending: '待发车',
    outbound: '在途',
    returned: '已返回',
    maintenance: '维护中',
    offline: '离线',
  };
  return statusMap[status] || '未知状态';
}

/**
 * 获取车辆状态的颜色
 */
export function getVehicleStatusColor(status: VehicleStatus): string {
  const colorMap: Record<VehicleStatus, string> = {
    pending: 'blue',
    outbound: 'green',
    returned: 'gray',
    maintenance: 'orange',
    offline: 'red',
  };
  return colorMap[status] || 'gray';
}

/**
 * 检查车辆是否可用于调度
 */
export function isVehicleAvailable(vehicle: Vehicle): boolean {
  // 基本状态检查
  if (!['pending', 'returned'].includes(vehicle.status)) {
    return false;
  }

  // 运营状态检查
  if (vehicle.operationalStatus && vehicle.operationalStatus !== 'normal') {
    return false;
  }

  // 检查是否已分配任务
  if (vehicle.assignedTaskId) {
    return false;
  }

  return true;
}

/**
 * 检查车辆是否在维护状态
 */
export function isVehicleInMaintenance(vehicle: Vehicle): boolean {
  return vehicle.operationalStatus === 'paused' || vehicle.operationalStatus === 'deactivated';
}

/**
 * 检查车辆是否离线
 */
export function isVehicleOffline(vehicle: Vehicle): boolean {
  return vehicle.operationalStatus === 'deactivated';
}

/**
 * 获取车辆的显示名称
 */
export function getVehicleDisplayName(vehicle: Vehicle): string {
  return vehicle.vehicleNumber || `车辆${vehicle.id}`;
}

/**
 * 获取车辆的容量信息
 */
export function getVehicleCapacityInfo(vehicle: Vehicle): {
  capacity: number;
  unit: string;
  displayText: string;
} {
  const capacity = (vehicle as any).capacity || 0;
  const unit = (vehicle as any).capacityUnit || 'm³';
  const displayText = `${capacity}${unit}`;

  return { capacity, unit, displayText };
}

/**
 * 计算车辆的利用率
 */
export function calculateVehicleUtilization(vehicle: Vehicle): number {
  const totalTrips = (vehicle as any).totalTrips;
  if (!totalTrips || totalTrips === 0) return 0;

  const workingDays = 30; // 假设30天为一个周期
  const maxTripsPerDay = 8; // 假设每天最多8趟
  const maxTrips = workingDays * maxTripsPerDay;

  return Math.min(100, (totalTrips / maxTrips) * 100);
}

/**
 * 获取车辆的工作状态
 */
export function getVehicleWorkStatus(
  vehicle: Vehicle
): 'idle' | 'working' | 'maintenance' | 'offline' {
  if (isVehicleOffline(vehicle)) return 'offline';
  if (isVehicleInMaintenance(vehicle)) return 'maintenance';
  if (vehicle.status === 'outbound') return 'working';
  return 'idle';
}

/**
 * 计算车辆的平均行程时间
 */
export function calculateAverageTripTime(vehicle: Vehicle): number | null {
  const totalTrips = (vehicle as any).totalTrips;
  const totalWorkingHours = (vehicle as any).totalWorkingHours;
  if (!totalTrips || !totalWorkingHours) return null;

  return totalWorkingHours / totalTrips;
}

/**
 * 获取车辆的效率评级
 */
export function getVehicleEfficiencyRating(
  vehicle: Vehicle
): 'excellent' | 'good' | 'average' | 'poor' {
  const utilization = calculateVehicleUtilization(vehicle);

  if (utilization >= 80) return 'excellent';
  if (utilization >= 60) return 'good';
  if (utilization >= 40) return 'average';
  return 'poor';
}

/**
 * 检查车辆是否需要维护
 */
export function needsMaintenance(vehicle: Vehicle): boolean {
  const vehicleAny = vehicle as any;

  // 基于里程数检查
  if (vehicleAny.mileage && vehicleAny.lastMaintenanceMileage) {
    const mileageSinceLastMaintenance = vehicleAny.mileage - vehicleAny.lastMaintenanceMileage;
    if (mileageSinceLastMaintenance > 10000) return true; // 超过10000公里
  }

  // 基于时间检查
  if (vehicleAny.lastMaintenanceDate) {
    const daysSinceLastMaintenance =
      (Date.now() - new Date(vehicleAny.lastMaintenanceDate).getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceLastMaintenance > 90) return true; // 超过90天
  }

  return false;
}

/**
 * 获取车辆的维护建议
 */
export function getMaintenanceRecommendation(vehicle: Vehicle): string | null {
  if (!needsMaintenance(vehicle)) return null;

  const recommendations = [];

  const vehicleAny = vehicle as any;

  if (vehicleAny.mileage && vehicleAny.lastMaintenanceMileage) {
    const mileageSinceLastMaintenance = vehicleAny.mileage - vehicleAny.lastMaintenanceMileage;
    if (mileageSinceLastMaintenance > 10000) {
      recommendations.push('建议进行定期保养');
    }
  }

  if (vehicleAny.lastMaintenanceDate) {
    const daysSinceLastMaintenance =
      (Date.now() - new Date(vehicleAny.lastMaintenanceDate).getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceLastMaintenance > 90) {
      recommendations.push('建议检查车辆状态');
    }
  }

  return recommendations.join('，');
}

/**
 * 车辆排序比较函数
 */
export const vehicleComparators = {
  byStatus: (a: Vehicle, b: Vehicle): number => {
    const statusOrder = { pending: 1, returned: 2, outbound: 3, maintenance: 4, offline: 5 };
    return statusOrder[a.status] - statusOrder[b.status];
  },

  byCapacity: (a: Vehicle, b: Vehicle): number => {
    return ((b as any).capacity || 0) - ((a as any).capacity || 0);
  },

  byUtilization: (a: Vehicle, b: Vehicle): number => {
    return calculateVehicleUtilization(b) - calculateVehicleUtilization(a);
  },

  byVehicleNumber: (a: Vehicle, b: Vehicle): number => {
    const aNum = a.vehicleNumber || '';
    const bNum = b.vehicleNumber || '';
    return aNum.localeCompare(bNum);
  },

  byLastActivity: (a: Vehicle, b: Vehicle): number => {
    const aTime = (a as any).lastActivityTime ? new Date((a as any).lastActivityTime).getTime() : 0;
    const bTime = (b as any).lastActivityTime ? new Date((b as any).lastActivityTime).getTime() : 0;
    return bTime - aTime;
  },
};

/**
 * 车辆过滤器
 */
export const vehicleFilters = {
  available: (vehicle: Vehicle): boolean => isVehicleAvailable(vehicle),
  inMaintenance: (vehicle: Vehicle): boolean => isVehicleInMaintenance(vehicle),
  offline: (vehicle: Vehicle): boolean => isVehicleOffline(vehicle),
  needsMaintenance: (vehicle: Vehicle): boolean => needsMaintenance(vehicle),
  highUtilization: (vehicle: Vehicle): boolean => calculateVehicleUtilization(vehicle) > 70,
  lowUtilization: (vehicle: Vehicle): boolean => calculateVehicleUtilization(vehicle) < 30,
  assigned: (vehicle: Vehicle): boolean => !!vehicle.assignedTaskId,
  unassigned: (vehicle: Vehicle): boolean => !vehicle.assignedTaskId,
};

/**
 * 生成车辆统计信息
 */
export function generateVehicleStats(vehicles: Vehicle[]): {
  total: number;
  available: number;
  inUse: number;
  maintenance: number;
  offline: number;
  averageUtilization: number;
  needsMaintenance: number;
} {
  const total = vehicles.length;
  const available = vehicles.filter(vehicleFilters.available).length;
  const inUse = vehicles.filter(v => v.status === 'outbound').length;
  const maintenance = vehicles.filter(vehicleFilters.inMaintenance).length;
  const offline = vehicles.filter(vehicleFilters.offline).length;
  const needsMaintenanceCount = vehicles.filter(vehicleFilters.needsMaintenance).length;

  const totalUtilization = vehicles.reduce((sum, v) => sum + calculateVehicleUtilization(v), 0);
  const averageUtilization = total > 0 ? totalUtilization / total : 0;

  return {
    total,
    available,
    inUse,
    maintenance,
    offline,
    averageUtilization,
    needsMaintenance: needsMaintenanceCount,
  };
}

/**
 * 获取车辆的详细信息摘要
 */
export function getVehicleDetailSummary(vehicle: Vehicle): {
  displayName: string;
  status: string;
  statusColor: string;
  capacity: string;
  utilization: number;
  efficiency: string;
  workStatus: string;
  maintenanceRecommendation: string | null;
} {
  return {
    displayName: getVehicleDisplayName(vehicle),
    status: getVehicleStatusText(vehicle.status as VehicleStatus),
    statusColor: getVehicleStatusColor(vehicle.status as VehicleStatus),
    capacity: getVehicleCapacityInfo(vehicle).displayText,
    utilization: calculateVehicleUtilization(vehicle),
    efficiency: getVehicleEfficiencyRating(vehicle),
    workStatus: getVehicleWorkStatus(vehicle),
    maintenanceRecommendation: getMaintenanceRecommendation(vehicle),
  };
}
