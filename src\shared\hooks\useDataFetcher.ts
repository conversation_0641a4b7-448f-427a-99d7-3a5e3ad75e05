/**
 * 通用数据获取Hook
 * 提供统一的数据获取、缓存和状态管理功能
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useToast } from '@/shared/hooks/use-toast';

interface UseDataFetcherOptions<T> {
  /** 是否自动获取数据 */
  autoFetch?: boolean;
  /** 缓存时间（毫秒） */
  cacheTime?: number;
  /** 重试次数 */
  retryCount?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 是否显示错误提示 */
  showErrorToast?: boolean;
  /** 成功回调 */
  onSuccess?: (data: T) => void;
  /** 错误回调 */
  onError?: (error: Error) => void;
  /** 数据转换函数 */
  transform?: (data: any) => T;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

// 全局缓存
const cache = new Map<string, CacheEntry<any>>();

/**
 * 通用数据获取Hook
 */
export function useDataFetcher<T>(
  fetcher: () => Promise<T>,
  dependencies: any[] = [],
  options: UseDataFetcherOptions<T> = {}
) {
  const {
    autoFetch = true,
    cacheTime = 5 * 60 * 1000, // 5分钟
    retryCount = 3,
    retryDelay = 1000,
    showErrorToast = true,
    onSuccess,
    onError,
    transform,
  } = options;

  const { toast } = useToast();

  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastFetchTime, setLastFetchTime] = useState<number | null>(null);

  const abortControllerRef = useRef<AbortController | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 生成缓存键
  const cacheKey = useCallback(() => {
    return `${fetcher.toString()}-${JSON.stringify(dependencies)}`;
  }, [fetcher, dependencies]);

  /**
   * 检查缓存是否有效
   */
  const isCacheValid = useCallback(
    (key: string): boolean => {
      const cached = cache.get(key);
      if (!cached) return false;
      return Date.now() - cached.timestamp < cacheTime;
    },
    [cacheTime]
  );

  /**
   * 从缓存获取数据
   */
  const getFromCache = useCallback((key: string): T | null => {
    const cached = cache.get(key);
    return cached ? cached.data : null;
  }, []);

  /**
   * 设置缓存
   */
  const setCache = useCallback((key: string, data: T) => {
    cache.set(key, { data, timestamp: Date.now() });
  }, []);

  /**
   * 执行数据获取
   */
  const fetchData = useCallback(
    async (attempt = 1): Promise<void> => {
      const key = cacheKey();

      // 检查缓存
      if (isCacheValid(key)) {
        const cachedData = getFromCache(key);
        if (cachedData) {
          setData(cachedData);
          setError(null);
          onSuccess?.(cachedData);
          return;
        }
      }

      try {
        setIsLoading(true);
        setError(null);

        // 取消之前的请求
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }

        // 创建新的AbortController
        abortControllerRef.current = new AbortController();

        const result = await fetcher();

        // 检查请求是否被取消
        if (abortControllerRef.current.signal.aborted) {
          return;
        }

        // 数据转换
        const transformedData = transform ? transform(result) : result;

        setData(transformedData);
        setLastFetchTime(Date.now());
        setCache(key, transformedData);
        onSuccess?.(transformedData);
      } catch (err) {
        // 检查请求是否被取消
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        const error = err instanceof Error ? err : new Error('数据获取失败');

        // 重试逻辑
        if (attempt < retryCount) {
          retryTimeoutRef.current = setTimeout(() => {
            fetchData(attempt + 1);
          }, retryDelay * attempt);
          return;
        }

        setError(error);
        onError?.(error);

        if (showErrorToast) {
          toast({
            title: '数据获取失败',
            description: error.message,
            variant: 'destructive',
          });
        }
      } finally {
        setIsLoading(false);
      }
    },
    [
      cacheKey,
      isCacheValid,
      getFromCache,
      setCache,
      fetcher,
      transform,
      retryCount,
      retryDelay,
      onSuccess,
      onError,
      showErrorToast,
      toast,
    ]
  );

  /**
   * 手动刷新数据
   */
  const refresh = useCallback(() => {
    // 清除缓存
    cache.delete(cacheKey());
    return fetchData();
  }, [cacheKey, fetchData]);

  /**
   * 清除错误
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * 取消请求
   */
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
    setIsLoading(false);
  }, []);

  // 自动获取数据
  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }

    return () => {
      cancel();
    };
  }, dependencies); // eslint-disable-line react-hooks/exhaustive-deps

  // 清理函数
  useEffect(() => {
    return () => {
      cancel();
    };
  }, [cancel]);

  return {
    data,
    isLoading,
    error,
    lastFetchTime,
    fetchData,
    refresh,
    clearError,
    cancel,
  };
}
