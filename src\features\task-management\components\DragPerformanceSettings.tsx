'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/card';
import { Switch } from '@/shared/components/switch';
import { Slider } from '@/shared/components/slider';
import { <PERSON><PERSON> } from '@/shared/components/button';
import { Badge } from '@/shared/components/badge';
import { Alert, AlertDescription } from '@/shared/components/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/tabs';

interface DragPerformanceConfig {
  enableHardwareAcceleration: boolean;
  enableAnimations: boolean;
  animationDuration: number;
  throttleMs: number;
  enablePerformanceMonitoring: boolean;
  enableReducedMotion: boolean;
  maxRenderCount: number;
  fpsThreshold: number;
}

const DEFAULT_CONFIG: DragPerformanceConfig = {
  enableHardwareAcceleration: true,
  enableAnimations: true,
  animationDuration: 150,
  throttleMs: 16,
  enablePerformanceMonitoring: true,
  enableReducedMotion: false,
  maxRenderCount: 100,
  fpsThreshold: 45,
};

interface DragPerformanceSettingsProps {
  onConfigChange?: (config: DragPerformanceConfig) => void;
  currentMetrics?: {
    averageFPS: number;
    frameDrops: number;
    renderCount: number;
    memoryUsage: number;
  } | null;
}

export const DragPerformanceSettings: React.FC<DragPerformanceSettingsProps> = ({
  onConfigChange,
  currentMetrics,
}) => {
  const [config, setConfig] = useState<DragPerformanceConfig>(DEFAULT_CONFIG);

  const updateConfig = (updates: Partial<DragPerformanceConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    onConfigChange?.(newConfig);
  };

  const resetToDefaults = () => {
    setConfig(DEFAULT_CONFIG);
    onConfigChange?.(DEFAULT_CONFIG);
  };

  const getPerformanceStatus = () => {
    if (!currentMetrics) return 'unknown';

    if (currentMetrics.averageFPS >= config.fpsThreshold && currentMetrics.frameDrops < 5) {
      return 'good';
    } else if (currentMetrics.averageFPS >= config.fpsThreshold * 0.8) {
      return 'fair';
    } else {
      return 'poor';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'bg-green-500';
      case 'fair':
        return 'bg-yellow-500';
      case 'poor':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const performanceStatus = getPerformanceStatus();

  return (
    <Card className='w-full max-w-4xl'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          🚀 拖拽性能设置
          {currentMetrics && (
            <Badge className={getStatusColor(performanceStatus)}>
              {performanceStatus === 'good' && '性能良好'}
              {performanceStatus === 'fair' && '性能一般'}
              {performanceStatus === 'poor' && '性能较差'}
              {performanceStatus === 'unknown' && '未知'}
            </Badge>
          )}
        </CardTitle>
        <CardDescription>调整车辆拖拽的性能设置以获得最佳体验</CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue='basic' className='w-full'>
          <TabsList className='grid w-full grid-cols-3'>
            <TabsTrigger value='basic'>基础设置</TabsTrigger>
            <TabsTrigger value='advanced'>高级设置</TabsTrigger>
            <TabsTrigger value='monitoring'>性能监控</TabsTrigger>
          </TabsList>

          <TabsContent value='basic' className='space-y-6'>
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <div>
                  <h4 className='text-sm font-medium'>启用硬件加速</h4>
                  <p className='text-sm text-muted-foreground'>使用 GPU 加速拖拽动画，提高性能</p>
                </div>
                <Switch
                  checked={config.enableHardwareAcceleration}
                  onCheckedChange={checked => updateConfig({ enableHardwareAcceleration: checked })}
                />
              </div>

              <div className='flex items-center justify-between'>
                <div>
                  <h4 className='text-sm font-medium'>启用动画效果</h4>
                  <p className='text-sm text-muted-foreground'>拖拽时显示动画效果</p>
                </div>
                <Switch
                  checked={config.enableAnimations}
                  onCheckedChange={checked => updateConfig({ enableAnimations: checked })}
                />
              </div>

              <div className='space-y-2'>
                <div className='flex items-center justify-between'>
                  <h4 className='text-sm font-medium'>动画持续时间</h4>
                  <span className='text-sm text-muted-foreground'>
                    {config.animationDuration}ms
                  </span>
                </div>
                <Slider
                  value={[config.animationDuration]}
                  onValueChange={([value]) => updateConfig({ animationDuration: value })}
                  min={50}
                  max={500}
                  step={50}
                  disabled={!config.enableAnimations}
                />
              </div>

              <div className='flex items-center justify-between'>
                <div>
                  <h4 className='text-sm font-medium'>减少动画</h4>
                  <p className='text-sm text-muted-foreground'>为低性能设备减少动画效果</p>
                </div>
                <Switch
                  checked={config.enableReducedMotion}
                  onCheckedChange={checked => updateConfig({ enableReducedMotion: checked })}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value='advanced' className='space-y-6'>
            <div className='space-y-4'>
              <div className='space-y-2'>
                <div className='flex items-center justify-between'>
                  <h4 className='text-sm font-medium'>事件节流间隔</h4>
                  <span className='text-sm text-muted-foreground'>
                    {config.throttleMs}ms ({Math.round(1000 / config.throttleMs)}fps)
                  </span>
                </div>
                <Slider
                  value={[config.throttleMs]}
                  onValueChange={([value]) => updateConfig({ throttleMs: value })}
                  min={8}
                  max={50}
                  step={2}
                />
              </div>

              <div className='space-y-2'>
                <div className='flex items-center justify-between'>
                  <h4 className='text-sm font-medium'>最大渲染次数</h4>
                  <span className='text-sm text-muted-foreground'>{config.maxRenderCount}</span>
                </div>
                <Slider
                  value={[config.maxRenderCount]}
                  onValueChange={([value]) => updateConfig({ maxRenderCount: value })}
                  min={50}
                  max={200}
                  step={10}
                />
              </div>

              <div className='space-y-2'>
                <div className='flex items-center justify-between'>
                  <h4 className='text-sm font-medium'>FPS 阈值</h4>
                  <span className='text-sm text-muted-foreground'>{config.fpsThreshold}fps</span>
                </div>
                <Slider
                  value={[config.fpsThreshold]}
                  onValueChange={([value]) => updateConfig({ fpsThreshold: value })}
                  min={30}
                  max={60}
                  step={5}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value='monitoring' className='space-y-6'>
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <div>
                  <h4 className='text-sm font-medium'>启用性能监控</h4>
                  <p className='text-sm text-muted-foreground'>实时监控拖拽性能指标</p>
                </div>
                <Switch
                  checked={config.enablePerformanceMonitoring}
                  onCheckedChange={checked =>
                    updateConfig({ enablePerformanceMonitoring: checked })
                  }
                />
              </div>

              {currentMetrics && (
                <div className='grid grid-cols-2 gap-4'>
                  <div className='p-3 border rounded-lg'>
                    <div className='text-sm font-medium'>平均 FPS</div>
                    <div className='text-2xl font-bold'>{currentMetrics.averageFPS.toFixed(1)}</div>
                  </div>
                  <div className='p-3 border rounded-lg'>
                    <div className='text-sm font-medium'>掉帧次数</div>
                    <div className='text-2xl font-bold'>{currentMetrics.frameDrops}</div>
                  </div>
                  <div className='p-3 border rounded-lg'>
                    <div className='text-sm font-medium'>渲染次数</div>
                    <div className='text-2xl font-bold'>{currentMetrics.renderCount}</div>
                  </div>
                  <div className='p-3 border rounded-lg'>
                    <div className='text-sm font-medium'>内存使用</div>
                    <div className='text-2xl font-bold'>
                      {currentMetrics.memoryUsage.toFixed(1)}MB
                    </div>
                  </div>
                </div>
              )}

              {performanceStatus === 'poor' && (
                <Alert>
                  <AlertDescription>
                    检测到性能问题。建议启用硬件加速、减少动画效果或降低事件频率。
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <div className='flex justify-between pt-6 border-t'>
          <Button variant='outline' onClick={resetToDefaults}>
            恢复默认设置
          </Button>
          <Button onClick={() => window.location.reload()}>应用设置并刷新</Button>
        </div>
      </CardContent>
    </Card>
  );
};
