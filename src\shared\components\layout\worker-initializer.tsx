'use client';

import { useEffect, useRef, useState } from 'react';

import { useToast } from '@/shared/hooks/use-toast';
import { useAppStore } from '@/infrastructure/storage/stores/appStore';
import type { Task } from '@/core/types';

// 辅助函数：检测任务列表变化
const getChangedTasks = (prevTasks: Task[], currentTasks: Task[]): Task[] => {
  if (!prevTasks || prevTasks.length === 0) {
    return currentTasks;
  }

  // 检查任务列表长度是否发生变化
  if (prevTasks.length !== currentTasks.length) {
    return currentTasks;
  }

  // 检查重要字段是否发生变化
  const changedTasks = currentTasks.filter((task, index) => {
    const prevTask = prevTasks[index];
    if (!prevTask) return true; // 如果没有对应的前一个任务，认为是变化
    return (
      task.id !== prevTask.id ||
      task.dispatchStatus !== prevTask.dispatchStatus ||
      task.dispatchFrequencyMinutes !== prevTask.dispatchFrequencyMinutes ||
      task.lastDispatchTime !== prevTask.lastDispatchTime ||
      task.defaultDispatchFrequencyMinutes !== prevTask.defaultDispatchFrequencyMinutes
    );
  });

  return changedTasks.length > 0 ? currentTasks : [];
};

export function WorkerInitializer() {
  const { initializeTaskManager, cleanupTaskManager, tasks, triggerWorkerUpdate } = useAppStore();
  const { toast } = useToast();
  const [retryCount, setRetryCount] = useState(0);
  const [workerError, setWorkerError] = useState<Error | null>(null);
  const lastTaskUpdateRef = useRef<Task[]>([]);

  // 初始化Worker和处理Worker错误
  useEffect(() => {
    // 初始化任务管理器
    initializeTaskManager();

    // 强制首次更新
    setTimeout(() => {
      triggerWorkerUpdate();
    }, 1000);

    return () => {
      // 清理任务管理器
      cleanupTaskManager();
    };
  }, [initializeTaskManager, cleanupTaskManager, triggerWorkerUpdate]);

  // 监听任务变化并更新Worker
  useEffect(() => {
    if (tasks.length === 0) return; // 跳过空任务列表

    const changedTasks = getChangedTasks(lastTaskUpdateRef.current, tasks);
    if (changedTasks.length) {
      // 触发Worker更新
      triggerWorkerUpdate();
      lastTaskUpdateRef.current = [...tasks];
    }
  }, [tasks, triggerWorkerUpdate]);

  // 处理Worker错误
  useEffect(() => {
    if (workerError) {
      console.error('Reminder Worker Error:', workerError);
      toast({
        title: '提醒服务错误',
        description: '后台提醒服务遇到问题，部分功能可能受限。',
        variant: 'destructive',
      });

      // 实现错误重试，但限制最大重试次数
      if (retryCount < 5) {
        const delay = Math.min(1000 * 2 ** retryCount, 30000);
        const timer = setTimeout(() => {
          // 检查组件是否仍然挂载
          setRetryCount(c => {
            if (c < 5) {
              triggerWorkerUpdate();
              return c + 1;
            }
            return c;
          });
        }, delay);

        return () => clearTimeout(timer);
      } else {
        // 达到最大重试次数，停止重试
        console.warn('Worker error retry limit reached, stopping retries');
        return undefined;
      }
    }
    return undefined;
  }, [workerError, toast, retryCount, triggerWorkerUpdate]);

  return null; // 此组件不渲染任何UI
}
