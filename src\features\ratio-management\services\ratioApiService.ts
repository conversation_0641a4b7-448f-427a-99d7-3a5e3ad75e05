/**
 * 配比API服务
 * 处理配比相关的API调用，支持Mock数据和真实API
 */

import { serviceLogger } from '@/core/lib/logger';
import { ExposureClass, PlacementMethod, FinishingRequirement } from '@/core/types/ratio';
import type {
  CalculateRequest,
  CalculateResponse,
  ReverseCalculateRequest,
  ReverseCalculateResponse,
  SaveRatioRequest,
  SaveRatioResponse,
  GetRatioRequest,
  GetRatioResponse,
  GetRatioHistoryResponse,
  RatioApiConfig,
  ApiErrorResponse,
  RatioApiErrorCodes,
} from '@/core/types/ratio-api';
import { httpClient } from '@/infrastructure/api/client/httpClient';

/**
 * 配比API服务类
 */
export class RatioApiService {
  private config: RatioApiConfig;

  constructor() {
    this.config = this.getApiConfig();
  }

  /**
   * 获取API配置
   */
  private getApiConfig(): RatioApiConfig {
    const useMock = process.env['NEXT_PUBLIC_USE_MOCK_DATA'] !== 'false';

    return {
      baseUrl: useMock
        ? ''
        : process.env['NEXT_PUBLIC_API_BASE_URL'] || 'http://47.107.119.236:7033',
      endpoints: {
        calculate: useMock ? '/ratio/calculate' : '/api/ratio/calculate',
        reverseCalculate: useMock ? '/ratio/reverse-calculate' : '/api/ratio/reverse-calculate',
        saveRatio: useMock ? '/ratio/save' : '/api/ratio/save',
        getRatio: useMock ? '/ratio/get' : '/api/ratio/get',
        getRatioHistory: useMock ? '/ratio/history' : '/api/ratio/history',
      },
      timeout: parseInt(process.env['NEXT_PUBLIC_API_TIMEOUT'] || '10000'),
      retries: 3,
      useMock,
    };
  }

  /**
   * 计算配比
   */
  async calculate(request: CalculateRequest): Promise<CalculateResponse> {
    try {
      serviceLogger.info('开始配比计算', { taskId: request.taskId });

      if (this.config.useMock) {
        return this.mockCalculate(request);
      }

      const response = await httpClient.post<CalculateResponse>(
        this.config.endpoints.calculate,
        request
      );

      serviceLogger.info('配比计算完成', {
        taskId: request.taskId,
        success: response.success,
      });

      return response;
    } catch (error) {
      serviceLogger.error('配比计算失败', error as Error, {
        operation: 'calculate',
        taskId: request.taskId,
      });

      // 如果真实API失败，回退到Mock数据
      if (!this.config.useMock) {
        serviceLogger.warn('API调用失败，回退到Mock数据', { taskId: request.taskId });
        return this.mockCalculate(request);
      }

      throw this.createApiError('CALCULATION_FAILED', '配比计算失败', error);
    }
  }

  /**
   * 反算配比参数
   */
  async reverseCalculate(request: ReverseCalculateRequest): Promise<ReverseCalculateResponse> {
    try {
      serviceLogger.info('开始配比反算', { taskId: request.taskId });

      if (this.config.useMock) {
        return this.mockReverseCalculate(request);
      }

      const response = await httpClient.post<ReverseCalculateResponse>(
        this.config.endpoints.reverseCalculate,
        request
      );

      serviceLogger.info('配比反算完成', {
        taskId: request.taskId,
        success: response.success,
      });

      return response;
    } catch (error) {
      serviceLogger.error('配比反算失败', error as Error, {
        operation: 'reverseCalculate',
        taskId: request.taskId,
      });

      // 如果真实API失败，回退到Mock数据
      if (!this.config.useMock) {
        serviceLogger.warn('API调用失败，回退到Mock数据', { taskId: request.taskId });
        return this.mockReverseCalculate(request);
      }

      throw this.createApiError('REVERSE_CALCULATION_FAILED', '配比反算失败', error);
    }
  }

  /**
   * 保存配比
   */
  async saveRatio(request: SaveRatioRequest): Promise<SaveRatioResponse> {
    try {
      serviceLogger.info('开始保存配比', { taskId: request.taskId });

      if (this.config.useMock) {
        return this.mockSaveRatio(request);
      }

      const response = await httpClient.post<SaveRatioResponse>(
        this.config.endpoints.saveRatio,
        request
      );

      serviceLogger.info('配比保存完成', {
        taskId: request.taskId,
        success: response.success,
      });

      return response;
    } catch (error) {
      serviceLogger.error('配比保存失败', error as Error, {
        operation: 'saveRatio',
        taskId: request.taskId,
      });

      throw this.createApiError('SAVE_FAILED', '配比保存失败', error);
    }
  }

  /**
   * 获取配比
   */
  async getRatio(request: GetRatioRequest): Promise<GetRatioResponse> {
    try {
      serviceLogger.info('开始获取配比', { taskId: request.taskId });

      if (this.config.useMock) {
        return this.mockGetRatio(request);
      }

      const response = await httpClient.get<GetRatioResponse>(this.config.endpoints.getRatio, {
        taskId: request.taskId,
        ratioId: request.ratioId || '',
        version: request.version?.toString() || '',
      });

      serviceLogger.info('配比获取完成', {
        taskId: request.taskId,
        success: response.success,
      });

      return response;
    } catch (error) {
      serviceLogger.error('配比获取失败', error as Error, {
        operation: 'getRatio',
        taskId: request.taskId,
      });

      throw this.createApiError('RATIO_NOT_FOUND', '配比获取失败', error);
    }
  }

  /**
   * 获取配比历史
   */
  async getRatioHistory(taskId: string): Promise<GetRatioHistoryResponse> {
    try {
      serviceLogger.info('开始获取配比历史', { taskId });

      if (this.config.useMock) {
        return this.mockGetRatioHistory(taskId);
      }

      const response = await httpClient.get<GetRatioHistoryResponse>(
        this.config.endpoints.getRatioHistory,
        { taskId }
      );

      serviceLogger.info('配比历史获取完成', {
        taskId,
        success: response.success,
      });

      return response;
    } catch (error) {
      serviceLogger.error('配比历史获取失败', error as Error, {
        operation: 'getRatioHistory',
        taskId,
      });

      throw this.createApiError('RATIO_NOT_FOUND', '配比历史获取失败', error);
    }
  }

  /**
   * 创建API错误
   */
  private createApiError(
    code: keyof typeof RatioApiErrorCodes,
    message: string,
    originalError?: any
  ): ApiErrorResponse {
    return {
      success: false,
      error: {
        code,
        message,
        details: originalError?.message || originalError,
      },
      timestamp: new Date().toISOString(),
    };
  }

  // ==================== Mock方法 ====================

  /**
   * Mock计算配比
   */
  private async mockCalculate(request: CalculateRequest): Promise<CalculateResponse> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));

    // 计算总重量
    const totalWeight = request.materials.reduce((sum, material) => sum + material.designValue, 0);

    // 生成Mock计算结果
    const mockResults = {
      totalWeight,
      materials: request.materials.reduce(
        (acc, material) => {
          acc[material.name] = material.designValue;
          return acc;
        },
        {} as Record<string, number>
      ),
      strengthPrediction: request.calculationParams.strengthGrade || 30,
      qualityScore: 85 + Math.random() * 10, // 85-95分
      warnings: this.generateMockWarnings(request),
      suggestions: this.generateMockSuggestions(request),
      carbonFootprint: totalWeight * 0.3, // 模拟碳足迹
      costEstimate: totalWeight * 0.8, // 模拟成本估算
    };

    return {
      success: true,
      data: mockResults,
      message: '配比计算完成',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Mock反算配比参数
   */
  private async mockReverseCalculate(
    request: ReverseCalculateRequest
  ): Promise<ReverseCalculateResponse> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1200 + Math.random() * 800));

    // 提取材料用量进行反算
    const waterAmount = this.extractMaterialAmount(request.materials, '水') || 180;
    const cementAmount = this.extractMaterialAmount(request.materials, '水泥') || 400;
    const flyashAmount = this.extractMaterialAmount(request.materials, '粉煤灰') || 0;
    const sandAmount = this.extractMaterialAmount(request.materials, '砂') || 700;
    const stoneAmount = this.extractMaterialAmount(request.materials, '石') || 1100;
    const additiveAmount = this.extractMaterialAmount(request.materials, '外加剂') || 8;

    // 计算反算参数
    const totalBinder = cementAmount + flyashAmount;
    const waterRatio = waterAmount / totalBinder;
    const sandRatio = (sandAmount / (sandAmount + stoneAmount)) * 100;
    const additiveRatio = (additiveAmount / totalBinder) * 100;
    const flyashRatio = (flyashAmount / totalBinder) * 100;
    const totalWeight = request.materials.reduce((sum, m) => sum + m.designValue, 0);
    const density = totalWeight / 1000;

    const mockParams = {
      // 基本参数
      targetStrength: 30,
      slump: 180,
      maxAggregateSize: 25,
      exposureClass: ExposureClass.XC1,

      // 环境参数
      ambientTemperature: 20,
      relativeHumidity: 60,
      cementTemperature: 20,
      aggregateTemperature: 20,

      // 材料选择
      selectedMaterials: [],
      cementType: 'P.O 42.5',
      aggregateType: '连续级配',
      waterType: '自来水',

      // 配比参数
      waterCementRatio: Math.round(waterRatio * 100) / 100,
      sandRatio: Math.round(sandRatio * 10) / 10,
      cementContent: Math.round(cementAmount),
      waterContent: Math.round(waterAmount),

      // 外加剂参数
      additiveRatio: Math.round(additiveRatio * 10) / 10,
      flyashRatio: Math.round(flyashRatio * 10) / 10,
      mineralPowderRatio: 0,
      silicaFumeRatio: 0,
      antifreezeRatio: 0,
      expansionRatio: 0,

      // 数量参数
      cementAmount: Math.round(cementAmount),
      waterAmount: Math.round(waterAmount),
      density: Math.round(density * 100) / 100,
      airContent: 4.5,
      strengthGrade: 30,
      ultraFineSandRatio: 0,
      earlyStrengthRatio: 0,

      // 施工参数
      placementMethod: PlacementMethod.PUMP,
      finishingRequirement: FinishingRequirement.SMOOTH,
      cureConditions: {
        method: '自然养护',
        duration: 28,
        temperature: 20,
        humidity: 95,
      },

      // 外加剂用量（具体数值）
      admixtureAmount: Math.round(((additiveRatio * cementAmount) / 100) * 10) / 10,
      antifreezeAmount: 0,
      flyAshAmount: Math.round((flyashRatio * cementAmount) / 100),
      mineralPowderAmount: 0,
      s105PowderAmount: 0,
      expansionAgentAmount: 0,
      earlyStrengthAgentAmount: 0,
      ultraFineSandAmount: 0,
    };

    return {
      success: true,
      data: mockParams,
      message: '配比反算完成',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Mock保存配比
   */
  private async mockSaveRatio(request: SaveRatioRequest): Promise<SaveRatioResponse> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 500));

    const ratioId = `ratio-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const version = Math.floor(Math.random() * 10) + 1;

    return {
      success: true,
      data: {
        ratioId,
        version,
      },
      message: '配比保存成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Mock获取配比
   */
  private async mockGetRatio(request: GetRatioRequest): Promise<GetRatioResponse> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 300));

    // 生成Mock配比数据
    const mockRatio = {
      taskId: request.taskId,
      ratioId: request.ratioId || `ratio-${Date.now()}`,
      version: request.version || 1,
      calculationParams: {
        // 基本参数
        targetStrength: 30,
        slump: 180,
        maxAggregateSize: 25,
        exposureClass: ExposureClass.XC1,

        // 环境参数
        ambientTemperature: 20,
        relativeHumidity: 60,
        cementTemperature: 20,
        aggregateTemperature: 20,

        // 材料选择
        selectedMaterials: [],
        cementType: 'P.O 42.5',
        aggregateType: '连续级配',
        waterType: '自来水',

        // 配比参数
        waterCementRatio: 0.45,
        sandRatio: 38.5,
        cementContent: 400,
        waterContent: 180,

        // 外加剂参数
        additiveRatio: 2.0,
        flyashRatio: 15.0,
        mineralPowderRatio: 0,
        silicaFumeRatio: 0,
        antifreezeRatio: 0,
        expansionRatio: 0,

        // 数量参数
        cementAmount: 400,
        waterAmount: 180,
        density: 2.4,
        airContent: 4.5,
        strengthGrade: 30,
        ultraFineSandRatio: 0,
        earlyStrengthRatio: 0,

        // 施工参数
        placementMethod: PlacementMethod.PUMP,
        finishingRequirement: FinishingRequirement.SMOOTH,
        cureConditions: {
          method: '自然养护',
          duration: 28,
          temperature: 20,
          humidity: 95,
        },

        // 外加剂用量（具体数值）
        admixtureAmount: 4.8,
        antifreezeAmount: 0,
        flyAshAmount: 80,
        mineralPowderAmount: 40,
        s105PowderAmount: 0,
        expansionAgentAmount: 0,
        earlyStrengthAgentAmount: 0,
        ultraFineSandAmount: 0,
      },
      materials: [],
      calculationResults: {
        totalWeight: 2400,
        materials: {},
        strengthPrediction: 30,
        qualityScore: 88,
        warnings: [],
        suggestions: [],
        carbonFootprint: 720,
        costEstimate: 1920,
      },
      ratioName: '标准C30配比',
      description: 'Mock配比数据',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return {
      success: true,
      data: mockRatio,
      message: '配比获取成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Mock获取配比历史
   */
  private async mockGetRatioHistory(taskId: string): Promise<GetRatioHistoryResponse> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 400 + Math.random() * 400));

    const mockHistory = [
      {
        ratioId: `ratio-${Date.now()}-1`,
        version: 1,
        ratioName: '初始配比',
        description: '首次创建的配比',
        createdAt: new Date(Date.now() - 86400000).toISOString(),
        createdBy: '系统管理员',
        operation: 'create' as const,
        changes: ['创建配比'],
      },
      {
        ratioId: `ratio-${Date.now()}-2`,
        version: 2,
        ratioName: '优化配比',
        description: '根据计算结果优化的配比',
        createdAt: new Date(Date.now() - 43200000).toISOString(),
        createdBy: '技术员',
        operation: 'calculate' as const,
        changes: ['更新水胶比', '调整砂率'],
      },
    ];

    return {
      success: true,
      data: mockHistory,
      message: '配比历史获取成功',
      timestamp: new Date().toISOString(),
    };
  }

  // ==================== 辅助方法 ====================

  /**
   * 提取指定材料的用量
   */
  private extractMaterialAmount(materials: any[], materialName: string): number {
    const material = materials.find(m => m.name?.includes(materialName));
    return material?.designValue || 0;
  }

  /**
   * 生成Mock警告信息
   */
  private generateMockWarnings(request: CalculateRequest): string[] {
    const warnings: string[] = [];

    if (request.calculationParams.waterCementRatio > 0.6) {
      warnings.push('水胶比偏高，可能影响强度');
    }

    if (request.calculationParams.sandRatio < 30) {
      warnings.push('砂率偏低，可能影响和易性');
    }

    return warnings;
  }

  /**
   * 生成Mock建议信息
   */
  private generateMockSuggestions(request: CalculateRequest): string[] {
    const suggestions: string[] = [];

    suggestions.push('建议进行试配验证');
    suggestions.push('注意控制搅拌时间');

    if (request.calculationParams.additiveRatio > 0) {
      suggestions.push('外加剂掺量适中，有利于改善工作性');
    }

    return suggestions;
  }
}

// 导出单例实例
export const ratioApiService = new RatioApiService();
