import * as React from 'react';

import { cn } from '@/core/lib/utils';

interface RingProgressProps extends React.SVGProps<SVGSVGElement> {
  value: number; // 0 to 100
  radius?: number;
  strokeWidth?: number;
  label?: string;
}

export function RingProgress({
  value,
  radius = 20,
  strokeWidth = 3,
  label,
  className,
  ...props
}: RingProgressProps) {
  const normalizedValue = Math.min(100, Math.max(0, value));
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (normalizedValue / 100) * circumference;
  const size = (radius + strokeWidth) * 2;

  return (
    <div className='relative inline-flex items-center justify-center'>
      <svg
        width={size}
        height={size}
        viewBox={`0 0 ${size} ${size}`}
        className={cn('transform -rotate-90', className)}
        {...props}
      >
        {/* 背景圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill='none'
          strokeWidth={strokeWidth}
          className='stroke-muted'
        />
        {/* 进度圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill='none'
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap='round'
          className='stroke-current transition-all duration-300 ease-in-out'
        />
      </svg>
      {label && <span className='absolute text-xs font-medium'>{label}</span>}
    </div>
  );
}
