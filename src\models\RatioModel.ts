/**
 * 配比数据模型
 * 统一的配比数据结构，用于数据访问层
 */

export interface RatioModel {
  id: string;
  taskId: string;
  ratioNumber: string;
  version: number;

  // 基本信息
  taskInfo: {
    taskNumber: string;
    projectName: string;
    customerName: string;
    strengthGrade: string;
    usagePart: string;
    constructionUnit: string;
    mixingStation: string;
  };

  // 计算参数
  calculationParams: {
    targetStrength: number;
    slump: number;
    maxAggregateSize: number;
    exposureClass: string;
    ambientTemperature: number;
    relativeHumidity: number;
    cementTemperature: number;
    aggregateTemperature: number;
    selectedMaterials: string[];
    cementType: string;
    aggregateType: string;
    waterType: string;
    waterCementRatio: number;
    sandRatio: number;
    cementContent: number;
    waterContent: number;
    additiveRatio: number;
    flyashRatio: number;
    mineralPowderRatio: number;
    silicaFumeRatio: number;
    antifreezeRatio: number;
    expansionRatio: number;
    cementAmount: number;
    waterAmount: number;
    density: number;
    airContent: number;
    strengthGrade: number;
    ultraFineSandRatio: number;
    earlyStrengthRatio: number;
    s105Ratio: number;
    placementMethod: string;
    finishingRequirement: string;
    cureConditions: {
      temperature: number;
      humidity: number;
      duration: number;
    };
  };

  // 材料配比
  materials: RatioMaterialModel[];

  // 计算结果
  calculationResults: {
    totalWeight: number;
    materials: Record<string, number>;
    strengthPrediction: number;
    qualityScore: number;
    warnings: string[];
    suggestions: string[];
    carbonFootprint: number;
    costEstimate: number;
  };

  // 质量评估
  qualityAssessment: {
    overallScore: number;
    warnings: string[];
    recommendations: string[];
    complianceStatus: 'compliant' | 'warning' | 'non-compliant';
  };

  // 元数据
  metadata: {
    createdAt: string;
    createdBy: string;
    updatedAt: string;
    updatedBy: string;
    status: 'draft' | 'approved' | 'in-use' | 'archived';
    approvedBy?: string;
    approvedAt?: string;
    notes?: string;
  };
}

export interface RatioMaterialModel {
  id: string;
  materialId: string;
  name: string;
  specification: string;
  category: 'cement' | 'sand' | 'stone' | 'water' | 'additive' | 'flyash' | 'other';
  theoreticalAmount: number;
  waterContent: number;
  stoneContent: number;
  actualAmount: number;
  designValue: number;
  siloId: string;
  siloName: string;
  unit: string;
  density: number;
  cost?: number;
  supplier?: string;
}

export interface RatioHistoryModel {
  id: string;
  ratioId: string;
  taskId: string;
  version: number;
  changeType: 'created' | 'modified' | 'approved' | 'archived';
  changes: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  changedBy: string;
  changedAt: string;
  reason?: string;
  snapshot: Partial<RatioModel>;
}

export interface RatioTemplateModel {
  id: string;
  name: string;
  description: string;
  category: string;
  strengthGrade: string;
  applicableConditions: {
    minStrength: number;
    maxStrength: number;
    exposureClasses: string[];
    placementMethods: string[];
  };
  defaultParams: RatioModel['calculationParams'];
  defaultMaterials: RatioMaterialModel[];
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
  usageCount: number;
  rating: number;
  tags: string[];
}

export interface RatioValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  score: number;
}

export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
  code: string;
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
  code: string;
}

// 计算结果模型
export interface CalculationResultModel {
  id: string;
  ratioId: string;
  calculationType: 'forward' | 'reverse' | 'optimization';
  inputParams: Record<string, any>;
  outputResults: {
    totalWeight: number;
    materials: Record<string, number>;
    strengthPrediction: number;
    qualityScore: number;
    warnings: string[];
    suggestions: string[];
    carbonFootprint: number;
    costEstimate: number;
    durabilityIndex: number;
    workabilityIndex: number;
    economyIndex: number;
  };
  calculatedAt: string;
  calculatedBy: string;
  engineVersion: string;
  metadata: {
    calculationTime: number; // ms
    complexity: 'simple' | 'standard' | 'complex';
    confidence: number; // 0-100
  };
}

// 优化结果模型
export interface OptimizationResultModel {
  id: string;
  originalRatioId: string;
  optimizedRatio: RatioModel;
  optimizationGoals: {
    cost: number; // weight 0-1
    strength: number; // weight 0-1
    durability: number; // weight 0-1
    workability: number; // weight 0-1
    environmental: number; // weight 0-1
  };
  improvements: {
    costReduction: number; // percentage
    strengthIncrease: number; // percentage
    carbonReduction: number; // percentage
    qualityImprovement: number; // percentage
  };
  optimizedAt: string;
  optimizedBy: string;
  algorithmUsed: string;
  iterations: number;
}
