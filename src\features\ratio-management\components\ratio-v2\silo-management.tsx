'use client';

import React, { useState } from 'react';

import { Alert<PERSON>riangle, CheckCircle, Container, Edit, Plus, Save, Trash2 } from 'lucide-react';

import { Badge } from '@/shared/components/badge';
// Dialog 组件已替换为自定义模态框
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { useToast } from '@/shared/hooks/use-toast';
import { cn } from '@/core/lib/utils';

// 导入统一的类型定义
import type { Silo, MaterialSpec, MixingStation } from '@/core/types/ratio';

// 导入统一的mock数据
import { useSiloManagement } from '@/features/ratio-management/hooks/useSiloManagement';

// 本地存放地接口（临时保留）
interface StorageLocation {
  id: string;
  name: string;
  description: string;
}

interface SiloManagementProps {
  isOpen: boolean;
  onClose: () => void;
  onAddMaterial?: (material: any) => void;
}

export function SiloManagement({ isOpen, onClose, onAddMaterial }: SiloManagementProps) {
  const { toast } = useToast();

  // 使用统一的料仓管理
  const { silos, addSilo, updateSilo, deleteSilo } = useSiloManagement();

  const [editingSilo, setEditingSilo] = useState<Silo | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);

  // 新增状态管理
  const [isManagingSpecs, setIsManagingSpecs] = useState(false);
  const [isManagingStations, setIsManagingStations] = useState(false);
  const [isManagingLocations, setIsManagingLocations] = useState(false);
  const [selectedMaterialType, setSelectedMaterialType] = useState<string>('');

  // 搅拌站数据
  const [mixingStations, setMixingStations] = useState<MixingStation[]>([
    { id: 'station-1', name: '搅拌站A', location: '东区' },
    { id: 'station-2', name: '搅拌站B', location: '西区' },
    { id: 'station-3', name: '搅拌站C', location: '南区' },
  ]);

  // 存放地数据
  const [storageLocations, setStorageLocations] = useState<StorageLocation[]>([
    { id: 'storage-1', name: '1号料仓', description: '主料仓区域' },
    { id: 'storage-2', name: '2号料仓', description: '副料仓区域' },
    { id: 'storage-3', name: '3号料仓', description: '临时存储区' },
    { id: 'storage-4', name: '外加剂仓', description: '外加剂专用仓' },
    { id: 'storage-5', name: '水箱', description: '生产用水存储' },
  ]);

  // 材料规格数据
  const [materialSpecs, setMaterialSpecs] = useState<MaterialSpec[]>([
    // 水泥规格
    {
      id: 'spec-1',
      materialType: 'cement',
      specName: 'P.O 32.5',
      description: '普通硅酸盐水泥32.5级',
    },
    {
      id: 'spec-2',
      materialType: 'cement',
      specName: 'P.O 42.5',
      description: '普通硅酸盐水泥42.5级',
    },
    {
      id: 'spec-3',
      materialType: 'cement',
      specName: 'P.O 52.5',
      description: '普通硅酸盐水泥52.5级',
    },
    // 砂规格
    { id: 'spec-4', materialType: 'sand', specName: '细砂', description: '细度模数1.5-2.2' },
    { id: 'spec-5', materialType: 'sand', specName: '中砂', description: '细度模数2.3-3.0' },
    { id: 'spec-6', materialType: 'sand', specName: '粗砂', description: '细度模数3.1-3.7' },
    // 石子规格
    { id: 'spec-7', materialType: 'stone', specName: '5-10mm', description: '小粒径碎石' },
    { id: 'spec-8', materialType: 'stone', specName: '10-20mm', description: '中粒径碎石' },
    { id: 'spec-9', materialType: 'stone', specName: '20-40mm', description: '大粒径碎石' },
    // 外加剂规格
    {
      id: 'spec-10',
      materialType: 'additive',
      specName: '聚羧酸减水剂',
      description: '高效减水剂',
    },
    { id: 'spec-11', materialType: 'additive', specName: '萘系减水剂', description: '普通减水剂' },
    { id: 'spec-12', materialType: 'additive', specName: '引气剂', description: '改善和易性' },
    // 粉煤灰规格
    { id: 'spec-13', materialType: 'flyash', specName: 'F类I级', description: '优质粉煤灰' },
    { id: 'spec-14', materialType: 'flyash', specName: 'F类II级', description: '普通粉煤灰' },
    // 水规格
    { id: 'spec-15', materialType: 'water', specName: '自来水', description: '符合混凝土用水标准' },
    { id: 'spec-16', materialType: 'water', specName: '地下水', description: '经检测合格的地下水' },
  ]);

  const [newSilo, setNewSilo] = useState<Partial<Silo>>({
    name: '',
    type: 'cement',
    capacity: 0,
    currentAmount: 0,
    unit: 't',
    density: 0,
    supplier: '',
    location: '',
    status: 'normal',
    mixingStation: '',
    materialName: '',
    storageLocation: '',
    specification: '',
  });

  const siloTypes = [
    { value: 'cement', label: '水泥', color: 'bg-gray-500' },
    { value: 'sand', label: '砂', color: 'bg-yellow-500' },
    { value: 'stone', label: '石子', color: 'bg-stone-500' },
    { value: 'water', label: '水', color: 'bg-blue-500' },
    { value: 'additive', label: '外加剂', color: 'bg-green-500' },
    { value: 'flyash', label: '粉煤灰', color: 'bg-purple-500' },
    { value: 'other', label: '其他', color: 'bg-gray-400' },
  ];

  // 获取指定材料类型的规格列表
  const getSpecsForMaterialType = (materialType: string) => {
    return materialSpecs.filter(spec => spec.materialType === materialType);
  };

  // 获取搅拌站名称
  const getMixingStationName = (stationId: string) => {
    const station = mixingStations.find(s => s.id === stationId);
    return station ? station.name : '未知搅拌站';
  };

  // 获取存放地名称
  const getStorageLocationName = (locationId: string) => {
    const location = storageLocations.find(l => l.id === locationId);
    return location ? location.name : '未知存放地';
  };

  // 获取规格名称
  const getSpecificationName = (specId: string) => {
    const spec = materialSpecs.find(s => s.id === specId);
    return spec ? spec.specName : '未知规格';
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'normal':
        return { label: '正常', color: 'bg-green-500', icon: CheckCircle };
      case 'low':
        return { label: '库存不足', color: 'bg-yellow-500', icon: AlertTriangle };
      case 'empty':
        return { label: '已空', color: 'bg-red-500', icon: AlertTriangle };
      case 'maintenance':
        return { label: '维护中', color: 'bg-gray-500', icon: AlertTriangle };
      default:
        return { label: '未知', color: 'bg-gray-400', icon: AlertTriangle };
    }
  };

  const getUsagePercentage = (current: number, capacity: number) => {
    return capacity > 0 ? (current / capacity) * 100 : 0;
  };

  const handleEdit = (silo: Silo) => {
    setEditingSilo({ ...silo });
  };

  const handleSave = () => {
    if (editingSilo) {
      updateSilo(editingSilo.id, editingSilo);
      setEditingSilo(null);
      toast({
        title: '料仓已更新',
        description: `${editingSilo.name}的信息已成功更新`,
      });
    }
  };

  const handleDelete = (siloId: string) => {
    deleteSilo(siloId);
    toast({
      title: '料仓已删除',
      description: '料仓已从系统中删除',
    });
  };

  const handleAddNew = () => {
    if (
      !newSilo.name ||
      !newSilo.capacity ||
      !newSilo.mixingStation ||
      !newSilo.materialName ||
      !newSilo.storageLocation
    ) {
      toast({
        title: '请填写必要信息',
        description: '料仓名称、容量、搅拌站、材料名称和存放地为必填项',
        variant: 'destructive',
      });
      return;
    }

    const siloData = {
      name: newSilo.name!,
      type: newSilo.type as any,
      capacity: newSilo.capacity!,
      currentAmount: newSilo.currentAmount || 0,
      unit: newSilo.unit || 't',
      density: newSilo.density || 1.0,
      supplier: newSilo.supplier || '',
      location: newSilo.location || '',
      status: (newSilo.status as any) || 'normal',
      image: '/images/silos/default-silo.jpg',
      mixingStation: newSilo.mixingStation!,
      materialName: newSilo.materialName!,
      storageLocation: newSilo.storageLocation!,
      specification: newSilo.specification || '',
    };

    const silo = addSilo(siloData);
    setNewSilo({
      name: '',
      type: 'cement',
      capacity: 0,
      currentAmount: 0,
      unit: 't',
      density: 0,
      supplier: '',
      location: '',
      status: 'normal',
      mixingStation: '',
      materialName: '',
      storageLocation: '',
      specification: '',
    });
    setIsAddingNew(false);

    toast({
      title: '料仓已添加',
      description: `${silo.name}已成功添加到系统中`,
    });
  };

  // 规格管理函数
  const handleAddSpec = (materialType: string, specName: string, description: string) => {
    const newSpec: MaterialSpec = {
      id: `spec-${Date.now()}`,
      materialType,
      specName,
      description,
    };
    setMaterialSpecs(prev => [...prev, newSpec]);
    toast({
      title: '规格已添加',
      description: `${specName}已成功添加`,
    });
  };

  const handleDeleteSpec = (specId: string) => {
    setMaterialSpecs(prev => prev.filter(spec => spec.id !== specId));
    toast({
      title: '规格已删除',
      description: '规格已从系统中删除',
    });
  };

  const handleUpdateSpec = (specId: string, specName: string, description: string) => {
    setMaterialSpecs(prev =>
      prev.map(spec => (spec.id === specId ? { ...spec, specName, description } : spec))
    );
    toast({
      title: '规格已更新',
      description: `${specName}已成功更新`,
    });
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 z-[100] bg-black/50 flex items-center justify-center p-4'>
      <div className='bg-white rounded-lg shadow-xl max-w-7xl w-full max-h-[90vh] overflow-y-auto'>
        <div className='p-6 border-b'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Container className='h-6 w-6 text-primary' />
              <h2 className='text-xl font-semibold'>料仓管理</h2>
            </div>
            <Button variant='ghost' size='sm' onClick={onClose}>
              ✕
            </Button>
          </div>
        </div>

        <div className='p-6'>
          <div className='space-y-4'>
            {/* 操作栏 */}
            <div className='flex justify-between items-center'>
              <div className='text-sm text-muted-foreground'>共 {silos.length} 个料仓</div>
              <div className='flex gap-2'>
                <Button
                  variant='outline'
                  onClick={() => setIsManagingSpecs(true)}
                  className='gap-2'
                >
                  <Edit className='h-4 w-4' />
                  规格管理
                </Button>
                <Button
                  variant='outline'
                  onClick={() => setIsManagingStations(true)}
                  className='gap-2'
                >
                  <Container className='h-4 w-4' />
                  搅拌站管理
                </Button>
                <Button
                  variant='outline'
                  onClick={() => setIsManagingLocations(true)}
                  className='gap-2'
                >
                  <Container className='h-4 w-4' />
                  存放地管理
                </Button>
                <Button onClick={() => setIsAddingNew(true)} className='gap-2'>
                  <Plus className='h-4 w-4' />
                  添加料仓
                </Button>
              </div>
            </div>

            {/* 料仓卡片网格 */}
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
              {silos.map(silo => {
                const statusInfo = getStatusInfo(silo.status);
                const usagePercentage = getUsagePercentage(silo.currentAmount, silo.capacity);
                const StatusIcon = statusInfo.icon;

                return (
                  <Card key={silo.id} className='hover:shadow-lg transition-all duration-200'>
                    <CardHeader className='pb-3'>
                      <div className='flex items-start justify-between'>
                        <div className='flex items-center gap-3'>
                          <div className='w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center'>
                            <Container className='h-6 w-6 text-white' />
                          </div>
                          <div>
                            <CardTitle className='text-base'>{silo.name}</CardTitle>
                            <p className='text-sm text-muted-foreground'>{silo.location}</p>
                          </div>
                        </div>
                        <Badge className={cn('text-white', statusInfo.color)}>
                          <StatusIcon className='h-3 w-3 mr-1' />
                          {statusInfo.label}
                        </Badge>
                      </div>
                    </CardHeader>

                    <CardContent className='space-y-3'>
                      {/* 容量信息 */}
                      <div className='space-y-2'>
                        <div className='flex justify-between text-sm'>
                          <span>库存量</span>
                          <span className='font-mono'>
                            {silo.currentAmount} / {silo.capacity} {silo.unit}
                          </span>
                        </div>
                        <div className='w-full bg-gray-200 rounded-full h-2'>
                          <div
                            className={cn(
                              'h-2 rounded-full transition-all',
                              usagePercentage > 80
                                ? 'bg-green-500'
                                : usagePercentage > 30
                                  ? 'bg-yellow-500'
                                  : 'bg-red-500'
                            )}
                            style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                          />
                        </div>
                        <div className='text-xs text-muted-foreground text-center'>
                          使用率: {usagePercentage.toFixed(1)}%
                        </div>
                      </div>

                      {/* 详细信息 */}
                      <div className='text-sm space-y-1'>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>搅拌站:</span>
                          <span className='font-medium'>
                            {getMixingStationName(silo.mixingStation)}
                          </span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>材料名称:</span>
                          <span className='truncate max-w-24'>{silo.materialName}</span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>存放地:</span>
                          <span className='truncate max-w-24'>
                            {getStorageLocationName(silo.storageLocation)}
                          </span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>规格:</span>
                          <span className='truncate max-w-24'>
                            {getSpecificationName(silo.specification)}
                          </span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>密度:</span>
                          <span className='font-mono'>{silo.density} t/m³</span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>供应商:</span>
                          <span className='truncate max-w-24'>{silo.supplier}</span>
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className='flex gap-2 pt-2'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleEdit(silo)}
                          className='flex-1 gap-1'
                        >
                          <Edit className='h-3 w-3' />
                          编辑
                        </Button>

                        {onAddMaterial && (
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => onAddMaterial(silo)}
                            className='flex-1 gap-1'
                          >
                            <Plus className='h-3 w-3' />
                            添加
                          </Button>
                        )}

                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleDelete(silo.id)}
                          className='gap-1 text-red-600 hover:text-red-700'
                        >
                          <Trash2 className='h-3 w-3' />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* 编辑料仓模态框 */}
          {editingSilo && (
            <div className='fixed inset-0 z-[110] bg-black/50 flex items-center justify-center p-4'>
              <div className='bg-white rounded-lg shadow-xl max-w-md w-full'>
                <div className='p-6 border-b'>
                  <div className='flex items-center justify-between'>
                    <h3 className='text-lg font-semibold'>编辑料仓</h3>
                    <Button variant='ghost' size='sm' onClick={() => setEditingSilo(null)}>
                      ✕
                    </Button>
                  </div>
                </div>

                <div className='p-6'>
                  <div className='space-y-4'>
                    <div className='grid grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label>料仓名称</Label>
                        <Input
                          value={editingSilo.name}
                          onChange={e =>
                            setEditingSilo(prev =>
                              prev ? { ...prev, name: e.target.value } : null
                            )
                          }
                        />
                      </div>

                      <div className='space-y-2'>
                        <Label>材料类型</Label>
                        <Select
                          value={editingSilo.type}
                          onValueChange={value =>
                            setEditingSilo(prev => (prev ? { ...prev, type: value as any } : null))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className='z-[150]'>
                            {siloTypes.map(type => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className='grid grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label>所属搅拌站</Label>
                        <Select
                          value={editingSilo.mixingStation}
                          onValueChange={value =>
                            setEditingSilo(prev =>
                              prev ? { ...prev, mixingStation: value } : null
                            )
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='选择搅拌站' />
                          </SelectTrigger>
                          <SelectContent className='z-[150]'>
                            {mixingStations.map(station => (
                              <SelectItem key={station.id} value={station.id}>
                                {station.name} - {station.location}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className='space-y-2'>
                        <Label>材料名称</Label>
                        <Input
                          value={editingSilo.materialName}
                          onChange={e =>
                            setEditingSilo(prev =>
                              prev ? { ...prev, materialName: e.target.value } : null
                            )
                          }
                          placeholder='请输入材料名称'
                        />
                      </div>
                    </div>

                    <div className='grid grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label>存放地</Label>
                        <Select
                          value={editingSilo.storageLocation}
                          onValueChange={value =>
                            setEditingSilo(prev =>
                              prev ? { ...prev, storageLocation: value } : null
                            )
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='选择存放地' />
                          </SelectTrigger>
                          <SelectContent className='z-[150]'>
                            {storageLocations.map(location => (
                              <SelectItem key={location.id} value={location.id}>
                                {location.name} - {location.description}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className='space-y-2'>
                        <Label>材料规格</Label>
                        <Select
                          value={editingSilo.specification}
                          onValueChange={value => {
                            if (value === 'manage-specs') {
                              setSelectedMaterialType(editingSilo.type);
                              setIsManagingSpecs(true);
                            } else {
                              setEditingSilo(prev =>
                                prev ? { ...prev, specification: value } : null
                              );
                            }
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='选择材料规格' />
                          </SelectTrigger>
                          <SelectContent className='z-[150]'>
                            <SelectItem value='manage-specs'>
                              <div className='flex items-center gap-2 text-blue-600'>
                                <Edit className='h-3 w-3' />
                                维护材料规格
                              </div>
                            </SelectItem>
                            {getSpecsForMaterialType(editingSilo.type).map(spec => (
                              <SelectItem key={spec.id} value={spec.id}>
                                {spec.specName} - {spec.description}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className='grid grid-cols-3 gap-4'>
                      <div className='space-y-2'>
                        <Label>总容量</Label>
                        <Input
                          type='number'
                          value={editingSilo.capacity}
                          onChange={e =>
                            setEditingSilo(prev =>
                              prev ? { ...prev, capacity: parseFloat(e.target.value) || 0 } : null
                            )
                          }
                        />
                      </div>

                      <div className='space-y-2'>
                        <Label>当前库存</Label>
                        <Input
                          type='number'
                          value={editingSilo.currentAmount}
                          onChange={e =>
                            setEditingSilo(prev =>
                              prev
                                ? { ...prev, currentAmount: parseFloat(e.target.value) || 0 }
                                : null
                            )
                          }
                        />
                      </div>

                      <div className='space-y-2'>
                        <Label>单位</Label>
                        <Input
                          value={editingSilo.unit}
                          onChange={e =>
                            setEditingSilo(prev =>
                              prev ? { ...prev, unit: e.target.value } : null
                            )
                          }
                        />
                      </div>
                    </div>

                    <div className='grid grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label>密度 (t/m³)</Label>
                        <Input
                          type='number'
                          step='0.1'
                          value={editingSilo.density}
                          onChange={e =>
                            setEditingSilo(prev =>
                              prev ? { ...prev, density: parseFloat(e.target.value) || 0 } : null
                            )
                          }
                        />
                      </div>

                      <div className='space-y-2'>
                        <Label>状态</Label>
                        <Select
                          value={editingSilo.status}
                          onValueChange={value =>
                            setEditingSilo(prev =>
                              prev ? { ...prev, status: value as any } : null
                            )
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className='z-[150]'>
                            <SelectItem value='normal'>正常</SelectItem>
                            <SelectItem value='low'>库存不足</SelectItem>
                            <SelectItem value='empty'>已空</SelectItem>
                            <SelectItem value='maintenance'>维护中</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className='space-y-2'>
                      <Label>供应商</Label>
                      <Input
                        value={editingSilo.supplier}
                        onChange={e =>
                          setEditingSilo(prev =>
                            prev ? { ...prev, supplier: e.target.value } : null
                          )
                        }
                      />
                    </div>

                    <div className='space-y-2'>
                      <Label>位置</Label>
                      <Input
                        value={editingSilo.location}
                        onChange={e =>
                          setEditingSilo(prev =>
                            prev ? { ...prev, location: e.target.value } : null
                          )
                        }
                      />
                    </div>

                    <div className='flex justify-end gap-2 pt-4'>
                      <Button variant='outline' onClick={() => setEditingSilo(null)}>
                        取消
                      </Button>
                      <Button onClick={handleSave} className='gap-2'>
                        <Save className='h-4 w-4' />
                        保存
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 添加新料仓模态框 */}
          {isAddingNew && (
            <div className='fixed inset-0 z-[110] bg-black/50 flex items-center justify-center p-4'>
              <div className='bg-white rounded-lg shadow-xl max-w-md w-full'>
                <div className='p-6 border-b'>
                  <div className='flex items-center justify-between'>
                    <h3 className='text-lg font-semibold'>添加新料仓</h3>
                    <Button variant='ghost' size='sm' onClick={() => setIsAddingNew(false)}>
                      ✕
                    </Button>
                  </div>
                </div>

                <div className='p-6'>
                  <div className='space-y-4'>
                    <div className='grid grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label>料仓名称 *</Label>
                        <Input
                          value={newSilo.name}
                          onChange={e => setNewSilo(prev => ({ ...prev, name: e.target.value }))}
                          placeholder='请输入料仓名称'
                        />
                      </div>

                      <div className='space-y-2'>
                        <Label>材料类型</Label>
                        <Select
                          value={newSilo.type}
                          onValueChange={value =>
                            setNewSilo(prev => ({ ...prev, type: value as any }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className='z-[150]'>
                            {siloTypes.map(type => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className='grid grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label>所属搅拌站 *</Label>
                        <Select
                          value={newSilo.mixingStation}
                          onValueChange={value =>
                            setNewSilo(prev => ({ ...prev, mixingStation: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='选择搅拌站' />
                          </SelectTrigger>
                          <SelectContent className='z-[150]'>
                            {mixingStations.map(station => (
                              <SelectItem key={station.id} value={station.id}>
                                {station.name} - {station.location}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className='space-y-2'>
                        <Label>材料名称 *</Label>
                        <Input
                          value={newSilo.materialName}
                          onChange={e =>
                            setNewSilo(prev => ({ ...prev, materialName: e.target.value }))
                          }
                          placeholder='请输入材料名称'
                        />
                      </div>
                    </div>

                    <div className='grid grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label>存放地 *</Label>
                        <Select
                          value={newSilo.storageLocation}
                          onValueChange={value =>
                            setNewSilo(prev => ({ ...prev, storageLocation: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='选择存放地' />
                          </SelectTrigger>
                          <SelectContent className='z-[150]'>
                            {storageLocations.map(location => (
                              <SelectItem key={location.id} value={location.id}>
                                {location.name} - {location.description}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className='space-y-2'>
                        <Label>材料规格</Label>
                        <Select
                          value={newSilo.specification}
                          onValueChange={value => {
                            if (value === 'manage-specs') {
                              setSelectedMaterialType(newSilo.type || 'cement');
                              setIsManagingSpecs(true);
                            } else {
                              setNewSilo(prev => ({ ...prev, specification: value }));
                            }
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='选择材料规格' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='manage-specs'>
                              <div className='flex items-center gap-2 text-blue-600'>
                                <Edit className='h-3 w-3' />
                                维护材料规格
                              </div>
                            </SelectItem>
                            {getSpecsForMaterialType(newSilo.type || 'cement').map(spec => (
                              <SelectItem key={spec.id} value={spec.id}>
                                {spec.specName} - {spec.description}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className='grid grid-cols-3 gap-4'>
                      <div className='space-y-2'>
                        <Label>总容量 *</Label>
                        <Input
                          type='number'
                          value={newSilo.capacity}
                          onChange={e =>
                            setNewSilo(prev => ({
                              ...prev,
                              capacity: parseFloat(e.target.value) || 0,
                            }))
                          }
                          placeholder='0'
                        />
                      </div>

                      <div className='space-y-2'>
                        <Label>当前库存</Label>
                        <Input
                          type='number'
                          value={newSilo.currentAmount}
                          onChange={e =>
                            setNewSilo(prev => ({
                              ...prev,
                              currentAmount: parseFloat(e.target.value) || 0,
                            }))
                          }
                          placeholder='0'
                        />
                      </div>

                      <div className='space-y-2'>
                        <Label>单位</Label>
                        <Input
                          value={newSilo.unit}
                          onChange={e => setNewSilo(prev => ({ ...prev, unit: e.target.value }))}
                          placeholder='t'
                        />
                      </div>
                    </div>

                    <div className='grid grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label>密度 (t/m³)</Label>
                        <Input
                          type='number'
                          step='0.1'
                          value={newSilo.density}
                          onChange={e =>
                            setNewSilo(prev => ({
                              ...prev,
                              density: parseFloat(e.target.value) || 0,
                            }))
                          }
                          placeholder='1.0'
                        />
                      </div>

                      <div className='space-y-2'>
                        <Label>状态</Label>
                        <Select
                          value={newSilo.status}
                          onValueChange={value =>
                            setNewSilo(prev => ({ ...prev, status: value as any }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='normal'>正常</SelectItem>
                            <SelectItem value='low'>库存不足</SelectItem>
                            <SelectItem value='empty'>已空</SelectItem>
                            <SelectItem value='maintenance'>维护中</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className='space-y-2'>
                      <Label>供应商</Label>
                      <Input
                        value={newSilo.supplier}
                        onChange={e => setNewSilo(prev => ({ ...prev, supplier: e.target.value }))}
                        placeholder='请输入供应商名称'
                      />
                    </div>

                    <div className='space-y-2'>
                      <Label>位置</Label>
                      <Input
                        value={newSilo.location}
                        onChange={e => setNewSilo(prev => ({ ...prev, location: e.target.value }))}
                        placeholder='请输入料仓位置'
                      />
                    </div>

                    <div className='flex justify-end gap-2 pt-4'>
                      <Button variant='outline' onClick={() => setIsAddingNew(false)}>
                        取消
                      </Button>
                      <Button onClick={handleAddNew} className='gap-2'>
                        <Plus className='h-4 w-4' />
                        添加
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 规格管理模态框 */}
          {isManagingSpecs && (
            <div className='fixed inset-0 z-[120] bg-black/50 flex items-center justify-center p-4'>
              <div className='bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-y-auto'>
                <div className='p-6 border-b'>
                  <div className='flex items-center justify-between'>
                    <h3 className='text-lg font-semibold'>材料规格管理</h3>
                    <Button variant='ghost' size='sm' onClick={() => setIsManagingSpecs(false)}>
                      ✕
                    </Button>
                  </div>
                </div>

                <div className='p-6'>
                  <div className='space-y-4'>
                    {/* 材料类型选择 */}
                    <div className='flex gap-2 flex-wrap'>
                      {siloTypes.map(type => (
                        <Button
                          key={type.value}
                          variant={selectedMaterialType === type.value ? 'default' : 'outline'}
                          size='sm'
                          onClick={() => setSelectedMaterialType(type.value)}
                        >
                          {type.label}
                        </Button>
                      ))}
                    </div>

                    {/* 规格列表 */}
                    <div className='space-y-2'>
                      <div className='flex justify-between items-center'>
                        <h4 className='font-medium'>
                          {siloTypes.find(t => t.value === selectedMaterialType)?.label || ''}
                          规格列表
                        </h4>
                        <Button
                          size='sm'
                          onClick={() => {
                            const specName = prompt('请输入规格名称:');
                            const description = prompt('请输入规格描述:');
                            if (specName && description) {
                              handleAddSpec(selectedMaterialType, specName, description);
                            }
                          }}
                          className='gap-2'
                        >
                          <Plus className='h-3 w-3' />
                          添加规格
                        </Button>
                      </div>

                      <div className='grid gap-2'>
                        {getSpecsForMaterialType(selectedMaterialType).map(spec => (
                          <div
                            key={spec.id}
                            className='flex items-center justify-between p-3 border rounded-lg'
                          >
                            <div>
                              <div className='font-medium'>{spec.specName}</div>
                              <div className='text-sm text-muted-foreground'>
                                {spec.description}
                              </div>
                            </div>
                            <div className='flex gap-2'>
                              <Button
                                variant='outline'
                                size='sm'
                                onClick={() => {
                                  const newName = prompt('请输入新的规格名称:', spec.specName);
                                  const newDesc = prompt('请输入新的规格描述:', spec.description);
                                  if (newName && newDesc) {
                                    handleUpdateSpec(spec.id, newName, newDesc);
                                  }
                                }}
                              >
                                <Edit className='h-3 w-3' />
                              </Button>
                              <Button
                                variant='outline'
                                size='sm'
                                onClick={() => {
                                  if (confirm(`确定要删除规格"${spec.specName}"吗？`)) {
                                    handleDeleteSpec(spec.id);
                                  }
                                }}
                                className='text-red-600'
                              >
                                <Trash2 className='h-3 w-3' />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className='flex justify-end gap-2 pt-4'>
                    <Button variant='outline' onClick={() => setIsManagingSpecs(false)}>
                      关闭
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 搅拌站管理模态框 */}
          {isManagingStations && (
            <div className='fixed inset-0 z-[120] bg-black/50 flex items-center justify-center p-4'>
              <div className='bg-white rounded-lg shadow-xl max-w-2xl w-full'>
                <div className='p-6 border-b'>
                  <div className='flex items-center justify-between'>
                    <h3 className='text-lg font-semibold'>搅拌站管理</h3>
                    <Button variant='ghost' size='sm' onClick={() => setIsManagingStations(false)}>
                      ✕
                    </Button>
                  </div>
                </div>

                <div className='p-6'>
                  <div className='space-y-4'>
                    <div className='flex justify-between items-center'>
                      <h4 className='font-medium'>搅拌站列表</h4>
                      <Button
                        size='sm'
                        onClick={() => {
                          const name = prompt('请输入搅拌站名称:');
                          const location = prompt('请输入搅拌站位置:');
                          if (name && location) {
                            const newStation: MixingStation = {
                              id: `station-${Date.now()}`,
                              name,
                              location,
                            };
                            setMixingStations(prev => [...prev, newStation]);
                            toast({
                              title: '搅拌站已添加',
                              description: `${name}已成功添加`,
                            });
                          }
                        }}
                        className='gap-2'
                      >
                        <Plus className='h-3 w-3' />
                        添加搅拌站
                      </Button>
                    </div>

                    <div className='grid gap-2'>
                      {mixingStations.map(station => (
                        <div
                          key={station.id}
                          className='flex items-center justify-between p-3 border rounded-lg'
                        >
                          <div>
                            <div className='font-medium'>{station.name}</div>
                            <div className='text-sm text-muted-foreground'>{station.location}</div>
                          </div>
                          <div className='flex gap-2'>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => {
                                const newName = prompt('请输入新的搅拌站名称:', station.name);
                                const newLocation = prompt(
                                  '请输入新的搅拌站位置:',
                                  station.location
                                );
                                if (newName && newLocation) {
                                  setMixingStations(prev =>
                                    prev.map(s =>
                                      s.id === station.id
                                        ? { ...s, name: newName, location: newLocation }
                                        : s
                                    )
                                  );
                                  toast({
                                    title: '搅拌站已更新',
                                    description: `${newName}已成功更新`,
                                  });
                                }
                              }}
                            >
                              <Edit className='h-3 w-3' />
                            </Button>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => {
                                if (confirm(`确定要删除搅拌站"${station.name}"吗？`)) {
                                  setMixingStations(prev => prev.filter(s => s.id !== station.id));
                                  toast({
                                    title: '搅拌站已删除',
                                    description: '搅拌站已从系统中删除',
                                  });
                                }
                              }}
                              className='text-red-600'
                            >
                              <Trash2 className='h-3 w-3' />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className='flex justify-end gap-2 pt-4'>
                    <Button variant='outline' onClick={() => setIsManagingStations(false)}>
                      关闭
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 存放地管理模态框 */}
          {isManagingLocations && (
            <div className='fixed inset-0 z-[120] bg-black/50 flex items-center justify-center p-4'>
              <div className='bg-white rounded-lg shadow-xl max-w-2xl w-full'>
                <div className='p-6 border-b'>
                  <div className='flex items-center justify-between'>
                    <h3 className='text-lg font-semibold'>存放地管理</h3>
                    <Button variant='ghost' size='sm' onClick={() => setIsManagingLocations(false)}>
                      ✕
                    </Button>
                  </div>
                </div>

                <div className='p-6'>
                  <div className='space-y-4'>
                    <div className='flex justify-between items-center'>
                      <h4 className='font-medium'>存放地列表</h4>
                      <Button
                        size='sm'
                        onClick={() => {
                          const name = prompt('请输入存放地名称:');
                          const description = prompt('请输入存放地描述:');
                          if (name && description) {
                            const newLocation: StorageLocation = {
                              id: `storage-${Date.now()}`,
                              name,
                              description,
                            };
                            setStorageLocations(prev => [...prev, newLocation]);
                            toast({
                              title: '存放地已添加',
                              description: `${name}已成功添加`,
                            });
                          }
                        }}
                        className='gap-2'
                      >
                        <Plus className='h-3 w-3' />
                        添加存放地
                      </Button>
                    </div>

                    <div className='grid gap-2'>
                      {storageLocations.map(location => (
                        <div
                          key={location.id}
                          className='flex items-center justify-between p-3 border rounded-lg'
                        >
                          <div>
                            <div className='font-medium'>{location.name}</div>
                            <div className='text-sm text-muted-foreground'>
                              {location.description}
                            </div>
                          </div>
                          <div className='flex gap-2'>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => {
                                const newName = prompt('请输入新的存放地名称:', location.name);
                                const newDesc = prompt(
                                  '请输入新的存放地描述:',
                                  location.description
                                );
                                if (newName && newDesc) {
                                  setStorageLocations(prev =>
                                    prev.map(l =>
                                      l.id === location.id
                                        ? { ...l, name: newName, description: newDesc }
                                        : l
                                    )
                                  );
                                  toast({
                                    title: '存放地已更新',
                                    description: `${newName}已成功更新`,
                                  });
                                }
                              }}
                            >
                              <Edit className='h-3 w-3' />
                            </Button>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => {
                                if (confirm(`确定要删除存放地"${location.name}"吗？`)) {
                                  setStorageLocations(prev =>
                                    prev.filter(l => l.id !== location.id)
                                  );
                                  toast({
                                    title: '存放地已删除',
                                    description: '存放地已从系统中删除',
                                  });
                                }
                              }}
                              className='text-red-600'
                            >
                              <Trash2 className='h-3 w-3' />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className='flex justify-end gap-2 pt-4'>
                    <Button variant='outline' onClick={() => setIsManagingLocations(false)}>
                      关闭
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 底部操作栏 */}
          <div className='flex justify-end pt-4 border-t'>
            <Button variant='outline' onClick={onClose}>
              关闭
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
