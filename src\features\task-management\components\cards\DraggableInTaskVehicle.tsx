'use client';

import React, { useRef } from 'react';
import { useDrag } from 'react-dnd';
import { ItemTypes } from '@/core/constants/dndItemTypes';
import type { InTaskVehicleCardStyle, Task, Vehicle, VehicleDisplayMode } from '@/core/types';
import { InTaskVehicleCard } from '../in-task-vehicle-card';

interface DraggableInTaskVehicleProps {
  vehicle: Vehicle;
  task: Task;
  vehicleDisplayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  productionLineCount: number;
  density?: 'compact' | 'normal' | 'loose';
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string) => void;
  onOpenContextMenu?: (event: React.MouseEvent, vehicle: Vehicle) => void;
}

/**
 * 可拖拽的任务内车辆卡片组件
 * 解决在map中使用Hook的问题
 */
export const DraggableInTaskVehicle: React.FC<DraggableInTaskVehicleProps> = ({
  vehicle,
  task,
  vehicleDisplayMode,
  inTaskVehicleCardStyles,
  productionLineCount,
  density = 'normal',
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenContextMenu,
}) => {
  const vehicleRef = useRef<HTMLDivElement>(null);

  // 使用React DnD的拖拽功能
  const [{ isDragging: vehicleIsDragging }, vehicleDragSource] = useDrag({
    type: ItemTypes.VEHICLE_CARD_DISPATCH,
    item: {
      vehicle,
      type: ItemTypes.VEHICLE_CARD_DISPATCH,
      sourceTaskId: task.id,
      isVehicleTransfer: true, // 标记这是车辆转发操作
    },
    canDrag: () => true, // 已调度车辆可以拖拽进行转发
    collect: (monitor: any) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  // 连接拖拽ref
  vehicleDragSource(vehicleRef);

  return (
    <div ref={vehicleRef} style={{ opacity: vehicleIsDragging ? 0.5 : 1 }}>
      <InTaskVehicleCard
        vehicle={vehicle}
        task={task}
        vehicleDisplayMode={vehicleDisplayMode}
        inTaskVehicleCardStyles={inTaskVehicleCardStyles}
        productionLineCount={productionLineCount}
        density={density}
        onCancelDispatch={onCancelDispatch}
        onOpenStyleEditor={onOpenStyleEditor}
        onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetails}
        onOpenContextMenu={onOpenContextMenu}
        isDragging={vehicleIsDragging}
      />
    </div>
  );
};
