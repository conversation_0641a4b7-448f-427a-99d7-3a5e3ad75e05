/**
 * 配比数据状态管理
 * 负责管理配比计算参数、材料数据、计算结果等核心业务数据
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type {
  RatioDataState,
  RatioCalculationParams,
  ReverseCalculationParams,
  RatioResult,
  UnifiedRatioMaterial,
  RatioCalculationMethod,
} from '@/core/types/ratio';
import { ExposureClass, PlacementMethod, FinishingRequirement } from '@/core/types';

// 默认配比计算参数
const defaultCalculationParams: RatioCalculationParams = {
  // 基本参数
  targetStrength: 25,
  slump: 180,
  maxAggregateSize: 20,
  exposureClass: ExposureClass.XC1,

  // 环境参数
  ambientTemperature: 20,
  relativeHumidity: 60,
  cementTemperature: 20,
  aggregateTemperature: 20,

  // 材料选择
  selectedMaterials: [],
  cementType: 'P.O 42.5',
  aggregateType: '碎石',
  waterType: '自来水',

  // 配比参数
  waterCementRatio: 0.45,
  sandRatio: 35,
  cementContent: 350,
  waterContent: 180,

  // 外加剂参数
  additiveRatio: 1.2,
  flyashRatio: 15,
  mineralPowderRatio: 10,
  silicaFumeRatio: 0,
  antifreezeRatio: 0,
  expansionRatio: 0,

  // 数量参数
  cementAmount: 350,
  waterAmount: 180,
  density: 2400,
  airContent: 2,
  strengthGrade: 25,
  ultraFineSandRatio: 0,
  earlyStrengthRatio: 0,

  // 施工参数
  placementMethod: PlacementMethod.PUMP,
  finishingRequirement: FinishingRequirement.SMOOTH,
  cureConditions: {
    method: '自然养护',
    duration: 28,
    temperature: 20,
    humidity: 95,
  },

  // 外加剂用量（具体数值）
  admixtureAmount: 4.2,
  antifreezeAmount: 0,
  flyAshAmount: 52.5,
  mineralPowderAmount: 35,
  s105PowderAmount: 0,
  expansionAgentAmount: 0,
  earlyStrengthAgentAmount: 0,
  ultraFineSandAmount: 0,
};

// 默认反算参数
const defaultReverseParams: ReverseCalculationParams = {
  targetVolume: 1,
  currentRatio: defaultCalculationParams,
  cementSubstitutionRate: 20,
  flyAshFactor: 0.7,
  flyAshDensity: 2.2,
  sandDensity: 2.65,
  flyAshCementFactor: 0.7,
  silicaFumeCementFactor: 2.0,
};

// 默认配比结果
const defaultProportions: RatioResult = {
  materials: {},
  totalVolume: 1,
  density: 2400,
  water: 180,
  cement: 350,
  flyAsh: 52.5,
  mineralPowder: 35,
  s105Powder: 0,
  expansionAgent: 0,
  earlyStrengthAgent: 0,
  sand: 650,
  stone: 1200,
  gravel: 1200,
  admixture: 4.2,
  antifreeze: 0,
  ultraFineSand: 0,
  totalWeight: 2421.7,
};

// 配比数据状态接口
interface RatioDataStoreState extends RatioDataState {
  // 操作方法
  initialize: (task: any) => void;
  setCalculationParams: (params: Partial<RatioCalculationParams>) => void;
  setReverseParam: (key: keyof ReverseCalculationParams, value: number) => void;
  setCalculationMethod: (method: RatioCalculationMethod) => void;
  setProportions: (proportions: RatioResult) => void;
  setMaterials: (materials: UnifiedRatioMaterial[]) => void;
  addMaterial: () => boolean;
  updateMaterial: (id: string, material: Partial<UnifiedRatioMaterial>) => void;
  deleteMaterial: (id: string) => void;
  reset: () => void;
}

// 创建配比数据Store
export const useRatioDataStore = create<RatioDataStoreState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // 初始状态
      task: null,
      calculationParams: defaultCalculationParams,
      reverseParams: defaultReverseParams,
      proportions: defaultProportions,
      materials: [],
      calculationMethod: 'method1',
      isInitialized: false,

      // 初始化
      initialize: (task: any) => {
        set(state => {
          state.task = task;
          state.isInitialized = true;
          // 根据任务信息初始化默认材料
          state.materials = [
            {
              id: 'material-1',
              materialId: 'cement-1',
              name: '水泥',
              specification: 'P.O 42.5',
              category: 'cementitious' as any,
              unit: 'kg',
              density: 3100,
              theoreticalAmount: 350,
              actualAmount: 350,
              waterContent: 0,
              stoneContent: 0,
              designValue: 350,
              siloId: 'silo-1',
              siloName: '1#水泥仓',
              cost: 450,
              supplier: '海螺水泥',
            },
            {
              id: 'material-2',
              materialId: 'water-1',
              name: '水',
              specification: '自来水',
              category: 'water' as any,
              unit: 'kg',
              density: 1000,
              theoreticalAmount: 180,
              actualAmount: 180,
              waterContent: 0,
              stoneContent: 0,
              designValue: 180,
              siloId: 'silo-2',
              siloName: '水罐',
              cost: 3,
              supplier: '自来水公司',
            },
          ];
        });
      },

      // 设置计算参数
      setCalculationParams: (params: Partial<RatioCalculationParams>) => {
        set(state => {
          Object.assign(state.calculationParams, params);
        });
      },

      // 设置反算参数
      setReverseParam: (key: keyof ReverseCalculationParams, value: number) => {
        set(state => {
          state.reverseParams[key] = value as any;
        });
      },

      // 设置计算方法
      setCalculationMethod: (method: RatioCalculationMethod) => {
        set(state => {
          state.calculationMethod = method;
        });
      },

      // 设置配比结果
      setProportions: (proportions: RatioResult) => {
        set(state => {
          state.proportions = proportions;
        });
      },

      // 设置材料列表
      setMaterials: (materials: UnifiedRatioMaterial[]) => {
        set(state => {
          state.materials = materials;
        });
      },

      // 添加材料
      addMaterial: () => {
        const state = get();
        // 检查是否有空的材料行
        const hasEmptyMaterial = state.materials.some(
          material => !material.name || material.name.trim() === ''
        );

        if (hasEmptyMaterial) {
          return false;
        }

        set(state => {
          const newMaterial: UnifiedRatioMaterial = {
            id: `material-${Date.now()}`,
            materialId: '',
            name: '',
            specification: '',
            category: 'aggregate' as any,
            unit: 'kg',
            density: 2650,
            theoreticalAmount: 0,
            actualAmount: 0,
            waterContent: 0,
            stoneContent: 0,
            designValue: 0,
            siloId: '',
            siloName: '',
            cost: 0,
            supplier: '',
          };
          state.materials.push(newMaterial);
        });
        return true;
      },

      // 更新材料
      updateMaterial: (id: string, material: Partial<UnifiedRatioMaterial>) => {
        set(state => {
          const index = state.materials.findIndex(m => m.id === id);
          if (index !== -1) {
            const currentMaterial = state.materials[index];
            const updatedMaterial = { ...currentMaterial, ...material };

            // 重新计算实际用量和设计值（添加null检查）
            if (
              updatedMaterial.theoreticalAmount !== undefined &&
              updatedMaterial.waterContent !== undefined
            ) {
              updatedMaterial.actualAmount =
                updatedMaterial.theoreticalAmount * (1 + updatedMaterial.waterContent / 100);
              updatedMaterial.designValue = updatedMaterial.actualAmount;
            }

            state.materials[index] = updatedMaterial as UnifiedRatioMaterial;
          }
        });
      },

      // 删除材料
      deleteMaterial: (id: string) => {
        set(state => {
          state.materials = state.materials.filter(m => m.id !== id);
        });
      },

      // 重置状态
      reset: () => {
        set(state => {
          state.task = null;
          state.calculationParams = defaultCalculationParams;
          state.reverseParams = defaultReverseParams;
          state.proportions = defaultProportions;
          state.materials = [];
          state.calculationMethod = 'method1';
          state.isInitialized = false;
        });
      },
    }))
  )
);

// 导出选择器
export const ratioDataSelectors = {
  task: (state: RatioDataStoreState) => state.task,
  calculationParams: (state: RatioDataStoreState) => state.calculationParams,
  reverseParams: (state: RatioDataStoreState) => state.reverseParams,
  proportions: (state: RatioDataStoreState) => state.proportions,
  materials: (state: RatioDataStoreState) => state.materials,
  calculationMethod: (state: RatioDataStoreState) => state.calculationMethod,
  isInitialized: (state: RatioDataStoreState) => state.isInitialized,
};
