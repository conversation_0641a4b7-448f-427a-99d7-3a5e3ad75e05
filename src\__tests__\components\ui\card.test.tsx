/**
 * Card组件单元测试
 */

import React from 'react';
import { screen } from '@testing-library/react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/shared/components/card';
import { render } from '../../utils/test-utils.helper';

describe('Card组件', () => {
  describe('Card基础组件', () => {
    it('应该正确渲染Card组件', () => {
      render(<Card data-testid='card'>卡片内容</Card>);

      const card = screen.getByTestId('card');
      expect(card).toBeInTheDocument();
      expect(card).toHaveClass(
        'rounded-lg',
        'border',
        'bg-card',
        'text-card-foreground',
        'shadow-sm'
      );
      expect(card).toHaveTextContent('卡片内容');
    });

    it('应该支持自定义className', () => {
      render(
        <Card className='custom-card' data-testid='card'>
          内容
        </Card>
      );

      const card = screen.getByTestId('card');
      expect(card).toHaveClass('custom-card');
      // 应该保留默认样式
      expect(card).toHaveClass('rounded-lg', 'border');
    });

    it('应该支持所有HTML div属性', () => {
      render(
        <Card id='test-card' role='region' aria-label='测试卡片' data-testid='card'>
          内容
        </Card>
      );

      const card = screen.getByTestId('card');
      expect(card).toHaveAttribute('id', 'test-card');
      expect(card).toHaveAttribute('role', 'region');
      expect(card).toHaveAttribute('aria-label', '测试卡片');
    });

    it('应该正确转发ref', () => {
      const ref = React.createRef<HTMLDivElement>();
      render(
        <Card ref={ref} data-testid='card'>
          内容
        </Card>
      );

      expect(ref.current).toBeInstanceOf(HTMLDivElement);
      expect(ref.current).toBe(screen.getByTestId('card'));
    });
  });

  describe('CardHeader组件', () => {
    it('应该正确渲染CardHeader组件', () => {
      render(<CardHeader data-testid='card-header'>头部内容</CardHeader>);

      const header = screen.getByTestId('card-header');
      expect(header).toBeInTheDocument();
      expect(header).toHaveClass('flex', 'flex-col', 'space-y-1.5', 'p-6');
      expect(header).toHaveTextContent('头部内容');
    });

    it('应该支持自定义className', () => {
      render(
        <CardHeader className='custom-header' data-testid='card-header'>
          头部
        </CardHeader>
      );

      const header = screen.getByTestId('card-header');
      expect(header).toHaveClass('custom-header');
      expect(header).toHaveClass('flex', 'flex-col', 'space-y-1.5', 'p-6');
    });
  });

  describe('CardTitle组件', () => {
    it('应该正确渲染CardTitle组件', () => {
      render(<CardTitle data-testid='card-title'>卡片标题</CardTitle>);

      const title = screen.getByTestId('card-title');
      expect(title).toBeInTheDocument();
      expect(title).toHaveClass('text-2xl', 'font-semibold', 'leading-none', 'tracking-tight');
      expect(title).toHaveTextContent('卡片标题');
    });

    it('应该支持自定义className', () => {
      render(
        <CardTitle className='custom-title' data-testid='card-title'>
          标题
        </CardTitle>
      );

      const title = screen.getByTestId('card-title');
      expect(title).toHaveClass('custom-title');
      expect(title).toHaveClass('text-2xl', 'font-semibold');
    });
  });

  describe('CardDescription组件', () => {
    it('应该正确渲染CardDescription组件', () => {
      render(<CardDescription data-testid='card-description'>卡片描述</CardDescription>);

      const description = screen.getByTestId('card-description');
      expect(description).toBeInTheDocument();
      expect(description).toHaveClass('text-sm', 'text-muted-foreground');
      expect(description).toHaveTextContent('卡片描述');
    });

    it('应该支持自定义className', () => {
      render(
        <CardDescription className='custom-description' data-testid='card-description'>
          描述
        </CardDescription>
      );

      const description = screen.getByTestId('card-description');
      expect(description).toHaveClass('custom-description');
      expect(description).toHaveClass('text-sm', 'text-muted-foreground');
    });
  });

  describe('CardContent组件', () => {
    it('应该正确渲染CardContent组件', () => {
      render(<CardContent data-testid='card-content'>卡片内容</CardContent>);

      const content = screen.getByTestId('card-content');
      expect(content).toBeInTheDocument();
      expect(content).toHaveClass('p-6', 'pt-0');
      expect(content).toHaveTextContent('卡片内容');
    });

    it('应该支持自定义className', () => {
      render(
        <CardContent className='custom-content' data-testid='card-content'>
          内容
        </CardContent>
      );

      const content = screen.getByTestId('card-content');
      expect(content).toHaveClass('custom-content');
      expect(content).toHaveClass('p-6', 'pt-0');
    });
  });

  describe('CardFooter组件', () => {
    it('应该正确渲染CardFooter组件', () => {
      render(<CardFooter data-testid='card-footer'>卡片底部</CardFooter>);

      const footer = screen.getByTestId('card-footer');
      expect(footer).toBeInTheDocument();
      expect(footer).toHaveClass('flex', 'items-center', 'p-6', 'pt-0');
      expect(footer).toHaveTextContent('卡片底部');
    });

    it('应该支持自定义className', () => {
      render(
        <CardFooter className='custom-footer' data-testid='card-footer'>
          底部
        </CardFooter>
      );

      const footer = screen.getByTestId('card-footer');
      expect(footer).toHaveClass('custom-footer');
      expect(footer).toHaveClass('flex', 'items-center', 'p-6', 'pt-0');
    });
  });

  describe('组合使用测试', () => {
    it('应该正确渲染完整的卡片结构', () => {
      render(
        <Card data-testid='complete-card'>
          <CardHeader data-testid='header'>
            <CardTitle data-testid='title'>任务卡片</CardTitle>
            <CardDescription data-testid='description'>这是一个任务卡片的描述</CardDescription>
          </CardHeader>
          <CardContent data-testid='content'>
            <p>这里是卡片的主要内容</p>
          </CardContent>
          <CardFooter data-testid='footer'>
            <button>操作按钮</button>
          </CardFooter>
        </Card>
      );

      // 验证所有组件都正确渲染
      expect(screen.getByTestId('complete-card')).toBeInTheDocument();
      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('title')).toBeInTheDocument();
      expect(screen.getByTestId('description')).toBeInTheDocument();
      expect(screen.getByTestId('content')).toBeInTheDocument();
      expect(screen.getByTestId('footer')).toBeInTheDocument();

      // 验证内容
      expect(screen.getByText('任务卡片')).toBeInTheDocument();
      expect(screen.getByText('这是一个任务卡片的描述')).toBeInTheDocument();
      expect(screen.getByText('这里是卡片的主要内容')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '操作按钮' })).toBeInTheDocument();
    });

    it('应该支持嵌套结构', () => {
      render(
        <Card data-testid='outer-card'>
          <CardContent>
            <Card data-testid='inner-card'>
              <CardContent>嵌套卡片内容</CardContent>
            </Card>
          </CardContent>
        </Card>
      );

      expect(screen.getByTestId('outer-card')).toBeInTheDocument();
      expect(screen.getByTestId('inner-card')).toBeInTheDocument();
      expect(screen.getByText('嵌套卡片内容')).toBeInTheDocument();
    });
  });

  describe('可访问性测试', () => {
    it('应该支持ARIA属性', () => {
      render(
        <Card
          role='article'
          aria-labelledby='card-title'
          aria-describedby='card-description'
          data-testid='accessible-card'
        >
          <CardTitle id='card-title'>可访问的卡片</CardTitle>
          <CardDescription id='card-description'>这是一个可访问的卡片描述</CardDescription>
        </Card>
      );

      const card = screen.getByTestId('accessible-card');
      expect(card).toHaveAttribute('role', 'article');
      expect(card).toHaveAttribute('aria-labelledby', 'card-title');
      expect(card).toHaveAttribute('aria-describedby', 'card-description');
    });

    it('应该支持键盘导航', () => {
      render(
        <Card tabIndex={0} data-testid='focusable-card'>
          可聚焦的卡片
        </Card>
      );

      const card = screen.getByTestId('focusable-card');
      expect(card).toHaveAttribute('tabIndex', '0');

      card.focus();
      expect(card).toHaveFocus();
    });
  });

  describe('边界条件测试', () => {
    it('应该处理空内容', () => {
      render(<Card data-testid='empty-card'></Card>);

      const card = screen.getByTestId('empty-card');
      expect(card).toBeInTheDocument();
      expect(card).toBeEmptyDOMElement();
    });

    it('应该处理复杂的子元素结构', () => {
      render(
        <Card data-testid='complex-card'>
          <div>
            <span>文本1</span>
            <div>
              <p>段落内容</p>
              <ul>
                <li>列表项1</li>
                <li>列表项2</li>
              </ul>
            </div>
          </div>
        </Card>
      );

      const card = screen.getByTestId('complex-card');
      expect(card).toBeInTheDocument();
      expect(screen.getByText('文本1')).toBeInTheDocument();
      expect(screen.getByText('段落内容')).toBeInTheDocument();
      expect(screen.getByText('列表项1')).toBeInTheDocument();
      expect(screen.getByText('列表项2')).toBeInTheDocument();
    });

    it('应该处理长文本内容', () => {
      const longText =
        '这是一个非常长的文本内容，用来测试卡片组件是否能够正确处理长文本的显示和布局。'.repeat(10);

      render(
        <Card data-testid='long-text-card'>
          <CardContent>{longText}</CardContent>
        </Card>
      );

      const card = screen.getByTestId('long-text-card');
      expect(card).toBeInTheDocument();
      expect(card).toHaveTextContent(longText);
    });
  });
});
