/**
 * AI配比生成逻辑Hook
 * 处理AI配比生成和应用的业务逻辑
 */

import { useCallback } from 'react';
import { useToast } from '@/shared/hooks/use-toast';
import { inferMaterialType } from '@/core/utils/ratio-type-adapters';
import { MaterialCategory } from '@/core/types/ratio';
import type { UnifiedRatioMaterial, RatioCalculationParams } from '@/core/types/ratio';

interface UseAIRatioLogicProps {
  selectedMaterials: UnifiedRatioMaterial[];
  removeMaterial: (id: string) => void;
  addMaterial: (material: UnifiedRatioMaterial) => void;
  updateCalculationParams: (params: RatioCalculationParams) => void;
  calculateRatio: () => Promise<void>;
}

/**
 * AI配比生成逻辑Hook
 */
export function useAIRatioLogic({
  selectedMaterials,
  removeMaterial,
  addMaterial,
  updateCalculationParams,
  calculateRatio,
}: UseAIRatioLogicProps) {
  const { toast } = useToast();

  /**
   * 处理AI生成的配比结果
   */
  const handleAIGenerate = useCallback(
    async (config: any) => {
      if (!config.generatedRatio) {
        toast({
          title: '生成失败',
          description: 'AI配比生成失败，请重试',
          variant: 'destructive',
        });
        return false;
      }

      const { calculationParams, materials, calculationResults } = config.generatedRatio;

      try {
        // 1. 清空当前材料
        selectedMaterials.forEach(material => {
          removeMaterial(material.id);
        });

        // 2. 应用AI生成的计算参数
        if (calculationParams) {
          updateCalculationParams({
            ...calculationParams,
            exposureClass: calculationParams.exposureClass as any,
          } as RatioCalculationParams);
        }

        // 3. 添加AI生成的材料
        if (materials && materials.length > 0) {
          materials.forEach((material: any) => {
            addMaterial({
              ...material,
              // 确保类型正确
              category: (material.category as MaterialCategory) || inferMaterialType(material.name),
              // 添加缺失的可选属性
              actualValue: material.actualAmount || 0,
              specificGravity: 2.6,
              grade: 'P.O 42.5',
              ratio: 1.0,
              percentage: 0,
            } as UnifiedRatioMaterial);
          });
        }

        // 4. 触发配比计算
        if (calculationParams) {
          await calculateRatio();
        }

        // 5. 显示成功提示
        toast({
          title: 'AI配比应用成功',
          description: `已应用${config.generatedRatio.ratioName}，质量评分：${calculationResults.qualityScore.toFixed(1)}分`,
        });

        return true;
      } catch (error) {
        console.error('应用AI配比失败:', error);
        toast({
          title: '应用失败',
          description: error instanceof Error ? error.message : '应用AI配比时发生未知错误',
          variant: 'destructive',
        });
        return false;
      }
    },
    [selectedMaterials, removeMaterial, addMaterial, updateCalculationParams, calculateRatio, toast]
  );

  /**
   * 处理AI推荐配比
   */
  const handleAIRecommendation = useCallback(
    async (params: any, materials: UnifiedRatioMaterial[]) => {
      try {
        // 应用推荐的配比
        updateCalculationParams(params);

        // 清空现有材料并添加推荐的材料
        selectedMaterials.forEach(material => {
          removeMaterial(material.id);
        });

        materials.forEach(material => {
          addMaterial(material);
        });

        toast({
          title: '配比推荐已应用',
          description: `已应用${params.cementType}配比，强度等级：C${params.targetStrength}`,
        });

        return true;
      } catch (error) {
        console.error('应用AI推荐失败:', error);
        toast({
          title: '应用失败',
          description: error instanceof Error ? error.message : '应用AI推荐时发生错误',
          variant: 'destructive',
        });
        return false;
      }
    },
    [selectedMaterials, removeMaterial, addMaterial, updateCalculationParams, toast]
  );

  /**
   * 验证AI生成的配比数据
   */
  const validateAIRatio = useCallback(
    (config: any): boolean => {
      if (!config.generatedRatio) {
        return false;
      }

      const { calculationParams, materials } = config.generatedRatio;

      // 检查必要的参数
      if (!calculationParams || !calculationParams.targetStrength) {
        toast({
          title: '配比数据无效',
          description: 'AI生成的配比缺少必要的强度参数',
          variant: 'destructive',
        });
        return false;
      }

      // 检查材料数据
      if (!materials || materials.length === 0) {
        toast({
          title: '配比数据无效',
          description: 'AI生成的配比缺少材料信息',
          variant: 'destructive',
        });
        return false;
      }

      return true;
    },
    [toast]
  );

  /**
   * 获取AI配比生成的预览信息
   */
  const getAIRatioPreview = useCallback((config: any) => {
    if (!config.generatedRatio) {
      return null;
    }

    const { calculationParams, materials, calculationResults } = config.generatedRatio;

    return {
      ratioName: config.generatedRatio.ratioName,
      targetStrength: calculationParams?.targetStrength,
      slump: calculationParams?.slump,
      waterCementRatio: calculationParams?.waterCementRatio,
      materialCount: materials?.length || 0,
      qualityScore: calculationResults?.qualityScore,
      estimatedCost: calculationResults?.estimatedCost,
    };
  }, []);

  return {
    handleAIGenerate,
    handleAIRecommendation,
    validateAIRatio,
    getAIRatioPreview,
  };
}
