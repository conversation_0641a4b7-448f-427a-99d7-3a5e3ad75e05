/**
 * 配比类型适配器工具
 * 处理不同配比架构之间的类型转换
 */

import type { UnifiedRatioMaterial, RatioCalculationParams } from '@/core/types/ratio';
import { MaterialCategory } from '@/core/types/ratio';

/**
 * 传统配比计算参数接口（兼容旧版本）
 */
export interface CalculationParams {
  density: number;
  waterRatio: number;
  waterAmount: number;
  sandRatio: number;
  cementAmount: number;
  flyashRatio: number;
  mineralPowderRatio: number;
  silicaFumeRatio: number;
  additiveRatio: number;
  antifreezeRatio: number;
  expansionRatio: number;
  earlyStrengthRatio: number;
  ultraFineSandRatio: number;
  s105Ratio: number;
  slump: number;
  airContent: number;
  strengthGrade: number;
}

/**
 * 材料类型推断函数
 * 根据材料名称推断材料类别
 */
export function inferMaterialType(materialName: string): MaterialCategory {
  const name = materialName.toLowerCase();

  if (name.includes('水泥') || name.includes('cement')) {
    return MaterialCategory.CEMENTITIOUS;
  } else if (name.includes('水') || name.includes('water')) {
    return MaterialCategory.WATER;
  } else if (name.includes('砂') || name.includes('sand')) {
    return MaterialCategory.AGGREGATE;
  } else if (name.includes('石') || name.includes('gravel') || name.includes('碎石')) {
    return MaterialCategory.AGGREGATE;
  } else if (name.includes('外加剂') || name.includes('减水剂') || name.includes('admixture')) {
    return MaterialCategory.ADMIXTURE;
  } else if (name.includes('粉煤灰') || name.includes('矿粉') || name.includes('硅灰')) {
    return MaterialCategory.SUPPLEMENTARY;
  } else {
    return MaterialCategory.SPECIAL_ADDITIVE;
  }
}

/**
 * 将统一架构的参数转换为传统组件期望的格式
 */
export function convertToCalculationParams(
  params: RatioCalculationParams | null
): CalculationParams {
  if (!params) {
    return {
      density: 2400,
      waterRatio: 0.45,
      waterAmount: 180,
      sandRatio: 35,
      cementAmount: 400,
      flyashRatio: 20,
      mineralPowderRatio: 10,
      silicaFumeRatio: 5,
      additiveRatio: 1.2,
      antifreezeRatio: 0,
      expansionRatio: 0,
      earlyStrengthRatio: 0,
      ultraFineSandRatio: 0,
      s105Ratio: 0,
      slump: 180,
      airContent: 4.5,
      strengthGrade: 30,
    };
  }

  return {
    density: params.density || 2400,
    waterRatio: params.waterCementRatio || 0.45,
    waterAmount: params.waterAmount || 180,
    sandRatio: params.sandRatio || 35,
    cementAmount: params.cementAmount || 400,
    flyashRatio: params.flyashRatio || 20,
    mineralPowderRatio: params.mineralPowderRatio || 10,
    silicaFumeRatio: params.silicaFumeRatio || 5,
    additiveRatio: params.additiveRatio || 1.2,
    antifreezeRatio: params.antifreezeRatio || 0,
    expansionRatio: params.expansionRatio || 0,
    earlyStrengthRatio: 0, // 统一架构中没有此字段，使用默认值
    ultraFineSandRatio: 0, // 统一架构中没有此字段，使用默认值
    s105Ratio: 0, // 统一架构中没有此字段，使用默认值
    slump: params.slump || 180,
    airContent: 4.5, // 统一架构中没有此字段，使用默认值
    strengthGrade: params.targetStrength || 30,
  };
}

/**
 * 将任意材料对象转换为统一架构的材料格式
 */
export function convertToUnifiedMaterial(ratioMaterial: any): UnifiedRatioMaterial {
  return {
    ...ratioMaterial,
    category: ratioMaterial.category || inferMaterialType(ratioMaterial.name),
    unit: ratioMaterial.unit || 'kg',
    density: ratioMaterial.density || 2.4,
    siloName: ratioMaterial.siloName || '',
    cost: ratioMaterial.cost || 0,
    supplier: ratioMaterial.supplier || '',
    // 添加缺失的可选属性
    actualValue: ratioMaterial.actualAmount || 0,
    specificGravity: 2.6,
    grade: 'P.O 42.5',
    ratio: 1.0,
    percentage: 0,
  };
}

/**
 * 将拖拽的料仓材料转换为统一架构的配比材料
 */
export function convertSiloMaterialToUnified(material: any): UnifiedRatioMaterial {
  return {
    id: `ratio-${Date.now()}`,
    materialId: material.id,
    name: material.name,
    specification: material.specification,
    category: inferMaterialType(material.name),
    unit: 'kg',
    density: material.density || 2.4,
    theoreticalAmount: 0,
    actualAmount: 0,
    waterContent: 0,
    stoneContent: 0,
    designValue: 0,
    siloId: material.siloId,
    siloName: material.name,
    cost: 0,
    supplier: '',
  };
}
