/**
 * 组件解耦工具
 * 提供具体的组件解耦方案和重构建议
 */

// ==================== 解耦模式类型定义 ====================

export interface DecouplingPattern {
  name: string;
  description: string;
  applicableScenarios: string[];
  benefits: string[];
  implementation: string;
  example: {
    before: string;
    after: string;
  };
}

export interface DecouplingStrategy {
  componentName: string;
  currentIssues: string[];
  recommendedPatterns: DecouplingPattern[];
  implementationPlan: ImplementationStep[];
  estimatedEffort: 'low' | 'medium' | 'high';
  expectedBenefits: string[];
}

export interface ImplementationStep {
  step: number;
  title: string;
  description: string;
  codeChanges: string[];
  dependencies: number[]; // 依赖的步骤
  estimatedTime: string;
}

// ==================== 解耦模式库 ====================

export const DECOUPLING_PATTERNS: Record<string, DecouplingPattern> = {
  dependencyInjection: {
    name: '依赖注入',
    description: '通过Props或Context注入依赖，而不是直接导入',
    applicableScenarios: [
      '组件直接导入服务或工具函数',
      '组件与特定Store紧耦合',
      '组件难以进行单元测试',
    ],
    benefits: ['提高组件的可测试性', '降低组件间的直接依赖', '便于Mock和替换依赖'],
    implementation: '将依赖作为Props传入，或通过Context提供',
    example: {
      before: `// 紧耦合的组件
import { apiService } from '@/services/api';
import { useAppStore } from '@/core/store/appStore';

function TaskCard({ task }) {
  const updateTask = useAppStore(state => state.updateTask);
  
  const handleSave = async () => {
    await apiService.updateTask(task.id, task);
    updateTask(task.id, task);
  };
  
  return <div>...</div>;
}`,
      after: `// 解耦后的组件
interface TaskCardProps {
  task: Task;
  onSave: (task: Task) => Promise<void>;
}

function TaskCard({ task, onSave }: TaskCardProps) {
  
  return <div>...</div>;
}

// 在父组件中注入依赖
function TaskList() {
  const updateTask = useAppStore(state => state.updateTask);
  
  const handleTaskSave = async (task: Task) => {
    await apiService.updateTask(task.id, task);
    updateTask(task.id, task);
  };
  
  return <TaskCard task={task} onSave={handleTaskSave} />;
}`,
    },
  },

  eventDrivenArchitecture: {
    name: '事件驱动架构',
    description: '通过事件系统实现组件间的松耦合通信',
    applicableScenarios: [
      '组件间需要复杂的状态同步',
      '存在多层级的回调传递',
      '组件间的通信关系复杂',
    ],
    benefits: ['减少Props drilling', '组件间解耦', '易于扩展新功能'],
    implementation: '使用EventEmitter或自定义事件系统',
    example: {
      before: `// Props drilling问题
function App() {
  const [data, setData] = useState();
  return <Parent onDataChange={setData} data={data} />;
}

function Parent({ onDataChange, data }) {
  return <Child onDataChange={onDataChange} data={data} />;
}

function Child({ onDataChange, data }) {
  return <GrandChild onDataChange={onDataChange} data={data} />;
}`,
      after: `// 事件驱动解决方案
const eventBus = new EventEmitter();

function App() {
  const [data, setData] = useState();
  
  useEffect(() => {
    eventBus.on('dataChange', setData);
    return () => eventBus.off('dataChange', setData);
  }, []);
  
  return <Parent />;
}

function GrandChild() {
  const handleChange = (newData) => {
    eventBus.emit('dataChange', newData);
  };
  
  return <button onClick={handleChange}>Change</button>;
}`,
    },
  },

  compositionPattern: {
    name: '组合模式',
    description: '将大组件拆分为多个小组件，通过组合使用',
    applicableScenarios: [
      '组件过大，职责不单一',
      '组件有多个独立的功能区域',
      '需要提高组件的复用性',
    ],
    benefits: ['提高代码复用性', '降低组件复杂度', '便于维护和测试'],
    implementation: '拆分组件并通过children或slots组合',
    example: {
      before: `// 大而全的组件
function TaskManager() {
  return (
    <div>
      <div className="header">
        <h1>任务管理</h1>
        <button>新建</button>
        <input placeholder="搜索..." />
      </div>
      <div className="filters">
        <select>...</select>
        <input type="date" />
      </div>
      <div className="list">
        {tasks.map(task => <TaskItem key={task.id} task={task} />)}
      </div>
      <div className="pagination">
        <button>上一页</button>
        <span>1/10</span>
        <button>下一页</button>
      </div>
    </div>
  );
}`,
      after: `// 组合模式重构
function TaskManager() {
  return (
    <TaskManagerContainer>
      <TaskManagerHeader>
        <TaskTitle />
        <TaskActions />
        <TaskSearch />
      </TaskManagerHeader>
      <TaskFilters />
      <TaskList />
      <TaskPagination />
    </TaskManagerContainer>
  );
}

// 每个子组件都有单一职责
function TaskManagerHeader({ children }) {
  return <div className="header">{children}</div>;
}

function TaskTitle() {
  return <h1>任务管理</h1>;
}`,
    },
  },

  stateManagementPattern: {
    name: '状态管理模式',
    description: '使用专门的状态管理方案替代Props传递',
    applicableScenarios: ['多个组件需要共享状态', '状态更新逻辑复杂', '存在深层级的状态传递'],
    benefits: ['集中管理状态', '减少Props传递', '状态更新可预测'],
    implementation: '使用Context、Zustand或Redux等状态管理方案',
    example: {
      before: `// Props传递状态
function App() {
  const [tasks, setTasks] = useState([]);
  const [filter, setFilter] = useState('all');
  
  return (
    <TaskList 
      tasks={tasks} 
      filter={filter}
      onTaskUpdate={setTasks}
      onFilterChange={setFilter}
    />
  );
}`,
      after: `// 使用Zustand管理状态
const useTaskStore = create((set) => ({
  tasks: [],
  filter: 'all',
  setTasks: (tasks) => set({ tasks }),
  setFilter: (filter) => set({ filter }),
}));

function App() {
  return <TaskList />;
}

function TaskList() {
  const { tasks, filter } = useTaskStore();
  // 组件直接从store获取状态，无需Props传递
}`,
    },
  },

  adapterPattern: {
    name: '适配器模式',
    description: '通过适配器层隔离组件与外部依赖',
    applicableScenarios: ['组件直接依赖外部API', '需要支持多种数据源', '外部依赖可能变化'],
    benefits: ['隔离外部变化', '提高组件稳定性', '便于切换实现'],
    implementation: '创建适配器层封装外部依赖',
    example: {
      before: `// 直接依赖外部API
function TaskList() {
  const [tasks, setTasks] = useState([]);
  
  useEffect(() => {
    fetch('/api/tasks')
      .then(res => res.json())
      .then(data => setTasks(data.tasks));
  }, []);
  
  return <div>...</div>;
}`,
      after: `// 使用适配器模式
interface TaskDataSource {
  getTasks(): Promise<Task[]>;
  updateTask(id: string, task: Task): Promise<void>;
}

class ApiTaskDataSource implements TaskDataSource {
  async getTasks() {
    const res = await fetch('/api/tasks');
    const data = await res.json();
    return data.tasks;
  }
  
  async updateTask(id: string, task: Task) {
    await fetch(\`/api/tasks/\${id}\`, {
      method: 'PUT',
      body: JSON.stringify(task),
    });
  }
}

function TaskList({ dataSource }: { dataSource: TaskDataSource }) {
  const [tasks, setTasks] = useState([]);
  
  useEffect(() => {
    dataSource.getTasks().then(setTasks);
  }, [dataSource]);
  
  return <div>...</div>;
}`,
    },
  },
};

// ==================== 组件解耦分析器 ====================

export class ComponentDecoupler {
  /**
   * 分析组件并生成解耦策略
   */
  static analyzeComponent(
    componentName: string,
    dependencies: string[],
    propsCount: number,
    hasDirectImports: boolean,
    hasStateManagement: boolean,
    hasComplexCallbacks: boolean
  ): DecouplingStrategy {
    const issues = this.identifyIssues({
      componentName,
      dependencies,
      propsCount,
      hasDirectImports,
      hasStateManagement,
      hasComplexCallbacks,
    });

    const recommendedPatterns = this.selectPatterns(issues);
    const implementationPlan = this.createImplementationPlan(recommendedPatterns);
    const estimatedEffort = this.estimateEffort(recommendedPatterns);
    const expectedBenefits = this.calculateBenefits(recommendedPatterns);

    return {
      componentName,
      currentIssues: issues,
      recommendedPatterns,
      implementationPlan,
      estimatedEffort,
      expectedBenefits,
    };
  }

  /**
   * 识别组件问题
   */
  private static identifyIssues(componentInfo: any): string[] {
    const issues: string[] = [];

    if (componentInfo.propsCount > 10) {
      issues.push('Props数量过多，组件接口复杂');
    }

    if (componentInfo.hasDirectImports) {
      issues.push('直接导入外部依赖，耦合度高');
    }

    if (componentInfo.hasStateManagement) {
      issues.push('组件内部管理复杂状态');
    }

    if (componentInfo.hasComplexCallbacks) {
      issues.push('回调函数过多，Props drilling严重');
    }

    if (componentInfo.dependencies.length > 5) {
      issues.push('依赖关系复杂，难以测试');
    }

    return issues;
  }

  /**
   * 选择适用的解耦模式
   */
  private static selectPatterns(issues: string[]): DecouplingPattern[] {
    const patterns: DecouplingPattern[] = [];

    if (issues.some(issue => issue.includes('直接导入') || issue.includes('难以测试'))) {
      if (DECOUPLING_PATTERNS['dependencyInjection']) {
        patterns.push(DECOUPLING_PATTERNS['dependencyInjection']);
      }
    }

    if (issues.some(issue => issue.includes('Props drilling') || issue.includes('回调函数过多'))) {
      if (DECOUPLING_PATTERNS['eventDrivenArchitecture']) {
        patterns.push(DECOUPLING_PATTERNS['eventDrivenArchitecture']);
      }
    }

    if (issues.some(issue => issue.includes('Props数量过多'))) {
      if (DECOUPLING_PATTERNS['compositionPattern']) {
        patterns.push(DECOUPLING_PATTERNS['compositionPattern']);
      }
    }

    if (issues.some(issue => issue.includes('复杂状态'))) {
      if (DECOUPLING_PATTERNS['stateManagementPattern']) {
        patterns.push(DECOUPLING_PATTERNS['stateManagementPattern']);
      }
    }

    if (issues.some(issue => issue.includes('外部依赖'))) {
      if (DECOUPLING_PATTERNS['adapterPattern']) {
        patterns.push(DECOUPLING_PATTERNS['adapterPattern']);
      }
    }

    return patterns;
  }

  /**
   * 创建实施计划
   */
  private static createImplementationPlan(patterns: DecouplingPattern[]): ImplementationStep[] {
    const steps: ImplementationStep[] = [];
    let stepNumber = 1;

    patterns.forEach(pattern => {
      switch (pattern.name) {
        case '依赖注入':
          steps.push({
            step: stepNumber++,
            title: '提取依赖接口',
            description: '定义组件所需的依赖接口',
            codeChanges: ['创建接口定义', '修改组件Props'],
            dependencies: [],
            estimatedTime: '2-4小时',
          });
          steps.push({
            step: stepNumber++,
            title: '重构组件',
            description: '移除直接导入，通过Props接收依赖',
            codeChanges: ['删除import语句', '修改组件实现'],
            dependencies: [stepNumber - 2],
            estimatedTime: '4-6小时',
          });
          break;

        case '组合模式':
          steps.push({
            step: stepNumber++,
            title: '拆分组件',
            description: '将大组件拆分为多个小组件',
            codeChanges: ['创建子组件', '提取独立功能'],
            dependencies: [],
            estimatedTime: '6-8小时',
          });
          steps.push({
            step: stepNumber++,
            title: '重构父组件',
            description: '使用组合模式重构父组件',
            codeChanges: ['修改组件结构', '调整Props传递'],
            dependencies: [stepNumber - 2],
            estimatedTime: '2-4小时',
          });
          break;

        // 其他模式的实施步骤...
      }
    });

    return steps;
  }

  /**
   * 估算工作量
   */
  private static estimateEffort(patterns: DecouplingPattern[]): 'low' | 'medium' | 'high' {
    if (patterns.length <= 1) return 'low';
    if (patterns.length <= 2) return 'medium';
    return 'high';
  }

  /**
   * 计算预期收益
   */
  private static calculateBenefits(patterns: DecouplingPattern[]): string[] {
    const benefits = new Set<string>();

    patterns.forEach(pattern => {
      pattern.benefits.forEach(benefit => benefits.add(benefit));
    });

    return Array.from(benefits);
  }

  /**
   * 生成解耦报告
   */
  static generateDecouplingReport(strategies: DecouplingStrategy[]): string {
    const report = [
      '# 组件解耦分析报告',
      '',
      '## 概述',
      `分析了 ${strategies.length} 个组件的解耦需求`,
      '',
    ];

    strategies.forEach(strategy => {
      report.push(`## ${strategy.componentName}`);
      report.push('');
      report.push('### 当前问题');
      strategy.currentIssues.forEach(issue => {
        report.push(`- ${issue}`);
      });
      report.push('');

      report.push('### 推荐解耦模式');
      strategy.recommendedPatterns.forEach(pattern => {
        report.push(`#### ${pattern.name}`);
        report.push(pattern.description);
        report.push('**适用场景**:');
        pattern.applicableScenarios.forEach(scenario => {
          report.push(`- ${scenario}`);
        });
        report.push('');
      });

      report.push(`### 预期收益`);
      strategy.expectedBenefits.forEach(benefit => {
        report.push(`- ${benefit}`);
      });
      report.push('');

      report.push(`### 工作量评估: ${strategy.estimatedEffort.toUpperCase()}`);
      report.push('');
    });

    return report.join('\n');
  }
}
