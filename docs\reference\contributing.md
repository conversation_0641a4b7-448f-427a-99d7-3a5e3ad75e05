# 🤝 贡献指南

## 🎯 贡献方式

我们欢迎各种形式的贡献，包括但不限于：

- 🐛 **Bug 报告**: 发现并报告问题
- ✨ **功能建议**: 提出新功能想法
- 💻 **代码贡献**: 修复 Bug 或实现新功能
- 📚 **文档改进**: 完善或更新文档
- 🧪 **测试用例**: 添加或改进测试
- 🎨 **UI/UX 改进**: 界面和体验优化

## 🚀 快速开始

### 1. Fork 项目
```bash
# 1. Fork 项目到你的 GitHub 账户
# 2. 克隆你的 Fork
git clone https://github.com/your-username/tmh-task-dispatcher.git
cd tmh-task-dispatcher

# 3. 添加上游仓库
git remote add upstream https://github.com/original-org/tmh-task-dispatcher.git
```

### 2. 设置开发环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm run test
```

### 3. 创建功能分支
```bash
# 从 main 分支创建新分支
git checkout -b feature/your-feature-name

# 或修复 Bug
git checkout -b fix/bug-description
```

## 📝 开发流程

### 分支命名规范
- **功能开发**: `feature/功能描述`
- **Bug 修复**: `fix/问题描述`
- **文档更新**: `docs/文档类型`
- **性能优化**: `perf/优化描述`
- **重构代码**: `refactor/重构描述`

```bash
# 示例
feature/task-batch-operations
fix/vehicle-dispatch-error
docs/api-documentation
perf/table-virtualization
refactor/state-management
```

### 提交信息规范
使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
# 格式
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### 提交类型
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例
```bash
feat(task-list): 添加批量操作功能

- 支持多选任务
- 添加批量删除功能
- 添加批量状态更新

Closes #123

fix(vehicle-dispatch): 修复拖拽调度时的状态同步问题

当车辆拖拽到任务上时，车辆状态没有正确更新。
现在确保状态同步更新。

Fixes #456

docs(api): 更新任务管理 API 文档

- 添加批量操作接口说明
- 更新错误码说明
- 添加使用示例
```

## 🔍 代码审查

### 提交 Pull Request
1. **确保代码质量**
   ```bash
   # 运行所有检查
   npm run lint
   npm run typecheck
   npm run test
   npm run build
   ```

2. **创建 Pull Request**
   - 使用清晰的标题和描述
   - 关联相关的 Issue
   - 添加截图或 GIF（如果是 UI 变更）
   - 列出测试步骤

3. **PR 模板**
   ```markdown
   ## 变更描述
   简要描述这个 PR 的变更内容

   ## 变更类型
   - [ ] Bug 修复
   - [ ] 新功能
   - [ ] 文档更新
   - [ ] 性能优化
   - [ ] 代码重构

   ## 测试
   - [ ] 单元测试通过
   - [ ] 集成测试通过
   - [ ] 手动测试完成

   ## 截图
   如果是 UI 变更，请添加截图

   ## 关联 Issue
   Closes #123
   ```

### 代码审查清单
#### 功能性
- [ ] 功能按预期工作
- [ ] 边界情况处理正确
- [ ] 错误处理完善
- [ ] 性能影响可接受

#### 代码质量
- [ ] 代码清晰易读
- [ ] 遵循项目规范
- [ ] 没有重复代码
- [ ] 适当的注释

#### 测试
- [ ] 有相应的测试用例
- [ ] 测试覆盖率足够
- [ ] 测试用例有意义

#### 文档
- [ ] 更新了相关文档
- [ ] API 变更有文档说明
- [ ] README 需要更新时已更新

## 🐛 Bug 报告

### Bug 报告模板
```markdown
## Bug 描述
清晰简洁地描述 Bug

## 复现步骤
1. 进入 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## 预期行为
清晰简洁地描述你期望发生什么

## 实际行为
清晰简洁地描述实际发生了什么

## 截图
如果适用，添加截图来帮助解释你的问题

## 环境信息
- 操作系统: [例如 iOS]
- 浏览器: [例如 chrome, safari]
- 版本: [例如 22]
- 设备: [例如 iPhone6]

## 附加信息
添加任何其他关于问题的信息
```

### Bug 优先级
- **P0 - 紧急**: 系统崩溃、数据丢失
- **P1 - 高**: 核心功能不可用
- **P2 - 中**: 功能异常但有替代方案
- **P3 - 低**: 小问题、优化建议

## ✨ 功能请求

### 功能请求模板
```markdown
## 功能描述
清晰简洁地描述你想要的功能

## 问题描述
这个功能要解决什么问题？

## 解决方案
描述你希望的解决方案

## 替代方案
描述你考虑过的其他替代方案

## 附加信息
添加任何其他关于功能请求的信息、截图等
```

## 📚 文档贡献

### 文档类型
- **API 文档**: 接口说明和示例
- **用户指南**: 功能使用说明
- **开发文档**: 技术实现细节
- **教程**: 分步骤的学习指南

### 文档规范
- 使用清晰的标题结构
- 提供代码示例
- 添加截图或图表
- 保持内容更新

### 文档审查
- [ ] 内容准确无误
- [ ] 格式规范统一
- [ ] 链接有效
- [ ] 示例代码可运行

## 🧪 测试贡献

### 测试类型
- **单元测试**: 测试单个函数或组件
- **集成测试**: 测试组件间交互
- **E2E 测试**: 测试完整用户流程

### 测试规范
```typescript
// 测试文件命名
ComponentName.test.tsx
utilFunction.test.ts

// 测试结构
describe('ComponentName', () => {
  beforeEach(() => {
    // 测试前准备
  });

  it('should render correctly', () => {
    // 测试用例
  });

  it('should handle user interaction', () => {
    // 交互测试
  });
});
```

## 🎨 UI/UX 贡献

### 设计原则
- **一致性**: 遵循设计系统
- **可访问性**: 支持无障碍访问
- **响应式**: 适配不同设备
- **性能**: 优化用户体验

### 设计资源
- **设计系统**: 查看 `docs/ui-ux/design-system.md`
- **组件库**: 使用 shadcn/ui 组件
- **图标**: 使用 Lucide React 图标
- **颜色**: 遵循主题色彩规范

## 🏆 贡献者认可

### 贡献者列表
我们会在以下地方认可贡献者：
- README.md 贡献者部分
- 项目发布说明
- 贡献者页面

### 贡献统计
- 代码提交数量
- 问题解决数量
- 文档贡献数量
- 社区帮助次数

## 📞 获取帮助

### 沟通渠道
- **GitHub Issues**: 技术问题讨论
- **GitHub Discussions**: 功能讨论和想法交流
- **Email**: <EMAIL>
- **内部 Slack**: #tmh-development

### 开发者资源
- **技术文档**: `docs/` 目录
- **代码示例**: `examples/` 目录
- **开发工具**: 推荐的 VS Code 扩展
- **学习资源**: 相关技术文档链接

## 📋 发布流程

### 版本管理
- 使用 [Semantic Versioning](https://semver.org/)
- 主版本号：不兼容的 API 修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 发布清单
- [ ] 更新版本号
- [ ] 更新 CHANGELOG.md
- [ ] 运行完整测试套件
- [ ] 构建生产版本
- [ ] 创建 Git 标签
- [ ] 发布到生产环境

## 🙏 致谢

感谢所有为 TMH 车辆调度系统做出贡献的开发者！

### 核心贡献者
- [@developer1](https://github.com/developer1) - 项目架构师
- [@developer2](https://github.com/developer2) - 前端负责人
- [@developer3](https://github.com/developer3) - UI/UX 设计师

### 特别感谢
感谢所有提交 Issue、PR 和参与讨论的社区成员！

---

**文档版本**: v1.0  
**最后更新**: 2025/7/16  
**维护者**: TMH 开源团队
