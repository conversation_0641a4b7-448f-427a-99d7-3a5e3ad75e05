import { useState, useCallback, useMemo } from 'react';
import type { TaskGroupConfig } from '@/core/types';

const initialGroupConfig: TaskGroupConfig = {
  groupBy: 'none',
  enabled: false,
  collapsible: true,
  defaultCollapsed: [],
  sortOrder: 'asc',
  showGroupStats: true,
  allowedGroupColumns: ['strength', 'constructionUnit', 'projectName'],
  disallowedGroupColumns: [],
  groupHeaderStyle: {
    backgroundColor: 'bg-gray-100',
    textColor: 'text-gray-900',
    fontSize: 'text-sm',
    fontWeight: 'font-medium',
    padding: 'py-2',
  },
};

export function useTaskListGrouping() {
  const [groupConfig, setGroupConfig] = useState<TaskGroupConfig>(initialGroupConfig);
  const [updateCount, setUpdateCount] = useState(0);

  const updateGroupConfig = useCallback((newConfig: Partial<TaskGroupConfig>) => {
    console.log('🔍 useTaskListGrouping updateGroupConfig:', newConfig);
    setGroupConfig(prev => ({
      ...prev,
      ...newConfig,
    }));
    setUpdateCount(prev => prev + 1);
  }, []);

  const confirmGroupByColumnSelect = useCallback(
    (columnId: string) => {
      console.log('🔍 useTaskListGrouping confirmGroupByColumnSelect:', columnId);
      updateGroupConfig({
        enabled: true,
        groupBy: columnId as any,
        defaultCollapsed: [],
      });
    },
    [updateGroupConfig]
  );

  const handleSetGroupBy = useCallback(
    (groupBy: TaskGroupConfig['groupBy']) => {
      console.log('🔍 useTaskListGrouping handleSetGroupBy:', groupBy);
      updateGroupConfig({
        groupBy,
        enabled: groupBy !== 'none',
        defaultCollapsed: [],
      });
    },
    [updateGroupConfig]
  );

  const handleToggleGrouping = useCallback(() => {
    console.log('🔍 useTaskListGrouping handleToggleGrouping');
    updateGroupConfig({
      enabled: !groupConfig.enabled,
    });
  }, [groupConfig.enabled, updateGroupConfig]);

  const handleToggleGroupCollapse = useCallback(
    (groupKey: string) => {
      console.log('🔍 useTaskListGrouping handleToggleGroupCollapse:', groupKey);
      const currentCollapsed = groupConfig.defaultCollapsed || [];
      const isCollapsed = currentCollapsed.includes(groupKey);

      updateGroupConfig({
        defaultCollapsed: isCollapsed
          ? currentCollapsed.filter(key => key !== groupKey)
          : [...currentCollapsed, groupKey],
      });
    },
    [groupConfig.defaultCollapsed, updateGroupConfig]
  );

  const confirmGroupByColumn = useCallback(() => {
    // 这个方法需要额外的状态来跟踪选择的列，暂时简化
    console.log('🔍 useTaskListGrouping confirmGroupByColumn - 需要实现');
  }, []);

  // 为了兼容性，返回与 useTaskListSettings 相同的接口
  const settings = useMemo(
    () => ({
      groupConfig,
      // 其他设置字段可以使用默认值或从其他地方获取
      displayMode: 'card' as const,
      columnOrder: [],
      columnWidths: {},
      columnTextStyles: {},
      columnBackgrounds: {},
      inTaskVehicleCardStyles: {},
      // ... 其他字段
    }),
    [groupConfig]
  );

  return {
    settings,
    groupConfig,
    updateCount,
    updateGroupConfig,
    confirmGroupByColumnSelect,
    handleSetGroupBy,
    handleToggleGrouping,
    handleToggleGroupCollapse,
    confirmGroupByColumn,
    // 其他兼容性方法
    isSettingsLoaded: true,
  };
}
