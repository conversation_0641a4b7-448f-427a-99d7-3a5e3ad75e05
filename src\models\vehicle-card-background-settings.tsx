// src/components/modals/vehicle-card-background-settings.tsx
'use client';

import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { cn } from '@/core/lib/utils';
import type { InTaskVehicleCardStyle } from '@/core/types';

// src/components/modals/vehicle-card-background-settings.tsx

interface VehicleCardBackgroundSettingsProps {
  currentStyles: InTaskVehicleCardStyle;
  onStylesChangeAction: (styles: InTaskVehicleCardStyle) => void;
}

// 单色背景选项
const SOLID_COLOR_OPTIONS = [
  { value: 'bg-card/80', label: '默认卡片色', className: 'bg-card/80' },
  {
    value: 'bg-blue-50 dark:bg-blue-900/20',
    label: '浅蓝色',
    className: 'bg-blue-50 dark:bg-blue-900/20',
  },
  {
    value: 'bg-green-50 dark:bg-green-900/20',
    label: '浅绿色',
    className: 'bg-green-50 dark:bg-green-900/20',
  },
  {
    value: 'bg-yellow-50 dark:bg-yellow-900/20',
    label: '浅黄色',
    className: 'bg-yellow-50 dark:bg-yellow-900/20',
  },
  {
    value: 'bg-purple-50 dark:bg-purple-900/20',
    label: '浅紫色',
    className: 'bg-purple-50 dark:bg-purple-900/20',
  },
  {
    value: 'bg-pink-50 dark:bg-pink-900/20',
    label: '浅粉色',
    className: 'bg-pink-50 dark:bg-pink-900/20',
  },
  {
    value: 'bg-gray-50 dark:bg-gray-900/20',
    label: '浅灰色',
    className: 'bg-gray-50 dark:bg-gray-900/20',
  },
];

// 获取车卡背景样式
export const getVehicleCardBackgroundStyle = (
  styles: InTaskVehicleCardStyle
): { className: string; style?: React.CSSProperties } => {
  // 仅使用单色背景
  return { className: styles.cardBgColor || 'bg-card/80' };
};

export function VehicleCardBackgroundSettings({
  currentStyles,
  onStylesChangeAction,
}: VehicleCardBackgroundSettingsProps) {
  const handleStyleChange = <K extends keyof InTaskVehicleCardStyle>(
    key: K,
    value: InTaskVehicleCardStyle[K]
  ) => {
    console.log('🎨 VehicleCardBackgroundSettings handleStyleChange:', key, value);
    const newStyles = { ...currentStyles, [key]: value };
    console.log('🎨 新的样式对象:', newStyles);
    onStylesChangeAction(newStyles);
  };

  // 获取当前背景样式用于预览

  return (
    <div className='space-y-4'>
      {/* 单色背景选择 */}
      <div className='space-y-2'>
        <Label className='text-sm font-medium'>卡片背景色</Label>
        <Select
          value={currentStyles.cardBgColor || 'bg-card/80'}
          onValueChange={value => {
            handleStyleChange('cardBgColor', value);
          }}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {SOLID_COLOR_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                <div className='flex items-center space-x-2'>
                  <div className={cn('w-4 h-4 rounded border', option.className)} />
                  <span>{option.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
