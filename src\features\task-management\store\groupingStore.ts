import { create } from 'zustand';
import type { TaskGroupConfig } from '@/core/types';

interface GroupingState {
  groupConfig: TaskGroupConfig;
  updateCount: number;
  setGroupBy: (groupBy: TaskGroupConfig['groupBy']) => void;
  updateGroupConfig: (config: Partial<TaskGroupConfig>) => void;
  toggleGrouping: () => void;
  toggleCollapse: (groupKey: string) => void;
  reset: () => void;
}

const initialGroupConfig: TaskGroupConfig = {
  groupBy: 'none',
  enabled: false,
  collapsible: true,
  defaultCollapsed: [],
  sortOrder: 'asc',
  showGroupStats: true,
  allowedGroupColumns: ['strength', 'constructionUnit', 'projectName'],
  disallowedGroupColumns: [],
  groupHeaderStyle: {
    backgroundColor: 'bg-gray-100',
    textColor: 'text-gray-900',
    fontSize: 'text-sm',
    fontWeight: 'font-medium',
    padding: 'py-2',
  },
};

export const useGroupingStore = create<GroupingState>((set, get) => ({
  groupConfig: initialGroupConfig,
  updateCount: 0,

  setGroupBy: groupBy => {
    console.log('🔍 Zustand setGroupBy:', groupBy);
    set(state => ({
      groupConfig: {
        ...state.groupConfig,
        groupBy,
        enabled: groupBy !== 'none',
        defaultCollapsed: [], // 清空折叠状态
      },
      updateCount: state.updateCount + 1,
    }));
  },

  updateGroupConfig: config => {
    console.log('🔍 Zustand updateGroupConfig:', config);
    set(state => ({
      groupConfig: {
        ...state.groupConfig,
        ...config,
      },
      updateCount: state.updateCount + 1,
    }));
  },

  toggleGrouping: () => {
    set(state => ({
      groupConfig: {
        ...state.groupConfig,
        enabled: !state.groupConfig.enabled,
      },
      updateCount: state.updateCount + 1,
    }));
  },

  toggleCollapse: groupKey => {
    set(state => {
      const currentCollapsed = state.groupConfig.defaultCollapsed || [];
      const isCollapsed = currentCollapsed.includes(groupKey);

      return {
        groupConfig: {
          ...state.groupConfig,
          defaultCollapsed: isCollapsed
            ? currentCollapsed.filter(key => key !== groupKey)
            : [...currentCollapsed, groupKey],
        },
        updateCount: state.updateCount + 1,
      };
    });
  },

  reset: () => {
    set({
      groupConfig: initialGroupConfig,
      updateCount: 0,
    });
  },
}));
