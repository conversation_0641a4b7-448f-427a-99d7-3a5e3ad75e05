/**
 * 任务列表头部模式管理Hook
 * 处理固定头部和悬浮头部之间的切换逻辑
 */

import { useState, useCallback } from 'react';

interface UseTaskListHeaderModeProps {
  /** 是否使用悬浮头部 */
  useFloatingHeader?: boolean;
  /** 悬浮头部初始位置 */
  floatingHeaderPosition?: { x: number; y: number };
  /** 悬浮头部位置变化回调 */
  onFloatingHeaderPositionChange?: (position: { x: number; y: number }) => void;
}

/**
 * 任务列表头部模式管理Hook
 */
export function useTaskListHeaderMode({
  useFloatingHeader = false,
  floatingHeaderPosition,
  onFloatingHeaderPositionChange,
}: UseTaskListHeaderModeProps = {}) {
  // 头部模式状态管理
  const [isFloatingHeader, setIsFloatingHeader] = useState(useFloatingHeader);
  const [floatingPosition, setFloatingPosition] = useState(
    floatingHeaderPosition || { x: 20, y: 100 }
  );

  // 切换到悬浮头部模式
  const handleSwitchToFloatingHeader = useCallback(() => {
    setIsFloatingHeader(true);
  }, []);

  // 切换到固定头部模式
  const handleSwitchToFixedHeader = useCallback(() => {
    setIsFloatingHeader(false);
  }, []);

  // 悬浮头部位置变化处理
  const handleFloatingPositionChange = useCallback(
    (position: { x: number; y: number }) => {
      setFloatingPosition(position);
      onFloatingHeaderPositionChange?.(position);
    },
    [onFloatingHeaderPositionChange]
  );

  // 切换头部模式
  const toggleHeaderMode = useCallback(() => {
    setIsFloatingHeader(prev => !prev);
  }, []);

  return {
    // 状态
    isFloatingHeader,
    floatingPosition,

    // 操作函数
    handleSwitchToFloatingHeader,
    handleSwitchToFixedHeader,
    handleFloatingPositionChange,
    toggleHeaderMode,

    // 设置函数
    setIsFloatingHeader,
    setFloatingPosition,
  };
}
