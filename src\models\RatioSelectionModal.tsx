'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { Search, CheckCircle, Filter, Eye, Clock, User } from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import { ScrollArea } from '@/shared/components/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/table';
import { cn } from '@/core/lib/utils';
import type { RatioSelectionRecord } from '@/core/types/ratio';

// 日期时间格式化函数
function formatDateTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}`;
  } catch {
    return dateString;
  }
}

interface RatioSelectionModalProps {
  isOpen?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onOpenChangeAction?: (open: boolean) => void;
  onSelectRatio?: (ratio: RatioSelectionRecord) => void;
  ratioRecords?: RatioSelectionRecord[];
  currentTaskId?: string;
}

export const RatioSelectionModal: React.FC<RatioSelectionModalProps> = ({
  isOpen = false,
  open = false,
  onOpenChange,
  onOpenChangeAction,
  onSelectRatio,
  ratioRecords = [],
  currentTaskId,
}) => {
  const [selectedRatioId, setSelectedRatioId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [strengthFilter, setStrengthFilter] = useState<string>('all');

  // 统一处理open状态和回调
  const modalOpen = isOpen || open;
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange?.(newOpen);
    onOpenChangeAction?.(newOpen);
  };

  // 当模态框关闭时清除选择状态
  useEffect(() => {
    if (!modalOpen) {
      setSelectedRatioId(null);
      setSearchTerm('');
      setStrengthFilter('all');
    }
  }, [modalOpen]);

  // 获取所有强度等级用于筛选
  const strengthGrades = useMemo(() => {
    const grades = Array.from(new Set(ratioRecords.map(record => record.strengthGrade)));
    return grades.sort();
  }, [ratioRecords]);

  // 筛选配比记录
  const filteredRecords = useMemo(() => {
    return ratioRecords.filter(record => {
      const matchesSearch =
        record.ratioNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.creator.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStrength = strengthFilter === 'all' || record.strengthGrade === strengthFilter;

      return matchesSearch && matchesStrength && record.status === 'active';
    });
  }, [ratioRecords, searchTerm, strengthFilter]);

  const selectedRatio = selectedRatioId
    ? ratioRecords.find(record => record.id === selectedRatioId)
    : null;

  const handleApplyRatio = () => {
    if (selectedRatio) {
      onSelectRatio?.(selectedRatio);
      handleOpenChange(false);
      setSelectedRatioId(null);
    }
  };

  const handleRowClick = (ratioId: string) => {
    setSelectedRatioId(ratioId === selectedRatioId ? null : ratioId);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant='default' className='text-xs'>
            启用
          </Badge>
        );
      case 'archived':
        return (
          <Badge variant='secondary' className='text-xs'>
            归档
          </Badge>
        );
      case 'draft':
        return (
          <Badge variant='outline' className='text-xs'>
            草稿
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
      <DialogContent className='max-w-7xl max-h-[90vh] flex flex-col'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <CheckCircle className='h-5 w-5 text-green-600' />
            选用配比
          </DialogTitle>
          <DialogDescription>从已有配比库中选择合适的配比方案应用到当前任务</DialogDescription>
        </DialogHeader>

        {/* 搜索和筛选区域 */}
        <div className='flex items-center gap-2 p-2 border-b'>
          <div className='flex-1 relative'>
            <Search className='absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='搜索配比编号、描述或创建人...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='pl-8 h-8 text-sm'
            />
          </div>
          <Select value={strengthFilter} onValueChange={setStrengthFilter}>
            <SelectTrigger className='w-32 h-8 text-sm'>
              <Filter className='h-3 w-3 mr-1' />
              <SelectValue placeholder='强度等级' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>全部强度</SelectItem>
              {strengthGrades.map(grade => (
                <SelectItem key={grade} value={grade}>
                  {grade}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Badge variant='outline' className='text-xs'>
            共 {filteredRecords.length} 条记录
          </Badge>
        </div>

        {/* 配比列表 */}
        <ScrollArea className='flex-1'>
          <Table>
            <TableHeader className='sticky top-0 bg-background'>
              <TableRow>
                <TableHead className='w-[100px]'>配比编号</TableHead>
                <TableHead className='w-[80px]'>强度等级</TableHead>
                <TableHead className='w-[80px]'>坍落度</TableHead>
                <TableHead className='w-[80px]'>水泥</TableHead>
                <TableHead className='w-[80px]'>砂</TableHead>
                <TableHead className='w-[80px]'>石子</TableHead>
                <TableHead className='w-[80px]'>水</TableHead>
                <TableHead className='w-[80px]'>外加剂</TableHead>
                <TableHead className='w-[80px]'>粉煤灰</TableHead>
                <TableHead className='w-[80px]'>矿粉</TableHead>
                <TableHead className='w-[80px]'>水胶比</TableHead>
                <TableHead className='w-[80px]'>砂率</TableHead>
                <TableHead className='w-[100px]'>创建人</TableHead>
                <TableHead className='w-[120px]'>创建时间</TableHead>
                <TableHead className='w-[80px]'>使用次数</TableHead>
                <TableHead className='w-[80px]'>状态</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.map(record => (
                <TableRow
                  key={record.id}
                  className={cn(
                    'cursor-pointer hover:bg-muted/50 transition-colors',
                    selectedRatioId === record.id && 'bg-blue-50 border-blue-200'
                  )}
                  onClick={() => handleRowClick(record.id)}
                >
                  <TableCell className='font-medium'>{record.ratioNumber}</TableCell>
                  <TableCell>
                    <Badge variant='secondary' className='text-xs'>
                      {record.strengthGrade}
                    </Badge>
                  </TableCell>
                  <TableCell>{record.slump}mm</TableCell>
                  <TableCell>{record.materials.cement}</TableCell>
                  <TableCell>{record.materials.sand}</TableCell>
                  <TableCell>{record.materials.stone}</TableCell>
                  <TableCell>{record.materials.water}</TableCell>
                  <TableCell>{record.materials.admixture}</TableCell>
                  <TableCell>{record.materials.flyAsh || '-'}</TableCell>
                  <TableCell>{record.materials.mineralPowder || '-'}</TableCell>
                  <TableCell>{record.parameters.waterRatio.toFixed(2)}</TableCell>
                  <TableCell>{record.parameters.sandRatio}%</TableCell>
                  <TableCell className='flex items-center gap-1'>
                    <User className='h-3 w-3 text-muted-foreground' />
                    {record.creator}
                  </TableCell>
                  <TableCell className='flex items-center gap-1'>
                    <Clock className='h-3 w-3 text-muted-foreground' />
                    {formatDateTime(record.createTime)}
                  </TableCell>
                  <TableCell>{record.usageCount}</TableCell>
                  <TableCell>{getStatusBadge(record.status)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>

        {/* 选中配比详情和操作按钮 */}
        {selectedRatio && (
          <div className='border-t p-4 bg-muted/20'>
            <div className='flex items-start justify-between'>
              <div className='flex-1'>
                <h4 className='font-medium text-sm mb-2'>选中配比详情</h4>
                <div className='grid grid-cols-2 gap-4 text-xs text-muted-foreground'>
                  <div>
                    <span className='font-medium'>配比编号:</span> {selectedRatio.ratioNumber}
                  </div>
                  <div>
                    <span className='font-medium'>强度等级:</span> {selectedRatio.strengthGrade}
                  </div>
                  <div>
                    <span className='font-medium'>描述:</span> {selectedRatio.description}
                  </div>
                  <div>
                    <span className='font-medium'>标签:</span>{' '}
                    {selectedRatio.tags?.join(', ') || '无'}
                  </div>
                </div>
              </div>
              <div className='flex items-center gap-2'>
                <Button variant='outline' size='sm' className='text-xs h-7'>
                  <Eye className='h-3 w-3 mr-1' />
                  预览
                </Button>
                <Button
                  onClick={handleApplyRatio}
                  size='sm'
                  className='text-xs h-7 bg-green-600 hover:bg-green-700'
                >
                  <CheckCircle className='h-3 w-3 mr-1' />
                  应用此配比
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 底部操作按钮 */}
        <div className='flex items-center justify-between pt-2 border-t'>
          <div className='text-xs text-muted-foreground'>
            {selectedRatio ? `已选择: ${selectedRatio.ratioNumber}` : '请选择一个配比记录'}
          </div>
          <div className='flex items-center gap-2'>
            <Button variant='outline' size='sm' onClick={() => handleOpenChange(false)}>
              取消
            </Button>
            <Button
              onClick={handleApplyRatio}
              disabled={!selectedRatio}
              size='sm'
              className='bg-green-600 hover:bg-green-700'
            >
              应用选中配比
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RatioSelectionModal;
