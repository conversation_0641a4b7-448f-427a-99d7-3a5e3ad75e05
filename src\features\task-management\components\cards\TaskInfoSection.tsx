'use client';

import React from 'react';

import {
  Building2,
  Calendar,
  Clock,
  FileText,
  MapPin,
  Phone,
  Truck,
  Users,
  Zap,
} from 'lucide-react';

import { cn } from '@/core/lib/utils';
import type { Task } from '@/core/types';

// 日期时间格式化函数
function formatDateTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}`;
  } catch {
    return dateString;
  }
}

interface TaskInfoSectionProps {
  task: Task;
  layout?: 'compact' | 'standard' | 'detailed' | 'minimal';
  size?: 'small' | 'medium' | 'large' | 'extra-large';
}

export const TaskInfoSection: React.FC<TaskInfoSectionProps> = ({
  task,
  layout = 'standard',
  size = 'medium',
}) => {
  // 获取尺寸样式
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          text: 'text-xs',
          icon: 'w-3 h-3',
          gap: 'gap-1',
          spacing: 'space-y-1',
        };
      case 'large':
        return {
          text: 'text-base',
          icon: 'w-5 h-5',
          gap: 'gap-3',
          spacing: 'space-y-3',
        };
      case 'extra-large':
        return {
          text: 'text-lg',
          icon: 'w-6 h-6',
          gap: 'gap-4',
          spacing: 'space-y-4',
        };
      default: // medium
        return {
          text: 'text-sm',
          icon: 'w-4 h-4',
          gap: 'gap-2',
          spacing: 'space-y-2',
        };
    }
  };

  const styles = getSizeStyles();

  // 信息项组件
  const InfoItem: React.FC<{
    icon: React.ReactNode;
    label: string;
    value: string | number;
    highlight?: boolean;
  }> = ({ icon, label, value, highlight = false }) => (
    <div
      className={cn(
        'flex items-center',
        styles.gap,
        highlight && 'bg-orange-50 rounded-md px-2 py-1 border border-orange-200 shadow-sm'
      )}
    >
      <div className={cn('text-muted-foreground flex-shrink-0', highlight && 'text-orange-600')}>
        {icon}
      </div>
      <div className='min-w-0 flex-1'>
        <div
          className={cn(
            'text-muted-foreground',
            styles.text,
            'text-[10px] uppercase tracking-wide',
            highlight && 'text-orange-600 font-medium'
          )}
        >
          {label}
        </div>
        <div
          className={cn(
            'font-medium truncate',
            styles.text,
            highlight && 'text-orange-700 font-bold text-sm'
          )}
        >
          {value}
        </div>
      </div>
    </div>
  );

  // 最小布局
  if (layout === 'minimal') {
    return (
      <div className={cn('grid grid-cols-2', styles.gap)}>
        <InfoItem
          icon={<Zap className={styles.icon} />}
          label='强度'
          value={task.strength}
          highlight
        />
        <InfoItem
          icon={<MapPin className={styles.icon} />}
          label='位置'
          value={task.constructionLocation || '未指定'}
        />
      </div>
    );
  }

  // 紧凑布局
  if (layout === 'compact') {
    return (
      <div className={cn('grid grid-cols-2', styles.gap, styles.spacing)}>
        <InfoItem
          icon={<MapPin className={styles.icon} />}
          label='施工位置'
          value={task.constructionLocation || '未指定'}
        />
        <InfoItem
          icon={<Zap className={styles.icon} />}
          label='强度'
          value={task.strength}
          highlight
        />
        <InfoItem
          icon={<Building2 className={styles.icon} />}
          label='浇筑方式'
          value={task.pouringMethod || '未指定'}
        />
        <InfoItem
          icon={<Calendar className={styles.icon} />}
          label='计划时间'
          value={task.plannedStartTime ? formatDateTime(task.plannedStartTime) : '未设定'}
        />
      </div>
    );
  }

  // 详细布局
  if (layout === 'detailed') {
    return (
      <div className={cn(styles.spacing)}>
        {/* 第一行：关键信息 */}
        <div className={cn('grid grid-cols-2', styles.gap)}>
          <InfoItem
            icon={<MapPin className={styles.icon} />}
            label='施工位置'
            value={task.constructionLocation || '未指定'}
          />
          <InfoItem
            icon={<Zap className={styles.icon} />}
            label='混凝土强度'
            value={task.strength}
            highlight
          />
        </div>

        {/* 第二行：施工信息 */}
        <div className={cn('grid grid-cols-2', styles.gap)}>
          <InfoItem
            icon={<Building2 className={styles.icon} />}
            label='浇筑方式'
            value={task.pouringMethod || '未指定'}
          />
          <InfoItem
            icon={<Truck className={styles.icon} />}
            label='运输距离'
            value={task.transportDistance ? `${task.transportDistance}km` : '未知'}
          />
        </div>

        {/* 第三行：时间和联系信息 */}
        <div className={cn('grid grid-cols-2', styles.gap)}>
          <InfoItem
            icon={<Calendar className={styles.icon} />}
            label='计划开始'
            value={task.plannedStartTime ? formatDateTime(task.plannedStartTime) : '未设定'}
          />
          <InfoItem
            icon={<Clock className={styles.icon} />}
            label='预计用时'
            value={task.estimatedDuration ? `${task.estimatedDuration}小时` : '未知'}
          />
        </div>

        {/* 第四行：联系信息 */}
        {(task.contactPerson || task.contactPhone) && (
          <div className={cn('grid grid-cols-2', styles.gap)}>
            {task.contactPerson && (
              <InfoItem
                icon={<Users className={styles.icon} />}
                label='联系人'
                value={task.contactPerson}
              />
            )}
            {task.contactPhone && (
              <InfoItem
                icon={<Phone className={styles.icon} />}
                label='联系电话'
                value={task.contactPhone}
              />
            )}
          </div>
        )}

        {/* 备注信息 */}
        {task.remarks && (
          <InfoItem icon={<FileText className={styles.icon} />} label='备注' value={task.remarks} />
        )}
      </div>
    );
  }

  // 标准布局（默认）
  return (
    <div className={cn(styles.spacing)}>
      {/* 第一行：位置和强度 */}
      <div className={cn('grid grid-cols-2', styles.gap)}>
        <InfoItem
          icon={<MapPin className={styles.icon} />}
          label='施工位置'
          value={task.constructionLocation || '未指定'}
        />
        <InfoItem
          icon={<Zap className={styles.icon} />}
          label='强度'
          value={task.strength}
          highlight
        />
      </div>

      {/* 第二行：浇筑方式和时间 */}
      <div className={cn('grid grid-cols-2', styles.gap)}>
        <InfoItem
          icon={<Building2 className={styles.icon} />}
          label='浇筑方式'
          value={task.pouringMethod || '未指定'}
        />
        <InfoItem
          icon={<Calendar className={styles.icon} />}
          label='计划时间'
          value={task.plannedStartTime ? formatDateTime(task.plannedStartTime) : '未设定'}
        />
      </div>

      {/* 联系信息（如果有） */}
      {task.contactPerson && (
        <InfoItem
          icon={<Users className={styles.icon} />}
          label='联系人'
          value={task.contactPerson}
        />
      )}
    </div>
  );
};
