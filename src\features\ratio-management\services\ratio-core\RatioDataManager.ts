/**
 * 配比数据管理服务
 * 统一处理所有版本的数据加载、保存、缓存等操作
 */

import type {
  RatioCoreData,
  RatioCoreMaterial,
  RatioCoreCalculationParams,
  RatioCoreCalculationResults,
  RatioCoreConfig,
  RatioCoreApiResponse,
} from '@/core/types/ratio-core';

import {
  convertRatioDataToCore,
  convertCoreToVersionMaterials,
  convertCoreCalculationParamsToVersion,
} from '@/core/utils/ratio-core/ratioDataConverter';

/**
 * 缓存管理器
 */
class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set(key: string, data: any, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }
}

/**
 * 配比数据管理器
 */
export class RatioDataManager {
  private cache = new CacheManager();
  private config: RatioCoreConfig;

  constructor(config: RatioCoreConfig) {
    this.config = config;
  }

  // ==================== 数据加载 ====================

  /**
   * 加载配比数据
   */
  async loadRatio(taskId: string): Promise<RatioCoreData | null> {
    const cacheKey = `ratio_${taskId}`;

    // 检查缓存
    if (this.config.cacheEnabled) {
      const cached = this.cache.get<RatioCoreData>(cacheKey);
      if (cached) {
        console.log('从缓存加载配比数据:', taskId);
        return cached;
      }
    }

    try {
      let ratioData: any;

      if (this.config.useMockData) {
        ratioData = await this.loadMockRatio(taskId);
      } else {
        ratioData = await this.loadRatioFromApi(taskId);
      }

      if (!ratioData) {
        return null;
      }

      // 转换为核心格式
      const coreData = convertRatioDataToCore(ratioData, this.config.version);

      // 缓存数据
      if (this.config.cacheEnabled) {
        this.cache.set(cacheKey, coreData, this.config.cacheTTL);
      }

      return coreData;
    } catch (error) {
      console.error('加载配比数据失败:', error);
      throw new Error(`加载配比数据失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 从API加载配比数据
   */
  private async loadRatioFromApi(taskId: string): Promise<any> {
    const response = await fetch(`${this.config.apiEndpoint}/ratios/${taskId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null; // 配比不存在
      }
      throw new Error(`API请求失败: ${response.statusText}`);
    }

    const result: RatioCoreApiResponse = await response.json();
    if (!result.success) {
      throw new Error(result.message || 'API返回错误');
    }

    return result.data;
  }

  /**
   * 加载Mock数据
   */
  private async loadMockRatio(taskId: string): Promise<any> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300));

    // 生成Mock数据
    return {
      id: `ratio_${taskId}`,
      taskId,
      name: `配比方案 ${taskId}`,
      description: '自动生成的配比方案',
      materials: this.generateMockMaterials(),
      calculationParams: this.generateMockCalculationParams(),
      calculationResults: this.generateMockCalculationResults(),
      version: this.config.version,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'draft',
    };
  }

  // ==================== 数据保存 ====================

  /**
   * 保存配比数据
   */
  async saveRatio(ratioData: RatioCoreData): Promise<void> {
    try {
      if (this.config.useMockData) {
        await this.saveMockRatio(ratioData);
      } else {
        await this.saveRatioToApi(ratioData);
      }

      // 更新缓存
      if (this.config.cacheEnabled) {
        const cacheKey = `ratio_${ratioData.taskId}`;
        this.cache.set(cacheKey, ratioData, this.config.cacheTTL);
      }
    } catch (error) {
      console.error('保存配比数据失败:', error);
      throw new Error(`保存配比数据失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 保存到API
   */
  private async saveRatioToApi(ratioData: RatioCoreData): Promise<void> {
    // 转换为目标版本格式
    const versionData = this.convertCoreToVersionData(ratioData);

    const response = await fetch(`${this.config.apiEndpoint}/ratios/${ratioData.taskId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(versionData),
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.statusText}`);
    }

    const result: RatioCoreApiResponse = await response.json();
    if (!result.success) {
      throw new Error(result.message || 'API返回错误');
    }
  }

  /**
   * 保存Mock数据
   */
  private async saveMockRatio(ratioData: RatioCoreData): Promise<void> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('Mock保存配比数据:', ratioData.taskId);
  }

  // ==================== 材料管理 ====================

  /**
   * 添加材料
   */
  addMaterial(ratioData: RatioCoreData, material: RatioCoreMaterial): RatioCoreData {
    return {
      ...ratioData,
      materials: [...ratioData.materials, material],
      updatedAt: new Date().toISOString(),
    };
  }

  /**
   * 移除材料
   */
  removeMaterial(ratioData: RatioCoreData, materialId: string): RatioCoreData {
    return {
      ...ratioData,
      materials: ratioData.materials.filter(m => m.id !== materialId),
      updatedAt: new Date().toISOString(),
    };
  }

  /**
   * 更新材料
   */
  updateMaterial(
    ratioData: RatioCoreData,
    materialId: string,
    updates: Partial<RatioCoreMaterial>
  ): RatioCoreData {
    return {
      ...ratioData,
      materials: ratioData.materials.map(m => (m.id === materialId ? { ...m, ...updates } : m)),
      updatedAt: new Date().toISOString(),
    };
  }

  // ==================== 计算参数管理 ====================

  /**
   * 更新计算参数
   */
  updateCalculationParams(
    ratioData: RatioCoreData,
    updates: Partial<RatioCoreCalculationParams>
  ): RatioCoreData {
    return {
      ...ratioData,
      calculationParams: {
        ...ratioData.calculationParams,
        ...updates,
      },
      updatedAt: new Date().toISOString(),
    };
  }

  // ==================== 工具方法 ====================

  /**
   * 清除缓存
   */
  clearCache(taskId?: string): void {
    if (taskId) {
      this.cache.delete(`ratio_${taskId}`);
    } else {
      this.cache.clear();
    }
  }

  /**
   * 检查是否有缓存
   */
  hasCache(taskId: string): boolean {
    return this.cache.has(`ratio_${taskId}`);
  }

  /**
   * 转换核心数据为版本特定格式
   */
  private convertCoreToVersionData(coreData: RatioCoreData): any {
    return {
      id: coreData.id,
      taskId: coreData.taskId,
      name: coreData.name,
      description: coreData.description,
      materials: convertCoreToVersionMaterials(coreData.materials, this.config.version),
      calculationParams: convertCoreCalculationParamsToVersion(
        coreData.calculationParams,
        this.config.version
      ),
      calculationResults: coreData.calculationResults,
      version: this.config.version,
      createdAt: coreData.createdAt,
      updatedAt: coreData.updatedAt,
      createdBy: coreData.createdBy,
      status: coreData.status,
      tags: coreData.tags,
    };
  }

  // ==================== Mock数据生成 ====================

  private generateMockMaterials(): any[] {
    return [
      {
        id: 'cement_001',
        name: 'P.O 42.5水泥',
        category: 'cement',
        designValue: 400,
        unit: 'kg/m³',
        density: 3100,
        grade: 'P.O 42.5',
      },
      {
        id: 'water_001',
        name: '拌合用水',
        category: 'water',
        designValue: 180,
        unit: 'kg/m³',
        density: 1000,
      },
      {
        id: 'sand_001',
        name: '中砂',
        category: 'sand',
        designValue: 650,
        unit: 'kg/m³',
        density: 2650,
        fineness: 2.6,
      },
      {
        id: 'gravel_001',
        name: '碎石 5-25mm',
        category: 'gravel',
        designValue: 1200,
        unit: 'kg/m³',
        density: 2700,
      },
    ];
  }

  private generateMockCalculationParams(): any {
    return {
      strengthGrade: 30,
      slump: 180,
      maxAggregateSize: 25,
      exposureClass: 'XC1',
      placementMethod: 'pump',
      finishingRequirement: 'smooth',
      waterCementRatio: 0.45,
      sandRatio: 35,
      cementAmount: 400,
      waterAmount: 180,
      density: 2400,
      airContent: 4.5,
      calculationMethod: 'standard',
    };
  }

  private generateMockCalculationResults(): any {
    return {
      totalWeight: 2430,
      materials: {
        'P.O 42.5水泥': 400,
        拌合用水: 180,
        中砂: 650,
        '碎石 5-25mm': 1200,
      },
      strengthPrediction: 32.5,
      workabilityScore: 85,
      durabilityScore: 88,
      qualityScore: 87,
      warnings: [],
      suggestions: [],
      costEstimate: 245.6,
      carbonFootprint: 156.8,
      calculationTime: 150,
      calculationMethod: 'standard',
      timestamp: new Date().toISOString(),
    };
  }
}
