/**
 * useBaseRatioManagement - 基于核心模块的统一配比管理Hook
 * 为所有版本提供一致的配比管理接口，基于新的核心模块和适配器架构
 */

import { useMemo } from 'react';

// 导入适配器
import {
  useRatioManagement as useV1RatioManagement,
  type UseRatioManagementOptions as V1Options,
  type UseRatioManagementReturn as V1Return,
} from '@/features/ratio-management/adapter/v1-adapter';

import {
  useUnifiedRatioManagement as useV2RatioManagement,
  type UseUnifiedRatioManagementOptions as V2Options,
} from '@/features/ratio-management/adapter/v2-adapter';

/**
 * 基础配比管理选项
 */
export interface BaseRatioManagementOptions {
  taskId: string;
  version: 'v1' | 'v2';
  autoLoad?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
  compatibilityMode?: 'legacy' | 'unified';
}

/**
 * 基础配比管理返回接口
 */
export interface BaseRatioManagementReturn {
  // 核心数据状态
  currentRatio: any;
  materials: any[];
  calculationParams: any;
  calculationResults: any;

  // UI状态
  isLoading: boolean;
  isCalculating: boolean;
  isSaving: boolean;
  error: string | null;
  isDirty: boolean;
  hasParamsChanged?: boolean;

  // 数据操作
  loadRatio: (taskId?: string) => Promise<void>;
  saveRatio: () => Promise<void>;
  clearRatio: () => void;

  // 材料操作
  addMaterial: (material: any) => void;
  removeMaterial: (materialId: string) => void;
  updateMaterial: (materialId: string, updates: any) => void;

  // 计算操作
  updateCalculationParams: (params: any) => void;
  calculate: () => Promise<void>;
  reverseCalculate?: () => Promise<void>;

  // 工具方法
  canSave: () => boolean;
  canCalculate: () => boolean;
  hasUnsavedChanges: () => boolean;

  // 版本特定功能
  versionSpecific?: {
    // V2特有
    convertLegacyMaterialsToUnified?: (materials: any[]) => any[];
    convertUnifiedMaterialsToLegacy?: (materials: any[]) => any[];
    exportToLegacyFormat?: () => any;
    importFromLegacyFormat?: (data: any) => void;

    // V3特有
    schemes?: any[];
    activeSchemeId?: string | null;
    createScheme?: (name: string, description?: string) => void;
    switchScheme?: (schemeId: string) => void;
    deleteScheme?: (schemeId: string) => void;
    duplicateScheme?: (schemeId: string, newName: string) => void;
    optimizeRatio?: (objectives?: string[]) => Promise<void>;
    generateRatioWithAI?: (requirements: any) => Promise<void>;
    getAIRecommendations?: () => Promise<any[]>;
    enableComparisonMode?: () => void;
    disableComparisonMode?: () => void;
    addToComparison?: (schemeId: string) => void;
    removeFromComparison?: (schemeId: string) => void;
    validateScheme?: () => boolean;
    exportScheme?: (format?: 'json' | 'excel' | 'pdf') => string | Blob;
    importScheme?: (data: string | File) => Promise<void>;
  };
}

/**
 * 基础配比管理Hook
 *
 * 根据版本自动选择对应的适配器，提供一致的接口
 */
export function useBaseRatioManagement(
  options: BaseRatioManagementOptions
): BaseRatioManagementReturn {
  const { version, ...restOptions } = options;

  // 根据版本选择对应的Hook
  const v1Result = useV1RatioManagement(
    version === 'v1' ? (restOptions as V1Options) : { taskId: '', autoLoad: false }
  );

  const v2Result = useV2RatioManagement(
    version === 'v2'
      ? ({
          ...restOptions,
          compatibilityMode: options.compatibilityMode || 'unified',
        } as V2Options)
      : { taskId: '', autoLoad: false }
  );

  // 根据版本返回对应的结果
  const result = useMemo((): BaseRatioManagementReturn => {
    switch (version) {
      case 'v1':
        return {
          currentRatio: v1Result.currentRatio,
          materials: v1Result.materials,
          calculationParams: v1Result.calculationParams,
          calculationResults: v1Result.calculationResults,
          isLoading: v1Result.isLoading,
          isCalculating: v1Result.isCalculating,
          isSaving: v1Result.isSaving,
          error: v1Result.error,
          isDirty: v1Result.isDirty,
          loadRatio: v1Result.loadRatio,
          saveRatio: v1Result.saveRatio,
          clearRatio: v1Result.clearRatio,
          addMaterial: v1Result.addMaterial,
          removeMaterial: v1Result.removeMaterial,
          updateMaterial: v1Result.updateMaterial,
          updateCalculationParams: v1Result.updateCalculationParams,
          calculate: v1Result.calculate,
          canSave: v1Result.canSave,
          canCalculate: v1Result.canCalculate,
          hasUnsavedChanges: v1Result.hasUnsavedChanges,
          versionSpecific: {},
        };

      case 'v2':
        return {
          currentRatio: v2Result.currentRatio,
          materials: v2Result.selectedMaterials,
          calculationParams: v2Result.calculationParams,
          calculationResults: v2Result.calculationResult,
          isLoading: v2Result.isLoading,
          isCalculating: v2Result.isCalculating,
          isSaving: v2Result.isSaving,
          error: v2Result.error,
          isDirty: v2Result.isDirty,
          hasParamsChanged: v2Result.hasParamsChanged,
          loadRatio: v2Result.loadRatio,
          saveRatio: v2Result.saveRatio,
          clearRatio: v2Result.clearRatio,
          addMaterial: v2Result.addMaterial,
          removeMaterial: v2Result.removeMaterial,
          updateMaterial: v2Result.updateMaterial,
          updateCalculationParams: v2Result.updateCalculationParams,
          calculate: v2Result.calculateRatio,
          reverseCalculate: v2Result.reverseCalculate,
          canSave: v2Result.canSave,
          canCalculate: v2Result.canCalculate,
          hasUnsavedChanges: v2Result.hasUnsavedChanges,
          versionSpecific: {
            convertLegacyMaterialsToUnified: v2Result.convertLegacyMaterialsToUnified,
            convertUnifiedMaterialsToLegacy: v2Result.convertUnifiedMaterialsToLegacy,
            exportToLegacyFormat: v2Result.exportToLegacyFormat,
            importFromLegacyFormat: v2Result.importFromLegacyFormat,
          },
        };

      default:
        throw new Error(`不支持的版本: ${version}`);
    }
  }, [version, v1Result, v2Result]);

  return result;
}

/**
 * 版本特定的Hook别名
 */
export function useV1BaseRatioManagement(options: Omit<BaseRatioManagementOptions, 'version'>) {
  return useBaseRatioManagement({ ...options, version: 'v1' });
}

export function useV2BaseRatioManagement(options: Omit<BaseRatioManagementOptions, 'version'>) {
  return useBaseRatioManagement({ ...options, version: 'v2' });
}

/**
 * 工具函数
 */
export function isVersionCompatible(fromVersion: string, toVersion: string): boolean {
  const compatibilityMatrix = {
    v1: ['v1', 'v2'],
    v2: ['v1', 'v2'],
  };
  return (
    compatibilityMatrix[fromVersion as keyof typeof compatibilityMatrix]?.includes(toVersion) ||
    false
  );
}

export function getVersionFeatures(version: 'v1' | 'v2'): string[] {
  const features = {
    v1: ['基础配比设计', '材料管理', '正向计算', '配比保存', '历史记录'],
    v2: [
      '基础配比设计',
      '材料管理',
      '正向计算',
      '反向计算',
      '配比保存',
      '历史记录',
      '拖拽操作',
      '自动保存',
      '格式转换',
      '备选配比',
      'AI生成',
      '配比推荐',
    ],
  };
  return features[version] || [];
}
