// src/hooks/useFilteredTasks.ts
import { useMemo } from 'react';

import { shallow } from 'zustand/shallow';

import { filterTasksForDisplay } from '@/features/task-management/services/taskFilteringService';
import { useAppStore } from '@/infrastructure/storage/stores/appStore';
import { useUiStore } from '@/infrastructure/storage/stores/uiStore';
import { globalCache } from '@/infrastructure/storage/cache/performance-cache';
import type { Task } from '@/core/types';

export function useFilteredTasks(): Task[] {
  const tasks = useAppStore(state => state.tasks, shallow);
  const selectedPlantId = useUiStore(state => state.selectedPlantId);
  const taskStatusFilter = useUiStore(state => state.taskStatusFilter);

  // 使用缓存的任务过滤，避免重复计算
  return useMemo(() => {
    // 创建缓存键
    const cacheKey = `filtered-tasks-${tasks.length}-${selectedPlantId || 'all'}-${taskStatusFilter || 'all'}`;

    // 尝试从缓存获取
    const cached = globalCache.get<Task[]>(cacheKey);
    if (cached) {
      return cached;
    }

    // 执行过滤计算
    const filtered = filterTasksForDisplay(tasks, selectedPlantId, taskStatusFilter);

    // 缓存结果（3分钟TTL）
    globalCache.set(cacheKey, filtered, { ttl: 3 * 60 * 1000 });

    return filtered;
  }, [tasks, selectedPlantId, taskStatusFilter]);
}
