/**
 * 配比页面统一数据类型定义
 * 解决新版和旧版配比页面中重复定义类型的问题
 */

// ==================== 基础枚举类型 ====================

export enum MaterialCategory {
  // 胶凝材料
  CEMENTITIOUS = 'cementitious',
  // 骨料
  AGGREGATE = 'aggregate',
  // 外加剂
  ADMIXTURE = 'admixture',
  // 掺合料
  SUPPLEMENTARY = 'supplementary',
  // 纤维
  FIBER = 'fiber',
  // 特殊添加剂
  SPECIAL_ADDITIVE = 'special_additive',
  // 水
  WATER = 'water',
}

export enum MaterialType {
  // 胶凝材料
  PORTLAND_CEMENT = 'portland_cement',
  SULFOALUMINATE_CEMENT = 'sulfoaluminate_cement',
  WHITE_CEMENT = 'white_cement',
  RAPID_HARDENING_CEMENT = 'rapid_hardening_cement',
  LOW_HEAT_CEMENT = 'low_heat_cement',

  // 粗骨料
  CRUSHED_STONE = 'crushed_stone',
  GRAVEL = 'gravel',
  RECYCLED_AGGREGATE = 'recycled_aggregate',
  LIGHTWEIGHT_AGGREGATE = 'lightweight_aggregate',
  HEAVYWEIGHT_AGGREGATE = 'heavyweight_aggregate',

  // 细骨料
  RIVER_SAND = 'river_sand',
  MACHINE_SAND = 'machine_sand',
  SEA_SAND = 'sea_sand',
  DESERT_SAND = 'desert_sand',
  RECYCLED_SAND = 'recycled_sand',

  // 掺合料
  FLY_ASH = 'fly_ash',
  SLAG_POWDER = 'slag_powder',
  SILICA_FUME = 'silica_fume',
  METAKAOLIN = 'metakaolin',
  LIMESTONE_POWDER = 'limestone_powder',

  // 外加剂
  SUPERPLASTICIZER = 'superplasticizer',
  RETARDER = 'retarder',
  ACCELERATOR = 'accelerator',
  AIR_ENTRAINING = 'air_entraining',
  WATERPROOFING = 'waterproofing',
  ANTIFREEZE = 'antifreeze',
  EXPANSION_AGENT = 'expansion_agent',
  SHRINKAGE_REDUCING = 'shrinkage_reducing',

  // 纤维
  STEEL_FIBER = 'steel_fiber',
  POLYPROPYLENE_FIBER = 'polypropylene_fiber',
  GLASS_FIBER = 'glass_fiber',
  CARBON_FIBER = 'carbon_fiber',
  BASALT_FIBER = 'basalt_fiber',

  // 特殊添加剂
  COLORING_AGENT = 'coloring_agent',
  CORROSION_INHIBITOR = 'corrosion_inhibitor',
  ALKALI_SILICA_INHIBITOR = 'alkali_silica_inhibitor',
  VISCOSITY_MODIFIER = 'viscosity_modifier',

  // 水
  POTABLE_WATER = 'potable_water',
  RECYCLED_WATER = 'recycled_water',
  SEA_WATER = 'sea_water',
}

export enum AvailabilityStatus {
  AVAILABLE = 'available',
  LIMITED = 'limited',
  OUT_OF_STOCK = 'out_of_stock',
  SEASONAL = 'seasonal',
  CUSTOM_ORDER = 'custom_order',
}

export enum ExposureClass {
  XC1 = 'XC1', // 干燥或永久湿润
  XC2 = 'XC2', // 湿润，很少干燥
  XC3 = 'XC3', // 中等湿度
  XC4 = 'XC4', // 干湿交替
  XD1 = 'XD1', // 中等湿度
  XD2 = 'XD2', // 湿润，很少干燥
  XD3 = 'XD3', // 干湿交替
  XS1 = 'XS1', // 海洋环境中的空气传播盐分
  XS2 = 'XS2', // 永久浸没
  XS3 = 'XS3', // 潮汐、飞溅和喷雾区域
  XF1 = 'XF1', // 中等水饱和，无除冰剂
  XF2 = 'XF2', // 中等水饱和，有除冰剂
  XF3 = 'XF3', // 高度水饱和，无除冰剂
  XF4 = 'XF4', // 高度水饱和，有除冰剂或海水
}

export enum PlacementMethod {
  MANUAL = 'manual',
  PUMP = 'pump',
  CRANE = 'crane',
  CONVEYOR = 'conveyor',
}

export enum FinishingRequirement {
  ROUGH = 'rough',
  SMOOTH = 'smooth',
  EXPOSED = 'exposed',
  ARCHITECTURAL = 'architectural',
}

// ==================== 基础接口类型 ====================

export interface ChemicalComposition {
  SiO2?: number; // 二氧化硅
  Al2O3?: number; // 三氧化二铝
  Fe2O3?: number; // 三氧化二铁
  CaO?: number; // 氧化钙
  MgO?: number; // 氧化镁
  SO3?: number; // 三氧化硫
  Na2O?: number; // 氧化钠
  K2O?: number; // 氧化钾
  Cl?: number; // 氯离子
  LOI?: number; // 烧失量
}

export interface PhysicalProperties {
  particleSize?: {
    d10?: number; // μm
    d50?: number; // μm
    d90?: number; // μm
  };
  surfaceArea?: number; // m²/kg
  bulkDensity?: number; // kg/m³
  voidRatio?: number; // 空隙率
  crushingValue?: number; // 压碎值
  abrasionValue?: number; // 磨耗值
  soundness?: number; // 安定性
}

export interface StorageRequirements {
  temperature?: {
    min?: number;
    max?: number;
  };
  humidity?: {
    max?: number;
  };
  shelfLife?: number; // 保质期（天）
  specialConditions?: string[]; // 特殊存储条件
}

export interface UsageRecommendation {
  application: string; // 应用场景
  dosage: {
    min: number;
    max: number;
    unit: string;
  };
  notes?: string; // 使用说明
}

// ==================== 材料相关类型 ====================

export interface EnhancedMaterial {
  id: string;
  name: string;
  category: MaterialCategory;
  type: MaterialType;
  density: number; // kg/m³
  specificGravity: number; // 比重
  absorptionRate?: number; // 吸水率 %
  fineness?: number; // 细度
  strength?: number; // 强度等级
  chemicalComposition?: ChemicalComposition;
  physicalProperties?: PhysicalProperties;
  qualityStandard?: string; // 质量标准
  supplier?: string; // 供应商
  cost?: number; // 成本 元/吨
  carbonFootprint?: number; // 碳足迹 kg CO2/吨
  availability: AvailabilityStatus;
  storageRequirements?: StorageRequirements;
  compatibilityMatrix?: string[]; // 兼容的材料ID列表
  recommendedUsage?: UsageRecommendation[];
}

export interface RatioMaterial {
  id: string;
  materialId: string;
  name: string;
  specification: string;
  theoreticalAmount: number;
  waterContent: number;
  stoneContent: number;
  actualAmount: number;
  designValue: number;
  siloId: string;
}

// 统一的配比材料类型（新旧版本共用）
export interface UnifiedRatioMaterial {
  actualValue?: number;
  specificGravity?: number;
  grade?: string;
  ratio?: number;
  percentage?: number;
  id: string;
  materialId: string;
  name: string; // 对应旧版的 materialType
  specification: string; // 对应旧版的 spec
  category: MaterialCategory;
  unit: string;
  density: number;
  theoreticalAmount: number; // 对应旧版的 theoryAmount
  actualAmount: number;
  waterContent: number; // 对应旧版的 waterRatio
  stoneContent: number; // 对应旧版的 stoneRatio
  designValue: number;
  siloId: string;
  siloName: string; // 对应旧版的 binName
  cost: number;
  supplier: string;
}

// 兼容旧版配比页面的材料类型（保留向后兼容）
export interface LegacyRatioMaterial {
  id: string;
  materialType: string;
  spec: string;
  theoryAmount: number;
  waterRatio: number;
  stoneRatio: number;
  actualAmount: number;
  designValue: number;
  binName: string;
}

// 数据转换工具函数类型
export interface RatioMaterialConverter {
  legacyToUnified: (legacy: LegacyRatioMaterial) => UnifiedRatioMaterial;
  unifiedToLegacy: (unified: UnifiedRatioMaterial) => LegacyRatioMaterial;
}

export interface MaterialName {
  id: string;
  name: string;
  order: number;
  type: 'Aggregate' | 'Powder' | 'Admixture' | 'Binder' | 'Water';
}

export interface StorageLocation {
  id: string;
  binId: string;
  binName: string;
  order: number;
}

export interface Specification {
  id: string;
  name: string;
}

export interface AdjustmentMaterial {
  id: string;
  name: string;
}

export interface MaterialDensity {
  id: string;
  name: string;
  density: number;
}

export interface MortarCoefficient {
  id: string;
  name: string;
  coefficient: number;
}

// ==================== 仓储管理相关类型 ====================

export interface Silo {
  id: string;
  name: string;
  type: 'cement' | 'sand' | 'stone' | 'water' | 'additive' | 'flyash' | 'other';
  capacity: number;
  currentAmount: number;
  unit: string;
  density: number;
  supplier: string;
  location: string;
  status: 'normal' | 'low' | 'empty' | 'maintenance';
  lastUpdated: string;
  image: string;
  // 新增字段
  mixingStation: string; // 所属搅拌站
  materialName: string; // 材料名称
  storageLocation: string; // 存放地名称
  specification: string; // 材料规格
}

export interface SiloMapping {
  id: string;
  siloType: string;
  materialName: string;
  spec: string;
  storageBin: string;
  lastEntry: string | null;
  enabled: boolean;
}

export interface MaterialSpec {
  id: string;
  materialType: string;
  specName: string;
  description: string;
}

export interface MixingStation {
  id: string;
  name: string;
  location: string;
}

// ==================== 拖拽相关类型 ====================

export interface DraggedMaterial {
  id: string;
  name: string;
  type: string;
  siloId: string;
  capacity: number;
  currentAmount: number;
  specification: string;
}

// ==================== 计算参数相关类型 ====================

export interface RatioCalculationParams {
  // 基本参数
  targetStrength: number; // 目标强度 MPa
  slump: number; // 坍落度 mm
  maxAggregateSize: number; // 最大骨料粒径 mm
  exposureClass: ExposureClass; // 暴露等级

  // 环境参数
  ambientTemperature: number; // 环境温度 °C
  relativeHumidity: number; // 相对湿度 %
  cementTemperature: number; // 水泥温度 °C
  aggregateTemperature: number; // 骨料温度 °C

  // 材料选择
  selectedMaterials: string[]; // 选中的材料ID列表
  cementType: string; // 水泥类型
  aggregateType: string; // 骨料类型
  waterType: string; // 水类型

  // 配比参数
  waterCementRatio: number; // 水胶比
  sandRatio: number; // 砂率 %
  cementContent: number; // 水泥用量 kg/m³
  waterContent: number; // 用水量 kg/m³

  // 外加剂参数
  additiveRatio: number; // 外加剂掺量 %
  flyashRatio: number; // 粉煤灰掺量 %
  mineralPowderRatio: number; // 矿粉掺量 %
  silicaFumeRatio: number; // 硅灰掺量 %
  antifreezeRatio: number; // 防冻剂掺量 %
  expansionRatio: number; // 膨胀剂掺量 %

  // 数量参数
  cementAmount: number; // 水泥用量 kg/m³
  waterAmount: number; // 用水量 kg/m³
  density: number; // 密度 kg/m³
  airContent: number; // 含气量 %
  strengthGrade: number; // 强度等级 MPa
  ultraFineSandRatio: number; // 超细砂掺量 %
  earlyStrengthRatio: number; // 早强剂掺量 %

  // 施工参数
  placementMethod: PlacementMethod; // 浇筑方式
  finishingRequirement: FinishingRequirement; // 表面处理要求
  cureConditions: {
    method: string; // 养护方法
    duration: number; // 养护时间 天
    temperature: number; // 养护温度 °C
    humidity: number; // 养护湿度 %
  };

  // 外加剂用量（具体数值）
  admixtureAmount: number;
  antifreezeAmount: number;
  flyAshAmount: number;
  mineralPowderAmount: number;
  s105PowderAmount: number;
  expansionAgentAmount: number;
  earlyStrengthAgentAmount: number;
  ultraFineSandAmount: number;
}

export interface ReverseCalculationParams {
  targetVolume: number;
  currentRatio: RatioCalculationParams;
  materialConstraints?: Record<string, { min?: number; max?: number }>;
  // 新增字段以匹配实际使用
  cementSubstitutionRate: number;
  flyAshFactor: number;
  flyAshDensity: number;
  sandDensity: number;
  flyAshCementFactor: number;
  silicaFumeCementFactor: number;
}

export interface CalculationResults {
  totalWeight: number;
  materials: Record<string, number>;
  strengthPrediction: number;
  qualityScore: number;
  warnings: string[];
  suggestions: string[];
  carbonFootprint?: number;
  costEstimate?: number;
  optimizations?: OptimizationAction[];
  validateParams?: string[];
}

export interface RatioResult {
  materials: Record<string, number>;
  totalVolume: number;
  density: number;
  cost?: number;
  warnings?: string[];
  // 新增字段以匹配实际使用
  water: number;
  cement: number;
  flyAsh: number;
  mineralPowder: number;
  s105Powder: number;
  expansionAgent: number;
  earlyStrengthAgent: number;
  sand: number;
  stone: number;
  gravel: number;
  admixture: number;
  antifreeze: number;
  ultraFineSand: number;
  totalWeight: number;
}

export interface OptimizationAction {
  type: 'material_substitution' | 'ratio_adjustment' | 'additive_optimization';
  description: string;
  impact: {
    cost?: number;
    strength?: number;
    workability?: number;
    durability?: number;
  };
  confidence: number; // 0-1
}

// ==================== 历史记录相关类型 ====================

export interface RatioHistoryEntry {
  id: string;
  taskId: string;
  editor: string;
  timestamp: string;
  price: number;
  materials: {
    cement: number;
    flyAsh: number;
    mineralPowder: number;
    sand: number;
    stone: number;
    water: number;
    admixture: number;
  };
}

// ==================== 配比选择相关类型 ====================

export interface RatioSelectionRecord {
  id: string;
  ratioNumber: string; // 配比编号
  strengthGrade: string; // 强度等级
  slump: number; // 坍落度 (mm)
  materials: {
    cement: number; // 水泥 (kg/m³)
    sand: number; // 砂 (kg/m³)
    stone: number; // 石子 (kg/m³)
    water: number; // 水 (kg/m³)
    admixture: number; // 外加剂 (kg/m³)
    flyAsh?: number; // 粉煤灰 (kg/m³)
    mineralPowder?: number; // 矿粉 (kg/m³)
  };
  parameters: {
    waterRatio: number; // 水胶比
    sandRatio: number; // 砂率 (%)
    density: number; // 密度 (kg/m³)
  };
  createTime: string;
  creator: string;
  status: 'active' | 'archived' | 'draft';
  usageCount: number;
  description?: string;
  tags?: string[];
  approver?: string;
  approveTime?: string;
}

// ==================== 表格数据相关类型 ====================

export interface TableRowData {
  id: string;
  materialName: string;
  spec: string;
  theoryAmount: number;
  waterContent: number;
  actualAmount: number;
  designValue: number;
}

// ==================== 权限相关类型 ====================

export interface UserPermission {
  id: string;
  username: string;
  permissions: {
    calculation: boolean;
    design: boolean;
    send: boolean;
    tweak: boolean;
    execute: boolean;
  };
}

// ==================== 配比页面组件Props类型 ====================

/**
 * 配比计算方法类型
 */
export type RatioCalculationMethod = 'method1' | 'method2' | 'method3' | 'method4';

/**
 * 配比页面UI状态
 */
export interface RatioUIState {
  // 模态框状态
  activeModal: string | null;

  // 加载状态
  isLoading: boolean;
  isCalculating: boolean;
  isSaving: boolean;

  // 错误状态
  error: string | null;

  // 表单状态
  isDirty: boolean;
  isValid: boolean;

  // 显示状态
  showAdvancedOptions: boolean;
  showCalculationDetails: boolean;
}

/**
 * 配比数据状态
 */
export interface RatioDataState {
  // 当前任务
  task: any | null;

  // 配比参数
  calculationParams: RatioCalculationParams;
  reverseParams: ReverseCalculationParams;

  // 计算结果
  proportions: RatioResult;

  // 材料列表
  materials: UnifiedRatioMaterial[];

  // 计算方法
  calculationMethod: RatioCalculationMethod;

  // 初始化状态
  isInitialized: boolean;
}

/**
 * 配比操作接口
 */
export interface RatioActions {
  // 初始化
  initialize: (task: any) => void;

  // 参数设置
  setCalculationParams: (params: Partial<RatioCalculationParams>) => void;
  setReverseParam: (key: keyof ReverseCalculationParams, value: number) => void;
  setCalculationMethod: (method: RatioCalculationMethod) => void;

  // 计算操作
  calculate: () => void;
  reverseCalculate: () => void;

  // 材料管理
  addMaterial: () => boolean;
  updateMaterial: (id: string, material: Partial<UnifiedRatioMaterial>) => void;
  deleteMaterial: (id: string) => void;

  // UI操作
  setActiveModal: (modal: string | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

/**
 * 配比操作栏Props
 */
export interface RatioActionBarProps {
  onOpenModal: (modal: string) => void;
  taskId: string;
}

/**
 * 任务信息面板Props
 */
export interface TaskInfoPanelProps {
  task: any;
}

/**
 * 计算面板Props
 */
export interface CalculationPanelProps {
  calculationParams: RatioCalculationParams;
  proportions: RatioResult;
  calculationMethod: RatioCalculationMethod;
  reverseParams: ReverseCalculationParams;
  onParamsChange: (params: Partial<RatioCalculationParams>) => void;
  onReverseParamChange: (key: keyof ReverseCalculationParams, value: number) => void;
  onMethodChange: (method: RatioCalculationMethod) => void;
  onCalculate: () => void;
  onReverseCalculate: () => void;
}

/**
 * 配比表格Props
 */
export interface ProportioningTableProps {
  materials: UnifiedRatioMaterial[];
  onOpenModal: (modal: string) => void;
  onAddMaterial: () => void;
  onUpdateMaterial: (id: string, material: Partial<UnifiedRatioMaterial>) => void;
  onDeleteMaterial: (id: string) => void;
}

/**
 * 物料侧边栏Props
 */
export interface MaterialsSidebarProps {
  // 暂时为空，后续可扩展
}

/**
 * 配比页脚Props
 */
export interface RatioFooterProps {
  onSave: () => void;
  onSubmit: () => void;
  onExit: () => void;
  isDirty: boolean;
  isSaving: boolean;
}

// ==================== 配比Hook返回类型 ====================

/**
 * 配比计算Hook返回类型
 */
export interface UseRatioCalculationReturn {
  // 计算参数
  calculationParams: RatioCalculationParams;
  reverseParams: ReverseCalculationParams;
  calculationMethod: RatioCalculationMethod;

  // 计算结果
  proportions: RatioResult;

  // 操作方法
  setCalculationParams: (params: Partial<RatioCalculationParams>) => void;
  setReverseParam: (key: keyof ReverseCalculationParams, value: number) => void;
  setCalculationMethod: (method: RatioCalculationMethod) => void;
  calculate: () => void;
  reverseCalculate: () => void;

  // 状态
  isCalculating: boolean;
  error: string | null;
  validateParams: (params: Partial<RatioCalculationParams>) => {
    isValid: boolean;
    errors: string[];
  };
  predictStrength: (params: RatioCalculationParams) => number;
}

/**
 * 配比材料管理Hook返回类型
 */
export interface UseRatioMaterialsReturn {
  // 材料数据
  materials: UnifiedRatioMaterial[];

  // 操作方法
  addMaterial: () => boolean;
  updateMaterial: (id: string, material: Partial<UnifiedRatioMaterial>) => void;
  deleteMaterial: (id: string) => void;

  // 状态
  isLoading: boolean;
  error: string | null;
}

/**
 * 配比模态框管理Hook返回类型
 */
export interface UseRatioModalsReturn {
  // 模态框状态
  activeModal: string | null;

  // 操作方法
  openModal: (modal: string) => void;
  closeModal: () => void;

  // 模态框数据
  modalData: Record<string, any>;
  setModalData: (modal: string, data: any) => void;
}
