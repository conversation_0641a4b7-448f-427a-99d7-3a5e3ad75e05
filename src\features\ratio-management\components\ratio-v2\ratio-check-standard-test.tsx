'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Badge } from '@/shared/components/badge';
import { RatioCheckStandardModal } from './ratio-check-standard-modal';
import { useRatioCheckStandards } from '@/features/ratio-management/hooks/ratio/useRatioCheckStandards';
import { Shield, CheckCircle, AlertTriangle, Info } from 'lucide-react';

/**
 * 配比检查标准测试组件
 * 用于测试和演示配比检查标准功能
 */
export function RatioCheckStandardTest() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { standards, validateRatio, getApplicableStandard, loading } = useRatioCheckStandards();

  // 测试配比数据
  const testRatioData = {
    cement: 350,
    sand: 750,
    stone: 1150,
    water: 175,
    additive: 8,
    flyAsh: 50,
    mineralPowder: 30,
  };

  // 测试验证功能
  const testValidation = () => {
    const result = validateRatio(testRatioData, 'C30', '普通混凝土');
    console.log('验证结果:', result);
    return result;
  };

  const validationResult = testValidation();

  if (loading) {
    return (
      <Card className='w-full max-w-4xl mx-auto'>
        <CardContent className='p-6'>
          <div className='flex items-center justify-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
            <span className='ml-2'>加载中...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className='w-full max-w-6xl mx-auto space-y-4 p-4'>
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Shield className='h-5 w-5 text-primary' />
            配比检查标准测试
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          {/* 操作按钮 */}
          <div className='flex items-center gap-2'>
            <Button onClick={() => setIsModalOpen(true)} className='gap-2'>
              <Shield className='h-4 w-4' />
              打开检查标准
            </Button>
            <Button variant='outline' onClick={testValidation} className='gap-2'>
              <CheckCircle className='h-4 w-4' />
              测试验证
            </Button>
          </div>

          {/* 标准统计 */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <Card>
              <CardContent className='p-4'>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-primary'>{standards.length}</div>
                  <div className='text-sm text-muted-foreground'>总标准数</div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className='p-4'>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-green-600'>
                    {standards.filter(s => s.isActive).length}
                  </div>
                  <div className='text-sm text-muted-foreground'>启用标准</div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className='p-4'>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-orange-600'>
                    {standards.filter(s => !s.isActive).length}
                  </div>
                  <div className='text-sm text-muted-foreground'>停用标准</div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 测试配比数据 */}
          <Card>
            <CardHeader>
              <CardTitle className='text-sm'>测试配比数据 (C30普通混凝土)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-2 md:grid-cols-4 gap-3 text-xs'>
                <div>
                  <span className='font-medium'>水泥:</span> {testRatioData.cement} kg/m³
                </div>
                <div>
                  <span className='font-medium'>砂子:</span> {testRatioData.sand} kg/m³
                </div>
                <div>
                  <span className='font-medium'>石子:</span> {testRatioData.stone} kg/m³
                </div>
                <div>
                  <span className='font-medium'>水:</span> {testRatioData.water} kg/m³
                </div>
                <div>
                  <span className='font-medium'>外加剂:</span> {testRatioData.additive} kg/m³
                </div>
                <div>
                  <span className='font-medium'>粉煤灰:</span> {testRatioData.flyAsh} kg/m³
                </div>
                <div>
                  <span className='font-medium'>矿粉:</span> {testRatioData.mineralPowder} kg/m³
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 验证结果 */}
          <Card>
            <CardHeader>
              <CardTitle className='text-sm flex items-center gap-2'>
                {validationResult.isCompliant ? (
                  <CheckCircle className='h-4 w-4 text-green-600' />
                ) : (
                  <AlertTriangle className='h-4 w-4 text-red-600' />
                )}
                验证结果
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <div className='flex items-center gap-2'>
                <Badge variant={validationResult.isCompliant ? 'default' : 'destructive'}>
                  {validationResult.isCompliant ? '符合标准' : '不符合标准'}
                </Badge>
                {validationResult.appliedStandard && (
                  <Badge variant='outline'>
                    应用标准: {validationResult.appliedStandard.strength}{' '}
                    {validationResult.appliedStandard.category}
                  </Badge>
                )}
              </div>

              {validationResult.violations.length > 0 && (
                <div>
                  <h4 className='text-sm font-medium mb-2 flex items-center gap-1'>
                    <AlertTriangle className='h-3 w-3 text-red-600' />
                    违规项目 ({validationResult.violations.length})
                  </h4>
                  <div className='space-y-1'>
                    {validationResult.violations.map((violation, index) => (
                      <div
                        key={index}
                        className='text-xs p-2 bg-red-50 border border-red-200 rounded'
                      >
                        <div className='font-medium text-red-800'>{violation.fieldName}</div>
                        <div className='text-red-600'>{violation.message}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {validationResult.violations.length === 0 && (
                <div className='text-xs text-green-600 flex items-center gap-1'>
                  <CheckCircle className='h-3 w-3' />
                  所有材料用量均符合标准要求
                </div>
              )}
            </CardContent>
          </Card>

          {/* 标准列表预览 */}
          <Card>
            <CardHeader>
              <CardTitle className='text-sm'>标准列表预览</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-2 max-h-40 overflow-y-auto'>
                {standards.slice(0, 5).map(standard => (
                  <div
                    key={standard.id}
                    className='flex items-center justify-between p-2 bg-muted/50 rounded text-xs'
                  >
                    <div className='flex items-center gap-2'>
                      <Badge variant='outline'>{standard.strength}</Badge>
                      <span>{standard.category}</span>
                      {!standard.isActive && (
                        <Badge variant='secondary' className='text-xs'>
                          停用
                        </Badge>
                      )}
                    </div>
                    <div className='text-muted-foreground'>{standard.description || '无描述'}</div>
                  </div>
                ))}
                {standards.length > 5 && (
                  <div className='text-center text-xs text-muted-foreground'>
                    还有 {standards.length - 5} 个标准...
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <CardTitle className='text-sm flex items-center gap-2'>
                <Info className='h-4 w-4 text-blue-600' />
                使用说明
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-xs text-muted-foreground space-y-1'>
                <p>• 点击"打开检查标准"按钮可以管理配比检查标准</p>
                <p>• 支持添加、编辑、删除标准，以及导入导出功能</p>
                <p>• 点击"测试验证"按钮可以验证当前测试配比是否符合标准</p>
                <p>• 系统会自动根据强度等级和类别匹配适用的标准</p>
                <p>• 验证结果会显示违规项目和具体的超限信息</p>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>

      {/* 检查标准模态框 */}
      <RatioCheckStandardModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
    </div>
  );
}
