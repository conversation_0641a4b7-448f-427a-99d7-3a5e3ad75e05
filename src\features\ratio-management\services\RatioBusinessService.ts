/**
 * 配比业务服务
 * 处理配比相关的业务逻辑
 */

import type { RatioModel, RatioMaterialModel, RatioValidationResult } from '@/models/RatioModel';
import {
  BusinessResult,
  BusinessErrorCodes,
  createBusinessWarning,
  type BusinessWarning,
} from '@/core/domain/BusinessResult';
import type { IRatioRepository } from '@/shared/services/ratioRepository';
import type {
  ICalculationEngine,
  CalculationParams,
  CalculationResult,
} from './ICalculationEngine';

// 重新导出引擎类型以保持向后兼容
export type { CalculationParams, CalculationResult } from './ICalculationEngine';
import type { IRatioValidator } from './RatioValidator';

export interface IRatioBusinessService {
  // 配比管理
  getRatio(taskId: string): Promise<BusinessResult<RatioModel>>;
  saveRatio(ratio: RatioModel): Promise<BusinessResult<RatioModel>>;
  updateRatio(ratioId: string, updates: Partial<RatioModel>): Promise<BusinessResult<RatioModel>>;
  deleteRatio(ratioId: string): Promise<BusinessResult<void>>;

  // 配比计算
  calculateRatio(params: CalculationParams): Promise<BusinessResult<CalculationResult>>;
  reverseCalculate(materials: RatioMaterialModel[]): Promise<BusinessResult<CalculationParams>>;
  optimizeRatio(ratio: RatioModel, goals: OptimizationGoals): Promise<BusinessResult<RatioModel>>;

  // 配比验证
  validateRatio(ratio: RatioModel): Promise<BusinessResult<RatioValidationResult>>;
  validateCalculationParams(params: CalculationParams): Promise<BusinessResult<void>>;

  // 配比分析
  analyzeQuality(ratio: RatioModel): Promise<BusinessResult<QualityAnalysis>>;
  analyzeCost(ratio: RatioModel): Promise<BusinessResult<CostAnalysis>>;
  analyzeEnvironmental(ratio: RatioModel): Promise<BusinessResult<EnvironmentalAnalysis>>;

  // 配比比较
  compareRatios(ratioIds: string[]): Promise<BusinessResult<RatioComparison>>;

  // 配比推荐
  recommendOptimizations(ratio: RatioModel): Promise<BusinessResult<OptimizationRecommendation[]>>;
  recommendMaterials(params: CalculationParams): Promise<BusinessResult<MaterialRecommendation[]>>;
}

export interface OptimizationGoals {
  cost: number; // weight 0-1
  strength: number; // weight 0-1
  durability: number; // weight 0-1
  workability: number; // weight 0-1
  environmental: number; // weight 0-1
}

export interface QualityAnalysis {
  overallScore: number;
  strengthAnalysis: {
    predictedStrength: number;
    targetStrength: number;
    confidence: number;
    factors: QualityFactor[];
  };
  durabilityAnalysis: {
    durabilityIndex: number;
    riskFactors: string[];
    recommendations: string[];
  };
  workabilityAnalysis: {
    workabilityIndex: number;
    slumpPrediction: number;
    pumpability: 'excellent' | 'good' | 'fair' | 'poor';
  };
  warnings: string[];
  recommendations: string[];
}

export interface CostAnalysis {
  totalCost: number;
  costPerCubicMeter: number;
  materialCosts: {
    materialId: string;
    materialName: string;
    amount: number;
    unitCost: number;
    totalCost: number;
    percentage: number;
  }[];
  costOptimizationPotential: number;
  recommendations: string[];
}

export interface EnvironmentalAnalysis {
  carbonFootprint: number; // kg CO2/m³
  materialImpacts: {
    materialId: string;
    materialName: string;
    carbonFootprint: number;
    percentage: number;
  }[];
  sustainabilityScore: number;
  recommendations: string[];
}

export interface RatioComparison {
  ratios: {
    id: string;
    name: string;
    metrics: ComparisonMetrics;
  }[];
  summary: {
    bestForCost: string;
    bestForStrength: string;
    bestForEnvironment: string;
    bestOverall: string;
  };
  recommendations: string[];
}

export interface ComparisonMetrics {
  cost: number;
  strength: number;
  carbonFootprint: number;
  qualityScore: number;
  durabilityIndex: number;
  workabilityIndex: number;
}

export interface OptimizationRecommendation {
  type: 'material_substitution' | 'proportion_adjustment' | 'additive_optimization';
  description: string;
  impact: {
    costChange: number; // percentage
    strengthChange: number; // percentage
    carbonChange: number; // percentage
    qualityChange: number; // percentage
  };
  confidence: number; // 0-100
  implementation: string;
}

export interface MaterialRecommendation {
  materialId: string;
  materialName: string;
  reason: string;
  benefits: string[];
  considerations: string[];
  confidence: number;
}

export interface QualityFactor {
  factor: string;
  impact: 'positive' | 'negative' | 'neutral';
  weight: number;
  description: string;
}

export class RatioBusinessService implements IRatioBusinessService {
  constructor(
    private ratioRepository: IRatioRepository,
    private calculationEngine: ICalculationEngine,
    private ratioValidator: IRatioValidator,
    private logger: ILogger
  ) {}

  async getRatio(taskId: string): Promise<BusinessResult<RatioModel>> {
    try {
      this.logger.info('Getting ratio for task', { taskId });

      const ratio = await this.ratioRepository.getRatio(taskId);
      if (!ratio) {
        return BusinessResult.failure(
          BusinessErrorCodes.DATA_NOT_FOUND,
          `未找到任务 ${taskId} 的配比数据`
        );
      }

      // 检查数据完整性
      const warnings: BusinessWarning[] = [];
      if (!ratio.calculationResults) {
        warnings.push(
          createBusinessWarning(
            'MISSING_CALCULATION_RESULTS',
            '配比缺少计算结果，建议重新计算',
            'medium'
          )
        );
      }

      if (ratio.materials.length === 0) {
        warnings.push(createBusinessWarning('NO_MATERIALS', '配比中没有材料，请添加材料', 'high'));
      }

      this.logger.info('Ratio retrieved successfully', {
        taskId,
        ratioId: ratio.id,
        materialsCount: ratio.materials.length,
      });

      return BusinessResult.success(ratio, warnings.length > 0 ? warnings : undefined);
    } catch (error) {
      this.logger.error('Failed to get ratio', error as Error, { taskId });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.SYSTEM_ERROR);
    }
  }

  async saveRatio(ratio: RatioModel): Promise<BusinessResult<RatioModel>> {
    try {
      this.logger.info('Saving ratio', { ratioId: ratio.id, taskId: ratio.taskId });

      // 验证配比数据
      const validationResult = await this.validateRatio(ratio);
      if (validationResult.isFailure()) {
        return BusinessResult.failure(
          BusinessErrorCodes.VALIDATION_FAILED,
          '配比数据验证失败',
          validationResult.error
        );
      }

      // 更新元数据
      const now = new Date().toISOString();
      const updatedRatio: RatioModel = {
        ...ratio,
        metadata: {
          ...ratio.metadata,
          updatedAt: now,
          // updatedBy: getCurrentUser(), // 需要从上下文获取
        },
      };

      // 保存到仓库
      const saveResult = await this.ratioRepository.saveRatio(updatedRatio);
      if (saveResult.isFailure()) {
        return saveResult;
      }

      // 添加历史记录
      await this.ratioRepository.addHistoryRecord({
        ratioId: ratio.id,
        taskId: ratio.taskId,
        version: ratio.version + 1,
        changeType: ratio.id ? 'modified' : 'created',
        changes: [], // 需要实现变更检测
        changedBy: ratio.metadata.updatedBy,
        changedAt: now,
        snapshot: ratio,
      });

      this.logger.info('Ratio saved successfully', {
        ratioId: saveResult.data!.id,
        taskId: ratio.taskId,
      });

      return saveResult;
    } catch (error) {
      this.logger.error('Failed to save ratio', error as Error, {
        ratioId: ratio.id,
        taskId: ratio.taskId,
      });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.SYSTEM_ERROR);
    }
  }

  async updateRatio(
    ratioId: string,
    updates: Partial<RatioModel>
  ): Promise<BusinessResult<RatioModel>> {
    try {
      this.logger.info('Updating ratio', { ratioId });

      // 获取现有配比
      const existingRatio = await this.ratioRepository.getRatioById(ratioId);
      if (!existingRatio) {
        return BusinessResult.failure(BusinessErrorCodes.DATA_NOT_FOUND, `未找到配比 ${ratioId}`);
      }

      // 合并更新
      const updatedRatio: RatioModel = {
        ...existingRatio,
        ...updates,
        metadata: {
          ...existingRatio.metadata,
          ...updates.metadata,
          updatedAt: new Date().toISOString(),
        },
      };

      // 验证更新后的配比
      const validationResult = await this.validateRatio(updatedRatio);
      if (validationResult.isFailure()) {
        return BusinessResult.failure(
          BusinessErrorCodes.VALIDATION_FAILED,
          '更新后的配比数据验证失败',
          validationResult.error
        );
      }

      // 保存更新
      const updateResult = await this.ratioRepository.updateRatio(ratioId, updatedRatio);

      this.logger.info('Ratio updated successfully', { ratioId });

      return updateResult;
    } catch (error) {
      this.logger.error('Failed to update ratio', error as Error, { ratioId });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.SYSTEM_ERROR);
    }
  }

  async deleteRatio(ratioId: string): Promise<BusinessResult<void>> {
    try {
      this.logger.info('Deleting ratio', { ratioId });

      // 检查是否可以删除
      const ratio = await this.ratioRepository.getRatioById(ratioId);
      if (!ratio) {
        return BusinessResult.failure(BusinessErrorCodes.DATA_NOT_FOUND, `未找到配比 ${ratioId}`);
      }

      if (ratio.metadata.status === 'in-use') {
        return BusinessResult.failure(
          BusinessErrorCodes.OPERATION_NOT_ALLOWED,
          '正在使用的配比不能删除'
        );
      }

      // 执行删除
      const deleteResult = await this.ratioRepository.deleteRatio(ratioId);

      this.logger.info('Ratio deleted successfully', { ratioId });

      return deleteResult;
    } catch (error) {
      this.logger.error('Failed to delete ratio', error as Error, { ratioId });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.SYSTEM_ERROR);
    }
  }

  async calculateRatio(params: CalculationParams): Promise<BusinessResult<CalculationResult>> {
    try {
      this.logger.info('Calculating ratio', { params });

      // 验证计算参数
      const paramValidation = await this.validateCalculationParams(params);
      if (paramValidation.isFailure()) {
        return BusinessResult.failure(
          BusinessErrorCodes.INVALID_CALCULATION_PARAMS,
          '计算参数验证失败',
          paramValidation.error
        );
      }

      // 执行计算
      const result = this.calculationEngine.calculate(params, []);

      // 分析结果质量
      const warnings: BusinessWarning[] = [];
      if (result.qualityScore < 70) {
        warnings.push(
          createBusinessWarning(
            'LOW_QUALITY_SCORE',
            `配比质量评分较低 (${result.qualityScore})，建议优化`,
            'high'
          )
        );
      }

      if (result.strengthPrediction < params.targetStrength * 0.9) {
        warnings.push(
          createBusinessWarning(
            'LOW_STRENGTH_PREDICTION',
            `预测强度 (${result.strengthPrediction}) 低于目标强度`,
            'high'
          )
        );
      }

      this.logger.info('Ratio calculation completed', {
        targetStrength: params.targetStrength,
        predictedStrength: result.strengthPrediction,
        qualityScore: result.qualityScore,
      });

      return BusinessResult.success(result, warnings.length > 0 ? warnings : undefined);
    } catch (error) {
      this.logger.error('Failed to calculate ratio', error as Error, { params });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.CALCULATION_FAILED);
    }
  }

  async reverseCalculate(
    materials: RatioMaterialModel[]
  ): Promise<BusinessResult<CalculationParams>> {
    try {
      this.logger.info('Reverse calculating parameters', { materialsCount: materials.length });

      if (materials.length === 0) {
        return BusinessResult.failure(BusinessErrorCodes.INVALID_PARAMETER, '材料列表不能为空');
      }

      // 执行反算
      const params = this.calculationEngine.reverseCalculate(materials, {} as any, 'method1');

      this.logger.info('Reverse calculation completed', { params });

      return BusinessResult.success(params);
    } catch (error) {
      this.logger.error('Failed to reverse calculate', error as Error, {
        materialsCount: materials.length,
      });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.CALCULATION_FAILED);
    }
  }

  async optimizeRatio(
    ratio: RatioModel,
    goals: OptimizationGoals
  ): Promise<BusinessResult<RatioModel>> {
    try {
      this.logger.info('Optimizing ratio', { ratioId: ratio.id, goals });

      // 验证优化目标
      const totalWeight = Object.values(goals).reduce((sum, weight) => sum + weight, 0);
      if (Math.abs(totalWeight - 1) > 0.01) {
        return BusinessResult.failure(
          BusinessErrorCodes.INVALID_PARAMETER,
          '优化目标权重总和必须等于1'
        );
      }

      // 执行优化算法
      // 这里需要实现具体的优化逻辑
      const optimizedRatio: RatioModel = {
        ...ratio,
        // 优化后的参数和材料
      };

      this.logger.info('Ratio optimization completed', {
        originalRatioId: ratio.id,
        optimizedRatioId: optimizedRatio.id,
      });

      return BusinessResult.success(optimizedRatio);
    } catch (error) {
      this.logger.error('Failed to optimize ratio', error as Error, { ratioId: ratio.id });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.CALCULATION_FAILED);
    }
  }

  async validateRatio(ratio: RatioModel): Promise<BusinessResult<RatioValidationResult>> {
    try {
      const validationResult = await this.ratioValidator.validate(ratio);

      if (!validationResult.isValid) {
        return BusinessResult.failure(
          BusinessErrorCodes.VALIDATION_FAILED,
          '配比验证失败',
          validationResult.errors
        );
      }

      const warnings = validationResult.warnings.map(w =>
        createBusinessWarning(w.code, w.message, 'medium', w.field)
      );

      return BusinessResult.success(validationResult, warnings.length > 0 ? warnings : undefined);
    } catch (error) {
      this.logger.error('Failed to validate ratio', error as Error, { ratioId: ratio.id });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.VALIDATION_FAILED);
    }
  }

  async validateCalculationParams(params: CalculationParams): Promise<BusinessResult<void>> {
    try {
      const validationResult = this.calculationEngine.validateParams(params);

      if (!validationResult.isValid) {
        return BusinessResult.failure(
          BusinessErrorCodes.INVALID_CALCULATION_PARAMS,
          '计算参数验证失败',
          validationResult.errors
        );
      }

      return BusinessResult.success(undefined);
    } catch (error) {
      this.logger.error('Failed to validate calculation params', error as Error, { params });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.VALIDATION_FAILED);
    }
  }

  async analyzeQuality(ratio: RatioModel): Promise<BusinessResult<QualityAnalysis>> {
    try {
      // 实现质量分析逻辑
      const analysis: QualityAnalysis = {
        overallScore: ratio.calculationResults?.qualityScore || 0,
        strengthAnalysis: {
          predictedStrength: ratio.calculationResults?.strengthPrediction || 0,
          targetStrength: ratio.calculationParams.targetStrength,
          confidence: 85,
          factors: [],
        },
        durabilityAnalysis: {
          durabilityIndex: 80,
          riskFactors: [],
          recommendations: [],
        },
        workabilityAnalysis: {
          workabilityIndex: 75,
          slumpPrediction: ratio.calculationParams.slump,
          pumpability: 'good',
        },
        warnings: [],
        recommendations: [],
      };

      return BusinessResult.success(analysis);
    } catch (error) {
      this.logger.error('Failed to analyze quality', error as Error, { ratioId: ratio.id });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.SYSTEM_ERROR);
    }
  }

  async analyzeCost(ratio: RatioModel): Promise<BusinessResult<CostAnalysis>> {
    try {
      // 实现成本分析逻辑
      const analysis: CostAnalysis = {
        totalCost: ratio.calculationResults?.costEstimate || 0,
        costPerCubicMeter: 0,
        materialCosts: [],
        costOptimizationPotential: 0,
        recommendations: [],
      };

      return BusinessResult.success(analysis);
    } catch (error) {
      this.logger.error('Failed to analyze cost', error as Error, { ratioId: ratio.id });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.SYSTEM_ERROR);
    }
  }

  async analyzeEnvironmental(ratio: RatioModel): Promise<BusinessResult<EnvironmentalAnalysis>> {
    try {
      // 实现环保分析逻辑
      const analysis: EnvironmentalAnalysis = {
        carbonFootprint: ratio.calculationResults?.carbonFootprint || 0,
        materialImpacts: [],
        sustainabilityScore: 0,
        recommendations: [],
      };

      return BusinessResult.success(analysis);
    } catch (error) {
      this.logger.error('Failed to analyze environmental impact', error as Error, {
        ratioId: ratio.id,
      });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.SYSTEM_ERROR);
    }
  }

  async compareRatios(ratioIds: string[]): Promise<BusinessResult<RatioComparison>> {
    try {
      // 实现配比比较逻辑
      const comparison: RatioComparison = {
        ratios: [],
        summary: {
          bestForCost: '',
          bestForStrength: '',
          bestForEnvironment: '',
          bestOverall: '',
        },
        recommendations: [],
      };

      return BusinessResult.success(comparison);
    } catch (error) {
      this.logger.error('Failed to compare ratios', error as Error, { ratioIds });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.SYSTEM_ERROR);
    }
  }

  async recommendOptimizations(
    ratio: RatioModel
  ): Promise<BusinessResult<OptimizationRecommendation[]>> {
    try {
      // 实现优化推荐逻辑
      const recommendations: OptimizationRecommendation[] = [];

      return BusinessResult.success(recommendations);
    } catch (error) {
      this.logger.error('Failed to recommend optimizations', error as Error, { ratioId: ratio.id });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.SYSTEM_ERROR);
    }
  }

  async recommendMaterials(
    params: CalculationParams
  ): Promise<BusinessResult<MaterialRecommendation[]>> {
    try {
      // 实现材料推荐逻辑
      const recommendations: MaterialRecommendation[] = [];

      return BusinessResult.success(recommendations);
    } catch (error) {
      this.logger.error('Failed to recommend materials', error as Error, { params });
      return BusinessResult.fromError(error as Error, BusinessErrorCodes.SYSTEM_ERROR);
    }
  }
}

// 依赖接口
interface ILogger {
  debug(message: string, meta?: any): void;
  info(message: string, meta?: any): void;
  error(message: string, error: Error, meta?: any): void;
}
