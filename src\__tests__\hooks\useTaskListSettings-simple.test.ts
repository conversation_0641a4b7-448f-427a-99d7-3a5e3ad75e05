/**
 * useTaskListSettings简单测试 - 验证无限循环是否修复
 */

import { renderHook } from '@testing-library/react';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock toast
const mockToast = jest.fn();
jest.mock('@/shared/hooks/use-toast', () => ({
  useToast: () => ({ toast: mockToast }),
}));

// Mock the task columns config that useTaskListSettings depends on
jest.mock('@/components/sections/task-list/task-list.config', () => ({
  ALL_TASK_COLUMNS_CONFIG: [
    { id: 'taskNumber', order: 1, isMandatory: true, defaultVisible: true, defaultWidth: 100 },
    { id: 'projectName', order: 2, defaultVisible: true, defaultWidth: 150 },
  ],
}));

describe('useTaskListSettings - 无限循环修复测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  it('应该能够正常渲染而不产生无限循环', async () => {
    // 这个测试的目的是确保hook可以正常渲染而不会导致无限循环
    // 如果有无限循环，这个测试会超时或抛出"Maximum update depth exceeded"错误

    const { useTaskListSettings } = await import(
      '@/features/task-management/hooks/useTaskListSettings'
    );

    expect(() => {
      renderHook(() => useTaskListSettings());
    }).not.toThrow();
  });

  it('应该在初始加载时不触发localStorage保存', async () => {
    const { useTaskListSettings } = await import(
      '@/features/task-management/hooks/useTaskListSettings'
    );

    renderHook(() => useTaskListSettings());

    // 等待一个tick让useEffect执行
    await new Promise(resolve => setTimeout(resolve, 0));

    // 初始加载时不应该调用setItem
    expect(mockLocalStorage.setItem).not.toHaveBeenCalled();
  });
});
