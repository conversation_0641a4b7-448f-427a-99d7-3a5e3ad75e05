'use client';

import React, { useEffect, useState } from 'react';

import { <PERSON><PERSON><PERSON><PERSON>, Printer, Trash2 } from 'lucide-react';

import { Button } from '@/shared/components/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/table';
// 导入统一的配比数据
import { mockMaterialNames } from '@/infrastructure/api/mock/ratio-mock-data';
import type { MaterialName } from '@/core/types';

// 材料类型定义
type MaterialType = 'Aggregate' | 'Powder' | 'Admixture' | 'Binder' | 'Water';

interface MaterialNameManagementModalProps {
  isOpen: boolean;
  onOpenChangeAction: (open: boolean) => void;
}

export const MaterialNameManagementModal: React.FC<MaterialNameManagementModalProps> = ({
  isOpen,
  onOpenChangeAction,
}) => {
  const [materials, setMaterials] = useState<MaterialName[]>([]);

  useEffect(() => {
    if (isOpen) {
      setMaterials(mockMaterialNames);
    }
  }, [isOpen]);

  const handleAddRow = () => {
    const newMaterial: MaterialName = {
      id: `new_${Date.now()}`,
      name: '',
      order: materials.length + 1,
      type: 'Powder',
    };
    setMaterials([...materials, newMaterial]);
  };

  const handleUpdate = (id: string, field: keyof MaterialName, value: string | number) => {
    setMaterials(materials.map(m => (m.id === id ? { ...m, [field]: value } : m)));
  };

  const handleDelete = (id: string) => {
    setMaterials(materials.filter(m => m.id !== id));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChangeAction}>
      <DialogContent className='max-w-3xl'>
        <DialogHeader>
          <DialogTitle>材料名称管理</DialogTitle>
        </DialogHeader>
        <div className='py-4 max-h-[60vh] overflow-y-auto'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className='w-[80px]'>序号</TableHead>
                <TableHead>料名</TableHead>
                <TableHead className='w-[150px]'>类型</TableHead>
                <TableHead className='w-[100px]'>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {materials.map(mat => (
                <TableRow key={mat.id}>
                  <TableCell>
                    <Input
                      type='number'
                      value={mat.order}
                      onChange={e => handleUpdate(mat.id, 'order', parseInt(e.target.value) || 0)}
                      className='h-8'
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      value={mat.name}
                      onChange={e => handleUpdate(mat.id, 'name', e.target.value)}
                      className='h-8'
                    />
                  </TableCell>
                  <TableCell>
                    <Select
                      value={mat.type}
                      onValueChange={(value: MaterialType) => handleUpdate(mat.id, 'type', value)}
                    >
                      <SelectTrigger className='h-8'>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='Aggregate'>骨料</SelectItem>
                        <SelectItem value='Powder'>粉料</SelectItem>
                        <SelectItem value='Admixture'>外加剂</SelectItem>
                        <SelectItem value='Binder'>胶料</SelectItem>
                        <SelectItem value='Water'>水</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Button variant='ghost' size='icon' onClick={() => handleDelete(mat.id)}>
                      <Trash2 className='h-4 w-4 text-destructive' />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <DialogFooter className='justify-between'>
          <div>
            <Button variant='outline' size='sm' onClick={handleAddRow} className='gap-1'>
              <PlusCircle className='h-4 w-4' />
              增加
            </Button>
          </div>
          <div className='flex gap-2'>
            <Button variant='outline' size='sm' className='gap-1'>
              <Printer className='h-4 w-4' />
              打印
            </Button>
            <Button variant='outline' size='sm' onClick={() => onOpenChangeAction(false)}>
              退出
            </Button>
            <Button size='sm'>保存</Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
