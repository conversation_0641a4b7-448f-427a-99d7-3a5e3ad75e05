'use client';

/**
 * 统一的配比管理Hook
 * 为新旧版本配比页面提供一致的业务逻辑
 */

import { useCallback, useEffect, useMemo, useState } from 'react';
import type {
  UnifiedRatioMaterial,
  LegacyRatioMaterial,
  RatioCalculationParams,
  CalculationResults,
} from '@/core/types/ratio';
import type { CalculateRequest, ReverseCalculateRequest } from '@/core/types/ratio-api';
import { ratioApiService } from '@/features/ratio-management/services/ratio/ratioApiService';
import {
  MaterialCategory,
  ExposureClass,
  PlacementMethod,
  FinishingRequirement,
} from '@/core/types/ratio';
import {
  ratioMaterialConverter,
  convertLegacyMaterialsToUnified,
  convertUnifiedMaterialsToLegacy,
  updateMaterialActualAmount,
} from '@/core/utils/ratio-converter';
import { useSiloManagement } from '@/features/ratio-management/hooks/useSiloManagement';

// 辅助函数：根据材料名称获取材料类别
function getMaterialCategory(materialName: string): MaterialCategory {
  const categoryMapping: Record<string, MaterialCategory> = {
    水: MaterialCategory.WATER,
    水泥: MaterialCategory.CEMENTITIOUS,
    粉煤灰: MaterialCategory.SUPPLEMENTARY,
    矿粉: MaterialCategory.SUPPLEMENTARY,
    砂: MaterialCategory.AGGREGATE,
    石子: MaterialCategory.AGGREGATE,
    外加剂: MaterialCategory.ADMIXTURE,
    防冻剂: MaterialCategory.ADMIXTURE,
    S105: MaterialCategory.ADMIXTURE,
    膨胀剂: MaterialCategory.SPECIAL_ADDITIVE,
    早强剂: MaterialCategory.SPECIAL_ADDITIVE,
  };

  return categoryMapping[materialName] || MaterialCategory.CEMENTITIOUS;
}

// 辅助函数：根据材料名称获取密度
function getMaterialDensity(materialName: string): number {
  const densityMapping: Record<string, number> = {
    水: 1.0,
    水泥: 3.1,
    粉煤灰: 2.2,
    矿粉: 2.9,
    砂: 2.65,
    石子: 2.7,
    外加剂: 1.1,
    防冻剂: 1.2,
    S105: 1.1,
    膨胀剂: 2.8,
    早强剂: 1.3,
  };

  return densityMapping[materialName] || 2.4;
}

export interface UnifiedRatioManagementOptions {
  taskId: string;
  autoLoad?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
  compatibilityMode?: 'legacy' | 'unified'; // 兼容模式
}

export interface UnifiedRatioManagementReturn {
  // 数据状态（统一格式）
  currentRatio: any;
  selectedMaterials: UnifiedRatioMaterial[];
  calculationParams: RatioCalculationParams | null;
  calculationResult: CalculationResults | null;

  // UI状态
  isLoading: boolean;
  isCalculating: boolean;
  isSaving: boolean;
  error: string | null;
  isDirty: boolean;
  hasParamsChanged: boolean; // 新增：参数是否已修改但未重新计算

  // 配比操作
  loadRatio: (taskId: string) => Promise<void>;
  saveRatio: () => Promise<void>;
  clearRatio: () => void;

  // 材料操作（统一接口）
  addMaterial: (material: UnifiedRatioMaterial) => void;
  removeMaterial: (materialId: string) => void;
  updateMaterial: (materialId: string, updates: Partial<UnifiedRatioMaterial>) => void;

  // 兼容性方法（用于旧版页面）
  addLegacyMaterial: (material: LegacyRatioMaterial) => void;
  getLegacyMaterials: () => LegacyRatioMaterial[];
  updateLegacyMaterial: (materialId: string, updates: Partial<LegacyRatioMaterial>) => void;

  // 计算操作
  updateCalculationParams: (params: Partial<RatioCalculationParams>) => void;
  calculateRatio: () => Promise<void>;
  reverseCalculate: () => Promise<void>;

  // UI操作
  setCurrentView: (view: string) => void;
  toggleDesignPanel: () => void;
  showCalculationResults: () => void;
  hideCalculationResults: () => void;

  // 工具方法
  canSave: boolean;
  canCalculate: boolean;
  canReverseCalculate: boolean;
  hasUnsavedChanges: boolean;
}

/**
 * 统一的配比管理Hook
 * 提供新旧版本一致的业务逻辑
 */
export function useUnifiedRatioManagement(
  options: UnifiedRatioManagementOptions
): UnifiedRatioManagementReturn {
  const {
    taskId,
    autoLoad = true,
    autoSave = false,
    autoSaveDelay = 3000,
    compatibilityMode = 'unified',
  } = options;

  // 料仓管理
  const { checkSiloExists } = useSiloManagement();

  // 核心状态
  const [currentRatio, setCurrentRatio] = useState<any>(null);
  const [selectedMaterials, setSelectedMaterials] = useState<UnifiedRatioMaterial[]>([]);
  const [calculationParams, setCalculationParams] = useState<RatioCalculationParams>({
    // 基本参数
    targetStrength: 30,
    slump: 180,
    maxAggregateSize: 25,
    exposureClass: 'XC1' as ExposureClass,

    // 环境参数
    ambientTemperature: 20,
    relativeHumidity: 60,
    cementTemperature: 20,
    aggregateTemperature: 20,

    // 材料选择
    selectedMaterials: [],
    cementType: 'P.O 42.5',
    aggregateType: '碎石',
    waterType: '自来水',

    // 配比参数
    waterCementRatio: 0.45,
    sandRatio: 35,
    cementContent: 400,
    waterContent: 180,

    // 外加剂参数
    additiveRatio: 1.2,
    flyashRatio: 20,
    mineralPowderRatio: 10,
    silicaFumeRatio: 5,
    antifreezeRatio: 0,
    expansionRatio: 0,

    // 数量参数
    cementAmount: 400,
    waterAmount: 180,
    density: 2400,
    airContent: 4.5,
    strengthGrade: 30,
    ultraFineSandRatio: 0,
    earlyStrengthRatio: 0,

    // 施工参数
    placementMethod: PlacementMethod.PUMP,
    finishingRequirement: FinishingRequirement.SMOOTH,
    cureConditions: {
      method: '自然养护',
      duration: 28,
      temperature: 20,
      humidity: 95,
    },

    // 外加剂用量（具体数值）
    admixtureAmount: 4.8,
    antifreezeAmount: 0,
    flyAshAmount: 80,
    mineralPowderAmount: 40,
    s105PowderAmount: 0,
    expansionAgentAmount: 0,
    earlyStrengthAgentAmount: 0,
    ultraFineSandAmount: 0,
  });
  const [calculationResult, setCalculationResult] = useState<CalculationResults | null>(null);

  // UI状态
  const [isLoading, setIsLoading] = useState(false);
  const [isCalculating, setIsCalculating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDirty, setIsDirty] = useState(false);
  const [hasParamsChanged, setHasParamsChanged] = useState(false);
  const [currentView, setCurrentView] = useState('design');
  const [showResults, setShowResults] = useState(false);

  // 配比操作
  const loadRatio = useCallback(
    async (taskId: string) => {
      setIsLoading(true);
      setError(null);

      try {
        // 模拟加载配比数据
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 根据兼容模式加载不同格式的数据
        if (compatibilityMode === 'legacy') {
          // 加载旧版格式数据并转换
          const legacyData = await loadLegacyRatioData(taskId);
          const unifiedMaterials = convertLegacyMaterialsToUnified(legacyData.materials || []);
          setSelectedMaterials(unifiedMaterials);
        } else {
          // 直接加载统一格式数据
          const unifiedData = await loadUnifiedRatioData(taskId);
          setSelectedMaterials(unifiedData.materials || []);
        }

        setCurrentRatio({ id: taskId, taskId });
        setIsDirty(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载配比数据失败');
      } finally {
        setIsLoading(false);
      }
    },
    [compatibilityMode]
  );

  const saveRatio = useCallback(async () => {
    setIsSaving(true);
    setError(null);

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 根据兼容模式保存不同格式的数据
      if (compatibilityMode === 'legacy') {
        const legacyMaterials = convertUnifiedMaterialsToLegacy(selectedMaterials);
        await saveLegacyRatioData(taskId, { materials: legacyMaterials });
      } else {
        await saveUnifiedRatioData(taskId, { materials: selectedMaterials });
      }

      setIsDirty(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存配比数据失败');
    } finally {
      setIsSaving(false);
    }
  }, [taskId, selectedMaterials, compatibilityMode]);

  const clearRatio = useCallback(() => {
    setSelectedMaterials([]);
    // 重置为默认值而不是 null
    setCalculationParams({
      // 基本参数
      targetStrength: 30,
      slump: 180,
      maxAggregateSize: 25,
      exposureClass: ExposureClass.XC1,

      // 环境参数
      ambientTemperature: 20,
      relativeHumidity: 60,
      cementTemperature: 20,
      aggregateTemperature: 20,

      // 材料选择
      selectedMaterials: [],
      cementType: 'P.O 42.5',
      aggregateType: '碎石',
      waterType: '自来水',

      // 配比参数
      waterCementRatio: 0.45,
      sandRatio: 35,
      cementContent: 400,
      waterContent: 180,

      // 外加剂参数
      additiveRatio: 1.2,
      flyashRatio: 20,
      mineralPowderRatio: 10,
      silicaFumeRatio: 5,
      antifreezeRatio: 0,
      expansionRatio: 0,

      // 数量参数
      cementAmount: 400,
      waterAmount: 180,
      density: 2400,
      airContent: 4.5,
      strengthGrade: 30,
      ultraFineSandRatio: 0,
      earlyStrengthRatio: 0,

      // 施工参数
      placementMethod: PlacementMethod.PUMP,
      finishingRequirement: FinishingRequirement.SMOOTH,
      cureConditions: {
        method: '自然养护',
        duration: 28,
        temperature: 20,
        humidity: 95,
      },

      // 外加剂用量（具体数值）
      admixtureAmount: 4.8,
      antifreezeAmount: 0,
      flyAshAmount: 80,
      mineralPowderAmount: 40,
      s105PowderAmount: 0,
      expansionAgentAmount: 0,
      earlyStrengthAgentAmount: 0,
      ultraFineSandAmount: 0,
    });
    setCalculationResult(null);
    setIsDirty(true);
  }, []);

  // 材料操作（统一接口）
  const addMaterial = useCallback((material: UnifiedRatioMaterial) => {
    setSelectedMaterials(prev => [...prev, material]);
    setIsDirty(true);
  }, []);

  const removeMaterial = useCallback((materialId: string) => {
    setSelectedMaterials(prev => prev.filter(m => m.id !== materialId));
    setIsDirty(true);
  }, []);

  const updateMaterial = useCallback(
    (materialId: string, updates: Partial<UnifiedRatioMaterial>) => {
      setSelectedMaterials(prev =>
        prev.map(material => {
          if (material.id === materialId) {
            const updated = { ...material, ...updates };
            // 自动更新实际用量
            return updateMaterialActualAmount(updated);
          }
          return material;
        })
      );
      setIsDirty(true);
    },
    []
  );

  // 兼容性方法（用于旧版页面）
  const addLegacyMaterial = useCallback(
    (material: LegacyRatioMaterial) => {
      const unifiedMaterial = ratioMaterialConverter.legacyToUnified(material);
      addMaterial(unifiedMaterial);
    },
    [addMaterial]
  );

  const getLegacyMaterials = useCallback((): LegacyRatioMaterial[] => {
    return convertUnifiedMaterialsToLegacy(selectedMaterials);
  }, [selectedMaterials]);

  const updateLegacyMaterial = useCallback(
    (materialId: string, updates: Partial<LegacyRatioMaterial>) => {
      // 找到对应的统一格式材料
      const currentMaterial = selectedMaterials.find(m => m.id === materialId);
      if (!currentMaterial) return;

      // 转换为旧版格式，应用更新，再转换回统一格式
      const legacyMaterial = ratioMaterialConverter.unifiedToLegacy(currentMaterial);
      const updatedLegacy = { ...legacyMaterial, ...updates };
      const updatedUnified = ratioMaterialConverter.legacyToUnified(updatedLegacy);

      updateMaterial(materialId, updatedUnified);
    },
    [selectedMaterials, updateMaterial]
  );

  // 计算操作
  const updateCalculationParams = useCallback((params: Partial<RatioCalculationParams>) => {
    setCalculationParams(prev => ({ ...prev, ...params }) as RatioCalculationParams);
    setIsDirty(true);
    setHasParamsChanged(true); // 标记参数已修改
  }, []);

  const calculateRatio = useCallback(async () => {
    setIsCalculating(true);
    setError(null);

    try {
      // 构建API请求
      const request: CalculateRequest = {
        taskId: 'task-001', // 实际应用中从props或context获取
        calculationParams,
        materials: selectedMaterials,
        calculationMethod: 'standard',
      };

      // 调用API服务
      const response = await ratioApiService.calculate(request);

      if (!response.success) {
        throw new Error(response.message || '计算失败');
      }

      const result = response.data;

      // 根据计算参数生成标准混凝土材料配比
      const calculatedMaterials: UnifiedRatioMaterial[] = [];

      // 基本材料计算（基于计算参数）
      const cementAmount = calculationParams.cementContent || 400;
      const waterAmount =
        calculationParams.waterContent ||
        cementAmount * (calculationParams.waterCementRatio || 0.45);
      const sandAmount = 650; // 标准砂用量
      const stoneAmount = 1200; // 标准石子用量

      // 添加水泥
      calculatedMaterials.push({
        id: `calc-cement-${Date.now()}`,
        materialId: 'material-cement',
        name: '水泥',
        specification: calculationParams.cementType || 'P.O 42.5',
        category: 'cement' as MaterialCategory,
        unit: 'kg',
        density: 3.1,
        theoreticalAmount: cementAmount,
        actualAmount: cementAmount,
        waterContent: 0,
        stoneContent: 0,
        designValue: cementAmount,
        siloId: checkSiloExists('水泥') ? 'silo-cement' : '',
        siloName: checkSiloExists('水泥') ? '水泥料仓' : '',
        cost: 0,
        supplier: '',
      });

      // 添加水
      calculatedMaterials.push({
        id: `calc-water-${Date.now()}`,
        materialId: 'material-water',
        name: '水',
        specification: calculationParams.waterType || '自来水',
        category: 'water' as MaterialCategory,
        unit: 'kg',
        density: 1.0,
        theoreticalAmount: waterAmount,
        actualAmount: waterAmount,
        waterContent: 0,
        stoneContent: 0,
        designValue: waterAmount,
        siloId: checkSiloExists('水') ? 'silo-water' : '',
        siloName: checkSiloExists('水') ? '水料仓' : '',
        cost: 0,
        supplier: '',
      });

      // 添加砂
      calculatedMaterials.push({
        id: `calc-sand-${Date.now()}`,
        materialId: 'material-sand',
        name: '砂',
        specification: '中砂',
        category: 'aggregate' as MaterialCategory,
        unit: 'kg',
        density: 2.65,
        theoreticalAmount: sandAmount,
        actualAmount: sandAmount,
        waterContent: 0,
        stoneContent: 0,
        designValue: sandAmount,
        siloId: checkSiloExists('砂') ? 'silo-sand' : '',
        siloName: checkSiloExists('砂') ? '砂料仓' : '',
        cost: 0,
        supplier: '',
      });

      // 添加石子
      calculatedMaterials.push({
        id: `calc-stone-${Date.now()}`,
        materialId: 'material-stone',
        name: '石子',
        specification: calculationParams.aggregateType || '碎石',
        category: 'aggregate' as MaterialCategory,
        unit: 'kg',
        density: 2.7,
        theoreticalAmount: stoneAmount,
        actualAmount: stoneAmount,
        waterContent: 0,
        stoneContent: 0,
        designValue: stoneAmount,
        siloId: checkSiloExists('石子') ? 'silo-stone' : '',
        siloName: checkSiloExists('石子') ? '石子料仓' : '',
        cost: 0,
        supplier: '',
      });

      // 添加外加剂（如果有）
      if (calculationParams.additiveRatio && calculationParams.additiveRatio > 0) {
        const additiveAmount = (cementAmount * calculationParams.additiveRatio) / 100;
        calculatedMaterials.push({
          id: `calc-additive-${Date.now()}`,
          materialId: 'material-additive',
          name: '外加剂',
          specification: '减水剂',
          category: 'admixture' as MaterialCategory,
          unit: 'kg',
          density: 1.2,
          theoreticalAmount: additiveAmount,
          actualAmount: additiveAmount,
          waterContent: 0,
          stoneContent: 0,
          designValue: additiveAmount,
          siloId: checkSiloExists('外加剂') ? 'silo-additive' : '',
          siloName: checkSiloExists('外加剂') ? '外加剂料仓' : '',
          cost: 0,
          supplier: '',
        });
      }

      // 添加粉煤灰（如果有）
      if (calculationParams.flyashRatio && calculationParams.flyashRatio > 0) {
        const flyashAmount = (cementAmount * calculationParams.flyashRatio) / 100;
        calculatedMaterials.push({
          id: `calc-flyash-${Date.now()}`,
          materialId: 'material-flyash',
          name: '粉煤灰',
          specification: 'I级',
          category: 'supplementary' as MaterialCategory,
          unit: 'kg',
          density: 2.2,
          theoreticalAmount: flyashAmount,
          actualAmount: flyashAmount,
          waterContent: 0,
          stoneContent: 0,
          designValue: flyashAmount,
          siloId: checkSiloExists('粉煤灰') ? 'silo-flyash' : '',
          siloName: checkSiloExists('粉煤灰') ? '粉煤灰料仓' : '',
          cost: 0,
          supplier: '',
        });
      }

      // 添加矿粉（如果有）
      if (calculationParams.mineralPowderRatio && calculationParams.mineralPowderRatio > 0) {
        const mineralAmount = (cementAmount * calculationParams.mineralPowderRatio) / 100;
        calculatedMaterials.push({
          id: `calc-mineral-${Date.now()}`,
          materialId: 'material-mineral',
          name: '矿粉',
          specification: 'S95',
          category: 'supplementary' as MaterialCategory,
          unit: 'kg',
          density: 2.9,
          theoreticalAmount: mineralAmount,
          actualAmount: mineralAmount,
          waterContent: 0,
          stoneContent: 0,
          designValue: mineralAmount,
          siloId: checkSiloExists('矿粉') ? 'silo-mineral' : '',
          siloName: checkSiloExists('矿粉') ? '矿粉料仓' : '',
          cost: 0,
          supplier: '',
        });
      }

      console.log('配比计算完成，生成材料列表:', calculatedMaterials);

      setSelectedMaterials(calculatedMaterials);
      setCalculationResult(result);
      setShowResults(true);
      setIsDirty(true);
      setHasParamsChanged(false); // 清除参数变化标志
    } catch (err) {
      setError(err instanceof Error ? err.message : '计算失败');
    } finally {
      setIsCalculating(false);
    }
  }, [calculationParams]);

  const reverseCalculate = useCallback(async () => {
    setIsCalculating(true);
    setError(null);

    try {
      // 构建API请求
      const request: ReverseCalculateRequest = {
        taskId: 'task-001', // 实际应用中从props或context获取
        materials: selectedMaterials,
        targetResults: {
          totalWeight: selectedMaterials.reduce((sum, m) => sum + m.designValue, 0),
          strengthPrediction: calculationParams.strengthGrade,
        },
        calculationMethod: 'standard',
      };

      // 调用API服务
      const response = await ratioApiService.reverseCalculate(request);

      if (!response.success) {
        throw new Error(response.message || '反算失败');
      }

      const reversedParams = response.data;

      // 更新计算参数
      setCalculationParams(prev => ({
        ...prev,
        waterCementRatio: reversedParams.waterCementRatio,
        sandRatio: reversedParams.sandRatio,
        cementContent: reversedParams.cementContent,
        waterContent: reversedParams.waterContent,
        additiveRatio: reversedParams.additiveRatio,
        flyashRatio: reversedParams.flyashRatio,
        mineralPowderRatio: reversedParams.mineralPowderRatio || 0,
        antifreezeRatio: reversedParams.antifreezeRatio || 0,
        expansionRatio: reversedParams.expansionRatio || 0,
        ultraFineSandRatio: reversedParams.ultraFineSandRatio || 0,
        earlyStrengthRatio: reversedParams.earlyStrengthRatio || 0,
        density: reversedParams.density,
      }));

      console.log('反算完成，参数已更新:', reversedParams);
    } catch (err) {
      setError(err instanceof Error ? err.message : '反算失败');
      console.error('配比反算失败:', err);
    } finally {
      setIsCalculating(false);
    }
  }, [selectedMaterials, calculationParams.strengthGrade]);

  // UI操作
  const toggleDesignPanel = useCallback(() => {
    setCurrentView(prev => (prev === 'design' ? 'calculation' : 'design'));
  }, []);

  const showCalculationResults = useCallback(() => {
    setShowResults(true);
  }, []);

  const hideCalculationResults = useCallback(() => {
    setShowResults(false);
  }, []);

  // 工具方法
  const canSave = useMemo(() => {
    return isDirty && selectedMaterials.length > 0 && !isSaving;
  }, [isDirty, selectedMaterials.length, isSaving]);

  const canCalculate = useMemo(() => {
    // 检查关键计算参数是否有值
    const hasValidParams =
      calculationParams &&
      (calculationParams.strengthGrade > 0 ||
        calculationParams.slump > 0 ||
        calculationParams.waterCementRatio > 0 ||
        calculationParams.sandRatio > 0 ||
        calculationParams.cementContent > 0 ||
        calculationParams.waterContent > 0);

    return hasValidParams && !isCalculating;
  }, [calculationParams, isCalculating]);

  const canReverseCalculate = useMemo(() => {
    // 反算按钮：当配比设计面板中至少有一个材料时启用
    return selectedMaterials.length > 0 && !isCalculating;
  }, [selectedMaterials.length, isCalculating]);

  const hasUnsavedChanges = useMemo(() => {
    return isDirty;
  }, [isDirty]);

  // 自动加载
  useEffect(() => {
    if (autoLoad && taskId && !currentRatio) {
      loadRatio(taskId);
    }
  }, [autoLoad, taskId, currentRatio, loadRatio]);

  // 自动保存
  useEffect(() => {
    if (!autoSave || !isDirty) return;

    const timer = setTimeout(() => {
      if (canSave) {
        saveRatio();
      }
    }, autoSaveDelay);

    return () => clearTimeout(timer);
  }, [autoSave, isDirty, canSave, saveRatio, autoSaveDelay]);

  return {
    // 数据状态
    currentRatio,
    selectedMaterials,
    calculationParams,
    calculationResult,

    // UI状态
    isLoading,
    isCalculating,
    isSaving,
    error,
    isDirty,
    hasParamsChanged,

    // 配比操作
    loadRatio,
    saveRatio,
    clearRatio,

    // 材料操作
    addMaterial,
    removeMaterial,
    updateMaterial,

    // 兼容性方法
    addLegacyMaterial,
    getLegacyMaterials,
    updateLegacyMaterial,

    // 计算操作
    updateCalculationParams,
    calculateRatio,
    reverseCalculate,

    // UI操作
    setCurrentView,
    toggleDesignPanel,
    showCalculationResults,
    hideCalculationResults,

    // 工具方法
    canSave,
    canCalculate,
    canReverseCalculate,
    hasUnsavedChanges,
  };
}

// 模拟数据加载函数
async function loadLegacyRatioData(taskId: string): Promise<{ materials: LegacyRatioMaterial[] }> {
  // 模拟旧版数据格式
  return {
    materials: [
      {
        id: '1',
        materialType: '水泥',
        spec: 'P.O 42.5',
        theoryAmount: 350,
        waterRatio: 0,
        stoneRatio: 0,
        actualAmount: 350,
        designValue: 350,
        binName: '1#水泥仓',
      },
    ],
  };
}

async function loadUnifiedRatioData(
  taskId: string
): Promise<{ materials: UnifiedRatioMaterial[] }> {
  // 模拟统一格式数据
  return {
    materials: [],
  };
}

async function saveLegacyRatioData(
  taskId: string,
  data: { materials: LegacyRatioMaterial[] }
): Promise<void> {
  console.log('保存旧版格式数据:', taskId, data);
}

async function saveUnifiedRatioData(
  taskId: string,
  data: { materials: UnifiedRatioMaterial[] }
): Promise<void> {
  console.log('保存统一格式数据:', taskId, data);
}
