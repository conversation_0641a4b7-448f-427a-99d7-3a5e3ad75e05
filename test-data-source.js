// 简单的数据源切换测试脚本
// 在浏览器控制台中运行

console.log('=== 数据源切换测试 ===');

// 测试1: 检查当前配置
console.log('1. 当前配置:');
console.log('   NODE_ENV:', process.env.NODE_ENV);
console.log('   NEXT_PUBLIC_USE_MOCK_DATA:', process.env.NEXT_PUBLIC_USE_MOCK_DATA);
console.log('   localStorage tmh_data_source:', localStorage.getItem('tmh_data_source'));

// 测试2: 测试DataSourceSwitcher
if (typeof window !== 'undefined') {
  // 动态导入模块进行测试
  import('./src/utils/dataSourceSwitcher.js')
    .then(module => {
      const { DataSourceSwitcher } = module;

      console.log('2. DataSourceSwitcher测试:');
      console.log('   当前模式:', DataSourceSwitcher.getCurrentMode());
      console.log('   配置信息:', DataSourceSwitcher.getConfigInfo());

      // 测试切换
      console.log('3. 测试切换:');
      const oldMode = DataSourceSwitcher.getCurrentMode();
      const newMode = DataSourceSwitcher.toggleMode();
      console.log('   切换前:', oldMode);
      console.log('   切换后:', newMode);

      // 切换回原来的模式
      DataSourceSwitcher.setMode(oldMode);
      console.log('   恢复为:', DataSourceSwitcher.getCurrentMode());
    })
    .catch(err => {
      console.error('导入模块失败:', err);
    });
}

// 测试3: 测试API配置
import('./src/config/api.js')
  .then(module => {
    const { getApiConfig } = module;

    console.log('4. API配置测试:');
    const config = getApiConfig();
    console.log('   API配置:', config);
    console.log('   基础URL:', config.baseUrl);
    console.log('   是否Mock模式:', config.baseUrl === '');
  })
  .catch(err => {
    console.error('导入API配置失败:', err);
  });

console.log('=== 测试完成 ===');
console.log('请检查上面的输出结果');
