// Ensure to keep other existing types and export them as well.

/**
 * 搅拌站实体接口
 *
 * 定义搅拌站的基本信息和统计数据，用于多厂区管理和生产线调度。
 *
 * @interface Plant
 * @since 1.0.0
 *
 * @example
 * ```typescript
 * const plant: Plant = {
 *   id: 'plant-001',
 *   name: '第一搅拌站',
 *   stats: { completedTasks: 150, totalTasks: 200 },
 *   productionLineCount: 4
 * };
 * ```
 */
export interface Plant {
  /**
   * 搅拌站唯一标识符
   * @type {string}
   */
  id: string;

  /**
   * 搅拌站名称
   * @type {string}
   * @example "第一搅拌站"
   */
  name: string;

  /**
   * 搅拌站统计信息
   * @type {object}
   */
  stats: {
    /**
     * 已完成任务数量
     * @type {number}
     */
    completedTasks: number;

    /**
     * 总任务数量
     * @type {number}
     */
    totalTasks: number;
  };

  /**
   * 生产线数量
   * @type {number}
   * @minimum 1
   */
  productionLineCount: number;
}
/**
 * 任务实体接口
 *
 * 定义了系统中任务的完整数据结构，包含任务基本信息、状态管理、
 * 车辆调度、消息通知等功能所需的所有字段。任务是系统的核心业务实体，
 * 代表一个具体的混凝土浇筑任务。
 *
 * @interface Task
 * @since 1.0.0
 *
 * @example
 * ```typescript
 * const task: Task = {
 *   id: 'task-001',
 *   plantId: 'plant-001',
 *   taskNumber: 'T20250107001',
 *   projectName: '某某大厦',
 *   strength: 'C30',
 *   dispatchStatus: 'New',
 *   status: 'New',
 *   // ... 其他必需字段
 * };
 * ```
 */
export interface Task {
  //发货单编号
  deliveryOrderNumber?: string;

  // 重量信息
  tareWeight?: number; // 皮重
  grossWeight?: number; // 毛重
  netWeight?: number; // 净重

  // 人员信息
  mainOperator?: string; // 主机操作
  siteDispatcher?: string; // 现场调度
  qualityInspector?: string; // 质检员
  weigher?: string; // 司磅员
  dispatcher?: string; // 调度员
  driverName?: string; // 司机姓名

  // 时间信息
  departureTime?: string; // 出车时间

  // 搅拌站信息
  mixingStation?: string; // 搅拌站

  // 坍落度信息
  slumpRange?: string; // 出机坍落度

  scheduledDate: string | undefined;
  concreteVolume: number;
  updatedAt: string | undefined;
  contractNumber: string | undefined;
  /**
   * 抗冻等级
   * @type {string}
   * @example "F50", "F100"
   */
  freezeResistance: string;

  /**
   * 抗渗等级
   * @type {string}
   * @example "P6", "P8", "P10"
   */
  impermeability: string;

  /**
   * 分配给此任务的车辆列表
   * @type {Vehicle[] | undefined}
   * @note 在数据处理过程中动态填充
   */
  vehicles: Vehicle[] | undefined;

  /**
   * 发货状态
   * @type {string}
   * @example "待发货", "发货中", "已完成"
   */
  deliveryStatus: string;

  /**
   * 发车提醒间隔（分钟）
   * @type {number}
   * @minimum 1
   * @default 30
   * @example 30
   */
  dispatchReminderMinutes: number;

  /**
   * 任务唯一标识符
   * @type {string}
   */
  id: string;

  /**
   * 所属搅拌站ID
   * @type {string}
   * @see {@link Plant.id}
   */
  plantId: string;

  /**
   * 任务编号，用于业务识别
   * @type {string}
   * @example "T20250107001"
   */
  taskNumber: string;

  /**
   * 项目名称
   * @type {string}
   * @example "某某大厦A座"
   */
  projectName: string;

  /**
   * 项目简称
   * @type {string}
   * @example "某某大厦"
   */
  projectAbbreviation: string;

  /**
   * 施工单位
   * @type {string}
   * @example "某某建筑公司"
   */
  constructionUnit: string;

  /**
   * 施工地点
   * @type {string}
   * @example "地下室B2层"
   */
  constructionSite: string;

  /**
   * 混凝土强度等级
   * @type {string}
   * @example "C30", "C35", "C40"
   */
  strength: string;

  /**
   * 浇筑方式
   * @type {string}
   * @example "泵送", "自卸", "吊斗"
   */
  pouringMethod: string;

  /**
   * 需要车辆数量
   * @type {number}
   * @minimum 1
   */
  vehicleCount: number;

  /**
   * 已完成方量（立方米）
   * @type {number}
   * @minimum 0
   */
  completedVolume: number;

  /**
   * 需求方量（立方米）
   * @type {number}
   * @minimum 0
   */
  requiredVolume: number;

  /**
   * 泵车要求
   * @type {string}
   * @example "37米泵车", "不需要泵车"
   */
  pumpTruck: string;

  /**
   * 其他要求
   * @type {string}
   * @example "早强型", "缓凝型"
   */
  otherRequirements: string;

  /**
   * 联系电话
   * @type {string}
   * @example "13800138000"
   */
  contactPhone: string;

  /**
   * 子主题（可选）
   * @type {string | undefined}
   */
  subTheme?: string | undefined;

  /**
   * 调度备注（可选）
   * @type {string | undefined}
   */
  dispatchNote?: string | undefined;

  /**
   * 供货时间
   * @type {string}
   * @example "08:30"
   */
  supplyTime: string;

  /**
   * 供货日期
   * @type {string}
   * @example "2025-01-07"
   */
  supplyDate: string;

  /**
   * 发布日期
   * @type {string}
   * @example "2025-01-06"
   */
  publishDate: string;

  /**
   * 定时信息（可选）
   * @type {string | undefined}
   * @example "00:00:00"
   */
  timing?: string | undefined;

  /**
   * 调度状态
   * @type {'New' | 'ReadyToProduce' | 'RatioSet' | 'InProgress' | 'Paused' | 'Completed' | 'Cancelled'}
   * @default 'New'
   */
  dispatchStatus:
    | 'New'
    | 'ReadyToProduce'
    | 'RatioSet'
    | 'InProgress'
    | 'Paused'
    | 'Completed'
    | 'Cancelled';

  /**
   * 任务状态
   * @type {'New' | 'ReadyToProduce' | 'RatioSet' | 'InProgress' | 'Paused' | 'Completed' | 'Cancelled'}
   * @default 'New'
   * @note 与dispatchStatus保持同步
   */
  status:
    | 'New'
    | 'ReadyToProduce'
    | 'RatioSet'
    | 'InProgress'
    | 'Paused'
    | 'Completed'
    | 'Cancelled';

  /**
   * 是否已开票（可选）
   * @type {boolean | undefined}
   * @default false
   */
  isTicketed?: boolean | undefined;

  // ==================== 发车提醒相关字段 ====================
  // 由store/worker管理的发车提醒功能字段

  /**
   * 发车频率（分钟）
   * @type {number | undefined}
   * @minimum 1
   * @example 30
   * @note 由系统自动管理，用于计算发车提醒时间
   */
  dispatchFrequencyMinutes?: number | undefined;

  /**
   * 上次发车时间
   * @type {string | undefined}
   * @format ISO 8601 datetime string
   * @example "2025-01-07T08:30:00.000Z"
   * @note 由worker自动更新
   */
  lastDispatchTime?: string | undefined;

  /**
   * 下次计划发车时间
   * @type {string | undefined}
   * @format ISO 8601 datetime string
   * @example "2025-01-07T09:00:00.000Z"
   * @note 由worker/store自动计算
   */
  nextScheduledDispatchTime?: string | undefined;

  /**
   * 是否到达发车提醒时间
   * @type {boolean | undefined}
   * @default false
   * @note 在提醒时间窗口内为true，由worker/store自动计算
   */
  isDueForDispatch?: boolean | undefined;

  /**
   * 默认发车频率（分钟）
   * @type {number | undefined}
   * @minimum 1
   * @default 30
   * @note 用于未配置发车频率的任务
   */
  defaultDispatchFrequencyMinutes?: number | undefined;

  /**
   * 距离下次发车的分钟数
   * @type {number | undefined}
   * @minimum 0
   * @note 用于多级提醒功能
   */
  minutesToDispatch?: number | undefined;

  // ==================== 扩展业务字段 ====================

  /**
   * 施工公司
   * @type {string | undefined}
   * @example "某某建设集团"
   */
  constructionCompany?: string | undefined;

  /**
   * 施工位置
   * @type {string | undefined}
   * @example "主楼15层"
   */
  constructionLocation?: string | undefined;

  /**
   * 生产线数量
   * @type {number | undefined}
   * @minimum 1
   */
  productionLineCount?: number | undefined;

  /**
   * 客户名称
   * @type {string | undefined}
   * @example "某某房地产公司"
   */
  customerName?: string | undefined;

  /**
   * 详细地址
   * @type {string | undefined}
   * @example "北京市朝阳区某某街道123号"
   */
  address?: string | undefined;

  /**
   * 备注信息
   * @type {string | undefined}
   * @example "注意安全，现场有其他施工"
   */
  notes?: string | undefined;

  /**
   * 备注信息（别名）
   * @type {string | undefined}
   * @note 与notes字段功能相同，用于兼容不同数据源
   */
  remarks?: string | undefined;

  /**
   * 创建时间
   * @type {string | undefined}
   * @format ISO 8601 datetime string
   * @example "2025-01-06T10:00:00.000Z"
   */
  createdAt?: string | undefined;

  /**
   * 计划时间
   * @type {string | undefined}
   * @example "09:00"
   */
  scheduledTime?: string | undefined;

  /**
   * 计划开始时间
   * @type {string | undefined}
   * @format ISO 8601 datetime string
   * @example "2025-01-07T09:00:00.000Z"
   */
  plannedStartTime?: string | undefined;

  /**
   * 预计时长（分钟）
   * @type {number | undefined}
   * @minimum 1
   * @example 120
   */
  estimatedDuration?: number | undefined;

  /**
   * 运输距离（公里）
   * @type {number | undefined}
   * @minimum 0
   * @example 15.5
   */
  transportDistance?: number | undefined;

  /**
   * 联系人
   * @type {string | undefined}
   * @example "张工"
   */
  contactPerson?: string | undefined;

  // ==================== 消息通知相关字段 ====================

  /**
   * 未读消息数量
   * @type {number | undefined}
   * @minimum 0
   * @default 0
   */
  unreadMessageCount?: number | undefined;

  /**
   * 是否有新消息
   * @type {boolean | undefined}
   * @default false
   */
  hasNewMessages?: boolean | undefined;

  /**
   * 任务消息列表
   * @type {TaskMessage[] | undefined}
   * @see {@link TaskMessage}
   */
  messages?: TaskMessage[] | undefined;
}

/**
 * 发货单状态类型
 *
 * 定义发货单在整个流程中的状态变化。
 *
 * - `newlyDispatched`: 新调度
 * - `inProduction`: 生产中
 * - `weighed`: 已称重
 * - `shipped`: 已发货
 *
 * @since 1.0.0
 */
export type DeliveryOrderStatus = 'newlyDispatched' | 'inProduction' | 'weighed' | 'shipped';

/**
 * 任务消息接口
 *
 * 定义任务相关的消息通知数据结构，支持不同优先级和类型的消息，
 * 用于工地与调度中心之间的沟通。
 *
 * @interface TaskMessage
 * @since 2.0.0
 *
 * @example
 * ```typescript
 * const message: TaskMessage = {
 *   id: 'msg-001',
 *   taskId: 'task-001',
 *   content: '现场准备就绪，可以发车',
 *   sender: '张工',
 *   timestamp: '2025-01-07T08:30:00.000Z',
 *   isRead: false,
 *   priority: 'normal',
 *   type: 'info'
 * };
 * ```
 */
export interface TaskMessage {
  /**
   * 消息唯一标识符
   * @type {string}
   */
  id: string;

  /**
   * 关联的任务ID
   * @type {string}
   * @see {@link Task.id}
   */
  taskId: string;

  /**
   * 消息内容
   * @type {string}
   * @example "现场准备就绪，可以发车"
   */
  content: string;

  /**
   * 发送者
   * @type {string}
   * @example "张工", "某某工地"
   * @note 可以是工地名称或联系人姓名
   */
  sender: string;

  /**
   * 发送时间
   * @type {string}
   * @format ISO 8601 datetime string
   * @example "2025-01-07T08:30:00.000Z"
   */
  timestamp: string;

  /**
   * 是否已读
   * @type {boolean}
   * @default false
   */
  isRead: boolean;

  /**
   * 消息优先级
   * @type {'low' | 'normal' | 'high' | 'urgent'}
   * @default 'normal'
   */
  priority: 'low' | 'normal' | 'high' | 'urgent';

  /**
   * 消息类型
   * @type {'info' | 'warning' | 'urgent' | 'question'}
   * @default 'info'
   */
  type: 'info' | 'warning' | 'urgent' | 'question';
}

/**
 * 已出厂车辆接口
 *
 * 定义已出厂车辆的完整信息，包含车辆行程时间、司机信息等。
 * 用于已出厂车辆表格的数据展示。
 *
 * @interface DispatchedVehicle
 * @since 1.0.0
 *
 * @example
 * ```typescript
 * const dispatchedVehicle: DispatchedVehicle = {
 *   id: 'vehicle-001',
 *   vehicleNumber: '京A12345',
 *   driver: '李师傅',
 *   departureTime: '2025-01-07T08:30:00.000Z',
 *   arrivalTime: '2025-01-07T09:15:00.000Z',
 *   returnTime: '2025-01-07T10:30:00.000Z',
 *   deliveryOrderNumber: 'DO20250107001',
 *   isRoundTrip: true
 * };
 * ```
 */
export interface DispatchedVehicle {
  /**
   * 车辆唯一标识符
   * @type {string}
   */
  id: string;

  /**
   * 车辆号码
   * @type {string}
   * @example "京A12345"
   */
  vehicleNumber: string;

  /**
   * 司机姓名
   * @type {string}
   * @example "李师傅"
   */
  driver: string;

  /**
   * 出站时间
   * @type {string}
   * @format ISO 8601 datetime string
   * @example "2025-01-07T08:30:00.000Z"
   */
  departureTime: string;

  /**
   * 到工地时间（可选）
   * @type {string | undefined}
   * @format ISO 8601 datetime string
   * @example "2025-01-07T09:15:00.000Z"
   */
  arrivalTime?: string;

  /**
   * 返程时间（可选）
   * @type {string | undefined}
   * @format ISO 8601 datetime string
   * @example "2025-01-07T10:30:00.000Z"
   */
  returnTime?: string;

  /**
   * 去程时长（分钟）
   * @type {number | undefined}
   * @example 45
   */
  outboundDuration?: number;

  /**
   * 工地时长（分钟）
   * @type {number | undefined}
   * @example 75
   */
  siteStayDuration?: number;

  /**
   * 返程时长（分钟）
   * @type {number | undefined}
   * @example 60
   */
  returnDuration?: number;

  /**
   * 总时长（分钟）
   * @type {number | undefined}
   * @example 180
   */
  totalDuration?: number;

  /**
   * 发货单编号
   * @type {string}
   * @example "DO20250107001"
   */
  deliveryOrderNumber: string;

  /**
   * 是否为往返行程
   * @type {boolean}
   * @default false
   */
  isRoundTrip: boolean;

  /**
   * 车辆类型（可选）
   * @type {'Tanker' | 'Pump' | 'Other' | undefined}
   */
  vehicleType?: 'Tanker' | 'Pump' | 'Other';

  /**
   * 项目名称（可选）
   * @type {string | undefined}
   */
  projectName?: string;

  /**
   * 任务编号（可选）
   * @type {string | undefined}
   */
  taskNumber?: string;

  /**
   * 备注信息（可选）
   * @type {string | undefined}
   */
  notes?: string;
}

/**
 * 发货单接口
 *
 * 定义发货单的完整信息，包含车辆、司机、货物等详细信息。
 * 发货单是连接任务和实际发货的重要业务实体。
 *
 * @interface DeliveryOrder
 * @since 1.0.0
 *
 * @example
 * ```typescript
 * const deliveryOrder: DeliveryOrder = {
 *   id: 'order-001',
 *   plantId: 'plant-001',
 *   productionLineId: 'line-001',
 *   vehicleNumber: '京A12345',
 *   driver: '李师傅',
 *   volume: 10.5,
 *   strength: 'C30',
 *   projectName: '某某大厦',
 *   taskNumber: 'T20250107001',
 *   status: 'inProduction'
 * };
 * ```
 */
export interface DeliveryOrder {
  /**
   * 发货单唯一标识符
   * @type {string}
   */
  id: string;

  /**
   * 所属搅拌站ID
   * @type {string}
   * @see {@link Plant.id}
   */
  plantId: string;

  /**
   * 生产线ID
   * @type {string}
   */
  productionLineId: string;

  /**
   * 车辆号码
   * @type {string}
   * @example "京A12345"
   */
  vehicleNumber: string;

  /**
   * 车辆状态图标（可选）
   * @type {string | undefined}
   * @example "🚛", "🔄"
   */
  vehicleStatusIcon?: string;

  /**
   * 司机姓名
   * @type {string}
   * @example "李师傅"
   */
  driver: string;

  /**
   * 货物体积（立方米）
   * @type {number}
   * @minimum 0
   * @example 10.5
   */
  volume: number;

  /**
   * 混凝土强度等级
   * @type {string}
   * @example "C30", "C35"
   */
  strength: string;

  /**
   * 项目名称
   * @type {string}
   * @example "某某大厦"
   */
  projectName: string;

  /**
   * 任务编号
   * @type {string}
   * @see {@link Task.taskNumber}
   */
  taskNumber: string;

  /**
   * 发货单状态（可选）
   * @type {DeliveryOrderStatus | undefined}
   * @see {@link DeliveryOrderStatus}
   */
  status?: DeliveryOrderStatus;

  /**
   * 客户名称（可选）
   * @type {string | undefined}
   * @example "某某房地产公司"
   */
  customerName?: string;

  /**
   * 备注信息（可选）
   * @type {string | undefined}
   * @example "注意保温"
   */
  notes?: string;
}

/**
 * 车辆接口
 *
 * 定义车辆的完整信息，包含车辆基本信息、状态管理、任务分配等。
 * 车辆是系统中的核心资源，负责混凝土的运输和配送。
 *
 * @interface Vehicle
 * @since 1.0.0
 *
 * @example
 * ```typescript
 * const vehicle: Vehicle = {
 *   id: 'vehicle-001',
 *   vehicleNumber: '京A12345',
 *   status: 'pending',
 *   type: 'Tanker',
 *   operationalStatus: 'normal',
 *   assignedTaskId: 'task-001',
 *   isDragging: false
 * };
 * ```
 */
export interface Vehicle {
  driverName?: string;
  /**
   * 是否正在拖拽状态（可选）
   * @type {unknown}
   * @note 用于拖拽操作的状态管理
   * @default false
   */
  isDragging?: unknown;

  /**
   * 车辆唯一标识符
   * @type {string}
   */
  id: string;

  /**
   * 车辆号码
   * @type {string}
   * @example "京A12345"
   */
  vehicleNumber: string;

  /**
   * 车辆当前状态
   * @type {'pending' | 'returned' | 'outbound'}
   * - `pending`: 待命
   * - `returned`: 已返回
   * - `outbound`: 出车中
   */
  status: 'pending' | 'returned' | 'outbound';

  /**
   * 车辆类型
   * @type {'Tanker' | 'Pump' | 'Other'}
   * - `Tanker`: 罐车
   * - `Pump`: 泵车
   * - `Other`: 其他类型
   */
  type: 'Tanker' | 'Pump' | 'Other';

  /**
   * 运营状态（可选）
   * @type {'normal' | 'paused' | 'deactivated' | undefined}
   * - `normal`: 正常
   * - `paused`: 暂停
   * - `deactivated`: 停用
   * @default 'normal'
   */
  operationalStatus?: 'normal' | 'paused' | 'deactivated' | undefined;

  /**
   * 分配的任务ID（可选）
   * @type {string | undefined}
   * @see {@link Task.id}
   */
  assignedTaskId?: string | undefined;

  /**
   * 分配的生产线ID（可选）
   * @type {string | undefined}
   */
  assignedProductionLineId?: string | undefined;

  /**
   * 当前行程类型（可选）
   * @type {'outboundLeg' | 'returnLeg' | undefined}
   * - `outboundLeg`: 出程
   * - `returnLeg`: 返程
   */
  currentTripType?: 'outboundLeg' | 'returnLeg' | undefined;

  /**
   * 生产状态（可选）
   * @type {'queued' | 'producing' | 'produced' | 'weighed' | 'ticketed' | 'shipped' | undefined}
   * - `queued`: 排队中
   * - `producing`: 生产中
   * - `produced`: 已生产
   * - `weighed`: 已称重
   * - `ticketed`: 已开票
   * - `shipped`: 已发货
   */
  productionStatus?:
    | 'queued'
    | 'producing'
    | 'produced'
    | 'weighed'
    | 'ticketed'
    | 'shipped'
    | undefined;

  /**
   * 是否允许称重室编辑（可选）
   * @type {boolean | undefined}
   * @default false
   */
  allowWeighRoomEdit?: boolean | undefined;

  /**
   * 关联的发货单ID（可选）
   * @type {string | undefined}
   * @see {@link DeliveryOrder.id}
   */
  deliveryOrderId?: string | undefined;

  /**
   * 所属搅拌站ID（可选）
   * @type {string | undefined}
   * @see {@link Plant.id}
   */
  plantId?: string | undefined;

  /**
   * 上次行程是否用泵水清洗（可选）
   * @type {boolean | undefined}
   * @default false
   * @note 用于清洗记录管理
   */
  lastTripWashedWithPumpWater?: boolean | undefined;
}

export interface InTaskVehicleCardStyle {
  gap: number;
  cardSize: string;
  cardWidth: 'w-8' | 'w-9' | 'w-10' | 'w-12' | 'w-14' | 'w-16' | 'w-20' | 'w-24';
  cardHeight: 'h-7' | 'h-8' | 'h-9' | 'h-10';
  fontSize: 'text-[8px]' | 'text-[9px]' | 'text-[10px]' | 'text-[11px]' | 'text-[12px]';
  fontColor: string;
  vehicleNumberFontWeight: 'font-normal' | 'font-medium' | 'font-semibold' | 'font-bold';
  cardBgColor: string;
  cardGradient?: string;
  // 新增渐变配置
  gradientEnabled?: boolean;
  gradientDirection?:
    | 'to-r'
    | 'to-l'
    | 'to-t'
    | 'to-b'
    | 'to-tr'
    | 'to-tl'
    | 'to-br'
    | 'to-bl'
    | 'radial';
  gradientStartColor?: string;
  gradientEndColor?: string;
  statusDotSize: 'w-0.5 h-0.5' | 'w-[3px] h-[3px]' | 'w-1 h-1' | 'w-[5px] h-[5px]' | 'w-1.5 h-1.5';
  borderRadius: string;
  boxShadow: string;
  vehiclesPerRow?: 2 | 3 | 4 | 5 | 6 | 7 | 8;
  className?: string;
  style?: React.CSSProperties;
}

export type TaskColumnId =
  | 'dispatchReminder'
  | 'taskNumber'
  | 'projectName'
  | 'projectAbbreviation'
  | 'constructionUnit'
  | 'constructionSite'
  | 'strength'
  | 'pouringMethod'
  | 'vehicleCount'
  | 'completedProgress'
  | 'requiredVolume'
  | 'completedVolume'
  | 'pumpTruck'
  | 'otherRequirements'
  | 'contactPhone'
  | 'supplyTime'
  | 'supplyDate'
  | 'publishDate'
  | 'timing'
  | 'messages'
  | 'dispatchedVehicles'
  | 'productionLines'
  | 'dispatchStatus'
  | 'constructionCompany'
  | 'constructionLocation'
  | 'deliveryStatus'
  | 'plantId';

/**
 * 任务分组配置接口
 * 定义任务列表的分组显示方式
 */
export interface TaskGroupConfig {
  /** 分组字段 */
  groupBy: TaskColumnId | 'none';
  /** 是否启用分组 */
  enabled: boolean;
  /** 分组是否可折叠 */
  collapsible: boolean;
  /** 默认折叠的分组 */
  defaultCollapsed: string[];
  /** 分组排序方式 */
  sortOrder: 'asc' | 'desc';
  /** 是否显示分组统计 */
  showGroupStats: boolean;
  /** 可分组的列 */
  allowedGroupColumns: TaskColumnId[];
  /** 禁止分组的列 */
  disallowedGroupColumns: TaskColumnId[];
  /** 分组样式配置 */
  groupHeaderStyle: {
    backgroundColor: string;
    textColor: string;
    fontSize: 'text-sm' | 'text-base' | 'text-lg';
    fontWeight: 'font-normal' | 'font-medium' | 'font-semibold' | 'font-bold';
    padding: 'py-1' | 'py-2' | 'py-3';
  };
}

/**
 * 任务分组统计信息
 */
export interface TaskGroupStats {
  /** 总任务数 */
  total: number;
  /** 已分派任务数 */
  dispatched: number;
  /** 配送中任务数 */
  inDelivery: number;
  /** 已完成任务数 */
  completed: number;
  /** 待处理任务数 */
  pending: number;
}

/**
 * 任务分组数据结构
 */
export interface TaskGroup {
  /** 分组键值 */
  key: string;
  /** 分组显示名称 */
  label: string;
  /** 分组内的任务列表 */
  tasks: Task[];
  /** 是否折叠 */
  collapsed: boolean;
}

export interface CustomColumnDefinition {
  id: TaskColumnId;
  label: string;
  isMandatory?: boolean;
  defaultVisible?: boolean;
  isReorderable?: boolean;
  isResizable?: boolean;
  defaultWidth?: number;
  minWidth?: number;
  maxWidth?: number; // Added maxWidth
  isStyleable?: boolean;
  fixed?: 'left' | 'right';
  getColumnBackgroundProps?: (
    headerId: string,
    isHeader: boolean,
    isFixed: boolean
  ) => InTaskVehicleCardStyle;
  densityStyles: Record<string, string | number>;
  order?: number;
}

export type StyleableColumnId = Extract<
  TaskColumnId,
  | 'dispatchReminder'
  | 'taskNumber'
  | 'projectName'
  | 'projectAbbreviation'
  | 'constructionUnit'
  | 'constructionSite'
  | 'strength'
  | 'pouringMethod'
  | 'vehicleCount'
  | 'completedProgress'
  | 'requiredVolume'
  | 'completedVolume'
  | 'pumpTruck'
  | 'otherRequirements'
  | 'contactPhone'
  | 'supplyTime'
  | 'supplyDate'
  | 'publishDate'
  | 'timing'
  | 'messages'
  | 'dispatchedVehicles'
  | 'productionLines'
  | 'dispatchStatus'
  | 'constructionCompany'
  | 'constructionLocation'
  | 'deliveryStatus'
  | 'plantId'
  | 'vehicleId'
  | 'vehicleLicensePlate'
  | 'vehicleInternalId'
  | 'vehicleType'
  | 'vehicleStatus'
  | 'vehicleBrand'
  | 'vehicleModel'
  | 'vehicleEngineNumber'
  | 'vehicleManufactureYear'
  | 'vehicleEngineModel'
  | 'vehicleAxleCount'
  | 'vehicleTonnage'
  | 'vehicleTireCount'
  | 'vehicleTireSize'
  | 'vehicleTirePressure'
  | 'vehicleTireBrand'
  | 'createdAt'
  | 'updatedAt'
  | 'vehicleCapacity'
>;

export type ColumnTextStyle = {
  color?: string;
  fontSize?: string;
  fontWeight?: string;
};

export type ColumnTextStyles = {
  [K in StyleableColumnId]?: ColumnTextStyle;
};

export type TaskListDensityMode = 'loose' | 'normal' | 'compact';
export type TaskListDisplayMode = 'table' | 'card';
export type TaskListRowDisplayMode = 'row' | 'stacked'; // 新增：行内显示和上下显示模式

export interface DensityStyleValues {
  headerPaddingX: string;
  headerPaddingY: string;
  headerHeight: string;
  headerFontSize: string;
  cellPaddingX: string;
  cellPaddingY: string;
  cellFontSize: string;
  cellFontWeight: string;
  productionLineBoxSize: string;
  productionLineBoxFontSize: string;
  productionLineBoxNumericWidth: number;
  productionLineBoxGap: number;
  cellHorizontalPaddingNumeric: number;
}

export type VehicleDisplayMode = 'licensePlate' | 'internalId';

export type CrossPlantDispatchInfo = {
  vehicle: Vehicle | null;
  sourcePlant: Plant | null;
  targetPlant: Plant | null;
};

export interface TaskListStoredSettings {
  [x: string]: unknown;
  displayMode: TaskListDisplayMode;
  density: Exclude<TaskListDensityMode, '' | 'card'>;
  enableZebraStriping: boolean;
  columnOrder: string[];
  columnVisibility: Record<string, boolean>;
  columnWidths: Record<string, number>;
  columnTextStyles: ColumnTextStyles;
  columnBackgrounds: Record<string, string>; // Value is the 'value' from BackgroundColorOption
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  selectedPlantId: string | null; // Kept for potential direct use though AppStore also has it
  /** 车辆显示模式 */
  vehicleDisplayMode: VehicleDisplayMode;
  /** 任务分组配置 */
  groupConfig: TaskGroupConfig;
  /** 表格样式配置 */
  tableStyleConfig?: {
    headerStyle?: {
      backgroundColor?: string;
      textColor?: string;
      fontSize?: string;
      fontWeight?: string;
      borderColor?: string;
      padding?: string;
    };
    rowStyle?: {
      evenRowBg?: string;
      oddRowBg?: string;
      hoverBg?: string;
      borderColor?: string;
      textColor?: string;
      fontSize?: string;
      padding?: string;
    };
    cellAlignment?: Record<string, string>;
    columnPriority?: Record<string, string>;
    stickyColumnStyle?: {
      backgroundColor?: string;
      borderColor?: string;
      shadowRight?: string;
      zIndex?: string;
    };
  };
}

// Types for Web Worker communication
export interface ReminderWorkerInput {
  tasks: Task[]; // Consider sending a more optimized Task structure if full Task is too large
  currentTime: number;
  reminderWindowMinutes: number;
}

export interface TaskUpdateForWorker {
  // Sent from worker to main thread
  id: string;
  nextScheduledDispatchTime?: string; // ISO string or undefined
  isDueForDispatch?: boolean;
  minutesToDispatch?: number; // 添加时间差（分钟）用于多级提醒
}

export interface ReminderWorkerOutput {
  tasksWithUpdates: TaskUpdateForWorker[];
  processedTime: number; // Timestamp from worker when processing finished
  error?: string; // 错误信息
  errorDetails?: string; // 详细错误信息，如堆栈跟踪
  recovery?: boolean; // 是否为恢复消息
  lastSuccessfulRun?: { time: number; taskCount: number }; // 上次成功运行信息
  stats?: {
    // 处理统计信息
    taskCount: number; // 处理的任务总数
    updatedCount: number; // 更新的任务数量
    processingTimeMs: number; // 处理时间（毫秒）
  };
  messages?: ReminderMessage[]; // 新的提醒消息
  messageType?: 'NEW_MESSAGES' | 'ALL_MESSAGES'; // 消息类型
}

// 新增的提醒消息类型定义
export type ReminderType = 'dispatchCountdown' | 'highlight' | 'popup' | 'sound' | 'error'; // 添加错误类型

export interface ReminderMessage {
  id: string;
  type: ReminderType;
  taskId: string;
  taskNumber: string;
  title: string;
  description: string;
  time: number; // 触发时间戳
  read: boolean;
  projectName?: string;
  extra?: Record<string, unknown>; // 扩展字段，用于存储不同类型提醒可能需要的额外数据
}

export interface ReminderConfig {
  enabled: boolean;
  taskId: string;
  reminderTypes: ReminderType[];
  reminderFrequencyMinutes: number;

  // 增加多级提醒设置
  reminderLevels: {
    minutes: number; // 提前多少分钟提醒
    types: ReminderType[]; // 该级别使用哪些提醒方式
  }[];

  // 设置任务默认发车频率（如果任务本身没有设置）
  defaultDispatchFrequencyMinutes?: number;

  // 自定义设置
  customSettings?: Record<string, unknown>;

  // 持久化标识，由系统自动管理
  lastUpdated?: number; // 最后更新时间戳
}

// InTaskVehicleCard specific props
export interface InTaskVehicleCardProps {
  vehicle: Vehicle;
  task?: Task;
  vehicleDisplayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  productionLineCount?: number;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (
    vehicleId: string,
    taskId: string,
    deliveryOrderId?: string
  ) => void;
  onOpenContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task?: Task) => void;
  density?: Exclude<TaskListDensityMode, '' | 'card'>;
  attributes?: Partial<React.HTMLAttributes<HTMLDivElement>>;
  listeners?: Partial<React.HTMLAttributes<HTMLDivElement>>;
  setNodeRef?: (node: HTMLElement | null) => void;
  style?: React.CSSProperties;
  isDragging?: boolean;
  isOverlay?: boolean;
  isDispatchPanelView?: boolean;
  isTableCellView?: boolean; // New prop
}

// 配比页面相关类型 - 已迁移到 src/types/ratio.ts
// 为了向后兼容，重新导出部分类型
export type {
  LegacyRatioMaterial as RatioMaterial,
  SiloMapping,
  RatioHistoryEntry,
  RatioSelectionRecord,
  MaterialName,
  StorageLocation,
  Specification,
  AdjustmentMaterial,
  UserPermission,
  MaterialDensity,
  MortarCoefficient,
} from './ratio';

// 导出枚举
export {
  ExposureClass,
  PlacementMethod,
  FinishingRequirement,
  MaterialCategory,
  MaterialType,
  AvailabilityStatus,
} from './ratio';

// 配比计算相关类型 - 已迁移到 src/types/ratio.ts
// 为了向后兼容，重新导出部分类型
export type { RatioCalculationParams, ReverseCalculationParams, RatioResult } from './ratio';

// 旧版配比计算参数接口（保留用于向后兼容）
export interface LegacyRatioCalculationParams {
  strength: string;
  slump: number;
  impermeability?: string;
  freezeResistance?: string;
  cementType: string;
  aggregateType: string;
  waterCementRatio: number;
  sandRatio: number;
  additiveRatio?: number;
  // 新增字段以匹配实际使用
  density: number;
  waterAmount: number;
  waterToBinderRatio: number;
  flyAshAmount: number;
  mineralPowderAmount: number;
  admixtureAmount: number;
  superplasticizerAmount: number;
  cementAmount: number;
  sandAmount: number;
  gravelAmount: number;
  ultraFineSandAmount: number;
  antifreezeAmount: number;
  s105PowderAmount: number;
  expansionAgentAmount: number;
  earlyStrengthAgentAmount: number;
}

// 导出 TaskCardConfig 类型
export type { TaskCardConfig } from './taskCardConfig';

// 缺失的类型定义
export interface TaskWithVehicles extends Omit<Task, 'vehicles'> {
  vehicles?: Vehicle[];
}

export type TaskStatusFilter =
  | 'all'
  | 'New'
  | 'ReadyToProduce'
  | 'RatioSet'
  | 'InProgress'
  | 'Paused'
  | 'Completed'
  | 'Cancelled';
