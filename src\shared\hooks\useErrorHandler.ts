/**
 * 错误处理相关的React Hooks
 * 提供统一的错误处理、恢复和监控功能
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { errorManager } from '@/infrastructure/error-handling/ErrorManager';
import { AppError, ErrorCategory, ErrorContext } from '@/core/types/error';

/**
 * 基础错误处理Hook
 */
export function useErrorHandler() {
  const [error, setError] = useState<AppError | null>(null);
  const [isRecovering, setIsRecovering] = useState(false);

  const handleError = useCallback((error: Error | AppError, context?: ErrorContext) => {
    const appError = error instanceof AppError ? error : AppError.component(error.message);
    setError(appError);
    errorManager.handleError(appError, context);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
    setIsRecovering(false);
  }, []);

  const retryOperation = useCallback(
    async (operation: () => Promise<void> | void) => {
      setIsRecovering(true);
      try {
        await operation();
        clearError();
      } catch (error) {
        handleError(error as Error);
      } finally {
        setIsRecovering(false);
      }
    },
    [handleError, clearError]
  );

  return {
    error,
    isRecovering,
    handleError,
    clearError,
    retryOperation,
  };
}

/**
 * 异步操作错误处理Hook
 */
export function useAsyncError() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<AppError | null>(null);
  const retryCountRef = useRef(0);
  const maxRetries = 3;

  const execute = useCallback(
    async <T>(
      operation: () => Promise<T>,
      context?: ErrorContext,
      options?: {
        maxRetries?: number;
        retryDelay?: number;
        onSuccess?: (result: T) => void;
        onError?: (error: AppError) => void;
      }
    ): Promise<T | null> => {
      const {
        maxRetries: customMaxRetries = maxRetries,
        retryDelay = 1000,
        onSuccess,
        onError,
      } = options || {};

      setLoading(true);
      setError(null);

      for (let attempt = 0; attempt <= customMaxRetries; attempt++) {
        try {
          const result = await operation();
          retryCountRef.current = 0;
          setLoading(false);

          if (onSuccess) {
            onSuccess(result);
          }

          return result;
        } catch (err) {
          const appError =
            err instanceof AppError
              ? err
              : AppError.api('OPERATION_FAILED', (err as Error).message, {
                  attempt: attempt + 1,
                  maxRetries: customMaxRetries,
                });

          // 如果是最后一次尝试，设置错误状态
          if (attempt === customMaxRetries) {
            setError(appError);
            setLoading(false);
            retryCountRef.current = 0;

            errorManager.handleError(appError, {
              ...context,
              metadata: {
                ...context?.metadata,
                totalAttempts: attempt + 1,
                maxRetries: customMaxRetries,
              },
            });

            if (onError) {
              onError(appError);
            }

            return null;
          }

          // 等待重试延迟
          if (attempt < customMaxRetries) {
            retryCountRef.current = attempt + 1;
            await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
          }
        }
      }

      return null;
    },
    []
  );

  const retry = useCallback(() => {
    setError(null);
    retryCountRef.current = 0;
  }, []);

  return {
    loading,
    error,
    retryCount: retryCountRef.current,
    execute,
    retry,
  };
}

/**
 * 网络状态感知的错误处理Hook
 */
export function useNetworkAwareError() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const { error, handleError, clearError, retryOperation } = useErrorHandler();

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      // 如果之前有网络错误，自动清除
      if (error?.category === ErrorCategory.NETWORK) {
        clearError();
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      // 触发网络断开错误
      handleError(AppError.network('网络连接已断开'));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [error, handleError, clearError]);

  const executeWithNetworkCheck = useCallback(
    async <T>(operation: () => Promise<T>, context?: ErrorContext): Promise<T | null> => {
      if (!isOnline) {
        const networkError = AppError.network('网络连接不可用');
        handleError(networkError, context);
        return null;
      }

      try {
        return await operation();
      } catch (error) {
        // 检查是否是网络错误
        if (error instanceof TypeError && error.message.includes('fetch')) {
          handleError(AppError.network('网络请求失败'), context);
        } else {
          handleError(error as Error, context);
        }
        return null;
      }
    },
    [isOnline, handleError]
  );

  return {
    isOnline,
    error,
    executeWithNetworkCheck,
    retryOperation,
    clearError,
  };
}

/**
 * 表单验证错误处理Hook
 */
export function useFormErrorHandler<T extends Record<string, any>>() {
  const [fieldErrors, setFieldErrors] = useState<Partial<Record<keyof T, string>>>({});
  const [globalError, setGlobalError] = useState<string | null>(null);

  const setFieldError = useCallback((field: keyof T, message: string) => {
    setFieldErrors(prev => ({ ...prev, [field]: message }));

    // 记录验证错误
    errorManager.handleError(
      AppError.validation(`字段验证失败: ${String(field)}`, {
        field: String(field),
        message,
      })
    );
  }, []);

  const clearFieldError = useCallback((field: keyof T) => {
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setFieldErrors({});
    setGlobalError(null);
  }, []);

  const validateField = useCallback(
    (field: keyof T, value: any, validators: Array<(value: any) => string | null>): boolean => {
      for (const validator of validators) {
        const error = validator(value);
        if (error) {
          setFieldError(field, error);
          return false;
        }
      }
      clearFieldError(field);
      return true;
    },
    [setFieldError, clearFieldError]
  );

  const validateForm = useCallback(
    (
      data: T,
      validationRules: Partial<Record<keyof T, Array<(value: any) => string | null>>>
    ): boolean => {
      let isValid = true;
      clearAllErrors();

      for (const [field, validators] of Object.entries(validationRules)) {
        const fieldValue = data[field as keyof T];
        if (
          !validateField(
            field as keyof T,
            fieldValue,
            validators as Array<(value: any) => string | null>
          )
        ) {
          isValid = false;
        }
      }

      return isValid;
    },
    [validateField, clearAllErrors]
  );

  const handleSubmitError = useCallback((error: Error | AppError) => {
    const appError =
      error instanceof AppError ? error : AppError.business('FORM_SUBMIT_ERROR', error.message);
    setGlobalError(appError.message);
    errorManager.handleError(appError);
  }, []);

  return {
    fieldErrors,
    globalError,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    validateField,
    validateForm,
    handleSubmitError,
    hasErrors: Object.keys(fieldErrors).length > 0 || globalError !== null,
  };
}

/**
 * 错误统计监控Hook
 */
export function useErrorStats() {
  const [stats, setStats] = useState(errorManager.getStats());

  useEffect(() => {
    const updateStats = () => {
      setStats(errorManager.getStats());
    };

    // 监听错误事件
    const unsubscribe = errorManager.onError(updateStats);

    // 定期更新统计信息
    const interval = setInterval(updateStats, 30000); // 每30秒更新一次

    return () => {
      unsubscribe();
      clearInterval(interval);
    };
  }, []);

  const clearStats = useCallback(() => {
    errorManager.clearHistory();
    setStats(errorManager.getStats());
  }, []);

  return {
    stats,
    clearStats,
    refreshStats: () => setStats(errorManager.getStats()),
  };
}

/**
 * 错误边界Hook (用于函数组件)
 */
export function useErrorBoundary() {
  const [error, setError] = useState<Error | null>(null);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  const captureError = useCallback((error: Error) => {
    setError(error);
  }, []);

  // 如果有错误，抛出它以便错误边界捕获
  if (error) {
    throw error;
  }

  return {
    captureError,
    resetError,
  };
}

/**
 * 常用验证器
 */
export const validators = {
  required:
    (message = '此字段为必填项') =>
    (value: any) => {
      if (value === null || value === undefined || value === '') {
        return message;
      }
      return null;
    },

  minLength: (min: number, message?: string) => (value: string) => {
    if (value && value.length < min) {
      return message || `最少需要 ${min} 个字符`;
    }
    return null;
  },

  maxLength: (max: number, message?: string) => (value: string) => {
    if (value && value.length > max) {
      return message || `最多允许 ${max} 个字符`;
    }
    return null;
  },

  email:
    (message = '请输入有效的邮箱地址') =>
    (value: string) => {
      if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        return message;
      }
      return null;
    },

  number:
    (message = '请输入有效的数字') =>
    (value: any) => {
      if (value && isNaN(Number(value))) {
        return message;
      }
      return null;
    },

  min: (min: number, message?: string) => (value: number) => {
    if (value !== null && value !== undefined && value < min) {
      return message || `值不能小于 ${min}`;
    }
    return null;
  },

  max: (max: number, message?: string) => (value: number) => {
    if (value !== null && value !== undefined && value > max) {
      return message || `值不能大于 ${max}`;
    }
    return null;
  },
};
