/**
 * 统一的日志管理系统
 * 提供类型安全的日志记录，支持不同环境的日志级别控制
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  context?: string;
  data?: Record<string, unknown>;
  error?: Error;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableStorage: boolean;
  maxStorageEntries: number;
  context?: string;
}

class Logger {
  private config: LoggerConfig;
  private storage: LogEntry[] = [];

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: process.env.NODE_ENV === 'production' ? LogLevel.WARN : LogLevel.DEBUG,
      enableConsole: true,
      enableStorage: process.env.NODE_ENV === 'development',
      maxStorageEntries: 1000,
      ...config,
    };
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level;
  }

  private formatMessage(entry: LogEntry): string {
    const timestamp = entry.timestamp.toISOString();
    const context = entry.context ? `[${entry.context}]` : '';
    return `${timestamp} ${context} ${entry.message}`;
  }

  private log(
    level: LogLevel,
    message: string,
    data?: Record<string, unknown>,
    error?: Error
  ): void {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      context: this.config.context,
      data,
      error,
    };

    // 控制台输出
    if (this.config.enableConsole) {
      const formattedMessage = this.formatMessage(entry);

      switch (level) {
        case LogLevel.DEBUG:
          console.debug(formattedMessage, data);
          break;
        case LogLevel.INFO:
          console.info(formattedMessage, data);
          break;
        case LogLevel.WARN:
          console.warn(formattedMessage, data);
          break;
        case LogLevel.ERROR:
          console.error(formattedMessage, error || data);
          break;
      }
    }

    // 存储日志
    if (this.config.enableStorage) {
      this.storage.push(entry);

      // 限制存储条目数量
      if (this.storage.length > this.config.maxStorageEntries) {
        this.storage = this.storage.slice(-this.config.maxStorageEntries);
      }
    }
  }

  debug(message: string, data?: Record<string, unknown>): void {
    this.log(LogLevel.DEBUG, message, data);
  }

  info(message: string, data?: Record<string, unknown>): void {
    this.log(LogLevel.INFO, message, data);
  }

  warn(message: string, data?: Record<string, unknown>): void {
    this.log(LogLevel.WARN, message, data);
  }

  error(message: string, error?: Error, data?: Record<string, unknown>): void {
    this.log(LogLevel.ERROR, message, data, error);
  }

  // 创建子日志器，带有特定上下文
  createChild(context: string): Logger {
    return new Logger({
      ...this.config,
      context: this.config.context ? `${this.config.context}:${context}` : context,
    });
  }

  // 获取存储的日志
  getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.storage.filter(entry => entry.level >= level);
    }
    return [...this.storage];
  }

  // 清除存储的日志
  clearLogs(): void {
    this.storage = [];
  }

  // 导出日志为JSON
  exportLogs(): string {
    return JSON.stringify(this.storage, null, 2);
  }
}

// 默认日志器实例
export const logger = new Logger();

// 特定模块的日志器
export const createLogger = (context: string, config?: Partial<LoggerConfig>): Logger => {
  return new Logger({ ...config, context });
};

// 便捷的模块日志器
export const appLogger = createLogger('App');
export const storeLogger = createLogger('Store');
export const serviceLogger = createLogger('Service');
export const componentLogger = createLogger('Component');
export const hookLogger = createLogger('Hook');

// 性能日志器
export const performanceLogger = createLogger('Performance', {
  level: LogLevel.INFO,
});

// 错误边界日志器
export const errorBoundaryLogger = createLogger('ErrorBoundary', {
  level: LogLevel.ERROR,
});
