/**
 * 持久化配置Hook
 * 提供统一的配置持久化管理
 */

import { useCallback, useEffect, useState } from 'react';
import {
  PersistenceManager,
  STORAGE_KEYS,
} from '@/infrastructure/storage/persistence/persistenceManager';
import { useToast } from '@/shared/hooks/use-toast';

export interface UsePersistentConfigOptions<T> {
  key: keyof typeof STORAGE_KEYS;
  defaultValue: T;
  validateConfig?: (config: any) => config is T;
  onLoad?: (config: T) => void;
  onSave?: (config: T) => void;
  onError?: (error: Error) => void;
  autoSave?: boolean;
  debounceMs?: number;
}

export function usePersistentConfig<T>(options: UsePersistentConfigOptions<T>) {
  const {
    key,
    defaultValue,
    validateConfig,
    onLoad,
    onSave,
    onError,
    autoSave = true,
    debounceMs = 500,
  } = options;

  const { toast } = useToast();
  const [config, setConfigState] = useState<T>(defaultValue);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveTimeout, setSaveTimeout] = useState<NodeJS.Timeout | null>(null);

  // 加载配置
  const loadConfig = useCallback(() => {
    try {
      const loadedConfig = PersistenceManager.load(key, defaultValue);

      // 验证配置
      if (validateConfig && !validateConfig(loadedConfig)) {
        console.warn(`配置验证失败，使用默认配置: ${key}`);
        setConfigState(defaultValue);
        onLoad?.(defaultValue);
      } else {
        setConfigState(loadedConfig);
        onLoad?.(loadedConfig);
      }

      setIsLoaded(true);
    } catch (error) {
      const err = error instanceof Error ? error : new Error('配置加载失败');
      console.error(`配置加载失败: ${key}`, err);
      onError?.(err);
      setConfigState(defaultValue);
      setIsLoaded(true);
    }
  }, [key, defaultValue, validateConfig, onLoad, onError]);

  // 保存配置
  const saveConfig = useCallback(
    (configToSave: T, showToast = false) => {
      try {
        setIsSaving(true);
        const success = PersistenceManager.save(key, configToSave);

        if (success) {
          onSave?.(configToSave);
          if (showToast) {
            toast({
              title: '配置已保存',
              description: `${key} 配置已成功保存`,
            });
          }
        } else {
          throw new Error('保存失败');
        }
      } catch (error) {
        const err = error instanceof Error ? error : new Error('配置保存失败');
        console.error(`配置保存失败: ${key}`, err);
        onError?.(err);
        if (showToast) {
          toast({
            title: '保存失败',
            description: `${key} 配置保存失败`,
            variant: 'destructive',
          });
        }
      } finally {
        setIsSaving(false);
      }
    },
    [key, onSave, onError, toast]
  );

  // 防抖保存
  const debouncedSave = useCallback(
    (configToSave: T) => {
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }

      const timeout = setTimeout(() => {
        saveConfig(configToSave);
      }, debounceMs);

      setSaveTimeout(timeout);
    },
    [saveConfig, debounceMs, saveTimeout]
  );

  // 更新配置
  const updateConfig = useCallback(
    (newConfig: T | ((prev: T) => T), options?: { immediate?: boolean; showToast?: boolean }) => {
      const { immediate = false, showToast = false } = options || {};

      setConfigState(prevConfig => {
        const updatedConfig =
          typeof newConfig === 'function' ? (newConfig as (prev: T) => T)(prevConfig) : newConfig;

        // 自动保存
        if (autoSave && isLoaded) {
          if (immediate) {
            saveConfig(updatedConfig, showToast);
          } else {
            debouncedSave(updatedConfig);
          }
        }

        return updatedConfig;
      });
    },
    [autoSave, isLoaded, saveConfig, debouncedSave]
  );

  // 重置配置
  const resetConfig = useCallback(
    (showToast = false) => {
      setConfigState(defaultValue);
      if (autoSave) {
        saveConfig(defaultValue, showToast);
      }
    },
    [defaultValue, autoSave, saveConfig]
  );

  // 手动保存
  const manualSave = useCallback(
    (showToast = true) => {
      saveConfig(config, showToast);
    },
    [config, saveConfig]
  );

  // 导出配置
  const exportConfig = useCallback(() => {
    return config;
  }, [config]);

  // 导入配置
  const importConfig = useCallback(
    (importedConfig: T, showToast = true) => {
      if (validateConfig && !validateConfig(importedConfig)) {
        const error = new Error('导入的配置格式无效');
        onError?.(error);
        if (showToast) {
          toast({
            title: '导入失败',
            description: '配置格式无效',
            variant: 'destructive',
          });
        }
        return false;
      }

      setConfigState(importedConfig);
      if (autoSave) {
        saveConfig(importedConfig, showToast);
      }
      return true;
    },
    [validateConfig, onError, autoSave, saveConfig, toast]
  );

  // 检查配置是否存在
  const configExists = useCallback(() => {
    if (typeof window === 'undefined') return false;
    return PersistenceManager.exists(key);
  }, [key]);

  // 获取配置大小
  const getConfigSize = useCallback(() => {
    if (typeof window === 'undefined') return 0;
    return PersistenceManager.getSize(key);
  }, [key]);

  // 初始化时加载配置
  useEffect(() => {
    loadConfig();
  }, [loadConfig]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }
    };
  }, [saveTimeout]);

  return {
    // 配置数据
    config,
    isLoaded,
    isSaving,

    // 操作方法
    updateConfig,
    resetConfig,
    manualSave,
    loadConfig,

    // 导入导出
    exportConfig,
    importConfig,

    // 工具方法
    configExists,
    getConfigSize,
  };
}

// 预定义的配置Hook
export function useTaskListSettings() {
  return usePersistentConfig({
    key: 'TASK_LIST_SETTINGS',
    defaultValue: {
      columnBackgrounds: {},
    } as any, // 这里应该使用实际的默认值
    autoSave: true,
    debounceMs: 300,
  });
}

export function useTaskCardConfig() {
  return usePersistentConfig({
    key: 'TASK_CARD_CONFIG',
    defaultValue: {} as any, // 这里应该使用实际的默认值
    autoSave: true,
    debounceMs: 500,
  });
}

export function useThemeConfig() {
  return usePersistentConfig({
    key: 'APP_THEME',
    defaultValue: 'oceanic-deep' as const,
    autoSave: true,
    debounceMs: 100,
  });
}

export function useDensityConfig() {
  return usePersistentConfig({
    key: 'APP_DENSITY',
    defaultValue: 'cozy' as const,
    autoSave: true,
    debounceMs: 100,
  });
}

export function useUIPreferences() {
  return usePersistentConfig({
    key: 'UI_PREFERENCES',
    defaultValue: {
      floatingHeaderPosition: { x: 100, y: 100 },
      sidebarCollapsed: false,
      vehicleDispatchPosition: { x: 200, y: 200 },
    },
    autoSave: true,
    debounceMs: 200,
  });
}

export function useVehicleCardStyles() {
  return usePersistentConfig({
    key: 'CARD_VEHICLE_STYLES',
    defaultValue: {
      gap: 0,
      cardSize: 'small',
      cardWidth: 'w-14' as const,
      cardHeight: 'h-8' as const,
      fontSize: 'text-[10px]' as const,
      fontColor: 'text-foreground',
      vehicleNumberFontWeight: 'font-medium' as const,
      cardBgColor: 'bg-background',
      statusDotSize: 'w-1 h-1' as const,
      borderRadius: 'rounded-sm',
      boxShadow: 'shadow-sm',
    },
    autoSave: true,
    debounceMs: 300,
  });
}

export function useTableColumnStyles() {
  return usePersistentConfig({
    key: 'TABLE_COLUMN_STYLES',
    defaultValue: {
      columnWidths: {},
      columnOrder: [],
      columnVisibility: {},
      columnStyles: {},
    },
    autoSave: true,
    debounceMs: 500,
  });
}
