/**
 * 配比管理自定义Hook
 * 连接UI状态和业务逻辑，提供统一的配比管理接口
 */

import { useCallback, useEffect, useMemo } from 'react';
import { useRatioUIStore } from '@/features/ratio-management/store/ratioUIStore';
import { useRatioBusinessStore } from '@/features/ratio-management/store/ratioBusinessStore';
import type { RatioModel, RatioMaterialModel } from '@/models/RatioModel';
import type {
  CalculationParams,
  CalculationResult,
} from '@/features/ratio-management/services/RatioBusinessService';

export interface UseRatioManagementOptions {
  taskId?: string;
  autoLoad?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

export interface UseRatioManagementReturn {
  // 数据状态
  currentRatio: RatioModel | null;
  selectedMaterials: RatioMaterialModel[];
  calculationParams: CalculationParams | null;
  calculationResult: CalculationResult | null;

  // UI状态
  isLoading: boolean;
  isCalculating: boolean;
  isSaving: boolean;
  error: string | null;
  isDirty: boolean;

  // 配比操作
  loadRatio: (taskId: string) => Promise<void>;
  saveRatio: () => Promise<void>;
  clearRatio: () => void;

  // 材料操作
  addMaterial: (material: RatioMaterialModel) => void;
  removeMaterial: (materialId: string) => void;
  updateMaterial: (materialId: string, updates: Partial<RatioMaterialModel>) => void;

  // 计算操作
  updateCalculationParams: (updates: Partial<CalculationParams>) => void;
  calculateRatio: () => Promise<void>;
  reverseCalculate: () => Promise<void>;

  // UI操作
  setCurrentView: (view: 'design' | 'calculation' | 'history' | 'templates') => void;
  toggleDesignPanel: () => void;
  showCalculationResults: () => void;
  hideCalculationResults: () => void;

  // 表单操作
  setFormError: (field: string, error: string | null) => void;
  clearFormErrors: () => void;
  markFormTouched: (field: string) => void;

  // 工具方法
  canSave: boolean;
  canCalculate: boolean;
  hasUnsavedChanges: boolean;
}

export function useRatioManagement(
  options: UseRatioManagementOptions = {}
): UseRatioManagementReturn {
  const { taskId, autoLoad = true, autoSave = false, autoSaveDelay = 2000 } = options;

  // UI状态
  const {
    currentView,
    isLoading: uiLoading,
    error: uiError,
    designPanel,
    calculationResults,
    forms,
    setCurrentView,
    setLoading,
    setError,
    toggleDesignPanel,
    toggleCalculationResults,
    setFormError,
    clearFormErrors,
    setFormTouched,
  } = useRatioUIStore();

  // 业务状态
  const {
    currentRatio,
    selectedMaterials,
    calculationParams,
    calculationResult,
    operationStatus,
    errors,
    cache,
    loadRatio: businessLoadRatio,
    saveRatio: businessSaveRatio,
    clearCurrentRatio,
    addMaterial: businessAddMaterial,
    removeMaterial: businessRemoveMaterial,
    updateMaterial: businessUpdateMaterial,
    updateCalculationParams: businessUpdateCalculationParams,
    calculateRatio: businessCalculateRatio,
    reverseCalculate: businessReverseCalculate,
    setError: setBusinessError,
    markDirty,
    markClean,
  } = useRatioBusinessStore();

  // 自动加载配比数据
  useEffect(() => {
    if (autoLoad && taskId && !currentRatio) {
      loadRatio(taskId);
    }
  }, [autoLoad, taskId]); // 移除 currentRatio 依赖，避免无限循环

  // 自动保存
  useEffect(() => {
    if (!autoSave || !cache.isDirty || !currentRatio) return;

    const timer = setTimeout(() => {
      saveRatio();
    }, autoSaveDelay);

    return () => clearTimeout(timer);
  }, [autoSave, cache.isDirty, currentRatio, autoSaveDelay]);

  // 同步UI和业务错误状态
  useEffect(() => {
    const businessError =
      errors.load || errors.save || errors.calculate || errors.analyze || errors.optimize;
    if (businessError !== uiError) {
      setError(businessError);
    }
  }, [errors, uiError, setError]);

  // 同步加载状态
  useEffect(() => {
    const isBusinessLoading = operationStatus.loading || operationStatus.saving;
    if (isBusinessLoading !== uiLoading) {
      setLoading(isBusinessLoading);
    }
  }, [operationStatus, uiLoading, setLoading]);

  // 配比操作
  const loadRatio = useCallback(
    async (taskId: string) => {
      try {
        setError(null);
        await businessLoadRatio(taskId);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '加载配比失败';
        setError(errorMessage);
        setBusinessError('load', errorMessage);
      }
    },
    [businessLoadRatio, setError, setBusinessError]
  );

  const saveRatio = useCallback(async () => {
    if (!currentRatio) {
      throw new Error('没有配比数据可保存');
    }

    try {
      setError(null);
      await businessSaveRatio(currentRatio);
      markClean();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '保存配比失败';
      setError(errorMessage);
      setBusinessError('save', errorMessage);
      throw error;
    }
  }, [currentRatio, businessSaveRatio, setError, setBusinessError, markClean]);

  const clearRatio = useCallback(() => {
    clearCurrentRatio();
    clearFormErrors('calculationParams');
    setError(null);
  }, [clearCurrentRatio, clearFormErrors, setError]);

  // 材料操作
  const addMaterial = useCallback(
    (material: RatioMaterialModel) => {
      businessAddMaterial(material);
      markDirty();
    },
    [businessAddMaterial, markDirty]
  );

  const removeMaterial = useCallback(
    (materialId: string) => {
      businessRemoveMaterial(materialId);
      markDirty();
    },
    [businessRemoveMaterial, markDirty]
  );

  const updateMaterial = useCallback(
    (materialId: string, updates: Partial<RatioMaterialModel>) => {
      businessUpdateMaterial(materialId, updates);
      markDirty();
    },
    [businessUpdateMaterial, markDirty]
  );

  // 计算操作
  const updateCalculationParams = useCallback(
    (updates: Partial<CalculationParams>) => {
      businessUpdateCalculationParams(updates);
      markDirty();
    },
    [businessUpdateCalculationParams, markDirty]
  );

  const calculateRatio = useCallback(async () => {
    try {
      setError(null);
      await businessCalculateRatio();
      // 计算成功后显示结果
      if (!calculationResults.isVisible) {
        toggleCalculationResults();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '计算失败';
      setError(errorMessage);
      setBusinessError('calculate', errorMessage);
    }
  }, [
    businessCalculateRatio,
    setError,
    setBusinessError,
    calculationResults.isVisible,
    toggleCalculationResults,
  ]);

  const reverseCalculate = useCallback(async () => {
    try {
      setError(null);
      await businessReverseCalculate();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '反算失败';
      setError(errorMessage);
      setBusinessError('calculate', errorMessage);
    }
  }, [businessReverseCalculate, setError, setBusinessError]);

  // UI操作
  const showCalculationResults = useCallback(() => {
    if (!calculationResults.isVisible) {
      toggleCalculationResults();
    }
  }, [calculationResults.isVisible, toggleCalculationResults]);

  const hideCalculationResults = useCallback(() => {
    if (calculationResults.isVisible) {
      toggleCalculationResults();
    }
  }, [calculationResults.isVisible, toggleCalculationResults]);

  // 表单操作
  const setFormErrorWrapper = useCallback(
    (field: string, error: string | null) => {
      setFormError('calculationParams', field, error);
    },
    [setFormError]
  );

  const clearFormErrorsWrapper = useCallback(() => {
    clearFormErrors('calculationParams');
  }, [clearFormErrors]);

  const markFormTouched = useCallback(
    (field: string) => {
      setFormTouched('calculationParams', field, true);
    },
    [setFormTouched]
  );

  // 计算属性
  const canSave = useMemo(() => {
    return currentRatio !== null && !operationStatus.saving;
  }, [currentRatio, operationStatus.saving]);

  const canCalculate = useMemo(() => {
    return (
      calculationParams !== null && selectedMaterials.length > 0 && !operationStatus.calculating
    );
  }, [calculationParams, selectedMaterials, operationStatus.calculating]);

  const hasUnsavedChanges = useMemo(() => {
    return cache.isDirty;
  }, [cache.isDirty]);

  const isLoading = useMemo(() => {
    return operationStatus.loading || uiLoading;
  }, [operationStatus.loading, uiLoading]);

  const isCalculating = useMemo(() => {
    return operationStatus.calculating;
  }, [operationStatus.calculating]);

  const isSaving = useMemo(() => {
    return operationStatus.saving;
  }, [operationStatus.saving]);

  const error = useMemo(() => {
    return (
      uiError || errors.load || errors.save || errors.calculate || errors.analyze || errors.optimize
    );
  }, [uiError, errors]);

  const isDirty = useMemo(() => {
    return cache.isDirty || forms.calculationParams.isDirty;
  }, [cache.isDirty, forms.calculationParams.isDirty]);

  return {
    // 数据状态
    currentRatio,
    selectedMaterials,
    calculationParams,
    calculationResult,

    // UI状态
    isLoading,
    isCalculating,
    isSaving,
    error,
    isDirty,

    // 配比操作
    loadRatio,
    saveRatio,
    clearRatio,

    // 材料操作
    addMaterial,
    removeMaterial,
    updateMaterial,

    // 计算操作
    updateCalculationParams,
    calculateRatio,
    reverseCalculate,

    // UI操作
    setCurrentView,
    toggleDesignPanel,
    showCalculationResults,
    hideCalculationResults,

    // 表单操作
    setFormError: setFormErrorWrapper,
    clearFormErrors: clearFormErrorsWrapper,
    markFormTouched,

    // 工具方法
    canSave,
    canCalculate,
    hasUnsavedChanges,
  };
}
