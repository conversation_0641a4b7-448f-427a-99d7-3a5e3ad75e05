/**
 * 备选配比相关类型定义
 */

import type { UnifiedRatioMaterial, RatioCalculationParams, CalculationResults } from './ratio';

// 备选配比数据结构
export interface BackupRatio {
  id: string;
  name: string;
  description?: string;
  taskId: string;

  // 配比数据
  materials: UnifiedRatioMaterial[];
  calculationParams: RatioCalculationParams;
  calculationResults?: CalculationResults;

  // 元数据
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  tags?: string[];

  // 快速访问属性
  targetStrength?: string; // 如 'C30'
  slump?: string; // 如 '180±20'
  waterCementRatio?: number;
  totalMaterials?: number;
  qualityScore?: number;
}

// 备选配比创建请求
export interface CreateBackupRatioRequest {
  name: string;
  description?: string;
  taskId: string;
  materials: UnifiedRatioMaterial[];
  calculationParams: RatioCalculationParams;
  calculationResults?: CalculationResults;
  tags?: string[];
}

// 备选配比列表项（用于选择界面）
export interface BackupRatioListItem {
  id: string;
  name: string;
  description?: string;
  targetStrength?: string;
  slump?: string;
  waterCementRatio?: number;
  totalMaterials: number;
  qualityScore?: number;
  createdAt: string;
  tags?: string[];
}

// 备选配比应用结果
export interface ApplyBackupRatioResult {
  success: boolean;
  message: string;
  appliedRatio?: BackupRatio;
  errors?: string[];
  warnings?: string[];
}

// 备选配比搜索过滤条件
export interface BackupRatioFilter {
  taskId?: string;
  targetStrength?: string[];
  slumpRange?: { min: number; max: number };
  waterCementRatioRange?: { min: number; max: number };
  tags?: string[];
  createdDateRange?: { start: string; end: string };
  searchText?: string;
}

// 备选配比排序选项
export type BackupRatioSortBy =
  | 'createdAt'
  | 'name'
  | 'targetStrength'
  | 'qualityScore'
  | 'waterCementRatio';

export type BackupRatioSortOrder = 'asc' | 'desc';

// 备选配比统计信息
export interface BackupRatioStats {
  total: number;
  byStrength: Record<string, number>;
  byTags: Record<string, number>;
  averageQualityScore: number;
  recentCount: number; // 最近7天创建的数量
}
