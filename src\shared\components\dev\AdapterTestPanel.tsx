/**
 * 适配器测试面板 - 用于测试和调试适配器系统
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Button } from '@/shared/components/button';
import { Badge } from '@/shared/components/badge';
import { AdapterManager, AdapterType } from '@/core/adapters/AdapterManager';
import { checkAdaptersHealth, isAdaptersInitialized } from '@/core/adapters/AdapterRegistry';
import { useVehicleDrag, useProductionLineDrop } from '@/core/adapters/hooks/useUnifiedDragDrop';

interface AdapterTestPanelProps {
  className?: string;
}

export function AdapterTestPanel({ className }: AdapterTestPanelProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [metrics, setMetrics] = useState<any>(null);

  // 刷新状态
  const refreshStatus = () => {
    setIsInitialized(isAdaptersInitialized());
    setHealthStatus(checkAdaptersHealth());

    if (isAdaptersInitialized()) {
      const adapterManager = AdapterManager.getInstance();
      setMetrics(adapterManager.getPerformanceMetrics());
    }
  };

  useEffect(() => {
    refreshStatus();

    // 定期刷新状态
    const interval = setInterval(refreshStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  // 测试拖拽功能
  const TestDragComponent = () => {
    const dragResult = useVehicleDrag('test-vehicle-1', {
      id: 'test-vehicle-1',
      licensePlate: '测试A001',
      status: 'available',
    });

    const dropResult = useProductionLineDrop(
      vehicleData => {
        console.log('🎯 车辆放置成功:', vehicleData);
        alert(`车辆 ${vehicleData.licensePlate} 已放置到生产线`);
      },
      vehicleData => {
        console.log('🔍 检查车辆是否可放置:', vehicleData);
        return true;
      }
    );

    const setDragRef = (node: HTMLDivElement | null) => {
      if (dragResult.dragRef) {
        dragResult.dragRef(node);
      }
    };

    const setDropRef = (node: HTMLDivElement | null) => {
      if (dropResult.dropRef) {
        dropResult.dropRef(node);
      }
    };

    return (
      <div className='flex gap-4 p-4'>
        <div
          ref={setDragRef}
          className={`
            p-4 border-2 border-dashed border-blue-500 rounded-lg cursor-move
            ${dragResult.isDragging ? 'opacity-50' : 'opacity-100'}
            hover:bg-blue-50 transition-colors
          `}
          {...dragResult.attributes}
          {...dragResult.listeners}
        >
          <div className='text-sm font-medium'>🚛 测试车辆</div>
          <div className='text-xs text-gray-500'>测试A001</div>
          <div className='text-xs text-blue-600 mt-1'>
            {dragResult.isDragging ? '拖拽中...' : '点击拖拽'}
          </div>
        </div>

        <div
          ref={setDropRef}
          className={`
            p-4 border-2 border-dashed border-green-500 rounded-lg
            ${dropResult.isOver ? 'bg-green-100' : 'bg-gray-50'}
            ${dropResult.canDrop ? 'border-green-500' : 'border-gray-300'}
            transition-colors
          `}
        >
          <div className='text-sm font-medium'>🏭 生产线</div>
          <div className='text-xs text-gray-500'>拖拽车辆到这里</div>
          <div className='text-xs text-green-600 mt-1'>
            {dropResult.isOver ? '可以放置' : '等待车辆'}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          🔧 适配器系统测试面板
          <Badge variant={isInitialized ? 'default' : 'destructive'}>
            {isInitialized ? '已初始化' : '未初始化'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        {/* 状态信息 */}
        <div className='grid grid-cols-2 gap-4'>
          <div>
            <h4 className='font-medium mb-2'>健康状态</h4>
            {healthStatus && (
              <Badge variant={healthStatus.status === 'healthy' ? 'default' : 'destructive'}>
                {healthStatus.status === 'healthy' ? '✅ 健康' : '❌ 异常'}
              </Badge>
            )}
          </div>

          <div>
            <h4 className='font-medium mb-2'>性能指标</h4>
            {metrics && (
              <div className='text-xs space-y-1'>
                <div>已注册: {metrics.registeredCount}</div>
                <div>活跃: {metrics.activeCount}</div>
                <div>映射: {metrics.featureMappingCount}</div>
              </div>
            )}
          </div>
        </div>

        {/* 适配器列表 */}
        {metrics && (
          <div>
            <h4 className='font-medium mb-2'>活跃适配器</h4>
            <div className='space-y-1'>
              {metrics.activeAdapters.map((adapter: any, index: number) => (
                <div key={index} className='flex items-center gap-2 text-xs'>
                  <Badge variant='outline'>{adapter.type}</Badge>
                  <span>{adapter.library}</span>
                  <Badge variant={adapter.enabled ? 'default' : 'secondary'}>
                    {adapter.enabled ? '启用' : '禁用'}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 拖拽测试 */}
        <div>
          <h4 className='font-medium mb-2'>拖拽功能测试</h4>
          {isInitialized ? (
            <TestDragComponent />
          ) : (
            <div className='text-sm text-gray-500 p-4 border border-dashed rounded'>
              适配器系统未初始化，无法测试拖拽功能
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className='flex gap-2'>
          <Button size='sm' onClick={refreshStatus}>
            🔄 刷新状态
          </Button>
          <Button
            size='sm'
            variant='outline'
            onClick={() => {
              console.log('📊 适配器详细信息:', {
                initialized: isInitialized,
                health: healthStatus,
                metrics: metrics,
              });
            }}
          >
            📊 输出日志
          </Button>
        </div>

        {/* 错误信息 */}
        {healthStatus?.status === 'error' && (
          <div className='p-3 bg-red-50 border border-red-200 rounded text-sm'>
            <div className='font-medium text-red-800'>错误详情:</div>
            <div className='text-red-600 mt-1'>
              {healthStatus.details.message || healthStatus.details.error}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
