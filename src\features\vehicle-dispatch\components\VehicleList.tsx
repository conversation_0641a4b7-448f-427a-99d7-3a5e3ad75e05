/**
 * 车辆列表组件
 * 显示特定状态的车辆列表
 */

import React from 'react';
import { VehicleGrid } from './VehicleGrid';
import type {
  Vehicle,
  VehicleDisplayMode,
  InTaskVehicleCardStyle,
  TaskListDensityMode,
} from '@/core/types';
import { cn } from '../../../core/lib/utils';

interface VehicleListProps {
  title: string;
  vehicles: Vehicle[];
  listType: 'pending' | 'returned' | 'outbound';
  globalDispatchActive: boolean;
  displayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  density: Exclude<TaskListDensityMode, '' | 'card'>;
  className?: string;
  columns?: 2 | 3 | 4 | 5 | 6;
  onContextMenu: (event: React.MouseEvent, vehicle: Vehicle) => void;
  onVisualMove: (draggedId: string, hoverId: string, statusList: 'pending' | 'returned') => void;
  onCommitReorder: (statusList: 'pending' | 'returned') => void;
}

/**
 * 车辆列表组件
 */
export const VehicleList: React.FC<VehicleListProps> = ({
  title,
  vehicles,
  listType,
  globalDispatchActive,
  displayMode,
  inTaskVehicleCardStyles,
  density,
  className,
  columns = 4,
  onContextMenu,
  onVisualMove,
  onCommitReorder,
}) => {
  return (
    <div className={cn('space-y-2', className)}>
      {/* 使用VehicleGrid组件实现4列布局 */}
      <VehicleGrid
        vehicles={vehicles}
        listType={listType}
        globalDispatchActive={globalDispatchActive}
        displayMode={displayMode}
        inTaskVehicleCardStyles={inTaskVehicleCardStyles}
        density={density}
        columns={columns}
        emptyMessage={`暂无${title.replace('车辆', '')}车辆`}
        onContextMenu={onContextMenu}
        onVisualMove={onVisualMove}
        onCommitReorder={onCommitReorder}
      />
    </div>
  );
};

// 导入InTaskVehicleCard组件
