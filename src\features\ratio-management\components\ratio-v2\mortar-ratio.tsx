'use client';

import React from 'react';

interface MortarRatioProps {
  isOpen: boolean;
  onClose: () => void;
}

export function MortarRatio({ isOpen, onClose }: MortarRatioProps) {
  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
      <div className='bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto'>
        <div className='flex justify-between items-center mb-4'>
          <h2 className='text-xl font-semibold'>砂浆配比设计</h2>
          <button onClick={onClose} className='text-gray-500 hover:text-gray-700'>
            ×
          </button>
        </div>

        <div className='space-y-4'>
          <p>砂浆配比设计功能正在开发中...</p>

          <div className='flex justify-end space-x-2'>
            <button
              onClick={onClose}
              className='px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300'
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
