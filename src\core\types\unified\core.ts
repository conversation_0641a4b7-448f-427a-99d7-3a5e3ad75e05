/**
 * 统一的核心类型定义
 * 整理和完善系统中的核心业务类型，确保类型安全和一致性
 */

// ==================== 基础类型 ====================

/**
 * 通用ID类型
 */
export type ID = string;

/**
 * 时间戳类型
 */
export type Timestamp = string;

/**
 * 日期字符串类型 (YYYY-MM-DD)
 */
export type DateString = string;

/**
 * 时间字符串类型 (HH:mm)
 */
export type TimeString = string;

/**
 * 电话号码类型
 */
export type PhoneNumber = string;

/**
 * 车牌号类型
 */
export type LicensePlate = string;

// ==================== 状态枚举 ====================

/**
 * 任务调度状态
 */
export enum TaskDispatchStatus {
  NEW = 'New',
  READY_TO_PRODUCE = 'ReadyToProduce',
  RATIO_SET = 'RatioSet',
  IN_PROGRESS = 'InProgress',
  PAUSED = 'Paused',
  COMPLETED = 'Completed',
  CANCELLED = 'Cancelled',
}

/**
 * 车辆状态
 */
export enum VehicleStatus {
  PENDING = 'pending',
  OUTBOUND = 'outbound',
  RETURNED = 'returned',
  MAINTENANCE = 'maintenance',
  OFFLINE = 'offline',
}

/**
 * 车辆运营状态
 */
export enum VehicleOperationalStatus {
  NORMAL = 'normal',
  PAUSED = 'paused',
  DEACTIVATED = 'deactivated',
  MAINTENANCE = 'maintenance',
  REPAIR = 'repair',
}

/**
 * 车辆生产状态
 */
export enum VehicleProductionStatus {
  QUEUED = 'queued',
  PRODUCING = 'producing',
  PRODUCED = 'produced',
  WEIGHED = 'weighed',
  TICKETED = 'ticketed',
  SHIPPED = 'shipped',
}

/**
 * 优先级枚举
 */
export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

// ==================== 核心实体接口 ====================

/**
 * 统一的任务接口
 * 整合了所有任务相关的字段，确保类型一致性
 */
export interface UnifiedTask {
  // 基本信息
  id: ID;
  plantId: ID;
  taskNumber: string;
  projectName: string;
  projectAbbreviation?: string;
  customerName?: string;
  constructionUnit?: string;
  constructionSite?: string;
  deliveryLocation?: string;

  // 技术规格
  strength: string;
  concreteGrade?: string;
  pouringMethod?: string;
  freezeResistance?: string;
  impermeability?: string;
  slump?: number;
  airContent?: number;

  // 数量和进度
  vehicleCount: number;
  requiredVolume: number;
  completedVolume: number;
  completedProgress: number;

  // 时间信息
  supplyDate: DateString;
  supplyTime: TimeString;
  publishDate?: DateString;
  timing?: string;

  // 状态管理
  dispatchStatus: TaskDispatchStatus;
  status?: string; // 兼容旧版本
  priority?: Priority;

  // 调度信息
  dispatchFrequencyMinutes: number;
  lastDispatchTime?: Timestamp;
  nextScheduledDispatchTime?: Timestamp;
  isDueForDispatch: boolean;
  minutesToDispatch: number;

  // 车辆和设备
  dispatchedVehicles: ID[];
  vehicles?: UnifiedVehicle[];
  pumpTruck?: string;

  // 联系信息
  contactPhone?: PhoneNumber;
  contactPerson?: string;

  // 其他信息
  otherRequirements?: string;
  notes?: string;
  subTheme?: string;
  dispatchNote?: string;
  isTicketed?: boolean;

  // 消息和提醒
  messages?: TaskMessage[];
  reminders?: ReminderMessage[];

  // 元数据
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
  createdBy?: string;
  updatedBy?: string;
}

/**
 * 统一的车辆接口
 * 整合了所有车辆相关的字段，确保类型一致性
 */
export interface UnifiedVehicle {
  // 基本信息
  id: ID;
  vehicleNumber: string;
  licensePlate?: LicensePlate;
  plantId: ID;

  // 状态信息
  status: VehicleStatus;
  operationalStatus?: VehicleOperationalStatus;
  productionStatus?: VehicleProductionStatus;

  // 分配信息
  assignedTaskId?: ID;
  assignedProductionLineId?: ID;
  currentTaskId?: ID; // 兼容字段

  // 驾驶员信息
  driver?: string;
  driverName?: string; // 兼容字段
  driverPhone?: PhoneNumber;

  // 车辆规格
  capacity: number;
  capacityUnit?: string;
  type?: string;
  model?: string;
  year?: number;

  // 位置和时间
  currentLocation?: string;
  location?: string; // 兼容字段
  lastDispatchTime?: Timestamp;
  estimatedReturnTime?: Timestamp;
  lastActivityTime?: Timestamp;

  // 维护信息
  mileage?: number;
  lastMaintenanceDate?: DateString;
  lastMaintenanceMileage?: number;
  nextMaintenanceDate?: DateString;

  // 统计信息
  totalTrips?: number;
  totalWorkingHours?: number;
  totalDistance?: number;

  // 其他信息
  notes?: string;
  isDragging?: boolean; // UI状态

  // 元数据
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

/**
 * 统一的工厂接口
 */
export interface UnifiedPlant {
  id: ID;
  name: string;
  code?: string;
  address?: string;
  contactPhone?: PhoneNumber;
  contactPerson?: string;
  productionLineCount: number;
  capacity?: number;
  status?: 'active' | 'inactive' | 'maintenance';

  // 统计信息
  stats?: {
    totalTasks: number;
    completedTasks: number;
    totalVehicles: number;
    activeVehicles: number;
    dailyProduction: number;
    monthlyProduction: number;
  };

  // 元数据
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// ==================== 消息和通知类型 ====================

/**
 * 任务消息接口
 */
export interface TaskMessage {
  id: ID;
  taskId: ID;
  content: string;
  sender: string;
  timestamp: Timestamp;
  isRead: boolean;
  priority: Priority;
  type: 'info' | 'warning' | 'error' | 'success';
  attachments?: string[];
}

/**
 * 提醒消息类型
 */
export type ReminderType = 'dispatchCountdown' | 'highlight' | 'popup' | 'sound' | 'error';

/**
 * 提醒消息接口
 */
export interface ReminderMessage {
  id: ID;
  type: ReminderType;
  taskId: ID;
  taskNumber: string;
  title: string;
  description: string;
  time: number; // 触发时间戳
  read: boolean;
  projectName?: string;
  extra?: Record<string, unknown>;
}

// ==================== API响应类型 ====================

/**
 * 通用API响应接口
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    total?: number;
    page?: number;
    pageSize?: number;
    timestamp?: string;
  };
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  meta: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    timestamp: string;
  };
}

// ==================== 类型守卫函数 ====================

/**
 * 检查是否为有效的任务对象
 */
export function isUnifiedTask(obj: any): obj is UnifiedTask {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.taskNumber === 'string' &&
    typeof obj.projectName === 'string' &&
    typeof obj.vehicleCount === 'number' &&
    typeof obj.requiredVolume === 'number' &&
    Object.values(TaskDispatchStatus).includes(obj.dispatchStatus)
  );
}

/**
 * 检查是否为有效的车辆对象
 */
export function isUnifiedVehicle(obj: any): obj is UnifiedVehicle {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.vehicleNumber === 'string' &&
    typeof obj.capacity === 'number' &&
    Object.values(VehicleStatus).includes(obj.status)
  );
}

/**
 * 检查是否为有效的工厂对象
 */
export function isUnifiedPlant(obj: any): obj is UnifiedPlant {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.productionLineCount === 'number'
  );
}

// ==================== 类型转换工具 ====================

/**
 * 将旧版任务类型转换为统一任务类型
 */
export function convertToUnifiedTask(oldTask: any): UnifiedTask {
  return {
    id: oldTask.id,
    plantId: oldTask.plantId || '',
    taskNumber: oldTask.taskNumber,
    projectName: oldTask.projectName,
    projectAbbreviation: oldTask.projectAbbreviation,
    customerName: oldTask.customerName,
    constructionUnit: oldTask.constructionUnit,
    constructionSite: oldTask.constructionSite,
    deliveryLocation: oldTask.deliveryLocation,
    strength: oldTask.strength,
    concreteGrade: oldTask.concreteGrade,
    pouringMethod: oldTask.pouringMethod,
    freezeResistance: oldTask.freezeResistance,
    impermeability: oldTask.impermeability,
    vehicleCount: oldTask.vehicleCount || 1,
    requiredVolume: oldTask.requiredVolume || 0,
    completedVolume: oldTask.completedVolume || 0,
    completedProgress: oldTask.completedProgress || 0,
    supplyDate: oldTask.supplyDate,
    supplyTime: oldTask.supplyTime,
    publishDate: oldTask.publishDate,
    dispatchStatus: oldTask.dispatchStatus || TaskDispatchStatus.NEW,
    dispatchFrequencyMinutes: oldTask.dispatchFrequencyMinutes || 30,
    lastDispatchTime: oldTask.lastDispatchTime,
    nextScheduledDispatchTime: oldTask.nextScheduledDispatchTime,
    isDueForDispatch: oldTask.isDueForDispatch || false,
    minutesToDispatch: oldTask.minutesToDispatch || 0,
    dispatchedVehicles: oldTask.dispatchedVehicles || [],
    vehicles: oldTask.vehicles,
    pumpTruck: oldTask.pumpTruck,
    contactPhone: oldTask.contactPhone,
    otherRequirements: oldTask.otherRequirements,
    notes: oldTask.notes,
    isTicketed: oldTask.isTicketed,
  };
}

/**
 * 将旧版车辆类型转换为统一车辆类型
 */
export function convertToUnifiedVehicle(oldVehicle: any): UnifiedVehicle {
  return {
    id: oldVehicle.id,
    vehicleNumber: oldVehicle.vehicleNumber,
    licensePlate: oldVehicle.licensePlate,
    plantId: oldVehicle.plantId || '',
    status: oldVehicle.status || VehicleStatus.PENDING,
    operationalStatus: oldVehicle.operationalStatus,
    productionStatus: oldVehicle.productionStatus,
    assignedTaskId: oldVehicle.assignedTaskId,
    assignedProductionLineId: oldVehicle.assignedProductionLineId,
    driver: oldVehicle.driver || oldVehicle.driverName,
    driverPhone: oldVehicle.driverPhone,
    capacity: oldVehicle.capacity || 0,
    capacityUnit: oldVehicle.capacityUnit || 'm³',
    type: oldVehicle.type,
    currentLocation: oldVehicle.currentLocation || oldVehicle.location,
    lastDispatchTime: oldVehicle.lastDispatchTime,
    estimatedReturnTime: oldVehicle.estimatedReturnTime,
    lastActivityTime: oldVehicle.lastActivityTime,
    mileage: oldVehicle.mileage,
    lastMaintenanceDate: oldVehicle.lastMaintenanceDate,
    lastMaintenanceMileage: oldVehicle.lastMaintenanceMileage,
    totalTrips: oldVehicle.totalTrips,
    totalWorkingHours: oldVehicle.totalWorkingHours,
    notes: oldVehicle.notes,
    isDragging: oldVehicle.isDragging,
  };
}
