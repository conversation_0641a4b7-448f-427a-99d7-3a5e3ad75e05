'use client';

import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';

interface RatioBackupModalProps {
  open?: boolean;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onOpenChangeAction?: (open: boolean) => void;
}

const RatioBackupModal: React.FC<RatioBackupModalProps> = ({
  open = false,
  isOpen = false,
  onOpenChange,
  onOpenChangeAction,
}) => {
  // 统一处理open状态和回调
  const modalOpen = (open || isOpen) ?? false;
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange?.(newOpen);
    onOpenChangeAction?.(newOpen);
  };

  return (
    <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>配比备份</DialogTitle>
        </DialogHeader>
        <div className='p-4'>
          <p>配比备份功能正在开发中...</p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RatioBackupModal;
