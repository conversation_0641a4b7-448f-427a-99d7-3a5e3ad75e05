# TMH任务调度系统 - 脚本目录说明

## 📁 脚本分类

### 🚀 部署相关 (Production)
- `deploy-intranet.js` - 内网自动部署脚本 (主要)
- `setup-ssh.js` - SSH密钥配置脚本 (整合版)

### 🔧 开发工具 (Development)
- `build-docs.js` - 文档构建脚本
- `setup-pre-commit.js` - Git预提交钩子配置
- `code-quality-optimizer.js` - 代码质量优化工具

### 🛠️ 维护工具 (Maintenance)
- `fix-import-paths.js` - 修复导入路径
- `fix-styles.js` - 修复样式问题
- `package-optimization.js` - 包优化脚本

### 🎵 功能脚本 (Features)
- `create-alert-sound.js` - 创建提醒音效

## 📋 使用指南

### 🚀 一键部署到内网
```bash
# 测试部署环境
npm run deploy:test

# 执行部署
npm run deploy:intranet
```

### 🔐 SSH配置
```bash
# 配置SSH密钥认证（推荐）
npm run setup:ssh

# 安装sshpass（密码认证）
powershell scripts/install-sshpass.ps1
```

### 🔧 开发工具
```bash
# 代码质量优化
npm run optimize:code

# 构建文档
npm run build:docs

# 清理脚本目录
npm run scripts:cleanup
```

## ⚙️ 配置文件

### `.env.deploy` - 部署配置
```env
SERVER_IP=*************
SERVER_USER=administrator
SERVER_PASSWORD=your_password
SSH_PORT=22
DEPLOY_PATH=C:\inetpub\tmh-task-dispatcher
SERVICE_NAME=tmh-task-dispatcher
SERVER_PORT=9001
```

## 🔍 故障排除

### SSH连接问题
1. **密钥认证失败**: 运行 `npm run setup:ssh`
2. **密码认证失败**: 检查 `.env.deploy` 中的密码
3. **sshpass未安装**: 运行 `powershell scripts/install-sshpass.ps1`

### 部署问题
1. **PM2启动失败**: 检查Node.js和PM2是否正确安装
2. **端口冲突**: 修改 `.env.deploy` 中的 `SERVER_PORT`
3. **权限问题**: 确保用户有部署目录的写权限

## 🗑️ 已清理的脚本
以下脚本已被整合或移除：
- `deploy.js` - 已整合到 `deploy-intranet.js`
- `auto-setup-ssh.ps1` - 已整合到 `setup-ssh.js`
- `setup-ssh-key.bat` - 已整合到 `setup-ssh.js`
- `setup-ssh-key.ps1` - 已整合到 `setup-ssh.js`
- `fix-deployment.ps1` - 一次性脚本，已移除
- `fix-pm2-windows.bat` - 一次性脚本，已移除
- `quick-fix-pm2.ps1` - 一次性脚本，已移除
- `quick-fix.bat` - 一次性脚本，已移除
- `setup-auto-deploy.bat` - 已整合到主部署脚本
- `build-without-problematic-pages.js` - 已修复问题，不再需要
