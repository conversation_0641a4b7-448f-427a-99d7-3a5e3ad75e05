/**
 * LazyAIComponents - AI相关组件的动态导入包装器
 * 只在需要时加载 genkit AI 相关库，减少初始包体积
 */

'use client';

import React, { Suspense, lazy } from 'react';
import { LoadingSpinner } from '@/shared/components/LoadingSpinner';
import { Card, CardContent } from '@/shared/components/card';
import { Bot, Sparkles } from 'lucide-react';

// 动态导入 AI 相关组件和服务
const LazyAIService = lazy(() =>
  import('@/features/ai/genkit').then(module => ({
    default: module.getAI as any,
  }))
) as any;

/**
 * AI组件加载占位符
 */
function AILoadingFallback({ message = '加载AI组件...' }: { message?: string }) {
  return (
    <Card>
      <CardContent className='flex items-center justify-center p-8'>
        <Bot className='w-5 h-5 mr-2 animate-pulse' />
        <LoadingSpinner />
        <span className='ml-2 text-sm text-muted-foreground'>{message}</span>
      </CardContent>
    </Card>
  );
}

/**
 * AI推荐按钮的懒加载包装器
 */
export function LazyAIRecommendButton({
  onRecommend,
  disabled = false,
  children,
}: {
  onRecommend: () => Promise<void>;
  disabled?: boolean;
  children: React.ReactNode;
}) {
  const [isLoading, setIsLoading] = React.useState(false);

  const handleClick = async () => {
    setIsLoading(true);
    try {
      await onRecommend();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Suspense fallback={<AILoadingFallback message='加载AI推荐功能...' />}>
      <button
        onClick={handleClick}
        disabled={disabled || isLoading}
        className='inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-pink-500 rounded-md hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200'
      >
        {isLoading ? <LoadingSpinner className='w-4 h-4' /> : <Sparkles className='w-4 h-4' />}
        {children}
      </button>
    </Suspense>
  );
}

/**
 * AI生成面板的懒加载包装器
 */
export function LazyAIGenerationPanel({
  onGenerate,
  parameters,
  children,
}: {
  onGenerate: (params: any) => Promise<any>;
  parameters: any;
  children: React.ReactNode;
}) {
  return (
    <Suspense fallback={<AILoadingFallback message='加载AI生成面板...' />}>
      <div className='space-y-4'>{children}</div>
    </Suspense>
  );
}

/**
 * AI聊天界面的懒加载包装器
 */
export function LazyAIChatInterface({
  onSendMessage,
  messages = [],
  placeholder = '输入您的问题...',
}: {
  onSendMessage: (message: string) => Promise<void>;
  messages: Array<{ role: 'user' | 'assistant'; content: string }>;
  placeholder?: string;
}) {
  return (
    <Suspense fallback={<AILoadingFallback message='加载AI对话界面...' />}>
      <Card>
        <CardContent className='p-4'>
          <div className='space-y-4'>
            <div className='flex items-center gap-2 text-sm font-medium text-muted-foreground'>
              <Bot className='w-4 h-4' />
              AI助手
            </div>

            {/* 消息列表 */}
            <div className='space-y-2 max-h-60 overflow-y-auto'>
              {messages.map((message, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-blue-50 text-blue-900 ml-8'
                      : 'bg-gray-50 text-gray-900 mr-8'
                  }`}
                >
                  <div className='text-xs font-medium mb-1'>
                    {message.role === 'user' ? '您' : 'AI助手'}
                  </div>
                  <div className='text-sm'>{message.content}</div>
                </div>
              ))}
            </div>

            {/* 输入框 */}
            <div className='flex gap-2'>
              <input
                type='text'
                placeholder={placeholder}
                className='flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                onKeyPress={e => {
                  if (e.key === 'Enter') {
                    const target = e.target as HTMLInputElement;
                    onSendMessage(target.value);
                    target.value = '';
                  }
                }}
              />
              <button
                onClick={e => {
                  const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                  onSendMessage(input.value);
                  input.value = '';
                }}
                className='px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-md hover:bg-blue-600 transition-colors'
              >
                发送
              </button>
            </div>
          </div>
        </CardContent>
      </Card>
    </Suspense>
  );
}

export default {
  AIRecommendButton: LazyAIRecommendButton,
  AIGenerationPanel: LazyAIGenerationPanel,
  AIChatInterface: LazyAIChatInterface,
};
