/**
 * 任务列表头部模式切换组件
 * 用于在传统头部和悬浮头部之间切换
 */

'use client';

import { Button } from '@/shared/components/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/components/tooltip';
import { Layout, Move, Pin, PinOff, Settings } from 'lucide-react';

interface TaskListHeaderToggleProps {
  /** 是否使用悬浮头部 */
  useFloatingHeader: boolean;
  /** 切换头部模式 */
  onToggleHeaderMode: (useFloating: boolean) => void;
  /** 重置悬浮头部位置 */
  onResetFloatingPosition?: () => void;
  /** 是否显示在悬浮头部中 */
  inFloatingHeader?: boolean;
}

export function TaskListHeaderToggle({
  useFloatingHeader,
  onToggleHeaderMode,
  onResetFloatingPosition,
  inFloatingHeader = false,
}: TaskListHeaderToggleProps) {
  if (inFloatingHeader) {
    // 在悬浮头部中显示的简化版本
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant='ghost'
              size='sm'
              className='h-8 w-8 p-0'
              onClick={() => onToggleHeaderMode(false)}
            >
              <PinOff className='h-4 w-4' />
            </Button>
          </TooltipTrigger>
          <TooltipContent>切换到固定头部</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // 在传统头部或独立使用时的完整版本
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='outline' size='sm' className='h-8'>
          <Layout className='h-4 w-4 mr-2' />
          头部模式
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-48'>
        <DropdownMenuLabel>头部显示模式</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuCheckboxItem
          checked={!useFloatingHeader}
          onCheckedChange={() => onToggleHeaderMode(false)}
        >
          <Pin className='h-4 w-4 mr-2' />
          固定头部
        </DropdownMenuCheckboxItem>

        <DropdownMenuCheckboxItem
          checked={useFloatingHeader}
          onCheckedChange={() => onToggleHeaderMode(true)}
        >
          <Move className='h-4 w-4 mr-2' />
          悬浮头部
        </DropdownMenuCheckboxItem>

        {useFloatingHeader && onResetFloatingPosition && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={onResetFloatingPosition}>
              <Settings className='h-4 w-4 mr-2' />
              重置悬浮位置
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

/**
 * 快速切换按钮（用于工具栏）
 */
export function QuickHeaderToggle({
  useFloatingHeader,
  onToggleHeaderMode,
}: Pick<TaskListHeaderToggleProps, 'useFloatingHeader' | 'onToggleHeaderMode'>) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={useFloatingHeader ? 'default' : 'outline'}
            size='sm'
            className='h-8'
            onClick={() => onToggleHeaderMode(!useFloatingHeader)}
          >
            {useFloatingHeader ? <Move className='h-4 w-4' /> : <Pin className='h-4 w-4' />}
          </Button>
        </TooltipTrigger>
        <TooltipContent>{useFloatingHeader ? '切换到固定头部' : '切换到悬浮头部'}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
