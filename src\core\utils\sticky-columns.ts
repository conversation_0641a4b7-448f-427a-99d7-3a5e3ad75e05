/**
 * 固定列工具函数
 * 提供固定列相关的计算、验证和样式生成功能
 */
import {
  ShadowConfig,
  StickyColumnConfig,
  StickyColumnPosition,
  StickyColumnStyles,
  ZIndexLevels,
} from '../types/sticky-columns';

/**
 * 固定列工具类
 * 集中管理所有固定列相关的业务逻辑
 */
export class StickyColumnUtils {
  /**
   * 计算左固定列的偏移量
   * @param columnId 当前列ID
   * @param columns 所有左固定列配置
   * @param getColumnSize 获取列宽度的函数
   * @returns 左偏移量（像素）
   */
  static calculateLeftOffset(
    columnId: string,
    columns: StickyColumnConfig[],
    getColumnSize: (id: string) => number
  ): number {
    const sortedColumns = columns
      .filter(col => col.position === StickyColumnPosition.LEFT)
      .sort((a, b) => a.order - b.order);

    let offset = 0;
    for (const col of sortedColumns) {
      if (col.columnId === columnId) break;
      offset += getColumnSize(col.columnId);
    }

    return offset;
  }

  /**
   * 计算右固定列的偏移量
   * @param columnId 当前列ID
   * @param columns 所有右固定列配置
   * @param getColumnSize 获取列宽度的函数
   * @returns 右偏移量（像素）
   */
  static calculateRightOffset(
    columnId: string,
    columns: StickyColumnConfig[],
    getColumnSize: (id: string) => number
  ): number {
    const sortedColumns = columns
      .filter(col => col.position === StickyColumnPosition.RIGHT)
      .sort((a, b) => a.order - b.order);

    const currentIndex = sortedColumns.findIndex(col => col.columnId === columnId);
    if (currentIndex === -1) return 0;

    let offset = 0;
    for (let i = currentIndex + 1; i < sortedColumns.length; i++) {
      offset += getColumnSize(sortedColumns[i]?.columnId || '');
    }

    return offset;
  }

  /**
   * 生成固定列的样式对象
   * @param config 固定列配置
   * @param isHeader 是否为表头
   * @param offset 偏移量
   * @param columnSize 列宽度
   * @returns 样式对象
   */
  static generateStickyStyles(
    config: StickyColumnConfig,
    isHeader: boolean,
    offset: number,
    columnSize: number
  ): StickyColumnStyles {
    const styles: StickyColumnStyles = {
      position: 'sticky',
      zIndex: isHeader ? ZIndexLevels.STICKY_HEADER : ZIndexLevels.STICKY_BODY,
      width: columnSize,
      minWidth: config.minWidth,
      maxWidth: config.maxWidth,
    };

    if (config.position === StickyColumnPosition.LEFT) {
      styles.left = `${offset}px`;
    } else if (config.position === StickyColumnPosition.RIGHT) {
      styles.right = `${offset}px`;
    }

    return styles;
  }

  /**
   * 生成阴影CSS字符串
   * @param config 阴影配置
   * @returns CSS box-shadow 值
   */
  static generateShadowCSS(config: ShadowConfig): string {
    return `${config.offsetX}px ${config.offsetY}px ${config.blur}px ${config.spread}px ${config.color}`;
  }

  /**
   * 判断是否为最后一个左固定列
   * @param columnId 列ID
   * @param leftColumns 左固定列配置数组
   * @returns 是否为最后一个左固定列
   */
  static isLastLeftFixed(columnId: string, leftColumns: StickyColumnConfig[]): boolean {
    if (leftColumns.length === 0) return false;

    const sortedColumns = leftColumns
      .filter(col => col.position === StickyColumnPosition.LEFT)
      .sort((a, b) => a.order - b.order);

    return sortedColumns[sortedColumns.length - 1]?.columnId === columnId;
  }

  /**
   * 判断是否为第一个右固定列
   * @param columnId 列ID
   * @param rightColumns 右固定列配置数组
   * @returns 是否为第一个右固定列
   */
  static isFirstRightFixed(columnId: string, rightColumns: StickyColumnConfig[]): boolean {
    if (rightColumns.length === 0) return false;

    const sortedColumns = rightColumns
      .filter(col => col.position === StickyColumnPosition.RIGHT)
      .sort((a, b) => a.order - b.order);

    return sortedColumns[0]?.columnId === columnId;
  }

  /**
   * 验证固定列配置的有效性
   * @param config 固定列配置
   * @returns 配置是否有效
   */
  static validateStickyConfig(config: StickyColumnConfig): boolean {
    // 检查必填字段
    if (!config.columnId || !config.position) {
      console.warn('固定列配置缺少必填字段:', config);
      return false;
    }

    // 检查位置枚举值
    if (!Object.values(StickyColumnPosition).includes(config.position)) {
      console.warn('无效的固定列位置:', config.position);
      return false;
    }

    // 检查排序值
    if (typeof config.order !== 'number' || config.order < 0) {
      console.warn('无效的排序值:', config.order);
      return false;
    }

    // 检查宽度配置
    if (config.minWidth && config.maxWidth && config.minWidth > config.maxWidth) {
      console.warn('最小宽度不能大于最大宽度:', config);
      return false;
    }

    return true;
  }

  /**
   * 创建默认的阴影配置
   * @param position 固定位置
   * @returns 默认阴影配置
   */
  static createDefaultShadowConfig(position: StickyColumnPosition): ShadowConfig {
    const baseConfig: ShadowConfig = {
      offsetX: position === StickyColumnPosition.LEFT ? 8 : -8,
      offsetY: 0,
      blur: 15,
      spread: -3,
      color: 'rgba(0, 0, 0, 0.3)',
    };

    return baseConfig;
  }

  /**
   * 合并用户自定义阴影配置
   * @param defaultConfig 默认配置
   * @param userConfig 用户配置
   * @returns 合并后的配置
   */
  static mergeShadowConfig(
    defaultConfig: ShadowConfig,
    userConfig?: Partial<ShadowConfig>
  ): ShadowConfig {
    return {
      ...defaultConfig,
      ...userConfig,
    };
  }

  /**
   * 获取响应式阴影配置
   * @param position 固定位置
   * @param isMobile 是否为移动端
   * @returns 响应式阴影配置
   */
  static getResponsiveShadowConfig(
    position: StickyColumnPosition,
    isMobile: boolean = false
  ): ShadowConfig {
    const baseConfig = this.createDefaultShadowConfig(position);

    if (isMobile) {
      return {
        ...baseConfig,
        offsetX: position === StickyColumnPosition.LEFT ? 6 : -6,
        blur: 12,
        spread: -2,
      };
    }

    return baseConfig;
  }

  /**
   * 生成CSS类名
   * @param columnId 列ID
   * @param position 固定位置
   * @param showShadow 是否显示阴影
   * @returns CSS类名数组
   */
  static generateClassNames(
    columnId: string,
    position: StickyColumnPosition,
    showShadow: boolean = true
  ): string[] {
    const classNames: string[] = ['sticky-column-base'];

    if (position === StickyColumnPosition.LEFT) {
      classNames.push('sticky-column-left');
      if (showShadow) classNames.push('sticky-col-shadow-right');
    } else if (position === StickyColumnPosition.RIGHT) {
      classNames.push('sticky-column-right');
      if (showShadow) classNames.push('sticky-col-shadow-left');
    }

    return classNames;
  }
}

/**
 * 导出便捷的工具函数
 */
export const {
  calculateLeftOffset,
  calculateRightOffset,
  generateStickyStyles,
  generateShadowCSS,
  isLastLeftFixed,
  isFirstRightFixed,
  validateStickyConfig,
  createDefaultShadowConfig,
  mergeShadowConfig,
  getResponsiveShadowConfig,
  generateClassNames,
} = StickyColumnUtils;
