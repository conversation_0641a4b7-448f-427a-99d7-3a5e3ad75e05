# ❓ 常见问题解答 (FAQ)

## 🚀 快速开始

### Q: 如何快速启动项目？
**A**: 按照以下步骤：
```bash
git clone https://github.com/your-org/tmh-task-dispatcher.git
cd tmh-task-dispatcher
npm install
npm run dev
```
然后访问 http://localhost:3000

### Q: 系统要求是什么？
**A**: 
- Node.js 18.0.0 或更高版本
- npm 9.0.0 或更高版本
- 现代浏览器（Chrome 90+, Firefox 88+, Safari 14+）
- 至少 8GB RAM（推荐 16GB）

### Q: 支持哪些操作系统？
**A**: 支持 Windows 10+、macOS 10.15+ 和主流 Linux 发行版。

## 🏗️ 技术架构

### Q: 为什么选择 Next.js 而不是 Create React App？
**A**: Next.js 提供了：
- 更好的性能优化（SSR/SSG）
- 内置的路由系统
- 更好的开发体验
- 生产就绪的配置

### Q: 为什么使用 Zustand 而不是 Redux？
**A**: Zustand 的优势：
- 更简单的 API
- 更少的样板代码
- 更好的 TypeScript 支持
- 更小的包体积

### Q: 可以切换到其他 UI 库吗？
**A**: 可以，但需要大量工作。当前使用 shadcn/ui 是因为：
- 基于 Radix UI，可访问性好
- 完全可定制
- TypeScript 原生支持
- 与 Tailwind CSS 完美集成

## 📋 功能使用

### Q: 如何创建新任务？
**A**: 
1. 点击任务列表上方的"新建任务"按钮
2. 填写任务信息（项目名称、混凝土规格等）
3. 设置交付时间和优先级
4. 点击"保存"创建任务

### Q: 如何进行车辆调度？
**A**: 
1. 在右侧车辆面板找到可用车辆
2. 将车辆拖拽到左侧的目标任务上
3. 系统会自动检查调度可行性
4. 确认后车辆状态会更新为"已调度"

### Q: 任务状态有哪些？
**A**: 
- **待处理**: 新创建的任务
- **进行中**: 已开始生产的任务
- **已完成**: 已交付的任务
- **已取消**: 被取消的任务
- **暂停**: 临时暂停的任务

### Q: 如何批量操作任务？
**A**: 
1. 在表格视图中勾选多个任务
2. 点击工具栏中的批量操作按钮
3. 选择要执行的操作（删除、更新状态等）
4. 确认操作

## 🎨 界面定制

### Q: 如何切换深色模式？
**A**: 点击右上角的主题切换按钮，或在设置页面中配置默认主题。

### Q: 可以自定义任务卡片显示的字段吗？
**A**: 可以，在设置页面的"显示配置"中可以：
- 选择显示的字段
- 调整字段顺序
- 设置字段样式
- 配置卡片布局

### Q: 如何调整表格列的显示？
**A**: 
- 右键点击表格头部可以显示/隐藏列
- 拖拽列头可以调整列顺序
- 拖拽列边界可以调整列宽

## 🔧 配置和设置

### Q: 数据存储在哪里？
**A**: 
- 开发环境：浏览器 localStorage
- 生产环境：可配置为数据库或云存储
- 支持数据导入导出功能

### Q: 如何备份配置？
**A**: 
1. 进入设置页面
2. 点击"数据管理"选项卡
3. 选择"导出配置"
4. 保存配置文件到本地

### Q: 如何恢复配置？
**A**: 
1. 进入设置页面
2. 点击"数据管理"选项卡
3. 选择"导入配置"
4. 选择之前导出的配置文件

## 🚛 车辆管理

### Q: 如何添加新车辆？
**A**: 
1. 进入车辆管理页面
2. 点击"添加车辆"按钮
3. 填写车辆信息（车牌号、类型、容量等）
4. 保存车辆信息

### Q: 车辆状态有哪些？
**A**: 
- **空闲**: 可用于调度
- **已调度**: 已分配任务
- **运输中**: 正在运输
- **维护中**: 正在维护
- **离线**: 不可用

### Q: 如何取消车辆调度？
**A**: 
1. 在任务详情中找到已调度的车辆
2. 点击车辆旁边的"取消调度"按钮
3. 确认取消操作
4. 车辆状态会恢复为"空闲"

## 🧪 配比管理

### Q: 如何创建新的混凝土配比？
**A**: 
1. 进入配比管理页面
2. 点击"新建配比"
3. 填写配比信息（强度等级、材料比例等）
4. 保存配比方案

### Q: 配比质量检测标准是什么？
**A**: 系统内置了常见的混凝土质量标准：
- 抗压强度标准
- 坍落度要求
- 含气量标准
- 氯离子含量限制

### Q: 如何计算配比成本？
**A**: 
1. 在配比详情页面
2. 输入各材料的单价
3. 系统会自动计算每立方米成本
4. 可以导出成本分析报告

## 🔍 故障排除

### Q: 页面加载很慢怎么办？
**A**: 
1. 检查网络连接
2. 清除浏览器缓存
3. 尝试刷新页面
4. 如果问题持续，联系技术支持

### Q: 拖拽功能不工作？
**A**: 
1. 确保使用支持的浏览器
2. 检查是否启用了拖拽功能
3. 尝试刷新页面
4. 检查控制台是否有错误信息

### Q: 数据没有保存？
**A**: 
1. 检查网络连接
2. 确认操作权限
3. 查看是否有错误提示
4. 尝试重新保存

## 📱 移动端使用

### Q: 支持移动端吗？
**A**: 支持，但功能有限：
- 可以查看任务列表
- 可以查看车辆状态
- 部分编辑功能可用
- 拖拽功能在移动端体验较差

### Q: 有移动端 App 吗？
**A**: 目前没有原生 App，但可以：
- 在浏览器中添加到主屏幕
- 使用 PWA 功能离线访问
- 计划未来开发原生 App

## 🔐 安全和权限

### Q: 如何管理用户权限？
**A**: 
1. 进入用户管理页面
2. 选择用户并编辑权限
3. 设置角色和权限范围
4. 保存权限配置

### Q: 支持单点登录 (SSO) 吗？
**A**: 计划在未来版本中支持，当前版本使用内置的用户认证系统。

### Q: 数据安全如何保障？
**A**: 
- 所有数据传输使用 HTTPS 加密
- 敏感数据在存储时加密
- 定期安全审计
- 访问日志记录

## 🚀 性能优化

### Q: 如何提高大数据量下的性能？
**A**: 
1. 启用虚拟滚动
2. 使用分页加载
3. 合理设置筛选条件
4. 定期清理历史数据

### Q: 浏览器兼容性如何？
**A**: 支持的浏览器：
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📞 技术支持

### Q: 如何获取技术支持？
**A**: 
- 查看项目文档
- 提交 GitHub Issue
- 发送邮件到 <EMAIL>
- 加入开发者社区讨论

### Q: 如何报告 Bug？
**A**: 
1. 在 GitHub 上创建 Issue
2. 使用 Bug 报告模板
3. 提供详细的复现步骤
4. 附上错误截图或日志

### Q: 如何提出功能建议？
**A**: 
1. 在 GitHub Discussions 中讨论
2. 创建功能请求 Issue
3. 参与社区投票
4. 考虑贡献代码实现

## 🔄 更新和升级

### Q: 如何更新到最新版本？
**A**: 
```bash
git pull origin main
npm install
npm run build
```

### Q: 更新会丢失数据吗？
**A**: 不会，但建议：
1. 更新前备份配置
2. 查看更新日志
3. 在测试环境先验证
4. 逐步部署到生产环境

### Q: 如何回滚到之前版本？
**A**: 
```bash
git checkout v0.0.3  # 回滚到指定版本
npm install
npm run build
```

---

**文档版本**: v1.0  
**最后更新**: 2025/7/16  
**维护者**: TMH 用户支持团队

如果您的问题没有在这里找到答案，请通过以下方式联系我们：
- 📧 Email: <EMAIL>
- 💬 GitHub Discussions: [项目讨论区](https://github.com/your-org/tmh-task-dispatcher/discussions)
- 🐛 Bug 报告: [GitHub Issues](https://github.com/your-org/tmh-task-dispatcher/issues)
