/**
 * 配比核心状态管理Hook
 * 统一所有版本的配比状态管理逻辑
 */

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import type {
  RatioCoreData,
  RatioCoreMaterial,
  RatioCoreCalculationParams,
  RatioCoreCalculationResults,
  RatioCoreOperationStatus,
  RatioCoreOperations,
  RatioCoreConfig,
  RatioCoreEvents,
} from '@/core/types/ratio-core';

import { RatioDataManager } from '@/features/ratio-management/services/ratio-core/RatioDataManager';
import { RatioCalculationEngine } from '@/features/ratio-management/services/ratio-core/RatioCalculationEngine';

/**
 * useRatioCore Hook 配置选项
 */
export interface UseRatioCoreOptions {
  taskId: string;
  version: 'v1' | 'v2';
  autoLoad?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
  useMockData?: boolean;
  apiEndpoint?: string;
  cacheEnabled?: boolean;
  cacheTTL?: number;
  events?: Partial<RatioCoreEvents>;
}

/**
 * useRatioCore Hook 返回值
 */
export interface UseRatioCoreReturn extends RatioCoreOperationStatus, RatioCoreOperations {
  // 核心数据
  currentRatio: RatioCoreData | null;
  materials: RatioCoreMaterial[];
  calculationParams: RatioCoreCalculationParams | null;
  calculationResults: RatioCoreCalculationResults | null;

  // 工具方法
  resetRatio: () => void;
  validateRatio: () => boolean;
  exportRatio: () => string;
  importRatio: (data: string) => void;
}

/**
 * 配比核心状态管理Hook
 */
export function useRatioCore(options: UseRatioCoreOptions): UseRatioCoreReturn {
  const {
    taskId,
    version,
    autoLoad = true,
    autoSave = false,
    autoSaveDelay = 3000,
    useMockData = true,
    apiEndpoint = '/api/ratio',
    cacheEnabled = true,
    cacheTTL = 5 * 60 * 1000,
    events = {},
  } = options;

  // ==================== 状态定义 ====================

  const [currentRatio, setCurrentRatio] = useState<RatioCoreData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCalculating, setIsCalculating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDirty, setIsDirty] = useState(false);
  const [hasParamsChanged, setHasParamsChanged] = useState(false);

  // ==================== 服务实例 ====================

  const config: RatioCoreConfig = useMemo(
    () => ({
      version,
      apiEndpoint,
      useMockData,
      autoSave,
      autoSaveDelay,
      cacheEnabled,
      cacheTTL,
    }),
    [version, apiEndpoint, useMockData, autoSave, autoSaveDelay, cacheEnabled, cacheTTL]
  );

  const dataManager = useMemo(() => new RatioDataManager(config), [config]);
  const calculationEngine = useMemo(() => new RatioCalculationEngine(config), [config]);

  // ==================== 自动保存 ====================

  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const scheduleAutoSave = useCallback(() => {
    if (!autoSave || !isDirty || !currentRatio) return;

    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    autoSaveTimeoutRef.current = setTimeout(async () => {
      try {
        await saveRatio();
      } catch (error) {
        console.error('自动保存失败:', error);
      }
    }, autoSaveDelay);
  }, [autoSave, isDirty, currentRatio, autoSaveDelay]);

  useEffect(() => {
    scheduleAutoSave();
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [scheduleAutoSave]);

  // ==================== 数据操作 ====================

  const loadRatio = useCallback(
    async (loadTaskId?: string) => {
      const targetTaskId = loadTaskId || taskId;
      setIsLoading(true);
      setError(null);

      try {
        const ratioData = await dataManager.loadRatio(targetTaskId);
        setCurrentRatio(ratioData);
        setIsDirty(false);
        setHasParamsChanged(false);

        events.onRatioLoaded?.(ratioData!);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '加载配比数据失败';
        setError(errorMessage);
        events.onError?.(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [taskId, dataManager, events]
  );

  const saveRatio = useCallback(async () => {
    if (!currentRatio) return;

    setIsSaving(true);
    setError(null);

    try {
      await dataManager.saveRatio(currentRatio);
      setIsDirty(false);

      events.onRatioSaved?.(currentRatio);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '保存配比数据失败';
      setError(errorMessage);
      events.onError?.(errorMessage);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [currentRatio, dataManager, events]);

  const clearRatio = useCallback(() => {
    setCurrentRatio(null);
    setIsDirty(false);
    setHasParamsChanged(false);
    setError(null);
    dataManager.clearCache(taskId);
  }, [dataManager, taskId]);

  // ==================== 材料操作 ====================

  const addMaterial = useCallback(
    (material: RatioCoreMaterial) => {
      if (!currentRatio) return;

      const updatedRatio = dataManager.addMaterial(currentRatio, material);
      setCurrentRatio(updatedRatio);
      setIsDirty(true);

      events.onMaterialAdded?.(material);
    },
    [currentRatio, dataManager, events]
  );

  const removeMaterial = useCallback(
    (materialId: string) => {
      if (!currentRatio) return;

      const updatedRatio = dataManager.removeMaterial(currentRatio, materialId);
      setCurrentRatio(updatedRatio);
      setIsDirty(true);

      events.onMaterialRemoved?.(materialId);
    },
    [currentRatio, dataManager, events]
  );

  const updateMaterial = useCallback(
    (materialId: string, updates: Partial<RatioCoreMaterial>) => {
      if (!currentRatio) return;

      const updatedRatio = dataManager.updateMaterial(currentRatio, materialId, updates);
      setCurrentRatio(updatedRatio);
      setIsDirty(true);

      const updatedMaterial = updatedRatio.materials.find(m => m.id === materialId);
      if (updatedMaterial) {
        events.onMaterialUpdated?.(materialId, updatedMaterial);
      }
    },
    [currentRatio, dataManager, events]
  );

  // ==================== 计算操作 ====================

  const updateCalculationParams = useCallback(
    (updates: Partial<RatioCoreCalculationParams>) => {
      if (!currentRatio) return;

      const updatedRatio = dataManager.updateCalculationParams(currentRatio, updates);
      setCurrentRatio(updatedRatio);
      setIsDirty(true);
      setHasParamsChanged(true);
    },
    [currentRatio, dataManager]
  );

  const calculate = useCallback(async () => {
    if (!currentRatio) return;

    setIsCalculating(true);
    setError(null);

    try {
      const results = await calculationEngine.calculate(
        currentRatio.materials,
        currentRatio.calculationParams
      );

      const updatedRatio: RatioCoreData = {
        ...currentRatio,
        calculationResults: results,
        updatedAt: new Date().toISOString(),
      };

      setCurrentRatio(updatedRatio);
      setIsDirty(true);
      setHasParamsChanged(false);

      events.onCalculationCompleted?.(results);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '计算失败';
      setError(errorMessage);
      events.onError?.(errorMessage);
    } finally {
      setIsCalculating(false);
    }
  }, [currentRatio, calculationEngine, events]);

  const reverseCalculate = useCallback(async () => {
    if (!currentRatio) return;

    setIsCalculating(true);
    setError(null);

    try {
      const results = await calculationEngine.reverseCalculate(
        currentRatio.materials,
        currentRatio.calculationParams
      );

      const updatedRatio: RatioCoreData = {
        ...currentRatio,
        calculationResults: results,
        updatedAt: new Date().toISOString(),
      };

      setCurrentRatio(updatedRatio);
      setIsDirty(true);
      setHasParamsChanged(false);

      events.onCalculationCompleted?.(results);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '反向计算失败';
      setError(errorMessage);
      events.onError?.(errorMessage);
    } finally {
      setIsCalculating(false);
    }
  }, [currentRatio, calculationEngine, events]);

  // ==================== 工具方法 ====================

  const canSave = useCallback(() => {
    return currentRatio !== null && isDirty && !isSaving;
  }, [currentRatio, isDirty, isSaving]);

  const canCalculate = useCallback(() => {
    return currentRatio !== null && currentRatio.materials.length > 0 && !isCalculating;
  }, [currentRatio, isCalculating]);

  const hasUnsavedChanges = useCallback(() => {
    return isDirty;
  }, [isDirty]);

  const resetRatio = useCallback(() => {
    if (currentRatio) {
      // 重置为初始状态，清除计算结果
      const resetRatio: RatioCoreData = {
        ...currentRatio,
        calculationResults: undefined,
        updatedAt: new Date().toISOString(),
      };
      setCurrentRatio(resetRatio);
      setIsDirty(true);
      setHasParamsChanged(false);
    }
  }, [currentRatio]);

  const validateRatio = useCallback(() => {
    if (!currentRatio) return false;

    // 基本验证
    const hasValidMaterials = currentRatio.materials.length > 0;
    const hasValidParams = currentRatio.calculationParams !== null;

    return hasValidMaterials && hasValidParams;
  }, [currentRatio]);

  const exportRatio = useCallback(() => {
    if (!currentRatio) return '';
    return JSON.stringify(currentRatio, null, 2);
  }, [currentRatio]);

  const importRatio = useCallback((data: string) => {
    try {
      const importedRatio: RatioCoreData = JSON.parse(data);
      setCurrentRatio(importedRatio);
      setIsDirty(true);
    } catch (error) {
      setError('导入数据格式错误');
    }
  }, []);

  // ==================== 初始化 ====================

  useEffect(() => {
    if (autoLoad && taskId) {
      loadRatio();
    }
  }, [autoLoad, taskId, loadRatio]);

  // ==================== 计算属性 ====================

  const materials = useMemo(() => {
    return currentRatio?.materials || [];
  }, [currentRatio]);

  const calculationParams = useMemo(() => {
    return currentRatio?.calculationParams || null;
  }, [currentRatio]);

  const calculationResults = useMemo(() => {
    return currentRatio?.calculationResults || null;
  }, [currentRatio]);

  // ==================== 返回值 ====================

  return {
    // 核心数据
    currentRatio,
    materials,
    calculationParams,
    calculationResults,

    // 状态
    isLoading,
    isCalculating,
    isSaving,
    error,
    isDirty,
    hasParamsChanged,

    // 数据操作
    loadRatio,
    saveRatio,
    clearRatio,

    // 材料操作
    addMaterial,
    removeMaterial,
    updateMaterial,

    // 计算操作
    updateCalculationParams,
    calculate,
    reverseCalculate,

    // 工具方法
    canSave,
    canCalculate,
    hasUnsavedChanges,
    resetRatio,
    validateRatio,
    exportRatio,
    importRatio,
  };
}
