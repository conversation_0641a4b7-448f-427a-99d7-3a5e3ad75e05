/**
 * 配比计算业务Hook
 * 封装配比计算相关的业务逻辑
 */

import { useCallback } from 'react';
import { useRatioStoreOrchestrator } from '@/features/ratio-management/store/ratioStoreOrchestrator';
import type {
  UseRatioCalculationReturn,
  RatioCalculationParams,
  ReverseCalculationParams,
  RatioCalculationMethod,
} from '@/core/types/ratio';

/**
 * 配比计算Hook
 * 提供配比计算相关的业务逻辑和状态管理
 */
export function useRatioCalculation(): UseRatioCalculationReturn {
  const orchestrator = useRatioStoreOrchestrator();

  // 设置计算参数
  const setCalculationParams = useCallback(
    (params: Partial<RatioCalculationParams>) => {
      orchestrator.actions.setCalculationParams(params);
    },
    [orchestrator.actions]
  );

  // 设置反算参数
  const setReverseParam = useCallback(
    (key: keyof ReverseCalculationParams, value: number) => {
      orchestrator.actions.setReverseParam(key, value);
    },
    [orchestrator.actions]
  );

  // 设置计算方法
  const setCalculationMethod = useCallback(
    (method: RatioCalculationMethod) => {
      orchestrator.actions.setCalculationMethod(method);
    },
    [orchestrator.actions]
  );

  // 执行计算
  const calculate = useCallback(async () => {
    await orchestrator.actions.calculate();
  }, [orchestrator.actions]);

  // 执行反算
  const reverseCalculate = useCallback(async () => {
    await orchestrator.actions.reverseCalculate();
  }, [orchestrator.actions]);

  // 验证计算参数
  const validateParams = useCallback((params: Partial<RatioCalculationParams>) => {
    const errors: string[] = [];

    if (
      params.targetStrength !== undefined &&
      (params.targetStrength < 10 || params.targetStrength > 100)
    ) {
      errors.push('目标强度应在10-100MPa之间');
    }

    if (params.slump !== undefined && (params.slump < 50 || params.slump > 250)) {
      errors.push('坍落度应在50-250mm之间');
    }

    if (
      params.waterCementRatio !== undefined &&
      (params.waterCementRatio < 0.2 || params.waterCementRatio > 0.8)
    ) {
      errors.push('水胶比应在0.2-0.8之间');
    }

    if (params.sandRatio !== undefined && (params.sandRatio < 20 || params.sandRatio > 60)) {
      errors.push('砂率应在20%-60%之间');
    }

    if (params.density !== undefined && (params.density < 2000 || params.density > 3000)) {
      errors.push('密度应在2000-3000kg/m³之间');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }, []);

  // 计算配比强度预测
  const predictStrength = useCallback((params: RatioCalculationParams) => {
    // 简化的强度预测公式（实际应用中需要更复杂的算法）
    const baseStrength = params.cementContent * 0.08; // 基础强度
    const waterEffect = (0.5 - params.waterCementRatio) * 50; // 水胶比影响
    const flyAshEffect = params.flyashRatio * 0.3; // 粉煤灰影响
    const mineralEffect = params.mineralPowderRatio * 0.4; // 矿粉影响

    const predictedStrength = baseStrength + waterEffect + flyAshEffect + mineralEffect;

    return Math.max(10, Math.min(100, predictedStrength));
  }, []);

  // 优化配比参数
  const optimizeParams = useCallback(
    (targetStrength: number, currentParams: RatioCalculationParams) => {
      const optimized = { ...currentParams };

      // 根据目标强度调整水胶比
      if (targetStrength > 40) {
        optimized.waterCementRatio = Math.min(0.4, optimized.waterCementRatio);
      } else if (targetStrength > 25) {
        optimized.waterCementRatio = Math.min(0.5, optimized.waterCementRatio);
      }

      // 根据强度要求调整水泥用量
      const minCementContent = targetStrength * 10; // 简化公式
      optimized.cementContent = Math.max(minCementContent, optimized.cementContent);

      // 调整用水量
      optimized.waterContent = optimized.cementContent * optimized.waterCementRatio;

      return optimized;
    },
    []
  );

  // 计算材料成本
  const calculateCost = useCallback((params: RatioCalculationParams) => {
    // 材料单价（元/吨）
    const prices = {
      cement: 450,
      water: 3,
      sand: 80,
      stone: 70,
      flyAsh: 200,
      mineralPowder: 180,
      admixture: 8000,
      antifreeze: 12000,
    };

    const cost =
      (params.cementAmount / 1000) * prices.cement +
      (params.waterAmount / 1000) * prices.water +
      (params.flyAshAmount / 1000) * prices.flyAsh +
      (params.mineralPowderAmount / 1000) * prices.mineralPowder +
      (params.admixtureAmount / 1000) * prices.admixture +
      (params.antifreezeAmount / 1000) * prices.antifreeze;

    return Math.round(cost * 100) / 100; // 保留两位小数
  }, []);

  return {
    // 计算参数
    calculationParams: orchestrator.data.calculationParams,
    reverseParams: orchestrator.data.reverseParams,
    calculationMethod: orchestrator.data.calculationMethod,

    // 计算结果
    proportions: orchestrator.data.proportions,

    // 操作方法
    setCalculationParams,
    setReverseParam,
    setCalculationMethod,
    calculate,
    reverseCalculate,

    // 状态
    isCalculating: orchestrator.ui.isCalculating,
    error: orchestrator.ui.error,

    // 工具方法
    validateParams,
    predictStrength,
  };
}
