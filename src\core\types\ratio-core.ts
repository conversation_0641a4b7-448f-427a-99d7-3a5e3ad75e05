/**
 * 配比系统核心类型定义
 * 统一所有版本的类型定义，消除重复代码
 */

// ==================== 基础枚举类型 ====================

export enum MaterialCategory {
  CEMENT = 'cement',
  WATER = 'water',
  SAND = 'sand',
  GRAVEL = 'gravel',
  ADDITIVE = 'additive',
  FLY_ASH = 'fly_ash',
  MINERAL_POWDER = 'mineral_powder',
  ADMIXTURE = 'admixture',
}

export enum ExposureClass {
  XC1 = 'XC1', // 干燥或永久湿润
  XC2 = 'XC2', // 湿润，很少干燥
  XC3 = 'XC3', // 中等湿度
  XC4 = 'XC4', // 干湿交替
  XD1 = 'XD1', // 中等湿度，氯化物
  XD2 = 'XD2', // 湿润，很少干燥，氯化物
  XD3 = 'XD3', // 干湿交替，氯化物
  XS1 = 'XS1', // 海水环境，暴露在空气中
  XS2 = 'XS2', // 海水环境，永久浸没
  XS3 = 'XS3', // 海水环境，潮汐、飞溅和喷雾区
}

export enum PlacementMethod {
  PUMP = 'pump', // 泵送
  CRANE = 'crane', // 吊斗
  CONVEYOR = 'conveyor', // 输送带
  DIRECT = 'direct', // 直接浇筑
}

export enum FinishingRequirement {
  SMOOTH = 'smooth', // 光滑面
  ROUGH = 'rough', // 粗糙面
  TEXTURED = 'textured', // 纹理面
  EXPOSED = 'exposed', // 清水混凝土
}

export enum CalculationMethod {
  STANDARD = 'standard', // 标准计算法
  ABSOLUTE_VOLUME = 'absolute_volume', // 绝对体积法
  MASS_METHOD = 'mass_method', // 质量法
  EMPIRICAL = 'empirical', // 经验法
}

// ==================== 核心数据结构 ====================

/**
 * 统一的材料定义
 * 兼容所有版本的材料数据结构
 */
export interface RatioCoreMaterial {
  id: string;
  name: string;
  category: MaterialCategory;

  // 设计参数
  designValue: number; // 设计用量
  actualValue?: number; // 实际用量
  unit: string; // 单位（kg/m³, %, 等）

  // 材料属性
  density?: number; // 密度
  specificGravity?: number; // 比重
  absorptionRate?: number; // 吸水率
  fineness?: number; // 细度

  // 质量参数
  grade?: string; // 等级/标号
  supplier?: string; // 供应商
  batchNumber?: string; // 批次号

  // 计算相关
  ratio?: number; // 配比（相对于水泥）
  percentage?: number; // 百分比

  // 版本兼容性
  version?: 'v1' | 'v2';
  metadata?: Record<string, any>; // 版本特定数据
}

/**
 * 统一的计算参数
 */
export interface RatioCoreCalculationParams {
  // 基本参数
  strengthGrade: number; // 强度等级 (MPa)
  slump: number; // 坍落度 (mm)
  maxAggregateSize: number; // 最大骨料粒径 (mm)

  // 环境参数
  exposureClass: ExposureClass;
  placementMethod: PlacementMethod;
  finishingRequirement: FinishingRequirement;

  // 配合比参数
  waterCementRatio: number; // 水胶比
  sandRatio: number; // 砂率 (%)
  cementAmount: number; // 水泥用量 (kg/m³)
  waterAmount: number; // 用水量 (kg/m³)

  // 高级参数
  density?: number; // 表观密度 (kg/m³)
  airContent?: number; // 含气量 (%)
  ultraFineSandRatio?: number; // 特细砂掺量 (%)
  earlyStrengthRatio?: number; // 早强剂掺量 (%)

  // 养护条件
  cureConditions?: {
    method: string; // 养护方法
    duration: number; // 养护时间 (天)
    temperature: number; // 养护温度 (°C)
    humidity: number; // 养护湿度 (%)
  };

  // 计算方法
  calculationMethod: CalculationMethod;

  // 版本兼容性
  version?: 'v1' | 'v2';
  metadata?: Record<string, any>;
}

/**
 * 统一的计算结果
 */
export interface RatioCoreCalculationResults {
  // 基本结果
  totalWeight: number; // 总重量 (kg/m³)
  materials: Record<string, number>; // 各材料用量

  // 性能预测
  strengthPrediction: number; // 强度预测 (MPa)
  workabilityScore: number; // 工作性评分
  durabilityScore: number; // 耐久性评分
  qualityScore: number; // 综合质量评分

  // 分析结果
  warnings: RatioCoreWarning[]; // 警告信息
  suggestions: RatioCoreSuggestion[]; // 优化建议

  // 成本和环保
  costEstimate: number; // 成本估算
  carbonFootprint: number; // 碳足迹

  // 计算元数据
  calculationTime: number; // 计算耗时 (ms)
  calculationMethod: CalculationMethod;
  timestamp: string; // 计算时间戳

  // 版本兼容性
  version?: 'v1' | 'v2';
  metadata?: Record<string, any>;
}

/**
 * 警告信息
 */
export interface RatioCoreWarning {
  id: string;
  type: 'error' | 'warning' | 'info';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  field?: string; // 相关字段
  suggestion?: string; // 建议解决方案
}

/**
 * 优化建议
 */
export interface RatioCoreSuggestion {
  id: string;
  type: 'optimization' | 'cost_reduction' | 'performance_improvement' | 'environmental';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  parameters?: Record<string, number>; // 建议的参数调整
}

// ==================== 配比数据结构 ====================

/**
 * 统一的配比数据
 */
export interface RatioCoreData {
  id: string;
  taskId: string;
  name: string;
  description?: string;

  // 核心数据
  materials: RatioCoreMaterial[];
  calculationParams: RatioCoreCalculationParams;
  calculationResults?: RatioCoreCalculationResults;

  // 元数据
  version: 'v1' | 'v2';
  createdAt: string;
  updatedAt: string;
  createdBy?: string;

  // 状态信息
  status: 'draft' | 'calculated' | 'approved' | 'archived';
  tags?: string[];

  // 版本特定数据
  metadata?: Record<string, any>;
}

// ==================== 操作接口 ====================

/**
 * 配比操作状态
 */
export interface RatioCoreOperationStatus {
  isLoading: boolean;
  isCalculating: boolean;
  isSaving: boolean;
  error: string | null;
  isDirty: boolean;
  hasParamsChanged: boolean;
}

/**
 * 配比操作接口
 */
export interface RatioCoreOperations {
  // 数据操作
  loadRatio: (taskId: string) => Promise<void>;
  saveRatio: () => Promise<void>;
  clearRatio: () => void;

  // 材料操作
  addMaterial: (material: RatioCoreMaterial) => void;
  removeMaterial: (materialId: string) => void;
  updateMaterial: (materialId: string, updates: Partial<RatioCoreMaterial>) => void;

  // 计算操作
  updateCalculationParams: (params: Partial<RatioCoreCalculationParams>) => void;
  calculate: () => Promise<void>;
  reverseCalculate: () => Promise<void>;

  // 工具方法
  canSave: () => boolean;
  canCalculate: () => boolean;
  hasUnsavedChanges: () => boolean;
}

// ==================== API 接口类型 ====================

export interface RatioCoreApiRequest {
  taskId: string;
  calculationParams: RatioCoreCalculationParams;
  materials: RatioCoreMaterial[];
  calculationMethod: CalculationMethod;
}

export interface RatioCoreApiResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  timestamp: string;
  errors?: RatioCoreWarning[];
}

// ==================== 配置类型 ====================

export interface RatioCoreConfig {
  version: 'v1' | 'v2';
  apiEndpoint: string;
  useMockData: boolean;
  autoSave: boolean;
  autoSaveDelay: number;
  cacheEnabled: boolean;
  cacheTTL: number;
}

// ==================== 事件类型 ====================

export interface RatioCoreEvents {
  onRatioLoaded: (ratio: RatioCoreData) => void;
  onRatioSaved: (ratio: RatioCoreData) => void;
  onCalculationCompleted: (results: RatioCoreCalculationResults) => void;
  onError: (error: string) => void;
  onMaterialAdded: (material: RatioCoreMaterial) => void;
  onMaterialRemoved: (materialId: string) => void;
  onMaterialUpdated: (materialId: string, material: RatioCoreMaterial) => void;
}
