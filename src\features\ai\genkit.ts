import { z } from 'zod';

// 延迟初始化 AI 实例，避免在客户端构建时加载
let ai: any = null;

export async function getAI() {
  if (!ai) {
    try {
      // 动态导入 Genkit AI，只在需要时加载
      // 使用更安全的导入方式，避免 Turbopack 问题
      const [genkitModule, googleAIModule] = await Promise.all([
        import('genkit').catch(() => null),
        import('@genkit-ai/googleai').catch(() => null),
      ]);

      if (!genkitModule || !googleAIModule) {
        throw new Error('Failed to load Genkit AI modules');
      }

      const { genkit } = genkitModule;
      const { googleAI } = googleAIModule;

      ai = genkit({
        plugins: [googleAI()],
        model: 'googleai/gemini-2.0-flash',
      });
    } catch (error) {
      console.error('Failed to initialize Genkit AI:', error);
      // 返回一个模拟的 AI 实例，避免应用崩溃
      ai = {
        generate: async () => ({ text: 'AI service temporarily unavailable' }),
        defineFlow: () => async () => ({
          success: false,
          message: 'AI service temporarily unavailable',
        }),
      };
    }
  }
  return ai;
}

// 配比生成请求参数的验证模式
export const RatioGenerationRequestSchema = z.object({
  targetStrength: z.string().describe('目标强度等级，如C25、C30、C35等'),
  environment: z.string().describe('使用环境，如normal、marine、freeze等'),
  costLevel: z.enum(['high', 'medium', 'low']).describe('成本要求级别'),
  specialRequirements: z.array(z.string()).describe('特殊要求，如早强、抗渗、抗冻等'),
  selectedMaterials: z.array(z.string()).describe('可用材料ID列表'),
  additionalParams: z.object({
    slump: z.number().describe('坍落度要求(mm)'),
    durability: z.string().describe('耐久性要求'),
    workability: z.string().describe('工作性要求'),
    temperature: z.number().describe('施工环境温度(°C)'),
  }),
  availableMaterials: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        type: z.string(),
        specification: z.string(),
        available: z.boolean(),
        amount: z.number(),
      })
    )
    .describe('可用材料清单'),
});

// 配比生成结果的验证模式
export const RatioGenerationResultSchema = z.object({
  calculationParams: z.object({
    targetStrength: z.number().describe('目标强度(MPa)'),
    slump: z.number().describe('坍落度(mm)'),
    maxAggregateSize: z.number().describe('最大骨料粒径(mm)'),
    exposureClass: z.string().describe('暴露等级'),
    ambientTemperature: z.number().describe('环境温度(°C)'),
    relativeHumidity: z.number().describe('相对湿度(%)'),
    cementTemperature: z.number().describe('水泥温度(°C)'),
    aggregateTemperature: z.number().describe('骨料温度(°C)'),
    waterCementRatio: z.number().describe('水胶比'),
    sandRatio: z.number().describe('砂率(%)'),
    cementContent: z.number().describe('水泥用量(kg/m³)'),
    waterContent: z.number().describe('用水量(kg/m³)'),
    additiveRatio: z.number().describe('外加剂掺量(%)'),
    flyashRatio: z.number().describe('粉煤灰掺量(%)'),
    mineralPowderRatio: z.number().describe('矿粉掺量(%)'),
    silicaFumeRatio: z.number().describe('硅灰掺量(%)'),
    density: z.number().describe('密度(kg/m³)'),
    airContent: z.number().describe('含气量(%)'),
  }),
  materials: z.array(
    z.object({
      id: z.string(),
      materialId: z.string(),
      name: z.string(),
      specification: z.string(),
      category: z.string(),
      unit: z.string(),
      density: z.number(),
      theoreticalAmount: z.number(),
      actualAmount: z.number(),
      waterContent: z.number(),
      stoneContent: z.number(),
      designValue: z.number(),
      siloId: z.string(),
      siloName: z.string(),
      cost: z.number(),
      supplier: z.string(),
    })
  ),
  calculationResults: z.object({
    totalWeight: z.number().describe('总重量(kg/m³)'),
    materials: z.record(z.string(), z.number()).describe('各材料用量'),
    strengthPrediction: z.number().describe('强度预测值(MPa)'),
    qualityScore: z.number().describe('质量评分(0-100)'),
    warnings: z.array(z.string()).describe('警告信息'),
    suggestions: z.array(z.string()).describe('建议信息'),
    carbonFootprint: z.number().describe('碳足迹(kg CO2/m³)'),
    costEstimate: z.number().describe('成本估算(元/m³)'),
  }),
  ratioName: z.string().describe('配比方案名称'),
  description: z.string().describe('配比方案描述'),
});

export type RatioGenerationRequest = z.infer<typeof RatioGenerationRequestSchema>;
export type RatioGenerationResult = z.infer<typeof RatioGenerationResultSchema>;

/**
 * AI配比生成功能
 * 基于用户需求和可用材料，生成最优的混凝土配比方案
 */
async function createGenerateRatioFlow() {
  const aiInstance = await getAI();
  return aiInstance.defineFlow(
    {
      name: 'generateRatio',
      inputSchema: RatioGenerationRequestSchema,
      outputSchema: RatioGenerationResultSchema,
    },
    async (request: RatioGenerationRequest) => {
      // 构建专业的配比设计提示词
      const prompt = buildRatioGenerationPrompt(request);

      // 调用AI模型生成配比
      const response = await aiInstance.generate({
        prompt,
        config: {
          temperature: 0.3, // 较低的温度确保结果的一致性和准确性
          maxOutputTokens: 2048,
        },
      });

      // 解析和验证AI生成的结果
      const result = parseAndValidateRatioResult(response.text, request);

      return result;
    }
  );
}

/**
 * 构建配比生成的专业提示词
 */
function buildRatioGenerationPrompt(request: RatioGenerationRequest): string {
  const {
    targetStrength,
    environment,
    costLevel,
    specialRequirements,
    additionalParams,
    availableMaterials,
  } = request;

  return `你是一位资深的混凝土配比设计专家，拥有20年的工程经验。请根据以下要求设计一个最优的混凝土配比方案：

## 设计要求
- **目标强度等级**: ${targetStrength}
- **使用环境**: ${environment}
- **成本要求**: ${costLevel === 'high' ? '高端配比，追求最佳性能' : costLevel === 'medium' ? '经济实用，性价比优先' : '低成本配比，满足基本要求'}
- **坍落度要求**: ${additionalParams.slump}mm
- **施工环境温度**: ${additionalParams.temperature}°C
- **耐久性要求**: ${additionalParams.durability}
- **工作性要求**: ${additionalParams.workability}
- **特殊要求**: ${specialRequirements.length > 0 ? specialRequirements.join('、') : '无'}

## 可用材料清单
${availableMaterials
  .map(
    material =>
      `- ${material.name} (${material.specification}) - ${material.available ? '可用' : '不可用'} - 库存: ${material.amount}吨`
  )
  .join('\n')}

## 设计原则
1. 严格遵循《混凝土结构设计规范》GB50010和《普通混凝土配合比设计规程》JGJ55
2. 确保强度满足要求，同时考虑耐久性和工作性
3. 优化材料用量，控制成本
4. 考虑施工环境和特殊要求
5. 确保材料相容性和质量稳定性

## 输出要求
请以JSON格式输出完整的配比设计方案，包括：

1. **计算参数** (calculationParams): 包含所有关键的配比设计参数
2. **材料清单** (materials): 详细的材料用量和规格
3. **计算结果** (calculationResults): 性能预测和质量评估
4. **方案信息**: 配比名称和详细描述

## 专业要求
- **水胶比控制**: 根据强度等级和耐久性要求确定，一般在0.25-0.65之间
- **砂率优化**: 应在30-45%之间，根据骨料级配和工作性要求优化
- **外加剂使用**:
  * 聚羧酸减水剂：掺量0.8-2.0%，减水率≥25%
  * 萘系减水剂：掺量0.5-1.2%，减水率≥18%
  * 引气剂：掺量0.01-0.05%，含气量3-6%
  * 缓凝剂：掺量0.02-0.2%，延缓初凝时间2-8小时
  * 早强剂：掺量1-4%，提高早期强度20-50%
- **掺合料应用**:
  * 粉煤灰：I级掺量15-30%，II级掺量10-20%
  * 矿粉：S95级掺量20-50%，S105级掺量30-60%
  * 硅灰：掺量5-15%，需配合减水剂使用
- **用水量控制**: 总用水量应控制在150-220kg/m³之间
- **材料相容性**: 确保胶凝材料、掺合料和外加剂之间的相容性

请确保配比设计科学合理，符合工程实际需要。`;
}

/**
 * 解析和验证AI生成的配比结果
 */
function parseAndValidateRatioResult(
  aiResponse: string,
  request: RatioGenerationRequest
): RatioGenerationResult {
  try {
    // 提取JSON内容
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('AI响应中未找到有效的JSON格式');
    }

    const parsedResult = JSON.parse(jsonMatch[0]);

    // 使用zod验证结果格式
    const validatedResult = RatioGenerationResultSchema.parse(parsedResult);

    // 进行工程合理性检查
    validateEngineeringConstraints(validatedResult, request);

    return validatedResult;
  } catch (error) {
    console.error('解析AI配比结果失败:', error);

    // 如果解析失败，返回基于规则的备用配比
    return generateFallbackRatio(request);
  }
}

/**
 * 验证工程约束条件
 */
function validateEngineeringConstraints(
  result: RatioGenerationResult,
  _request: RatioGenerationRequest
): void {
  const { calculationParams } = result;

  // 检查水胶比合理性
  if (calculationParams.waterCementRatio < 0.25 || calculationParams.waterCementRatio > 0.8) {
    console.warn(`水胶比 ${calculationParams.waterCementRatio} 超出合理范围 [0.25, 0.8]`);
  }

  // 检查砂率合理性
  if (calculationParams.sandRatio < 25 || calculationParams.sandRatio > 50) {
    console.warn(`砂率 ${calculationParams.sandRatio}% 超出合理范围 [25%, 50%]`);
  }

  // 检查水泥用量合理性
  if (calculationParams.cementContent < 200 || calculationParams.cementContent > 600) {
    console.warn(`水泥用量 ${calculationParams.cementContent}kg/m³ 超出合理范围 [200, 600]kg/m³`);
  }

  // 检查用水量合理性
  if (calculationParams.waterContent < 120 || calculationParams.waterContent > 250) {
    console.warn(`用水量 ${calculationParams.waterContent}kg/m³ 超出合理范围 [120, 250]kg/m³`);
  }

  // 检查外加剂掺量合理性
  if (calculationParams.additiveRatio < 0 || calculationParams.additiveRatio > 5) {
    console.warn(`外加剂掺量 ${calculationParams.additiveRatio}% 超出合理范围 [0%, 5%]`);
  }
}

/**
 * 生成备用配比方案（基于工程经验的规则配比）
 */
function generateFallbackRatio(request: RatioGenerationRequest): RatioGenerationResult {
  const strengthValue = parseFloat(request.targetStrength.replace(/[^\d]/g, '')) || 30;

  // 基于强度等级确定基本参数
  const waterCementRatio = Math.max(0.35, Math.min(0.65, 0.8 - strengthValue * 0.008));
  const cementContent = Math.max(300, Math.min(500, strengthValue * 12));
  const waterContent = cementContent * waterCementRatio;
  const sandRatio = request.additionalParams.slump > 150 ? 38 : 35;

  // 计算骨料用量（修正计算方法）
  const cementVolume = cementContent / 3100; // 水泥体积 m³
  const waterVolume = waterContent / 1000; // 水体积 m³
  const airVolume = 0.045; // 含气量4.5%
  const totalAggregateVolume = 1 - cementVolume - waterVolume - airVolume; // 剩余体积给骨料

  const sandVolume = (totalAggregateVolume * sandRatio) / 100;
  const gravelVolume = totalAggregateVolume - sandVolume;
  const sandAmount = sandVolume * 2650; // 砂子密度 kg/m³
  const gravelAmount = gravelVolume * 2700; // 石子密度 kg/m³

  // 外加剂用量
  const additiveAmount = cementContent * 0.015; // 1.5%掺量

  // 掺合料用量
  const flyashRatio = request.costLevel === 'low' ? 20 : 15; // 粉煤灰掺量%
  const mineralPowderRatio = request.costLevel === 'high' ? 15 : 10; // 矿粉掺量%
  const flyashAmount = (cementContent * flyashRatio) / 100;
  const mineralPowderAmount = (cementContent * mineralPowderRatio) / 100;

  const totalWeight =
    cementContent +
    waterContent +
    sandAmount +
    gravelAmount +
    additiveAmount +
    flyashAmount +
    mineralPowderAmount;

  return {
    calculationParams: {
      targetStrength: strengthValue,
      slump: request.additionalParams.slump,
      maxAggregateSize: 25,
      exposureClass: 'XC1',
      ambientTemperature: request.additionalParams.temperature,
      relativeHumidity: 60,
      cementTemperature: 20,
      aggregateTemperature: 20,
      waterCementRatio,
      sandRatio,
      cementContent,
      waterContent,
      additiveRatio: 2.0,
      flyashRatio: request.costLevel === 'low' ? 20 : 15,
      mineralPowderRatio: request.costLevel === 'high' ? 10 : 5,
      silicaFumeRatio: request.specialRequirements.includes('高强') ? 8 : 0,
      density: totalWeight,
      airContent: 4.5,
    },
    materials: [
      {
        id: 'cement_1',
        materialId: 'cement_p_o_42_5',
        name: '水泥',
        specification: 'P.O 42.5',
        category: 'CEMENTITIOUS',
        unit: 'kg',
        density: 3100,
        theoreticalAmount: cementContent,
        actualAmount: cementContent,
        waterContent: 0,
        stoneContent: 0,
        designValue: cementContent,
        siloId: 'silo_cement_1',
        siloName: '1#水泥仓',
        cost: cementContent * 0.45,
        supplier: '海螺水泥',
      },
      {
        id: 'water_1',
        materialId: 'potable_water',
        name: '水',
        specification: '饮用水',
        category: 'WATER',
        unit: 'kg',
        density: 1000,
        theoreticalAmount: waterContent,
        actualAmount: waterContent,
        waterContent: 0,
        stoneContent: 0,
        designValue: waterContent,
        siloId: 'silo_water_1',
        siloName: '水仓',
        cost: waterContent * 0.005,
        supplier: '自来水公司',
      },
      {
        id: 'sand_1',
        materialId: 'river_sand',
        name: '砂子',
        specification: '机制砂',
        category: 'AGGREGATE',
        unit: 'kg',
        density: 2650,
        theoreticalAmount: sandAmount,
        actualAmount: sandAmount * 1.025, // 考虑含水率
        waterContent: 2.5,
        stoneContent: 0,
        designValue: sandAmount,
        siloId: 'silo_sand_1',
        siloName: '砂仓1',
        cost: sandAmount * 0.08,
        supplier: '本地砂场',
      },
      {
        id: 'gravel_1',
        materialId: 'crushed_stone',
        name: '石子',
        specification: '1-2碎石',
        category: 'AGGREGATE',
        unit: 'kg',
        density: 2700,
        theoreticalAmount: gravelAmount,
        actualAmount: gravelAmount * 1.01, // 考虑含水率
        waterContent: 1.0,
        stoneContent: 0,
        designValue: gravelAmount,
        siloId: 'silo_gravel_1',
        siloName: '石仓1',
        cost: gravelAmount * 0.06,
        supplier: '本地石场',
      },
      {
        id: 'additive_1',
        materialId: 'superplasticizer_pce',
        name: '聚羧酸减水剂',
        specification: 'PCE-40%',
        category: 'ADMIXTURE',
        unit: 'kg',
        density: 1050,
        theoreticalAmount: additiveAmount,
        actualAmount: additiveAmount,
        waterContent: 0,
        stoneContent: 0,
        designValue: additiveAmount,
        siloId: 'silo_additive_1',
        siloName: '1#外加剂罐',
        cost: additiveAmount * 8.5,
        supplier: '建业化工',
      },
      {
        id: 'flyash_1',
        materialId: 'fly_ash_grade_1',
        name: '粉煤灰',
        specification: 'I级',
        category: 'SUPPLEMENTARY',
        unit: 'kg',
        density: 2200,
        theoreticalAmount: flyashAmount,
        actualAmount: flyashAmount,
        waterContent: 0,
        stoneContent: 0,
        designValue: flyashAmount,
        siloId: 'silo_flyash_1',
        siloName: '1#粉煤灰仓',
        cost: flyashAmount * 0.15,
        supplier: '华能电厂',
      },
      {
        id: 'mineral_powder_1',
        materialId: 'slag_powder_s95',
        name: '矿粉',
        specification: 'S95',
        category: 'SUPPLEMENTARY',
        unit: 'kg',
        density: 2900,
        theoreticalAmount: mineralPowderAmount,
        actualAmount: mineralPowderAmount,
        waterContent: 0,
        stoneContent: 0,
        designValue: mineralPowderAmount,
        siloId: 'silo_mineral_1',
        siloName: '1#矿粉仓',
        cost: mineralPowderAmount * 0.25,
        supplier: '首钢矿业',
      },
    ],
    calculationResults: {
      totalWeight,
      materials: {
        水泥: cementContent,
        水: waterContent,
        砂子: sandAmount,
        石子: gravelAmount,
        外加剂: additiveAmount,
        粉煤灰: flyashAmount,
        矿粉: mineralPowderAmount,
      },
      strengthPrediction: strengthValue,
      qualityScore: 85,
      warnings: ['这是基于工程经验的备用配比，建议进行试配验证'],
      suggestions: [
        '建议根据实际材料性能调整配比',
        '施工前进行坍落度和强度试验',
        '注意控制搅拌时间和养护条件',
      ],
      carbonFootprint: totalWeight * 0.3,
      costEstimate: totalWeight * 0.8,
    },
    ratioName: `${request.targetStrength}备用配比方案`,
    description: `基于工程经验生成的${request.targetStrength}强度等级混凝土配比，适用于${request.environment}环境，${request.costLevel}成本要求。`,
  };
}

/**
 * 简化的配比生成API接口
 * 供前端组件直接调用
 */
export async function generateConcreteRatio(
  request: RatioGenerationRequest
): Promise<RatioGenerationResult> {
  try {
    console.log('开始AI配比生成:', request);

    // 创建并调用genkit流程
    const generateRatio = await createGenerateRatioFlow();
    const result = await generateRatio(request);

    console.log('AI配比生成完成:', result.ratioName);
    return result;
  } catch (error) {
    console.error('AI配比生成失败:', error);

    // 出错时返回备用配比
    console.log('使用备用配比方案');
    return generateFallbackRatio(request);
  }
}
