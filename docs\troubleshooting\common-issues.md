# 🔍 常见问题解决方案

## 🚀 启动和运行问题

### 问题1: 端口被占用
**错误信息**: `Error: listen EADDRINUSE: address already in use :::3000`

**解决方案**:
```bash
# 查找占用端口的进程
# macOS/Linux
lsof -ti:3000 | xargs kill -9

# Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# 或者使用不同端口
npm run dev -- -p 3001
```

### 问题2: 依赖安装失败
**错误信息**: `npm ERR! peer dep missing`

**解决方案**:
```bash
# 清理缓存和重新安装
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# 如果仍有问题，尝试使用 --legacy-peer-deps
npm install --legacy-peer-deps
```

### 问题3: TypeScript 编译错误
**错误信息**: `Type 'string' is not assignable to type 'number'`

**解决方案**:
```bash
# 重新生成类型定义
npm run typecheck

# 重启 TypeScript 服务 (VS Code)
# Ctrl+Shift+P -> "TypeScript: Restart TS Server"

# 检查 tsconfig.json 配置
cat tsconfig.json
```

## 🎨 样式和UI问题

### 问题4: Tailwind CSS 样式不生效
**症状**: 样式类名不起作用

**解决方案**:
```bash
# 检查 Tailwind 配置
cat tailwind.config.js

# 重新构建样式
npm run build:css

# 确保类名在 content 配置中
# tailwind.config.js
module.exports = {
  content: [
    './src/**/*.{js,ts,jsx,tsx}',
    './pages/**/*.{js,ts,jsx,tsx}',
  ],
}
```

### 问题5: 组件样式冲突
**症状**: 组件样式被覆盖或不一致

**解决方案**:
```typescript
// 使用 cn() 函数合并样式
import { cn } from '@/core/lib/utils';

<div className={cn(
  'base-styles',
  'conditional-styles',
  props.className
)}>
```

### 问题6: 深色模式切换问题
**症状**: 主题切换不生效

**解决方案**:
```typescript
// 检查主题提供者配置
// app/layout.tsx
import { ThemeProvider } from '@/components/theme-provider';

export default function RootLayout({ children }) {
  return (
    <html lang="zh" suppressHydrationWarning>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
```

## 🔄 状态管理问题

### 问题7: Zustand 状态不更新
**症状**: 组件不响应状态变化

**解决方案**:
```typescript
// 确保使用 immer 中间件
import { immer } from 'zustand/middleware/immer';

export const useStore = create<State>()(
  immer((set) => ({
    // 状态定义
    updateState: (newData) => set((state) => {
      // 使用 immer 语法
      state.data = newData;
    }),
  }))
);

// 在组件中正确使用
const { data, updateState } = useStore();
```

### 问题8: 状态持久化失败
**症状**: 页面刷新后状态丢失

**解决方案**:
```typescript
// 添加持久化中间件
import { persist } from 'zustand/middleware';

export const useStore = create<State>()(
  persist(
    immer((set) => ({
      // 状态定义
    })),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
```

## 🔌 API和网络问题

### 问题9: API请求失败
**错误信息**: `Network Error` 或 `CORS Error`

**解决方案**:
```typescript
// 检查 API 配置
// next.config.js
module.exports = {
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:3001/api/:path*',
      },
    ];
  },
};

// 或配置代理
// package.json
{
  "proxy": "http://localhost:3001"
}
```

### 问题10: 环境变量不生效
**症状**: `process.env.VARIABLE` 返回 undefined

**解决方案**:
```bash
# 检查环境变量文件
cat .env.local

# 确保变量名以 NEXT_PUBLIC_ 开头（客户端使用）
NEXT_PUBLIC_API_URL=http://localhost:3001

# 重启开发服务器
npm run dev
```

## 🧪 测试问题

### 问题11: Jest 测试失败
**错误信息**: `Cannot find module` 或 `Unexpected token`

**解决方案**:
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }],
  },
};
```

### 问题12: 组件测试渲染错误
**错误信息**: `useRouter` 或 `useSearchParams` 错误

**解决方案**:
```typescript
// 测试文件中模拟 Next.js hooks
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
}));
```

## 🚀 性能问题

### 问题13: 页面加载缓慢
**症状**: 首屏加载时间过长

**解决方案**:
```typescript
// 使用动态导入
import dynamic from 'next/dynamic';

const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <div>Loading...</div>,
  ssr: false,
});

// 优化图片
import Image from 'next/image';

<Image
  src="/image.jpg"
  alt="Description"
  width={500}
  height={300}
  priority // 关键图片
/>
```

### 问题14: 内存泄漏
**症状**: 页面使用时间长后变慢

**解决方案**:
```typescript
// 清理副作用
useEffect(() => {
  const timer = setInterval(() => {
    // 定时器逻辑
  }, 1000);

  return () => {
    clearInterval(timer); // 清理定时器
  };
}, []);

// 取消网络请求
useEffect(() => {
  const controller = new AbortController();
  
  fetch('/api/data', {
    signal: controller.signal,
  });

  return () => {
    controller.abort(); // 取消请求
  };
}, []);
```

## 🔧 构建和部署问题

### 问题15: 构建失败
**错误信息**: `Build failed` 或 `Out of memory`

**解决方案**:
```bash
# 增加内存限制
NODE_OPTIONS="--max-old-space-size=4096" npm run build

# 检查构建配置
# next.config.js
module.exports = {
  experimental: {
    // 启用 SWC 编译器
    swcMinify: true,
  },
  // 优化打包
  webpack: (config) => {
    config.optimization.splitChunks = {
      chunks: 'all',
    };
    return config;
  },
};
```

### 问题16: 部署后页面空白
**症状**: 生产环境页面不显示内容

**解决方案**:
```bash
# 检查控制台错误
# 常见原因：
# 1. 环境变量配置错误
# 2. 静态资源路径问题
# 3. CSP 策略过严

# 检查 next.config.js
module.exports = {
  output: 'standalone', // 如果使用 Docker
  trailingSlash: true,  // 如果需要
  assetPrefix: process.env.NODE_ENV === 'production' ? '/app' : '',
};
```

## 🔍 调试技巧

### 开发工具使用
```typescript
// 1. 使用 React DevTools
// 安装浏览器扩展：React Developer Tools

// 2. 使用 Redux DevTools (Zustand)
import { devtools } from 'zustand/middleware';

export const useStore = create<State>()(
  devtools(
    immer((set) => ({
      // 状态定义
    })),
    { name: 'app-store' }
  )
);

// 3. 性能分析
import { Profiler } from 'react';

function onRenderCallback(id, phase, actualDuration) {
  console.log('Render:', { id, phase, actualDuration });
}

<Profiler id="TaskList" onRender={onRenderCallback}>
  <TaskList />
</Profiler>
```

### 日志调试
```typescript
// 条件日志
const DEBUG = process.env.NODE_ENV === 'development';

function debugLog(message: string, data?: any) {
  if (DEBUG) {
    console.log(`[DEBUG] ${message}`, data);
  }
}

// 性能测量
console.time('operation');
// 执行操作
console.timeEnd('operation');
```

## 📞 获取帮助

### 内部资源
- **技术文档**: 查看项目 `docs/` 目录
- **代码示例**: 参考 `src/examples/` 目录
- **测试用例**: 查看 `__tests__/` 目录

### 外部资源
- **Next.js 文档**: https://nextjs.org/docs
- **React 文档**: https://react.dev
- **Tailwind CSS**: https://tailwindcss.com/docs
- **Zustand**: https://zustand-demo.pmnd.rs

### 社区支持
- **GitHub Issues**: 提交 Bug 报告
- **Stack Overflow**: 搜索相关问题
- **Discord/Slack**: 团队内部讨论

## 🚨 紧急问题处理

### 生产环境问题
1. **立即回滚**: 如果是部署导致的问题
2. **检查监控**: 查看错误日志和性能指标
3. **通知团队**: 及时沟通问题状态
4. **记录问题**: 详细记录问题和解决过程

### 问题报告模板
```markdown
## 问题描述
简要描述遇到的问题

## 复现步骤
1. 步骤一
2. 步骤二
3. 步骤三

## 预期行为
描述期望的正确行为

## 实际行为
描述实际发生的情况

## 环境信息
- 操作系统：
- 浏览器：
- Node.js 版本：
- 项目版本：

## 错误信息
```
粘贴完整的错误信息
```

## 附加信息
其他可能有用的信息
```

---

**文档版本**: v1.0  
**最后更新**: 2025/7/16  
**维护者**: TMH技术支持团队
