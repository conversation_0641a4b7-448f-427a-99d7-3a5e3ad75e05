/**
 * 高性能缓存管理器
 * 实现多层缓存策略以提升应用性能
 */

// ==================== 缓存配置 ====================

export interface CacheConfig {
  maxSize: number;
  ttl: number; // Time to live in milliseconds
  enableCompression: boolean;
  enablePersistence: boolean;
  storageKey: string;
}

export interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  size: number;
  accessCount: number;
  lastAccessed: number;
}

// ==================== 内存缓存 ====================

class MemoryCache {
  private cache = new Map<string, CacheItem>();
  private maxSize: number;
  private currentSize = 0;

  constructor(maxSize = 50 * 1024 * 1024) {
    // 50MB default
    this.maxSize = maxSize;
  }

  set<T>(key: string, data: T, ttl = 5 * 60 * 1000): void {
    const size = this.calculateSize(data);

    // 如果数据太大，直接拒绝
    if (size > this.maxSize * 0.5) {
      console.warn(`Cache item too large: ${key} (${size} bytes)`);
      return;
    }

    // 清理过期项
    this.cleanup();

    // 如果空间不足，清理最少使用的项
    while (this.currentSize + size > this.maxSize && this.cache.size > 0) {
      this.evictLRU();
    }

    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      size,
      accessCount: 0,
      lastAccessed: Date.now(),
    };

    // 如果key已存在，先减去旧的大小
    if (this.cache.has(key)) {
      this.currentSize -= this.cache.get(key)!.size;
    }

    this.cache.set(key, item);
    this.currentSize += size;
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return null;
    }

    // 更新访问统计
    item.accessCount++;
    item.lastAccessed = Date.now();

    return item.data as T;
  }

  delete(key: string): boolean {
    const item = this.cache.get(key);
    if (item) {
      this.currentSize -= item.size;
      return this.cache.delete(key);
    }
    return false;
  }

  clear(): void {
    this.cache.clear();
    this.currentSize = 0;
  }

  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;

    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return false;
    }

    return true;
  }

  private calculateSize(data: any): number {
    try {
      return new Blob([JSON.stringify(data)]).size;
    } catch {
      return JSON.stringify(data).length * 2; // 估算
    }
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.delete(key);
      }
    }
  }

  private evictLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (item.lastAccessed < oldestTime) {
        oldestTime = item.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.delete(oldestKey);
    }
  }

  getStats() {
    return {
      size: this.cache.size,
      currentSize: this.currentSize,
      maxSize: this.maxSize,
      utilization: (this.currentSize / this.maxSize) * 100,
    };
  }
}

// ==================== 持久化缓存 ====================

class PersistentCache {
  private storageKey: string;

  constructor(storageKey = 'app-cache') {
    this.storageKey = storageKey;
  }

  set<T>(key: string, data: T, ttl = 24 * 60 * 60 * 1000): void {
    try {
      const item: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        ttl,
        size: 0,
        accessCount: 0,
        lastAccessed: Date.now(),
      };

      const cache = this.getCache();
      cache[key] = item;

      localStorage.setItem(this.storageKey, JSON.stringify(cache));
    } catch (error) {
      console.warn('Failed to set persistent cache:', error);
    }
  }

  get<T>(key: string): T | null {
    try {
      const cache = this.getCache();
      const item = cache[key];

      if (!item) return null;

      if (Date.now() - item.timestamp > item.ttl) {
        this.delete(key);
        return null;
      }

      return item.data as T;
    } catch (error) {
      console.warn('Failed to get persistent cache:', error);
      return null;
    }
  }

  delete(key: string): void {
    try {
      const cache = this.getCache();
      delete cache[key];
      localStorage.setItem(this.storageKey, JSON.stringify(cache));
    } catch (error) {
      console.warn('Failed to delete persistent cache:', error);
    }
  }

  clear(): void {
    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.warn('Failed to clear persistent cache:', error);
    }
  }

  private getCache(): Record<string, CacheItem> {
    try {
      const cached = localStorage.getItem(this.storageKey);
      return cached ? JSON.parse(cached) : {};
    } catch {
      return {};
    }
  }
}

// ==================== 统一缓存管理器 ====================

export class PerformanceCache {
  private memoryCache: MemoryCache;
  private persistentCache: PersistentCache;
  private config: CacheConfig;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 50 * 1024 * 1024, // 50MB
      ttl: 5 * 60 * 1000, // 5 minutes
      enableCompression: false,
      enablePersistence: true,
      storageKey: 'app-performance-cache',
      ...config,
    };

    this.memoryCache = new MemoryCache(this.config.maxSize);
    this.persistentCache = new PersistentCache(this.config.storageKey);
  }

  /**
   * 设置缓存项
   */
  set<T>(key: string, data: T, options: { ttl?: number; persistent?: boolean } = {}): void {
    const { ttl = this.config.ttl, persistent = this.config.enablePersistence } = options;

    // 总是设置内存缓存
    this.memoryCache.set(key, data, ttl);

    // 根据配置设置持久化缓存
    if (persistent) {
      this.persistentCache.set(key, data, ttl);
    }
  }

  /**
   * 获取缓存项
   */
  get<T>(key: string): T | null {
    // 先尝试内存缓存
    let data = this.memoryCache.get<T>(key);
    if (data !== null) {
      return data;
    }

    // 再尝试持久化缓存
    if (this.config.enablePersistence) {
      data = this.persistentCache.get<T>(key);
      if (data !== null) {
        // 将数据重新加载到内存缓存
        this.memoryCache.set(key, data, this.config.ttl);
        return data;
      }
    }

    return null;
  }

  /**
   * 删除缓存项
   */
  delete(key: string): void {
    this.memoryCache.delete(key);
    if (this.config.enablePersistence) {
      this.persistentCache.delete(key);
    }
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.memoryCache.clear();
    if (this.config.enablePersistence) {
      this.persistentCache.clear();
    }
  }

  /**
   * 检查缓存是否存在
   */
  has(key: string): boolean {
    return (
      this.memoryCache.has(key) ||
      (this.config.enablePersistence && this.persistentCache.get(key) !== null)
    );
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      memory: this.memoryCache.getStats(),
      config: this.config,
    };
  }
}

// ==================== 全局缓存实例 ====================

export const globalCache = new PerformanceCache({
  maxSize: 100 * 1024 * 1024, // 100MB
  ttl: 10 * 60 * 1000, // 10 minutes
  enablePersistence: true,
  storageKey: 'tmh-app-cache',
});

// ==================== 缓存装饰器 ====================

export function cached<T extends (...args: any[]) => any>(
  ttl = 5 * 60 * 1000,
  keyGenerator?: (...args: Parameters<T>) => string
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: Parameters<T>) {
      const cacheKey = keyGenerator
        ? keyGenerator(...args)
        : `${target.constructor.name}.${propertyKey}.${JSON.stringify(args)}`;

      // 尝试从缓存获取
      const cached = globalCache.get(cacheKey);
      if (cached !== null) {
        return cached;
      }

      // 执行原方法
      const result = originalMethod.apply(this, args);

      // 缓存结果
      if (result instanceof Promise) {
        return result.then(data => {
          globalCache.set(cacheKey, data, { ttl });
          return data;
        });
      } else {
        globalCache.set(cacheKey, result, { ttl });
        return result;
      }
    };

    return descriptor;
  };
}
