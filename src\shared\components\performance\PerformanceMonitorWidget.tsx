'use client';

/**
 * 性能监控小部件
 * 实时显示应用性能指标
 */

import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { useUnifiedPerformanceMonitor } from '@/infrastructure/monitoring/unified-performance-monitor';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import { Progress } from '@/shared/components/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/tabs';
import {
  Activity,
  Zap,
  Clock,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  MemoryStick,
  Cpu,
  Monitor,
  RefreshCw,
  Database,
  Target,
  Trash2,
} from 'lucide-react';
import { computationCache } from '@/infrastructure/storage/cache/computation-cache';
import { globalCache } from '@/infrastructure/storage/cache/performance-cache';

interface PerformanceMonitorWidgetProps {
  className?: string;
  compact?: boolean;
}

interface CacheMetrics {
  totalKeys: number;
  overallHitRate: number;
  stats: Array<{
    key: string;
    hitRate: number;
    totalRequests: number;
    avgComputeTime: number;
    hits: number;
    misses: number;
  }>;
}

interface CacheStats {
  memory: {
    size: number;
    currentSize: number;
    maxSize: number;
    utilization: number;
  };
}

const PerformanceMonitorWidget: React.FC<PerformanceMonitorWidgetProps> = ({
  className = '',
  compact = false,
}) => {
  const { generateReport, detectIssues, getMetrics } = useUnifiedPerformanceMonitor({
    componentName: 'PerformanceMonitorWidget',
  });
  const [isVisible, setIsVisible] = useState(false);
  const [report, setReport] = useState<any>(null);
  const [analysis, setAnalysis] = useState<any>(null);
  const [updateInterval, setUpdateInterval] = useState<NodeJS.Timeout | null>(null);
  const [frameRate, setFrameRate] = useState<number>(0);
  const [memoryInfo, setMemoryInfo] = useState<any>(null);
  const [componentPerformance, setComponentPerformance] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // 缓存相关状态
  const [cacheMetrics, setCacheMetrics] = useState<CacheMetrics | null>(null);
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);

  // 更新性能数据
  const updatePerformanceData = () => {
    try {
      const newReport = generateReport();
      const issues = detectIssues();
      const metrics = getMetrics();

      // 更新帧率 - 使用模拟数据
      const currentFrameRate = Math.floor(Math.random() * 20) + 50; // 50-70 FPS
      setFrameRate(currentFrameRate);

      // 更新内存信息
      if ('memory' in performance) {
        const memInfo = (performance as any).memory;
        setMemoryInfo({
          used: Math.round(memInfo.usedJSHeapSize / 1024 / 1024),
          total: Math.round(memInfo.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(memInfo.jsHeapSizeLimit / 1024 / 1024),
        });
      }

      // 更新组件性能数据 - 使用渲染指标
      setComponentPerformance({
        rendering: metrics.rendering || {},
        memory: metrics.memory || {},
        network: metrics.network || {},
      });

      // 更新缓存指标
      try {
        const computationMetrics = computationCache.getMetrics();
        const globalStats = globalCache.getStats();
        setCacheMetrics(computationMetrics);
        setCacheStats(globalStats);
      } catch (error) {
        console.error('Failed to fetch cache metrics:', error);
      }

      // 构建兼容的报告数据结构
      const compatibleReport = {
        ...newReport,
        pageLoad: {
          lcp: metrics.webVitals?.lcp || 0,
          fid: metrics.webVitals?.fid || 0,
          cls: metrics.webVitals?.cls || 0,
          fcp: metrics.webVitals?.fcp || 0,
        },
        resources: {
          cacheHitRate: metrics.network?.cacheHitRate || 0,
          totalSize:
            (metrics.resources?.jsSize || 0) +
            (metrics.resources?.cssSize || 0) +
            (metrics.resources?.imageSize || 0),
          loadTime: metrics.resources?.loadTime || 0,
        },
        components: {
          memoryUsage: metrics.memory?.used || 0,
          renderTime: metrics.rendering?.renderTime || 0,
        },
      };

      setReport(compatibleReport);

      // 构建兼容的分析数据结构
      const compatibleAnalysis = {
        issues,
        metrics,
        score: newReport.score,
        grade: newReport.grade,
        recommendations: newReport.recommendations,
        overall:
          newReport.grade === 'A'
            ? 'excellent'
            : newReport.grade === 'B'
              ? 'good'
              : newReport.grade === 'C'
                ? 'needs-improvement'
                : 'poor',
        scores: {
          pageLoad: Math.round(newReport.score * 0.4), // 40% 权重
          resources: Math.round(newReport.score * 0.3), // 30% 权重
          interactions: Math.round(newReport.score * 0.2), // 20% 权重
          lazyLoading: Math.round(newReport.score * 0.1), // 10% 权重
        },
      };

      setAnalysis(compatibleAnalysis);
    } catch (error) {
      console.error('Failed to update performance data:', error);
      // 设置默认值以防止错误
      setReport({
        score: 0,
        grade: 'F',
        pageLoad: {
          lcp: 0,
          fid: 0,
          cls: 0,
          fcp: 0,
        },
        resources: {
          cacheHitRate: 0,
          totalSize: 0,
          loadTime: 0,
        },
        components: {
          memoryUsage: 0,
          renderTime: 0,
        },
      });

      setAnalysis({
        issues: [],
        metrics: {},
        score: 0,
        grade: 'F',
        recommendations: [],
        overall: 'poor',
        scores: {
          pageLoad: 0,
          resources: 0,
          interactions: 0,
          lazyLoading: 0,
        },
      });
    }
  };

  // 切换显示状态
  const toggleVisibility = () => {
    setIsVisible(!isVisible);
    if (!isVisible) {
      updatePerformanceData();
      // 每5秒更新一次数据
      const interval = setInterval(updatePerformanceData, 5000);
      setUpdateInterval(interval);
    } else {
      if (updateInterval) {
        clearInterval(updateInterval);
        setUpdateInterval(null);
      }
    }
  };

  // 清理缓存
  const clearCache = () => {
    try {
      globalCache.clear();
      updatePerformanceData(); // 刷新数据
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (updateInterval) {
        clearInterval(updateInterval);
      }
    };
  }, [updateInterval]);

  // 获取性能等级颜色
  const getPerformanceColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-50';
    if (score >= 75) return 'text-blue-600 bg-blue-50';
    if (score >= 50) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  // 获取性能等级图标
  const getPerformanceIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className='w-4 h-4' />;
    if (score >= 75) return <TrendingUp className='w-4 h-4' />;
    if (score >= 50) return <AlertTriangle className='w-4 h-4' />;
    return <XCircle className='w-4 h-4' />;
  };

  // 格式化数值
  const formatValue = (value: number, unit: string = 'ms') => {
    if (unit === 'ms') {
      return `${value.toFixed(1)}${unit}`;
    }
    if (unit === 'MB') {
      return `${(value / 1024 / 1024).toFixed(1)}${unit}`;
    }
    if (unit === '%') {
      return `${(value * 100).toFixed(1)}${unit}`;
    }
    return `${value.toFixed(1)}${unit}`;
  };

  if (compact) {
    return (
      <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
        <Button
          onClick={toggleVisibility}
          variant='outline'
          size='sm'
          className='bg-white shadow-lg hover:shadow-xl transition-shadow'
        >
          <Activity className='w-4 h-4 mr-1' />
          性能
          {analysis && (
            <Badge
              variant='secondary'
              className={`ml-2 ${getPerformanceColor((analysis.scores.pageLoad + analysis.scores.resources + analysis.scores.interactions + analysis.scores.lazyLoading) / 4)}`}
            >
              {analysis.overall === 'excellent'
                ? '优秀'
                : analysis.overall === 'good'
                  ? '良好'
                  : analysis.overall === 'needs-improvement'
                    ? '待优化'
                    : '较差'}
            </Badge>
          )}
        </Button>

        {isVisible && (
          <Card className='absolute bottom-12 right-0 w-96 shadow-xl max-h-[80vh] overflow-hidden'>
            <CardHeader className='pb-2'>
              <CardTitle className='text-sm flex items-center justify-between'>
                <span className='flex items-center'>
                  <Monitor className='w-4 h-4 mr-2' />
                  性能监控中心
                </span>
                <div className='flex items-center gap-2'>
                  <Button
                    onClick={updatePerformanceData}
                    variant='ghost'
                    size='sm'
                    className='h-6 w-6 p-0'
                    title='刷新数据'
                  >
                    <RefreshCw className='w-3 h-3' />
                  </Button>
                  <Button
                    onClick={toggleVisibility}
                    variant='ghost'
                    size='sm'
                    className='h-6 w-6 p-0'
                  >
                    ×
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className='p-3 overflow-y-auto max-h-[60vh]'>
              <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
                <TabsList className='grid w-full grid-cols-4 mb-3'>
                  <TabsTrigger value='overview' className='text-xs'>
                    概览
                  </TabsTrigger>
                  <TabsTrigger value='details' className='text-xs'>
                    详情
                  </TabsTrigger>
                  <TabsTrigger value='system' className='text-xs'>
                    系统
                  </TabsTrigger>
                  <TabsTrigger value='cache' className='text-xs'>
                    缓存
                  </TabsTrigger>
                </TabsList>

                <TabsContent value='overview' className='space-y-3 mt-0'>
                  {report && analysis && (
                    <>
                      {/* 总体评分 */}
                      <div className='flex items-center justify-between p-2 bg-gray-50 rounded'>
                        <span className='text-sm font-medium'>总体评分</span>
                        <div className='flex items-center space-x-2'>
                          {getPerformanceIcon(
                            (analysis.scores.pageLoad +
                              analysis.scores.resources +
                              analysis.scores.interactions +
                              analysis.scores.lazyLoading) /
                              4
                          )}
                          <Badge
                            className={getPerformanceColor(
                              (analysis.scores.pageLoad +
                                analysis.scores.resources +
                                analysis.scores.interactions +
                                analysis.scores.lazyLoading) /
                                4
                            )}
                          >
                            {Math.round(
                              (analysis.scores.pageLoad +
                                analysis.scores.resources +
                                analysis.scores.interactions +
                                analysis.scores.lazyLoading) /
                                4
                            )}
                          </Badge>
                        </div>
                      </div>

                      {/* 关键指标 */}
                      <div className='grid grid-cols-2 gap-2 text-xs'>
                        <div className='flex justify-between'>
                          <span className='text-gray-600'>LCP:</span>
                          <span
                            className={
                              report.pageLoad.lcp > 2500 ? 'text-red-600' : 'text-green-600'
                            }
                          >
                            {formatValue(report.pageLoad.lcp)}
                          </span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-gray-600'>FID:</span>
                          <span
                            className={
                              report.pageLoad.fid > 100 ? 'text-red-600' : 'text-green-600'
                            }
                          >
                            {formatValue(report.pageLoad.fid)}
                          </span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-gray-600'>CLS:</span>
                          <span
                            className={
                              report.pageLoad.cls > 0.1 ? 'text-red-600' : 'text-green-600'
                            }
                          >
                            {report.pageLoad.cls.toFixed(3)}
                          </span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-gray-600'>缓存率:</span>
                          <span
                            className={
                              report.resources.cacheHitRate < 0.7
                                ? 'text-red-600'
                                : 'text-green-600'
                            }
                          >
                            {formatValue(report.resources.cacheHitRate, '%')}
                          </span>
                        </div>
                      </div>

                      {/* 懒加载统计 */}
                      <div className='flex justify-between items-center p-2 bg-blue-50 rounded'>
                        <span className='text-sm text-blue-800'>懒加载成功率</span>
                        <Badge variant='secondary' className='bg-blue-100 text-blue-800'>
                          {analysis.scores.lazyLoading}%
                        </Badge>
                      </div>

                      {/* 内存使用 */}
                      <div className='flex justify-between items-center p-2 bg-purple-50 rounded'>
                        <span className='text-sm text-purple-800'>内存使用</span>
                        <span className='text-sm text-purple-600'>
                          {formatValue(report.components.memoryUsage, 'MB')}
                        </span>
                      </div>

                      {/* 建议 */}
                      {analysis.recommendations.length > 0 && (
                        <div className='mt-3 p-2 bg-yellow-50 rounded'>
                          <div className='text-xs font-medium text-yellow-800 mb-1'>优化建议:</div>
                          <div className='text-xs text-yellow-700'>
                            {analysis.recommendations[0]}
                          </div>
                          {analysis.recommendations.length > 1 && (
                            <div className='text-xs text-yellow-600 mt-1'>
                              +{analysis.recommendations.length - 1} 条建议
                            </div>
                          )}
                        </div>
                      )}
                    </>
                  )}
                </TabsContent>

                <TabsContent value='details' className='space-y-3 mt-0'>
                  {/* 帧率监控 */}
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm font-medium flex items-center gap-2'>
                        <Zap className='w-3 h-3' />
                        帧率
                      </span>
                      <Badge
                        variant={
                          frameRate >= 55
                            ? 'default'
                            : frameRate >= 30
                              ? 'secondary'
                              : 'destructive'
                        }
                      >
                        {frameRate} FPS
                      </Badge>
                    </div>
                    <Progress value={Math.min(frameRate, 60)} max={60} className='h-2' />
                  </div>

                  {/* 内存使用详情 */}
                  {memoryInfo && (
                    <div className='space-y-2'>
                      <div className='flex items-center justify-between'>
                        <span className='text-sm font-medium flex items-center gap-2'>
                          <MemoryStick className='w-3 h-3' />
                          内存使用
                        </span>
                        <Badge variant='secondary'>
                          {memoryInfo.used}MB / {memoryInfo.total}MB
                        </Badge>
                      </div>
                      <Progress
                        value={(memoryInfo.used / memoryInfo.total) * 100}
                        max={100}
                        className='h-2'
                      />
                      <div className='text-xs text-muted-foreground'>
                        限制: {memoryInfo.limit}MB
                      </div>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value='system' className='space-y-3 mt-0'>
                  {/* 组件性能 */}
                  {componentPerformance && (
                    <div className='space-y-3'>
                      <div className='text-sm font-medium flex items-center gap-2'>
                        <Cpu className='w-3 h-3' />
                        组件性能
                      </div>

                      {componentPerformance.rendering && (
                        <div className='p-2 bg-blue-50 rounded'>
                          <div className='text-xs font-medium text-blue-800 mb-1'>渲染性能</div>
                          <div className='text-xs text-blue-600'>
                            渲染时间: {componentPerformance.rendering.renderTime?.toFixed(1) || 0}ms
                          </div>
                          <div className='text-xs text-blue-600'>
                            组件数量: {componentPerformance.rendering.componentCount || 0}
                          </div>
                        </div>
                      )}

                      {componentPerformance.network && (
                        <div className='p-2 bg-green-50 rounded'>
                          <div className='text-xs font-medium text-green-800 mb-1'>网络性能</div>
                          <div className='text-xs text-green-600'>
                            缓存命中率:{' '}
                            {((componentPerformance.network.cacheHitRate || 0) * 100).toFixed(1)}%
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value='cache' className='space-y-3 mt-0'>
                  {cacheMetrics && cacheStats ? (
                    <div className='space-y-3'>
                      {/* 缓存概览 */}
                      <div className='text-sm font-medium flex items-center gap-2'>
                        <Database className='w-3 h-3' />
                        计算缓存监控
                      </div>

                      {/* 核心指标 */}
                      <div className='grid grid-cols-2 gap-3'>
                        <div className='space-y-1'>
                          <div className='flex items-center gap-1'>
                            <Target className='h-3 w-3' />
                            <span className='text-xs text-muted-foreground'>命中率</span>
                          </div>
                          <div
                            className={`text-lg font-bold ${
                              cacheMetrics.overallHitRate >= 80
                                ? 'text-green-600'
                                : cacheMetrics.overallHitRate >= 60
                                  ? 'text-yellow-600'
                                  : 'text-red-600'
                            }`}
                          >
                            {cacheMetrics.overallHitRate.toFixed(1)}%
                          </div>
                        </div>

                        <div className='space-y-1'>
                          <div className='flex items-center gap-1'>
                            <Zap className='h-3 w-3' />
                            <span className='text-xs text-muted-foreground'>缓存项</span>
                          </div>
                          <div className='text-lg font-bold'>{cacheMetrics.totalKeys}</div>
                        </div>
                      </div>

                      {/* 内存使用率 */}
                      <div className='space-y-2'>
                        <div className='flex items-center justify-between'>
                          <span className='text-xs text-muted-foreground'>内存使用</span>
                          <span
                            className={`text-xs font-medium ${
                              cacheStats.memory.utilization >= 80
                                ? 'text-red-600'
                                : cacheStats.memory.utilization >= 60
                                  ? 'text-yellow-600'
                                  : 'text-green-600'
                            }`}
                          >
                            {cacheStats.memory.utilization.toFixed(1)}%
                          </span>
                        </div>
                        <Progress value={cacheStats.memory.utilization} className='h-2' />
                        <div className='text-xs text-muted-foreground'>
                          {Math.round(cacheStats.memory.currentSize / 1024 / 1024)}MB /
                          {Math.round(cacheStats.memory.maxSize / 1024 / 1024)}MB
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className='flex gap-2'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={updatePerformanceData}
                          className='flex-1'
                        >
                          <RefreshCw className='h-3 w-3 mr-1' />
                          刷新
                        </Button>
                        <Button variant='outline' size='sm' onClick={clearCache} className='flex-1'>
                          <Trash2 className='h-3 w-3 mr-1' />
                          清理
                        </Button>
                      </div>

                      {/* 热门缓存项 */}
                      {cacheMetrics.stats.length > 0 && (
                        <div className='space-y-3 pt-3 border-t'>
                          <div className='text-sm font-medium'>热门缓存项</div>
                          <div className='space-y-2 max-h-32 overflow-y-auto'>
                            {cacheMetrics.stats.slice(0, 3).map((stat, index) => (
                              <div key={index} className='space-y-1'>
                                <div className='flex items-center justify-between'>
                                  <span className='text-xs font-medium truncate max-w-24'>
                                    {stat.key.split('-')[0]}
                                  </span>
                                  <Badge variant='secondary' className='text-xs'>
                                    {stat.hitRate.toFixed(0)}%
                                  </Badge>
                                </div>
                                <div className='flex items-center justify-between text-xs text-muted-foreground'>
                                  <span>{stat.totalRequests} 次</span>
                                  <span className='flex items-center gap-1'>
                                    <Clock className='h-3 w-3' />
                                    {stat.avgComputeTime.toFixed(1)}ms
                                  </span>
                                </div>
                                <Progress value={stat.hitRate} className='h-1' />
                              </div>
                            ))}
                          </div>

                          {/* 性能提升指标 */}
                          <div className='space-y-2 pt-2 border-t'>
                            <div className='text-sm font-medium'>性能提升</div>
                            <div className='grid grid-cols-2 gap-2 text-xs'>
                              <div className='space-y-1'>
                                <span className='text-muted-foreground'>节省计算</span>
                                <div className='font-medium text-green-600'>
                                  {cacheMetrics.stats.reduce((sum, s) => sum + s.hits, 0)} 次
                                </div>
                              </div>
                              <div className='space-y-1'>
                                <span className='text-muted-foreground'>节省时间</span>
                                <div className='font-medium text-green-600'>
                                  {(
                                    cacheMetrics.stats.reduce(
                                      (sum, s) => sum + s.hits * s.avgComputeTime,
                                      0
                                    ) / 1000
                                  ).toFixed(1)}
                                  s
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className='text-center text-muted-foreground py-4'>
                      <Database className='w-8 h-8 mx-auto mb-2 opacity-50' />
                      <div className='text-sm'>缓存数据加载中...</div>
                    </div>
                  )}
                </TabsContent>
              </Tabs>

              <div className='text-xs text-gray-500 text-center pt-2 border-t mt-3'>
                每5秒自动更新 • {new Date().toLocaleTimeString()}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  // 完整版本的性能监控面板
  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle className='flex items-center'>
          <Activity className='w-5 h-5 mr-2' />
          性能监控面板
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* 完整版本的内容可以在这里扩展 */}
        <div className='text-center text-gray-500'>完整版性能监控面板开发中...</div>
      </CardContent>
    </Card>
  );
};

export default PerformanceMonitorWidget;
