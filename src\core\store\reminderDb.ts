import { DBSchema, openDB } from 'idb';

import type { ReminderConfig, ReminderMessage } from '@/core/types';

interface DispatchReminderDB extends DBSchema {
  reminderConfigs: {
    key: string; // taskId
    value: ReminderConfig;
  };
  reminderMessages: {
    key: string; // id
    value: ReminderMessage;
  };
}

const DB_NAME = 'dispatch-reminder';
const DB_VERSION = 1;

const isBrowser = typeof window !== 'undefined' && typeof window.indexedDB !== 'undefined';

async function getDb() {
  if (!isBrowser) {
    // Return null when running on server
    return null;
  }
  return openDB<DispatchReminderDB>(DB_NAME, DB_VERSION, {
    upgrade(db) {
      if (!db.objectStoreNames.contains('reminderConfigs')) {
        db.createObjectStore('reminderConfigs', { keyPath: 'taskId' });
      }
      if (!db.objectStoreNames.contains('reminderMessages')) {
        db.createObjectStore('reminderMessages', { keyPath: 'id' });
      }
    },
  });
}

// Reminder Configs
export async function getAllConfigs(): Promise<ReminderConfig[]> {
  const db = await getDb();
  if (!db) return []; // Return empty array if DB access is not available (SSR)
  return db.getAll('reminderConfigs');
}

export async function addOrUpdateConfig(config: ReminderConfig) {
  const db = await getDb();
  if (!db) return; // No-op when running on server
  await db.put('reminderConfigs', config);
}

export async function removeConfig(taskId: string) {
  const db = await getDb();
  if (!db) return; // No-op when running on server
  await db.delete('reminderConfigs', taskId);
}

// Reminder Messages
export async function getAllMessages(): Promise<ReminderMessage[]> {
  const db = await getDb();
  if (!db) return []; // Return empty array if DB access is not available (SSR)
  return db.getAll('reminderMessages');
}

export async function addMessage(message: ReminderMessage) {
  const db = await getDb();
  if (!db) return; // No-op when running on server
  await db.put('reminderMessages', message);
}

export async function updateMessage(id: string, patch: Partial<ReminderMessage>) {
  const db = await getDb();
  if (!db) return; // No-op when running on server

  const old = await db.get('reminderMessages', id);
  if (old) {
    await db.put('reminderMessages', { ...old, ...patch });
  }
}

export async function removeMessage(id: string) {
  const db = await getDb();
  if (!db) return; // No-op when running on server
  await db.delete('reminderMessages', id);
}

export async function clearAll() {
  const db = await getDb();
  if (!db) return; // No-op when running on server
  await db.clear('reminderConfigs');
  await db.clear('reminderMessages');
}
