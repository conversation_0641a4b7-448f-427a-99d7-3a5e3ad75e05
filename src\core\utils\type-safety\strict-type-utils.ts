/**
 * 严格类型工具函数
 * 提供类型安全的工具函数，减少any类型的使用
 */

// ==================== 类型安全的对象操作 ====================

/**
 * 类型安全的对象键获取
 */
export function getObjectKeys<T extends Record<string, any>>(obj: T): Array<keyof T> {
  return Object.keys(obj) as Array<keyof T>;
}

/**
 * 类型安全的对象值获取
 */
export function getObjectValues<T extends Record<string, any>>(obj: T): Array<T[keyof T]> {
  return Object.values(obj);
}

/**
 * 类型安全的对象条目获取
 */
export function getObjectEntries<T extends Record<string, any>>(
  obj: T
): Array<[keyof T, T[keyof T]]> {
  return Object.entries(obj) as Array<[keyof T, T[keyof T]]>;
}

/**
 * 类型安全的对象属性检查
 */
export function hasProperty<T extends Record<string, any>, K extends string>(
  obj: T,
  key: K
): obj is T & Record<K, unknown> {
  return key in obj;
}

/**
 * 类型安全的对象属性获取
 */
export function getProperty<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  key: K
): T[K] {
  return obj[key];
}

/**
 * 类型安全的对象属性设置
 */
export function setProperty<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  key: K,
  value: T[K]
): T {
  return { ...obj, [key]: value };
}

/**
 * 类型安全的对象深度合并
 */
export function deepMerge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
  if (!sources.length) return target;
  const source = sources.shift();

  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} });
        deepMerge(target[key], source[key] as any);
      } else {
        Object.assign(target, { [key]: source[key] });
      }
    }
  }

  return deepMerge(target, ...sources);
}

// ==================== 类型安全的数组操作 ====================

/**
 * 类型安全的数组过滤（移除undefined和null）
 */
export function filterDefined<T>(array: Array<T | undefined | null>): T[] {
  return array.filter((item): item is T => item !== undefined && item !== null);
}

/**
 * 类型安全的数组映射
 */
export function mapArray<T, U>(array: T[], mapper: (item: T, index: number) => U): U[] {
  return array.map(mapper);
}

/**
 * 类型安全的数组查找
 */
export function findInArray<T>(
  array: T[],
  predicate: (item: T, index: number) => boolean
): T | undefined {
  return array.find(predicate);
}

/**
 * 类型安全的数组分组
 */
export function groupBy<T, K extends string | number | symbol>(
  array: T[],
  keySelector: (item: T) => K
): Record<K, T[]> {
  return array.reduce(
    (groups, item) => {
      const key = keySelector(item);
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
      return groups;
    },
    {} as Record<K, T[]>
  );
}

/**
 * 类型安全的数组去重
 */
export function uniqueArray<T>(array: T[], keySelector?: (item: T) => string | number): T[] {
  if (!keySelector) {
    return [...new Set(array)];
  }

  const seen = new Set<string | number>();
  return array.filter(item => {
    const key = keySelector(item);
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
}

// ==================== 类型安全的JSON操作 ====================

/**
 * 类型安全的JSON解析
 */
export function parseJSON<T = unknown>(
  json: string,
  reviver?: (key: string, value: any) => any
): T | null {
  try {
    return JSON.parse(json, reviver) as T;
  } catch {
    return null;
  }
}

/**
 * 类型安全的JSON字符串化
 */
export function stringifyJSON<T>(
  value: T,
  replacer?: (key: string, value: any) => any,
  space?: string | number
): string | null {
  try {
    return JSON.stringify(value, replacer, space);
  } catch {
    return null;
  }
}

// ==================== 类型安全的异步操作 ====================

/**
 * 类型安全的Promise包装
 */
export async function safePromise<T, E = Error>(
  promise: Promise<T>
): Promise<[T | null, E | null]> {
  try {
    const data = await promise;
    return [data, null];
  } catch (error) {
    return [null, error as E];
  }
}

/**
 * 类型安全的超时Promise
 */
export function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  timeoutMessage = 'Operation timed out'
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error(timeoutMessage)), timeoutMs)
    ),
  ]);
}

/**
 * 类型安全的重试机制
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxRetries) {
        throw lastError;
      }

      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError!;
}

// ==================== 类型安全的事件处理 ====================

/**
 * 类型安全的事件监听器
 */
export function createTypedEventListener<T extends Event>(
  element: EventTarget,
  eventType: string,
  handler: (event: T) => void,
  options?: AddEventListenerOptions
): () => void {
  const typedHandler = (event: Event) => handler(event as T);
  element.addEventListener(eventType, typedHandler, options);

  return () => element.removeEventListener(eventType, typedHandler, options);
}

/**
 * 类型安全的自定义事件创建
 */
export function createCustomEvent<T = any>(
  type: string,
  detail: T,
  options?: CustomEventInit
): CustomEvent<T> {
  return new CustomEvent(type, {
    ...options,
    detail,
  });
}

// ==================== 类型安全的存储操作 ====================

/**
 * 类型安全的localStorage操作
 */
export const typedLocalStorage = {
  getItem<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch {
      return null;
    }
  },

  setItem<T>(key: string, value: T): boolean {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch {
      return false;
    }
  },

  removeItem(key: string): boolean {
    try {
      localStorage.removeItem(key);
      return true;
    } catch {
      return false;
    }
  },

  clear(): boolean {
    try {
      localStorage.clear();
      return true;
    } catch {
      return false;
    }
  },
};

/**
 * 类型安全的sessionStorage操作
 */
export const typedSessionStorage = {
  getItem<T>(key: string): T | null {
    try {
      const item = sessionStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch {
      return null;
    }
  },

  setItem<T>(key: string, value: T): boolean {
    try {
      sessionStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch {
      return false;
    }
  },

  removeItem(key: string): boolean {
    try {
      sessionStorage.removeItem(key);
      return true;
    } catch {
      return false;
    }
  },

  clear(): boolean {
    try {
      sessionStorage.clear();
      return true;
    } catch {
      return false;
    }
  },
};

// ==================== 类型安全的工具函数 ====================

/**
 * 类型安全的对象检查
 */
export function isObject(value: unknown): value is Record<string, any> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

/**
 * 类型安全的数组检查
 */
export function isArray<T>(value: unknown): value is T[] {
  return Array.isArray(value);
}

/**
 * 类型安全的函数检查
 */
export function isFunction(value: unknown): value is Function {
  return typeof value === 'function';
}

/**
 * 类型安全的字符串检查
 */
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

/**
 * 类型安全的数字检查
 */
export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

/**
 * 类型安全的布尔值检查
 */
export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

/**
 * 类型安全的空值检查
 */
export function isNullish(value: unknown): value is null | undefined {
  return value === null || value === undefined;
}

/**
 * 类型安全的非空值检查
 */
export function isNotNullish<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * 类型安全的空字符串检查
 */
export function isEmptyString(value: unknown): value is '' {
  return value === '';
}

/**
 * 类型安全的非空字符串检查
 */
export function isNonEmptyString(value: unknown): value is string {
  return isString(value) && value.length > 0;
}

// ==================== 类型安全的断言函数 ====================

/**
 * 类型安全的断言函数
 */
export function assert(condition: any, message: string): asserts condition {
  if (!condition) {
    throw new Error(`断言失败: ${message}`);
  }
}

/**
 * 类型安全的类型断言
 */
export function assertType<T>(
  value: unknown,
  typeGuard: (value: unknown) => value is T,
  message: string
): asserts value is T {
  if (!typeGuard(value)) {
    throw new TypeError(`类型断言失败: ${message}`);
  }
}
