// src/utils/__tests__/task-grouping.test.ts
import { groupTasks, getTaskGroupKey } from '../task-grouping';
import type { Task, TaskGroupConfig } from '@/core/types';

// Mock task data
const mockTasks: Task[] = [
  {
    id: '1',
    taskNumber: 'T001',
    strength: 'C30',
    projectName: '项目A',
    dispatchStatus: 'New',
    status: 'New',
    plantId: 'plant1',
    constructionUnit: '施工单位A',
    constructionSite: '部位A',
    pouringMethod: '泵送',
    supplyDate: '2025-01-01',
    supplyTime: '08:00',
    publishDate: '2025-01-01',
    pumpTruck: '泵车1',
    vehicleCount: 2,
    projectAbbreviation: 'PA',
    contactPhone: '123456789',
    otherRequirements: '无',
    timing: '正常',
    requiredVolume: 100,
    completedVolume: 0,
    completedProgress: 0,
    deliveryStatus: '未配送',
    freezeResistance: 'F50',
    impermeability: 'P6',
    vehicles: [],
    updatedAt: '2025-01-01',
  } as unknown as Task,
  {
    id: '2',
    taskNumber: 'T002',
    strength: 'C35',
    projectName: '项目B',
    dispatchStatus: 'InProgress',
    status: 'InProgress',
    plantId: 'plant1',
    constructionUnit: '施工单位B',
    constructionSite: '部位B',
    pouringMethod: '泵送',
    supplyDate: '2025-01-01',
    supplyTime: '09:00',
    publishDate: '2025-01-01',
    pumpTruck: '泵车2',
    vehicleCount: 3,
    projectAbbreviation: 'PB',
    contactPhone: '987654321',
    otherRequirements: '无',
    timing: '正常',
    requiredVolume: 150,
    completedVolume: 50,
    completedProgress: 33,
    deliveryStatus: '配送中',
    freezeResistance: 'F50',
    impermeability: 'P8',
    vehicles: [],
    updatedAt: '2025-01-01',
  } as unknown as Task,
  {
    id: '3',
    taskNumber: 'T003',
    strength: 'C30',
    projectName: '项目C',
    dispatchStatus: 'New',
    status: 'New',
    plantId: 'plant1',
    constructionUnit: '施工单位C',
    constructionSite: '部位C',
    pouringMethod: '直接浇筑',
    supplyDate: '2025-01-01',
    supplyTime: '10:00',
    publishDate: '2025-01-01',
    pumpTruck: '无',
    vehicleCount: 1,
    projectAbbreviation: 'PC',
    contactPhone: '555666777',
    otherRequirements: '特殊要求',
    timing: '紧急',
    requiredVolume: 80,
    completedVolume: 0,
    completedProgress: 0,
    deliveryStatus: '未配送',
    freezeResistance: 'F100',
    impermeability: 'P10',
    vehicles: [],
    updatedAt: '2025-01-01',
  } as unknown as Task,
];

const mockGroupConfig: TaskGroupConfig = {
  groupBy: 'strength',
  enabled: true,
  collapsible: true,
  defaultCollapsed: [],
  sortOrder: 'asc',
  showGroupStats: true,
  allowedGroupColumns: ['strength'],
  disallowedGroupColumns: [],
  groupHeaderStyle: {
    backgroundColor: 'bg-gray-100',
    textColor: 'text-gray-900',
    fontSize: 'text-sm',
    fontWeight: 'font-medium',
    padding: 'py-2',
  },
};

describe('task-grouping', () => {
  describe('getTaskGroupKey', () => {
    it('should return correct group key for strength', () => {
      const task = mockTasks[0] ? mockTasks[0] : ({} as Task);
      const groupKey = getTaskGroupKey(task, 'strength');
      expect(groupKey).toBe('C30');
    });

    it('should return "all" for none grouping', () => {
      const task = mockTasks[0] ? mockTasks[0] : ({} as Task);
      const groupKey = getTaskGroupKey(task, 'none');
      expect(groupKey).toBe('all');
    });

    it('should handle different field types', () => {
      const task = mockTasks[0] ? mockTasks[0] : ({} as Task);

      // String field
      expect(getTaskGroupKey(task, 'projectName')).toBe('项目A');

      // Number field
      expect(getTaskGroupKey(task, 'vehicleCount')).toBe('2');

      // Status field
      expect(getTaskGroupKey(task, 'dispatchStatus')).toBe('New');
    });
  });

  describe('groupTasks', () => {
    it('should return single group when grouping is disabled', () => {
      const disabledConfig = { ...mockGroupConfig, enabled: false };
      const result = groupTasks(mockTasks, disabledConfig);

      expect(result).toHaveLength(1);
      expect(result[0]?.key).toBe('all');
      expect(result[0]?.label).toBe('所有任务');
      expect(result[0]?.tasks).toHaveLength(3);
    });

    it('should return single group when groupBy is none', () => {
      const noneConfig = { ...mockGroupConfig, groupBy: 'none' as const };
      const result = groupTasks(mockTasks, noneConfig);

      expect(result).toHaveLength(1);
      expect(result[0]?.key).toBe('all');
      expect(result[0]?.tasks).toHaveLength(3);
    });

    it('should group tasks by strength correctly', () => {
      const result = groupTasks(mockTasks, mockGroupConfig);

      // Should have 2 groups: C30 and C35
      expect(result).toHaveLength(2);

      // Find C30 group
      const c30Group = result.find(g => g.key === 'C30');
      expect(c30Group).toBeDefined();
      expect(c30Group!.tasks).toHaveLength(2); // T001 and T003
      expect(c30Group!.label).toBe('💪 C30');

      // Find C35 group
      const c35Group = result.find(g => g.key === 'C35');
      expect(c35Group).toBeDefined();
      expect(c35Group!.tasks).toHaveLength(1); // T002
      expect(c35Group!.label).toBe('💪 C35');
    });

    it('should group tasks by project name correctly', () => {
      const projectConfig = { ...mockGroupConfig, groupBy: 'projectName' as const };
      const result = groupTasks(mockTasks, projectConfig);

      // Should have 3 groups: 项目A, 项目B, 项目C
      expect(result).toHaveLength(3);

      result.forEach(group => {
        expect(group.tasks).toHaveLength(1);
        expect(group.label).toMatch(/🏗️/);
      });
    });

    it('should handle empty task list', () => {
      const result = groupTasks([], mockGroupConfig);
      expect(result).toHaveLength(0);
    });

    it('should handle collapsed groups', () => {
      const collapsedConfig = {
        ...mockGroupConfig,
        defaultCollapsed: ['C30'],
      };
      const result = groupTasks(mockTasks, collapsedConfig);

      const c30Group = result.find(g => g.key === 'C30');
      expect(c30Group?.collapsed).toBe(true);

      const c35Group = result.find(g => g.key === 'C35');
      expect(c35Group?.collapsed).toBe(false);
    });
  });
});
