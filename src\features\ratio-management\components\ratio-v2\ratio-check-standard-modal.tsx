'use client';

import React, { useState, useEffect } from 'react';
import {
  Search,
  Plus,
  Trash2,
  Save,
  X,
  Download,
  Upload,
  RotateCcw,
  Info,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { But<PERSON> } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/table';
import { useToast } from '@/shared/hooks/use-toast';
import { cn } from '@/core/lib/utils';
import type { RatioCheckStandard } from '@/core/types/ratio-check-standard';
import { useRatioCheckStandards } from '@/features/ratio-management/hooks/ratio/useRatioCheckStandards';

interface RatioCheckStandardModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function RatioCheckStandardModal({ isOpen, onClose }: RatioCheckStandardModalProps) {
  const { toast } = useToast();
  const {
    standards,
    loading,
    addStandard,
    updateStandard,
    deleteStandard,
    resetToDefaults,
    exportStandards,
    importStandards,
  } = useRatioCheckStandards();

  const [selectedStandard, setSelectedStandard] = useState<RatioCheckStandard | null>(null);
  const [editingStandard, setEditingStandard] = useState<Partial<RatioCheckStandard>>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  // 过滤标准列表
  const filteredStandards = standards.filter(standard => {
    // 确保 strength 是字符串类型，兼容旧数据
    const strengthStr =
      typeof standard.strength === 'string' ? standard.strength : String(standard.strength);

    return (
      strengthStr.toLowerCase().includes(searchTerm.toLowerCase()) ||
      standard.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // 选择标准时初始化编辑数据
  useEffect(() => {
    if (selectedStandard) {
      setEditingStandard({ ...selectedStandard });
      setIsEditing(false);
    }
  }, [selectedStandard]);

  // 处理新增标准
  const handleAddStandard = () => {
    const newStandard = {
      strength: 'C30',
      category: '普通混凝土',
      isActive: true,
      description: '',
    };
    setEditingStandard(newStandard);
    setSelectedStandard(null);
    setIsEditing(true);
  };

  // 处理保存标准
  const handleSaveStandard = () => {
    if (!editingStandard.strength || !editingStandard.category) {
      toast({
        title: '保存失败',
        description: '请填写强度等级和类别',
        variant: 'destructive',
      });
      return;
    }

    try {
      if (selectedStandard) {
        // 更新现有标准
        updateStandard(selectedStandard.id, editingStandard);
        toast({
          title: '保存成功',
          description: '标准已更新',
        });
      } else {
        // 添加新标准
        const newStandard = addStandard(
          editingStandard as Omit<RatioCheckStandard, 'id' | 'createdAt' | 'updatedAt'>
        );
        setSelectedStandard(newStandard);
        toast({
          title: '添加成功',
          description: '新标准已添加',
        });
      }
      setIsEditing(false);
    } catch (error) {
      toast({
        title: '保存失败',
        description: '操作失败，请重试',
        variant: 'destructive',
      });
    }
  };

  // 处理删除标准
  const handleDeleteStandard = () => {
    if (!selectedStandard) return;

    try {
      deleteStandard(selectedStandard.id);
      setSelectedStandard(null);
      setEditingStandard({});
      toast({
        title: '删除成功',
        description: '标准已删除',
      });
    } catch (error) {
      toast({
        title: '删除失败',
        description: '操作失败，请重试',
        variant: 'destructive',
      });
    }
  };

  // 处理导入文件
  const handleImportFile = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    importStandards(file)
      .then(() => {
        toast({
          title: '导入成功',
          description: '标准数据已导入',
        });
      })
      .catch(error => {
        toast({
          title: '导入失败',
          description: error.message,
          variant: 'destructive',
        });
      });

    // 清空文件输入
    event.target.value = '';
  };

  // 处理重置为默认
  const handleResetToDefaults = () => {
    resetToDefaults();
    setSelectedStandard(null);
    setEditingStandard({});
    toast({
      title: '重置成功',
      description: '已恢复默认标准配置',
    });
  };

  // 渲染字段输入组件
  const renderFieldInput = (field: string, label: string, unit: string) => {
    const value = editingStandard[field as keyof RatioCheckStandard] as number | undefined;

    return (
      <div className='space-y-1'>
        <Label htmlFor={field} className='text-xs font-medium'>
          {label}
        </Label>
        <div className='relative'>
          <Input
            id={field}
            type='number'
            value={value || ''}
            onChange={e => {
              const numValue = e.target.value ? parseFloat(e.target.value) : undefined;
              setEditingStandard(prev => ({
                ...prev,
                [field]: numValue,
              }));
            }}
            disabled={!isEditing}
            className='text-xs pr-12'
            placeholder='未设置'
          />
          <span className='absolute right-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground'>
            {unit}
          </span>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className='max-w-6xl max-h-[90vh]'>
          <div className='flex items-center justify-center h-64'>
            <div className='text-center'>
              <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2'></div>
              <p className='text-sm text-muted-foreground'>加载中...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-6xl max-h-[90vh] overflow-hidden'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <CheckCircle className='h-5 w-5 text-primary' />
            配比检查标准
          </DialogTitle>
        </DialogHeader>

        <div className='flex gap-4 h-[calc(90vh-120px)]'>
          {/* 左侧：标准列表 */}
          <Card className='w-80 flex flex-col'>
            <CardHeader className='pb-2'>
              <div className='flex items-center gap-2'>
                <div className='relative flex-1'>
                  <Search className='absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground' />
                  <Input
                    placeholder='搜索强度或类别...'
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    className='pl-8 text-xs'
                  />
                </div>
                <Button size='sm' onClick={handleAddStandard} className='gap-1'>
                  <Plus className='h-3 w-3' />
                  新增
                </Button>
              </div>
            </CardHeader>
            <CardContent className='flex-1 overflow-auto p-0'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className='text-xs'>强度</TableHead>
                    <TableHead className='text-xs'>类别</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStandards.map(standard => (
                    <TableRow
                      key={standard.id}
                      className={cn(
                        'cursor-pointer hover:bg-muted/50',
                        selectedStandard?.id === standard.id && 'bg-muted'
                      )}
                      onClick={() => setSelectedStandard(standard)}
                    >
                      <TableCell className='text-xs font-medium'>
                        {standard.strength}
                        {!standard.isActive && (
                          <Badge variant='secondary' className='ml-1 text-xs'>
                            停用
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className='text-xs'>{standard.category}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* 右侧：编辑表单 */}
          <Card className='flex-1 flex flex-col'>
            <CardHeader className='pb-2'>
              <div className='flex items-center justify-between'>
                <CardTitle className='text-sm'>
                  {selectedStandard ? `编辑标准 - C${editingStandard.strength}` : '新增标准'}
                </CardTitle>
                <div className='flex items-center gap-1'>
                  {selectedStandard && (
                    <Button
                      size='sm'
                      variant='outline'
                      onClick={() => setIsEditing(!isEditing)}
                      className='gap-1'
                    >
                      {isEditing ? '取消' : '编辑'}
                    </Button>
                  )}
                  <Button
                    size='sm'
                    onClick={handleSaveStandard}
                    disabled={!isEditing}
                    className='gap-1'
                  >
                    <Save className='h-3 w-3' />
                    保存
                  </Button>
                  {selectedStandard && (
                    <Button
                      size='sm'
                      variant='destructive'
                      onClick={handleDeleteStandard}
                      className='gap-1'
                    >
                      <Trash2 className='h-3 w-3' />
                      删除
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className='flex-1 overflow-auto space-y-4'>
              {/* 基本信息 */}
              <div className='grid grid-cols-3 gap-3'>
                <div className='space-y-1'>
                  <Label htmlFor='strength' className='text-xs font-medium'>
                    强度等级 *
                  </Label>
                  <Input
                    id='strength'
                    type='text'
                    value={editingStandard.strength || ''}
                    onChange={e =>
                      setEditingStandard(prev => ({
                        ...prev,
                        strength: e.target.value,
                      }))
                    }
                    disabled={!isEditing}
                    className='text-xs'
                    placeholder='如：C30、C40-P6'
                  />
                </div>
                <div className='space-y-1'>
                  <Label htmlFor='category' className='text-xs font-medium'>
                    类别 *
                  </Label>
                  <Select
                    value={editingStandard.category || ''}
                    onValueChange={value =>
                      setEditingStandard(prev => ({
                        ...prev,
                        category: value,
                      }))
                    }
                    disabled={!isEditing}
                  >
                    <SelectTrigger className='text-xs'>
                      <SelectValue placeholder='选择类别' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='普通混凝土'>普通混凝土</SelectItem>
                      <SelectItem value='高强混凝土'>高强混凝土</SelectItem>
                      <SelectItem value='轻质混凝土'>轻质混凝土</SelectItem>
                      <SelectItem value='特种混凝土'>特种混凝土</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className='space-y-1'>
                  <Label htmlFor='isActive' className='text-xs font-medium'>
                    状态
                  </Label>
                  <Select
                    value={editingStandard.isActive ? 'active' : 'inactive'}
                    onValueChange={value =>
                      setEditingStandard(prev => ({
                        ...prev,
                        isActive: value === 'active',
                      }))
                    }
                    disabled={!isEditing}
                  >
                    <SelectTrigger className='text-xs'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='active'>启用</SelectItem>
                      <SelectItem value='inactive'>停用</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 材料限制 */}
              <div className='space-y-3'>
                {/* 水泥类 */}
                <div>
                  <h4 className='text-xs font-medium mb-2 text-muted-foreground'>
                    水泥类材料 (kg/m³)
                  </h4>
                  <div className='grid grid-cols-3 gap-3'>
                    {renderFieldInput('cementMin', '水泥下限', 'kg/m³')}
                    {renderFieldInput('cementMax', '水泥上限', 'kg/m³')}
                    <div></div>
                    {renderFieldInput('cement325Min', '325下限', 'kg/m³')}
                    {renderFieldInput('cement325Max', '325上限', 'kg/m³')}
                    <div></div>
                    {renderFieldInput('cement425Min', '425下限', 'kg/m³')}
                    {renderFieldInput('cement425Max', '425上限', 'kg/m³')}
                  </div>
                </div>

                {/* 骨料类 */}
                <div>
                  <h4 className='text-xs font-medium mb-2 text-muted-foreground'>
                    骨料类材料 (kg/m³)
                  </h4>
                  <div className='grid grid-cols-3 gap-3'>
                    {renderFieldInput('sandMin', '砂子下限', 'kg/m³')}
                    {renderFieldInput('sandMax', '砂子上限', 'kg/m³')}
                    <div></div>
                    {renderFieldInput('stoneMin', '石子下限', 'kg/m³')}
                    {renderFieldInput('stoneMax', '石子上限', 'kg/m³')}
                  </div>
                </div>

                {/* 外加剂类 */}
                <div>
                  <h4 className='text-xs font-medium mb-2 text-muted-foreground'>
                    外加剂类材料 (kg/m³)
                  </h4>
                  <div className='grid grid-cols-3 gap-3'>
                    {renderFieldInput('additiveMin', '外加剂下限', 'kg/m³')}
                    {renderFieldInput('additiveMax', '外加剂上限', 'kg/m³')}
                    <div></div>
                    {renderFieldInput('flyAshMin', '粉煤灰下限', 'kg/m³')}
                    {renderFieldInput('flyAshMax', '粉煤灰上限', 'kg/m³')}
                    <div></div>
                    {renderFieldInput('mineralPowderMin', '矿粉下限', 'kg/m³')}
                    {renderFieldInput('mineralPowderMax', '矿粉上限', 'kg/m³')}
                  </div>
                </div>

                {/* 水 */}
                <div>
                  <h4 className='text-xs font-medium mb-2 text-muted-foreground'>水 (kg/m³)</h4>
                  <div className='grid grid-cols-3 gap-3'>
                    {renderFieldInput('waterMin', '水下限', 'kg/m³')}
                    {renderFieldInput('waterMax', '水上限', 'kg/m³')}
                  </div>
                </div>
              </div>

              {/* 描述 */}
              <div className='space-y-1'>
                <Label htmlFor='description' className='text-xs font-medium'>
                  描述
                </Label>
                <Input
                  id='description'
                  value={editingStandard.description || ''}
                  onChange={e =>
                    setEditingStandard(prev => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  disabled={!isEditing}
                  className='text-xs'
                  placeholder='标准描述（可选）'
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 底部操作栏 */}
        <div className='flex items-center justify-between pt-2 border-t'>
          <div className='flex items-center gap-2'>
            <Button
              size='sm'
              variant='outline'
              onClick={() => {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.json';
                input.onchange = event => {
                  // Convert native Event to React ChangeEvent
                  const target = event.target as HTMLInputElement;
                  const reactEvent = {
                    ...event,
                    target,
                    currentTarget: target,
                    nativeEvent: event,
                    isDefaultPrevented: () => false,
                    isPropagationStopped: () => false,
                    persist: () => {},
                  } as React.ChangeEvent<HTMLInputElement>;
                  handleImportFile(reactEvent);
                };
                input.click();
              }}
              className='gap-1'
            >
              <Upload className='h-3 w-3' />
              导入
            </Button>
            <Button size='sm' variant='outline' onClick={exportStandards} className='gap-1'>
              <Download className='h-3 w-3' />
              导出
            </Button>
            <Button size='sm' variant='outline' onClick={handleResetToDefaults} className='gap-1'>
              <RotateCcw className='h-3 w-3' />
              恢复默认
            </Button>
          </div>

          <div className='flex items-center gap-2'>
            <Button
              size='sm'
              variant='outline'
              onClick={() => {
                // 显示操作说明
                toast({
                  title: '操作说明',
                  description: '左侧选择标准，右侧编辑参数。支持导入导出JSON格式文件。',
                });
              }}
              className='gap-1'
            >
              <Info className='h-3 w-3' />
              操作说明
            </Button>
            <Button size='sm' variant='outline' onClick={onClose} className='gap-1'>
              <X className='h-3 w-3' />
              退出
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
