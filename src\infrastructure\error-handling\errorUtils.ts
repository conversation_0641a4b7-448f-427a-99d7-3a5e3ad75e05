/**
 * 错误处理工具函数
 * 提供常用的错误处理、转换和分析功能
 */

import { errorManager } from '@/infrastructure/error-handling/ErrorManager';
import { AppError, ErrorCategory, ErrorSeverity } from '@/core/types/error';

/**
 * 错误转换工具
 */
export const errorConverter = {
  /**
   * 将原生Error转换为AppError
   */
  fromError(error: Error, category: ErrorCategory = ErrorCategory.SYSTEM): AppError {
    if (error instanceof AppError) {
      return error;
    }

    // 根据错误消息判断类别
    const detectedCategory = this.detectCategory(error);
    const finalCategory = detectedCategory || category;

    return AppError.create(
      this.generateErrorCode(finalCategory, error.name),
      error.message,
      finalCategory,
      this.detectSeverity(error),
      {
        originalError: error,
        stack: error.stack,
        name: error.name,
      }
    );
  },

  /**
   * 从API响应错误转换
   */
  fromApiResponse(response: Response, data?: any): AppError {
    const status = response.status;
    let category = ErrorCategory.API;
    let severity = ErrorSeverity.MEDIUM;
    let message = `API请求失败 (${status})`;

    // 根据状态码确定类别和严重程度
    if (status >= 500) {
      severity = ErrorSeverity.HIGH;
      message = '服务器内部错误';
    } else if (status === 404) {
      severity = ErrorSeverity.MEDIUM;
      message = '请求的资源不存在';
    } else if (status === 403) {
      category = ErrorCategory.PERMISSION;
      severity = ErrorSeverity.MEDIUM;
      message = '没有访问权限';
    } else if (status === 401) {
      category = ErrorCategory.PERMISSION;
      severity = ErrorSeverity.HIGH;
      message = '身份验证失败';
    } else if (status >= 400) {
      category = ErrorCategory.VALIDATION;
      severity = ErrorSeverity.LOW;
      message = '请求参数错误';
    }

    return AppError.create(`API_${status}`, data?.message || message, category, severity, {
      status,
      url: response.url,
      statusText: response.statusText,
      responseData: data,
    });
  },

  /**
   * 从网络错误转换
   */
  fromNetworkError(error: Error): AppError {
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return AppError.network('网络连接失败', {
        originalError: error,
        offline: !navigator.onLine,
      });
    }

    if (error.message.includes('timeout')) {
      return AppError.network('请求超时', {
        originalError: error,
      });
    }

    return AppError.network(`网络错误: ${error.message}`, {
      originalError: error,
    });
  },

  /**
   * 检测错误类别
   */
  detectCategory(error: Error): ErrorCategory | null {
    const message = error.message.toLowerCase();
    const name = error.name.toLowerCase();

    if (name.includes('network') || message.includes('fetch') || message.includes('network')) {
      return ErrorCategory.NETWORK;
    }

    if (
      name.includes('validation') ||
      message.includes('validation') ||
      message.includes('invalid')
    ) {
      return ErrorCategory.VALIDATION;
    }

    if (
      name.includes('permission') ||
      message.includes('permission') ||
      message.includes('unauthorized')
    ) {
      return ErrorCategory.PERMISSION;
    }

    if (name.includes('reference') || name.includes('type') || message.includes('undefined')) {
      return ErrorCategory.COMPONENT;
    }

    return null;
  },

  /**
   * 检测错误严重程度
   */
  detectSeverity(error: Error): ErrorSeverity {
    const message = error.message.toLowerCase();
    const name = error.name.toLowerCase();

    if (name.includes('critical') || message.includes('critical') || message.includes('fatal')) {
      return ErrorSeverity.CRITICAL;
    }

    if (name.includes('reference') || name.includes('type') || message.includes('cannot read')) {
      return ErrorSeverity.HIGH;
    }

    if (name.includes('validation') || message.includes('validation')) {
      return ErrorSeverity.LOW;
    }

    return ErrorSeverity.MEDIUM;
  },

  /**
   * 生成错误代码
   */
  generateErrorCode(category: ErrorCategory, errorName?: string): string {
    const categoryPrefix = category.toUpperCase();
    const namePrefix = errorName ? `_${errorName.toUpperCase()}` : '';
    const timestamp = Date.now().toString(36);
    return `${categoryPrefix}${namePrefix}_${timestamp}`;
  },
};

/**
 * 错误分析工具
 */
export const errorAnalyzer = {
  /**
   * 分析错误模式
   */
  analyzePattern(errors: AppError[]): {
    mostCommonCategory: ErrorCategory;
    mostCommonSeverity: ErrorSeverity;
    errorRate: number;
    trends: {
      increasing: boolean;
      stable: boolean;
      decreasing: boolean;
    };
  } {
    if (errors.length === 0) {
      return {
        mostCommonCategory: ErrorCategory.SYSTEM,
        mostCommonSeverity: ErrorSeverity.LOW,
        errorRate: 0,
        trends: { increasing: false, stable: true, decreasing: false },
      };
    }

    // 统计类别
    const categoryCount: Record<ErrorCategory, number> = {} as any;
    const severityCount: Record<ErrorSeverity, number> = {} as any;

    errors.forEach(error => {
      categoryCount[error.category] = (categoryCount[error.category] || 0) + 1;
      severityCount[error.severity] = (severityCount[error.severity] || 0) + 1;
    });

    const categoryEntries = Object.entries(categoryCount).sort(([, a], [, b]) => b - a);
    const mostCommonCategory = (categoryEntries[0]?.[0] || 'UNKNOWN') as ErrorCategory;

    const severityEntries = Object.entries(severityCount).sort(([, a], [, b]) => b - a);
    const mostCommonSeverity = (severityEntries[0]?.[0] || 'LOW') as ErrorSeverity;

    // 计算错误率趋势
    const recentErrors = errors.slice(-10);
    const olderErrors = errors.slice(-20, -10);
    const recentRate = recentErrors.length;
    const olderRate = olderErrors.length;

    const trends = {
      increasing: recentRate > olderRate,
      stable: recentRate === olderRate,
      decreasing: recentRate < olderRate,
    };

    return {
      mostCommonCategory,
      mostCommonSeverity,
      errorRate: errors.length,
      trends,
    };
  },

  /**
   * 检查是否为重复错误
   */
  isDuplicateError(error: AppError, recentErrors: AppError[], timeWindow = 60000): boolean {
    const now = Date.now();
    return recentErrors.some(
      recentError =>
        recentError.message === error.message &&
        recentError.category === error.category &&
        now - recentError.timestamp < timeWindow
    );
  },

  /**
   * 获取错误建议
   */
  getSuggestion(error: AppError): string {
    switch (error.category) {
      case ErrorCategory.NETWORK:
        return '请检查网络连接，确保网络稳定后重试。';

      case ErrorCategory.API:
        if (error.code.includes('500')) {
          return '服务器遇到问题，请稍后重试或联系技术支持。';
        }
        if (error.code.includes('404')) {
          return '请求的资源不存在，请检查URL或联系管理员。';
        }
        return '请检查请求参数，确保数据格式正确。';

      case ErrorCategory.VALIDATION:
        return '请检查输入的数据格式，确保符合要求。';

      case ErrorCategory.PERMISSION:
        return '您没有执行此操作的权限，请联系管理员。';

      case ErrorCategory.COMPONENT:
        return '页面组件加载失败，请刷新页面重试。';

      case ErrorCategory.BUSINESS:
        return '业务规则验证失败，请检查操作是否符合业务要求。';

      case ErrorCategory.SYSTEM:
        return '系统遇到问题，请刷新页面或联系技术支持。';

      default:
        return '遇到未知错误，请刷新页面或联系技术支持。';
    }
  },
};

/**
 * 错误处理装饰器
 */
export function withErrorHandling<T extends (...args: any[]) => any>(
  fn: T,
  options?: {
    category?: ErrorCategory;
    severity?: ErrorSeverity;
    context?: Record<string, any>;
    fallback?: any;
  }
): T {
  return ((...args: any[]) => {
    try {
      const result = fn(...args);

      // 如果是Promise，处理异步错误
      if (result instanceof Promise) {
        return result.catch(error => {
          const appError = errorConverter.fromError(
            error,
            options?.category || ErrorCategory.SYSTEM
          );

          if (options?.severity) {
            (appError as any).severity = options.severity;
          }

          errorManager.handleError(appError, {
            ...(options?.context || {}),
            functionName: fn.name,
            arguments: args,
          } as any);

          if (options?.fallback !== undefined) {
            return options.fallback;
          }

          throw appError;
        });
      }

      return result;
    } catch (error) {
      const appError = errorConverter.fromError(
        error as Error,
        options?.category || ErrorCategory.SYSTEM
      );

      if (options?.severity) {
        (appError as any).severity = options.severity;
      }

      errorManager.handleError(appError, {
        ...(options?.context || {}),
        functionName: fn.name,
        arguments: args,
      } as any);

      if (options?.fallback !== undefined) {
        return options.fallback;
      }

      throw appError;
    }
  }) as T;
}

/**
 * 安全执行函数
 */
export async function safeExecute<T>(
  operation: () => Promise<T> | T,
  options?: {
    fallback?: T;
    category?: ErrorCategory;
    context?: Record<string, any>;
    silent?: boolean;
  }
): Promise<T | undefined> {
  try {
    const result = await operation();
    return result;
  } catch (error) {
    const appError = errorConverter.fromError(
      error as Error,
      options?.category || ErrorCategory.SYSTEM
    );

    if (!options?.silent) {
      errorManager.handleError(appError, options?.context);
    }

    return options?.fallback;
  }
}

/**
 * 重试执行函数
 */
export async function retryExecute<T>(
  operation: () => Promise<T>,
  options?: {
    maxRetries?: number;
    delay?: number;
    backoff?: boolean;
    category?: ErrorCategory;
    context?: Record<string, any>;
  }
): Promise<T> {
  const {
    maxRetries = 3,
    delay = 1000,
    backoff = true,
    category = ErrorCategory.SYSTEM,
    context = {},
  } = options || {};

  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxRetries) {
        const appError = errorConverter.fromError(lastError, category);
        errorManager.handleError(appError, {
          ...context,
          retryAttempts: attempt,
          maxRetries,
        } as any);
        throw appError;
      }

      // 等待重试延迟
      const currentDelay = backoff ? delay * attempt : delay;
      await new Promise(resolve => setTimeout(resolve, currentDelay));
    }
  }

  throw lastError!;
}
