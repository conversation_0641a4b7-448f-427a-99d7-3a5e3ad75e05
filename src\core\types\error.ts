/**
 * 统一错误类型定义
 * 为整个应用提供一致的错误分类和处理机制
 */

/**
 * 错误严重程度
 */
export enum ErrorSeverity {
  LOW = 'low', // 低级错误，不影响核心功能
  MEDIUM = 'medium', // 中级错误，影响部分功能
  HIGH = 'high', // 高级错误，影响主要功能
  CRITICAL = 'critical', // 严重错误，影响系统稳定性
}

/**
 * 错误类别
 */
export enum ErrorCategory {
  NETWORK = 'network', // 网络相关错误
  API = 'api', // API调用错误
  VALIDATION = 'validation', // 数据验证错误
  PERMISSION = 'permission', // 权限相关错误
  COMPONENT = 'component', // 组件渲染错误
  BUSINESS = 'business', // 业务逻辑错误
  SYSTEM = 'system', // 系统级错误
  USER_INPUT = 'user_input', // 用户输入错误
  CONFIGURATION = 'configuration', // 配置错误
}

/**
 * 错误恢复策略
 */
export enum ErrorRecoveryStrategy {
  RETRY = 'retry', // 重试操作
  FALLBACK = 'fallback', // 使用备用方案
  RELOAD = 'reload', // 重新加载
  REDIRECT = 'redirect', // 重定向
  IGNORE = 'ignore', // 忽略错误
  USER_ACTION = 'user_action', // 需要用户操作
}

/**
 * 基础错误接口
 */
export interface BaseError {
  id: string;
  code: string;
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  timestamp: number;
  context?: Record<string, any>;
  stack?: string;
  userAgent?: string;
  url?: string;
}

/**
 * 应用错误类
 */
export class AppError extends Error implements BaseError {
  static create(
    id: string,
    message: string,
    category: ErrorCategory,
    severity: ErrorSeverity,
    context: {
      originalError?: Error;
      stack?: string;
      name?: string;
      status?: number;
      url?: string;
      statusText?: string;
      responseData?: any;
      [key: string]: any;
    } = {}
  ): AppError {
    const error = new AppError(id, message, category, severity, context);
    (error as any).id = id;
    return error;
  }
  public readonly id: string;
  public readonly code: string;
  public readonly category: ErrorCategory;
  public readonly severity: ErrorSeverity;
  public readonly timestamp: number;
  public readonly context?: Record<string, any>;
  public readonly userAgent?: string;
  public readonly url?: string;

  constructor(
    code: string,
    message: string,
    category: ErrorCategory,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: Record<string, any>
  ) {
    super(message);
    this.name = 'AppError';
    this.id = `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    this.code = code;
    this.category = category;
    this.severity = severity;
    this.timestamp = Date.now();
    this.context = context;

    if (typeof window !== 'undefined') {
      this.userAgent = navigator.userAgent;
      this.url = window.location.href;
    }

    // 确保错误堆栈正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }

  /**
   * 转换为可序列化的对象
   */
  toJSON(): BaseError {
    return {
      id: this.id,
      code: this.code,
      message: this.message,
      category: this.category,
      severity: this.severity,
      timestamp: this.timestamp,
      context: this.context,
      stack: this.stack,
      userAgent: this.userAgent,
      url: this.url,
    };
  }

  /**
   * 创建网络错误
   */
  static network(message: string, context?: Record<string, any>): AppError {
    return new AppError(
      'NETWORK_ERROR',
      message,
      ErrorCategory.NETWORK,
      ErrorSeverity.HIGH,
      context
    );
  }

  /**
   * 创建API错误
   */
  static api(code: string, message: string, context?: Record<string, any>): AppError {
    return new AppError(code, message, ErrorCategory.API, ErrorSeverity.MEDIUM, context);
  }

  /**
   * 创建验证错误
   */
  static validation(message: string, context?: Record<string, any>): AppError {
    return new AppError(
      'VALIDATION_ERROR',
      message,
      ErrorCategory.VALIDATION,
      ErrorSeverity.LOW,
      context
    );
  }

  /**
   * 创建组件错误
   */
  static component(message: string, context?: Record<string, any>): AppError {
    return new AppError(
      'COMPONENT_ERROR',
      message,
      ErrorCategory.COMPONENT,
      ErrorSeverity.HIGH,
      context
    );
  }

  /**
   * 创建业务逻辑错误
   */
  static business(code: string, message: string, context?: Record<string, any>): AppError {
    return new AppError(code, message, ErrorCategory.BUSINESS, ErrorSeverity.MEDIUM, context);
  }

  /**
   * 创建系统错误
   */
  static system(message: string, context?: Record<string, any>): AppError {
    return new AppError(
      'SYSTEM_ERROR',
      message,
      ErrorCategory.SYSTEM,
      ErrorSeverity.CRITICAL,
      context
    );
  }
}

/**
 * 错误恢复配置
 */
export interface ErrorRecoveryConfig {
  strategy: ErrorRecoveryStrategy;
  maxRetries?: number;
  retryDelay?: number;
  fallbackComponent?: React.ComponentType<any>;
  fallbackData?: any;
  redirectUrl?: string;
  onRecovery?: (error: AppError) => void;
}

/**
 * 错误处理器
 */
export interface ErrorHandler {
  handle: (error: AppError, context?: ErrorContext) => void;
  canHandle: (error: AppError) => boolean;
}

/**
 * 错误指标
 */
export interface ErrorMetrics {
  totalErrors: number;
  errorsByCategory: Record<ErrorCategory, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  recentErrors: AppError[];
}

/**
 * 错误处理器配置
 */
export interface ErrorHandlerConfig {
  category: ErrorCategory;
  severity: ErrorSeverity[];
  recovery: ErrorRecoveryConfig;
  shouldReport?: boolean;
  shouldNotify?: boolean;
  customHandler?: (error: AppError) => void;
}

/**
 * 错误上下文
 */
export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  feature?: string;
  metadata?: Record<string, any>;
}

/**
 * 错误报告
 */
export interface ErrorReport {
  error: BaseError;
  context: ErrorContext;
  userFeedback?: string;
  reproductionSteps?: string[];
  browserInfo: {
    userAgent: string;
    viewport: { width: number; height: number };
    language: string;
    cookieEnabled: boolean;
  };
  performanceInfo?: {
    memory?: any;
    timing?: any;
    connection?: any;
  };
}

/**
 * 错误统计
 */
export interface ErrorStats {
  totalErrors: number;
  errorsByCategory: Record<ErrorCategory, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  errorsByCode: Record<string, number>;
  recentErrors: BaseError[];
  errorTrends: {
    hourly: number[];
    daily: number[];
  };
}

/**
 * 预定义错误代码
 */
export const ERROR_CODES = {
  // 网络错误
  NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',
  NETWORK_OFFLINE: 'NETWORK_OFFLINE',
  NETWORK_FAILED: 'NETWORK_FAILED',

  // API错误
  API_UNAUTHORIZED: 'API_UNAUTHORIZED',
  API_FORBIDDEN: 'API_FORBIDDEN',
  API_NOT_FOUND: 'API_NOT_FOUND',
  API_SERVER_ERROR: 'API_SERVER_ERROR',
  API_RATE_LIMIT: 'API_RATE_LIMIT',

  // 验证错误
  VALIDATION_REQUIRED: 'VALIDATION_REQUIRED',
  VALIDATION_FORMAT: 'VALIDATION_FORMAT',
  VALIDATION_RANGE: 'VALIDATION_RANGE',

  // 业务错误
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  BUSINESS_STATE_INVALID: 'BUSINESS_STATE_INVALID',
  BUSINESS_OPERATION_FAILED: 'BUSINESS_OPERATION_FAILED',

  // 系统错误
  SYSTEM_OUT_OF_MEMORY: 'SYSTEM_OUT_OF_MEMORY',
  SYSTEM_STORAGE_FULL: 'SYSTEM_STORAGE_FULL',
  SYSTEM_FEATURE_UNSUPPORTED: 'SYSTEM_FEATURE_UNSUPPORTED',
} as const;

export type ErrorCode = (typeof ERROR_CODES)[keyof typeof ERROR_CODES];
