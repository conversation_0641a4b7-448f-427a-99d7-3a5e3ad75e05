'use client';

import { <PERSON>culator, RotateCcw, Refresh<PERSON>w, ArrowRight, ArrowLeft } from 'lucide-react';
import { But<PERSON> } from '@/shared/components/button';
import { cn } from '@/core/lib/utils';

interface CalculationButtonsProps {
  onCalculate: () => void;
  onReverseCalculate: () => void;
  canCalculate: boolean;
  canReverseCalculate: boolean;
  isCalculating: boolean;
  hasParamsChanged?: boolean; // 新增：参数是否已修改
  className?: string;
}

export function CalculationButtons({
  onCalculate,
  onReverseCalculate,
  canCalculate,
  canReverseCalculate,
  isCalculating,
  hasParamsChanged = false,
  className,
}: CalculationButtonsProps) {
  return (
    <div className={cn('flex items-center justify-center gap-3 py-2', className)}>
      {/* 计算按钮：从计算参数 → 配比设计 */}
      <div className='flex items-center gap-2'>
        <span className='text-xs text-muted-foreground'>参数</span>
        <Button
          onClick={onCalculate}
          disabled={!canCalculate || isCalculating}
          size='sm'
          className={cn(
            'gap-1 h-8 px-3 text-xs',
            hasParamsChanged
              ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 animate-pulse'
              : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'
          )}
        >
          {isCalculating ? (
            <RefreshCw className='h-3 w-3 animate-spin' />
          ) : (
            <Calculator className='h-3 w-3' />
          )}
          {isCalculating ? '计算中' : hasParamsChanged ? '重新计算' : '计算'}
        </Button>
        <ArrowRight className='h-4 w-4 text-blue-500' />
        <span className='text-xs text-muted-foreground'>配比</span>
      </div>

      {/* 分隔线 */}
      <div className='h-6 w-px bg-gray-300' />

      {/* 反算按钮：从配比设计 ← 计算参数 */}
      <div className='flex items-center gap-2'>
        <span className='text-xs text-muted-foreground'>配比</span>
        <ArrowLeft className='h-4 w-4 text-orange-500' />
        <Button
          onClick={onReverseCalculate}
          disabled={!canReverseCalculate || isCalculating}
          variant='outline'
          size='sm'
          className='gap-1 h-8 px-3 text-xs border-orange-200 text-orange-600 hover:bg-orange-50 disabled:opacity-50 disabled:cursor-not-allowed'
        >
          <RotateCcw className='h-3 w-3' />
          反算
        </Button>
        <span className='text-xs text-muted-foreground'>参数</span>
      </div>
    </div>
  );
}
