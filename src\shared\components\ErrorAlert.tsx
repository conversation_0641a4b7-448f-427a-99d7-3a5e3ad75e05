/**
 * 错误提示组件
 */

import React, { useEffect } from 'react';
import { AlertCircle, X, RefreshCw } from 'lucide-react';
import { Button } from '@/shared/components/button';
import { cn } from '@/core/lib/utils';

interface ErrorAlertProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  onDismiss?: () => void;
  autoClose?: number; // 自动关闭时间（毫秒）
  className?: string;
  variant?: 'error' | 'warning' | 'info';
}

export function ErrorAlert({
  title = '错误',
  message,
  onRetry,
  onDismiss,
  autoClose,
  className,
  variant = 'error',
}: ErrorAlertProps) {
  // 自动关闭功能
  useEffect(() => {
    if (autoClose && onDismiss) {
      const timer = setTimeout(() => {
        onDismiss();
      }, autoClose);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [autoClose, onDismiss]);

  const variantClasses = {
    error: 'border-red-200 bg-red-50 text-red-800',
    warning: 'border-yellow-200 bg-yellow-50 text-yellow-800',
    info: 'border-blue-200 bg-blue-50 text-blue-800',
  };

  const iconClasses = {
    error: 'text-red-500',
    warning: 'text-yellow-500',
    info: 'text-blue-500',
  };

  return (
    <div className={cn('rounded-lg border p-4 shadow-sm', variantClasses[variant], className)}>
      <div className='flex items-start gap-3'>
        <AlertCircle className={cn('h-5 w-5 flex-shrink-0 mt-0.5', iconClasses[variant])} />

        <div className='flex-1 min-w-0'>
          <h3 className='font-medium text-sm'>{title}</h3>
          <p className='mt-1 text-sm opacity-90'>{message}</p>

          {(onRetry || onDismiss) && (
            <div className='mt-3 flex gap-2'>
              {onRetry && (
                <Button variant='outline' size='sm' onClick={onRetry} className='h-8 px-3 text-xs'>
                  <RefreshCw className='h-3 w-3 mr-1' />
                  重试
                </Button>
              )}
              {onDismiss && (
                <Button variant='ghost' size='sm' onClick={onDismiss} className='h-8 px-3 text-xs'>
                  关闭
                </Button>
              )}
            </div>
          )}
        </div>

        {onDismiss && (
          <Button
            variant='ghost'
            size='sm'
            onClick={onDismiss}
            className='h-6 w-6 p-0 flex-shrink-0'
          >
            <X className='h-4 w-4' />
          </Button>
        )}
      </div>
    </div>
  );
}

export default ErrorAlert;
