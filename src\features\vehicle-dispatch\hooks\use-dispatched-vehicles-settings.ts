// src/features/vehicle-dispatch/hooks/use-dispatched-vehicles-settings.ts
import { useCallback, useEffect, useState } from 'react';
import {
  ColumnOrderState,
  ColumnSizingState,
  OnChangeFn,
  VisibilityState,
} from '@tanstack/react-table';

import {
  getDefaultDispatchedVehiclesColumnOrder,
  getDefaultDispatchedVehiclesColumnVisibility,
  getDefaultDispatchedVehiclesColumnWidths,
} from '../config/dispatched-vehicles-columns.config';

/**
 * 已出厂车辆表格设置接口
 */
export interface DispatchedVehiclesSettings {
  columnOrder: string[];
  columnVisibility: Record<string, boolean>;
  columnWidths: Record<string, number>;
  enableZebraStriping: boolean;
  density: 'compact' | 'normal' | 'comfortable';
}

/**
 * 默认设置
 */
const defaultSettings: DispatchedVehiclesSettings = {
  columnOrder: getDefaultDispatchedVehiclesColumnOrder(),
  columnVisibility: getDefaultDispatchedVehiclesColumnVisibility(),
  columnWidths: getDefaultDispatchedVehiclesColumnWidths(),
  enableZebraStriping: true,
  density: 'compact',
};

/**
 * 本地存储键名
 */
const STORAGE_KEY = 'dispatched-vehicles-settings';

/**
 * 从本地存储加载设置
 */
const loadSettingsFromStorage = (): DispatchedVehiclesSettings => {
  // 检查是否在客户端环境
  if (typeof window === 'undefined') {
    return defaultSettings;
  }

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      return {
        ...defaultSettings,
        ...parsed,
      };
    }
  } catch (error) {
    console.warn('Failed to load dispatched vehicles settings from localStorage:', error);
  }
  return defaultSettings;
};

/**
 * 保存设置到本地存储
 */
const saveSettingsToStorage = (settings: DispatchedVehiclesSettings): void => {
  // 检查是否在客户端环境
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
  } catch (error) {
    console.warn('Failed to save dispatched vehicles settings to localStorage:', error);
  }
};

/**
 * 已出厂车辆表格设置Hook
 */
export function useDispatchedVehiclesSettings() {
  const [settings, setSettings] = useState<DispatchedVehiclesSettings>(loadSettingsFromStorage);

  // 保存设置到本地存储
  useEffect(() => {
    saveSettingsToStorage(settings);
  }, [settings]);

  // 更新列顺序
  const onColumnOrderChange: OnChangeFn<ColumnOrderState> = useCallback(updater => {
    setSettings(prev => {
      const newOrder = typeof updater === 'function' ? updater(prev.columnOrder) : updater;
      return {
        ...prev,
        columnOrder: newOrder,
      };
    });
  }, []);

  // 更新列可见性
  const onColumnVisibilityChange: OnChangeFn<VisibilityState> = useCallback(updater => {
    setSettings(prev => {
      const newVisibility =
        typeof updater === 'function' ? updater(prev.columnVisibility) : updater;
      return {
        ...prev,
        columnVisibility: newVisibility,
      };
    });
  }, []);

  // 更新列宽度
  const onColumnSizingChange: OnChangeFn<ColumnSizingState> = useCallback(updater => {
    setSettings(prev => {
      const newSizing = typeof updater === 'function' ? updater(prev.columnWidths) : updater;
      return {
        ...prev,
        columnWidths: newSizing,
      };
    });
  }, []);

  // 更新斑马纹设置
  const onZebraStripingChange = useCallback((enabled: boolean) => {
    setSettings(prev => ({
      ...prev,
      enableZebraStriping: enabled,
    }));
  }, []);

  // 更新密度设置
  const onDensityChange = useCallback((density: 'compact' | 'normal' | 'comfortable') => {
    setSettings(prev => ({
      ...prev,
      density,
    }));
  }, []);

  // 重置设置
  const resetSettings = useCallback(() => {
    setSettings(defaultSettings);
  }, []);

  // 导出设置
  const exportSettings = useCallback(() => {
    return JSON.stringify(settings, null, 2);
  }, [settings]);

  // 导入设置
  const importSettings = useCallback((settingsJson: string) => {
    try {
      const imported = JSON.parse(settingsJson);
      setSettings({
        ...defaultSettings,
        ...imported,
      });
      return true;
    } catch (error) {
      console.error('Failed to import settings:', error);
      return false;
    }
  }, []);

  return {
    settings,
    onColumnOrderChange,
    onColumnVisibilityChange,
    onColumnSizingChange,
    onZebraStripingChange,
    onDensityChange,
    resetSettings,
    exportSettings,
    importSettings,
  };
}
