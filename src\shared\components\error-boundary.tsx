'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';

import { Alert<PERSON><PERSON>gle, Bug, Home, RefreshCw } from 'lucide-react';

import { Button } from '@/shared/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/card';
import { errorBoundaryLogger } from '@/core/lib/logger';
import { errorManager } from '@/infrastructure/error-handling/ErrorManager';
import { AppError } from '@/core/types/error';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 使用新的错误管理系统
    const appError = AppError.component(`组件错误: ${error.message}`, {
      originalError: error,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
    });

    // 记录错误信息 (向后兼容)
    errorBoundaryLogger.error('Component error caught by boundary', error, {
      errorInfo: {
        componentStack: errorInfo.componentStack,
        errorBoundary: this.constructor.name,
      },
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
    });

    // 使用新的错误管理器处理错误
    errorManager.handleError(appError, {
      component: 'ErrorBoundary',
      action: 'componentDidCatch',
      metadata: {
        errorId: this.state.errorId,
        componentStack: errorInfo.componentStack,
      },
    });

    this.setState({
      errorInfo,
    });

    // 调用外部错误处理器
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleReportError = () => {
    const { error, errorInfo, errorId } = this.state;
    const errorReport = {
      errorId,
      message: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
    };

    // 这里可以发送错误报告到监控服务
    console.log('Error Report:', errorReport);

    // 复制到剪贴板
    navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2)).then(() => {
      alert('错误报告已复制到剪贴板');
    });
  };

  override render() {
    if (this.state.hasError) {
      // 如果提供了自定义 fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误 UI
      return (
        <div className='min-h-screen flex items-center justify-center p-4 bg-background'>
          <Card className='w-full max-w-2xl'>
            <CardHeader className='text-center'>
              <div className='mx-auto mb-4 w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center'>
                <AlertTriangle className='w-6 h-6 text-destructive' />
              </div>
              <CardTitle className='text-2xl'>出现了一个错误</CardTitle>
              <CardDescription>
                很抱歉，应用程序遇到了意外错误。请尝试以下解决方案：
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              {/* 操作按钮 */}
              <div className='flex flex-wrap gap-2 justify-center'>
                <Button onClick={this.handleRetry} className='flex items-center gap-2'>
                  <RefreshCw className='w-4 h-4' />
                  重试
                </Button>
                <Button
                  variant='outline'
                  onClick={this.handleReload}
                  className='flex items-center gap-2'
                >
                  <RefreshCw className='w-4 h-4' />
                  刷新页面
                </Button>
                <Button
                  variant='outline'
                  onClick={this.handleGoHome}
                  className='flex items-center gap-2'
                >
                  <Home className='w-4 h-4' />
                  返回首页
                </Button>
                <Button
                  variant='outline'
                  onClick={this.handleReportError}
                  className='flex items-center gap-2'
                >
                  <Bug className='w-4 h-4' />
                  报告错误
                </Button>
              </div>

              {/* 错误详情 (仅在开发环境或明确要求时显示) */}
              {(process.env.NODE_ENV === 'development' || this.props.showDetails) &&
                this.state.error && (
                  <details className='mt-6'>
                    <summary className='cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground'>
                      查看错误详情
                    </summary>
                    <div className='mt-2 p-4 bg-muted rounded-lg'>
                      <div className='space-y-2 text-sm'>
                        <div>
                          <strong>错误ID:</strong> {this.state.errorId}
                        </div>
                        <div>
                          <strong>错误消息:</strong> {this.state.error.message}
                        </div>
                        {this.state.error.stack && (
                          <div>
                            <strong>错误堆栈:</strong>
                            <pre className='mt-1 text-xs bg-background p-2 rounded overflow-auto max-h-40'>
                              {this.state.error.stack}
                            </pre>
                          </div>
                        )}
                        {this.state.errorInfo?.componentStack && (
                          <div>
                            <strong>组件堆栈:</strong>
                            <pre className='mt-1 text-xs bg-background p-2 rounded overflow-auto max-h-40'>
                              {this.state.errorInfo.componentStack}
                            </pre>
                          </div>
                        )}
                      </div>
                    </div>
                  </details>
                )}

              {/* 帮助信息 */}
              <div className='text-center text-sm text-muted-foreground'>
                <p>如果问题持续存在，请联系技术支持或刷新页面重试。</p>
                <p className='mt-1'>错误ID: {this.state.errorId}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// 高阶组件，用于包装其他组件
export function withErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const EnhancedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      {React.createElement(WrappedComponent, props)}
    </ErrorBoundary>
  );

  EnhancedComponent.displayName = `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;
  return EnhancedComponent;
}

// Hook 用于在函数组件中抛出错误到错误边界
export function useErrorHandler() {
  return React.useCallback((error: Error) => {
    throw error;
  }, []);
}
