'use client';

import React, { useState } from 'react';

import { useDrag } from 'react-dnd';
import {
  AlertTriangle,
  CheckCircle,
  Container,
  Droplets,
  Eye,
  Filter,
  Mountain,
  Package,
  Settings,
  TestTube2,
  Zap,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Progress } from '@/shared/components/progress';
import { cn } from '@/core/lib/utils';
import {
  RatioDragItemTypes,
  useRatioDragDrop,
  type DraggedSiloMaterial,
} from '@/shared/components/contexts/RatioDragDropContext';
import { SiloContextMenu } from './silo-context-menu';
import { useToast } from '@/shared/hooks/use-toast';
import { useSiloManagement } from '@/features/ratio-management/hooks/useSiloManagement';
import type { Silo } from '@/core/types/ratio';

// 物料类型图标映射
const materialIcons = {
  cement: Container,
  water: Droplets,
  sand: Mountain,
  stone: Mountain,
  additive: Zap,
  powder: Package,
  flyash: Package, // 粉煤灰使用包装图标
};

// 物料类型颜色映射
const materialColors = {
  cement: 'from-gray-400 to-gray-600',
  water: 'from-blue-400 to-blue-600',
  sand: 'from-yellow-400 to-yellow-600',
  stone: 'from-stone-400 to-stone-600',
  additive: 'from-purple-400 to-purple-600',
  powder: 'from-green-400 to-green-600',
  flyash: 'from-slate-400 to-slate-600', // 粉煤灰使用灰色系
};

interface SiloData {
  id: string;
  name: string;
  materialType: keyof typeof materialIcons;
  materialName: string;
  specification: string;
  capacity: number;
  currentAmount: number;
  status: 'normal' | 'warning' | 'critical' | 'empty';
  temperature?: number;
  humidity?: number;
  lastUpdated: string;
}

interface DraggableSiloProps {
  silo: SiloData;
  onContextMenu: (silo: SiloData, position: { x: number; y: number }) => void;
}

function DraggableSilo({ silo, onContextMenu }: DraggableSiloProps) {
  const { onMaterialDragStart, onMaterialDragEnd } = useRatioDragDrop();

  // 构建拖拽数据
  const dragData: DraggedSiloMaterial = {
    id: silo.id,
    name: silo.materialName,
    type: silo.materialType,
    siloId: silo.id,
    capacity: silo.capacity,
    currentAmount: silo.currentAmount,
    specification: silo.specification,
    materialType: silo.materialType,
    density: 2.4, // 默认密度
    unit: 'kg',
  };

  const [{ isDragging }, drag] = useDrag({
    type: RatioDragItemTypes.SILO_MATERIAL,
    item: () => {
      onMaterialDragStart(dragData);
      return dragData;
    },
    end: () => {
      onMaterialDragEnd();
    },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const Icon = materialIcons[silo.materialType];
  const utilizationRate = (silo.currentAmount / silo.capacity) * 100;

  const getStatusColor = () => {
    switch (silo.status) {
      case 'normal':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'critical':
        return 'text-red-600';
      case 'empty':
        return 'text-gray-400';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = () => {
    switch (silo.status) {
      case 'normal':
        return <CheckCircle className='h-3 w-3' />;
      case 'warning':
      case 'critical':
        return <AlertTriangle className='h-3 w-3' />;
      case 'empty':
        return <Container className='h-3 w-3' />;
      default:
        return <Container className='h-3 w-3' />;
    }
  };

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onContextMenu(silo, { x: e.clientX, y: e.clientY });
  };

  return (
    <div
      ref={drag as any}
      className={cn(
        'cursor-grab active:cursor-grabbing transition-all duration-300 transform-gpu',
        isDragging && 'opacity-70 scale-110 z-50'
      )}
    >
      <Card
        className={cn(
          'transition-all duration-300 border shadow-sm hover:shadow-xl hover:shadow-primary/20',
          'hover:scale-[1.02] hover:-translate-y-1',
          isDragging
            ? 'border-primary shadow-2xl shadow-primary/30 ring-2 ring-primary/20'
            : 'border-border hover:border-primary/30 hover:bg-primary/5'
        )}
        onContextMenu={handleContextMenu}
      >
        <CardContent className='p-1'>
          <div className='flex items-center gap-2'>
            {/* 左侧：紧凑筒仓可视化 */}
            <div className='relative flex-shrink-0'>
              <div
                className={cn(
                  'w-8 h-12 rounded bg-gradient-to-b border border-gray-300 relative overflow-hidden',
                  materialColors[silo.materialType]
                )}
              >
                {/* 物料填充效果 */}
                <div
                  className='absolute bottom-0 left-0 right-0 bg-gradient-to-t from-white/30 to-white/10 transition-all duration-500'
                  style={{ height: `${utilizationRate}%` }}
                />

                {/* 物料图标 */}
                <div className='absolute inset-0 flex items-center justify-center'>
                  <Icon className='h-3 w-3 text-white/80' />
                </div>

                {/* 容量指示器 */}
                <div className='absolute top-0.5 right-0.5'>
                  <div className={cn('w-1 h-1 rounded-full', getStatusColor())} />
                </div>
              </div>

              {/* 容量百分比 */}
              <div className='text-center mt-0.5'>
                <span className='text-xs font-mono font-bold'>{utilizationRate.toFixed(0)}%</span>
              </div>
            </div>

            {/* 右侧：详细信息 */}
            <div className='flex-1 min-w-0 space-y-1'>
              {/* 物料名称 */}
              <div>
                <h4 className='font-medium text-xs truncate'>{silo.name}</h4>
                <p className='text-xs text-muted-foreground truncate'>{silo.materialName}</p>
              </div>

              {/* 容量信息 */}
              <div className='flex items-center justify-between text-xs'>
                <div className={cn('flex items-center gap-1', getStatusColor())}>
                  {getStatusIcon()}
                  <span className='capitalize'>
                    {silo.status === 'normal'
                      ? '正常'
                      : silo.status === 'warning'
                        ? '偏低'
                        : silo.status === 'critical'
                          ? '告警'
                          : '空仓'}
                  </span>
                </div>
                <span className='font-mono text-muted-foreground'>
                  {silo.currentAmount}t/{silo.capacity}t
                </span>
              </div>

              {/* 进度条和剩余量 */}
              <div className='space-y-0.5'>
                <Progress value={utilizationRate} className='h-1' />
                <div className='text-xs text-muted-foreground text-right'>
                  剩余: {(silo.capacity - silo.currentAmount).toFixed(0)}t
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export function SiloVisualizationPanel() {
  const [filter, setFilter] = useState<
    'all' | 'cement' | 'water' | 'sand' | 'stone' | 'additive' | 'powder'
  >('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showDetails, setShowDetails] = useState(false);

  // 右键菜单状态
  const [contextMenu, setContextMenu] = useState<{
    isOpen: boolean;
    position: { x: number; y: number } | null;
    silo: SiloData | null;
  }>({
    isOpen: false,
    position: null,
    silo: null,
  });

  const { toast } = useToast();

  // 使用统一的料仓管理
  const { silos, updateSilo, deleteSilo } = useSiloManagement();

  // 将Silo类型转换为SiloData类型
  const convertSiloToSiloData = (silo: Silo): SiloData => ({
    id: silo.id,
    name: silo.name,
    materialType: silo.type === 'other' ? 'powder' : (silo.type as keyof typeof materialIcons),
    materialName: silo.materialName,
    specification: silo.specification,
    capacity: silo.capacity,
    currentAmount: silo.currentAmount,
    status: silo.status === 'normal' ? 'normal' : 'warning',
    lastUpdated: silo.lastUpdated,
  });

  // 转换料仓数据
  const siloData: SiloData[] = silos.map(convertSiloToSiloData);

  // 模拟筒仓数据（保留作为备用）
  const mockSilos: SiloData[] = [
    {
      id: 'silo-1',
      name: '1#水泥仓',
      materialType: 'cement',
      materialName: 'P.O 42.5水泥',
      specification: '42.5R',
      capacity: 200,
      currentAmount: 150,
      status: 'normal',
      lastUpdated: '2024-12-28 14:20',
    },
    {
      id: 'silo-2',
      name: '2#水泥仓',
      materialType: 'cement',
      materialName: 'P.O 42.5水泥',
      specification: '42.5R',
      capacity: 200,
      currentAmount: 80,
      status: 'warning',
      lastUpdated: '2024-12-28 14:15',
    },
    {
      id: 'silo-3',
      name: '1#粉煤灰仓',
      materialType: 'powder',
      materialName: '粉煤灰',
      specification: 'II级',
      capacity: 150,
      currentAmount: 120,
      status: 'normal',
      lastUpdated: '2024-12-28 14:10',
    },
    {
      id: 'silo-4',
      name: '1#砂仓',
      materialType: 'sand',
      materialName: '中砂',
      specification: '2.3-3.0',
      capacity: 300,
      currentAmount: 250,
      status: 'normal',
      lastUpdated: '2024-12-28 14:05',
    },
    {
      id: 'silo-5',
      name: '2#砂仓',
      materialType: 'sand',
      materialName: '细砂',
      specification: '1.6-2.3',
      capacity: 300,
      currentAmount: 45,
      status: 'critical',
      lastUpdated: '2024-12-28 13:50',
    },
    {
      id: 'silo-6',
      name: '1#石仓',
      materialType: 'stone',
      materialName: '碎石',
      specification: '5-25mm',
      capacity: 400,
      currentAmount: 320,
      status: 'normal',
      lastUpdated: '2024-12-28 14:00',
    },
    {
      id: 'silo-7',
      name: '水箱',
      materialType: 'water',
      materialName: '生产用水',
      specification: '自来水',
      capacity: 50,
      currentAmount: 45,
      status: 'normal',
      lastUpdated: '2024-12-28 14:25',
    },
    {
      id: 'silo-8',
      name: '外加剂罐',
      materialType: 'additive',
      materialName: '减水剂',
      specification: 'PC-1',
      capacity: 20,
      currentAmount: 15,
      status: 'normal',
      lastUpdated: '2024-12-28 14:20',
    },
  ];

  const filteredSilos =
    filter === 'all' ? siloData : siloData.filter(silo => silo.materialType === filter);

  // 右键菜单处理函数
  const handleSiloContextMenu = (silo: SiloData, position: { x: number; y: number }) => {
    setContextMenu({
      isOpen: true,
      position,
      silo,
    });
  };

  const closeContextMenu = () => {
    setContextMenu({
      isOpen: false,
      position: null,
      silo: null,
    });
  };

  // 右键菜单回调函数
  const handleSendToRatioPanel = (silo: SiloData) => {
    toast({
      title: '发送到配比面板',
      description: `已将 ${silo.materialName} 发送到配比面板`,
    });
    closeContextMenu();
  };

  const handleEditSilo = (silo: SiloData) => {
    toast({
      title: '编辑料仓信息',
      description: `编辑 ${silo.name} 的信息`,
    });
    closeContextMenu();
  };

  const handleDeleteSilo = (siloId: string) => {
    const silo = siloData.find(s => s.id === siloId);
    deleteSilo(siloId);
    toast({
      title: '删除料仓',
      description: `已删除料仓 ${silo?.name}`,
      variant: 'destructive',
    });
    closeContextMenu();
  };

  const handleViewSiloInfo = (silo: SiloData) => {
    toast({
      title: '查看料仓信息',
      description: `查看 ${silo.name} 的详细信息`,
    });
    closeContextMenu();
  };

  const handleModifySiloStatus = (silo: SiloData) => {
    toast({
      title: '修改料仓状态',
      description: `修改 ${silo.name} 的状态`,
    });
    closeContextMenu();
  };

  return (
    <Card className='h-full flex flex-col shadow-sm'>
      <CardHeader className='pb-2 pt-2'>
        <div className='flex items-center justify-between'>
          <CardTitle className='text-sm flex items-center gap-2 font-medium'>
            <Container className='h-4 w-4 text-primary' />
            物料仓库
            <Badge variant='outline' className='text-xs ml-1'>
              {filteredSilos.length}/{siloData.length}
            </Badge>
          </CardTitle>

          <div className='flex items-center gap-1'>
            <Button
              variant={showDetails ? 'default' : 'ghost'}
              size='sm'
              onClick={() => setShowDetails(!showDetails)}
              className='h-6 px-2 text-xs'
            >
              <Eye className='h-3 w-3' />
            </Button>

            <Button variant='ghost' size='sm' className='h-6 px-2'>
              <Settings className='h-3 w-3' />
            </Button>
          </div>
        </div>

        {/* 紧凑筛选器 */}
        <div className='flex flex-wrap gap-1 mt-2'>
          {[
            { value: 'all', label: '全部', icon: Container },
            { value: 'cement', label: '水泥', icon: Container },
            { value: 'water', label: '水', icon: Droplets },
            { value: 'sand', label: '砂', icon: Mountain },
            { value: 'stone', label: '石', icon: Mountain },
            { value: 'additive', label: '外加剂', icon: TestTube2 },
            { value: 'powder', label: '粉料', icon: Container },
          ].map(({ value, label, icon: Icon }) => (
            <Button
              key={value}
              variant={filter === value ? 'default' : 'ghost'}
              size='sm'
              onClick={() => setFilter(value as any)}
              className='text-xs h-6 px-2 gap-1'
            >
              <Icon className='h-3 w-3' />
              {label}
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent className='flex-1 overflow-y-auto p-1'>
        {/* 物料仓网格 - 更紧凑的布局 */}
        <div className='grid grid-cols-2 gap-1'>
          {filteredSilos.map(silo => (
            <DraggableSilo key={silo.id} silo={silo} onContextMenu={handleSiloContextMenu} />
          ))}
        </div>

        {/* 空状态提示 */}
        {filteredSilos.length === 0 && (
          <div className='text-center py-8 text-muted-foreground'>
            <Container className='h-8 w-8 mx-auto mb-2 opacity-50' />
            <p className='text-sm'>暂无符合条件的物料仓</p>
          </div>
        )}

        {/* 拖拽提示 - 更紧凑 */}
        <div className='mt-2 p-2 bg-blue-50 dark:bg-blue-950/20 rounded border border-blue-200 dark:border-blue-800'>
          <p className='text-xs text-blue-700 dark:text-blue-300 text-center'>
            💡 拖拽物料仓到配比设计面板
          </p>
        </div>

        {/* 统计信息 */}
        <div className='mt-2 p-2 bg-gray-50 dark:bg-gray-950/20 rounded border'>
          <div className='grid grid-cols-2 gap-2 text-xs'>
            <div className='text-center'>
              <div className='font-mono font-bold text-green-600'>
                {siloData.filter(s => s.status === 'normal').length}
              </div>
              <div className='text-muted-foreground'>正常</div>
            </div>
            <div className='text-center'>
              <div className='font-mono font-bold text-yellow-600'>
                {siloData.filter(s => s.status === 'warning').length}
              </div>
              <div className='text-muted-foreground'>警告</div>
            </div>
          </div>
        </div>
      </CardContent>

      {/* 右键菜单 */}
      <SiloContextMenu
        isOpen={contextMenu.isOpen}
        position={contextMenu.position}
        silo={contextMenu.silo}
        onClose={closeContextMenu}
        onSendToRatioPanel={handleSendToRatioPanel}
        onEditSilo={handleEditSilo}
        onDeleteSilo={handleDeleteSilo}
        onViewSiloInfo={handleViewSiloInfo}
        onModifySiloStatus={handleModifySiloStatus}
      />
    </Card>
  );
}
