'use client';

import { useMemo } from 'react';

import { useQuery } from '@tanstack/react-query';

import { getStaticPlants } from '@/shared/services/dataService';
import { useUiStore } from '@/infrastructure/storage/stores/uiStore';
import type { Plant } from '@/core/types';

export function useCurrentPlantInfo() {
  const {
    data: plants = [],
    isLoading: isLoadingPlants,
    error: plantsError,
  } = useQuery<Plant[]>({
    queryKey: ['plants'], // This key should match the one used in MainLayout
    queryFn: getStaticPlants,
    staleTime: Infinity, // Data is static, no need to refetch often
  });

  const selectedPlantId = useUiStore(state => state.selectedPlantId);

  const currentPlant = useMemo(() => {
    return plants.find(p => p.id === selectedPlantId);
  }, [plants, selectedPlantId]);

  const productionLineCount = useMemo(() => {
    return currentPlant?.productionLineCount || 0;
  }, [currentPlant]);

  return {
    plants, // Exposing all plants might be useful for other components using this hook
    currentPlant,
    productionLineCount,
    isLoadingPlants,
    plantsError,
  };
}
