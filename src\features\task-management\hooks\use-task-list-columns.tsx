// src/components/sections/task-list/hooks/use-task-list-columns.ts
import React from 'react';
import { useCallback, useMemo } from 'react';

import type { CellContext, ColumnDef } from '@tanstack/react-table';

import { allTextColorOptionsMap } from '@/models/column-specific-style-modal';
import { cn } from '@/core/lib/utils';
import { useReminderStore } from '@/features/task-management/store/reminderStore';
import type {
  DensityStyleValues,
  StyleableColumnId,
  Task,
  TaskListDensityMode,
  Vehicle
} from '@/core/types';

import { DispatchedVehiclesCell } from '../components/cells/DispatchedVehiclesCell';
// Cell components
import { DispatchReminderCell } from '../components/cells/DispatchReminderCell';
import { MessageCell } from '../components/cells/MessageCell';
import { ProductionLinesCell } from '../components/cells/ProductionLinesCell';
import { TaskProgressCell } from '../components/cells/TaskProgressCell';
import { getStyleableColumnId } from '../components/task-list-type-guards';
import { ALL_TASK_COLUMNS_CONFIG } from '../components/task-list.config';

// 日期时间格式化函数
function formatDateTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}`;
  } catch {
    return dateString;
  }
}

interface TaskListSettings {
  columnWidths: Record<string, number>;
  columnTextStyles: Record<string, {
    color?: string;
    textAlign?: string;
    fontSize?: string;
    fontWeight?: string;
  }>;
  columnBackgrounds: Record<string, any>;
  density: TaskListDensityMode;
}

interface UseTaskListColumnsProps {
  densityStyles: DensityStyleValues;
  vehicleDisplayMode: 'licensePlate' | 'internalId';
  inTaskVehicleCardStyles: any;
  productionLineCount: number;
  onDropOnProductionLine: (
    vehicle: Vehicle,
    taskId: string,
    lineId: string,
    sourceTaskId?: string
  ) => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onOpenStyleEditor: () => void;
  onOpenDeliveryOrderDetails: (vehicle: Vehicle, task: Task) => void;
  onOpenVehicleCardContextMenu: (e: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  getColumnBackgroundProps: (
    columnId: string,
    isHeader: boolean,
    isFixed: boolean
  ) => { style: React.CSSProperties; className: string };
  settings: TaskListSettings; // 添加settings参数
}

/**
 * 任务列表表格列配置Hook
 * 负责生成和管理表格列的配置
 */
export function useTaskListColumns({
  densityStyles,
  vehicleDisplayMode,
  inTaskVehicleCardStyles,
  productionLineCount,
  onDropOnProductionLine,
  onCancelVehicleDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenVehicleCardContextMenu,
  getColumnBackgroundProps,
  settings,
}: UseTaskListColumnsProps) {
  const { messages, markAsRead } = useReminderStore();

  // 获取单元格文本样式
  const getCellTextClasses = useCallback(
    (columnId: StyleableColumnId): string => {
      const styles = settings.columnTextStyles[columnId];
      const classes: string[] = [];

      if (styles?.color && styles.color !== 'default') {
        const colorOption = allTextColorOptionsMap.get(styles.color);
        const colorClass = colorOption ? colorOption.className : 'text-foreground';
        classes.push(colorClass);
      } else {
        classes.push('text-foreground');
      }

      if (styles?.textAlign && styles.textAlign !== 'default') {
        classes.push(styles.textAlign);
      } else {
        classes.push('text-left');
      }

      if (styles?.fontSize && styles.fontSize !== 'default') {
        classes.push(styles.fontSize);
      } else {
        classes.push(densityStyles.cellFontSize);
      }

      if (styles?.fontWeight && styles.fontWeight !== 'default') {
        classes.push(styles.fontWeight);
      } else {
        classes.push(densityStyles.cellFontWeight);
      }

      const result = cn(classes);
      return result;
    },
    [settings.columnTextStyles, densityStyles]
  );

  // Memoize column metadata separately to reduce re-renders
  const columnMeta = useMemo(
    () => ({
      getColumnBackgroundProps,
      densityStyles,
    }),
    [getColumnBackgroundProps, densityStyles]
  );

  // Memoize cell renderers with optimized dependencies
  const cellRenderers = useMemo(
    () => ({
      dispatchReminder: (task: Task, columnId: StyleableColumnId) => (
        <DispatchReminderCell
          key={`reminder-${task.id}`}
          task={task}
          textClassName={getCellTextClasses(columnId)}
        />
      ),
      messages: (task: Task, columnId: StyleableColumnId) => {
        const taskMessages = messages.filter(msg => msg.taskId === task.id);
        const unreadCount = taskMessages.filter(msg => !msg.read).length;
        return (
          <MessageCell
            key={`messages-${task.id}`}
            task={task}
            taskMessages={taskMessages}
            unreadCount={unreadCount}
            onMarkAsRead={markAsRead}
            textClassName={getCellTextClasses(columnId)}
          />
        );
      },
      completedProgress: (task: Task, columnId: StyleableColumnId) => {
        const progressValue =
          task.requiredVolume > 0 ? (task.completedVolume / task.requiredVolume) * 100 : 0;
        return (
          <TaskProgressCell
            key={`progress-${task.id}`}
            progressValue={progressValue}
            textClassName={getCellTextClasses(columnId)}
          />
        );
      },
      dispatchedVehicles: (task: Task) => {
        // 直接从任务对象获取车辆列表
        const vehicles = (task as any).vehicles || [];
        return (
          <DispatchedVehiclesCell
            key={`vehicles-${task.id}`}
            task={task}
            taskVehicles={vehicles}
            vehicleDisplayMode={vehicleDisplayMode}
            inTaskVehicleCardStyles={inTaskVehicleCardStyles}
            productionLineCount={productionLineCount}
            density={settings.density}
            onCancelDispatch={onCancelVehicleDispatch}
            onOpenStyleEditor={onOpenStyleEditor}
            onOpenDeliveryOrderDetails={(vehicle, currentTask) =>
              onOpenDeliveryOrderDetails(vehicle, currentTask)
            }
            onOpenContextMenu={(e, vehicle, currentTask) =>
              onOpenVehicleCardContextMenu(e, vehicle, currentTask)
            }
          />
        );
      },
      productionLines: (task: Task) => (
        <ProductionLinesCell
          key={`productionLines-${task.id}`}
          task={task}
          productionLineCount={productionLineCount}
          densityStyles={densityStyles}
          onDropVehicleOnLine={onDropOnProductionLine}
        />
      ),
      supplyTime: (task: Task, columnId: StyleableColumnId) => {
        const combinedSupplyTime = `${task.supplyDate || ''} ${task.supplyTime || ''}`.trim();
        return (
          <span
            key={`supplyTime-${task.id}`}
            className={cn('truncate block w-full h-full', getCellTextClasses(columnId))}
            title={combinedSupplyTime}
          >
            {combinedSupplyTime}
          </span>
        );
      },
      default: (value: any, columnId: StyleableColumnId) => (
        <span className={cn('truncate block w-full h-full', getCellTextClasses(columnId))}>
          {String(value ?? '')}
        </span>
      ),
    }),
    [
      getCellTextClasses,
      messages,
      markAsRead,
      vehicleDisplayMode,
      inTaskVehicleCardStyles,
      productionLineCount,
      settings.density,
      densityStyles,
      onCancelVehicleDispatch,
      onOpenStyleEditor,
      onOpenDeliveryOrderDetails,
      onOpenVehicleCardContextMenu,
      onDropOnProductionLine,
      settings.columnTextStyles,
      settings.columnBackgrounds,
    ]
  );

  // 生成表格列配置
  const tableColumns = useMemo<ColumnDef<Task>[]>(() => {
    return Array.from(ALL_TASK_COLUMNS_CONFIG).map(colDef => {
      const baseCol: ColumnDef<Task> = {
        id: colDef.id,
        accessorKey: colDef.id,
        header: () => colDef.label,
        size: settings.columnWidths[colDef.id] || colDef.defaultWidth || 150,
        minSize: colDef.id === 'dispatchedVehicles' ? 200 : colDef.minWidth || 50,
        maxSize: colDef.id === 'dispatchedVehicles' ? 600 : colDef.maxWidth,
        enableResizing: colDef.isResizable !== false,
        enableHiding: !colDef.isMandatory,
        meta: { customDef: { ...colDef, ...columnMeta } },
        cell: (info: CellContext<Task, any>) => {
          const task = info.row.original;
          const columnId = getStyleableColumnId(info.column.id);

          switch (columnId) {
            case 'dispatchReminder':
              return cellRenderers.dispatchReminder(task, columnId);
            case 'messages':
              return cellRenderers.messages(task, columnId);
            case 'completedProgress':
              return cellRenderers.completedProgress(task, columnId);
            case 'dispatchedVehicles':
              return cellRenderers.dispatchedVehicles(task);
            case 'productionLines':
              return cellRenderers.productionLines(task);
            case 'supplyTime':
              return cellRenderers.supplyTime(task, columnId);
            case 'publishDate':
            case 'timing':
              // 日期字段特殊处理
              const dateValue = (task as any)[info.column.id];
              const formattedDate = dateValue ? formatDateTime(dateValue) : '';
              return (
                <span className={cn('truncate block w-full h-full', getCellTextClasses(columnId))}>
                  {formattedDate}
                </span>
              );
            default:
              // 处理其他字段，包括日期字段
              const value = (task as any)[info.column.id];

              // 特殊处理日期字段
              if (['createdAt', 'updatedAt', 'scheduledTime'].includes(info.column.id)) {
                const formattedDate = value ? formatDateTime(value) : '';
                return (
                  <span
                    className={cn('truncate block w-full h-full', getCellTextClasses(columnId))}
                  >
                    {formattedDate}
                  </span>
                );
              }

              return cellRenderers.default(value, columnId);
          }
        },
      };
      return { ...baseCol }; // 确保返回新对象
    });
  }, [
    settings.columnWidths, // Affects column sizes
    settings.columnTextStyles, // Affects text styling
    settings.columnBackgrounds, // Affects background styling
    columnMeta, // Affects meta data
    cellRenderers, // Affects cell rendering
    getCellTextClasses, // Affects text styling for date fields
  ]);

  return {
    tableColumns,
    getCellTextClasses,
    cellRenderers,
  };
}
