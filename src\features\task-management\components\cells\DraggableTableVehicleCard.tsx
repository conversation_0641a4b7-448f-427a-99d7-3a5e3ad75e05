'use client';

import React, { useRef } from 'react';
import { useDrag } from 'react-dnd';
import { ItemTypes } from '@/core/constants/dndItemTypes';
import type { InTaskVehicleCardStyle, Task, Vehicle, VehicleDisplayMode } from '@/core/types';
import { InTaskVehicleCard } from '../in-task-vehicle-card';

interface DraggableTableVehicleCardProps {
  vehicle: Vehicle;
  task: Task;
  index: number;
  vehicleDisplayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  productionLineCount: number;
  density?: 'compact' | 'normal' | 'loose';
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: () => void;
  onOpenContextMenu?: (e: React.MouseEvent, vehicle: Vehicle, task?: Task) => void;
}

/**
 * 可拖拽的表格模式车辆卡片组件
 * 用于表格模式下的车辆转发功能
 */
export const DraggableTableVehicleCard: React.FC<DraggableTableVehicleCardProps> = ({
  vehicle,
  task,
  index,
  vehicleDisplayMode,
  inTaskVehicleCardStyles,
  productionLineCount,
  density = 'normal',
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenContextMenu,
}) => {
  const vehicleRef = useRef<HTMLDivElement>(null);

  // 使用React DnD的拖拽功能
  const [{ isDragging }, dragSource] = useDrag({
    type: ItemTypes.VEHICLE_CARD_DISPATCH,
    item: {
      vehicle,
      type: ItemTypes.VEHICLE_CARD_DISPATCH,
      sourceTaskId: task.id,
      isVehicleTransfer: true, // 标记这是车辆转发操作
      index,
      statusList: 'dispatched', // 表示这是已调度的车辆
    },
    canDrag: () => true, // 已调度车辆可以拖拽进行转发
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  });

  // 连接拖拽ref
  dragSource(vehicleRef);

  return (
    <div ref={vehicleRef} style={{ opacity: isDragging ? 0.5 : 1 }}>
      <InTaskVehicleCard
        vehicle={vehicle}
        task={task}
        vehicleDisplayMode={vehicleDisplayMode}
        inTaskVehicleCardStyles={inTaskVehicleCardStyles}
        productionLineCount={productionLineCount}
        onCancelDispatch={onCancelDispatch}
        onOpenStyleEditor={onOpenStyleEditor}
        onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetails}
        onOpenContextMenu={onOpenContextMenu}
        density={density}
        isDispatchPanelView={false}
        isTableCellView={true}
        isDragging={isDragging}
      />
    </div>
  );
};
