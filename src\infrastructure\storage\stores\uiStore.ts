// src/store/uiStore.ts
import { createWithEqualityFn } from 'zustand/traditional';
import { subscribeWithSelector } from 'zustand/middleware';

import { taskStatusOptions as defaultTaskStatusOptions } from '@/infrastructure/api/mock/mock-data';
import { PersistenceManager } from '@/infrastructure/storage/persistence/persistenceManager';
import type { TaskListDisplayMode, TaskStatusFilter, VehicleDisplayMode } from '@/core/types';

const ALERT_SOUND_THROTTLE_MS = 30000; // 30 seconds

// 持久化的UI状态接口
interface PersistentUiState {
  selectedPlantId: string | null;
  taskStatusFilter: TaskStatusFilter;
  vehicleDisplayMode: VehicleDisplayMode;
  vehicleListDisplayMode: TaskListDisplayMode;
}

// 完整的UI状态接口
interface UiState extends PersistentUiState {
  setSelectedPlantId: (plantId: string | null) => void;
  setTaskStatusFilter: (status: TaskStatusFilter) => void;
  setVehicleDisplayMode: (mode: VehicleDisplayMode) => void;
  setVehicleListDisplayMode: (mode: TaskListDisplayMode) => void;

  shouldPlayAlertSound: number; // Counter
  lastAlertSoundTime: number;
  incrementAlertSoundCounter: () => void;
  resetAlertSoundThrottle: () => void; // For testing or specific reset scenarios

  // 持久化相关方法
  loadPersistedState: () => void;
  savePersistedState: () => void;
}

// 默认的持久化状态
const defaultPersistentState: PersistentUiState = {
  selectedPlantId: null,
  taskStatusFilter:
    (defaultTaskStatusOptions?.find?.(opt => opt.label === '正在进行')
      ?.value as TaskStatusFilter) || 'InProgress',
  vehicleDisplayMode: 'licensePlate',
  vehicleListDisplayMode: 'table',
};

export const useUiStore = createWithEqualityFn<UiState>()(
  subscribeWithSelector((set, get) => {
    // 加载持久化状态
    const loadPersistedState = () => {
      try {
        const persistedState = PersistenceManager.load('UI_PREFERENCES', defaultPersistentState);
        console.log('✅ UI状态已从持久化存储加载:', persistedState);
        return persistedState;
      } catch (error) {
        console.error('❌ 加载UI状态失败:', error);
        return defaultPersistentState;
      }
    };

    // 保存持久化状态
    const savePersistedState = () => {
      try {
        const currentState = get();
        const stateToSave: PersistentUiState = {
          selectedPlantId: currentState.selectedPlantId,
          taskStatusFilter: currentState.taskStatusFilter,
          vehicleDisplayMode: currentState.vehicleDisplayMode,
          vehicleListDisplayMode: currentState.vehicleListDisplayMode,
        };
        PersistenceManager.save('UI_PREFERENCES', stateToSave);
        console.log('✅ UI状态已保存到持久化存储:', stateToSave);
      } catch (error) {
        console.error('❌ 保存UI状态失败:', error);
      }
    };

    // 初始化状态
    const initialState = loadPersistedState();

    return {
      // 持久化状态
      ...initialState,

      // 非持久化状态
      shouldPlayAlertSound: 0,
      lastAlertSoundTime: 0,

      // 状态更新方法
      setSelectedPlantId: (plantId: string | null) => {
        set({ selectedPlantId: plantId });
        // 延迟保存以避免频繁写入
        setTimeout(savePersistedState, 100);
      },

      setTaskStatusFilter: (status: TaskStatusFilter) => {
        set({ taskStatusFilter: status });
        setTimeout(savePersistedState, 100);
      },

      setVehicleDisplayMode: (mode: VehicleDisplayMode) => {
        set({ vehicleDisplayMode: mode });
        setTimeout(savePersistedState, 100);
      },

      setVehicleListDisplayMode: (mode: TaskListDisplayMode) => {
        set({ vehicleListDisplayMode: mode });
        setTimeout(savePersistedState, 100);
      },

      incrementAlertSoundCounter: () => {
        if (Date.now() - get().lastAlertSoundTime > ALERT_SOUND_THROTTLE_MS) {
          set(state => ({
            shouldPlayAlertSound: state.shouldPlayAlertSound + 1,
            lastAlertSoundTime: Date.now(),
          }));
        }
      },

      resetAlertSoundThrottle: () => {
        set({ lastAlertSoundTime: 0 });
      },

      // 持久化方法
      loadPersistedState,
      savePersistedState,
    };
  }),
  Object.is
);

// This function will be called from appStore (data store) after initial plant data is fetched
export const initializeUiStoreWithPlantData = (
  initialPlantId: string | null,
  initialTaskStatusFilter?: TaskStatusFilter
) => {
  useUiStore.setState({
    selectedPlantId: initialPlantId,
    taskStatusFilter:
      initialTaskStatusFilter ||
      (defaultTaskStatusOptions?.find?.(opt => opt.label === '正在进行')
        ?.value as TaskStatusFilter) ||
      'InProgress',
  });
};
