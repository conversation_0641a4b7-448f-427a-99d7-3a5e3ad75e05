/**
 * BaseRatioPage - 配比页面基础组件
 * 提取三个版本页面的共同结构和逻辑
 */

import React, { useEffect, useState, useCallback, ReactNode } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useToast } from '@/shared/hooks/use-toast';
import { LoadingSpinner } from '@/shared/components/LoadingSpinner';
import { ErrorAlert } from '@/shared/components/ErrorAlert';
import { generateMockTasks } from '@/infrastructure/api/mock/mock-data';
import type { Task } from '@/core/types';

// 统一模态框管理
import { RatioModalManager } from './RatioModalManager';

/**
 * 基础页面配置接口
 */
export interface BaseRatioPageConfig {
  version: 'v1' | 'v2';
  title: string;
  enableAutoSave?: boolean;
  autoSaveDelay?: number;
  enableBeforeUnloadWarning?: boolean;
  enableDragDrop?: boolean;
  layoutMode?: 'classic' | 'modern' | 'grid';
}

/**
 * 页面状态接口
 */
export interface BaseRatioPageState {
  task: Task | null;
  isInitializing: boolean;
  error: string | null;
  modals: any;
}

/**
 * 页面回调接口
 */
export interface BaseRatioPageCallbacks {
  onTaskLoad?: (task: Task) => void;
  onTaskLoadError?: (error: Error) => void;
  onModalOpen?: (modalName: string) => void;
  onModalClose?: (modalName: string) => void;
  onBeforeUnload?: () => boolean; // 返回true表示有未保存更改
  onSave?: () => Promise<void>;
  onNavigateAway?: (targetPath: string) => void;
}

/**
 * 基础页面属性
 */
export interface BaseRatioPageProps {
  config: BaseRatioPageConfig;
  callbacks?: BaseRatioPageCallbacks;
  children: (props: BaseRatioPageRenderProps) => ReactNode;
}

/**
 * 渲染属性接口
 */
export interface BaseRatioPageRenderProps {
  // 基础状态
  task: Task | null;
  isInitializing: boolean;
  error: string | null;

  // 模态框管理
  modals: any;
  openModal: (modalName: string) => void;
  closeModal: (modalName: string) => void;
  closeAllModals: () => void;

  // 导航方法
  navigateToVersion: (version: 'v1' | 'v2') => void;

  // 工具方法
  showToast: ReturnType<typeof useToast>['toast'];
  taskId: string;
}

/**
 * BaseRatioPage 组件
 *
 * 功能：
 * 1. 统一的任务加载逻辑
 * 2. 统一的模态框管理
 * 3. 统一的页面生命周期管理
 * 4. 统一的错误处理
 * 5. 统一的导航逻辑
 */
export function BaseRatioPage({ config, callbacks = {}, children }: BaseRatioPageProps) {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const taskId = params?.['taskId'] as string;

  // 页面状态
  const [state, setState] = useState<BaseRatioPageState>({
    task: null,
    isInitializing: true,
    error: null,
    modals: {},
  });

  // ==================== 任务加载逻辑 ====================

  const loadTask = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isInitializing: true, error: null }));

      // 模拟任务加载（实际应该从API获取）
      const tasks = generateMockTasks();
      const task = tasks.find(t => t.id === taskId) || tasks[2];

      if (task) {
        setState(prev => ({ ...prev, task, isInitializing: false }));

        // 触发回调
        callbacks.onTaskLoad?.(task);

        // 显示加载成功消息
        toast({
          title: `${config.title}已加载`,
          description: `任务 ${task.taskNumber} 加载完成`,
        });
      } else {
        throw new Error('任务未找到');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isInitializing: false,
      }));

      callbacks.onTaskLoadError?.(error as Error);

      toast({
        title: '加载失败',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  }, [taskId, config.title, callbacks, toast]);

  // 初始化任务
  useEffect(() => {
    if (taskId) {
      loadTask();
    }
  }, [taskId, loadTask]);

  // ==================== 页面生命周期管理 ====================

  // 页面卸载前警告
  useEffect(() => {
    if (!config.enableBeforeUnloadWarning) return;

    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      const hasUnsavedChanges = callbacks.onBeforeUnload?.() || false;
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '您有未保存的更改，确定要离开吗？';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [config.enableBeforeUnloadWarning, callbacks]);

  // ==================== 模态框管理 ====================

  const openModal = useCallback(
    (modalName: string) => {
      setState(prev => ({
        ...prev,
        modals: { ...prev.modals, [modalName]: true },
      }));
      callbacks.onModalOpen?.(modalName);
    },
    [callbacks]
  );

  const closeModal = useCallback(
    (modalName: string) => {
      setState(prev => ({
        ...prev,
        modals: { ...prev.modals, [modalName]: false },
      }));
      callbacks.onModalClose?.(modalName);
    },
    [callbacks]
  );

  const closeAllModals = useCallback(() => {
    setState(prev => ({
      ...prev,
      modals: {},
    }));
  }, []);

  // ==================== 导航方法 ====================

  const navigateToVersion = useCallback(
    (version: 'v1' | 'v2') => {
      const basePath = version === 'v1' ? '/ratio' : `/ratio-${version}`;
      const targetPath = `${basePath}/${taskId}`;

      callbacks.onNavigateAway?.(targetPath);
      router.push(targetPath);
    },
    [taskId, router, callbacks]
  );

  // ==================== 渲染逻辑 ====================

  // 加载中状态
  if (state.isInitializing) {
    return (
      <div className='h-screen w-screen flex items-center justify-center bg-muted/20'>
        <div className='text-center space-y-4'>
          <LoadingSpinner size='large' />
          <p className='text-muted-foreground'>正在加载{config.title}...</p>
        </div>
      </div>
    );
  }

  // 错误状态
  if (state.error) {
    return (
      <div className='h-screen w-screen flex items-center justify-center bg-muted/20 p-4'>
        <ErrorAlert title='页面加载失败' message={state.error} onRetry={loadTask} />
      </div>
    );
  }

  // 正常渲染
  const renderProps: BaseRatioPageRenderProps = {
    // 基础状态
    task: state.task,
    isInitializing: state.isInitializing,
    error: state.error,

    // 模态框管理
    modals: state.modals,
    openModal,
    closeModal,
    closeAllModals,

    // 导航方法
    navigateToVersion,

    // 工具方法
    showToast: toast,
    taskId,
  };

  return (
    <>
      {children(renderProps)}

      {/* 统一模态框管理器 */}
      <RatioModalManager
        modals={state.modals}
        task={state.task}
        taskId={taskId}
        onClose={closeModal}
        version={config.version}
      />
    </>
  );
}

/**
 * 创建特定版本的BaseRatioPage
 */
export function createVersionedRatioPage(config: BaseRatioPageConfig) {
  return function VersionedRatioPage(props: Omit<BaseRatioPageProps, 'config'>) {
    return <BaseRatioPage {...props} config={config} />;
  };
}

/**
 * 预定义的版本配置
 */
export const V1_CONFIG: BaseRatioPageConfig = {
  version: 'v1',
  title: '砼配比',
  enableAutoSave: false,
  enableBeforeUnloadWarning: true,
  enableDragDrop: false,
  layoutMode: 'classic',
};

export const V2_CONFIG: BaseRatioPageConfig = {
  version: 'v2',
  title: '现代配比设计',
  enableAutoSave: true,
  autoSaveDelay: 3000,
  enableBeforeUnloadWarning: true,
  enableDragDrop: true,
  layoutMode: 'modern',
};

// 导出预配置的组件
export const V1RatioPage = createVersionedRatioPage(V1_CONFIG);
export const V2RatioPage = createVersionedRatioPage(V2_CONFIG);
