'use client';

/**
 * 懒加载模态框管理器
 * 实现模态框的按需加载，减少初始包体积
 */

import React, { Suspense, lazy, useState, useCallback } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

// ==================== 模态框懒加载定义 ====================

// 调度相关模态框
const TaskEditModal = lazy(() => import('./TaskEditModal'));
const TankTruckDispatchModal = lazy(() => import('./TankTruckDispatchModal'));
const PumpTruckDispatchModal = lazy(() => import('./PumpTruckDispatchModal'));
const TaskProgressModal = lazy(() => import('./TaskProgressModal'));
const VehicleManagementModal = lazy(() => import('./VehicleManagementModal'));

// 配比相关模态框
const SiloManagementModal = lazy(() => import('./SiloManagementModal'));
const RatioHistoryModal = lazy(() => import('./RatioHistoryModal'));
const MortarRatioModal = lazy(() => import('./MortarRatioModal'));
const RatioSettingsModal = lazy(() => import('./RatioSettingsModal'));
const RatioSelectionModal = lazy(() => import('./RatioSelectionModal'));
const RatioBackupModal = lazy(() => import('./RatioBackupModal'));

// 通用模态框
const ConfirmationModal = lazy(() => import('./ConfirmationModal'));
const NotificationModal = lazy(() => import('./NotificationModal'));
const PerformanceModal = lazy(() => import('./PerformanceModal'));

// ==================== 模态框类型定义 ====================

export type ModalType =
  // 调度相关
  | 'taskEdit'
  | 'tankTruckDispatch'
  | 'pumpTruckDispatch'
  | 'taskProgress'
  | 'vehicleManagement'
  // 配比相关
  | 'siloManagement'
  | 'ratioHistory'
  | 'mortarRatio'
  | 'ratioSettings'
  | 'ratioSelection'
  | 'ratioBackup'
  // 通用
  | 'confirmation'
  | 'notification'
  | 'performance';

export interface ModalConfig {
  type: ModalType;
  props?: Record<string, any>;
  onClose?: () => void;
}

export interface LazyModalManagerProps {
  children?: React.ReactNode;
}

// ==================== 模态框映射 ====================

const MODAL_COMPONENTS = {
  // 调度相关
  taskEdit: TaskEditModal,
  tankTruckDispatch: TankTruckDispatchModal,
  pumpTruckDispatch: PumpTruckDispatchModal,
  taskProgress: TaskProgressModal,
  vehicleManagement: VehicleManagementModal,

  // 配比相关
  siloManagement: SiloManagementModal,
  ratioHistory: RatioHistoryModal,
  mortarRatio: MortarRatioModal,
  ratioSettings: RatioSettingsModal,
  ratioSelection: RatioSelectionModal,
  ratioBackup: RatioBackupModal,

  // 通用
  confirmation: ConfirmationModal,
  notification: NotificationModal,
  performance: PerformanceModal,
} as const;

// ==================== 加载状态组件 ====================

const ModalLoadingSkeleton: React.FC<{ type: ModalType }> = ({ type }) => (
  <div className='fixed inset-0 z-50 flex items-center justify-center bg-black/50'>
    <div className='bg-white rounded-lg p-6 max-w-md w-full mx-4'>
      <div className='animate-pulse'>
        <div className='h-6 bg-gray-200 rounded mb-4'></div>
        <div className='space-y-3'>
          <div className='h-4 bg-gray-200 rounded w-3/4'></div>
          <div className='h-4 bg-gray-200 rounded w-1/2'></div>
          <div className='h-4 bg-gray-200 rounded w-5/6'></div>
        </div>
        <div className='flex justify-end space-x-2 mt-6'>
          <div className='h-8 bg-gray-200 rounded w-16'></div>
          <div className='h-8 bg-gray-200 rounded w-16'></div>
        </div>
      </div>
      <div className='text-xs text-gray-500 mt-2 text-center'>正在加载 {type} 模态框...</div>
    </div>
  </div>
);

// ==================== 错误回退组件 ====================

const ModalErrorFallback: React.FC<{
  error: Error;
  resetErrorBoundary: () => void;
  modalType: ModalType;
}> = ({ error, resetErrorBoundary, modalType }) => (
  <div className='fixed inset-0 z-50 flex items-center justify-center bg-black/50'>
    <div className='bg-white rounded-lg p-6 max-w-md w-full mx-4'>
      <div className='text-red-600 mb-4'>
        <h3 className='text-lg font-semibold'>模态框加载失败</h3>
        <p className='text-sm mt-2'>无法加载 {modalType} 模态框</p>
      </div>
      <div className='bg-gray-100 p-3 rounded text-xs text-gray-600 mb-4'>
        错误信息: {error.message}
      </div>
      <div className='flex justify-end space-x-2'>
        <button
          onClick={resetErrorBoundary}
          className='px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
        >
          重试
        </button>
        <button
          onClick={() => window.location.reload()}
          className='px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600'
        >
          刷新页面
        </button>
      </div>
    </div>
  </div>
);

// ==================== 主要组件 ====================

export const LazyModalManager: React.FC<LazyModalManagerProps> = ({ children }) => {
  const [openModals, setOpenModals] = useState<Map<string, ModalConfig>>(new Map());
  const [loadingModals, setLoadingModals] = useState<Set<string>>(new Set());

  // 打开模态框
  const openModal = useCallback((id: string, config: ModalConfig) => {
    setLoadingModals(prev => new Set(prev).add(id));

    // 预加载模态框组件
    const ModalComponent = MODAL_COMPONENTS[config.type];
    // 触发懒加载
    Promise.resolve().then(() => {
      setOpenModals(prev => new Map(prev).set(id, config));
      setLoadingModals(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    });
  }, []);

  // 关闭模态框
  const closeModal = useCallback((id: string) => {
    setOpenModals(prev => {
      const newMap = new Map(prev);
      newMap.delete(id);
      return newMap;
    });

    setLoadingModals(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  }, []);

  // 关闭所有模态框
  const closeAllModals = useCallback(() => {
    setOpenModals(new Map());
    setLoadingModals(new Set());
  }, []);

  // 渲染模态框
  const renderModal = useCallback(
    (id: string, config: ModalConfig) => {
      const ModalComponent = MODAL_COMPONENTS[config.type];

      if (!ModalComponent) {
        console.error(`Unknown modal type: ${config.type}`);
        return null;
      }

      return (
        <ErrorBoundary
          key={id}
          FallbackComponent={props => <ModalErrorFallback {...props} modalType={config.type} />}
          onReset={() => closeModal(id)}
        >
          <Suspense fallback={<ModalLoadingSkeleton type={config.type} />}>
            <ModalComponent
              {...config.props}
              open={true}
              isOpen={true}
              onOpenChange={(open: boolean) => {
                if (!open) {
                  closeModal(id);
                  config.onClose?.();
                }
              }}
              onOpenChangeAction={(open: boolean) => {
                if (!open) {
                  closeModal(id);
                  config.onClose?.();
                }
              }}
            />
          </Suspense>
        </ErrorBoundary>
      );
    },
    [closeModal]
  );

  // 提供给子组件的上下文
  const contextValue = {
    openModal,
    closeModal,
    closeAllModals,
    openModals: Array.from(openModals.keys()),
    loadingModals: Array.from(loadingModals),
  };

  return (
    <LazyModalContext.Provider value={contextValue}>
      {children}

      {/* 渲染加载中的模态框骨架 */}
      {Array.from(loadingModals).map(id => {
        const config = openModals.get(id);
        return config ? <ModalLoadingSkeleton key={`loading-${id}`} type={config.type} /> : null;
      })}

      {/* 渲染已加载的模态框 */}
      {Array.from(openModals.entries()).map(([id, config]) =>
        !loadingModals.has(id) ? renderModal(id, config) : null
      )}
    </LazyModalContext.Provider>
  );
};

// ==================== Context ====================

export const LazyModalContext = React.createContext<{
  openModal: (id: string, config: ModalConfig) => void;
  closeModal: (id: string) => void;
  closeAllModals: () => void;
  openModals: string[];
  loadingModals: string[];
} | null>(null);

// ==================== Hook ====================

export const useLazyModal = () => {
  const context = React.useContext(LazyModalContext);
  if (!context) {
    throw new Error('useLazyModal must be used within LazyModalManager');
  }
  return context;
};

// ==================== 便捷函数 ====================

export const useModalActions = () => {
  const { openModal, closeModal } = useLazyModal();

  return {
    // 调度相关模态框
    openTaskEdit: (props?: any) => openModal('task-edit', { type: 'taskEdit', props }),
    openTankTruckDispatch: (props?: any) =>
      openModal('tank-truck', { type: 'tankTruckDispatch', props }),
    openPumpTruckDispatch: (props?: any) =>
      openModal('pump-truck', { type: 'pumpTruckDispatch', props }),
    openTaskProgress: (props?: any) => openModal('task-progress', { type: 'taskProgress', props }),

    // 配比相关模态框
    openSiloManagement: (props?: any) => openModal('silo', { type: 'siloManagement', props }),
    openRatioHistory: (props?: any) => openModal('ratio-history', { type: 'ratioHistory', props }),
    openMortarRatio: (props?: any) => openModal('mortar-ratio', { type: 'mortarRatio', props }),
    openRatioSettings: (props?: any) =>
      openModal('ratio-settings', { type: 'ratioSettings', props }),

    // 通用模态框
    openConfirmation: (props?: any) => openModal('confirmation', { type: 'confirmation', props }),
    openNotification: (props?: any) => openModal('notification', { type: 'notification', props }),

    // 关闭模态框
    closeModal,
  };
};
