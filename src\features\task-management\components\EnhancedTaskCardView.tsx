'use client';

import React, { useCallback, useMemo, useState, Suspense, lazy } from 'react';

import { LayoutGrid } from 'lucide-react';

import { cn } from '@/core/lib/utils';
import type { Task, TaskListStoredSettings, Vehicle, VehicleDisplayMode } from '@/core/types';
import { defaultTaskCardConfig, TaskCardConfig } from '@/core/types/taskCardConfig';
import { globalCache } from '@/infrastructure/storage/cache/performance-cache';
import { SkeletonLoader } from '@/shared/components/enhanced-loading';

// 懒加载组件以提升LCP
const ConfigurableTaskCard = lazy(() =>
  import('./cards/ConfigurableTaskCard').then(module => ({
    default: module.ConfigurableTaskCard,
  }))
);

// const TaskCardPerformancePanel = lazy(() =>
//   import('./cards/TaskCardPerformancePanel').then(module => ({
//     default: module.TaskCardPerformancePanel,
//   }))
// );

import { useCardPerformance } from './cards/useCardPerformance';

interface CardConfig {
  size: 'small' | 'medium' | 'large' | 'extra-large';
  layout: 'compact' | 'standard' | 'detailed' | 'minimal';
  theme: 'default' | 'modern' | 'glass' | 'gradient' | 'dark';
  spacing: 'tight' | 'normal' | 'loose';
  borderRadius: 'none' | 'small' | 'medium' | 'large' | 'full';
  shadow: 'none' | 'small' | 'medium' | 'large' | 'glow';
  animation: 'none' | 'subtle' | 'smooth' | 'bouncy';
  columns: 'auto' | '1' | '2' | '3' | '4' | '5' | '6';
}

interface EnhancedTaskCardViewProps {
  filteredTasks: Task[];
  vehicles: Vehicle[];
  settings: TaskListStoredSettings;
  productionLineCount: number;
  vehicleDisplayMode: VehicleDisplayMode;
  taskStatusFilter: string;
  taskCardConfig?: TaskCardConfig;
  onCancelVehicleDispatchAction: (vehicleId: string) => void;
  onOpenDeliveryOrderDetailsForVehicleAction: (vehicleId: string, taskId: string) => void;
  onOpenVehicleCardContextMenuAction: (
    event: React.MouseEvent,
    vehicle: Vehicle,
    task: Task
  ) => void;
  onTaskContextMenuAction: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClickAction: (task: Task) => void;
  onOpenStyleEditorAction: () => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
}

export const EnhancedTaskCardView: React.FC<EnhancedTaskCardViewProps> = ({
  filteredTasks,
  vehicles,
  settings,
  productionLineCount,
  vehicleDisplayMode,
  taskStatusFilter,
  taskCardConfig = defaultTaskCardConfig,
  onCancelVehicleDispatchAction,
  onOpenDeliveryOrderDetailsForVehicleAction,
  onOpenVehicleCardContextMenuAction,
  onTaskContextMenuAction,
  onTaskDoubleClickAction,
  onOpenStyleEditorAction,
  onDropVehicleOnLine,
}) => {
  const [cardConfig, setCardConfig] = useState<CardConfig>({
    size: 'small',
    layout: 'standard',
    theme: 'default',
    spacing: 'normal',
    borderRadius: 'medium',
    shadow: 'medium',
    animation: 'smooth',
    columns: 'auto',
  });

  // TaskCardConfig is now passed as prop

  // Config modal is managed at higher level

  // 性能优化 Hook
  const { containerRef } = useCardPerformance({
    componentName: 'EnhancedTaskCardView',
    enableMonitoring: process.env.NODE_ENV === 'development',
  });

  // 获取网格列数 - 使用 useMemo 缓存计算结果
  const gridColumns = useMemo(() => {
    if (cardConfig.columns === 'auto') {
      switch (cardConfig.size) {
        case 'small':
          return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6';
        case 'medium':
          return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5';
        case 'large':
          return 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4';
        case 'extra-large':
          return 'grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3';
        default:
          return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
      }
    } else {
      return `grid-cols-${cardConfig.columns}`;
    }
  }, [cardConfig.size, cardConfig.columns]);

  // 获取间距样式 - 使用 useMemo 缓存
  const spacingClass = useMemo(() => {
    switch (cardConfig.spacing) {
      case 'tight':
        return 'gap-1.5 p-1.5';
      case 'loose':
        return 'gap-2 p-2';
      default:
        return 'gap-1 p-1';
    }
  }, [cardConfig.spacing]);

  // 获取容器主题样式 - 使用 useMemo 缓存
  const containerTheme = useMemo(() => {
    switch (cardConfig.theme) {
      case 'modern':
        return 'bg-gradient-to-br from-slate-50 to-gray-100 dark:from-slate-900 dark:to-gray-800';
      case 'glass':
        return 'bg-white/70 backdrop-blur-sm dark:bg-gray-900/70';
      case 'gradient':
        return 'bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-blue-900/20 dark:via-gray-900 dark:to-purple-900/20';
      case 'dark':
        return 'bg-gray-900 text-white';
      default:
        return 'bg-background';
    }
  }, [cardConfig.theme]);

  // 处理任务右键菜单 - 使用 useCallback 优化
  const handleTaskContextMenu = useCallback(
    (e: React.MouseEvent, task: Task) => {
      onTaskContextMenuAction(e, task);
    },
    [onTaskContextMenuAction]
  );

  // 处理车辆双击 - 使用 useCallback 优化
  const handleVehicleDoubleClick = useCallback(
    (vehicleId: string, taskId: string) => {
      onOpenDeliveryOrderDetailsForVehicleAction(vehicleId, taskId);
    },
    [onOpenDeliveryOrderDetailsForVehicleAction]
  );

  // 使用缓存的车辆分组计算，避免重复计算
  const vehiclesByTask = useMemo(() => {
    // 确保vehicles是数组
    const validVehicles = Array.isArray(vehicles) ? vehicles : [];

    try {
      // 创建基于车辆状态的缓存键，包含assignedTaskId信息
      const vehicleStateKey = validVehicles
        .map(v => `${v?.id || ''}-${v?.assignedTaskId || ''}-${v?.status || ''}`)
        .join('|');
      const cacheKey = `vehicles-by-task-${validVehicles.length}-${vehicleStateKey}`;

      // 尝试从全局缓存获取
      const cached = globalCache.get<Map<string, Vehicle[]>>(cacheKey);
      if (cached && cached instanceof Map) {
        return cached;
      }

      // 执行分组计算
      const map = new Map<string, Vehicle[]>();
      validVehicles.forEach(vehicle => {
        if (vehicle && vehicle.assignedTaskId) {
          if (!map.has(vehicle.assignedTaskId)) {
            map.set(vehicle.assignedTaskId, []);
          }
          map.get(vehicle.assignedTaskId)!.push(vehicle);
        }
      });

      // 缓存结果（30秒TTL，减少缓存时间以确保数据及时更新）
      try {
        globalCache.set(cacheKey, map, { ttl: 30 * 1000 });
      } catch (cacheError) {
        console.warn('Failed to cache vehiclesByTask:', cacheError);
      }

      return map;
    } catch (error) {
      console.error('Error in vehiclesByTask calculation:', error);
      // 返回空的 Map 作为后备
      return new Map<string, Vehicle[]>();
    }
  }, [vehicles]);

  // 空状态显示
  const validTasks = Array.isArray(filteredTasks) ? filteredTasks : [];

  if (validTasks.length === 0) {
    return (
      <div className={cn('flex flex-col h-full transition-all duration-300', containerTheme)}>
        <div className='flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
          <div className='flex items-center gap-2'>
            <LayoutGrid className='w-5 h-5 text-muted-foreground' />
            <span className='font-medium'>任务卡片 (0)</span>
          </div>
        </div>

        <div
          className={cn(
            'flex flex-col items-center justify-center flex-1 text-muted-foreground',
            spacingClass
          )}
        >
          <div className='text-center space-y-4'>
            <div className='w-24 h-24 mx-auto bg-muted rounded-full flex items-center justify-center'>
              <LayoutGrid className='w-12 h-12 text-muted-foreground' />
            </div>
            <div>
              <h3 className='text-lg font-medium mb-2'>暂无任务</h3>
              <p className='text-sm text-muted-foreground'>当前筛选条件下没有找到任务</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={cn('flex flex-col h-full min-h-0 transition-all duration-300', containerTheme)}
    >
      {/* 卡片网格 - 直接渲染，不使用虚拟滚动 */}
      <div className='flex-1 overflow-auto custom-scrollbar min-h-0'>
        <div className={cn('grid', gridColumns, spacingClass, 'p-4')}>
          {(Array.isArray(filteredTasks) ? filteredTasks : []).map(task => {
            const taskVehicles =
              task && task.id && vehiclesByTask instanceof Map
                ? vehiclesByTask.get(task.id) || []
                : [];

            return (
              <Suspense key={task.id} fallback={<SkeletonLoader type='card' className='h-64' />}>
                <ConfigurableTaskCard
                  task={task}
                  vehicles={taskVehicles}
                  config={taskCardConfig}
                  vehicleDisplayMode={vehicleDisplayMode}
                  inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
                  density={settings.density}
                  onCancelDispatch={onCancelVehicleDispatchAction}
                  onOpenStyleEditor={onOpenStyleEditorAction}
                  onOpenDeliveryOrderDetails={handleVehicleDoubleClick}
                  onOpenVehicleContextMenu={onOpenVehicleCardContextMenuAction}
                  onTaskContextMenu={handleTaskContextMenu}
                  onTaskDoubleClick={onTaskDoubleClickAction}
                  onDropVehicleOnLine={onDropVehicleOnLine}
                />
              </Suspense>
            );
          })}
        </div>
      </div>
    </div>
  );
};
