'use client';

/**
 * 组件级懒加载器
 * 实现细粒度的组件懒加载和预加载策略
 */

import React, { Suspense, lazy, useState, useEffect, useRef } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { SkeletonLoader } from '@/shared/components/enhanced-loading';
import {
  unifiedPerformanceMonitor,
  useUnifiedPerformanceMonitor,
} from '@/infrastructure/monitoring/unified-performance-monitor';

// ==================== 懒加载策略类型 ====================

export type LazyLoadStrategy =
  | 'immediate' // 立即加载
  | 'viewport' // 进入视口时加载
  | 'hover' // 悬停时加载
  | 'click' // 点击时加载
  | 'idle' // 空闲时加载
  | 'delay'; // 延迟加载

export interface LazyComponentConfig {
  strategy: LazyLoadStrategy;
  delay?: number;
  threshold?: number;
  preload?: boolean;
  fallback?: React.ComponentType;
  errorFallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

// ==================== 懒加载组件工厂 ====================

export function createLazyComponent<T extends Record<string, any> = Record<string, any>>(
  importFn: () => Promise<{ default: React.ComponentType<T> }>,
  config: LazyComponentConfig = { strategy: 'immediate' }
) {
  const LazyComponent = lazy(importFn);

  return function LazyWrapper(props: T) {
    const [shouldLoad, setShouldLoad] = useState(config.strategy === 'immediate');
    const [hasLoaded, setHasLoaded] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const elementRef = useRef<HTMLDivElement>(null);
    const { recordCustomMetric } = useUnifiedPerformanceMonitor({
      componentName: 'ComponentLazyLoader',
    });

    // 视口观察器
    useEffect(() => {
      if (config.strategy !== 'viewport' || shouldLoad) return;

      const observer = new IntersectionObserver(
        entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              setShouldLoad(true);
              observer.disconnect();
            }
          });
        },
        { threshold: config.threshold || 0.1 }
      );

      if (elementRef.current) {
        observer.observe(elementRef.current);
      }

      return () => observer.disconnect();
    }, [config.strategy, config.threshold, shouldLoad]);

    // 空闲时加载
    useEffect(() => {
      if (config.strategy !== 'idle' || shouldLoad) return;

      if ('requestIdleCallback' in window) {
        const id = requestIdleCallback(() => setShouldLoad(true));
        return () => cancelIdleCallback(id);
      } else {
        const timer = setTimeout(() => setShouldLoad(true), 100);
        return () => clearTimeout(timer);
      }
    }, [config.strategy, shouldLoad]);

    // 延迟加载
    useEffect(() => {
      if (config.strategy !== 'delay' || shouldLoad) return;

      const timer = setTimeout(() => setShouldLoad(true), config.delay || 1000);
      return () => clearTimeout(timer);
    }, [config.strategy, config.delay, shouldLoad]);

    // 预加载
    useEffect(() => {
      if (config.preload && !hasLoaded) {
        importFn().then(() => setHasLoaded(true));
      }
    }, [config.preload, hasLoaded]);

    // 事件处理器
    const handleHover = () => {
      if (config.strategy === 'hover' && !shouldLoad) {
        setShouldLoad(true);
      }
    };

    const handleClick = () => {
      if (config.strategy === 'click' && !shouldLoad) {
        setShouldLoad(true);
      }
    };

    const handleRetry = () => {
      setError(null);
      setShouldLoad(true);
    };

    // 默认加载中组件
    const DefaultFallback =
      config.fallback || (() => <SkeletonLoader type='card' className='w-full h-32' />);

    // 默认错误组件
    const DefaultErrorFallback =
      config.errorFallback ||
      (({ error, retry }) => (
        <div className='p-4 border border-red-200 rounded-lg bg-red-50'>
          <div className='text-red-600 text-sm mb-2'>组件加载失败</div>
          <div className='text-gray-600 text-xs mb-3'>{error.message}</div>
          <button
            onClick={retry}
            className='px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700'
          >
            重试
          </button>
        </div>
      ));

    // 如果还未开始加载，显示占位符
    if (!shouldLoad) {
      return (
        <div
          ref={elementRef}
          onMouseEnter={handleHover}
          onClick={handleClick}
          className='lazy-component-placeholder'
        >
          {config.strategy === 'viewport' && <DefaultFallback />}
          {config.strategy === 'hover' && (
            <div className='p-4 border border-gray-200 rounded-lg bg-gray-50 cursor-pointer hover:bg-gray-100'>
              <div className='text-gray-500 text-sm'>悬停加载组件</div>
            </div>
          )}
          {config.strategy === 'click' && (
            <div className='p-4 border border-gray-200 rounded-lg bg-gray-50 cursor-pointer hover:bg-gray-100'>
              <div className='text-gray-500 text-sm'>点击加载组件</div>
            </div>
          )}
        </div>
      );
    }

    // 错误状态
    if (error) {
      return <DefaultErrorFallback error={error} retry={handleRetry} />;
    }

    // 加载组件
    return (
      <ErrorBoundary
        fallback={<DefaultErrorFallback error={new Error('组件渲染错误')} retry={handleRetry} />}
        onError={error => setError(error)}
      >
        <Suspense fallback={<DefaultFallback />}>
          <LazyComponent {...(props as any)} />
        </Suspense>
      </ErrorBoundary>
    );
  };
}

// ==================== 预定义懒加载组件 ====================

// 图表组件懒加载器 - 简化版本
export const LazyChart = createLazyComponent<any>(
  () =>
    import('@/shared/components/charts/LazyChartManager').then(module => ({
      default: module.LazyChart as any,
    })),
  {
    strategy: 'viewport',
    threshold: 0.2,
    preload: false,
  }
);

// 模态框懒加载器
export function createLazyModal(importFn: () => Promise<any>) {
  return createLazyComponent(importFn, {
    strategy: 'click',
    preload: false,
  });
}

// 表单懒加载器
export function createLazyForm(importFn: () => Promise<any>) {
  return createLazyComponent(importFn, {
    strategy: 'hover',
    preload: true,
  });
}

// 报表懒加载器
export function createLazyReport(importFn: () => Promise<any>) {
  return createLazyComponent(importFn, {
    strategy: 'viewport',
    threshold: 0.1,
    preload: false,
  });
}

// ==================== 批量懒加载管理器 ====================

export class BatchLazyLoader {
  private static loadQueue: Array<() => Promise<any>> = [];
  private static isProcessing = false;
  private static maxConcurrent = 3;

  /**
   * 添加到加载队列
   */
  static addToQueue(importFn: () => Promise<any>) {
    this.loadQueue.push(importFn);
    this.processQueue();
  }

  /**
   * 处理加载队列
   */
  private static async processQueue() {
    if (this.isProcessing || this.loadQueue.length === 0) return;

    this.isProcessing = true;
    const batch = this.loadQueue.splice(0, this.maxConcurrent);

    try {
      await Promise.all(batch.map(fn => fn()));
    } catch (error) {
      console.warn('批量懒加载失败:', error);
    }

    this.isProcessing = false;

    // 继续处理剩余队列
    if (this.loadQueue.length > 0) {
      setTimeout(() => this.processQueue(), 100);
    }
  }

  /**
   * 清空队列
   */
  static clearQueue() {
    this.loadQueue = [];
    this.isProcessing = false;
  }
}

// ==================== 懒加载性能监控 ====================

export function useLazyLoadMetrics() {
  const [metrics, setMetrics] = useState({
    totalComponents: 0,
    loadedComponents: 0,
    failedComponents: 0,
    averageLoadTime: 0,
  });

  useEffect(() => {
    // 简化的性能监控
    const updateMetrics = () => {
      setMetrics(prev => ({
        ...prev,
        totalComponents: document.querySelectorAll('.lazy-component-placeholder').length,
        loadedComponents: document.querySelectorAll('[data-lazy-loaded="true"]').length,
      }));
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 2000);

    return () => clearInterval(interval);
  }, []);

  return metrics;
}

// ==================== 懒加载调试工具 ====================

export function LazyLoadDebugger() {
  const metrics = useLazyLoadMetrics();
  const [isVisible, setIsVisible] = useState(false);

  if (process.env.NODE_ENV !== 'development') return null;

  return (
    <>
      <button
        onClick={() => setIsVisible(!isVisible)}
        className='fixed bottom-20 right-4 bg-blue-600 text-white p-2 rounded-full shadow-lg z-50'
        title='懒加载调试器'
      >
        🔍
      </button>

      {isVisible && (
        <div className='fixed bottom-32 right-4 bg-white border rounded-lg p-4 shadow-lg z-50 text-xs'>
          <div className='font-medium mb-2'>懒加载状态</div>
          <div>总组件: {metrics.totalComponents}</div>
          <div>已加载: {metrics.loadedComponents}</div>
          <div>失败: {metrics.failedComponents}</div>
          <div>平均加载时间: {metrics.averageLoadTime.toFixed(2)}ms</div>

          <button
            onClick={() => BatchLazyLoader.clearQueue()}
            className='mt-2 px-2 py-1 bg-red-500 text-white rounded text-xs'
          >
            清空队列
          </button>
        </div>
      )}
    </>
  );
}

export default {
  createLazyComponent,
  createLazyModal,
  createLazyForm,
  createLazyReport,
  LazyChart,
  BatchLazyLoader,
  LazyLoadDebugger,
  useLazyLoadMetrics,
};
