import React from 'react';

// 数据源切换工具
export class DataSourceSwitcher {
  private static readonly STORAGE_KEY = 'tmh_data_source';

  // 获取当前数据源模式
  static getCurrentMode(): 'mock' | 'api' {
    if (typeof window === 'undefined') {
      return process.env['NEXT_PUBLIC_USE_MOCK_DATA'] === 'true' ? 'mock' : 'api';
    }

    const stored = localStorage.getItem(this.STORAGE_KEY);
    if (stored) {
      return stored as 'mock' | 'api';
    }

    return process.env['NEXT_PUBLIC_USE_MOCK_DATA'] === 'true' ? 'mock' : 'api';
  }

  // 设置数据源模式
  static setMode(mode: 'mock' | 'api'): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.STORAGE_KEY, mode);
      // 设置环境变量（仅在客户端有效）
      (window as any).__NEXT_PUBLIC_USE_MOCK_DATA = mode === 'mock' ? 'true' : 'false';
    }
  }

  // 切换数据源模式
  static toggleMode(): 'mock' | 'api' {
    const current = this.getCurrentMode();
    const newMode = current === 'mock' ? 'api' : 'mock';
    this.setMode(newMode);
    return newMode;
  }

  // 检查是否使用mock数据
  static isMockMode(): boolean {
    return this.getCurrentMode() === 'mock';
  }

  // 获取当前API配置信息
  static getConfigInfo() {
    const mode = this.getCurrentMode();
    const baseUrl =
      mode === 'mock' ? 'Mock Data' : process.env['NEXT_PUBLIC_API_BASE_URL'] || 'Not Set';

    return {
      mode,
      baseUrl,
      description: mode === 'mock' ? '使用本地Mock数据' : '使用真实API数据',
    };
  }
}

// 开发工具：数据源切换组件
export function DataSourceToggle() {
  if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
    return null;
  }

  const [mode, setMode] = React.useState(DataSourceSwitcher.getCurrentMode());

  const handleToggle = () => {
    const newMode = DataSourceSwitcher.toggleMode();
    setMode(newMode);
    // 刷新页面以应用新配置
    window.location.reload();
  };

  const containerStyle: React.CSSProperties = {
    position: 'fixed',
    top: '2px',
    right: '200px',
    zIndex: 9999,
    background: mode === 'mock' ? '#fef3c7' : '#dcfce7',
    border: `2px solid ${mode === 'mock' ? '#f59e0b' : '#16a34a'}`,
    borderRadius: '8px',
    padding: '8px 12px',
    fontSize: '12px',
    cursor: 'pointer',
    userSelect: 'none',
  };

  const titleStyle: React.CSSProperties = {
    fontWeight: 'bold',
    color: mode === 'mock' ? '#92400e' : '#166534',
  };

  const subtitleStyle: React.CSSProperties = {
    fontSize: '10px',
    color: mode === 'mock' ? '#92400e' : '#166534',
  };

  return (
    <div style={containerStyle} onClick={handleToggle}>
      <div style={titleStyle}>数据源: {mode === 'mock' ? 'Mock' : 'API'}</div>
      <div style={subtitleStyle}>点击切换</div>
    </div>
  );
}
