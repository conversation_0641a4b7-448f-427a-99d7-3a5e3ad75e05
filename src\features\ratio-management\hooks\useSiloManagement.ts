'use client';

import { useState, useEffect, useCallback } from 'react';
import type { Silo } from '@/core/types/ratio';
import { mockSilos } from '@/infrastructure/api/mock/ratio-mock-data';

const SILO_STORAGE_KEY = 'tmh_silo_data';

export function useSiloManagement() {
  const [silos, setSilos] = useState<Silo[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const loadSilos = useCallback(() => {
    try {
      const stored = localStorage.getItem(SILO_STORAGE_KEY);
      if (stored) {
        const parsedSilos = JSON.parse(stored);
        setSilos(parsedSilos);
      } else {
        setSilos(mockSilos);
        localStorage.setItem(SILO_STORAGE_KEY, JSON.stringify(mockSilos));
      }
    } catch (error) {
      console.error('加载料仓数据失败:', error);
      setSilos(mockSilos);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const saveSilos = useCallback((newSilos: Silo[]) => {
    try {
      localStorage.setItem(SILO_STORAGE_KEY, JSON.stringify(newSilos));
      setSilos(newSilos);
    } catch (error) {
      console.error('保存料仓数据失败:', error);
    }
  }, []);

  const addSilo = useCallback(
    (silo: Omit<Silo, 'id' | 'lastUpdated'>) => {
      const newSilo: Silo = {
        ...silo,
        id: `silo-${Date.now()}`,
        lastUpdated: new Date().toISOString(),
      };
      const updatedSilos = [...silos, newSilo];
      saveSilos(updatedSilos);
      return newSilo;
    },
    [silos, saveSilos]
  );

  const updateSilo = useCallback(
    (siloId: string, updates: Partial<Silo>) => {
      const updatedSilos = silos.map(silo =>
        silo.id === siloId ? { ...silo, ...updates, lastUpdated: new Date().toISOString() } : silo
      );
      saveSilos(updatedSilos);
    },
    [silos, saveSilos]
  );

  const deleteSilo = useCallback(
    (siloId: string) => {
      const updatedSilos = silos.filter(silo => silo.id !== siloId);
      saveSilos(updatedSilos);
    },
    [silos, saveSilos]
  );

  const findSiloByMaterial = useCallback(
    (materialName: string) => {
      return silos.find(
        silo =>
          silo.materialName === materialName ||
          silo.name.includes(materialName) ||
          materialName.includes(silo.materialName)
      );
    },
    [silos]
  );

  const checkSiloExists = useCallback(
    (materialName: string) => {
      return !!findSiloByMaterial(materialName);
    },
    [findSiloByMaterial]
  );

  const getSiloStats = useCallback(() => {
    const total = silos.length;
    const normal = silos.filter(s => s.status === 'normal').length;
    const low = silos.filter(s => s.status === 'low').length;
    const empty = silos.filter(s => s.status === 'empty').length;
    const maintenance = silos.filter(s => s.status === 'maintenance').length;

    return { total, normal, low, empty, maintenance };
  }, [silos]);

  const resetToDefault = useCallback(() => {
    saveSilos(mockSilos);
  }, [saveSilos]);

  useEffect(() => {
    loadSilos();
  }, [loadSilos]);

  return {
    silos,
    isLoading,
    addSilo,
    updateSilo,
    deleteSilo,
    findSiloByMaterial,
    checkSiloExists,
    getSiloStats,
    resetToDefault,
    refreshSilos: loadSilos,
  };
}

export function SiloManagementProvider({ children }: { children: React.ReactNode }) {
  const siloManagement = useSiloManagement();

  useEffect(() => {
    // 全局状态管理逻辑
  }, [siloManagement]);

  return children;
}
