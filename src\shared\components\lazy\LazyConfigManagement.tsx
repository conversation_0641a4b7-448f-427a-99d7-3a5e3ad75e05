/**
 * LazyConfigManagement - 配置管理界面的动态导入包装器
 * 只在需要时加载配置管理组件，减少初始包体积
 */

'use client';

import React, { Suspense, lazy } from 'react';
import { LoadingSpinner } from '@/shared/components/LoadingSpinner';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Settings } from 'lucide-react';

// 动态导入配置管理组件
const ConfigManagement = lazy(() =>
  import('@/shared/components/settings/ConfigManagement').then(module => ({
    default: module.ConfigManagement,
  }))
);

/**
 * 配置管理加载占位符
 */
function ConfigManagementLoadingFallback() {
  return (
    <div className='space-y-6'>
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Settings className='w-5 h-5' />
            配置管理
          </CardTitle>
        </CardHeader>
        <CardContent className='flex items-center justify-center p-8'>
          <LoadingSpinner />
          <span className='ml-2 text-sm text-muted-foreground'>加载配置管理界面...</span>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * 配置管理的懒加载包装器
 * 提供加载状态和错误处理
 */
export function LazyConfigManagement() {
  return (
    <Suspense fallback={<ConfigManagementLoadingFallback />}>
      <ConfigManagement />
    </Suspense>
  );
}

export default LazyConfigManagement;
