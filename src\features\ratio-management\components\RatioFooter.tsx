/**
 * 配比页脚组件
 * 包含保存、提交等操作按钮和状态信息
 */

import React from 'react';
import { Save } from 'lucide-react';
import { Button } from '@/shared/components/button';
import { Card } from '@/shared/components/card';
import { Checkbox } from '@/shared/components/checkbox';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import type { RatioFooterProps } from '@/core/types/ratio';

/**
 * 配比页脚组件
 */
export const RatioFooter = React.memo<RatioFooterProps>(function RatioFooter({
  onSave,
  onSubmit,
  onExit,
  isDirty,
  isSaving,
}) {
  const handleSave = () => {
    onSave();
  };

  const handleSubmit = () => {
    onSubmit();
  };

  const handleExit = () => {
    if (isDirty) {
      const confirmed = window.confirm('有未保存的更改，确定要退出吗？');
      if (!confirmed) return;
    }
    onExit();
  };

  return (
    <Card className='p-1 flex items-center justify-between text-sm flex-shrink-0'>
      {/* 左侧：输入区域 */}
      <div className='flex items-center gap-4 flex-wrap'>
        <Input placeholder='搅拌生产时提示内容...' className='w-64 h-8 text-xs' />

        <div className='flex items-center gap-2'>
          <Label className='text-xs'>流水号:</Label>
          <Input className='w-48 h-8 text-xs' placeholder='自动生成或手动输入' />
        </div>
      </div>

      {/* 右侧：状态信息和操作按钮 */}
      <div className='flex items-center gap-2 flex-wrap'>
        {/* 创建和修改信息 */}
        <div className='flex flex-col text-xs text-muted-foreground'>
          <span>2025-04-09 13:53:09 白彬彬(电脑)创建</span>
          <span>2025-04-11 09:56:53 李文远(电脑)最后修改</span>
        </div>

        {/* 同步选项 */}
        <div className='flex items-center space-x-2'>
          <Checkbox id='sync' />
          <Label htmlFor='sync' className='text-xs'>
            同步【同选用配比】
          </Label>
        </div>

        {/* 操作按钮 */}
        <div className='flex items-center gap-2'>
          <Button
            size='sm'
            variant='outline'
            onClick={handleSave}
            disabled={!isDirty || isSaving}
            className='h-8'
          >
            <Save className='w-4 h-4 mr-1' />
            {isSaving ? '保存中...' : '保存'}
          </Button>

          <Button size='sm' onClick={handleSubmit} disabled={isSaving} className='h-8'>
            审核-发送
          </Button>

          <Button
            size='sm'
            variant='secondary'
            onClick={handleExit}
            disabled={isSaving}
            className='h-8'
          >
            退出
          </Button>
        </div>

        {/* 状态指示器 */}
        {isDirty && (
          <div className='flex items-center gap-1'>
            <div className='w-2 h-2 bg-orange-500 rounded-full'></div>
            <span className='text-xs text-orange-600'>未保存</span>
          </div>
        )}

        {isSaving && (
          <div className='flex items-center gap-1'>
            <div className='w-2 h-2 bg-blue-500 rounded-full animate-pulse'></div>
            <span className='text-xs text-blue-600'>保存中</span>
          </div>
        )}
      </div>
    </Card>
  );
});
