/**
 * 测试工具函数
 * 提供测试中常用的工具函数和组件
 *
 * 注意：这个文件不包含测试用例，只是工具函数
 * @jest-environment jsdom
 */

// 防止Jest将此文件识别为测试套件
if (typeof describe === 'undefined') {
  // 这是一个工具文件，不是测试文件
}

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import '@testing-library/jest-dom';
import { RenderOptions, render as rtlRender } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';

/**
 * 测试提供者组件
 * 包装测试组件，提供必要的上下文
 */
interface TestProvidersProps {
  children: React.ReactNode;
  queryClient?: QueryClient;
}

function TestProviders({ children, queryClient }: TestProvidersProps) {
  // 创建默认的QueryClient
  const defaultQueryClient = React.useMemo(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            retry: false,
            staleTime: 0,
            gcTime: 0,
          },
          mutations: {
            retry: false,
          },
        },
      }),
    []
  );

  const client = queryClient || defaultQueryClient;

  return (
    <QueryClientProvider client={client}>
      <div data-testid='test-providers'>{children}</div>
    </QueryClientProvider>
  );
}

/**
 * 自定义渲染函数
 * 自动包装测试提供者
 */
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  wrapper?: React.ComponentType<any>;
  queryClient?: QueryClient;
}

export function render(ui: React.ReactElement, options: CustomRenderOptions = {}) {
  const { wrapper, queryClient, ...renderOptions } = options;

  // 如果提供了自定义wrapper，使用它；否则使用TestProviders
  const Wrapper =
    wrapper || ((props: any) => <TestProviders queryClient={queryClient} {...props} />);

  return rtlRender(ui, {
    wrapper: Wrapper,
    ...renderOptions,
  });
}

/**
 * 创建用户事件实例
 */
export function mockUserEvent() {
  return userEvent.setup();
}

/**
 * Mock localStorage
 */
export function mockLocalStorage() {
  const store: Record<string, string> = {};

  const mockStorage = {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    }),
    key: jest.fn((index: number) => Object.keys(store)[index] || null),
    get length() {
      return Object.keys(store).length;
    },
  };

  Object.defineProperty(window, 'localStorage', {
    value: mockStorage,
    writable: true,
  });

  return mockStorage;
}

/**
 * Mock sessionStorage
 */
export function mockSessionStorage() {
  const store: Record<string, string> = {};

  const mockStorage = {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    }),
    key: jest.fn((index: number) => Object.keys(store)[index] || null),
    get length() {
      return Object.keys(store).length;
    },
  };

  Object.defineProperty(window, 'sessionStorage', {
    value: mockStorage,
    writable: true,
  });

  return mockStorage;
}

/**
 * Mock window.matchMedia
 */
export function mockMatchMedia(matches: boolean = false) {
  const mockMatchMedia = jest.fn().mockImplementation(query => ({
    matches,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  }));

  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: mockMatchMedia,
  });

  return mockMatchMedia;
}

/**
 * Mock ResizeObserver
 */
export function mockResizeObserver() {
  const mockResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));

  global.ResizeObserver = mockResizeObserver;
  return mockResizeObserver;
}

/**
 * Mock IntersectionObserver
 */
export function mockIntersectionObserver() {
  const mockIntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
    root: null,
    rootMargin: '',
    thresholds: [],
  }));

  global.IntersectionObserver = mockIntersectionObserver;
  return mockIntersectionObserver;
}

/**
 * 等待异步操作完成
 */
export function waitForAsync(ms: number = 0): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 创建模拟的Promise
 */
export function createMockPromise<T>(
  resolveValue?: T,
  rejectValue?: any,
  delay: number = 0
): Promise<T> {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (rejectValue !== undefined) {
        reject(rejectValue);
      } else {
        resolve(resolveValue as T);
      }
    }, delay);
  });
}

/**
 * 模拟网络延迟
 */
export function mockNetworkDelay(ms: number = 100): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 清理所有Mock
 */
export function cleanupMocks() {
  jest.clearAllMocks();
  jest.clearAllTimers();
  jest.restoreAllMocks();
}

/**
 * 创建模拟的事件对象
 */
export function createMockEvent(type: string, properties: any = {}) {
  const event = new Event(type, { bubbles: true, cancelable: true });
  Object.assign(event, properties);
  return event;
}

/**
 * 模拟文件上传
 */
export function createMockFile(
  name: string = 'test.txt',
  content: string = 'test content',
  type: string = 'text/plain'
): File {
  const blob = new Blob([content], { type });
  return new File([blob], name, { type });
}

/**
 * 模拟拖拽事件
 */
export function createMockDragEvent(type: string, dataTransfer: any = {}) {
  const event = new Event(type, { bubbles: true, cancelable: true });
  Object.defineProperty(event, 'dataTransfer', {
    value: {
      getData: jest.fn(),
      setData: jest.fn(),
      clearData: jest.fn(),
      dropEffect: 'none',
      effectAllowed: 'all',
      files: [],
      items: [],
      types: [],
      ...dataTransfer,
    },
    writable: false,
  });
  return event;
}

/**
 * 断言工具函数
 */
export const assertions = {
  /**
   * 断言元素可见
   */
  toBeVisible: (element: HTMLElement) => {
    expect(element).toBeInTheDocument();
    expect(element).toBeVisible();
  },

  /**
   * 断言元素不可见
   */
  toBeHidden: (element: HTMLElement | null) => {
    if (element) {
      expect(element).not.toBeVisible();
    } else {
      expect(element).not.toBeInTheDocument();
    }
  },

  /**
   * 断言元素有特定类名
   */
  toHaveClass: (element: HTMLElement, className: string) => {
    expect(element).toHaveClass(className);
  },

  /**
   * 断言元素有特定属性
   */
  toHaveAttribute: (element: HTMLElement, attribute: string, value?: string) => {
    if (value !== undefined) {
      expect(element).toHaveAttribute(attribute, value);
    } else {
      expect(element).toHaveAttribute(attribute);
    }
  },
};

// 重新导出常用的测试库函数
export * from '@testing-library/react';
export { userEvent };
