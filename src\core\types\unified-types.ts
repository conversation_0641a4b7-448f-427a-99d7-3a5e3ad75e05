/**
 * 统一类型定义入口文件
 * 重新导出所有类型定义，提供统一的导入入口
 */

// ==================== 核心枚举类型 ====================
export * from './core-enums';

// ==================== 核心接口类型 ====================
export * from './core-interfaces';

// ==================== 工具类型 ====================
export * from './utility-types';

// ==================== Hook类型 ====================
export * from './hook-types';

// ==================== 系统常量 ====================
export * from '../constants/system-constants';

// ==================== 类型别名 ====================
// 为常用类型提供简短的别名

import type {
  UnifiedVehicle,
  UnifiedTask,
  UnifiedPlant,
  RatioCalculationParams,
  UnifiedRatioMaterial,
} from './core-interfaces';

import type {
  UseRatioCalculationReturn,
  UseRatioFormReturn,
  UseRatioMaterialsReturn,
  UseVehicleManagementReturn,
  UseTaskManagementReturn,
} from './hook-types';

// 车辆相关别名
export type Vehicle = UnifiedVehicle;
export type VehicleHook = UseVehicleManagementReturn;

// 任务相关别名
export type Task = UnifiedTask;
export type TaskHook = UseTaskManagementReturn;

// 工厂相关别名
export type Plant = UnifiedPlant;

// 配比相关别名
export type RatioParams = RatioCalculationParams;
export type RatioMaterial = UnifiedRatioMaterial;
export type RatioCalculationHook = UseRatioCalculationReturn;
export type RatioFormHook = UseRatioFormReturn;
export type RatioMaterialsHook = UseRatioMaterialsReturn;

// ==================== 类型映射 ====================
// 提供类型之间的映射关系

/**
 * 旧类型到新类型的映射
 */
export const TYPE_MIGRATION_MAP = {
  // 枚举类型映射
  VehicleStatus: 'VehicleStatus', // 保持不变
  TaskStatus: 'TaskDispatchStatus', // 重命名
  MaterialCategory: 'MaterialCategory', // 保持不变

  // 接口类型映射
  Vehicle: 'UnifiedVehicle',
  Task: 'UnifiedTask',
  Plant: 'UnifiedPlant',
  RatioCalculationParams: 'RatioCalculationParams', // 保持不变但字段有变化

  // Hook类型映射
  useRatioCalculation: 'UseRatioCalculationReturn',
  useRatioForm: 'UseRatioFormReturn',
  useRatioMaterials: 'UseRatioMaterialsReturn',
} as const;

// ==================== 类型验证工具 ====================

/**
 * 验证对象是否符合指定接口
 */
export function validateInterface<T>(obj: any, requiredFields: (keyof T)[]): obj is T {
  if (!obj || typeof obj !== 'object') {
    return false;
  }

  return requiredFields.every(field => field in obj);
}

/**
 * 验证车辆对象
 */
export function isValidVehicle(obj: any): obj is UnifiedVehicle {
  return validateInterface<UnifiedVehicle>(obj, [
    'id',
    'vehicleNumber',
    'plantId',
    'status',
    'capacity',
  ]);
}

/**
 * 验证任务对象
 */
export function isValidTask(obj: any): obj is UnifiedTask {
  return validateInterface<UnifiedTask>(obj, [
    'id',
    'plantId',
    'taskNumber',
    'projectName',
    'strength',
    'volume',
    'status',
    'scheduledTime',
    'dispatchFrequencyMinutes',
  ]);
}

/**
 * 验证配比参数对象
 */
export function isValidRatioParams(obj: any): obj is RatioCalculationParams {
  return validateInterface<RatioCalculationParams>(obj, [
    'targetStrength',
    'slump',
    'maxAggregateSize',
    'exposureClass',
    'waterCementRatio',
    'sandRatio',
    'cementContent',
    'waterContent',
  ]);
}

// ==================== 默认值导出 ====================

export { DEFAULT_RATIO_CALCULATION_PARAMS } from './core-interfaces';

// ==================== 类型文档 ====================

/**
 * 类型系统使用指南
 *
 * 1. 导入类型：
 *    ```typescript
 *    import type { Vehicle, Task, RatioParams } from '@/core/types/unified-types';
 *    ```
 *
 * 2. 使用枚举：
 *    ```typescript
 *    import { VehicleStatus, MaterialCategory } from '@/core/types/unified-types';
 *    ```
 *
 * 3. 使用常量：
 *    ```typescript
 *    import { RATIO_CONSTANTS, ERROR_MESSAGES } from '@/core/types/unified-types';
 *    ```
 *
 * 4. 使用Hook类型：
 *    ```typescript
 *    import type { RatioCalculationHook } from '@/core/types/unified-types';
 *    ```
 *
 * 5. 类型验证：
 *    ```typescript
 *    import { isValidVehicle, isValidTask } from '@/core/types/unified-types';
 *    ```
 */
