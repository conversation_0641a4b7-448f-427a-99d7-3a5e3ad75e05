/**
 * 包结构优化配置
 * 定义代码分割、依赖提取和缓存策略
 */

// ==================== 包分割策略 ====================

export const BUNDLE_SPLITTING_STRATEGY = {
  // 核心包 - 立即加载
  core: {
    name: 'core',
    chunks: ['main', 'runtime'],
    priority: 1,
    cacheGroup: 'default',
    dependencies: ['react', 'react-dom', 'next'],
  },

  // 第三方库包 - 长期缓存
  vendor: {
    name: 'vendor',
    chunks: ['vendor'],
    priority: 2,
    cacheGroup: 'vendor',
    dependencies: [
      '@mui/material',
      '@mui/icons-material',
      '@mui/x-date-pickers',
      'zustand',
      'lodash',
      'dayjs',
    ],
  },

  // UI 组件包 - 中期缓存
  ui: {
    name: 'ui',
    chunks: ['ui-components'],
    priority: 3,
    cacheGroup: 'ui',
    dependencies: ['@/components/ui', '@/components/layout', '@/components/common'],
  },

  // 图表包 - 按需加载
  charts: {
    name: 'charts',
    chunks: ['charts'],
    priority: 4,
    cacheGroup: 'charts',
    dependencies: ['recharts', 'd3', '@/components/charts'],
  },

  // 功能模块包 - 路由级分割
  modules: {
    dispatch: {
      name: 'dispatch-module',
      chunks: ['dispatch'],
      priority: 5,
      cacheGroup: 'modules',
      routes: ['/task-list', '/vehicle-dispatch'],
    },
    ratio: {
      name: 'ratio-module',
      chunks: ['ratio'],
      priority: 5,
      cacheGroup: 'modules',
      routes: ['/ratio', '/ratio/v1', '/ratio/v2', '/ratio/v3'],
    },
    reports: {
      name: 'reports-module',
      chunks: ['reports'],
      priority: 5,
      cacheGroup: 'modules',
      routes: ['/reports', '/analytics'],
    },
  },
} as const;

// ==================== 依赖优化配置 ====================

export const DEPENDENCY_OPTIMIZATION = {
  // 外部化依赖 - 不打包到 bundle 中
  externals: {
    development: [],
    production: [
      // CDN 依赖
      // 'react',
      // 'react-dom',
    ],
  },

  // 别名配置 - 减少包大小
  aliases: {
    '@mui/icons-material': '@mui/icons-material/esm',
    lodash: 'lodash-es',
  },

  // Tree Shaking 配置
  treeShaking: {
    enabled: true,
    sideEffects: false,
    usedExports: true,
    providedExports: true,
  },

  // 代码压缩配置
  minification: {
    removeComments: true,
    removeConsole: true,
    dropDebugger: true,
    mangleProps: false,
  },
} as const;

// ==================== 缓存策略配置 ====================

export const CACHE_STRATEGY = {
  // 文件名哈希策略
  hashing: {
    content: '[contenthash:8]', // 内容哈希
    chunk: '[chunkhash:8]', // 块哈希
    module: '[modulehash:8]', // 模块哈希
  },

  // 缓存组配置
  cacheGroups: {
    vendor: {
      test: /[\\/]node_modules[\\/]/,
      name: 'vendor',
      chunks: 'all',
      priority: 2,
      reuseExistingChunk: true,
      cacheGroupKey: 'vendor',
    },
    common: {
      name: 'common',
      chunks: 'all',
      minChunks: 2,
      priority: 1,
      reuseExistingChunk: true,
      cacheGroupKey: 'common',
    },
  },

  // HTTP 缓存策略
  httpCache: {
    vendor: {
      maxAge: 31536000, // 1年
      immutable: true,
    },
    app: {
      maxAge: 86400, // 1天
      immutable: false,
    },
    chunks: {
      maxAge: 604800, // 1周
      immutable: true,
    },
  },
} as const;

// ==================== 性能预算配置 ====================

export const PERFORMANCE_BUDGET = {
  // 包大小限制
  maxAssetSize: 500000, // 500KB
  maxEntrypointSize: 800000, // 800KB

  // 警告阈值
  warnings: {
    assetSize: 300000, // 300KB
    entrypointSize: 600000, // 600KB
  },

  // 性能提示
  hints: 'warning',

  // 分析配置
  analysis: {
    enabled: process.env['ANALYZE'] === 'true',
    openAnalyzer: false,
    generateStatsFile: true,
  },
} as const;

// ==================== 运行时优化配置 ====================

export const RUNTIME_OPTIMIZATION = {
  // 模块联邦配置
  moduleFederation: {
    enabled: false,
    name: 'tmh_task_dispatcher',
    remotes: {},
    exposes: {},
  },

  // 预加载配置
  preload: {
    // 关键资源预加载
    critical: ['core', 'vendor', 'ui'],

    // 预取资源
    prefetch: ['dispatch-module'],

    // 预连接
    preconnect: ['https://fonts.googleapis.com', 'https://fonts.gstatic.com'],
  },

  // Service Worker 配置
  serviceWorker: {
    enabled: process.env.NODE_ENV === 'production',
    scope: '/',
    strategies: {
      pages: 'NetworkFirst',
      assets: 'CacheFirst',
      api: 'NetworkFirst',
    },
  },
} as const;

// ==================== 开发环境优化 ====================

export const DEVELOPMENT_OPTIMIZATION = {
  // 热更新配置
  hmr: {
    enabled: true,
    overlay: true,
    quiet: false,
  },

  // 源码映射
  sourceMap: {
    development: 'eval-source-map',
    production: 'source-map',
  },

  // 构建缓存
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename],
    },
  },
} as const;

// ==================== 包分析工具 ====================

export class BundleAnalyzer {
  private static metrics = new Map<string, BundleMetrics>();

  /**
   * 记录包加载指标
   */
  static recordBundleLoad(bundleName: string, size: number, loadTime: number) {
    const existing = this.metrics.get(bundleName) || {
      name: bundleName,
      totalSize: 0,
      loadCount: 0,
      totalLoadTime: 0,
      averageLoadTime: 0,
    };

    existing.totalSize = size;
    existing.loadCount++;
    existing.totalLoadTime += loadTime;
    existing.averageLoadTime = existing.totalLoadTime / existing.loadCount;

    this.metrics.set(bundleName, existing);
  }

  /**
   * 获取包分析报告
   */
  static getAnalysisReport(): BundleAnalysisReport {
    const bundles = Array.from(this.metrics.values());
    const totalSize = bundles.reduce((sum, bundle) => sum + bundle.totalSize, 0);
    const totalLoadTime = bundles.reduce((sum, bundle) => sum + bundle.totalLoadTime, 0);

    return {
      bundles,
      summary: {
        totalBundles: bundles.length,
        totalSize,
        averageSize: bundles.length > 0 ? totalSize / bundles.length : 0,
        totalLoadTime,
        averageLoadTime: bundles.length > 0 ? totalLoadTime / bundles.length : 0,
      },
      recommendations: this.generateRecommendations(bundles),
    };
  }

  /**
   * 生成优化建议
   */
  private static generateRecommendations(bundles: BundleMetrics[]): string[] {
    const recommendations: string[] = [];

    // 检查大包
    const largeBundles = bundles.filter(b => b.totalSize > PERFORMANCE_BUDGET.warnings.assetSize);
    if (largeBundles.length > 0) {
      recommendations.push(`发现 ${largeBundles.length} 个大包，建议进一步分割`);
    }

    // 检查加载时间
    const slowBundles = bundles.filter(b => b.averageLoadTime > 1000);
    if (slowBundles.length > 0) {
      recommendations.push(`发现 ${slowBundles.length} 个加载缓慢的包，建议优化或预加载`);
    }

    // 检查未使用的包
    const unusedBundles = bundles.filter(b => b.loadCount === 0);
    if (unusedBundles.length > 0) {
      recommendations.push(`发现 ${unusedBundles.length} 个未使用的包，建议移除`);
    }

    return recommendations;
  }

  /**
   * 清理指标数据
   */
  static clearMetrics() {
    this.metrics.clear();
  }
}

// ==================== 类型定义 ====================

export interface BundleMetrics {
  name: string;
  totalSize: number;
  loadCount: number;
  totalLoadTime: number;
  averageLoadTime: number;
}

export interface BundleAnalysisReport {
  bundles: BundleMetrics[];
  summary: {
    totalBundles: number;
    totalSize: number;
    averageSize: number;
    totalLoadTime: number;
    averageLoadTime: number;
  };
  recommendations: string[];
}

// ==================== 工具函数 ====================

/**
 * 获取包大小（字节）
 */
export function getBundleSize(bundleName: string): Promise<number> {
  return new Promise(resolve => {
    // 简化实现，实际应该从构建工具获取
    const estimatedSizes: Record<string, number> = {
      core: 200000,
      vendor: 800000,
      ui: 300000,
      charts: 400000,
      'dispatch-module': 150000,
      'ratio-module': 200000,
      'reports-module': 100000,
    };

    resolve(estimatedSizes[bundleName] || 50000);
  });
}

/**
 * 检查包是否已加载
 */
export function isBundleLoaded(bundleName: string): boolean {
  // 简化实现，检查是否有对应的 script 标签
  return document.querySelector(`script[data-chunk="${bundleName}"]`) !== null;
}

/**
 * 预加载包
 */
export function preloadBundle(bundleName: string): Promise<void> {
  return new Promise((resolve, reject) => {
    if (isBundleLoaded(bundleName)) {
      resolve();
      return;
    }

    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'script';
    link.href = `/chunks/${bundleName}.js`;

    link.onload = () => resolve();
    link.onerror = () => reject(new Error(`Failed to preload ${bundleName}`));

    document.head.appendChild(link);
  });
}

export default {
  BUNDLE_SPLITTING_STRATEGY,
  DEPENDENCY_OPTIMIZATION,
  CACHE_STRATEGY,
  PERFORMANCE_BUDGET,
  RUNTIME_OPTIMIZATION,
  DEVELOPMENT_OPTIMIZATION,
  BundleAnalyzer,
  getBundleSize,
  isBundleLoaded,
  preloadBundle,
};
