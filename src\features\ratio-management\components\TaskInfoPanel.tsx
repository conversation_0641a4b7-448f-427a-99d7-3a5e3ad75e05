/**
 * 任务信息面板组件
 * 显示当前任务的基本信息
 */

import React from 'react';
import { Card, CardContent } from '@/shared/components/card';
import { Label } from '@/shared/components/label';
import type { TaskInfoPanelProps } from '@/core/types/ratio';

/**
 * 任务信息面板组件
 */
export const TaskInfoPanel = React.memo<TaskInfoPanelProps>(function TaskInfoPanel({ task }) {
  if (!task) {
    return (
      <Card className='flex-shrink-0'>
        <CardContent className='p-2 text-sm'>
          <div className='text-center text-muted-foreground'>暂无任务信息</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className='flex-shrink-0'>
      <CardContent className='p-2 text-sm'>
        {/* 第一行：基本信息 */}
        <div className='grid grid-cols-2 lg:grid-cols-5 gap-y-2 gap-x-2 mb-2'>
          <div className='flex items-center'>
            <Label className='text-xs text-muted-foreground whitespace-nowrap text-right mr-1'>
              任务编号：
            </Label>
            <span className='font-semibold text-xs'>{task.taskNumber}</span>
          </div>

          <div className='flex items-center'>
            <Label className='text-xs text-muted-foreground whitespace-nowrap text-right mr-1'>
              强度：
            </Label>
            <span className='font-semibold text-xs'>{task.strength}</span>
          </div>

          <div className='flex items-center'>
            <Label className='text-xs text-muted-foreground whitespace-nowrap text-right mr-1'>
              抗渗：
            </Label>
            <span className='font-semibold text-xs'>{task.waterproofGrade || '--'}</span>
          </div>

          <div className='flex items-center'>
            <Label className='text-xs text-muted-foreground whitespace-nowrap text-right mr-1'>
              抗冻：
            </Label>
            <span className='font-semibold text-xs'>{task.freezeResistanceGrade || '--'}</span>
          </div>

          <div className='flex items-center'>
            <Label className='text-xs text-muted-foreground whitespace-nowrap text-right mr-1'>
              配比编号：
            </Label>
            <span className='font-semibold text-xs'>{task.ratioNumber || task.taskNumber}</span>
          </div>
        </div>

        {/* 第二行：详细信息 */}
        <div className='grid grid-cols-1 lg:grid-cols-4 gap-y-2 gap-x-2'>
          <div className='flex items-center lg:col-span-2'>
            <Label className='text-xs text-muted-foreground whitespace-nowrap text-right mr-1'>
              工程名称：
            </Label>
            <span className='font-semibold text-xs truncate' title={task.projectName}>
              {task.projectName}
            </span>
          </div>

          <div className='flex items-center'>
            <Label className='text-xs text-muted-foreground whitespace-nowrap text-right mr-1'>
              施工部位：
            </Label>
            <span className='font-semibold text-xs'>{task.constructionSite}</span>
          </div>

          <div className='flex items-center'>
            <Label className='text-xs text-muted-foreground whitespace-nowrap text-right mr-1'>
              坍落度：
            </Label>
            <span className='font-semibold text-xs'>{task.slumpRange || '180±20'}</span>
          </div>
        </div>

        {/* 第三行：施工信息 */}
        <div className='grid grid-cols-1 lg:grid-cols-4 gap-y-2 gap-x-2 mt-2'>
          <div className='flex items-center'>
            <Label className='text-xs text-muted-foreground whitespace-nowrap text-right mr-1'>
              浇筑方式：
            </Label>
            <span className='font-semibold text-xs'>{task.pouringMethod}</span>
          </div>

          <div className='flex items-center'>
            <Label className='text-xs text-muted-foreground whitespace-nowrap text-right mr-1'>
              设计方量：
            </Label>
            <span className='font-semibold text-xs'>{task.designVolume || '--'} m³</span>
          </div>

          <div className='flex items-center'>
            <Label className='text-xs text-muted-foreground whitespace-nowrap text-right mr-1'>
              已生产：
            </Label>
            <span className='font-semibold text-xs'>{task.producedVolume || 0} m³</span>
          </div>

          <div className='flex items-center'>
            <Label className='text-xs text-muted-foreground whitespace-nowrap text-right mr-1'>
              剩余方量：
            </Label>
            <span className='font-semibold text-xs'>
              {task.designVolume && task.producedVolume
                ? task.designVolume - task.producedVolume
                : '--'}{' '}
              m³
            </span>
          </div>
        </div>

        {/* 状态指示器 */}
        {task.status && (
          <div className='flex items-center justify-end mt-2'>
            <div
              className={`
              px-2 py-1 rounded-full text-xs font-medium
              ${task.status === 'active' ? 'bg-green-100 text-green-800' : ''}
              ${task.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : ''}
              ${task.status === 'completed' ? 'bg-blue-100 text-blue-800' : ''}
              ${task.status === 'cancelled' ? 'bg-red-100 text-red-800' : ''}
            `}
            >
              {task.status === 'active' && '进行中'}
              {task.status === 'pending' && '待开始'}
              {task.status === 'completed' && '已完成'}
              {task.status === 'cancelled' && '已取消'}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
});
