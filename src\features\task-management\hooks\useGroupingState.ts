import { useState, useCallback, useRef } from 'react';
import type { TaskGroupConfig } from '@/core/types';

const initialGroupConfig: TaskGroupConfig = {
  groupBy: 'none',
  enabled: false,
  collapsible: true,
  defaultCollapsed: [],
  sortOrder: 'asc',
  showGroupStats: true,
  allowedGroupColumns: ['strength', 'constructionUnit', 'projectName'],
  disallowedGroupColumns: [],
  groupHeaderStyle: {
    backgroundColor: 'bg-gray-100',
    textColor: 'text-gray-900',
    fontSize: 'text-sm',
    fontWeight: 'font-medium',
    padding: 'py-2',
  },
};

export function useGroupingState() {
  const [groupConfig, setGroupConfig] = useState<TaskGroupConfig>(initialGroupConfig);
  const updateCountRef = useRef(0);

  const updateGroupConfig = useCallback((newConfig: Partial<TaskGroupConfig>) => {
    updateCountRef.current += 1;
    console.log('🔍 useGroupingState 更新:', {
      updateCount: updateCountRef.current,
      newConfig,
      timestamp: new Date().toLocaleTimeString(),
    });

    setGroupConfig(prev => ({
      ...prev,
      ...newConfig,
    }));
  }, []);

  const setGroupBy = useCallback(
    (groupBy: TaskGroupConfig['groupBy']) => {
      updateGroupConfig({
        groupBy,
        enabled: groupBy !== 'none',
        defaultCollapsed: [], // 清空折叠状态
      });
    },
    [updateGroupConfig]
  );

  const toggleGrouping = useCallback(() => {
    updateGroupConfig({
      enabled: !groupConfig.enabled,
    });
  }, [groupConfig.enabled, updateGroupConfig]);

  const toggleCollapse = useCallback(
    (groupKey: string) => {
      updateGroupConfig({
        defaultCollapsed: groupConfig.defaultCollapsed.includes(groupKey)
          ? groupConfig.defaultCollapsed.filter(key => key !== groupKey)
          : [...groupConfig.defaultCollapsed, groupKey],
      });
    },
    [groupConfig.defaultCollapsed, updateGroupConfig]
  );

  return {
    groupConfig,
    updateGroupConfig,
    setGroupBy,
    toggleGrouping,
    toggleCollapse,
    updateCount: updateCountRef.current,
  };
}
