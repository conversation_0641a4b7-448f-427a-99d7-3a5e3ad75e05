/**
 * 配比页面容器组件
 * 统一管理所有配比相关的子组件和状态
 */

import React, { useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useToast } from '@/shared/hooks/use-toast';
import { useRatioStoreOrchestrator } from '@/features/ratio-management/store/ratioStoreOrchestrator';
import { useRatioCalculation } from '@/features/ratio-management/hooks/ratio/useRatioCalculation';
import { useRatioMaterials } from '@/features/ratio-management/hooks/ratio/useRatioMaterials';
import { useSimpleRatioModals } from '@/features/ratio-management/hooks/ratio/useRatioModals';
import { generateMockTasks } from '@/infrastructure/api/mock/mock-data';
import {
  RatioActionBar,
  TaskInfoPanel,
  CalculationPanel,
  ProportioningTable,
  MaterialsSidebar,
  RatioFooter,
} from './index';

// 导入模态框组件
import { MortarRatioModal } from '@/models/MortarRatioModal';
import { RatioHistoryModal } from '@/models/RatioHistoryModal';
import { RatioSelectionModal } from '@/models/RatioSelectionModal';
import { RatioSettingsModal } from '@/models/RatioSettingsModal';
import { SiloManagementModal } from '@/models/SiloManagementModal';

// 导入配比数据
import {
  generateMockRatioHistory,
  generateMockRatioSelectionRecords,
} from '@/infrastructure/api/mock/ratio-mock-data';
import { generateMockSiloMappings } from '@/infrastructure/api/mock/mock-data';

/**
 * 配比页面容器组件Props
 */
export interface RatioPageContainerProps {
  taskId: string;
}

/**
 * 配比页面容器组件
 */
export const RatioPageContainer = React.memo<RatioPageContainerProps>(function RatioPageContainer({
  taskId,
}) {
  const { toast } = useToast();

  // 获取状态管理
  const orchestrator = useRatioStoreOrchestrator();
  const calculation = useRatioCalculation();
  const materials = useRatioMaterials();
  const modals = useSimpleRatioModals();

  // 调试信息（开发环境）
  if (process.env.NODE_ENV === 'development') {
    console.log('RatioPageContainer 渲染:', {
      taskId,
      isInitialized: orchestrator.data.isInitialized,
      isLoading: orchestrator.ui.isLoading,
      error: orchestrator.ui.error,
      activeModal: modals.activeModal,
    });
  }

  // 初始化页面数据
  useEffect(() => {
    const initializeData = async () => {
      try {
        // 获取任务数据
        const tasks = generateMockTasks();
        const task = tasks.find(t => t.id === taskId) || tasks[2];

        if (task) {
          // 初始化配比数据
          orchestrator.actions.initialize(task);
        } else {
          throw new Error('任务不存在');
        }
      } catch (error) {
        console.error('初始化配比页面失败:', error);
        orchestrator.actions.setError(error instanceof Error ? error.message : '初始化失败');
      }
    };

    initializeData();
  }, [taskId]); // 只依赖 taskId，避免 orchestrator.actions 导致的重复初始化

  // 处理保存操作
  const handleSave = async () => {
    try {
      const success = await orchestrator.saveRatio();
      if (success) {
        toast({
          title: '保存成功',
          description: '配比数据已成功保存',
        });
      }
    } catch (error) {
      toast({
        title: '保存失败',
        description: error instanceof Error ? error.message : '保存失败',
        variant: 'destructive',
      });
    }
  };

  // 处理提交操作
  const handleSubmit = async () => {
    try {
      const success = await orchestrator.submitRatio();
      if (success) {
        toast({
          title: '提交成功',
          description: '配比已成功提交审核',
        });
      }
    } catch (error) {
      toast({
        title: '提交失败',
        description: error instanceof Error ? error.message : '提交失败',
        variant: 'destructive',
      });
    }
  };

  // 处理退出操作
  const handleExit = () => {
    // 返回任务列表或上一页
    window.history.back();
  };

  // 处理配比选择
  const handleSelectRatio = (ratio: any) => {
    console.log('应用配比:', ratio);
    toast({
      title: '配比应用成功',
      description: `已成功应用配比 ${ratio.ratioNumber}`,
    });
    modals.closeModal();
  };

  // 如果未初始化，显示加载状态
  if (!orchestrator.data.isInitialized || orchestrator.ui.isLoading) {
    return (
      <div className='h-screen w-screen bg-muted/20 p-2 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2'></div>
          <p className='text-lg animate-pulse'>加载配比数据...</p>
        </div>
      </div>
    );
  }

  // 如果有错误，显示错误状态
  if (orchestrator.ui.error) {
    return (
      <div className='h-screen w-screen bg-muted/20 p-2 flex items-center justify-center'>
        <div className='text-center'>
          <div className='text-red-500 text-lg mb-2'>加载失败</div>
          <p className='text-muted-foreground'>{orchestrator.ui.error}</p>
          <button
            onClick={() => window.location.reload()}
            className='mt-4 px-4 py-2 bg-primary text-primary-foreground rounded'
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className='h-screen w-screen bg-muted/20 p-2 flex flex-col gap-2'>
      {/* 顶部操作栏 */}
      <RatioActionBar onOpenModal={modals.openModal} taskId={taskId} />

      {/* 主要内容区域 */}
      <main className='flex-1 grid grid-cols-[3fr_1fr] gap-2 overflow-hidden'>
        {/* 左侧：主要配比内容 */}
        <div className='flex flex-col gap-2 overflow-y-auto custom-thin-scrollbar'>
          {/* 任务信息面板 */}
          <TaskInfoPanel task={orchestrator.data.task} />

          {/* 计算面板 */}
          <CalculationPanel
            calculationParams={calculation.calculationParams}
            proportions={calculation.proportions}
            calculationMethod={calculation.calculationMethod}
            reverseParams={calculation.reverseParams}
            onParamsChange={calculation.setCalculationParams}
            onReverseParamChange={calculation.setReverseParam}
            onMethodChange={calculation.setCalculationMethod}
            onCalculate={calculation.calculate}
            onReverseCalculate={calculation.reverseCalculate}
          />

          {/* 配比表格 */}
          <ProportioningTable
            materials={materials.materials}
            onOpenModal={modals.openModal}
            onAddMaterial={materials.addMaterial}
            onUpdateMaterial={materials.updateMaterial}
            onDeleteMaterial={materials.deleteMaterial}
          />
        </div>

        {/* 右侧：物料侧边栏 */}
        <div className='overflow-y-auto custom-thin-scrollbar'>
          <MaterialsSidebar />
        </div>
      </main>

      {/* 底部操作区域 */}
      <RatioFooter
        onSave={handleSave}
        onSubmit={handleSubmit}
        onExit={handleExit}
        isDirty={orchestrator.ui.isDirty}
        isSaving={orchestrator.ui.isSaving}
      />

      {/* 模态框组件 */}
      <SiloManagementModal
        isOpen={modals.activeModal === 'silo'}
        onOpenChangeAction={isOpen => !isOpen && modals.closeModal()}
        siloMappings={generateMockSiloMappings()}
      />

      <RatioHistoryModal
        isOpen={modals.activeModal === 'history'}
        onOpenChangeAction={isOpen => !isOpen && modals.closeModal()}
        task={orchestrator.data.task}
        history={generateMockRatioHistory(taskId)}
      />

      <MortarRatioModal
        isOpen={modals.activeModal === 'mortar'}
        onOpenChangeAction={isOpen => !isOpen && modals.closeModal()}
      />

      <RatioSettingsModal
        isOpen={modals.activeModal === 'settings'}
        onOpenChangeAction={isOpen => !isOpen && modals.closeModal()}
      />

      <RatioSelectionModal
        isOpen={modals.activeModal === 'ratioSelection'}
        onOpenChange={isOpen => !isOpen && modals.closeModal()}
        onSelectRatio={handleSelectRatio}
        ratioRecords={generateMockRatioSelectionRecords()}
        currentTaskId={taskId}
      />
    </div>
  );
});
