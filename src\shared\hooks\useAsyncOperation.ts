/**
 * 通用异步操作Hook
 * 提供统一的异步操作状态管理和错误处理
 */

import { useState, useCallback } from 'react';
import { useToast } from '@/shared/hooks/use-toast';

interface UseAsyncOperationOptions {
  /** 成功时的提示消息 */
  successMessage?: string;
  /** 错误时的提示消息 */
  errorMessage?: string;
  /** 是否显示成功提示 */
  showSuccessToast?: boolean;
  /** 是否显示错误提示 */
  showErrorToast?: boolean;
  /** 成功回调 */
  onSuccess?: (result: any) => void;
  /** 错误回调 */
  onError?: (error: Error) => void;
}

/**
 * 通用异步操作Hook
 */
export function useAsyncOperation(options: UseAsyncOperationOptions = {}) {
  const {
    successMessage = '操作成功',
    errorMessage = '操作失败',
    showSuccessToast = true,
    showErrorToast = true,
    onSuccess,
    onError,
  } = options;

  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<any>(null);

  /**
   * 执行异步操作
   */
  const execute = useCallback(
    async <T>(operation: () => Promise<T>): Promise<T | null> => {
      try {
        setIsLoading(true);
        setError(null);

        const result = await operation();
        setData(result);

        if (showSuccessToast) {
          toast({
            title: '成功',
            description: successMessage,
          });
        }

        onSuccess?.(result);
        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error('未知错误');
        setError(error);

        if (showErrorToast) {
          toast({
            title: '错误',
            description: error.message || errorMessage,
            variant: 'destructive',
          });
        }

        onError?.(error);
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [successMessage, errorMessage, showSuccessToast, showErrorToast, onSuccess, onError, toast]
  );

  /**
   * 重置状态
   */
  const reset = useCallback(() => {
    setIsLoading(false);
    setError(null);
    setData(null);
  }, []);

  /**
   * 清除错误
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    isLoading,
    error,
    data,
    execute,
    reset,
    clearError,
  };
}
