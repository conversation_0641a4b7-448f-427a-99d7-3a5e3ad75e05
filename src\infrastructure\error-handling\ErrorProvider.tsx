/**
 * 全局错误提供者
 * 为整个应用提供统一的错误处理上下文和配置
 */

'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
// import { toast } from 'sonner';
const toast = {
  error: (message: string, options?: any) => console.error(message),
  warning: (message: string, options?: any) => console.warn(message),
  info: (message: string, options?: any) => console.info(message),
}; // Placeholder

import { EnhancedErrorBoundary } from '@/shared/components/error/EnhancedErrorBoundary';
import { errorManager } from '@/infrastructure/error-handling/ErrorManager';
import { AppError, ErrorCategory, ErrorSeverity } from '@/core/types/error';

interface ErrorProviderConfig {
  enableToastNotifications?: boolean;
  enableErrorReporting?: boolean;
  reportingEndpoint?: string;
  maxRetries?: number;
  retryDelay?: number;
  enableAutoRecovery?: boolean;
  enableNetworkMonitoring?: boolean;
}

interface ErrorContextValue {
  config: ErrorProviderConfig;
  updateConfig: (newConfig: Partial<ErrorProviderConfig>) => void;
  reportError: (error: AppError) => void;
  clearErrors: () => void;
  isOnline: boolean;
}

const ErrorContext = createContext<ErrorContextValue | undefined>(undefined);

interface ErrorProviderProps {
  children: React.ReactNode;
  config?: ErrorProviderConfig;
}

const defaultConfig: ErrorProviderConfig = {
  enableToastNotifications: true,
  enableErrorReporting: true,
  maxRetries: 3,
  retryDelay: 1000,
  enableAutoRecovery: true,
  enableNetworkMonitoring: true,
};

export function ErrorProvider({ children, config: initialConfig }: ErrorProviderProps) {
  const [config, setConfig] = useState<ErrorProviderConfig>({
    ...defaultConfig,
    ...initialConfig,
  });
  const [isOnline, setIsOnline] = useState(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  );

  useEffect(() => {
    // 配置错误管理器
    if (config.reportingEndpoint) {
      errorManager.setReportingEndpoint(config.reportingEndpoint);
    }

    // 监听错误事件并显示通知
    const unsubscribe = errorManager.onError((error: AppError) => {
      if (config.enableToastNotifications) {
        toast.error(error.message, {
          description:
            error.context?.['userMessage'] || error.context?.['technicalDetails'] || error.code,
        });
      }
    });

    return unsubscribe;
  }, [config]);

  useEffect(() => {
    if (!config.enableNetworkMonitoring || typeof window === 'undefined') return;

    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [config.enableNetworkMonitoring]);

  const updateConfig = (newConfig: Partial<ErrorProviderConfig>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
  };

  const reportError = (error: AppError) => {
    errorManager.handleError(error);
  };

  const clearErrors = () => {
    errorManager.clearHistory();
  };

  const contextValue: ErrorContextValue = {
    config,
    updateConfig,
    reportError,
    clearErrors,
    isOnline,
  };

  return (
    <ErrorContext.Provider value={contextValue}>
      <EnhancedErrorBoundary
        level='page'
        feature='Application'
        enableRecovery={config.enableAutoRecovery}
        maxRetries={config.maxRetries}
        onError={reportError}
      >
        {children}
      </EnhancedErrorBoundary>
    </ErrorContext.Provider>
  );
}

/**
 * 使用错误上下文的Hook
 */
export function useErrorContext(): ErrorContextValue {
  const context = useContext(ErrorContext);
  if (context === undefined) {
    throw new Error('useErrorContext must be used within an ErrorProvider');
  }
  return context;
}

/**
 * 错误边界HOC，用于包装页面级组件
 */
export function withPageErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  feature?: string
) {
  const EnhancedComponent = (props: P) => (
    <EnhancedErrorBoundary level='page' feature={feature} enableRecovery={true} maxRetries={3}>
      {React.createElement(WrappedComponent, props)}
    </EnhancedErrorBoundary>
  );

  EnhancedComponent.displayName = `withPageErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;
  return EnhancedComponent;
}

/**
 * 错误边界HOC，用于包装组件级组件
 */
export function withComponentErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  feature?: string
) {
  const EnhancedComponent = (props: P) => (
    <EnhancedErrorBoundary
      level='component'
      feature={feature}
      enableRecovery={false}
      maxRetries={1}
    >
      {React.createElement(WrappedComponent, props)}
    </EnhancedErrorBoundary>
  );

  EnhancedComponent.displayName = `withComponentErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;
  return EnhancedComponent;
}

/**
 * 全局错误处理器设置
 */
export function setupGlobalErrorHandling(config?: ErrorProviderConfig) {
  // 设置全局未捕获错误处理
  window.addEventListener('error', event => {
    const error = AppError.system(`全局错误: ${event.message}`, {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack,
    });
    errorManager.handleError(error);
  });

  // 设置Promise rejection处理
  window.addEventListener('unhandledrejection', event => {
    const error = AppError.system(`未处理的Promise rejection: ${event.reason}`, {
      reason: event.reason,
      promise: event.promise,
    });
    errorManager.handleError(error);
    event.preventDefault();
  });

  // 设置资源加载错误处理
  window.addEventListener(
    'error',
    event => {
      if (event.target !== window) {
        const target = event.target as any;
        const error = AppError.network(`资源加载失败: ${target.src || target.href}`, {
          tagName: target.tagName,
          src: target.src,
          href: target.href,
        });
        errorManager.handleError(error);
      }
    },
    true
  );

  console.log('Global error handling setup completed');
}

/**
 * 错误恢复工具函数
 */
export const errorRecoveryUtils = {
  /**
   * 重试操作
   */
  async retry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        if (attempt === maxRetries) {
          throw AppError.system(`操作失败，已重试${maxRetries}次`, {
            originalError: lastError,
            attempts: attempt,
          });
        }

        // 等待重试延迟
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }

    throw lastError!;
  },

  /**
   * 带超时的操作
   */
  async withTimeout<T>(operation: () => Promise<T>, timeoutMs: number = 10000): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(AppError.system(`操作超时 (${timeoutMs}ms)`));
      }, timeoutMs);
    });

    return Promise.race([operation(), timeoutPromise]);
  },

  /**
   * 安全执行操作
   */
  async safeExecute<T>(operation: () => Promise<T>, fallback?: T): Promise<T | undefined> {
    try {
      return await operation();
    } catch (error) {
      errorManager.handleError(error as AppError);
      return fallback;
    }
  },
};
