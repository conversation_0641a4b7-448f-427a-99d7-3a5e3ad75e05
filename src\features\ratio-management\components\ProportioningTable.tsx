/**
 * 配比表格组件
 * 显示和编辑配比材料信息
 */

import React, { useCallback, useMemo } from 'react';
import { Layers, PlusCircle, X } from 'lucide-react';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/table';
import { EditableCell } from './EditableCell';
import { mockMaterials } from '@/infrastructure/api/mock/mock-data';
import { useToast } from '@/shared/hooks/use-toast';
import type { ProportioningTableProps, UnifiedRatioMaterial } from '@/core/types/ratio';

/**
 * 配比表格组件
 */
export const ProportioningTable = React.memo<ProportioningTableProps>(function ProportioningTable({
  materials,
  onOpenModal,
  onAddMaterial,
  onUpdateMaterial,
  onDeleteMaterial,
}) {
  const { toast } = useToast();

  // 获取唯一的材料类型
  const uniqueMaterialTypes = useMemo(() => {
    return [...new Map(mockMaterials.map(item => [(item as any).materialType, item])).values()];
  }, []);

  // 处理添加材料
  const handleAddMaterial = useCallback(() => {
    try {
      onAddMaterial();
    } catch (error) {
      toast({
        title: '操作无效',
        description: '请先设置当前空的材料行，再添加新行。',
        variant: 'destructive',
      });
    }
  }, [onAddMaterial, toast]);

  // 处理材料更新
  const handleUpdate = useCallback(
    (id: string, newMaterial: Partial<UnifiedRatioMaterial>) => {
      onUpdateMaterial(id, newMaterial);
    },
    [onUpdateMaterial]
  );

  // 处理材料删除
  const handleDelete = useCallback(
    (id: string) => {
      onDeleteMaterial(id);
    },
    [onDeleteMaterial]
  );

  // 处理材料类型选择
  const handleMaterialTypeChange = useCallback(
    (materialId: string, materialType: string) => {
      const selectedMat = mockMaterials.find(m => m.materialType === materialType);
      if (selectedMat) {
        handleUpdate(materialId, {
          name: materialType,
          specification: selectedMat.spec,
          siloName: selectedMat.storageBin || '',
          density: selectedMat.density || 2650,
        });
      }
    },
    [handleUpdate]
  );

  // 处理规格选择
  const handleSpecificationChange = useCallback(
    (materialId: string, materialType: string, spec: string) => {
      const selectedMat = mockMaterials.find(
        m => m.materialType === materialType && m.spec === spec
      );
      if (selectedMat) {
        handleUpdate(materialId, {
          specification: spec,
          siloName: selectedMat.storageBin || '',
          density: selectedMat.density || 2650,
        });
      }
    },
    [handleUpdate]
  );

  return (
    <Card className='flex-shrink-0'>
      <CardHeader className='px-1 py-1 flex-shrink-0 flex flex-row items-center justify-between'>
        <CardTitle className='text-base flex items-center gap-2'>
          <Layers className='w-5 h-5 text-primary' />
          配比
        </CardTitle>

        <div className='flex items-center gap-1'>
          <Button
            variant='outline'
            size='sm'
            className='text-xs h-7'
            onClick={() => onOpenModal('ratioSelection')}
          >
            选用配比
          </Button>

          <Button variant='outline' size='sm' className='text-xs h-7'>
            检查标准
          </Button>

          <Button variant='outline' size='sm' className='text-xs h-7'>
            应用通知单
          </Button>

          <Button variant='outline' size='sm' className='text-xs h-7'>
            保存为备选
          </Button>
        </div>
      </CardHeader>

      <CardContent className='p-0'>
        <Table>
          <TableHeader className='py-0 px-1'>
            <TableRow className='py-0 px-1'>
              {[
                '材料名称',
                '规格',
                '理论量',
                '含水率',
                '含石率',
                '实际量',
                '设计值',
                '罐名',
                '操作',
              ].map(h => (
                <TableHead key={h} className='text-xs h-4 p-2'>
                  {h}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>

          <TableBody>
            {materials.map(mat => (
              <TableRow key={mat.id} className='text-xs h-9'>
                {/* 材料名称 */}
                <TableCell className='p-1'>
                  <Select
                    value={mat.name}
                    onValueChange={val => handleMaterialTypeChange(mat.id, val)}
                  >
                    <SelectTrigger className='h-7 text-xs'>
                      <SelectValue placeholder='选择材料' />
                    </SelectTrigger>
                    <SelectContent>
                      {uniqueMaterialTypes.map(m => (
                        <SelectItem key={m.id} value={m.materialType}>
                          {m.materialType}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </TableCell>

                {/* 规格 */}
                <TableCell className='p-1'>
                  <Select
                    value={mat.specification}
                    onValueChange={val => handleSpecificationChange(mat.id, mat.name, val)}
                  >
                    <SelectTrigger className='h-7 text-xs'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {mockMaterials
                        .filter(m => m.materialType === mat.name)
                        .map(m => (
                          <SelectItem key={`${m.id}-spec`} value={m.spec}>
                            {m.spec}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </TableCell>

                {/* 理论量 */}
                <TableCell className='p-1'>
                  <EditableCell
                    value={mat.theoreticalAmount}
                    onUpdate={val => handleUpdate(mat.id, { theoreticalAmount: val })}
                    min={0}
                    max={10000}
                    step={1}
                    precision={1}
                  />
                </TableCell>

                {/* 含水率 */}
                <TableCell className='p-1'>
                  <EditableCell
                    value={mat.waterContent}
                    onUpdate={val => handleUpdate(mat.id, { waterContent: val })}
                    min={0}
                    max={100}
                    step={0.1}
                    precision={1}
                  />
                </TableCell>

                {/* 含石率 */}
                <TableCell className='p-1'>
                  <EditableCell
                    value={mat.stoneContent}
                    onUpdate={val => handleUpdate(mat.id, { stoneContent: val })}
                    min={0}
                    max={100}
                    step={0.1}
                    precision={1}
                  />
                </TableCell>

                {/* 实际量 */}
                <TableCell className='p-1 font-medium'>{mat.actualAmount.toFixed(2)}</TableCell>

                {/* 设计值 */}
                <TableCell className='p-1 font-bold text-red-600'>
                  {mat.designValue.toFixed(2)}
                </TableCell>

                {/* 罐名 */}
                <TableCell className='p-1 text-xs'>{mat.siloName}</TableCell>

                {/* 操作 */}
                <TableCell className='p-1'>
                  <Button
                    variant='ghost'
                    size='icon'
                    className='h-6 w-6'
                    onClick={() => handleDelete(mat.id)}
                  >
                    <X className='h-4 w-4' />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>

          <TableFooter>
            <TableRow>
              <TableCell colSpan={9} className='p-1'>
                <Button
                  variant='outline'
                  size='sm'
                  className='w-full h-7 text-xs flex items-center gap-1'
                  onClick={handleAddMaterial}
                >
                  <PlusCircle className='w-3 h-3' />
                  新增材料
                </Button>
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </CardContent>
    </Card>
  );
});
