/**
 * 固定列配置示例
 * 展示如何在实际项目中配置和使用固定列功能
 */
import {
  ShadowConfig,
  StickyColumnConfig,
  StickyColumnContext,
  StickyColumnPosition,
  ZIndexLevels,
} from '../types/sticky-columns';
import {
  calculateLeftOffset,
  calculateRightOffset,
  StickyColumnUtils,
} from '../utils/sticky-columns';

/**
 * 任务调度表格的固定列配置
 */
export const TASK_TABLE_STICKY_CONFIG = {
  // 左固定列配置
  leftColumns: [
    {
      columnId: 'vehicle-id',
      position: StickyColumnPosition.LEFT,
      order: 0,
      minWidth: 120,
      maxWidth: 180,
      showShadow: false, // 不是最后一列，不显示阴影
      priority: 'high' as const,
    },
    {
      columnId: 'driver-name',
      position: StickyColumnPosition.LEFT,
      order: 1,
      minWidth: 100,
      maxWidth: 150,
      showShadow: false,
      priority: 'medium' as const,
    },
    {
      columnId: 'departure-reminder',
      position: StickyColumnPosition.LEFT,
      order: 2,
      minWidth: 80,
      maxWidth: 120,
      showShadow: true, // 最后一个左固定列，显示右阴影
      priority: 'low' as const,
    },
  ] as StickyColumnConfig[],

  // 右固定列配置
  rightColumns: [
    {
      columnId: 'dispatch-vehicle',
      position: StickyColumnPosition.RIGHT,
      order: 0,
      minWidth: 100,
      maxWidth: 150,
      showShadow: true, // 第一个右固定列，显示左阴影
      priority: 'high' as const,
    },
    {
      columnId: 'actions',
      position: StickyColumnPosition.RIGHT,
      order: 1,
      minWidth: 80,
      maxWidth: 120,
      showShadow: false,
      priority: 'high' as const,
    },
  ] as StickyColumnConfig[],
};

/**
 * 阴影配置
 */
export const SHADOW_CONFIGS = {
  // 默认阴影配置
  default: {
    right: {
      offsetX: 8,
      offsetY: 0,
      blur: 15,
      spread: -3,
      color: 'rgba(0, 0, 0, 0.3)',
    } as ShadowConfig,
    left: {
      offsetX: -8,
      offsetY: 0,
      blur: 15,
      spread: -3,
      color: 'rgba(0, 0, 0, 0.3)',
    } as ShadowConfig,
  },

  // 移动端阴影配置
  mobile: {
    right: {
      offsetX: 6,
      offsetY: 0,
      blur: 12,
      spread: -2,
      color: 'rgba(0, 0, 0, 0.25)',
    } as ShadowConfig,
    left: {
      offsetX: -6,
      offsetY: 0,
      blur: 12,
      spread: -2,
      color: 'rgba(0, 0, 0, 0.25)',
    } as ShadowConfig,
  },

  // 深色主题阴影配置
  dark: {
    right: {
      offsetX: 8,
      offsetY: 0,
      blur: 15,
      spread: -3,
      color: 'rgba(0, 0, 0, 0.5)',
    } as ShadowConfig,
    left: {
      offsetX: -8,
      offsetY: 0,
      blur: 15,
      spread: -3,
      color: 'rgba(0, 0, 0, 0.5)',
    } as ShadowConfig,
  },
};

/**
 * 响应式断点配置
 */
export const RESPONSIVE_BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
};

/**
 * 根据屏幕尺寸获取适配的固定列配置
 * @param screenWidth 屏幕宽度
 * @returns 适配后的固定列配置
 */
export function getResponsiveStickyConfig(screenWidth: number) {
  const { leftColumns, rightColumns } = TASK_TABLE_STICKY_CONFIG;

  if (screenWidth <= RESPONSIVE_BREAKPOINTS.mobile) {
    // 移动端：只保留最重要的固定列
    return {
      leftColumns: leftColumns.filter(col => col.priority === 'high'),
      rightColumns: rightColumns.filter(col => col.priority === 'high'),
    };
  }

  if (screenWidth <= RESPONSIVE_BREAKPOINTS.tablet) {
    // 平板端：保留高优先级和中优先级的固定列
    return {
      leftColumns: leftColumns.filter(col => col.priority === 'high' || col.priority === 'medium'),
      rightColumns: rightColumns.filter(col => col.priority === 'high'),
    };
  }

  // 桌面端：保留所有固定列
  return { leftColumns, rightColumns };
}

/**
 * 根据主题获取阴影配置
 * @param theme 主题名称
 * @param isMobile 是否为移动端
 * @returns 阴影配置
 */
export function getThemeShadowConfig(theme: 'light' | 'dark' = 'light', isMobile: boolean = false) {
  if (isMobile) {
    return SHADOW_CONFIGS.mobile;
  }

  return theme === 'dark' ? SHADOW_CONFIGS.dark : SHADOW_CONFIGS.default;
}

/**
 * 创建固定列上下文
 * @param screenWidth 屏幕宽度
 * @param theme 主题
 * @param getColumnWidth 获取列宽度的函数
 * @returns 固定列上下文
 */
export function createStickyColumnContext(
  screenWidth: number,
  theme: 'light' | 'dark' = 'light',
  getColumnWidth: (columnId: string) => number
): StickyColumnContext {
  const isMobile = screenWidth <= RESPONSIVE_BREAKPOINTS.mobile;
  const { leftColumns, rightColumns } = getResponsiveStickyConfig(screenWidth);
  const shadowConfigs = getThemeShadowConfig(theme, isMobile);

  return {
    // 基础配置
    leftColumns,
    rightColumns,
    leftFixedColumns: leftColumns,
    rightFixedColumns: rightColumns,

    shadowConfigs: {
      [StickyColumnPosition.LEFT]: shadowConfigs.left,
      [StickyColumnPosition.RIGHT]: shadowConfigs.right,
      [StickyColumnPosition.NONE]: {
        offsetX: 0,
        offsetY: 0,
        blur: 0,
        spread: 0,
        color: 'transparent',
      },
    },
    screenWidth,
    isMobile,
    theme,
    getColumnWidth,

    // 工具方法
    calculateLeftOffset: (columnId: string) =>
      StickyColumnUtils.calculateLeftOffset(columnId, leftColumns, getColumnWidth),

    calculateRightOffset: (columnId: string) =>
      StickyColumnUtils.calculateRightOffset(columnId, rightColumns, getColumnWidth),

    isLastLeftFixed: (columnId: string) => StickyColumnUtils.isLastLeftFixed(columnId, leftColumns),

    isFirstRightFixed: (columnId: string) =>
      StickyColumnUtils.isFirstRightFixed(columnId, rightColumns),

    getStickyStyles: (columnId: string, isHeader: boolean) => {
      const config = [...leftColumns, ...rightColumns].find(c => c.columnId === columnId);
      if (!config) {
        return {
          position: 'sticky',
          zIndex: isHeader ? ZIndexLevels.STICKY_HEADER : ZIndexLevels.STICKY_BODY,
        };
      }

      const offset =
        config.position === StickyColumnPosition.LEFT
          ? StickyColumnUtils.calculateLeftOffset(columnId, leftColumns, getColumnWidth)
          : StickyColumnUtils.calculateRightOffset(columnId, rightColumns, getColumnWidth);

      return StickyColumnUtils.generateStickyStyles(
        config,
        isHeader,
        offset,
        getColumnWidth(columnId)
      );
    },

    updateStickyConfig: (columnId: string, config: Partial<StickyColumnConfig>) => {
      const allColumns = [...leftColumns, ...rightColumns];
      const index = allColumns.findIndex(c => c.columnId === columnId);
      if (index >= 0 && allColumns[index]) {
        allColumns[index] = { ...allColumns[index], ...config } as any;
      }
    },

    generateClassNames: (columnId: string, position: StickyColumnPosition, showShadow?: boolean) =>
      StickyColumnUtils.generateClassNames(columnId, position, showShadow),
  };
}

/**
 * 验证并修复固定列配置
 * @param configs 固定列配置数组
 * @returns 修复后的配置数组
 */
export function validateAndFixStickyConfigs(configs: StickyColumnConfig[]): StickyColumnConfig[] {
  return configs
    .filter(config => StickyColumnUtils.validateStickyConfig(config))
    .sort((a, b) => a.order - b.order)
    .map((config, index) => ({
      ...config,
      order: index, // 重新分配连续的order值
    }));
}

/**
 * 导出默认配置
 */
export default {
  TASK_TABLE_STICKY_CONFIG,
  SHADOW_CONFIGS,
  RESPONSIVE_BREAKPOINTS,
  getResponsiveStickyConfig,
  getThemeShadowConfig,
  createStickyColumnContext,
  validateAndFixStickyConfigs,
};
