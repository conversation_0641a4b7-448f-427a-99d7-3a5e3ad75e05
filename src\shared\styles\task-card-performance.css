/**
 * 任务卡片性能优化样式
 * 专门解决选中状态导致的性能问题
 */

/* 性能优化的卡片容器 */
.performance-optimized-card {
  /* 启用 GPU 加速 */
  transform: translateZ(0);
  /* 优化渲染性能 */
  contain: layout style paint;
  /* 减少重绘区域 */
  isolation: isolate;
}

/* 性能优化的行容器 */
.performance-optimized-row {
  /* 启用 GPU 加速 */
  transform: translateZ(0);
  /* 优化渲染性能 */
  contain: layout style paint;
  /* 提示浏览器优化 */
  will-change: transform;
}

/* 优化的选中状态样式 - 使用 CSS 变量避免重绘 */
.task-card.task-row-selected {
  /* 使用 CSS 变量控制透明度，避免类切换 */
  background-color: hsl(var(--accent) / calc(0.12 * var(--selection-opacity, 1))) !important;
  border: 2px solid hsl(var(--accent) / calc(0.4 * var(--selection-opacity, 1))) !important;
  border-left: 4px solid hsl(var(--accent) / var(--selection-opacity, 1)) !important;

  /* 优化的阴影 - 减少模糊半径 */
  box-shadow: 0 2px 8px hsl(var(--accent) / calc(0.15 * var(--selection-opacity, 1))) !important;

  /* 简化的变换 */
  transform: translateY(calc(-1px * var(--selection-opacity, 1))) !important;

  /* 固定层级 */
  position: relative !important;
  z-index: 10 !important;

  /* 优化渲染性能 */
  will-change: transform, opacity, box-shadow;
  contain: layout style paint;

  /* 简化的过渡动画 */
  transition:
    background-color 0.15s ease-out,
    border-color 0.15s ease-out,
    box-shadow 0.15s ease-out,
    transform 0.15s ease-out !important;
}

/* 移除复杂的脉动动画，只保留初始选中动画 */
@keyframes task-select-optimized {
  0% {
    transform: scale(0.99) translateY(0);
    opacity: 0.9;
  }
  100% {
    transform: scale(1) translateY(-1px);
    opacity: 1;
  }
}

/* 只在初次选中时播放简单动画 */
.task-card.task-row-selected {
  animation: task-select-optimized 0.15s ease-out forwards;
}

/* 悬停状态优化 - 减少动画复杂度 */
.task-card-clickable:hover:not(.task-row-selected) {
  transform: translateY(-0.5px) !important;
  box-shadow: 0 1px 4px hsl(var(--border) / 0.2) !important;
  transition: all 0.1s ease-out !important;
}

/* 选中状态与高亮状态的组合 - 简化处理 */
.task-card.task-row-selected.task-row-highlight {
  background-color: hsl(var(--accent) / 0.18) !important;
  /* 移除复杂的阴影动画 */
  box-shadow: 0 3px 10px hsl(var(--accent) / 0.25) !important;
}

/* 虚拟化行的性能优化 */
.virtual-row-performance {
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* CSS 包含优化 */
  contain: layout style paint;
  /* 减少重绘 */
  backface-visibility: hidden;
  /* 优化合成层 */
  isolation: isolate;
}

/* 卡片内容区域的性能优化 */
.task-card .card-content-optimized {
  /* 避免不必要的重绘 */
  contain: layout style;
  /* 启用硬件加速 */
  transform: translateZ(0);
}

/* 车辆区域的性能优化 */
.task-card .vehicle-area-optimized {
  /* 固定高度避免布局抖动 */
  height: 75px;
  /* 优化滚动性能 */
  overflow-y: auto;
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化滚动 */
  -webkit-overflow-scrolling: touch;
  /* 减少重绘 */
  contain: layout style paint;
}

/* 生产线面板的性能优化 */
.production-panel-optimized {
  /* 固定定位避免重排 */
  position: absolute;
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化渲染 */
  contain: layout style paint;
  /* 减少重绘区域 */
  isolation: isolate;
}

/* 消息图标的性能优化 */
.message-icon-optimized {
  /* 避免重绘 */
  contain: layout style paint;
  /* 启用硬件加速 */
  transform: translateZ(0);
}

/* 状态徽章的性能优化 */
.status-badge-optimized {
  /* 固定尺寸避免布局抖动 */
  min-width: 60px;
  /* 避免重绘 */
  contain: layout style paint;
}

/* 进度条的性能优化 */
.progress-bar-optimized {
  /* 使用 transform 代替 width 变化 */
  transform-origin: left center;
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化动画 */
  will-change: transform;
}

/* 拖拽状态的性能优化 */
.task-card.drag-over-optimized {
  /* 简化拖拽高亮效果 */
  outline: 2px solid hsl(var(--primary) / 0.6);
  outline-offset: 2px;
  /* 避免使用 box-shadow 减少重绘 */
  background-color: hsl(var(--primary) / 0.05);
  /* 简化过渡 */
  transition:
    outline-color 0.1s ease-out,
    background-color 0.1s ease-out;
}

/* 响应式优化 - 减少媒体查询的复杂度 */
@media (max-width: 768px) {
  .performance-optimized-card {
    /* 移动端进一步简化动画 */
    transition: none !important;
    animation: none !important;
  }

  .task-card.task-row-selected {
    /* 移动端简化选中效果 */
    transform: none !important;
    box-shadow: 0 1px 3px hsl(var(--accent) / 0.2) !important;
  }
}

/* 高对比度模式优化 */
@media (prefers-contrast: high) {
  .task-card.task-row-selected {
    /* 高对比度下增强边框 */
    border-width: 3px !important;
    border-left-width: 5px !important;
  }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  .performance-optimized-card,
  .task-card.task-row-selected,
  .task-card-clickable {
    /* 移除所有动画 */
    animation: none !important;
    transition: none !important;
  }
}

/* 快速点击优化样式 */
.fast-click-optimized-card {
  /* 启用 GPU 加速 */
  transform: translateZ(0);
  /* 优化渲染性能 */
  contain: layout style paint;
  /* 减少重绘区域 */
  isolation: isolate;
  /* 快速响应 */
  transition: all 0.1s ease-out;
}

/* 快速点击反馈效果 */
.fast-click-feedback {
  /* 立即的视觉反馈 */
  transform: scale(0.98) translateZ(0) !important;
  opacity: 0.9 !important;
  transition: all 0.05s ease-out !important;
}

/* 快速点击优化的选中状态 */
.fast-click-optimized-card.task-row-selected {
  /* 简化的选中效果，减少重绘 */
  background-color: hsl(var(--accent) / 0.1) !important;
  border: 2px solid hsl(var(--accent) / 0.4) !important;
  border-left: 4px solid hsl(var(--accent)) !important;
  box-shadow: 0 2px 8px hsl(var(--accent) / 0.15) !important;
  transform: translateY(-1px) translateZ(0) !important;
  position: relative !important;
  z-index: 10 !important;

  /* 快速过渡 */
  transition: all 0.1s ease-out !important;

  /* 优化渲染性能 */
  will-change: transform, opacity;
  contain: layout style paint;
}

/* 快速点击时的悬停效果优化 */
.fast-click-optimized-card:hover:not(.task-row-selected) {
  transform: translateY(-0.5px) translateZ(0) !important;
  box-shadow: 0 1px 4px hsl(var(--border) / 0.2) !important;
  transition: all 0.05s ease-out !important;
}

/* 快速点击防抖期间的样式 */
.fast-click-optimized-card.processing {
  pointer-events: none;
  opacity: 0.8;
}

/* 超快速任务卡片优化样式 */
.ultra-fast-task-card {
  /* 最强的 GPU 加速 */
  transform: translate3d(0, 0, 0);
  /* 最强的渲染优化 */
  contain: layout style paint size;
  /* 减少重绘 */
  isolation: isolate;
  /* 优化合成 */
  will-change: transform, opacity;
  /* 超快速过渡 */
  transition: all 0.05s ease-out;
}

/* 超快速反馈效果 */
.ultra-fast-feedback {
  /* 立即的视觉反馈 */
  transform: scale(0.99) translate3d(0, 0, 0) !important;
  opacity: 0.95 !important;
  transition: all 0.03s ease-out !important;
}

/* 使用数据属性选择器避免类名切换的性能开销 */
.ultra-fast-task-card [data-selected='true'] {
  /* 超快速选中效果 */
  background-color: hsl(var(--accent) / 0.1) !important;
  border: 2px solid hsl(var(--accent) / 0.4) !important;
  border-left: 4px solid hsl(var(--accent)) !important;
  box-shadow: 0 2px 8px hsl(var(--accent) / 0.15) !important;
  transform: translateY(-1px) translate3d(0, 0, 0) !important;
  position: relative !important;
  z-index: 10 !important;

  /* 超快速过渡 */
  transition: all 0.05s ease-out !important;

  /* 最强的渲染优化 */
  will-change: transform, opacity, background-color, border-color, box-shadow;
  contain: layout style paint size;
}

/* 超快速悬停效果 */
.ultra-fast-task-card:hover:not([data-selected='true']) {
  transform: translateY(-0.5px) translate3d(0, 0, 0) !important;
  box-shadow: 0 1px 4px hsl(var(--border) / 0.2) !important;
  transition: all 0.03s ease-out !important;
}

/* 内存优化 - 减少不必要的样式计算 */
.ultra-fast-task-card * {
  /* 减少子元素的重绘 */
  contain: layout style;
}

/* 打印样式优化 */
@media print {
  .performance-optimized-card,
  .fast-click-optimized-card,
  .ultra-fast-task-card {
    /* 打印时移除所有效果 */
    transform: none !important;
    box-shadow: none !important;
    animation: none !important;
    transition: none !important;
  }

  .task-card.task-row-selected,
  .ultra-fast-task-card [data-selected='true'] {
    /* 打印时使用简单边框 */
    border: 2px solid #000 !important;
    background-color: #f0f0f0 !important;
  }
}
