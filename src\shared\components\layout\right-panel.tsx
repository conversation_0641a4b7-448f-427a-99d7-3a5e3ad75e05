import React, { useState } from 'react';

import { Button } from '@/shared/components/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';

import { Grid3X3, Settings, X } from 'lucide-react';
import { FixedVehicleDispatchContainer } from '@/features/vehicle-dispatch/components';
import {
  VehicleGridPreset,
  VEHICLE_GRID_PRESETS,
} from '@/features/vehicle-dispatch/components/VehicleGrid';

const MemoizedFixedVehicleDispatch = React.memo(FixedVehicleDispatchContainer);

interface RightPanelProps {
  // 可以添加其他配置props
}

export function RightPanel({}: RightPanelProps) {
  const [gridPreset, setGridPreset] = useState<VehicleGridPreset>('standard');
  const [showSettings, setShowSettings] = useState(false);

  return (
    <div className='h-full p-0.5 pl-0 flex flex-col'>
      {/* 设置栏 - 紧凑版本 */}
      {showSettings && (
        <div className='flex-shrink-0 mb-1'>
          <div className='flex items-center gap-1 p-1 bg-gray-50 border border-gray-200 rounded text-xs'>
            <Grid3X3 className='w-3 h-3' />
            <span className='text-xs'>列数:</span>
            <Select
              value={gridPreset}
              onValueChange={(value: VehicleGridPreset) => setGridPreset(value)}
            >
              <SelectTrigger className='h-5 w-12 text-xs border-0 bg-transparent p-0'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(VEHICLE_GRID_PRESETS).map(([key, preset]) => (
                  <SelectItem key={key} value={key} className='text-xs'>
                    {preset.columns}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => setShowSettings(false)}
              className='h-4 w-4 p-0 ml-auto'
              title='关闭设置'
            >
              <X className='w-2 h-2' />
            </Button>
          </div>
        </div>
      )}

      {/* 车辆调度容器 */}
      <div className='flex-1 min-h-0'>
        <MemoizedFixedVehicleDispatch
          gridColumns={VEHICLE_GRID_PRESETS[gridPreset].columns}
          onOpenSettings={() => setShowSettings(true)}
        />
      </div>
    </div>
  );
}
