/**
 * 类型安全的React Hooks
 * 提供强类型的Hook实现，确保状态和副作用的类型安全
 */

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { validateData, BaseValidator } from './runtime-validator';
import {
  isNotNullish,
  safePromise,
  typedLocalStorage,
  typedSessionStorage,
} from './strict-type-utils';

// ==================== 类型安全的状态Hook ====================

/**
 * 类型安全的useState，支持运行时验证
 */
export function useTypedState<T>(
  initialValue: T,
  validator?: BaseValidator<T>
): [T, (value: T | ((prev: T) => T)) => void] {
  const [state, setState] = useState<T>(initialValue);

  const setValidatedState = useCallback(
    (value: T | ((prev: T) => T)) => {
      setState(prevState => {
        const newValue = typeof value === 'function' ? (value as (prev: T) => T)(prevState) : value;

        if (validator && process.env.NODE_ENV === 'development') {
          try {
            validateData(newValue, validator);
          } catch (error) {
            console.error('状态验证失败:', error);
          }
        }

        return newValue;
      });
    },
    [validator]
  );

  return [state, setValidatedState];
}

/**
 * 类型安全的localStorage状态Hook
 */
export function useTypedLocalStorage<T>(
  key: string,
  defaultValue: T,
  validator?: BaseValidator<T>
): [T, (value: T | ((prev: T) => T)) => void] {
  const [state, setState] = useState<T>(() => {
    const stored = typedLocalStorage.getItem<T>(key);

    if (stored !== null) {
      if (validator) {
        try {
          return validateData(stored, validator);
        } catch {
          // 如果存储的数据无效，使用默认值
          return defaultValue;
        }
      }
      return stored;
    }

    return defaultValue;
  });

  const setStoredState = useCallback(
    (value: T | ((prev: T) => T)) => {
      setState(prevState => {
        const newValue = typeof value === 'function' ? (value as (prev: T) => T)(prevState) : value;

        if (validator && process.env.NODE_ENV === 'development') {
          try {
            validateData(newValue, validator);
          } catch (error) {
            console.error('localStorage状态验证失败:', error);
          }
        }

        typedLocalStorage.setItem(key, newValue);
        return newValue;
      });
    },
    [key, validator]
  );

  return [state, setStoredState];
}

/**
 * 类型安全的sessionStorage状态Hook
 */
export function useTypedSessionStorage<T>(
  key: string,
  defaultValue: T,
  validator?: BaseValidator<T>
): [T, (value: T | ((prev: T) => T)) => void] {
  const [state, setState] = useState<T>(() => {
    const stored = typedSessionStorage.getItem<T>(key);

    if (stored !== null) {
      if (validator) {
        try {
          return validateData(stored, validator);
        } catch {
          return defaultValue;
        }
      }
      return stored;
    }

    return defaultValue;
  });

  const setStoredState = useCallback(
    (value: T | ((prev: T) => T)) => {
      setState(prevState => {
        const newValue = typeof value === 'function' ? (value as (prev: T) => T)(prevState) : value;

        if (validator && process.env.NODE_ENV === 'development') {
          try {
            validateData(newValue, validator);
          } catch (error) {
            console.error('sessionStorage状态验证失败:', error);
          }
        }

        typedSessionStorage.setItem(key, newValue);
        return newValue;
      });
    },
    [key, validator]
  );

  return [state, setStoredState];
}

// ==================== 类型安全的异步Hook ====================

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
}

/**
 * 类型安全的异步数据Hook
 */
export function useTypedAsync<T>(
  asyncFunction: () => Promise<T>,
  dependencies: React.DependencyList = [],
  validator?: BaseValidator<T>
): AsyncState<T> & {
  refetch: () => Promise<void>;
} {
  const [state, setState] = useState<AsyncState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    const [data, error] = await safePromise(asyncFunction());

    if (error) {
      setState(prev => ({ ...prev, loading: false, error }));
    } else {
      let validatedData = data;

      if (validator && process.env.NODE_ENV === 'development') {
        try {
          validatedData = validateData(data, validator);
        } catch (validationError) {
          console.error('异步数据验证失败:', validationError);
          setState(prev => ({
            ...prev,
            loading: false,
            error: new Error('数据验证失败'),
          }));
          return;
        }
      }

      setState(prev => ({ ...prev, loading: false, data: validatedData }));
    }
  }, [asyncFunction, validator]);

  useEffect(() => {
    execute();
  }, dependencies);

  return {
    ...state,
    refetch: execute,
  };
}

// ==================== 类型安全的表单Hook ====================

export interface FormField<T> {
  value: T;
  error: string | null;
  touched: boolean;
}

export interface FormState<T extends Record<string, any>> {
  fields: Record<keyof T, FormField<T[keyof T]>>;
  isValid: boolean;
  isSubmitting: boolean;
  submitError: string | null;
}

export type FormValidators<T extends Record<string, any>> = {
  [K in keyof T]?: BaseValidator<T[K]>;
};

/**
 * 类型安全的表单Hook
 */
export function useTypedForm<T extends Record<string, any>>(
  initialValues: T,
  validators: FormValidators<T> = {}
): {
  state: FormState<T>;
  setValue: <K extends keyof T>(field: K, value: T[K]) => void;
  setTouched: <K extends keyof T>(field: K, touched?: boolean) => void;
  setError: <K extends keyof T>(field: K, error: string | null) => void;
  reset: () => void;
  validate: () => boolean;
  handleSubmit: (
    onSubmit: (values: T) => Promise<void> | void
  ) => (e?: React.FormEvent) => Promise<void>;
} {
  const [state, setState] = useState<FormState<T>>(() => {
    const fields = {} as { [K in keyof T]: FormField<T[K]> };

    for (const key in initialValues) {
      fields[key] = {
        value: initialValues[key],
        error: null,
        touched: false,
      };
    }

    return {
      fields,
      isValid: true,
      isSubmitting: false,
      submitError: null,
    };
  });

  const validateField = useCallback(
    <K extends keyof T>(field: K, value: T[K]): string | null => {
      const validator = validators[field];
      if (!validator) return null;

      try {
        validateData(value, validator);
        return null;
      } catch (error) {
        return error instanceof Error ? error.message : '验证失败';
      }
    },
    [validators]
  );

  const setValue = useCallback(
    <K extends keyof T>(field: K, value: T[K]) => {
      setState(prev => {
        const error = validateField(field, value);
        const newFields = {
          ...prev.fields,
          [field]: {
            ...prev.fields[field],
            value,
            error,
          },
        };

        const isValid = Object.values(newFields).every(f => f.error === null);

        return {
          ...prev,
          fields: newFields,
          isValid,
        };
      });
    },
    [validateField]
  );

  const setTouched = useCallback(<K extends keyof T>(field: K, touched = true) => {
    setState(prev => ({
      ...prev,
      fields: {
        ...prev.fields,
        [field]: {
          ...prev.fields[field],
          touched,
        },
      },
    }));
  }, []);

  const setError = useCallback(<K extends keyof T>(field: K, error: string | null) => {
    setState(prev => {
      const newFields = {
        ...prev.fields,
        [field]: {
          ...prev.fields[field],
          error,
        },
      };

      const isValid = Object.values(newFields).every(f => f.error === null);

      return {
        ...prev,
        fields: newFields,
        isValid,
      };
    });
  }, []);

  const reset = useCallback(() => {
    setState(prev => {
      const fields = {} as { [K in keyof T]: FormField<T[K]> };

      for (const key in initialValues) {
        fields[key] = {
          value: initialValues[key],
          error: null,
          touched: false,
        };
      }

      return {
        ...prev,
        fields,
        isValid: true,
        isSubmitting: false,
        submitError: null,
      };
    });
  }, [initialValues]);

  const validate = useCallback((): boolean => {
    setState(prev => {
      const newFields = { ...prev.fields };
      let isValid = true;

      for (const key in newFields) {
        const error = validateField(key, newFields[key].value);
        newFields[key] = {
          ...newFields[key],
          error,
          touched: true,
        };

        if (error) {
          isValid = false;
        }
      }

      return {
        ...prev,
        fields: newFields,
        isValid,
      };
    });

    return state.isValid;
  }, [validateField, state.isValid]);

  const handleSubmit = useCallback(
    (onSubmit: (values: T) => Promise<void> | void) => {
      return async (e?: React.FormEvent) => {
        if (e) {
          e.preventDefault();
        }

        if (!validate()) {
          return;
        }

        setState(prev => ({ ...prev, isSubmitting: true, submitError: null }));

        try {
          const values = {} as T;
          for (const key in state.fields) {
            (values as any)[key] = state.fields[key].value;
          }

          await onSubmit(values);

          setState(prev => ({ ...prev, isSubmitting: false }));
        } catch (error) {
          setState(prev => ({
            ...prev,
            isSubmitting: false,
            submitError: error instanceof Error ? error.message : '提交失败',
          }));
        }
      };
    },
    [validate, state.fields]
  );

  return {
    state,
    setValue,
    setTouched,
    setError,
    reset,
    validate,
    handleSubmit,
  };
}

// ==================== 类型安全的引用Hook ====================

/**
 * 类型安全的ref Hook，确保ref的类型安全
 */
export function useTypedRef<T>(initialValue: T | null = null): React.MutableRefObject<T | null> {
  return useRef<T | null>(initialValue);
}

/**
 * 类型安全的回调ref Hook
 */
export function useTypedCallbackRef<T>(): [T | null, (node: T | null) => void] {
  const [node, setNode] = useState<T | null>(null);

  const ref = useCallback((node: T | null) => {
    setNode(node);
  }, []);

  return [node, ref];
}

// ==================== 类型安全的记忆化Hook ====================

/**
 * 类型安全的useMemo，确保依赖项的类型安全
 */
export function useTypedMemo<T>(factory: () => T, deps: React.DependencyList): T {
  return useMemo(factory, deps);
}

/**
 * 类型安全的useCallback，确保回调函数的类型安全
 */
export function useTypedCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T {
  return useCallback(callback, deps);
}
