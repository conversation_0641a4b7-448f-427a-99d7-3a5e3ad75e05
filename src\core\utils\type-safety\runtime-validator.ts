/**
 * 运行时类型验证系统
 * 提供强类型的运行时验证，确保数据类型安全
 */

// ==================== 基础验证器类型 ====================

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  data?: any; // 验证通过的数据
}

export interface ValidationError {
  path: string;
  message: string;
  expected: string;
  received: string;
  code: string;
}

export interface ValidationWarning {
  path: string;
  message: string;
  suggestion?: string;
}

export interface ValidatorOptions {
  strict?: boolean; // 严格模式，不允许额外字段
  allowNull?: boolean; // 允许null值
  allowUndefined?: boolean; // 允许undefined值
  coerce?: boolean; // 自动类型转换
  stripUnknown?: boolean; // 移除未知字段
}

// ==================== 基础验证器 ====================

export abstract class BaseValidator<T = any> {
  protected options: ValidatorOptions;

  constructor(options: ValidatorOptions = {}) {
    this.options = {
      strict: false,
      allowNull: false,
      allowUndefined: false,
      coerce: false,
      stripUnknown: false,
      ...options,
    };
  }

  abstract validate(value: unknown, path?: string): ValidationResult;

  protected createError(
    path: string,
    message: string,
    expected: string,
    received: string,
    code: string
  ): ValidationError {
    return {
      path,
      message,
      expected,
      received: typeof received === 'object' ? JSON.stringify(received) : String(received),
      code,
    };
  }

  protected createWarning(path: string, message: string, suggestion?: string): ValidationWarning {
    return {
      path,
      message,
      suggestion,
    };
  }

  protected getTypeName(value: unknown): string {
    if (value === null) return 'null';
    if (Array.isArray(value)) return 'array';
    return typeof value;
  }
}

// ==================== 基础类型验证器 ====================

export class StringValidator extends BaseValidator<string> {
  private minLength?: number;
  private maxLength?: number;
  private pattern?: RegExp;
  private enum?: string[];

  constructor(
    options: ValidatorOptions & {
      minLength?: number;
      maxLength?: number;
      pattern?: RegExp;
      enum?: string[];
    } = {}
  ) {
    super(options);
    this.minLength = options.minLength;
    this.maxLength = options.maxLength;
    this.pattern = options.pattern;
    this.enum = options.enum;
  }

  validate(value: unknown, path = 'root'): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 基础类型检查
    if (value === null && this.options.allowNull) {
      return { isValid: true, errors, warnings, data: value };
    }

    if (value === undefined && this.options.allowUndefined) {
      return { isValid: true, errors, warnings, data: value };
    }

    if (typeof value !== 'string') {
      if (this.options.coerce) {
        value = String(value);
        warnings.push(this.createWarning(path, `值被强制转换为字符串: ${value}`));
      } else {
        errors.push(
          this.createError(
            path,
            '期望字符串类型',
            'string',
            this.getTypeName(value),
            'TYPE_MISMATCH'
          )
        );
        return { isValid: false, errors, warnings };
      }
    }

    const stringValue = value as string;

    // 长度检查
    if (this.minLength !== undefined && stringValue.length < this.minLength) {
      errors.push(
        this.createError(
          path,
          `字符串长度不能少于 ${this.minLength} 个字符`,
          `>= ${this.minLength}`,
          String(stringValue.length),
          'MIN_LENGTH'
        )
      );
    }

    if (this.maxLength !== undefined && stringValue.length > this.maxLength) {
      errors.push(
        this.createError(
          path,
          `字符串长度不能超过 ${this.maxLength} 个字符`,
          `<= ${this.maxLength}`,
          String(stringValue.length),
          'MAX_LENGTH'
        )
      );
    }

    // 模式检查
    if (this.pattern && !this.pattern.test(stringValue)) {
      errors.push(
        this.createError(
          path,
          `字符串不匹配模式 ${this.pattern}`,
          this.pattern.toString(),
          stringValue,
          'PATTERN_MISMATCH'
        )
      );
    }

    // 枚举检查
    if (this.enum && !this.enum.includes(stringValue)) {
      errors.push(
        this.createError(
          path,
          `值必须是以下之一: ${this.enum.join(', ')}`,
          this.enum.join(' | '),
          stringValue,
          'ENUM_MISMATCH'
        )
      );
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      data: stringValue,
    };
  }
}

export class NumberValidator extends BaseValidator<number> {
  private min?: number;
  private max?: number;
  private integer?: boolean;

  constructor(
    options: ValidatorOptions & {
      min?: number;
      max?: number;
      integer?: boolean;
    } = {}
  ) {
    super(options);
    this.min = options.min;
    this.max = options.max;
    this.integer = options.integer;
  }

  validate(value: unknown, path = 'root'): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 基础类型检查
    if (value === null && this.options.allowNull) {
      return { isValid: true, errors, warnings, data: value };
    }

    if (value === undefined && this.options.allowUndefined) {
      return { isValid: true, errors, warnings, data: value };
    }

    if (typeof value !== 'number') {
      if (this.options.coerce) {
        const coerced = Number(value);
        if (isNaN(coerced)) {
          errors.push(
            this.createError(
              path,
              '无法转换为数字',
              'number',
              this.getTypeName(value),
              'COERCION_FAILED'
            )
          );
          return { isValid: false, errors, warnings };
        }
        value = coerced;
        warnings.push(this.createWarning(path, `值被强制转换为数字: ${value}`));
      } else {
        errors.push(
          this.createError(path, '期望数字类型', 'number', this.getTypeName(value), 'TYPE_MISMATCH')
        );
        return { isValid: false, errors, warnings };
      }
    }

    const numberValue = value as number;

    // NaN检查
    if (isNaN(numberValue)) {
      errors.push(this.createError(path, '数字不能是NaN', 'valid number', 'NaN', 'INVALID_NUMBER'));
    }

    // 无穷大检查
    if (!isFinite(numberValue)) {
      errors.push(
        this.createError(path, '数字必须是有限的', 'finite number', 'Infinity', 'INFINITE_NUMBER')
      );
    }

    // 整数检查
    if (this.integer && !Number.isInteger(numberValue)) {
      errors.push(
        this.createError(path, '期望整数', 'integer', String(numberValue), 'NOT_INTEGER')
      );
    }

    // 范围检查
    if (this.min !== undefined && numberValue < this.min) {
      errors.push(
        this.createError(
          path,
          `数字不能小于 ${this.min}`,
          `>= ${this.min}`,
          String(numberValue),
          'MIN_VALUE'
        )
      );
    }

    if (this.max !== undefined && numberValue > this.max) {
      errors.push(
        this.createError(
          path,
          `数字不能大于 ${this.max}`,
          `<= ${this.max}`,
          String(numberValue),
          'MAX_VALUE'
        )
      );
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      data: numberValue,
    };
  }
}

export class BooleanValidator extends BaseValidator<boolean> {
  validate(value: unknown, path = 'root'): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 基础类型检查
    if (value === null && this.options.allowNull) {
      return { isValid: true, errors, warnings, data: value };
    }

    if (value === undefined && this.options.allowUndefined) {
      return { isValid: true, errors, warnings, data: value };
    }

    if (typeof value !== 'boolean') {
      if (this.options.coerce) {
        // 智能布尔转换
        if (value === 'true' || value === 1 || value === '1') {
          value = true;
        } else if (value === 'false' || value === 0 || value === '0') {
          value = false;
        } else {
          value = Boolean(value);
        }
        warnings.push(this.createWarning(path, `值被强制转换为布尔值: ${value}`));
      } else {
        errors.push(
          this.createError(
            path,
            '期望布尔类型',
            'boolean',
            this.getTypeName(value),
            'TYPE_MISMATCH'
          )
        );
        return { isValid: false, errors, warnings };
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      data: value as boolean,
    };
  }
}

// ==================== 复合类型验证器 ====================

export class ArrayValidator<T = any> extends BaseValidator<T[]> {
  private itemValidator?: BaseValidator<T>;
  private minItems?: number;
  private maxItems?: number;
  private uniqueItems?: boolean;

  constructor(
    itemValidator?: BaseValidator<T>,
    options: ValidatorOptions & {
      minItems?: number;
      maxItems?: number;
      uniqueItems?: boolean;
    } = {}
  ) {
    super(options);
    this.itemValidator = itemValidator;
    this.minItems = options.minItems;
    this.maxItems = options.maxItems;
    this.uniqueItems = options.uniqueItems;
  }

  validate(value: unknown, path = 'root'): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 基础类型检查
    if (value === null && this.options.allowNull) {
      return { isValid: true, errors, warnings, data: value };
    }

    if (value === undefined && this.options.allowUndefined) {
      return { isValid: true, errors, warnings, data: value };
    }

    if (!Array.isArray(value)) {
      errors.push(
        this.createError(path, '期望数组类型', 'array', this.getTypeName(value), 'TYPE_MISMATCH')
      );
      return { isValid: false, errors, warnings };
    }

    const arrayValue = value as unknown[];
    const validatedItems: T[] = [];

    // 长度检查
    if (this.minItems !== undefined && arrayValue.length < this.minItems) {
      errors.push(
        this.createError(
          path,
          `数组长度不能少于 ${this.minItems} 个元素`,
          `>= ${this.minItems}`,
          String(arrayValue.length),
          'MIN_ITEMS'
        )
      );
    }

    if (this.maxItems !== undefined && arrayValue.length > this.maxItems) {
      errors.push(
        this.createError(
          path,
          `数组长度不能超过 ${this.maxItems} 个元素`,
          `<= ${this.maxItems}`,
          String(arrayValue.length),
          'MAX_ITEMS'
        )
      );
    }

    // 验证每个元素
    if (this.itemValidator) {
      arrayValue.forEach((item, index) => {
        const itemPath = `${path}[${index}]`;
        const result = this.itemValidator!.validate(item, itemPath);

        if (!result.isValid) {
          errors.push(...result.errors);
        }

        warnings.push(...result.warnings);

        if (result.data !== undefined) {
          validatedItems.push(result.data);
        }
      });
    } else {
      validatedItems.push(...(arrayValue as T[]));
    }

    // 唯一性检查
    if (this.uniqueItems) {
      const seen = new Set();
      const duplicates: number[] = [];

      validatedItems.forEach((item, index) => {
        const key = JSON.stringify(item);
        if (seen.has(key)) {
          duplicates.push(index);
        } else {
          seen.add(key);
        }
      });

      if (duplicates.length > 0) {
        errors.push(
          this.createError(
            path,
            `数组包含重复元素，索引: ${duplicates.join(', ')}`,
            'unique items',
            'duplicate items',
            'DUPLICATE_ITEMS'
          )
        );
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      data: validatedItems,
    };
  }
}

export class ObjectValidator<
  T extends Record<string, any> = Record<string, any>,
> extends BaseValidator<T> {
  private schema: Record<string, BaseValidator>;
  private requiredFields: Set<string>;

  constructor(
    schema: Record<string, BaseValidator>,
    requiredFields: string[] = [],
    options: ValidatorOptions = {}
  ) {
    super(options);
    this.schema = schema;
    this.requiredFields = new Set(requiredFields);
  }

  validate(value: unknown, path = 'root'): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 基础类型检查
    if (value === null && this.options.allowNull) {
      return { isValid: true, errors, warnings, data: value };
    }

    if (value === undefined && this.options.allowUndefined) {
      return { isValid: true, errors, warnings, data: value };
    }

    if (typeof value !== 'object' || value === null || Array.isArray(value)) {
      errors.push(
        this.createError(path, '期望对象类型', 'object', this.getTypeName(value), 'TYPE_MISMATCH')
      );
      return { isValid: false, errors, warnings };
    }

    const objectValue = value as Record<string, unknown>;
    const validatedObject: Record<string, any> = {};

    // 检查必需字段
    for (const field of this.requiredFields) {
      if (!(field in objectValue)) {
        errors.push(
          this.createError(
            `${path}.${field}`,
            `缺少必需字段: ${field}`,
            field,
            'undefined',
            'MISSING_REQUIRED_FIELD'
          )
        );
      }
    }

    // 验证已知字段
    for (const [fieldName, validator] of Object.entries(this.schema)) {
      const fieldPath = `${path}.${fieldName}`;
      const fieldValue = objectValue[fieldName];

      const result = validator.validate(fieldValue, fieldPath);

      if (!result.isValid) {
        errors.push(...result.errors);
      }

      warnings.push(...result.warnings);

      if (result.data !== undefined) {
        validatedObject[fieldName] = result.data;
      }
    }

    // 处理未知字段
    for (const fieldName in objectValue) {
      if (!(fieldName in this.schema)) {
        const fieldPath = `${path}.${fieldName}`;

        if (this.options.strict) {
          errors.push(
            this.createError(
              fieldPath,
              `不允许的字段: ${fieldName}`,
              'known field',
              fieldName,
              'UNKNOWN_FIELD'
            )
          );
        } else if (this.options.stripUnknown) {
          warnings.push(this.createWarning(fieldPath, `移除未知字段: ${fieldName}`));
        } else {
          warnings.push(
            this.createWarning(fieldPath, `未知字段: ${fieldName}`, '考虑添加到schema中')
          );
          validatedObject[fieldName] = objectValue[fieldName];
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      data: validatedObject as T,
    };
  }
}

// ==================== 联合类型验证器 ====================

export class UnionValidator<T = any> extends BaseValidator<T> {
  private validators: BaseValidator[];

  constructor(validators: BaseValidator[], options: ValidatorOptions = {}) {
    super(options);
    this.validators = validators;
  }

  validate(value: unknown, path = 'root'): ValidationResult {
    const allErrors: ValidationError[] = [];
    const allWarnings: ValidationWarning[] = [];

    // 尝试每个验证器
    for (let i = 0; i < this.validators.length; i++) {
      const validator = this.validators[i];
      if (!validator) continue;
      const result = validator.validate(value, path);

      if (result.isValid) {
        return {
          isValid: true,
          errors: [],
          warnings: result.warnings,
          data: result.data,
        };
      }

      allErrors.push(
        ...result.errors.map(error => ({
          ...error,
          message: `联合类型选项 ${i + 1}: ${error.message}`,
        }))
      );
      allWarnings.push(...result.warnings);
    }

    return {
      isValid: false,
      errors: allErrors,
      warnings: allWarnings,
    };
  }
}

// ==================== 可选类型验证器 ====================

export class OptionalValidator<T = any> extends BaseValidator<T | undefined> {
  private innerValidator: BaseValidator<T>;

  constructor(innerValidator: BaseValidator<T>, options: ValidatorOptions = {}) {
    super({ ...options, allowUndefined: true });
    this.innerValidator = innerValidator;
  }

  validate(value: unknown, path = 'root'): ValidationResult {
    if (value === undefined) {
      return {
        isValid: true,
        errors: [],
        warnings: [],
        data: undefined,
      };
    }

    return this.innerValidator.validate(value, path);
  }
}

// ==================== 工厂函数 ====================

export const v = {
  string: (options?: any) => new StringValidator(options),
  number: (options?: any) => new NumberValidator(options),
  boolean: (options?: any) => new BooleanValidator(options),
  array: <T>(itemValidator?: BaseValidator<T>, options?: any) =>
    new ArrayValidator(itemValidator, options),
  object: <T extends Record<string, any>>(
    schema: Record<string, BaseValidator>,
    requiredFields?: string[],
    options?: ValidatorOptions
  ) => new ObjectValidator<T>(schema, requiredFields, options),
  union: (validators: BaseValidator[], options?: ValidatorOptions) =>
    new UnionValidator(validators, options),
  optional: <T>(validator: BaseValidator<T>, options?: ValidatorOptions) =>
    new OptionalValidator(validator, options),

  // 常用预设
  email: () =>
    new StringValidator({
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    }),
  url: () =>
    new StringValidator({
      pattern: /^https?:\/\/.+/,
    }),
  uuid: () =>
    new StringValidator({
      pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    }),
  positiveNumber: () => new NumberValidator({ min: 0 }),
  positiveInteger: () => new NumberValidator({ min: 0, integer: true }),
  nonEmptyString: () => new StringValidator({ minLength: 1 }),
};

// ==================== 类型安全的验证函数 ====================

export function validateData<T>(
  data: unknown,
  validator: BaseValidator<T>,
  options: { throwOnError?: boolean } = {}
): T {
  const result = validator.validate(data);

  if (!result.isValid) {
    const errorMessage = result.errors.map(e => `${e.path}: ${e.message}`).join('; ');

    if (options.throwOnError !== false) {
      throw new TypeError(`数据验证失败: ${errorMessage}`);
    }
  }

  return result.data as T;
}

export function isValidData<T>(data: unknown, validator: BaseValidator<T>): data is T {
  const result = validator.validate(data);
  return result.isValid;
}
