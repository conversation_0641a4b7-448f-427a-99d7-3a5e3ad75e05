/**
 * 适配器管理器 - 使用策略模式 + 工厂模式 + 单例模式
 * 统一管理所有适配器的注册、创建和配置
 */

import React from 'react';

// 适配器类型枚举
export enum AdapterType {
  DRAG_DROP = 'dragDrop',
  DATE = 'date',
  STATE = 'state',
}

// 适配器库枚举
export enum DragDropLibrary {
  REACT_DND = 'react-dnd',
  DND_KIT = 'dnd-kit',
}

export enum DateLibrary {
  DAYJS = 'dayjs',
  DATE_FNS = 'date-fns',
}

// 适配器配置接口
export interface AdapterConfig {
  type: AdapterType;
  library: string;
  priority: number;
  features: string[];
  enabled: boolean;
}

// 适配器注册信息
export interface AdapterRegistration {
  config: AdapterConfig;
  factory: () => any;
  provider?: React.ComponentType<{ children: React.ReactNode }>;
}

/**
 * 适配器管理器 - 单例模式
 */
export class AdapterManager {
  private static instance: AdapterManager;
  private adapters: Map<string, AdapterRegistration> = new Map();
  private activeAdapters: Map<AdapterType, AdapterRegistration> = new Map();
  private featureMapping: Map<string, string> = new Map();

  private constructor() {
    this.loadConfiguration();
  }

  static getInstance(): AdapterManager {
    if (!AdapterManager.instance) {
      AdapterManager.instance = new AdapterManager();
    }
    return AdapterManager.instance;
  }

  /**
   * 注册适配器
   */
  register(registration: AdapterRegistration): void {
    const key = `${registration.config.type}-${registration.config.library}`;
    this.adapters.set(key, registration);

    // 如果是第一个注册的该类型适配器，或者优先级更高，则设为活跃适配器
    const currentActive = this.activeAdapters.get(registration.config.type);
    if (!currentActive || registration.config.priority > currentActive.config.priority) {
      this.activeAdapters.set(registration.config.type, registration);
    }

    console.log(`✅ 适配器已注册: ${key}`, registration.config);
  }

  /**
   * 获取适配器实例 - 工厂模式
   */
  getAdapter<T>(type: AdapterType, feature?: string): T {
    // 懒加载初始化：如果没有注册任何适配器，尝试初始化
    if (this.adapters.size === 0) {
      console.warn('⚠️ 适配器未初始化，尝试懒加载初始化...');
      this.lazyInitialize();
    }

    let registration: AdapterRegistration | undefined;

    // 如果指定了功能特性，优先使用功能映射
    if (feature) {
      const mappedLibrary = this.featureMapping.get(feature);
      if (mappedLibrary) {
        const key = `${type}-${mappedLibrary}`;
        registration = this.adapters.get(key);
      }
    }

    // 如果没有找到特定映射，使用默认活跃适配器
    if (!registration) {
      registration = this.activeAdapters.get(type);
    }

    if (!registration) {
      throw new Error(
        `No adapter found for type: ${type}. Available adapters: ${Array.from(this.adapters.keys()).join(', ')}`
      );
    }

    if (!registration.config.enabled) {
      throw new Error(`Adapter ${type}-${registration.config.library} is disabled`);
    }

    return registration.factory();
  }

  /**
   * 获取适配器Provider组件
   */
  getProvider(
    type: AdapterType,
    feature?: string
  ): React.ComponentType<{ children: React.ReactNode }> | null {
    let registration: AdapterRegistration | undefined;

    if (feature) {
      const mappedLibrary = this.featureMapping.get(feature);
      if (mappedLibrary) {
        const key = `${type}-${mappedLibrary}`;
        registration = this.adapters.get(key);
      }
    }

    if (!registration) {
      registration = this.activeAdapters.get(type);
    }

    return registration?.provider || null;
  }

  /**
   * 设置功能映射
   */
  setFeatureMapping(feature: string, library: string): void {
    this.featureMapping.set(feature, library);
  }

  /**
   * 批量设置功能映射
   */
  setFeatureMappings(mappings: Record<string, string>): void {
    Object.entries(mappings).forEach(([feature, library]) => {
      this.featureMapping.set(feature, library);
    });
  }

  /**
   * 切换适配器
   */
  switchAdapter(type: AdapterType, library: string): void {
    const key = `${type}-${library}`;
    const registration = this.adapters.get(key);

    if (!registration) {
      throw new Error(`Adapter ${key} not found`);
    }

    this.activeAdapters.set(type, registration);
    console.log(`🔄 适配器已切换: ${type} -> ${library}`);
  }

  /**
   * 启用/禁用适配器
   */
  toggleAdapter(type: AdapterType, library: string, enabled: boolean): void {
    const key = `${type}-${library}`;
    const registration = this.adapters.get(key);

    if (registration) {
      registration.config.enabled = enabled;
      console.log(`${enabled ? '✅' : '❌'} 适配器${enabled ? '启用' : '禁用'}: ${key}`);
    }
  }

  /**
   * 获取所有已注册的适配器
   */
  getRegisteredAdapters(): AdapterRegistration[] {
    return Array.from(this.adapters.values());
  }

  /**
   * 获取所有活跃适配器
   */
  getActiveAdapters(): Map<AdapterType, AdapterRegistration> {
    return new Map(this.activeAdapters);
  }

  /**
   * 懒加载初始化适配器
   */
  private lazyInitialize(): void {
    try {
      console.log('🔄 执行懒加载适配器初始化...');

      // 直接注册后备适配器，确保同步可用
      this.registerFallbackAdapters();

      // 异步尝试加载React DnD适配器
      if (typeof window !== 'undefined') {
        console.log('🌐 客户端环境，尝试加载React DnD适配器...');

        import('@/core/adapters/ReactDndAdapterV2')
          .then(({ ReactDndAdapter, ReactDndProviderFactory }) => {
            console.log('✅ React DnD适配器模块加载成功');

            const reactDndAdapter = new ReactDndAdapter();
            const reactDndProviderFactory = new ReactDndProviderFactory();

            this.register({
              config: {
                type: AdapterType.DRAG_DROP,
                library: DragDropLibrary.REACT_DND,
                priority: 10,
                features: ['task-list', 'vehicle-dispatch', 'cross-task-transfer'],
                enabled: true,
              },
              factory: () => reactDndAdapter,
              provider: reactDndProviderFactory.createProvider(),
            });

            console.log('✅ React DnD适配器懒加载注册成功');
          })
          .catch(error => {
            console.warn('⚠️ React DnD适配器懒加载失败:', error);
          });
      }
    } catch (error) {
      console.error('❌ 懒加载初始化失败:', error);
      this.registerFallbackAdapters();
    }
  }

  /**
   * 注册后备适配器（最小化实现）
   */
  private registerFallbackAdapters(): void {
    console.log('🆘 注册后备适配器...');

    try {
      // 注册一个基本的日期适配器
      this.register({
        config: {
          type: AdapterType.DATE,
          library: 'fallback',
          priority: 1,
          features: ['datetime-picker', 'vehicle-dispatch', 'countdown', 'task-scheduling'],
          enabled: true,
        },
        factory: () => ({
          library: 'fallback',
          now: () => ({
            value: new Date(),
            format: (_pattern: string) => new Date().toISOString(),
          }),
          create: (input?: any) => ({
            value: new Date(input || Date.now()),
            format: (_pattern: string) => new Date(input || Date.now()).toISOString(),
          }),
          format: (date: any, _pattern: string) => new Date(date).toISOString(),
          formatDistance: (_date: any) => '刚刚',
          diff: (left: any, right: any, _unit?: string) =>
            Math.floor((new Date(left).getTime() - new Date(right).getTime()) / 1000),
        }),
      });

      // 注册一个基本的拖拽适配器
      this.register({
        config: {
          type: AdapterType.DRAG_DROP,
          library: 'fallback',
          priority: 1,
          features: ['task-list', 'vehicle-dispatch', 'ratio-design', 'card-config'],
          enabled: true,
        },
        factory: () => ({
          library: 'fallback',
          // 基本的拖拽接口实现，匹配 IDragDropAdapter 接口
          useDrag: (_config: any) => ({
            ref: { current: null },
            dragRef: () => {},
            isDragging: false,
            attributes: {},
            listeners: {},
          }),
          useDrop: (config: any) => {
            // 创建一个简单的拖拽状态管理
            const [isOver, setIsOver] = React.useState(false);
            const [canDrop, setCanDrop] = React.useState(true);

            const dropRef = React.useCallback(
              (node: HTMLElement | null) => {
                if (!node) return;

                // 添加基本的拖拽事件监听
                const handleDragOver = (e: DragEvent) => {
                  e.preventDefault();
                  setIsOver(true);
                  setCanDrop(true);
                };

                const handleDragLeave = (e: DragEvent) => {
                  // 检查是否真的离开了元素
                  if (!node.contains(e.relatedTarget as Node)) {
                    setIsOver(false);
                  }
                };

                const handleDrop = (e: DragEvent) => {
                  e.preventDefault();
                  setIsOver(false);

                  try {
                    const dragData = e.dataTransfer?.getData('application/json');
                    if (dragData && config.onDrop) {
                      const item = JSON.parse(dragData);
                      config.onDrop(item);
                    }
                  } catch (error) {
                    console.warn('⚠️ Fallback拖拽数据解析失败:', error);
                  }
                };

                node.addEventListener('dragover', handleDragOver);
                node.addEventListener('dragleave', handleDragLeave);
                node.addEventListener('drop', handleDrop);

                return () => {
                  node.removeEventListener('dragover', handleDragOver);
                  node.removeEventListener('dragleave', handleDragLeave);
                  node.removeEventListener('drop', handleDrop);
                };
              },
              [config]
            );

            return {
              ref: { current: null },
              dropRef,
              isOver,
              canDrop,
            };
          },
          convertItemType: (type: string) => type,
          convertDragData: (data: any) => data,
        }),
        provider: ({ children }: { children: React.ReactNode }) =>
          React.createElement('div', { 'data-fallback-drag-provider': true }, children),
      });

      // 设置功能映射
      this.setFeatureMappings({
        // 日期功能映射
        'datetime-picker': 'fallback',
        'task-scheduling': 'fallback',
        countdown: 'fallback',
        'vehicle-dispatch': 'fallback',
        calendar: 'fallback',
        // 拖拽功能映射
        'task-list': 'fallback',
        'ratio-design': 'fallback',
        'card-config': 'fallback',
      });

      console.log('✅ 后备适配器注册完成');
    } catch (error) {
      console.error('❌ 后备适配器注册失败:', error);
    }
  }

  /**
   * 加载配置
   */
  private loadConfiguration(): void {
    // 设置默认功能映射
    this.setFeatureMappings({
      'task-list': DragDropLibrary.REACT_DND,
      'vehicle-dispatch': DragDropLibrary.REACT_DND,
      'ratio-design': DragDropLibrary.REACT_DND,
      'card-config': DragDropLibrary.REACT_DND,
      'datetime-picker': DateLibrary.DAYJS,
      'task-scheduling': DateLibrary.DATE_FNS,
      countdown: DateLibrary.DATE_FNS,
      calendar: DateLibrary.DAYJS,
    });
  }

  /**
   * 性能监控
   */
  getPerformanceMetrics(): Record<string, any> {
    return {
      registeredCount: this.adapters.size,
      activeCount: this.activeAdapters.size,
      featureMappingCount: this.featureMapping.size,
      adapters: Array.from(this.adapters.keys()),
      activeAdapters: Array.from(this.activeAdapters.entries()).map(([type, reg]) => ({
        type,
        library: reg.config.library,
        enabled: reg.config.enabled,
      })),
    };
  }
}

/**
 * 便捷的全局访问函数
 */
export const adapterManager = AdapterManager.getInstance();

/**
 * React Hook 集成
 */
export function useAdapter<T>(type: AdapterType, feature?: string): T {
  return React.useMemo(() => {
    return adapterManager.getAdapter<T>(type, feature);
  }, [type, feature]);
}

/**
 * 适配器Provider Hook
 */
export function useAdapterProvider(type: AdapterType, feature?: string) {
  return React.useMemo(() => {
    return adapterManager.getProvider(type, feature);
  }, [type, feature]);
}
