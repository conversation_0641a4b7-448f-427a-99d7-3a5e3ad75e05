/**
 * 车辆右键菜单Hook
 * 处理车辆卡片的右键菜单逻辑
 */

import { useState, useCallback } from 'react';
import { useToast } from '@/shared/hooks/use-toast';
import type { Vehicle } from '@/core/types';

/**
 * 车辆右键菜单Hook
 */
export function useVehicleContextMenu() {
  const { toast } = useToast();

  // 右键菜单状态
  const [contextMenuOpen, setContextMenuOpen] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number } | null>(
    null
  );
  const [contextMenuVehicle, setContextMenuVehicle] = useState<Vehicle | null>(null);

  // 打开右键菜单
  const openContextMenu = useCallback((event: React.MouseEvent, vehicle: Vehicle) => {
    event.preventDefault();
    setContextMenuPosition({ x: event.clientX, y: event.clientY });
    setContextMenuVehicle(vehicle);
    setContextMenuOpen(true);
  }, []);

  // 关闭右键菜单
  const closeContextMenu = useCallback(() => {
    setContextMenuOpen(false);
    setContextMenuPosition(null);
    setContextMenuVehicle(null);
  }, []);

  // 处理车辆操作
  const handleVehicleAction = useCallback(
    (action: string, vehicle: Vehicle) => {
      switch (action) {
        case 'pause':
          toast({
            title: '车辆已暂停',
            description: `车辆 ${vehicle.vehicleNumber} 已暂停调度`,
          });
          break;
        case 'resume':
          toast({
            title: '车辆已恢复',
            description: `车辆 ${vehicle.vehicleNumber} 已恢复调度`,
          });
          break;
        case 'stop':
          toast({
            title: '车辆已停止',
            description: `车辆 ${vehicle.vehicleNumber} 已停止调度`,
          });
          break;
        case 'details':
          toast({
            title: '车辆详情',
            description: `查看车辆 ${vehicle.vehicleNumber} 的详细信息`,
          });
          break;
        case 'maintenance':
          toast({
            title: '维护模式',
            description: `车辆 ${vehicle.vehicleNumber} 已进入维护模式`,
          });
          break;
        default:
          console.warn('未知的车辆操作:', action);
      }
      closeContextMenu();
    },
    [toast, closeContextMenu]
  );

  // 获取车辆可用操作
  const getAvailableActions = useCallback((vehicle: Vehicle) => {
    const actions = [];

    // 根据车辆状态确定可用操作
    if (vehicle.status === 'pending') {
      actions.push(
        { id: 'pause', label: '暂停调度', icon: 'pause' },
        { id: 'stop', label: '停止调度', icon: 'stop' }
      );
    } else if (vehicle.status === 'outbound') {
      actions.push({ id: 'details', label: '查看详情', icon: 'info' });
    } else if (vehicle.status === 'returned') {
      actions.push({ id: 'resume', label: '恢复调度', icon: 'play' });
    }

    // 通用操作
    actions.push(
      { id: 'maintenance', label: '维护模式', icon: 'wrench' },
      { id: 'details', label: '车辆详情', icon: 'info' }
    );

    return actions;
  }, []);

  return {
    // 状态
    contextMenuOpen,
    contextMenuPosition,
    contextMenuVehicle,

    // 操作函数
    openContextMenu,
    closeContextMenu,
    handleVehicleAction,
    getAvailableActions,
  };
}
