'use client';

import React, { useState } from 'react';

import {
  Calculator,
  Database,
  Download,
  RotateCcw,
  Save,
  Settings,
  Shield,
  Upload,
} from 'lucide-react';

// 移除 Dialog 导入，使用自定义模态框
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';

import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/shared/components/tabs';
import { useToast } from '@/shared/hooks/use-toast';

interface RatioSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SettingsData {
  permissions: {
    allowConcreteRatio: boolean;
    allowMortarRatio: boolean;
    allowTemplateEdit: boolean;
    allowHistoryDelete: boolean;
    allowSiloManagement: boolean;
    allowSystemSettings: boolean;
    requireApproval: boolean;
    approvalLevel: 'none' | 'supervisor' | 'engineer' | 'manager';
    maxRatioStrength: string;
    allowExport: boolean;
    allowImport: boolean;
  };
  mortarCoefficients: {
    strengthCoefficients: {
      M5: number;
      M7_5: number;
      M10: number;
      M15: number;
      M20: number;
      M25: number;
      M30: number;
    };
    consistencyFactor: number;
    waterRetentionFactor: number;
    sandFinenessFactor: number;
    cementStrengthFactor: number;
    additiveFactor: number;
    limeFactor: number;
    flyashFactor: number;
    temperatureFactor: number;
    humidityFactor: number;
  };
  materialDensities: {
    cement: {
      PO325: number;
      PO425: number;
      PO525: number;
      PC325: number;
      PC425: number;
      PC525: number;
    };
    aggregates: {
      fineSand: number;
      mediumSand: number;
      coarseSand: number;
      smallStone: number;
      mediumStone: number;
      largeStone: number;
    };
    additives: {
      flyash: number;
      silicaFume: number;
      mineralPowder: number;
      lime: number;
      gypsum: number;
    };
    admixtures: {
      waterReducer: number;
      superplasticizer: number;
      airEntraining: number;
      accelerator: number;
      retarder: number;
      antifreeze: number;
    };
    water: {
      tapWater: number;
      recycledWater: number;
      seaWater: number;
    };
  };
}

export function RatioSettings({ isOpen, onClose }: RatioSettingsProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('permissions');
  const [hasChanges, setHasChanges] = useState(false);

  const [settings, setSettings] = useState<SettingsData>({
    permissions: {
      allowConcreteRatio: true,
      allowMortarRatio: true,
      allowTemplateEdit: true,
      allowHistoryDelete: false,
      allowSiloManagement: true,
      allowSystemSettings: false,
      requireApproval: false,
      approvalLevel: 'none',
      maxRatioStrength: 'C50',
      allowExport: true,
      allowImport: false,
    },
    mortarCoefficients: {
      strengthCoefficients: {
        M5: 0.8,
        M7_5: 0.9,
        M10: 1.0,
        M15: 1.2,
        M20: 1.4,
        M25: 1.6,
        M30: 1.8,
      },
      consistencyFactor: 1.0,
      waterRetentionFactor: 1.0,
      sandFinenessFactor: 1.0,
      cementStrengthFactor: 1.0,
      additiveFactor: 1.0,
      limeFactor: 0.7,
      flyashFactor: 0.7,
      temperatureFactor: 1.0,
      humidityFactor: 1.0,
    },
    materialDensities: {
      cement: {
        PO325: 3.1,
        PO425: 3.1,
        PO525: 3.1,
        PC325: 3.0,
        PC425: 3.0,
        PC525: 3.0,
      },
      aggregates: {
        fineSand: 2.65,
        mediumSand: 2.65,
        coarseSand: 2.65,
        smallStone: 2.7,
        mediumStone: 2.7,
        largeStone: 2.7,
      },
      additives: {
        flyash: 2.2,
        silicaFume: 2.2,
        mineralPowder: 2.8,
        lime: 2.2,
        gypsum: 2.3,
      },
      admixtures: {
        waterReducer: 1.2,
        superplasticizer: 1.1,
        airEntraining: 1.0,
        accelerator: 1.3,
        retarder: 1.2,
        antifreeze: 1.1,
      },
      water: {
        tapWater: 1.0,
        recycledWater: 1.0,
        seaWater: 1.025,
      },
    },
  });

  // 更新设置值
  const updateSetting = (
    category: keyof SettingsData,
    subCategory: string,
    key: string,
    value: any
  ) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [subCategory]: {
          ...(prev[category] as any)[subCategory],
          [key]: value,
        },
      },
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    setTimeout(() => {
      setHasChanges(false);
      toast({
        title: '设置已保存',
        description: '所有设置已成功保存',
      });
    }, 500);
  };

  const handleReset = () => {
    setHasChanges(true);
    toast({
      title: '设置已重置',
      description: '所有设置已重置为默认值',
    });
  };

  const handleExportSettings = () => {
    toast({
      title: '设置已导出',
      description: '设置文件已下载到本地',
    });
  };

  const handleImportSettings = () => {
    toast({
      title: '设置已导入',
      description: '设置已从文件中导入',
    });
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 z-[100] bg-black/50 flex items-center justify-center p-4'>
      <div className='bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-y-auto'>
        <div className='p-6 border-b'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Settings className='h-6 w-6 text-primary' />
              <h2 className='text-xl font-semibold'>系统设置</h2>
              {hasChanges && (
                <span className='text-sm text-orange-600 font-normal'>(有未保存的更改)</span>
              )}
            </div>
            <Button variant='ghost' size='sm' onClick={onClose}>
              ✕
            </Button>
          </div>
        </div>

        <div className='p-6'>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className='grid w-full grid-cols-3 bg-gradient-to-r from-blue-50 to-purple-50'>
              <TabsTrigger
                value='permissions'
                className='gap-1 data-[state=active]:bg-white data-[state=active]:shadow-sm'
              >
                <Shield className='h-4 w-4' />
                配比权限设置
              </TabsTrigger>
              <TabsTrigger
                value='mortarCoefficients'
                className='gap-1 data-[state=active]:bg-white data-[state=active]:shadow-sm'
              >
                <Calculator className='h-4 w-4' />
                砂浆生成系数
              </TabsTrigger>
              <TabsTrigger
                value='materialDensities'
                className='gap-1 data-[state=active]:bg-white data-[state=active]:shadow-sm'
              >
                <Database className='h-4 w-4' />
                材料换算密度
              </TabsTrigger>
            </TabsList>

            <TabsContent value='permissions' className='space-y-4'>
              <div className='grid grid-cols-2 gap-6'>
                {/* 配比权限设置 */}
                <Card className='bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200'>
                  <CardHeader>
                    <CardTitle className='text-lg text-blue-800'>配比权限设置</CardTitle>
                  </CardHeader>
                  <CardContent className='space-y-4'>
                    <div className='space-y-3'>
                      <div className='flex items-center justify-between p-3 bg-white rounded-lg border'>
                        <div className='flex items-center gap-2'>
                          <input
                            type='checkbox'
                            checked={settings.permissions.allowConcreteRatio}
                            onChange={e =>
                              updateSetting(
                                'permissions',
                                'permissions',
                                'allowConcreteRatio',
                                e.target.checked
                              )
                            }
                            className='rounded'
                          />
                          <Label className='font-medium'>水</Label>
                        </div>
                        <Button
                          variant='outline'
                          size='sm'
                          className='text-blue-600 border-blue-200'
                        >
                          范围
                        </Button>
                      </div>

                      <div className='flex items-center justify-between p-3 bg-white rounded-lg border'>
                        <div className='flex items-center gap-2'>
                          <input
                            type='checkbox'
                            checked={settings.permissions.allowMortarRatio}
                            onChange={e =>
                              updateSetting(
                                'permissions',
                                'permissions',
                                'allowMortarRatio',
                                e.target.checked
                              )
                            }
                            className='rounded'
                          />
                          <Label className='font-medium'>硅灰</Label>
                        </div>
                        <Button
                          variant='outline'
                          size='sm'
                          className='text-blue-600 border-blue-200'
                        >
                          范围
                        </Button>
                      </div>

                      <div className='flex items-center justify-between p-3 bg-white rounded-lg border'>
                        <div className='flex items-center gap-2'>
                          <input
                            type='checkbox'
                            checked={settings.permissions.allowTemplateEdit}
                            onChange={e =>
                              updateSetting(
                                'permissions',
                                'permissions',
                                'allowTemplateEdit',
                                e.target.checked
                              )
                            }
                            className='rounded'
                          />
                          <Label className='font-medium'>超细砂</Label>
                        </div>
                        <Button
                          variant='outline'
                          size='sm'
                          className='text-blue-600 border-blue-200'
                        >
                          范围
                        </Button>
                      </div>

                      <div className='flex items-center justify-between p-3 bg-white rounded-lg border'>
                        <div className='flex items-center gap-2'>
                          <input
                            type='checkbox'
                            checked={settings.permissions.allowHistoryDelete}
                            onChange={e =>
                              updateSetting(
                                'permissions',
                                'permissions',
                                'allowHistoryDelete',
                                e.target.checked
                              )
                            }
                            className='rounded'
                          />
                          <Label className='font-medium'>膨胀剂</Label>
                        </div>
                        <Button
                          variant='outline'
                          size='sm'
                          className='text-blue-600 border-blue-200'
                        >
                          范围
                        </Button>
                      </div>

                      <div className='flex items-center justify-between p-3 bg-white rounded-lg border'>
                        <div className='flex items-center gap-2'>
                          <input
                            type='checkbox'
                            checked={settings.permissions.allowSiloManagement}
                            onChange={e =>
                              updateSetting(
                                'permissions',
                                'permissions',
                                'allowSiloManagement',
                                e.target.checked
                              )
                            }
                            className='rounded'
                          />
                          <Label className='font-medium'>早强剂</Label>
                        </div>
                        <Button
                          variant='outline'
                          size='sm'
                          className='text-blue-600 border-blue-200'
                        >
                          范围
                        </Button>
                      </div>

                      <div className='flex items-center justify-between p-3 bg-white rounded-lg border'>
                        <div className='flex items-center gap-2'>
                          <input
                            type='checkbox'
                            checked={settings.permissions.allowSystemSettings}
                            onChange={e =>
                              updateSetting(
                                'permissions',
                                'permissions',
                                'allowSystemSettings',
                                e.target.checked
                              )
                            }
                            className='rounded'
                          />
                          <Label className='font-medium'>防冻剂</Label>
                        </div>
                        <Button
                          variant='outline'
                          size='sm'
                          className='text-blue-600 border-blue-200'
                        >
                          范围
                        </Button>
                      </div>

                      <div className='flex items-center justify-between p-3 bg-white rounded-lg border'>
                        <div className='flex items-center gap-2'>
                          <input
                            type='checkbox'
                            checked={settings.permissions.allowExport}
                            onChange={e =>
                              updateSetting(
                                'permissions',
                                'permissions',
                                'allowExport',
                                e.target.checked
                              )
                            }
                            className='rounded'
                          />
                          <Label className='font-medium'>外加剂</Label>
                        </div>
                        <Button
                          variant='outline'
                          size='sm'
                          className='text-blue-600 border-blue-200'
                        >
                          范围
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 用户权限管理 */}
                <Card className='bg-gradient-to-br from-green-50 to-green-100 border-green-200'>
                  <CardHeader>
                    <CardTitle className='text-lg text-green-800'>用户权限管理</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-4'>
                      <div className='grid grid-cols-5 gap-2 text-sm font-medium text-center'>
                        <div>用户名</div>
                        <div>配比计算</div>
                        <div>配比设计</div>
                        <div>配比发送</div>
                        <div>配比微调</div>
                        <div>配比执行</div>
                      </div>

                      <div className='space-y-2'>
                        {['白彬彬', '李文远', '申合波', '系统管'].map((user, index) => (
                          <div
                            key={user}
                            className='grid grid-cols-6 gap-2 items-center p-2 bg-white rounded border'
                          >
                            <div className='text-sm font-medium'>{user}</div>
                            {[1, 2, 3, 4, 5].map(col => (
                              <div key={col} className='flex justify-center'>
                                <input
                                  type='checkbox'
                                  defaultChecked={index < 2 || (index === 2 && col <= 3)}
                                  className='rounded'
                                />
                              </div>
                            ))}
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value='mortarCoefficients' className='space-y-4'>
              <Card className='bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200'>
                <CardHeader>
                  <CardTitle className='text-lg text-purple-800'>砂浆生成系数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-3 gap-6'>
                    {/* 第一列 */}
                    <div className='space-y-3'>
                      {[
                        { label: '水:', value: 1 },
                        { label: '膨胀剂:', value: 1 },
                        { label: '外加剂:', value: 1 },
                        { label: '砂子:', value: 1 },
                        { label: '尾矿石:', value: 1 },
                        { label: '矿粉:', value: 1 },
                      ].map((item, index) => (
                        <div
                          key={index}
                          className='flex items-center justify-between p-2 bg-white rounded border'
                        >
                          <Label className='font-medium'>{item.label}</Label>
                          <Input
                            type='number'
                            value={item.value}
                            className='w-16 h-8 text-sm'
                            step='0.1'
                          />
                        </div>
                      ))}
                    </div>

                    {/* 第二列 */}
                    <div className='space-y-3'>
                      {[
                        { label: '硅灰:', value: 1 },
                        { label: '早强剂:', value: 1 },
                        { label: '矿渣粉:', value: 1 },
                        { label: '石子:', value: 1 },
                        { label: '煤矸石:', value: 1 },
                      ].map((item, index) => (
                        <div
                          key={index}
                          className='flex items-center justify-between p-2 bg-white rounded border'
                        >
                          <Label className='font-medium'>{item.label}</Label>
                          <Input
                            type='number'
                            value={item.value}
                            className='w-16 h-8 text-sm'
                            step='0.1'
                          />
                        </div>
                      ))}
                    </div>

                    {/* 第三列 */}
                    <div className='space-y-3'>
                      {[
                        { label: '超细砂:', value: 1 },
                        { label: '防冻剂:', value: 1 },
                        { label: '超细粉:', value: 1 },
                        { label: '碎屑:', value: 1 },
                        { label: '水泥:', value: 1 },
                      ].map((item, index) => (
                        <div
                          key={index}
                          className='flex items-center justify-between p-2 bg-white rounded border'
                        >
                          <Label className='font-medium'>{item.label}</Label>
                          <Input
                            type='number'
                            value={item.value}
                            className='w-16 h-8 text-sm'
                            step='0.1'
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='materialDensities' className='space-y-4'>
              <Card className='bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200'>
                <CardHeader>
                  <CardTitle className='text-lg text-orange-800'>材料换算密度</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-3 gap-6'>
                    {/* 第一列 */}
                    <div className='space-y-3'>
                      {[
                        { label: '水泥认识密度:', value: 1000, unit: 'kg/m³' },
                        { label: '矿粉认识密度:', value: 2900, unit: 'kg/m³' },
                        { label: '外加剂认识密度:', value: 1100, unit: 'kg/m³' },
                        { label: '膨胀剂认识密度:', value: 2800, unit: 'kg/m³' },
                      ].map((item, index) => (
                        <div
                          key={index}
                          className='flex items-center justify-between p-2 bg-white rounded border'
                        >
                          <Label className='font-medium text-sm'>{item.label}</Label>
                          <div className='flex items-center gap-2'>
                            <Input type='number' value={item.value} className='w-20 h-8 text-sm' />
                            <span className='text-xs text-muted-foreground'>{item.unit}</span>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* 第二列 */}
                    <div className='space-y-3'>
                      {[
                        { label: '水泥认识密度:', value: 3100, unit: 'kg/m³' },
                        { label: '砂子认识密度:', value: 2650, unit: 'kg/m³' },
                        { label: '硅灰认识密度:', value: 2200, unit: 'kg/m³' },
                      ].map((item, index) => (
                        <div
                          key={index}
                          className='flex items-center justify-between p-2 bg-white rounded border'
                        >
                          <Label className='font-medium text-sm'>{item.label}</Label>
                          <div className='flex items-center gap-2'>
                            <Input type='number' value={item.value} className='w-20 h-8 text-sm' />
                            <span className='text-xs text-muted-foreground'>{item.unit}</span>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* 第三列 */}
                    <div className='space-y-3'>
                      {[
                        { label: '粉煤灰认识密度:', value: 2200, unit: 'kg/m³' },
                        { label: '石子认识密度:', value: 2700, unit: 'kg/m³' },
                        { label: '超细砂认识密度:', value: 2600, unit: 'kg/m³' },
                      ].map((item, index) => (
                        <div
                          key={index}
                          className='flex items-center justify-between p-2 bg-white rounded border'
                        >
                          <Label className='font-medium text-sm'>{item.label}</Label>
                          <div className='flex items-center gap-2'>
                            <Input type='number' value={item.value} className='w-20 h-8 text-sm' />
                            <span className='text-xs text-muted-foreground'>{item.unit}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* 底部操作栏 */}
          <div className='flex justify-between items-center pt-4 border-t'>
            <div className='flex gap-2'>
              <Button variant='outline' onClick={handleExportSettings} className='gap-2'>
                <Download className='h-4 w-4' />
                导出设置
              </Button>
              <Button variant='outline' onClick={handleImportSettings} className='gap-2'>
                <Upload className='h-4 w-4' />
                导入设置
              </Button>
              <Button variant='outline' onClick={handleReset} className='gap-2'>
                <RotateCcw className='h-4 w-4' />
                重置
              </Button>
            </div>

            <div className='flex gap-2'>
              <Button variant='outline' onClick={onClose}>
                取消
              </Button>
              <Button onClick={handleSave} disabled={!hasChanges} className='gap-2'>
                <Save className='h-4 w-4' />
                保存设置
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
