/**
 * Store协调器
 * 管理多个Store之间的协调和数据同步
 */

import { useEffect } from 'react';
import { useAppStore } from '../../infrastructure/storage/stores/appStore';
import { useUiStore } from '../../infrastructure/storage/stores/uiStore';
import { useTaskListStore } from '../../features/task-management/store/taskListStore';
import { useVehicleDispatchStore } from '../../features/vehicle-dispatch/store/vehicleDispatchStore';
import { useModalStore } from '../../infrastructure/storage/stores/modalStore';

/**
 * Store协调器Hook
 * 处理Store之间的数据同步和状态协调
 */
export function useStoreOrchestrator() {
  // 获取各个Store的状态和操作
  const appStore = useAppStore();
  const uiStore = useUiStore();
  const taskListStore = useTaskListStore();
  const vehicleDispatchStore = useVehicleDispatchStore();
  const modalStore = useModalStore();

  // 同步任务数据：appStore -> taskListStore
  useEffect(() => {
    if (appStore.tasks.length > 0) {
      taskListStore.setTasks(appStore.tasks);
    }
  }, [appStore.tasks, taskListStore.setTasks]);

  // 同步车辆数据：appStore -> taskListStore & vehicleDispatchStore
  useEffect(() => {
    if (appStore.vehicles.length > 0) {
      taskListStore.setVehicles(appStore.vehicles);
      vehicleDispatchStore.setAllVehicles(appStore.vehicles);
    }
  }, [appStore.vehicles, taskListStore.setVehicles, vehicleDispatchStore.setAllVehicles]);

  // 同步UI状态：uiStore -> taskListStore & vehicleDispatchStore
  useEffect(() => {
    vehicleDispatchStore.setVehicleDisplayMode(uiStore.vehicleDisplayMode);
  }, [uiStore.vehicleDisplayMode, vehicleDispatchStore.setVehicleDisplayMode]);

  useEffect(() => {
    vehicleDispatchStore.setVehicleListDisplayMode(uiStore.vehicleListDisplayMode);
  }, [uiStore.vehicleListDisplayMode, vehicleDispatchStore.setVehicleListDisplayMode]);

  // 任务状态过滤：根据uiStore的过滤条件更新taskListStore的过滤结果
  useEffect(() => {
    const filteredTasks = appStore.tasks.filter(task => {
      if (uiStore.taskStatusFilter === 'all') return true;
      return task.status === uiStore.taskStatusFilter;
    });
    taskListStore.setFilteredTasks(filteredTasks);
  }, [appStore.tasks, uiStore.taskStatusFilter, taskListStore.setFilteredTasks]);

  // 处理任务更新：taskListStore -> appStore
  const handleTaskUpdate = (taskId: string, updates: any) => {
    appStore.updateTask(taskId, updates);
    taskListStore.updateTask(taskId, updates);
  };

  // 处理车辆更新：vehicleDispatchStore -> appStore
  const handleVehicleUpdate = (vehicleId: string, updates: any) => {
    // 这里可以添加业务逻辑验证
    appStore.updateTask(vehicleId, updates); // 假设appStore有updateVehicle方法
    vehicleDispatchStore.updateVehicle(vehicleId, updates);
    taskListStore.updateVehicle(vehicleId, updates);
  };

  // 处理车辆调度：协调多个Store的状态更新
  const handleVehicleDispatch = async (vehicleId: string, taskId: string, lineId?: string) => {
    try {
      // 设置操作状态
      vehicleDispatchStore.setOperationStatus('dispatching', true);
      taskListStore.setOperationStatus('dispatching', true);

      // 调用appStore的调度方法
      const result = await appStore.dispatchVehicleToTask(vehicleId, taskId, lineId);

      if (result) {
        // 更新车辆状态
        handleVehicleUpdate(vehicleId, { status: 'outbound', currentTaskId: taskId });

        // 更新任务状态
        handleTaskUpdate(taskId, { assignedVehicleId: vehicleId });

        // 刷新车辆列表
        vehicleDispatchStore.refreshVehicleLists();
      }

      return result;
    } catch (error) {
      console.error('车辆调度失败:', error);
      vehicleDispatchStore.setError('车辆调度失败');
      taskListStore.setError('车辆调度失败');
      return null;
    } finally {
      // 清除操作状态
      vehicleDispatchStore.setOperationStatus('dispatching', false);
      taskListStore.setOperationStatus('dispatching', false);
    }
  };

  // 处理模态框状态同步
  const handleModalSync = () => {
    // 当有模态框打开时，可能需要暂停某些自动更新
    const hasOpenModals = modalStore.hasOpenModals();

    if (hasOpenModals) {
      // 暂停自动刷新等操作
      console.log('模态框已打开，暂停自动刷新');
    }
  };

  useEffect(() => {
    handleModalSync();
  }, [modalStore.modalStack.length]);

  // 全局错误处理
  const handleGlobalError = (error: string, source: string) => {
    console.error(`[${source}] ${error}`);

    // 可以在这里添加全局错误处理逻辑
    // 比如显示全局错误提示、记录错误日志等
  };

  // 监听各Store的错误状态
  useEffect(() => {
    if (taskListStore.error) {
      handleGlobalError(taskListStore.error, 'TaskList');
    }
  }, [taskListStore.error]);

  useEffect(() => {
    if (vehicleDispatchStore.error) {
      handleGlobalError(vehicleDispatchStore.error, 'VehicleDispatch');
    }
  }, [vehicleDispatchStore.error]);

  // 数据持久化
  const handleDataPersistence = () => {
    // 可以在这里添加数据持久化逻辑
    // 比如保存用户偏好设置、缓存数据等
  };

  // 性能监控
  const handlePerformanceMonitoring = () => {
    // 可以在这里添加性能监控逻辑
    // 比如监控Store状态变化频率、内存使用等
  };

  return {
    // 协调后的操作方法
    handleTaskUpdate,
    handleVehicleUpdate,
    handleVehicleDispatch,
    handleGlobalError,
    handleDataPersistence,
    handlePerformanceMonitoring,

    // Store状态访问
    stores: {
      app: appStore,
      ui: uiStore,
      taskList: taskListStore,
      vehicleDispatch: vehicleDispatchStore,
      modal: modalStore,
    },

    // 全局状态
    globalState: {
      isLoading: taskListStore.isLoading || vehicleDispatchStore.operationStatus.loading,
      hasErrors: !!(taskListStore.error || vehicleDispatchStore.error),
      hasOpenModals: modalStore.hasOpenModals(),
    },
  };
}

/**
 * Store初始化Hook
 * 在应用启动时初始化各个Store
 */
export function useStoreInitializer() {
  const appStore = useAppStore();

  useEffect(() => {
    // 初始化应用数据
    const initializeStores = async () => {
      try {
        // 获取初始数据
        await appStore.fetchInitialData();

        // 初始化任务管理器
        appStore.initializeTaskManager();

        console.log('Store初始化完成');
      } catch (error) {
        console.error('Store初始化失败:', error);
      }
    };

    initializeStores();

    // 清理函数
    return () => {
      appStore.cleanupTaskManager();
    };
  }, []);

  return {
    isInitialized: appStore.tasks.length > 0 || appStore.vehicles.length > 0,
  };
}
