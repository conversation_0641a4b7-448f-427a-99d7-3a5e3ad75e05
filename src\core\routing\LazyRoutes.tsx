'use client';

/**
 * 路由级懒加载组件
 * 实现按模块的代码分割和预加载策略
 */

import React, { Suspense } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { SkeletonLoader } from '@/shared/components/enhanced-loading';
import { createRouteLazyComponent, PreloadManager } from '../config/code-splitting';

// ==================== 路由级懒加载组件定义 ====================

// 调度模块 - 高优先级，立即预加载
export const DispatchModule = createRouteLazyComponent(
  () => import('@/features/vehicle-dispatch'),
  {
    chunkName: 'dispatch-module',
    preload: true,
  }
);

// 任务列表页面 - 暂时注释掉不存在的页面
// export const TaskListPage = createRouteLazyComponent(
//   () => import('@/app/task-list/page'),
//   {
//     chunkName: 'task-list-page',
//     preload: true,
//   }
// );

// 车辆调度页面 - 暂时注释掉不存在的页面
// export const VehicleDispatchPage = createRouteLazyComponent(
//   () => import('@/app/vehicle-dispatch/page'),
//   {
//     chunkName: 'vehicle-dispatch-page',
//     preload: true,
//   }
// );

// 配比模块 - 中等优先级，空闲时预加载
export const RatioModule = createRouteLazyComponent(() => import('@/features/ratio-management'), {
  chunkName: 'ratio-module',
  preload: false,
});

// 配比页面 - 按版本分割 - 暂时注释掉不存在的页面
// export const RatioV1Page = createRouteLazyComponent(
//   () => import('@/app/ratio/v1/page'),
//   {
//     chunkName: 'ratio-v1-page',
//     preload: false,
//   }
// );

// export const RatioV2Page = createRouteLazyComponent(
//   () => import('@/app/ratio/v2/page'),
//   {
//     chunkName: 'ratio-v2-page',
//     preload: false,
//   }
// );

// export const RatioV3Page = createRouteLazyComponent(
//   () => import('@/app/ratio/v3/page'),
//   {
//     chunkName: 'ratio-v3-page',
//     preload: false,
//   }
// );

// 报表模块 - 低优先级，悬停时预加载 - 暂时注释掉不存在的模块
// export const ReportsModule = createRouteLazyComponent(
//   () => import('@/modules/reports'),
//   {
//     chunkName: 'reports-module',
//     preload: false,
//   }
// );

// 设置模块 - 低优先级 - 暂时注释掉不存在的模块
// export const SettingsModule = createRouteLazyComponent(
//   () => import('@/modules/settings'),
//   {
//     chunkName: 'settings-module',
//     preload: false,
//   }
// );

// 演示页面 - 独立分割
// export const LazyLoadingDemoPage = createRouteLazyComponent(
//   () => import('@/app/lazy-loading-demo/page'),
//   {
//     chunkName: 'demo-page',
//     preload: false,
//   }
// );

// ==================== 路由包装器组件 ====================

interface LazyRouteWrapperProps {
  children: React.ReactNode;
  moduleName?: string;
  fallback?: React.ReactNode;
}

export function LazyRouteWrapper({ children, moduleName, fallback }: LazyRouteWrapperProps) {
  const defaultFallback = (
    <div className='min-h-screen bg-gray-50 p-4'>
      <div className='max-w-7xl mx-auto'>
        <SkeletonLoader type='card' className='w-full h-96' />
        {moduleName && (
          <div className='text-center mt-4 text-gray-500 text-sm'>
            正在加载 {moduleName} 模块...
          </div>
        )}
      </div>
    </div>
  );

  return (
    <ErrorBoundary
      fallback={
        <div className='min-h-screen bg-red-50 p-4 flex items-center justify-center'>
          <div className='text-center'>
            <div className='text-red-600 text-lg mb-2'>⚠️ 模块加载失败</div>
            <div className='text-gray-600 text-sm'>
              {moduleName ? `${moduleName} 模块` : '页面'}加载时出现错误
            </div>
            <button
              onClick={() => window.location.reload()}
              className='mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700'
            >
              重新加载
            </button>
          </div>
        </div>
      }
    >
      <Suspense fallback={fallback || defaultFallback}>{children}</Suspense>
    </ErrorBoundary>
  );
}

// ==================== 预加载钩子 ====================

/**
 * 路由预加载钩子
 */
export function useRoutePreload() {
  const preloadRoute = React.useCallback(async (routeName: string) => {
    const routeMap: Record<string, () => Promise<any>> = {
      dispatch: () => import('@/features/vehicle-dispatch'),
      ratio: () => import('@/features/ratio-management'),
      // demo: () => import('@/app/lazy-loading-demo/page'),
      // 注释掉不存在的路由
      // 'task-list': () => import('@/app/task-list/page'),
      // 'vehicle-dispatch': () => import('@/app/vehicle-dispatch/page'),
      // 'ratio-v1': () => import('@/app/ratio/v1/page'),
      // 'ratio-v2': () => import('@/app/ratio/v2/page'),
      // 'ratio-v3': () => import('@/app/ratio/v3/page'),
      // 'reports': () => import('@/modules/reports'),
      // 'settings': () => import('@/modules/settings'),
    };

    const importFn = routeMap[routeName];
    if (importFn) {
      return PreloadManager.preloadModule(routeName, importFn);
    }
  }, []);

  const preloadOnHover = React.useCallback(
    (routeName: string) => {
      return {
        onMouseEnter: () => preloadRoute(routeName),
      };
    },
    [preloadRoute]
  );

  return {
    preloadRoute,
    preloadOnHover,
  };
}

// ==================== 智能预加载组件 ====================

interface SmartPreloadProps {
  routes: string[];
  strategy: 'immediate' | 'idle' | 'hover' | 'viewport';
  children?: React.ReactNode;
}

export function SmartPreload({ routes, strategy, children }: SmartPreloadProps) {
  const { preloadRoute } = useRoutePreload();

  React.useEffect(() => {
    if (strategy === 'immediate') {
      routes.forEach(route => preloadRoute(route));
    } else if (strategy === 'idle') {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          routes.forEach(route => preloadRoute(route));
        });
      } else {
        setTimeout(() => {
          routes.forEach(route => preloadRoute(route));
        }, 100);
      }
    }
  }, [routes, strategy, preloadRoute]);

  if (strategy === 'hover' && children) {
    return <div onMouseEnter={() => routes.forEach(route => preloadRoute(route))}>{children}</div>;
  }

  return children || null;
}

// ==================== 路由性能监控 ====================

export function RoutePerformanceMonitor() {
  const [metrics, setMetrics] = React.useState<any>(null);

  React.useEffect(() => {
    const updateMetrics = () => {
      setMetrics({
        preloadStatus: PreloadManager.getPreloadStatus(),
        timestamp: new Date().toLocaleTimeString(),
      });
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000);

    return () => clearInterval(interval);
  }, []);

  if (!metrics) return null;

  return (
    <div className='fixed bottom-4 right-4 bg-white border rounded-lg p-3 text-xs shadow-lg z-50'>
      <div className='font-medium mb-1'>路由预加载状态</div>
      <div>已预加载: {metrics.preloadStatus.preloadedModules.length}</div>
      <div>待加载: {metrics.preloadStatus.pendingPreloads.length}</div>
      <div className='text-gray-500 mt-1'>更新: {metrics.timestamp}</div>
    </div>
  );
}

// ==================== 导出 ====================

export default {
  // 页面组件
  // LazyLoadingDemoPage,

  // 模块组件
  DispatchModule,
  RatioModule,

  // 工具组件
  LazyRouteWrapper,
  SmartPreload,
  RoutePerformanceMonitor,

  // 钩子
  useRoutePreload,
};
