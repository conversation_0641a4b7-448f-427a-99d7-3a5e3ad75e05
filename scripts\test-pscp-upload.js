#!/usr/bin/env node

/**
 * PSCP 上传测试脚本
 * 用于测试不同路径格式的上传功能
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 测试配置
const testConfig = {
  serverIP: '*************',
  serverUser: 'administrator',
  serverPassword: '13834567299Goodbye!',
  sshPort: '22',
};

/**
 * 彩色日志输出
 */
function colorLog(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    gray: '\x1b[90m',
    reset: '\x1b[0m',
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

/**
 * 获取PSCP路径
 */
function getPscpPath() {
  const pscpPaths = [
    'pscp',
    'C:\\Program Files\\PuTTY\\pscp.exe',
    'C:\\Program Files (x86)\\PuTTY\\pscp.exe',
  ];

  for (const pscpPath of pscpPaths) {
    try {
      execSync(`"${pscpPath}" -V`, { stdio: 'pipe' });
      return pscpPath;
    } catch (error) {
      // 继续尝试下一个路径
    }
  }

  throw new Error('PSCP工具未找到');
}

/**
 * 创建测试文件
 */
function createTestFile() {
  const testFileName = `test-upload-${Date.now()}.txt`;
  const testContent = `测试文件 - 创建时间: ${new Date().toISOString()}`;
  fs.writeFileSync(testFileName, testContent);
  colorLog('green', `✅ 创建测试文件: ${testFileName}`);
  return testFileName;
}

/**
 * 测试上传到不同路径
 */
function testUpload(testFile) {
  const pscpPath = getPscpPath();
  colorLog('blue', `📦 使用PSCP: ${pscpPath}`);

  const testPaths = [
    { path: '', description: '默认路径（无路径）' },
    { path: '.', description: '当前目录' },
    { path: 'C:\\Users\\<USER>\\', description: 'Windows用户目录' },
    { path: '/tmp/', description: 'Unix临时目录' },
  ];

  for (const { path: remotePath, description } of testPaths) {
    try {
      colorLog('cyan', `\n🔄 测试上传到: ${description}`);

      // 构建命令
      let scpCmd;
      if (remotePath === '' || remotePath === '.') {
        scpCmd = `"${pscpPath}" -P ${testConfig.sshPort} -l ${testConfig.serverUser} -pw "${testConfig.serverPassword}" -batch "${testFile}" ${testConfig.serverIP}:`;
      } else {
        scpCmd = `"${pscpPath}" -P ${testConfig.sshPort} -l ${testConfig.serverUser} -pw "${testConfig.serverPassword}" -batch "${testFile}" ${testConfig.serverIP}:"${remotePath}"`;
      }

      colorLog('gray', `📝 命令: ${scpCmd}`);

      // 执行上传
      execSync(scpCmd, { stdio: 'pipe' });
      colorLog('green', `✅ ${description} 上传成功`);

      // 清理远程文件
      const cleanupCmd = `plink -ssh -P ${testConfig.sshPort} -l ${testConfig.serverUser} -pw "${testConfig.serverPassword}" -batch ${testConfig.serverIP} "del ${remotePath}${testFile} 2>nul || rm -f ${remotePath}${testFile} 2>/dev/null || true"`;
      execSync(cleanupCmd, { stdio: 'pipe' });

      return { success: true, workingPath: remotePath, description };
    } catch (error) {
      colorLog('red', `❌ ${description} 上传失败: ${error.message}`);
    }
  }

  return { success: false };
}

/**
 * 主函数
 */
function main() {
  colorLog('blue', '🧪 PSCP 上传路径测试');
  colorLog('cyan', `🎯 目标服务器: ${testConfig.serverIP}`);

  try {
    // 创建测试文件
    const testFile = createTestFile();

    // 测试上传
    const result = testUpload(testFile);

    // 清理本地测试文件
    fs.unlinkSync(testFile);
    colorLog('gray', `🗑️ 清理本地测试文件: ${testFile}`);

    if (result.success) {
      colorLog('green', `\n🎉 找到可用的上传路径: ${result.description}`);
      colorLog('cyan', `📝 建议在部署脚本中使用此路径格式`);
    } else {
      colorLog('red', '\n❌ 所有路径都无法上传');
      colorLog('yellow', '💡 建议检查：');
      colorLog('white', '  1. 服务器连接是否正常');
      colorLog('white', '  2. 用户权限是否足够');
      colorLog('white', '  3. PuTTY是否正确安装');
    }
  } catch (error) {
    colorLog('red', `❌ 测试失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = { testUpload, createTestFile };
