/**
 * DOM 安全操作工具函数
 * 防止在组件卸载或DOM结构变化时出现错误
 */

/**
 * 安全地添加CSS类
 */
export function safeAddClass(element: HTMLElement | null, className: string): boolean {
  try {
    if (element && element.isConnected && element.classList) {
      element.classList.add(className);
      return true;
    }
  } catch (error) {
    console.warn('Safe add class error:', error);
  }
  return false;
}

/**
 * 安全地移除CSS类
 */
export function safeRemoveClass(element: HTMLElement | null, className: string): boolean {
  try {
    if (
      element &&
      element.isConnected &&
      element.classList &&
      element.classList.contains(className)
    ) {
      element.classList.remove(className);
      return true;
    }
  } catch (error) {
    console.warn('Safe remove class error:', error);
  }
  return false;
}

/**
 * 安全地切换CSS类
 */
export function safeToggleClass(element: HTMLElement | null, className: string): boolean {
  try {
    if (element && element.isConnected && element.classList) {
      element.classList.toggle(className);
      return true;
    }
  } catch (error) {
    console.warn('Safe toggle class error:', error);
  }
  return false;
}

/**
 * 安全地设置元素属性
 */
export function safeSetAttribute(
  element: HTMLElement | null,
  name: string,
  value: string
): boolean {
  try {
    if (element && element.isConnected) {
      element.setAttribute(name, value);
      return true;
    }
  } catch (error) {
    console.warn('Safe set attribute error:', error);
  }
  return false;
}

/**
 * 安全地移除元素属性
 */
export function safeRemoveAttribute(element: HTMLElement | null, name: string): boolean {
  try {
    if (element && element.isConnected && element.hasAttribute(name)) {
      element.removeAttribute(name);
      return true;
    }
  } catch (error) {
    console.warn('Safe remove attribute error:', error);
  }
  return false;
}

/**
 * 安全地设置元素样式
 */
export function safeSetStyle(
  element: HTMLElement | null,
  property: string,
  value: string
): boolean {
  try {
    if (element && element.isConnected && element.style) {
      (element.style as any)[property] = value;
      return true;
    }
  } catch (error) {
    console.warn('Safe set style error:', error);
  }
  return false;
}

/**
 * 安全地执行DOM操作，带有延迟检查
 */
export function safeDelayedOperation(
  element: HTMLElement | null,
  operation: (el: HTMLElement) => void,
  delay: number = 0
): void {
  if (!element || !element.isConnected) {
    return;
  }

  if (delay === 0) {
    try {
      operation(element);
    } catch (error) {
      console.warn('Safe delayed operation error:', error);
    }
  } else {
    setTimeout(() => {
      try {
        if (element.isConnected) {
          operation(element);
        }
      } catch (error) {
        console.warn('Safe delayed operation error:', error);
      }
    }, delay);
  }
}

/**
 * 安全地执行requestAnimationFrame操作
 */
export function safeAnimationFrame(
  element: HTMLElement | null,
  operation: (el: HTMLElement) => void
): void {
  if (!element || !element.isConnected) {
    return;
  }

  requestAnimationFrame(() => {
    try {
      if (element.isConnected) {
        operation(element);
      }
    } catch (error) {
      console.warn('Safe animation frame operation error:', error);
    }
  });
}

/**
 * 安全地添加临时CSS类（自动移除）
 */
export function safeTemporaryClass(
  element: HTMLElement | null,
  className: string,
  duration: number = 100
): void {
  if (!safeAddClass(element, className)) {
    return;
  }

  safeDelayedOperation(element, el => safeRemoveClass(el, className), duration);
}

/**
 * 安全地添加临时CSS类（使用requestAnimationFrame）
 */
export function safeTemporaryClassAnimated(element: HTMLElement | null, className: string): void {
  if (!safeAddClass(element, className)) {
    return;
  }

  safeAnimationFrame(element, el => safeRemoveClass(el, className));
}

/**
 * 检查元素是否安全可操作
 */
export function isElementSafe(element: HTMLElement | null): element is HTMLElement {
  return !!(element && element.isConnected && element.parentNode);
}

/**
 * 安全地查询子元素
 */
export function safeQuerySelector(
  element: HTMLElement | null,
  selector: string
): HTMLElement | null {
  try {
    if (element && element.isConnected) {
      return element.querySelector(selector);
    }
  } catch (error) {
    console.warn('Safe query selector error:', error);
  }
  return null;
}

/**
 * 安全地查询所有子元素
 */
export function safeQuerySelectorAll(
  element: HTMLElement | null,
  selector: string
): NodeListOf<HTMLElement> | null {
  try {
    if (element && element.isConnected) {
      return element.querySelectorAll(selector);
    }
  } catch (error) {
    console.warn('Safe query selector all error:', error);
  }
  return null;
}

/**
 * 安全地获取元素的边界矩形
 */
export function safeGetBoundingClientRect(element: HTMLElement | null): DOMRect | null {
  try {
    if (element && element.isConnected) {
      return element.getBoundingClientRect();
    }
  } catch (error) {
    console.warn('Safe get bounding client rect error:', error);
  }
  return null;
}

/**
 * 安全地滚动到元素
 */
export function safeScrollIntoView(
  element: HTMLElement | null,
  options?: ScrollIntoViewOptions
): boolean {
  try {
    if (element && element.isConnected) {
      element.scrollIntoView(options);
      return true;
    }
  } catch (error) {
    console.warn('Safe scroll into view error:', error);
  }
  return false;
}

/**
 * 安全地聚焦元素
 */
export function safeFocus(element: HTMLElement | null): boolean {
  try {
    if (element && element.isConnected && typeof element.focus === 'function') {
      element.focus();
      return true;
    }
  } catch (error) {
    console.warn('Safe focus error:', error);
  }
  return false;
}

/**
 * 安全地失焦元素
 */
export function safeBlur(element: HTMLElement | null): boolean {
  try {
    if (element && element.isConnected && typeof element.blur === 'function') {
      element.blur();
      return true;
    }
  } catch (error) {
    console.warn('Safe blur error:', error);
  }
  return false;
}

/**
 * 安全地下载文件
 */
export function safeDownloadFile(
  data: string | Blob,
  filename: string,
  mimeType: string = 'application/json'
): void {
  try {
    const blob = data instanceof Blob ? data : new Blob([data], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link); // safe
    link.click();

    // 安全地移除元素
    setTimeout(() => {
      try {
        if (link && link.parentNode && link.parentNode === document.body) {
          document.body.removeChild(link); // safe
        }
      } catch (error) {
        console.warn('Safe remove download link error:', error);
      }
      URL.revokeObjectURL(url);
    }, 100);
  } catch (error) {
    console.error('Safe download file error:', error);
    throw error;
  }
}

/**
 * 安全地移除DOM元素
 */
export function safeRemoveElement(element: HTMLElement | null): boolean {
  try {
    if (element && element.parentNode && element.isConnected) {
      element.parentNode.removeChild(element); // safe
      return true;
    }
  } catch (error) {
    console.warn('Safe remove element error:', error);
  }
  return false;
}
