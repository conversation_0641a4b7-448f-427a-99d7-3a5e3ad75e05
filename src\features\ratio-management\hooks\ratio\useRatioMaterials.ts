/**
 * 配比材料管理业务Hook
 * 封装配比材料管理相关的业务逻辑
 */

import { useCallback, useMemo } from 'react';
import { useRatioStoreOrchestrator } from '@/features/ratio-management/store/ratioStoreOrchestrator';
import { mockMaterials } from '@/infrastructure/api/mock/mock-data';
import type {
  UseRatioMaterialsReturn,
  UnifiedRatioMaterial,
  MaterialCategory,
} from '@/core/types/ratio';

/**
 * 配比材料管理Hook
 * 提供材料管理相关的业务逻辑和状态管理
 */
export function useRatioMaterials(): UseRatioMaterialsReturn {
  const orchestrator = useRatioStoreOrchestrator();

  // 获取可用材料类型
  const availableMaterialTypes = useMemo(() => {
    return [...new Map(mockMaterials.map(item => [item.materialType, item])).values()];
  }, []);

  // 按类别分组的材料
  const materialsByCategory = useMemo(() => {
    const grouped: Record<string, typeof mockMaterials> = {};
    mockMaterials.forEach(material => {
      const category = material.category || 'other';
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(material);
    });
    return grouped;
  }, []);

  // 添加材料
  const addMaterial = useCallback(() => {
    return orchestrator.actions.addMaterial();
  }, [orchestrator.actions]);

  // 更新材料
  const updateMaterial = useCallback(
    (id: string, material: Partial<UnifiedRatioMaterial>) => {
      orchestrator.actions.updateMaterial(id, material);
    },
    [orchestrator.actions]
  );

  // 删除材料
  const deleteMaterial = useCallback(
    (id: string) => {
      orchestrator.actions.deleteMaterial(id);
    },
    [orchestrator.actions]
  );

  // 根据材料类型获取规格选项
  const getSpecificationsByType = useCallback((materialType: string) => {
    return mockMaterials
      .filter(m => m.materialType === materialType)
      .map(m => ({
        id: m.id,
        spec: m.spec,
        density: m.density || 2650,
        storageBin: m.storageBin,
      }));
  }, []);

  // 验证材料数据
  const validateMaterial = useCallback((material: Partial<UnifiedRatioMaterial>) => {
    const errors: string[] = [];

    if (!material.name || material.name.trim() === '') {
      errors.push('材料名称不能为空');
    }

    if (!material.specification || material.specification.trim() === '') {
      errors.push('材料规格不能为空');
    }

    if (material.theoreticalAmount !== undefined && material.theoreticalAmount < 0) {
      errors.push('理论用量不能为负数');
    }

    if (
      material.waterContent !== undefined &&
      (material.waterContent < 0 || material.waterContent > 100)
    ) {
      errors.push('含水率应在0-100%之间');
    }

    if (
      material.stoneContent !== undefined &&
      (material.stoneContent < 0 || material.stoneContent > 100)
    ) {
      errors.push('含石率应在0-100%之间');
    }

    if (material.density !== undefined && (material.density < 500 || material.density > 5000)) {
      errors.push('密度应在500-5000kg/m³之间');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }, []);

  // 计算材料实际用量
  const calculateActualAmount = useCallback((material: UnifiedRatioMaterial) => {
    const waterAdjustment = 1 + (material.waterContent || 0) / 100;
    const stoneAdjustment = 1 - (material.stoneContent || 0) / 100;
    return material.theoreticalAmount * waterAdjustment * stoneAdjustment;
  }, []);

  // 批量更新材料用量
  const updateMaterialAmounts = useCallback(
    (updates: Array<{ id: string; amount: number }>) => {
      updates.forEach(({ id, amount }) => {
        const material = orchestrator.data.materials.find(m => m.id === id);
        if (material) {
          const actualAmount = calculateActualAmount({ ...material, theoreticalAmount: amount });
          updateMaterial(id, {
            theoreticalAmount: amount,
            actualAmount,
            designValue: actualAmount,
          });
        }
      });
    },
    [orchestrator.data.materials, calculateActualAmount, updateMaterial]
  );

  // 从模板应用材料配置
  const applyMaterialTemplate = useCallback(
    (templateMaterials: UnifiedRatioMaterial[]) => {
      // 清空当前材料
      orchestrator.data.materials.forEach(material => {
        deleteMaterial(material.id);
      });

      // 添加模板材料
      templateMaterials.forEach(templateMaterial => {
        const newMaterial: UnifiedRatioMaterial = {
          ...templateMaterial,
          id: `material-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          actualAmount: calculateActualAmount(templateMaterial),
        };
        newMaterial.designValue = newMaterial.actualAmount;

        // 这里需要直接操作store，因为addMaterial只能添加空材料
        orchestrator.stores.data.setMaterials([...orchestrator.data.materials, newMaterial]);
      });
    },
    [orchestrator.data.materials, orchestrator.stores.data, deleteMaterial, calculateActualAmount]
  );

  // 导出材料配置
  const exportMaterialConfig = useCallback(() => {
    return {
      materials: orchestrator.data.materials,
      timestamp: new Date().toISOString(),
      version: '1.0',
    };
  }, [orchestrator.data.materials]);

  // 计算材料总成本
  const calculateTotalCost = useCallback(() => {
    return orchestrator.data.materials.reduce((total, material) => {
      const amount = material.actualAmount / 1000; // 转换为吨
      const cost = amount * (material.cost || 0);
      return total + cost;
    }, 0);
  }, [orchestrator.data.materials]);

  // 获取材料统计信息
  const getMaterialStats = useCallback(() => {
    const materials = orchestrator.data.materials;
    const totalWeight = materials.reduce((sum, m) => sum + m.actualAmount, 0);
    const totalCost = calculateTotalCost();

    const categoryStats: Record<string, { count: number; weight: number; cost: number }> = {};

    materials.forEach(material => {
      const category = material.category || 'other';
      if (!categoryStats[category]) {
        categoryStats[category] = { count: 0, weight: 0, cost: 0 };
      }
      categoryStats[category].count++;
      categoryStats[category].weight += material.actualAmount;
      categoryStats[category].cost += (material.actualAmount / 1000) * (material.cost || 0);
    });

    return {
      totalMaterials: materials.length,
      totalWeight,
      totalCost,
      categoryStats,
    };
  }, [orchestrator.data.materials, calculateTotalCost]);

  return {
    // 材料数据
    materials: orchestrator.data.materials,

    // 操作方法
    addMaterial,
    updateMaterial,
    deleteMaterial,

    // 状态
    isLoading: orchestrator.ui.isLoading,
    error: orchestrator.ui.error,

    // Note: Only returning methods defined in UseRatioMaterialsReturn interface
  };
}
