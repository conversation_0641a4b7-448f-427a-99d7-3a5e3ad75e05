// src/utils/taskCardConfigManager.ts
'use client';

import { defaultTaskCardConfig, TaskCardConfig } from '@/core/types/taskCardConfig';
import { safeDownloadFile } from '@/core/utils/dom-safe-operations';

// src/utils/taskCardConfigManager.ts

const TASK_CARD_CONFIG_KEY = 'taskCardConfig_v1.0';

/**
 * 任务卡片配置管理器
 */
export class TaskCardConfigManager {
  /**
   * 从localStorage加载配置
   */
  static loadConfig(): TaskCardConfig {
    try {
      const savedConfig = localStorage.getItem(TASK_CARD_CONFIG_KEY);
      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig);
        // 验证配置结构的完整性
        if (this.validateConfig(parsedConfig)) {
          return parsedConfig;
        } else {
          console.warn('Invalid task card config structure, using default config');
          return defaultTaskCardConfig;
        }
      }
      return defaultTaskCardConfig;
    } catch (error) {
      console.error('Failed to load task card config from localStorage:', error);
      return defaultTaskCardConfig;
    }
  }

  /**
   * 保存配置到localStorage
   */
  static saveConfig(config: TaskCardConfig): boolean {
    try {
      if (!this.validateConfig(config)) {
        throw new Error('Invalid config structure');
      }
      localStorage.setItem(TASK_CARD_CONFIG_KEY, JSON.stringify(config));
      return true;
    } catch (error) {
      console.error('Failed to save task card config to localStorage:', error);
      return false;
    }
  }

  /**
   * 重置配置为默认值
   */
  static resetConfig(): boolean {
    try {
      localStorage.setItem(TASK_CARD_CONFIG_KEY, JSON.stringify(defaultTaskCardConfig));
      return true;
    } catch (error) {
      console.error('Failed to reset task card config:', error);
      return false;
    }
  }

  /**
   * 导出配置为JSON文件
   */
  static exportConfig(config: TaskCardConfig): void {
    try {
      const configJson = JSON.stringify(config, null, 2);
      // 使用安全的下载函数
      safeDownloadFile(
        configJson,
        `task-card-config-${new Date().toISOString().split('T')[0]}.json`,
        'application/json'
      );
    } catch (error) {
      console.error('Failed to export task card config:', error);
      throw error;
    }
  }

  /**
   * 从文件导入配置
   */
  static importConfig(file: File): Promise<TaskCardConfig> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = e => {
        try {
          const configJson = e.target?.result as string;
          const importedConfig = JSON.parse(configJson);

          if (this.validateConfig(importedConfig)) {
            resolve(importedConfig);
          } else {
            reject(new Error('Invalid config structure'));
          }
        } catch (error) {
          reject(new Error('Failed to parse config file'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  /**
   * 验证配置结构的完整性
   */
  static validateConfig(config: any): config is TaskCardConfig {
    if (!config || typeof config !== 'object') {
      return false;
    }

    // 验证style配置
    if (!config.style || typeof config.style !== 'object') {
      return false;
    }

    const requiredStyleProps = ['theme', 'borderRadius', 'shadow', 'animation', 'spacing'];
    for (const prop of requiredStyleProps) {
      if (!(prop in config.style)) {
        return false;
      }
    }

    // 验证areas配置
    if (!config.areas || typeof config.areas !== 'object') {
      return false;
    }

    const requiredAreas = ['top', 'vehicle', 'content', 'bottom'];
    for (const area of requiredAreas) {
      if (!(area in config.areas)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 合并配置（用于部分更新）
   */
  static mergeConfig(baseConfig: TaskCardConfig, updates: Partial<TaskCardConfig>): TaskCardConfig {
    return {
      ...baseConfig,
      style: {
        ...baseConfig.style,
        ...(updates.style || {}),
      },
      areas: {
        ...baseConfig.areas,
        ...(updates.areas || {}),
      },
    };
  }

  /**
   * 获取配置版本信息
   */
  static getConfigVersion(): string {
    return TASK_CARD_CONFIG_KEY.split('_')[1] || '1.0';
  }

  /**
   * 清除所有配置数据
   */
  static clearConfig(): boolean {
    try {
      localStorage.removeItem(TASK_CARD_CONFIG_KEY);
      return true;
    } catch (error) {
      console.error('Failed to clear task card config:', error);
      return false;
    }
  }

  /**
   * 检查是否有保存的配置
   */
  static hasStoredConfig(): boolean {
    try {
      return localStorage.getItem(TASK_CARD_CONFIG_KEY) !== null;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取配置大小（字节）
   */
  static getConfigSize(): number {
    try {
      const config = localStorage.getItem(TASK_CARD_CONFIG_KEY);
      return config ? new Blob([config]).size : 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 创建配置备份
   */
  static createBackup(): string | null {
    try {
      const config = localStorage.getItem(TASK_CARD_CONFIG_KEY);
      if (config) {
        const backupKey = `${TASK_CARD_CONFIG_KEY}_backup_${Date.now()}`;
        localStorage.setItem(backupKey, config);
        return backupKey;
      }
      return null;
    } catch (error) {
      console.error('Failed to create config backup:', error);
      return null;
    }
  }

  /**
   * 恢复配置备份
   */
  static restoreBackup(backupKey: string): boolean {
    try {
      const backupConfig = localStorage.getItem(backupKey);
      if (backupConfig) {
        localStorage.setItem(TASK_CARD_CONFIG_KEY, backupConfig);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to restore config backup:', error);
      return false;
    }
  }
}
