// src/components/sections/task-list/components/task-list-fab.tsx
import React, { useRef } from 'react';

import {
  BarChart3,
  Columns,
  FileDown,
  FileUp,
  LayoutGrid,
  MoreVertical,
  RotateCcw,
  Rows,
  Settings2,
} from 'lucide-react';

import { Button } from '@/shared/components/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/shared/components/dropdown-menu';
import { cn } from '@/core/lib/utils';
import type { TaskListDensityMode, TaskListDisplayMode, VehicleDisplayMode } from '@/core/types';

import {
  getTaskListDensityMode,
  getTaskListDisplayMode,
  getVehicleDisplayMode,
} from './task-list-type-guards';

interface TaskListFABProps {
  // Position and dragging
  fabPosition: { x: number; y: number };
  isFabDragging: boolean;
  fabSize: number;

  // Settings
  displayMode: TaskListDisplayMode;
  density: Exclude<TaskListDensityMode, 'table' | 'card'>;
  enableZebraStriping: boolean;
  vehicleDisplayMode: VehicleDisplayMode;
  isSettingsLoaded: boolean;

  // Event handlers
  onDisplayModeChange: (mode: TaskListDisplayMode) => void;
  onDensityChange: (density: Exclude<TaskListDensityMode, 'table' | 'card'>) => void;
  onZebraStripingChange: (enabled: boolean) => void;
  onVehicleDisplayModeChange: (mode: VehicleDisplayMode) => void;
  onOpenColumnVisibilityModal: () => void;
  onOpenGroupConfig: () => void;
  onOpenStyleEditorModal: () => void;
  onTriggerImport: () => void;
  onExportSettings: () => void;
  onResetAllSettings: () => void;
  onFabContextMenu: (e: React.MouseEvent) => void;
}

export function TaskListFAB({
  fabPosition,
  isFabDragging,
  fabSize,
  displayMode,
  density,
  enableZebraStriping,
  vehicleDisplayMode,
  isSettingsLoaded,
  onDisplayModeChange,
  onDensityChange,
  onZebraStripingChange,
  onVehicleDisplayModeChange,
  onOpenColumnVisibilityModal,
  onOpenGroupConfig,
  onOpenStyleEditorModal,
  onTriggerImport,
  onExportSettings,
  onResetAllSettings,
  onFabContextMenu,
}: TaskListFABProps) {
  const fabRef = useRef<HTMLButtonElement>(null);

  if (displayMode !== 'table' || !isSettingsLoaded) {
    return null;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          ref={fabRef}
          size='icon'
          className={cn(
            'rounded-full shadow-lg absolute z-40 transition-all duration-200',
            isFabDragging ? 'cursor-grabbing scale-110 shadow-xl' : 'cursor-grab hover:scale-105'
          )}
          style={{
            top: fabPosition.y,
            left: fabPosition.x,
            width: `${fabSize}px`,
            height: `${fabSize}px`,
          }}
          title='更多 (可拖拽调整位置)'
          onContextMenu={onFabContextMenu}
          onMouseDown={e => {
            // 阻止左键点击时的默认行为，确保只有右键才能拖拽
            if (e.button === 2) {
              e.preventDefault();
              e.stopPropagation();
              onFabContextMenu(e);
            }
          }}
        >
          <MoreVertical className='h-5 w-5' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-56'>
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <Settings2 className='mr-2 h-3.5 w-3.5' />
            视图设置
          </DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent>
              <DropdownMenuLabel>显示模式</DropdownMenuLabel>
              <DropdownMenuRadioGroup
                value={displayMode}
                onValueChange={value => onDisplayModeChange(getTaskListDisplayMode(value))}
              >
                <DropdownMenuRadioItem value='table'>
                  <Rows className='mr-2 h-3.5 w-3.5' />
                  表格
                </DropdownMenuRadioItem>
                <DropdownMenuRadioItem value='card'>
                  <LayoutGrid className='mr-2 h-3.5 w-3.5' />
                  卡片
                </DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>密度</DropdownMenuLabel>
              <DropdownMenuRadioGroup
                value={density}
                onValueChange={value => onDensityChange(getTaskListDensityMode(value))}
              >
                <DropdownMenuRadioItem value='compact'>紧凑</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value='normal'>标准</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value='loose'>宽松</DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem
                checked={enableZebraStriping}
                onCheckedChange={checked => onZebraStripingChange(!!checked)}
              >
                斑马条纹
              </DropdownMenuCheckboxItem>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>车辆显示</DropdownMenuLabel>
              <DropdownMenuRadioGroup
                value={vehicleDisplayMode}
                onValueChange={value => onVehicleDisplayModeChange(getVehicleDisplayMode(value))}
              >
                <DropdownMenuRadioItem value='licensePlate'>车牌号</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value='internalId'>内部编号</DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>
        <DropdownMenuItem onClick={onOpenColumnVisibilityModal}>
          <Columns className='mr-2 h-3.5 w-3.5' />
          列显示与排序
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onOpenGroupConfig}>
          <BarChart3 className='mr-2 h-3.5 w-3.5' />
          分组设置
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onOpenStyleEditorModal}>
          <MoreVertical className='mr-2 h-3.5 w-3.5' />
          车卡样式
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={onTriggerImport}>
          <FileUp className='mr-2 h-3.5 w-3.5' />
          导入样式
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onExportSettings}>
          <FileDown className='mr-2 h-3.5 w-3.5' />
          导出样式
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onResetAllSettings}>
          <RotateCcw className='mr-2 h-3.5 w-3.5' />
          恢复默认
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
