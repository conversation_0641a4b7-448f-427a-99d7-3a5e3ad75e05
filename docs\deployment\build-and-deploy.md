# 🚀 构建部署指南

## 📋 部署概述

TMH车辆调度系统支持多种部署方式，包括传统服务器部署、容器化部署和云平台部署。

## 🏗️ 构建流程

### 本地构建
```bash
# 1. 安装依赖
npm install

# 2. 环境配置
cp .env.example .env.production
# 编辑 .env.production 文件

# 3. 类型检查
npm run typecheck

# 4. 代码检查
npm run lint

# 5. 运行测试
npm run test

# 6. 构建生产版本
npm run build

# 7. 启动生产服务器
npm run start
```

### 构建优化
```bash
# 分析打包大小
npm run analyze

# 生成静态文件
npm run export

# 压缩构建产物
npm run compress
```

## 🐳 Docker 部署

### Dockerfile
```dockerfile
# 多阶段构建
FROM node:18-alpine AS base

# 安装依赖阶段
FROM base AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# 构建阶段
FROM base AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# 运行阶段
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Docker Compose
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=https://api.tmh.com
    depends_on:
      - redis
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
```

### 构建和运行
```bash
# 构建镜像
docker build -t tmh-dispatcher .

# 运行容器
docker run -p 3000:3000 tmh-dispatcher

# 使用 Docker Compose
docker-compose up -d

# 查看日志
docker-compose logs -f app

# 停止服务
docker-compose down
```

## ☁️ 云平台部署

### Vercel 部署
```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login

# 部署到 Vercel
vercel --prod

# 配置环境变量
vercel env add NEXT_PUBLIC_API_BASE_URL
```

### Netlify 部署
```toml
# netlify.toml
[build]
  command = "npm run build"
  publish = "out"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### AWS 部署
```yaml
# aws-deploy.yml
version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
  
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG .
      - docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$IMAGE_TAG
  
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker image...
      - docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$IMAGE_TAG
```

## 🌐 Nginx 配置

### 基础配置
```nginx
server {
    listen 80;
    server_name tmh-dispatcher.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name tmh-dispatcher.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=********; includeSubDomains" always;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 代理到 Next.js 应用
    location / {
        proxy_pass http://app:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 负载均衡
```nginx
upstream app_servers {
    server app1:3000 weight=3;
    server app2:3000 weight=2;
    server app3:3000 weight=1;
}

server {
    listen 443 ssl http2;
    server_name tmh-dispatcher.com;

    location / {
        proxy_pass http://app_servers;
        # 其他配置...
    }
}
```

## 🔧 环境配置

### 生产环境变量
```bash
# 应用配置
NODE_ENV=production
NEXT_PUBLIC_APP_NAME="TMH车辆调度系统"
NEXT_PUBLIC_APP_VERSION="1.0.0"

# API 配置
NEXT_PUBLIC_API_BASE_URL="https://api.tmh.com"
NEXT_PUBLIC_USE_MOCK_DATA="false"

# 安全配置
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="https://tmh-dispatcher.com"

# 数据库配置
DATABASE_URL="postgresql://user:password@localhost:5432/tmh_db"
REDIS_URL="redis://localhost:6379"

# 第三方服务
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY="your-api-key"
SENTRY_DSN="your-sentry-dsn"

# 功能开关
NEXT_PUBLIC_ENABLE_DEBUG="false"
NEXT_PUBLIC_ENABLE_ANALYTICS="true"
```

### 配置管理
```typescript
// config/environment.ts
export const config = {
  app: {
    name: process.env.NEXT_PUBLIC_APP_NAME || 'TMH车辆调度系统',
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    env: process.env.NODE_ENV || 'development',
  },
  api: {
    baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001',
    timeout: parseInt(process.env.API_TIMEOUT || '10000'),
  },
  features: {
    enableDebug: process.env.NEXT_PUBLIC_ENABLE_DEBUG === 'true',
    enableAnalytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
    useMockData: process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true',
  },
};
```

## 📊 监控和日志

### 健康检查
```typescript
// pages/api/health.ts
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.NEXT_PUBLIC_APP_VERSION,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV,
  };

  res.status(200).json(health);
}
```

### 日志配置
```typescript
// lib/logger.ts
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

export default logger;
```

### 性能监控
```typescript
// lib/monitoring.ts
import { NextApiRequest, NextApiResponse } from 'next';

export function withMonitoring(handler: Function) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const start = Date.now();
    
    try {
      await handler(req, res);
    } catch (error) {
      // 错误上报
      console.error('API Error:', error);
    } finally {
      const duration = Date.now() - start;
      console.log(`${req.method} ${req.url} - ${duration}ms`);
    }
  };
}
```

## 🔄 CI/CD 流程

### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run typecheck
      - run: npm run lint
      - run: npm run test

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run build
      
      - name: Build Docker image
        run: docker build -t tmh-dispatcher:${{ github.sha }} .
      
      - name: Push to registry
        run: |
          echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
          docker push tmh-dispatcher:${{ github.sha }}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to production
        run: |
          # 部署脚本
          echo "Deploying to production..."
```

### 部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 开始部署..."

# 1. 拉取最新代码
git pull origin main

# 2. 安装依赖
npm ci

# 3. 运行测试
npm run test

# 4. 构建应用
npm run build

# 5. 重启服务
pm2 restart tmh-dispatcher

# 6. 健康检查
sleep 10
curl -f http://localhost:3000/api/health || exit 1

echo "✅ 部署完成!"
```

## 🔒 安全配置

### HTTPS 配置
```bash
# 使用 Let's Encrypt 获取 SSL 证书
certbot --nginx -d tmh-dispatcher.com

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

### 安全头配置
```typescript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};
```

## 📈 性能优化

### 缓存策略
```typescript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=********, immutable',
          },
        ],
      },
    ];
  },
};
```

### CDN 配置
```typescript
// next.config.js
module.exports = {
  assetPrefix: process.env.NODE_ENV === 'production' 
    ? 'https://cdn.tmh.com' 
    : '',
  
  images: {
    domains: ['cdn.tmh.com'],
  },
};
```

---

**文档版本**: v1.0  
**最后更新**: 2025/7/16  
**运维负责人**: TMH运维团队
