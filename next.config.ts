import type { NextConfig } from 'next';

// 包体积分析 - 条件导入
let withBundleAnalyzer: any = (config: NextConfig) => config;
try {
  if (process.env['ANALYZE'] === 'true') {
    withBundleAnalyzer = require('@next/bundle-analyzer')({
      enabled: true,
    });
  }
} catch (error) {
  console.warn('Bundle analyzer not available, skipping...');
}

const nextConfig: NextConfig = {
  devIndicators: false,
  /* config options here */
  // 修复React DevTools兼容性问题
  reactStrictMode: false, // 暂时禁用严格模式以减少DevTools错误
  typescript: {
    // 只在开发环境忽略构建错误，生产环境保持严格检查
    ignoreBuildErrors: process.env.NODE_ENV === 'development',
  },
  eslint: {
    // 只在开发环境忽略ESLint错误，生产环境保持严格检查
    ignoreDuringBuilds: process.env.NODE_ENV === 'development',
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
    ],
  },
  // 跳过有问题页面的静态生成
  output: 'standalone',

  // 启用实验性功能以提升性能
  experimental: {
    optimizePackageImports: [
      '@radix-ui/react-icons',
      'lucide-react',
      'recharts',

      '@tanstack/react-query',
      '@tanstack/react-table',
      'date-fns',
      'clsx',
      'class-variance-authority'
    ],
    // reactCompiler: true, // 暂时禁用React编译器，等待稳定版本
    webpackMemoryOptimizations: true, // 减少Webpack内存使用
    mdxRs: true, // 使用Rust编译器处理MDX文件（如果项目使用MDX）
    turbo: {
      // Turbopack 优化配置
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
      // 修复 Genkit AI 包的 Turbopack 兼容性问题
      resolveAlias: {
        '@genkit-ai/googleai': '@genkit-ai/googleai/lib/index.js',
        'genkit': 'genkit/lib/index.js',
      },
    },
  },
  // 编译器优化
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
  },
  // Webpack 配置优化
  webpack: (config, { dev, isServer }) => {
    // 修复 ESM 模块解析问题
    config.module.rules.push({
      test: /\.m?js$/,
      type: 'javascript/auto',
      resolve: {
        fullySpecified: false,
      },
    });

    // 处理 Genkit AI 依赖问题 - 修复 Turbopack 兼容性
    if (!isServer) {
      // 在客户端构建时忽略服务器端依赖
      config.resolve.fallback = {
        ...config.resolve.fallback,
        '@genkit-ai/firebase': false,
        '@genkit-ai/googleai': false,
        'genkit': false,
        'handlebars': false,
        'fs': false,
        'path': false,
        'os': false,
        'crypto': false,
        'stream': false,
        'util': false,
      };
    }

    // 修复 Genkit AI ESM 模块解析问题
    config.resolve.alias = {
      ...config.resolve.alias,
      '@genkit-ai/googleai$': '@genkit-ai/googleai/lib/index.js',
      'genkit$': 'genkit/lib/index.js',
    };

    // 忽略 Genkit AI 的可选依赖
    config.externals = config.externals || [];
    if (Array.isArray(config.externals)) {
      config.externals.push({
        '@genkit-ai/firebase': 'commonjs @genkit-ai/firebase',
        'handlebars': 'commonjs handlebars',
      });
    }

    // 添加对 handlebars 的特殊处理
    config.module.rules.push({
      test: /node_modules\/handlebars\/lib\/index\.js$/,
      use: 'null-loader'
    });

    // 生产环境优化
    if (!dev) {
      // 启用更激进的代码分割
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            // 核心基础包 - 最高优先级
            core: {
              name: 'core',
              test: /[\\/]src[\\/](types|constants|utils[\\/]common|lib)[\\/]/,
              priority: 100,
              chunks: 'all',
              enforce: true,
              minSize: 10000,
            },

            // 调度功能包
            dispatch: {
              name: 'dispatch',
              test: /[\\/]src[\\/](modules[\\/]dispatch|components[\\/](task-list|vehicle-dispatch)|store[\\/](taskListStore|vehicleDispatchStore|storeOrchestrator)|hooks[\\/](task-list|vehicle-dispatch|useTask|useVehicle))[\\/]/,
              priority: 80,
              chunks: 'all',
              minSize: 20000,
            },

            // 配比功能包
            ratio: {
              name: 'ratio',
              test: /[\\/]src[\\/](modules[\\/]ratio|components[\\/](pages[\\/]ratio|ratio)|store[\\/]ratio|hooks[\\/]ratio|services[\\/]ratio|business[\\/]ratio)[\\/]/,
              priority: 80,
              chunks: 'all',
              minSize: 20000,
            },

            // 模态框包
            modals: {
              name: 'modals',
              test: /[\\/]src[\\/]components[\\/]modals[\\/]/,
              priority: 75,
              chunks: 'async',
              minSize: 15000,
            },

            // 图表包
            charts: {
              name: 'charts',
              test: /[\\/]src[\\/]components[\\/]charts[\\/]/,
              priority: 75,
              chunks: 'async',
              minSize: 15000,
            },

            // UI组件包
            uiComponents: {
              name: 'ui-components',
              test: /[\\/]src[\\/](components[\\/](ui|common|shared|layout)|contexts)[\\/]/,
              priority: 70,
              chunks: 'all',
              minSize: 15000,
            },

            // React 相关库单独打包 - 最高优先级
            react: {
              test: /[\\/]node_modules[\\/](react|react-dom|@tanstack|zustand)[\\/]/,
              name: 'react-libs',
              chunks: 'all',
              priority: 90,
              enforce: true,
            },

            // UI 组件库单独打包
            uiLibs: {
              test: /[\\/]node_modules[\\/](@radix-ui|lucide-react)[\\/]/,
              name: 'ui-libs',
              chunks: 'all',
              priority: 85,
              minSize: 25000,
            },

            // 图表库单独打包
            chartLibs: {
              test: /[\\/]node_modules[\\/](recharts|victory|d3)[\\/]/,
              name: 'chart-libs',
              chunks: 'all',
              priority: 75,
              minSize: 30000,
            },

            // 工具库单独打包
            utils: {
              test: /[\\/]node_modules[\\/](lodash|date-fns|clsx|immer)[\\/]/,
              name: 'util-libs',
              chunks: 'all',
              priority: 65,
              minSize: 20000,
            },

            // 第三方库单独打包
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 60,
              minSize: 30000,
            },
          },
        },
      };

      // 启用 Tree Shaking
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
    }

    return config;
  },
  transpilePackages: [
    '@tanstack/react-table',
    '@tanstack/react-query',
    '@tanstack/react-virtual',
    '@tanstack/react-query-devtools',
    '@genkit-ai/googleai',
    '@genkit-ai/next',
    'genkit'
  ], // 需要特别处理的包

};

export default withBundleAnalyzer(nextConfig);
