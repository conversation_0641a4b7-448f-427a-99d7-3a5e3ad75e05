/**
 * 配比通知单解析和应用服务
 */

import type {
  RatioNotification,
  NotificationParseResult,
  NotificationApplyResult,
  SupportedFileFormat,
  FileParseConfig,
} from '@/core/types/ratio-notification';
import type { RatioCalculationParams, UnifiedRatioMaterial } from '@/core/types/ratio';

/**
 * 配比通知单服务类
 */
export class RatioNotificationService {
  /**
   * 解析文件内容为通知单数据
   */
  static async parseFile(file: File, config?: FileParseConfig): Promise<NotificationParseResult> {
    try {
      const content = await this.readFileContent(file);
      const format = this.detectFileFormat(file, config?.format);

      switch (format) {
        case 'json':
          return this.parseJsonContent(content);
        case 'xml':
          return this.parseXmlContent(content);
        case 'txt':
          return this.parseTextContent(content);
        case 'csv':
          return this.parseCsvContent(content);
        default:
          return {
            success: false,
            errors: [`不支持的文件格式: ${format}`],
          };
      }
    } catch (error) {
      return {
        success: false,
        errors: [error instanceof Error ? error.message : '文件解析失败'],
      };
    }
  }

  /**
   * 应用通知单到当前配比
   */
  static applyNotification(
    notification: RatioNotification,
    currentParams?: RatioCalculationParams,
    currentMaterials?: UnifiedRatioMaterial[]
  ): NotificationApplyResult {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];
      const summary = {
        updatedParams: [] as string[],
        updatedMaterials: [] as string[],
        addedMaterials: [] as string[],
        removedMaterials: [] as string[],
      };

      // 转换计算参数
      const calculationParams = {
        targetStrength: parseInt(notification.info.strengthGrade.replace('C', '')),
        waterCementRatio: notification.params.waterCementRatio,
        sandRatio: notification.params.sandRatio,
        waterContent: notification.params.waterContent,
        slump: notification.params.slump,
        cementType: 'P.O 42.5', // 默认值，可以从材料中推断
        aggregateType: '碎石', // 默认值
        // admixtureType: '聚羧酸减水剂', // 默认值 - 移除不存在的属性
        exposureClass: 'XC1' as any, // 默认值
        placementMethod: 'pump' as any, // 默认值
        finishingRequirement: 'standard' as any, // 默认值
        // specialRequirements: [], // 移除不存在的属性
      };

      // 记录参数变更
      if (currentParams) {
        if (currentParams['targetStrength'] !== calculationParams.targetStrength) {
          summary.updatedParams.push('目标强度');
        }
        if (currentParams['waterCementRatio'] !== calculationParams.waterCementRatio) {
          summary.updatedParams.push('水胶比');
        }
        if (currentParams['sandRatio'] !== calculationParams.sandRatio) {
          summary.updatedParams.push('砂率');
        }
        if (currentParams['waterContent'] !== calculationParams.waterContent) {
          summary.updatedParams.push('用水量');
        }
        if (currentParams['slump'] !== calculationParams.slump) {
          summary.updatedParams.push('坍落度');
        }
      }

      // 转换材料列表
      const materials = notification.materials.map((notifMaterial: any, index: number) => {
        const materialType = this.mapMaterialType(notifMaterial.materialType);

        return {
          id: `material-${index + 1}`,
          name: notifMaterial.materialName,
          type: materialType,
          category: this.getMaterialCategory(materialType),
          specification: notifMaterial.specification || '',
          theoreticalAmount: notifMaterial.dosage,
          actualAmount: notifMaterial.dosage,
          waterContent: materialType === 'sand' || materialType === 'stone' ? 2.5 : 0,
          stoneContent: materialType === 'sand' ? 0 : materialType === 'stone' ? 1 : 0,
          density: this.getDefaultDensity(materialType),
          unitPrice: 0, // 默认值
          supplier: notifMaterial.supplier || '',
          storageLocation: this.getDefaultStorageLocation(materialType),
          availabilityStatus: 'available',
          lastUpdated: new Date().toISOString(),
          notes: notifMaterial.remarks || '',
        };
      });

      // 记录材料变更
      if (currentMaterials) {
        const currentMaterialNames = new Set(currentMaterials.map(m => m.name));
        const newMaterialNames = new Set(materials.map(m => m.name));

        materials.forEach(material => {
          if (currentMaterialNames.has(material.name)) {
            summary.updatedMaterials.push(material.name);
          } else {
            summary.addedMaterials.push(material.name);
          }
        });

        currentMaterials.forEach(material => {
          if (!newMaterialNames.has(material.name)) {
            summary.removedMaterials.push(material.name);
          }
        });
      } else {
        summary.addedMaterials = materials.map(m => m.name);
      }

      // 验证数据合理性
      this.validateNotificationData(notification, errors, warnings);

      return {
        success: errors.length === 0,
        calculationParams: calculationParams as any,
        materials: materials as any,
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined,
        summary,
      };
    } catch (error) {
      return {
        success: false,
        errors: [error instanceof Error ? error.message : '应用通知单失败'],
      };
    }
  }

  /**
   * 读取文件内容
   */
  private static readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = e => resolve(e.target?.result as string);
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsText(file, 'utf-8');
    });
  }

  /**
   * 检测文件格式
   */
  private static detectFileFormat(
    file: File,
    configFormat?: SupportedFileFormat
  ): SupportedFileFormat {
    if (configFormat) return configFormat;

    const extension = file.name.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'json':
        return 'json';
      case 'xml':
        return 'xml';
      case 'txt':
        return 'txt';
      case 'csv':
        return 'csv';
      default:
        return 'json'; // 默认尝试JSON格式
    }
  }

  /**
   * 解析JSON格式内容
   */
  private static parseJsonContent(content: string): NotificationParseResult {
    try {
      const data = JSON.parse(content);

      // 验证JSON结构
      if (!this.isValidNotificationStructure(data)) {
        return {
          success: false,
          errors: ['JSON格式不符合通知单结构要求'],
          rawData: data,
        };
      }

      return {
        success: true,
        notification: data as RatioNotification,
        rawData: data,
      };
    } catch (error) {
      return {
        success: false,
        errors: ['JSON格式解析失败'],
      };
    }
  }

  /**
   * 解析XML格式内容（简化实现）
   */
  private static parseXmlContent(content: string): NotificationParseResult {
    // 这里应该实现XML解析逻辑
    // 为了简化，暂时返回不支持
    return {
      success: false,
      errors: ['XML格式解析功能正在开发中'],
    };
  }

  /**
   * 解析文本格式内容（简化实现）
   */
  private static parseTextContent(content: string): NotificationParseResult {
    // 这里应该实现文本解析逻辑
    // 为了简化，暂时返回不支持
    return {
      success: false,
      errors: ['文本格式解析功能正在开发中'],
    };
  }

  /**
   * 解析CSV格式内容（简化实现）
   */
  private static parseCsvContent(content: string): NotificationParseResult {
    // 这里应该实现CSV解析逻辑
    // 为了简化，暂时返回不支持
    return {
      success: false,
      errors: ['CSV格式解析功能正在开发中'],
    };
  }

  /**
   * 验证通知单JSON结构
   */
  private static isValidNotificationStructure(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      data.id &&
      data.info &&
      data.params &&
      data.materials &&
      Array.isArray(data.materials) &&
      data.quality &&
      data.approval
    );
  }

  /**
   * 映射材料类型
   */
  private static mapMaterialType(notificationMaterialType: string): string {
    const typeMap: Record<string, string> = {
      cement: 'cement',
      sand: 'sand',
      stone: 'stone',
      water: 'water',
      admixture: 'admixture',
      flyash: 'flyash',
      mineralPowder: 'mineralPowder',
      水泥: 'cement',
      砂子: 'sand',
      石子: 'stone',
      水: 'water',
      外加剂: 'admixture',
      粉煤灰: 'flyash',
      矿粉: 'mineralPowder',
    };

    return typeMap[notificationMaterialType] || 'other';
  }

  /**
   * 获取材料分类
   */
  private static getMaterialCategory(materialType: string): string {
    const categoryMap: Record<string, string> = {
      cement: 'cementitious',
      flyash: 'cementitious',
      mineralPowder: 'cementitious',
      sand: 'aggregate',
      stone: 'aggregate',
      water: 'water',
      admixture: 'admixture',
    };

    return categoryMap[materialType] || 'other';
  }

  /**
   * 获取默认密度
   */
  private static getDefaultDensity(materialType: string): number {
    const densityMap: Record<string, number> = {
      cement: 3100,
      sand: 2650,
      stone: 2700,
      water: 1000,
      admixture: 1200,
      flyash: 2200,
      mineralPowder: 2900,
    };

    return densityMap[materialType] || 2500;
  }

  /**
   * 获取默认存储位置
   */
  private static getDefaultStorageLocation(materialType: string): string {
    const locationMap: Record<string, string> = {
      cement: '1#水泥仓',
      sand: '砂仓1',
      stone: '石仓1',
      water: '水仓',
      admixture: '外加剂仓',
      flyash: '粉煤灰仓',
      mineralPowder: '矿粉仓',
    };

    return locationMap[materialType] || '其他仓';
  }

  /**
   * 验证通知单数据合理性
   */
  private static validateNotificationData(
    notification: RatioNotification,
    errors: string[],
    warnings: string[]
  ): void {
    // 验证水胶比范围
    if (notification.params.waterCementRatio < 0.25 || notification.params.waterCementRatio > 0.8) {
      warnings.push(`水胶比 ${notification.params.waterCementRatio} 超出常规范围 (0.25-0.8)`);
    }

    // 验证砂率范围
    if (notification.params.sandRatio < 25 || notification.params.sandRatio > 45) {
      warnings.push(`砂率 ${notification.params.sandRatio}% 超出常规范围 (25%-45%)`);
    }

    // 验证坍落度范围
    if (notification.params.slump < 30 || notification.params.slump > 240) {
      warnings.push(`坍落度 ${notification.params.slump}mm 超出常规范围 (30-240mm)`);
    }

    // 验证强度等级
    const strengthValue = parseInt(notification.info.strengthGrade.replace('C', ''));
    if (strengthValue < 15 || strengthValue > 80) {
      warnings.push(`强度等级 ${notification.info.strengthGrade} 超出常规范围 (C15-C80)`);
    }

    // 验证材料完整性
    const requiredMaterials = ['cement', 'sand', 'stone', 'water'];
    const availableMaterials = notification.materials.map(m =>
      this.mapMaterialType(m.materialType)
    );

    requiredMaterials.forEach(required => {
      if (!availableMaterials.includes(required)) {
        errors.push(`缺少必需材料: ${required}`);
      }
    });

    // 验证审批状态
    if (notification.approval.approvalStatus !== 'approved') {
      warnings.push(`通知单审批状态为: ${notification.approval.approvalStatus}`);
    }

    // 验证有效期
    if (notification.info.validUntil) {
      const validUntil = new Date(notification.info.validUntil);
      const now = new Date();
      if (validUntil < now) {
        warnings.push('通知单已过期');
      }
    }
  }
}
