// src/components/sections/task-list/cells/DispatchReminderCell.tsx
'use client';

import React, { memo } from 'react';

import { DispatchCountdown } from '@/shared/components/dispatch-countdown';
import type { Task } from '@/core/types';

// src/components/sections/task-list/cells/DispatchReminderCell.tsx

interface DispatchReminderCellProps {
  task: Task;
  textClassName?: string;
}

const DispatchReminderCellComponent: React.FC<DispatchReminderCellProps> = ({
  task,
  textClassName,
}) => {
  // 在开发环境下添加调试日志
  if (process.env.NODE_ENV === 'development' && task.taskNumber === 'S10001') {
    console.log('🔔 DispatchReminderCell 渲染:', {
      taskNumber: task.taskNumber,
      dispatchStatus: task.dispatchStatus,
      nextScheduledTime: task.nextScheduledDispatchTime,
      lastDispatchTime: task.lastDispatchTime,
      dispatchFrequency: task.dispatchFrequencyMinutes,
      timestamp: new Date().toLocaleTimeString(),
    });
  }

  return (
    <div className={`${textClassName} flex items-center justify-center h-full text-xs`}>
      <DispatchCountdown
        nextScheduledTime={task.nextScheduledDispatchTime}
        lastDispatchTime={task.lastDispatchTime}
        dispatchFrequencyMinutes={task.dispatchFrequencyMinutes}
        dispatchStatus={task.dispatchStatus}
        compact={true}
        showIcon={false}
      />
    </div>
  );
};

export const DispatchReminderCell = memo(DispatchReminderCellComponent, (prevProps, nextProps) => {
  // 只有当发车提醒相关的关键字段都相同时才跳过重新渲染
  return (
    prevProps.task.id === nextProps.task.id &&
    prevProps.task.nextScheduledDispatchTime === nextProps.task.nextScheduledDispatchTime &&
    prevProps.task.lastDispatchTime === nextProps.task.lastDispatchTime &&
    prevProps.task.dispatchFrequencyMinutes === nextProps.task.dispatchFrequencyMinutes &&
    prevProps.task.dispatchStatus === nextProps.task.dispatchStatus &&
    prevProps.textClassName === nextProps.textClassName
  );
});
