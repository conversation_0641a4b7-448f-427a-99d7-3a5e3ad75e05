/**
 * 测试数据工厂
 * 提供创建测试数据的工厂函数
 *
 * 注意：这个文件不包含测试用例，只是数据工厂
 */

import type { DeliveryOrder, Plant, Task, TaskListStoredSettings, Vehicle } from '@/core/types';

/**
 * 创建模拟任务数据
 * @param count - 创建数量
 * @param overrides - 覆盖属性
 * @returns 模拟任务数组
 */
export function createMockTasks(count: number = 1, overrides: Partial<Task> = {}): Task[] {
  return Array.from(
    { length: count },
    (_, index) =>
      ({
        // 必需字段
        id: `task-${index + 1}`,
        plantId: overrides.plantId || `plant-${(index % 3) + 1}`,
        taskNumber: `T${String(index + 1).padStart(4, '0')}`,
        projectName: overrides.projectName || `项目${index + 1}`,
        projectAbbreviation: overrides.projectAbbreviation || `项目${index + 1}`,
        constructionUnit: overrides.constructionUnit || `施工单位${index + 1}`,
        constructionSite: overrides.constructionSite || `施工地点${index + 1}`,
        strength: overrides.strength || 'C30',
        pouringMethod: overrides.pouringMethod || '泵送',
        vehicleCount: overrides.vehicleCount || Math.floor(Math.random() * 3) + 1,
        completedVolume: overrides.completedVolume || 0,
        requiredVolume: overrides.requiredVolume || Math.floor(Math.random() * 100) + 20,
        pumpTruck: overrides.pumpTruck || `37米泵车`,
        otherRequirements: overrides.otherRequirements || '无特殊要求',
        contactPhone: overrides.contactPhone || `138${String(index + 1).padStart(8, '0')}`,
        supplyTime: overrides.supplyTime || '08:00',
        supplyDate:
          overrides.supplyDate ||
          new Date(Date.now() + index * 3600000).toISOString().split('T')[0],
        publishDate: overrides.publishDate || new Date().toISOString().split('T')[0],
        dispatchStatus: overrides.dispatchStatus || 'New',
        status: overrides.status || 'New',
        freezeResistance: overrides.freezeResistance || 'F50',
        impermeability: overrides.impermeability || 'P6',
        deliveryStatus: overrides.deliveryStatus || '待发货',
        dispatchReminderMinutes: overrides.dispatchReminderMinutes || 30,
        vehicles: overrides.vehicles || undefined,
        ...overrides,
      }) as Task
  );
}

/**
 * 创建模拟车辆数据
 * @param count - 创建数量
 * @param overrides - 覆盖属性
 * @returns 模拟车辆数组
 */
export function createMockVehicles(count: number = 1, overrides: Partial<Vehicle> = {}): Vehicle[] {
  return Array.from({ length: count }, (_, index) => ({
    // 必需字段
    id: `vehicle-${index + 1}`,
    vehicleNumber: overrides.vehicleNumber || `京A${String(index + 1).padStart(5, '0')}`,
    status:
      overrides.status || (['pending', 'returned', 'outbound'][index % 3] as Vehicle['status']),
    type: overrides.type || (['Tanker', 'Pump', 'Other'][index % 3] as Vehicle['type']),
    // 可选字段
    operationalStatus: overrides.operationalStatus || 'normal',
    assignedTaskId: overrides.assignedTaskId || undefined,
    assignedProductionLineId: overrides.assignedProductionLineId || undefined,
    currentTripType: overrides.currentTripType || undefined,
    productionStatus: overrides.productionStatus || undefined,
    allowWeighRoomEdit: overrides.allowWeighRoomEdit || false,
    deliveryOrderId: overrides.deliveryOrderId || undefined,
    plantId: overrides.plantId || `plant-${(index % 3) + 1}`,
    lastTripWashedWithPumpWater: overrides.lastTripWashedWithPumpWater || false,
    isDragging: overrides.isDragging || false,
    ...overrides,
  }));
}

/**
 * 创建模拟搅拌站数据
 * @param count - 创建数量
 * @param overrides - 覆盖属性
 * @returns 模拟搅拌站数组
 */
export function createMockPlants(count: number = 1, overrides: Partial<Plant> = {}): Plant[] {
  return Array.from({ length: count }, (_, index) => ({
    id: `plant-${index + 1}`,
    name: overrides.name || `搅拌站${index + 1}`,
    stats: overrides.stats || {
      completedTasks: Math.floor(Math.random() * 50) + 10,
      totalTasks: Math.floor(Math.random() * 100) + 50,
    },
    productionLineCount: overrides.productionLineCount || Math.floor(Math.random() * 3) + 2,
    ...overrides,
  }));
}

/**
 * 创建模拟配送单数据
 * @param overrides - 覆盖属性
 * @returns 模拟配送单
 */
export function createMockDeliveryOrder(overrides: Partial<DeliveryOrder> = {}): DeliveryOrder {
  const id = Math.random().toString(36).substring(2, 11);
  return {
    id: `order-${id}`,
    plantId: overrides.plantId || `plant-${id}`,
    productionLineId: overrides.productionLineId || `line-${id}`,
    vehicleNumber: overrides.vehicleNumber || `京A${id.toUpperCase()}`,
    driver: overrides.driver || `司机${id}`,
    volume: overrides.volume || Math.floor(Math.random() * 100) + 20,
    strength: overrides.strength || 'C30',
    projectName: overrides.projectName || `项目${id}`,
    taskNumber: overrides.taskNumber || `T${id.toUpperCase()}`,
    status: overrides.status || 'newlyDispatched',
    customerName: overrides.customerName || `客户${id}`,
    notes: overrides.notes || '',
    vehicleStatusIcon: overrides.vehicleStatusIcon || undefined,
    ...overrides,
  };
}

/**
 * 创建模拟任务列表设置
 * @param overrides - 覆盖属性
 * @returns 模拟设置对象
 */
export function createMockTaskListSettings(
  overrides: Partial<TaskListStoredSettings> = {}
): TaskListStoredSettings {
  return {
    displayMode: overrides.displayMode || 'table',
    density: overrides.density || 'normal',
    enableZebraStriping: overrides.enableZebraStriping ?? false,
    columnOrder: overrides.columnOrder || ['taskNumber', 'projectName', 'strength', 'vehicleCount'],
    columnVisibility: {
      taskNumber: true,
      projectName: true,
      customerName: true,
      deliveryAddress: true,
      concreteGrade: true,
      volume: true,
      scheduledTime: true,
      status: true,
      priority: true,
      vehicleInfo: true,
      actions: true,
      ...overrides.columnVisibility,
    },
    columnWidths: {
      taskNumber: 120,
      projectName: 150,
      customerName: 120,
      deliveryAddress: 200,
      concreteGrade: 100,
      volume: 80,
      scheduledTime: 150,
      status: 100,
      priority: 100,
      vehicleInfo: 150,
      actions: 120,
      ...overrides.columnWidths,
    },
    columnTextStyles: overrides.columnTextStyles || {},
    columnBackgrounds: overrides.columnBackgrounds || {},
    inTaskVehicleCardStyles: overrides.inTaskVehicleCardStyles || {
      gap: 4,
      cardSize: 'small',
      cardWidth: 'w-14',
      cardHeight: 'h-8',
      fontSize: 'text-[12px]',
      fontColor: 'text-foreground',
      vehicleNumberFontWeight: 'font-medium',
      cardBgColor: 'bg-card/80',
      cardGradient: 'bg-gradient-to-r from-pink-100 to-pink-200',
      gradientEnabled: false,
      gradientDirection: 'to-r',
      gradientStartColor: '#3b82f6',
      gradientEndColor: '#8b5cf6',
      statusDotSize: 'w-1 h-1',
      borderRadius: 'rounded-md',
      boxShadow: 'shadow-sm',
      vehiclesPerRow: 3,
    },
    selectedPlantId: overrides.selectedPlantId ?? null,
    vehicleDisplayMode: overrides.vehicleDisplayMode || 'licensePlate',
    groupConfig: {
      enabled: false,
      groupBy: 'none',
      collapsible: true,
      defaultCollapsed: [],
      sortOrder: 'asc',
      showGroupStats: true,
      allowedGroupColumns: ['projectName', 'strength', 'pouringMethod'],
      disallowedGroupColumns: ['taskNumber', 'vehicleCount'],
      groupHeaderStyle: {
        backgroundColor: 'bg-muted/50',
        textColor: 'text-foreground',
        fontSize: 'text-sm',
        fontWeight: 'font-medium',
        padding: 'py-2',
      },
      ...overrides.groupConfig,
    },
    ...overrides,
  };
}

/**
 * 创建测试场景数据
 * @returns 包含各种测试数据的对象
 */
export function createTestScenario() {
  const plants = createMockPlants(3);
  const vehicles = createMockVehicles(8);
  const tasks = createMockTasks(10);

  // 建立一些关联关系
  tasks.forEach((task, index) => {
    if (plants[index % plants.length]) {
      task.plantId = plants[index % plants.length]!.id;
    }
    if (index < vehicles.length && vehicles[index]) {
      vehicles[index]!.assignedTaskId = task.id;
      vehicles[index]!.status = 'outbound';
    }
  });

  return {
    plants,
    vehicles,
    tasks,
    deliveryOrders: tasks.map(task =>
      createMockDeliveryOrder({
        plantId: task.plantId,
        projectName: task.projectName,
        strength: task.strength,
        volume: task.requiredVolume,
        taskNumber: task.taskNumber,
        customerName: task.customerName,
      })
    ),
  };
}

/**
 * 创建性能测试数据
 * @param scale - 数据规模倍数
 * @returns 大量测试数据
 */
export function createPerformanceTestData(scale: number = 1) {
  return {
    tasks: createMockTasks(100 * scale),
    vehicles: createMockVehicles(50 * scale),
    plants: createMockPlants(10 * scale),
  };
}

/**
 * 创建边界测试数据
 * @returns 包含边界情况的测试数据
 */
export function createBoundaryTestData() {
  return {
    emptyTasks: [],
    singleTask: createMockTasks(1),
    tasksWithNullValues: createMockTasks(3, {
      notes: undefined,
      subTheme: undefined,
      dispatchNote: undefined,
    }),
    tasksWithExtremeValues: [
      ...createMockTasks(1, { requiredVolume: 0 }),
      ...createMockTasks(1, { requiredVolume: 999999 }),
      ...createMockTasks(1, { supplyDate: '1970-01-01' }),
      ...createMockTasks(1, { supplyDate: '2099-12-31' }),
    ],
  };
}
