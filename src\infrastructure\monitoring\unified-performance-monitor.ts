/**
 * 统一性能监控工具
 * 合并并优化了原有的两个性能监控组件
 * 提供全面的性能监控、分析和报告功能
 */

import React from 'react';

// ==================== 统一性能指标类型定义 ====================

export interface UnifiedPerformanceMetrics {
  // Web Vitals 核心指标
  webVitals: {
    fcp: number; // First Contentful Paint
    lcp: number; // Largest Contentful Paint
    fid: number; // First Input Delay
    cls: number; // Cumulative Layout Shift
    ttfb: number; // Time to First Byte
    inp: number; // Interaction to Next Paint
  };

  // 渲染性能
  rendering: {
    renderTime: number;
    mountTime: number;
    updateTime: number;
    componentCount: number;
    rerenderCount: number;
    wastedRenders: number;
  };

  // 内存使用
  memory: {
    used: number; // MB
    total: number; // MB
    percentage: number;
    heapSize: number;
    heapLimit: number;
  };

  // 网络性能
  network: {
    requestCount: number;
    totalTime: number;
    averageTime: number;
    failedCount: number;
    cacheHitRate: number;
    totalSize: number; // KB
  };

  // 用户交互性能
  interactions: {
    clickResponseTime: number;
    scrollPerformance: number;
    inputLatency: number;
    touchResponseTime: number;
  };

  // 资源加载性能
  resources: {
    jsSize: number;
    cssSize: number;
    imageSize: number;
    fontSize: number;
    loadTime: number;
    compressionRatio: number;
  };

  // 自定义指标
  custom: Record<string, number>;
}

export interface PerformanceBenchmark {
  id: string;
  name: string;
  timestamp: number;
  metrics: UnifiedPerformanceMetrics;
  environment: {
    userAgent: string;
    viewport: { width: number; height: number };
    connection: string;
    deviceMemory?: number;
    hardwareConcurrency?: number;
  };
  score: number;
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
}

export interface PerformanceIssue {
  type: 'critical' | 'warning' | 'info';
  category: 'rendering' | 'memory' | 'network' | 'interaction' | 'resource';
  message: string;
  metric: string;
  value: number;
  threshold: number;
  recommendation: string;
  impact: 'high' | 'medium' | 'low';
}

export interface PerformanceReport {
  id: string;
  timestamp: number;
  duration: number; // 监控时长(ms)
  metrics: UnifiedPerformanceMetrics;
  issues: PerformanceIssue[];
  recommendations: string[];
  score: number;
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
  trends: {
    renderTime: 'improving' | 'stable' | 'degrading';
    memoryUsage: 'improving' | 'stable' | 'degrading';
    networkPerformance: 'improving' | 'stable' | 'degrading';
  };
}

// ==================== 性能监控配置 ====================

export interface PerformanceMonitorConfig {
  enabled: boolean;
  sampleRate: number; // 0-1
  enableWebVitals: boolean;
  enableMemoryTracking: boolean;
  enableNetworkMonitoring: boolean;
  enableInteractionTracking: boolean;
  enableResourceMonitoring: boolean;

  thresholds: {
    renderTime: number; // ms
    memoryUsage: number; // MB
    networkLatency: number; // ms
    interactionDelay: number; // ms
    resourceLoadTime: number; // ms
  };

  reporting: {
    interval: number; // ms
    autoGenerate: boolean;
    includeRecommendations: boolean;
  };
}

// ==================== 统一性能监控器 ====================

export class UnifiedPerformanceMonitor {
  private static instance: UnifiedPerformanceMonitor;
  private config: PerformanceMonitorConfig;
  private metrics: Partial<UnifiedPerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];
  private measurements: Map<string, number> = new Map();
  private benchmarks: PerformanceBenchmark[] = [];
  private reports: PerformanceReport[] = [];
  private startTime: number = 0;
  private isMonitoring: boolean = false;

  private constructor(config?: Partial<PerformanceMonitorConfig>) {
    this.config = {
      enabled: true,
      sampleRate: 1.0,
      enableWebVitals: true,
      enableMemoryTracking: true,
      enableNetworkMonitoring: true,
      enableInteractionTracking: true,
      enableResourceMonitoring: true,
      thresholds: {
        renderTime: 16, // 60fps
        memoryUsage: 100, // 100MB
        networkLatency: 200, // 200ms
        interactionDelay: 100, // 100ms
        resourceLoadTime: 1000, // 1s
      },
      reporting: {
        interval: 30000, // 30s
        autoGenerate: true,
        includeRecommendations: true,
      },
      ...config,
    };

    this.initializeMonitoring();
  }

  static getInstance(config?: Partial<PerformanceMonitorConfig>): UnifiedPerformanceMonitor {
    if (!UnifiedPerformanceMonitor.instance) {
      UnifiedPerformanceMonitor.instance = new UnifiedPerformanceMonitor(config);
    }
    return UnifiedPerformanceMonitor.instance;
  }

  // ==================== 初始化和配置 ====================

  private initializeMonitoring(): void {
    if (typeof window === 'undefined' || !this.config.enabled) return;

    this.startTime = performance.now();
    this.isMonitoring = true;

    // 初始化各种监控
    if (this.config.enableWebVitals) this.initializeWebVitals();
    if (this.config.enableMemoryTracking) this.initializeMemoryTracking();
    if (this.config.enableNetworkMonitoring) this.initializeNetworkMonitoring();
    if (this.config.enableInteractionTracking) this.initializeInteractionTracking();
    if (this.config.enableResourceMonitoring) this.initializeResourceMonitoring();

    // 启动自动报告
    if (this.config.reporting.autoGenerate) {
      this.startAutoReporting();
    }
  }

  private initializeWebVitals(): void {
    // Web Vitals 监控
    if ('PerformanceObserver' in window) {
      const vitalsObserver = new PerformanceObserver(list => {
        list.getEntries().forEach((entry: any) => {
          if (!this.metrics.webVitals) {
            this.metrics.webVitals = {
              fcp: 0,
              lcp: 0,
              fid: 0,
              cls: 0,
              ttfb: 0,
              inp: 0,
            };
          }

          switch (entry.entryType) {
            case 'paint':
              if (entry.name === 'first-contentful-paint') {
                this.metrics.webVitals.fcp = entry.startTime;
              }
              break;
            case 'largest-contentful-paint':
              this.metrics.webVitals.lcp = entry.startTime;
              break;
            case 'first-input':
              this.metrics.webVitals.fid = entry.processingStart - entry.startTime;
              break;
            case 'layout-shift':
              if (!entry.hadRecentInput) {
                this.metrics.webVitals.cls += entry.value;
              }
              break;
          }
        });
      });

      try {
        vitalsObserver.observe({
          entryTypes: ['paint', 'largest-contentful-paint', 'first-input', 'layout-shift'],
        });
        this.observers.push(vitalsObserver);
      } catch (error) {
        console.warn('Web Vitals monitoring not supported:', error);
      }
    }
  }

  private initializeMemoryTracking(): void {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        this.metrics.memory = {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
          percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100),
          heapSize: Math.round(memory.usedJSHeapSize / 1024 / 1024),
          heapLimit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024),
        };
      }, 1000);
    }
  }

  private initializeNetworkMonitoring(): void {
    if ('PerformanceObserver' in window) {
      const networkObserver = new PerformanceObserver(list => {
        const entries = list.getEntries();
        let totalTime = 0;
        let totalSize = 0;
        let failedCount = 0;

        entries.forEach((entry: any) => {
          totalTime += entry.duration;
          totalSize += entry.transferSize || 0;
          if (entry.responseStatus >= 400) failedCount++;
        });

        if (!this.metrics.network) {
          this.metrics.network = {
            requestCount: 0,
            totalTime: 0,
            averageTime: 0,
            failedCount: 0,
            cacheHitRate: 0,
            totalSize: 0,
          };
        }

        this.metrics.network.requestCount += entries.length;
        this.metrics.network.totalTime += totalTime;
        this.metrics.network.averageTime =
          this.metrics.network.totalTime / this.metrics.network.requestCount;
        this.metrics.network.failedCount += failedCount;
        this.metrics.network.totalSize += Math.round(totalSize / 1024);
      });

      try {
        networkObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(networkObserver);
      } catch (error) {
        console.warn('Network monitoring not supported:', error);
      }
    }
  }

  private initializeInteractionTracking(): void {
    // 点击响应时间监控
    let clickStartTime = 0;

    document.addEventListener('mousedown', () => {
      clickStartTime = performance.now();
    });

    document.addEventListener('click', () => {
      if (clickStartTime > 0) {
        const responseTime = performance.now() - clickStartTime;
        if (!this.metrics.interactions) {
          this.metrics.interactions = {
            clickResponseTime: 0,
            scrollPerformance: 0,
            inputLatency: 0,
            touchResponseTime: 0,
          };
        }
        this.metrics.interactions.clickResponseTime = responseTime;
        clickStartTime = 0;
      }
    });

    // 滚动性能监控
    let scrollStartTime = 0;
    document.addEventListener('scroll', () => {
      if (scrollStartTime === 0) {
        scrollStartTime = performance.now();
        requestAnimationFrame(() => {
          const scrollTime = performance.now() - scrollStartTime;
          if (this.metrics.interactions) {
            this.metrics.interactions.scrollPerformance = scrollTime;
          }
          scrollStartTime = 0;
        });
      }
    });
  }

  private initializeResourceMonitoring(): void {
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver(list => {
        const entries = list.getEntries();
        let jsSize = 0,
          cssSize = 0,
          imageSize = 0,
          fontSize = 0;
        let totalLoadTime = 0;

        entries.forEach((entry: any) => {
          const size = entry.transferSize || 0;
          totalLoadTime += entry.duration;

          if (entry.name.includes('.js')) jsSize += size;
          else if (entry.name.includes('.css')) cssSize += size;
          else if (entry.name.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) imageSize += size;
          else if (entry.name.match(/\.(woff|woff2|ttf|otf)$/)) fontSize += size;
        });

        this.metrics.resources = {
          jsSize: Math.round(jsSize / 1024),
          cssSize: Math.round(cssSize / 1024),
          imageSize: Math.round(imageSize / 1024),
          fontSize: Math.round(fontSize / 1024),
          loadTime: totalLoadTime,
          compressionRatio: 0, // 需要额外计算
        };
      });

      try {
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);
      } catch (error) {
        console.warn('Resource monitoring not supported:', error);
      }
    }
  }

  private startAutoReporting(): void {
    setInterval(() => {
      if (this.isMonitoring) {
        this.generateReport();
      }
    }, this.config.reporting.interval);
  }

  // ==================== 公共API ====================

  /**
   * 开始性能测量
   */
  startMeasurement(name: string): void {
    this.measurements.set(name, performance.now());
    performance.mark(`${name}-start`);
  }

  /**
   * 结束性能测量
   */
  endMeasurement(name: string): number {
    const startTime = this.measurements.get(name);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);

    this.measurements.delete(name);
    return duration;
  }

  /**
   * 记录组件渲染性能
   */
  recordComponentRender(componentName: string, renderTime: number, isMount: boolean = false): void {
    if (!this.metrics.rendering) {
      this.metrics.rendering = {
        renderTime: 0,
        mountTime: 0,
        updateTime: 0,
        componentCount: 0,
        rerenderCount: 0,
        wastedRenders: 0,
      };
    }

    if (isMount) {
      this.metrics.rendering.mountTime = renderTime;
      this.metrics.rendering.componentCount++;
    } else {
      this.metrics.rendering.updateTime = renderTime;
      this.metrics.rendering.rerenderCount++;
    }

    this.metrics.rendering.renderTime = renderTime;

    // 检查性能问题
    if (renderTime > this.config.thresholds.renderTime) {
      console.warn(`Slow render detected: ${componentName} took ${renderTime.toFixed(2)}ms`);
    }
  }

  /**
   * 记录自定义指标
   */
  recordCustomMetric(name: string, value: number): void {
    if (!this.metrics.custom) {
      this.metrics.custom = {};
    }
    this.metrics.custom[name] = value;
  }

  /**
   * 获取当前性能指标
   */
  getMetrics(): Partial<UnifiedPerformanceMetrics> {
    return { ...this.metrics };
  }

  /**
   * 计算性能评分
   */
  calculatePerformanceScore(): number {
    const metrics = this.metrics;
    let score = 100;

    // Web Vitals 评分 (40%)
    if (metrics.webVitals) {
      if (metrics.webVitals.lcp > 2500) score -= 15;
      else if (metrics.webVitals.lcp > 1200) score -= 8;

      if (metrics.webVitals.fid > 100) score -= 10;
      else if (metrics.webVitals.fid > 50) score -= 5;

      if (metrics.webVitals.cls > 0.25) score -= 15;
      else if (metrics.webVitals.cls > 0.1) score -= 8;
    }

    // 渲染性能评分 (30%)
    if (metrics.rendering) {
      if (metrics.rendering.renderTime > 50) score -= 15;
      else if (metrics.rendering.renderTime > 16) score -= 8;
    }

    // 内存使用评分 (20%)
    if (metrics.memory) {
      if (metrics.memory.percentage > 80) score -= 10;
      else if (metrics.memory.percentage > 60) score -= 5;
    }

    // 网络性能评分 (10%)
    if (metrics.network) {
      if (metrics.network.averageTime > 1000) score -= 5;
      else if (metrics.network.averageTime > 500) score -= 3;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 获取性能等级
   */
  getPerformanceGrade(score: number): 'A' | 'B' | 'C' | 'D' | 'F' {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }

  /**
   * 检测性能问题
   */
  detectIssues(): PerformanceIssue[] {
    const issues: PerformanceIssue[] = [];
    const metrics = this.metrics;

    // 检查渲染性能
    if (
      metrics.rendering?.renderTime &&
      metrics.rendering.renderTime > this.config.thresholds.renderTime
    ) {
      issues.push({
        type: 'warning',
        category: 'rendering',
        message: '组件渲染时间过长',
        metric: 'renderTime',
        value: metrics.rendering.renderTime,
        threshold: this.config.thresholds.renderTime,
        recommendation: '考虑使用 React.memo 或优化渲染逻辑',
        impact: 'high',
      });
    }

    // 检查内存使用
    if (metrics.memory?.used && metrics.memory.used > this.config.thresholds.memoryUsage) {
      issues.push({
        type: 'critical',
        category: 'memory',
        message: '内存使用过高',
        metric: 'memoryUsage',
        value: metrics.memory.used,
        threshold: this.config.thresholds.memoryUsage,
        recommendation: '检查内存泄漏，优化数据结构',
        impact: 'high',
      });
    }

    // 检查网络性能
    if (
      metrics.network?.averageTime &&
      metrics.network.averageTime > this.config.thresholds.networkLatency
    ) {
      issues.push({
        type: 'warning',
        category: 'network',
        message: '网络请求延迟过高',
        metric: 'networkLatency',
        value: metrics.network.averageTime,
        threshold: this.config.thresholds.networkLatency,
        recommendation: '优化API响应时间，考虑使用缓存',
        impact: 'medium',
      });
    }

    return issues;
  }

  /**
   * 生成性能报告
   */
  generateReport(): PerformanceReport {
    const metrics = this.getMetrics() as UnifiedPerformanceMetrics;
    const score = this.calculatePerformanceScore();
    const grade = this.getPerformanceGrade(score);
    const issues = this.detectIssues();
    const duration = performance.now() - this.startTime;

    const report: PerformanceReport = {
      id: `report-${Date.now()}`,
      timestamp: Date.now(),
      duration,
      metrics,
      issues,
      recommendations: this.generateRecommendations(issues),
      score,
      grade,
      trends: this.analyzeTrends(),
    };

    this.reports.push(report);

    // 保持最近50个报告
    if (this.reports.length > 50) {
      this.reports = this.reports.slice(-50);
    }

    return report;
  }

  private generateRecommendations(issues: PerformanceIssue[]): string[] {
    const recommendations: string[] = [];

    issues.forEach(issue => {
      recommendations.push(issue.recommendation);
    });

    // 通用建议
    if (this.metrics.rendering?.rerenderCount && this.metrics.rendering.rerenderCount > 10) {
      recommendations.push('考虑使用 useMemo 和 useCallback 减少不必要的重渲染');
    }

    if (this.metrics.resources?.jsSize && this.metrics.resources.jsSize > 1000) {
      recommendations.push('考虑代码分割和懒加载减少 JavaScript 包大小');
    }

    return [...new Set(recommendations)]; // 去重
  }

  private analyzeTrends(): PerformanceReport['trends'] {
    // 简化的趋势分析，实际应该基于历史数据
    return {
      renderTime: 'stable',
      memoryUsage: 'stable',
      networkPerformance: 'stable',
    };
  }

  /**
   * 获取所有报告
   */
  getReports(): PerformanceReport[] {
    return [...this.reports];
  }

  /**
   * 清理监控数据
   */
  cleanup(): void {
    this.isMonitoring = false;
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.measurements.clear();
    this.metrics = {};
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<PerformanceMonitorConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// ==================== React Hook ====================

export interface UseUnifiedPerformanceMonitorOptions {
  componentName: string;
  enableAutoTracking?: boolean;
  sampleRate?: number;
  thresholds?: Partial<PerformanceMonitorConfig['thresholds']>;
}

export function useUnifiedPerformanceMonitor(options: UseUnifiedPerformanceMonitorOptions) {
  const { componentName, enableAutoTracking = true, sampleRate = 1.0, thresholds } = options;
  const monitor = UnifiedPerformanceMonitor.getInstance();
  const renderStartTime = React.useRef<number>(0);
  const renderCount = React.useRef<number>(0);

  // 更新配置
  React.useEffect(() => {
    if (thresholds) {
      monitor.updateConfig({ thresholds: { ...monitor['config'].thresholds, ...thresholds } });
    }
  }, [thresholds, monitor]);

  // 自动跟踪渲染性能
  React.useEffect(() => {
    if (!enableAutoTracking || Math.random() > sampleRate) return;

    renderCount.current++;
    const isMount = renderCount.current === 1;

    if (renderStartTime.current > 0) {
      const renderTime = performance.now() - renderStartTime.current;
      monitor.recordComponentRender(componentName, renderTime, isMount);
      renderStartTime.current = 0;
    }
  });

  // 开始渲染测量
  React.useLayoutEffect(() => {
    if (enableAutoTracking && Math.random() <= sampleRate) {
      renderStartTime.current = performance.now();
    }
  });

  // 组件卸载时清理
  React.useEffect(() => {
    return () => {
      if (renderStartTime.current > 0) {
        const renderTime = performance.now() - renderStartTime.current;
        monitor.recordComponentRender(componentName, renderTime, false);
      }
    };
  }, [componentName, monitor]);

  return {
    monitor,
    startMeasurement: (name: string) => monitor.startMeasurement(`${componentName}-${name}`),
    endMeasurement: (name: string) => monitor.endMeasurement(`${componentName}-${name}`),
    recordCustomMetric: (name: string, value: number) =>
      monitor.recordCustomMetric(`${componentName}-${name}`, value),
    getMetrics: () => monitor.getMetrics(),
    generateReport: () => monitor.generateReport(),
    detectIssues: () => monitor.detectIssues(),
  };
}

// 导出全局实例
export const unifiedPerformanceMonitor = UnifiedPerformanceMonitor.getInstance();
