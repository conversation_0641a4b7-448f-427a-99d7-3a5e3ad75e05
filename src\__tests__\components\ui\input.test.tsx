/**
 * Input组件单元测试
 */

import { Input } from '@/shared/components/input';
import { fireEvent, screen } from '@testing-library/react';
import React from 'react';
import { render } from '../../utils/test-utils.helper';

describe('Input组件', () => {
  describe('基础渲染', () => {
    it('应该正确渲染默认输入框', () => {
      render(<Input placeholder='请输入内容' />);

      const input = screen.getByPlaceholderText('请输入内容');
      expect(input).toBeInTheDocument();
      expect(input).toHaveClass('flex', 'h-10', 'w-full', 'rounded-md', 'border');
    });

    it('应该支持不同的input类型', () => {
      const { rerender } = render(<Input type='text' data-testid='input' />);
      expect(screen.getByTestId('input')).toHaveAttribute('type', 'text');

      rerender(<Input type='password' data-testid='input' />);
      expect(screen.getByTestId('input')).toHaveAttribute('type', 'password');

      rerender(<Input type='email' data-testid='input' />);
      expect(screen.getByTestId('input')).toHaveAttribute('type', 'email');

      rerender(<Input type='number' data-testid='input' />);
      expect(screen.getByTestId('input')).toHaveAttribute('type', 'number');

      rerender(<Input type='tel' data-testid='input' />);
      expect(screen.getByTestId('input')).toHaveAttribute('type', 'tel');

      rerender(<Input type='url' data-testid='input' />);
      expect(screen.getByTestId('input')).toHaveAttribute('type', 'url');
    });

    it('应该支持自定义className', () => {
      render(<Input className='custom-input' data-testid='input' />);

      const input = screen.getByTestId('input');
      expect(input).toHaveClass('custom-input');
      // 应该保留默认样式
      expect(input).toHaveClass('flex', 'h-10', 'w-full');
    });

    it('应该支持placeholder', () => {
      render(<Input placeholder='输入用户名' />);

      const input = screen.getByPlaceholderText('输入用户名');
      expect(input).toBeInTheDocument();
    });

    it('应该支持disabled状态', () => {
      render(<Input disabled data-testid='input' />);

      const input = screen.getByTestId('input');
      expect(input).toBeDisabled();
      expect(input).toHaveClass('disabled:cursor-not-allowed', 'disabled:opacity-50');
    });

    it('应该支持readonly状态', () => {
      render(<Input readOnly value='只读内容' data-testid='input' />);

      const input = screen.getByTestId('input');
      expect(input).toHaveAttribute('readonly');
      expect(input).toHaveValue('只读内容');
    });
  });

  describe('值管理', () => {
    it('应该支持defaultValue', () => {
      render(<Input defaultValue='默认值' data-testid='input' />);

      const input = screen.getByTestId('input') as HTMLInputElement;
      expect(input.value).toBe('默认值');
    });

    it('应该支持受控组件模式', () => {
      const TestComponent = () => {
        const [value, setValue] = React.useState('初始值');
        return <Input value={value} onChange={e => setValue(e.target.value)} data-testid='input' />;
      };

      render(<TestComponent />);

      const input = screen.getByTestId('input') as HTMLInputElement;
      expect(input.value).toBe('初始值');

      fireEvent.change(input, { target: { value: '新值' } });
      expect(input.value).toBe('新值');
    });

    it('应该支持非受控组件模式', () => {
      render(<Input defaultValue='默认值' data-testid='input' />);

      const input = screen.getByTestId('input') as HTMLInputElement;
      expect(input.value).toBe('默认值');

      fireEvent.change(input, { target: { value: '用户输入' } });
      expect(input.value).toBe('用户输入');
    });
  });

  describe('事件处理', () => {
    it('应该响应onChange事件', () => {
      const handleChange = jest.fn();
      render(<Input onChange={handleChange} data-testid='input' />);

      const input = screen.getByTestId('input');
      fireEvent.change(input, { target: { value: '测试输入' } });

      expect(handleChange).toHaveBeenCalledTimes(1);
      expect(handleChange).toHaveBeenCalledWith(
        expect.objectContaining({
          target: expect.objectContaining({
            value: '测试输入',
          }),
        })
      );
    });

    it('应该响应onFocus和onBlur事件', () => {
      const handleFocus = jest.fn();
      const handleBlur = jest.fn();
      render(<Input onFocus={handleFocus} onBlur={handleBlur} data-testid='input' />);

      const input = screen.getByTestId('input');

      fireEvent.focus(input);
      expect(handleFocus).toHaveBeenCalledTimes(1);

      fireEvent.blur(input);
      expect(handleBlur).toHaveBeenCalledTimes(1);
    });

    it('应该响应键盘事件', () => {
      const handleKeyDown = jest.fn();
      const handleKeyUp = jest.fn();
      render(<Input onKeyDown={handleKeyDown} onKeyUp={handleKeyUp} data-testid='input' />);

      const input = screen.getByTestId('input');

      fireEvent.keyDown(input, { key: 'Enter' });
      expect(handleKeyDown).toHaveBeenCalledTimes(1);

      fireEvent.keyUp(input, { key: 'Enter' });
      expect(handleKeyUp).toHaveBeenCalledTimes(1);
    });

    it('禁用状态下应该有正确的样式和属性', () => {
      const handleChange = jest.fn();
      render(<Input disabled onChange={handleChange} data-testid='input' />);

      const input = screen.getByTestId('input');

      // 检查禁用属性
      expect(input).toBeDisabled();

      // 检查禁用样式类
      expect(input).toHaveClass('disabled:cursor-not-allowed', 'disabled:opacity-50');
    });
  });

  describe('可访问性测试', () => {
    it('应该支持aria-label', () => {
      render(<Input aria-label='用户名输入框' />);

      const input = screen.getByLabelText('用户名输入框');
      expect(input).toBeInTheDocument();
    });

    it('应该支持aria-describedby', () => {
      render(
        <>
          <Input aria-describedby='help-text' data-testid='input' />
          <div id='help-text'>请输入6-20位字符</div>
        </>
      );

      const input = screen.getByTestId('input');
      expect(input).toHaveAttribute('aria-describedby', 'help-text');
    });

    it('应该支持aria-invalid', () => {
      render(<Input aria-invalid='true' data-testid='input' />);

      const input = screen.getByTestId('input');
      expect(input).toHaveAttribute('aria-invalid', 'true');
    });

    it('应该支持required属性', () => {
      render(<Input required data-testid='input' />);

      const input = screen.getByTestId('input');
      expect(input).toBeRequired();
    });
  });

  describe('表单集成测试', () => {
    it('应该支持name属性', () => {
      render(<Input name='username' data-testid='input' />);

      const input = screen.getByTestId('input');
      expect(input).toHaveAttribute('name', 'username');
    });

    it('应该支持id属性', () => {
      render(<Input id='user-input' data-testid='input' />);

      const input = screen.getByTestId('input');
      expect(input).toHaveAttribute('id', 'user-input');
    });

    it('应该支持autoComplete', () => {
      render(<Input autoComplete='username' data-testid='input' />);

      const input = screen.getByTestId('input');
      expect(input).toHaveAttribute('autocomplete', 'username');
    });

    it('应该支持maxLength', () => {
      render(<Input maxLength={10} data-testid='input' />);

      const input = screen.getByTestId('input');
      expect(input).toHaveAttribute('maxlength', '10');
    });

    it('应该支持minLength', () => {
      render(<Input minLength={3} data-testid='input' />);

      const input = screen.getByTestId('input');
      expect(input).toHaveAttribute('minlength', '3');
    });
  });

  describe('边界条件测试', () => {
    it('应该处理空值', () => {
      render(<Input value='' data-testid='input' />);

      const input = screen.getByTestId('input') as HTMLInputElement;
      expect(input.value).toBe('');
    });

    it('应该处理特殊字符', () => {
      const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      render(<Input defaultValue={specialChars} data-testid='input' />);

      const input = screen.getByTestId('input') as HTMLInputElement;
      expect(input.value).toBe(specialChars);
    });

    it('应该处理中文输入', () => {
      render(<Input defaultValue='中文测试' data-testid='input' />);

      const input = screen.getByTestId('input') as HTMLInputElement;
      expect(input.value).toBe('中文测试');
    });

    it('应该处理数字输入', () => {
      render(<Input type='number' defaultValue='123.45' data-testid='input' />);

      const input = screen.getByTestId('input') as HTMLInputElement;
      expect(input.value).toBe('123.45');
    });
  });
});
