'use client';

import React, { useEffect, useState } from 'react';

import { PlusCircle, Trash2 } from 'lucide-react';

import { Button } from '@/shared/components/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import { mockSpecifications } from '@/infrastructure/api/mock/mock-data';
import type { Specification } from '@/core/types';

interface SpecificationManagementModalProps {
  isOpen: boolean;
  onOpenChangeAction: (open: boolean) => void;
  materialName?: string;
}

export const SpecificationManagementModal: React.FC<SpecificationManagementModalProps> = ({
  isOpen,
  onOpenChangeAction,
  materialName,
}) => {
  const [specs, setSpecs] = useState<Specification[]>([]);

  useEffect(() => {
    if (isOpen) {
      setSpecs(mockSpecifications);
    }
  }, [isOpen]);

  const handleAdd = () => {
    setSpecs([...specs, { id: `new_spec_${Date.now()}`, name: '' }]);
  };

  const handleDelete = (id: string) => {
    setSpecs(specs.filter(s => s.id !== id));
  };

  const handleUpdate = (id: string, name: string) => {
    setSpecs(specs.map(s => (s.id === id ? { ...s, name } : s)));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChangeAction}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>规格维护 for {materialName || 'Material'}</DialogTitle>
        </DialogHeader>
        <div className='space-y-2 py-4 max-h-[50vh] overflow-y-auto'>
          {specs.map(spec => (
            <div key={spec.id} className='flex items-center gap-2'>
              <Input
                value={spec.name}
                onChange={e => handleUpdate(spec.id, e.target.value)}
                className='h-8'
                placeholder='请输入规格名称'
              />
              <Button variant='ghost' size='icon' onClick={() => handleDelete(spec.id)}>
                <Trash2 className='h-4 w-4 text-destructive' />
              </Button>
            </div>
          ))}
        </div>
        <DialogFooter className='justify-between'>
          <Button variant='outline' size='sm' onClick={handleAdd} className='gap-1'>
            <PlusCircle className='h-4 w-4' />
            增加规格
          </Button>
          <div className='flex gap-2'>
            <Button variant='outline' onClick={() => onOpenChangeAction(false)}>
              关闭
            </Button>
            <Button>保存</Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
