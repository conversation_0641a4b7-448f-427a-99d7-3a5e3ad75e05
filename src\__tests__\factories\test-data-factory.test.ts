/**
 * 测试数据工厂测试
 * 验证工厂函数能够正确创建测试数据
 */

import {
  createBoundaryTestData,
  createMockDeliveryOrder,
  createMockPlants,
  createMockTaskListSettings,
  createMockTasks,
  createMockVehicles,
  createPerformanceTestData,
  createTestScenario,
} from './test-data-factory.factory';

describe('测试数据工厂', () => {
  describe('createMockTasks', () => {
    it('应该创建指定数量的任务', () => {
      const tasks = createMockTasks(3);
      expect(tasks).toHaveLength(3);
      expect(tasks[0]).toHaveProperty('id');
      expect(tasks[0]).toHaveProperty('taskNumber');
      expect(tasks[0]).toHaveProperty('projectName');
      expect(tasks[0]).toHaveProperty('strength');
      expect(tasks[0]).toHaveProperty('dispatchStatus');
      expect(tasks[0]).toHaveProperty('status');
    });

    it('应该支持覆盖属性', () => {
      const tasks = createMockTasks(1, { projectName: '自定义项目' });
      expect(tasks).toHaveLength(1);
      expect(tasks[0]!.projectName).toBe('自定义项目');
    });
  });

  describe('createMockVehicles', () => {
    it('应该创建指定数量的车辆', () => {
      const vehicles = createMockVehicles(2);
      expect(vehicles).toHaveLength(2);
      expect(vehicles[0]).toHaveProperty('id');
      expect(vehicles[0]).toHaveProperty('vehicleNumber');
      expect(vehicles[0]).toHaveProperty('status');
      expect(vehicles[0]).toHaveProperty('type');
    });

    it('应该支持覆盖属性', () => {
      const vehicles = createMockVehicles(1, { vehicleNumber: '京A88888' });
      expect(vehicles).toHaveLength(1);
      expect(vehicles[0]!.vehicleNumber).toBe('京A88888');
    });
  });

  describe('createMockPlants', () => {
    it('应该创建指定数量的搅拌站', () => {
      const plants = createMockPlants(2);
      expect(plants).toHaveLength(2);
      expect(plants[0]).toHaveProperty('id');
      expect(plants[0]).toHaveProperty('name');
      expect(plants[0]).toHaveProperty('stats');
      expect(plants[0]).toHaveProperty('productionLineCount');
    });

    it('应该支持覆盖属性', () => {
      const plants = createMockPlants(1, { name: '自定义搅拌站' });
      expect(plants).toHaveLength(1);
      expect(plants[0]!.name).toBe('自定义搅拌站');
    });
  });

  describe('createMockDeliveryOrder', () => {
    it('应该创建配送单', () => {
      const order = createMockDeliveryOrder();
      expect(order).toHaveProperty('id');
      expect(order).toHaveProperty('plantId');
      expect(order).toHaveProperty('productionLineId');
      expect(order).toHaveProperty('vehicleNumber');
      expect(order).toHaveProperty('driver');
      expect(order).toHaveProperty('volume');
      expect(order).toHaveProperty('strength');
      expect(order).toHaveProperty('projectName');
      expect(order).toHaveProperty('taskNumber');
    });

    it('应该支持覆盖属性', () => {
      const order = createMockDeliveryOrder({ projectName: '自定义项目' });
      expect(order.projectName).toBe('自定义项目');
    });
  });

  describe('createMockTaskListSettings', () => {
    it('应该创建任务列表设置', () => {
      const settings = createMockTaskListSettings();
      expect(settings).toHaveProperty('displayMode');
      expect(settings).toHaveProperty('density');
      expect(settings).toHaveProperty('columnVisibility');
      expect(settings).toHaveProperty('columnWidths');
      expect(settings).toHaveProperty('groupConfig');
    });

    it('应该支持覆盖属性', () => {
      const settings = createMockTaskListSettings({ displayMode: 'card' });
      expect(settings.displayMode).toBe('card');
    });
  });

  describe('createTestScenario', () => {
    it('应该创建完整的测试场景', () => {
      const scenario = createTestScenario();
      expect(scenario).toHaveProperty('plants');
      expect(scenario).toHaveProperty('vehicles');
      expect(scenario).toHaveProperty('tasks');
      expect(scenario).toHaveProperty('deliveryOrders');

      expect(scenario.plants).toHaveLength(3);
      expect(scenario.vehicles).toHaveLength(8);
      expect(scenario.tasks).toHaveLength(10);
      expect(scenario.deliveryOrders).toHaveLength(10);
    });
  });

  describe('createPerformanceTestData', () => {
    it('应该创建性能测试数据', () => {
      const data = createPerformanceTestData(2);
      expect(data).toHaveProperty('tasks');
      expect(data).toHaveProperty('vehicles');
      expect(data).toHaveProperty('plants');

      expect(data.tasks).toHaveLength(200);
      expect(data.vehicles).toHaveLength(100);
      expect(data.plants).toHaveLength(20);
    });
  });

  describe('createBoundaryTestData', () => {
    it('应该创建边界测试数据', () => {
      const data = createBoundaryTestData();
      expect(data).toHaveProperty('emptyTasks');
      expect(data).toHaveProperty('singleTask');
      expect(data).toHaveProperty('tasksWithNullValues');
      expect(data).toHaveProperty('tasksWithExtremeValues');

      expect(data.emptyTasks).toHaveLength(0);
      expect(data.singleTask).toHaveLength(1);
      expect(data.tasksWithNullValues).toHaveLength(3);
      expect(data.tasksWithExtremeValues).toHaveLength(4);
    });
  });
});
