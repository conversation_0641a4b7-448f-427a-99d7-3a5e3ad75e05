# TMH任务调度系统 - 手动修复指南

## 🎯 当前问题

1. **SSH密码认证** - 部署时需要重复输入密码
2. **PM2启动失败** - Windows上执行Unix shell脚本导致语法错误

## 🔧 立即修复步骤

### 步骤1: 登录服务器
```bash
ssh administrator@192.168.0.200
# 密码: 13834567299Goodbye!
```

### 步骤2: 停止当前服务
```bash
cd /d "C:\inetpub\tmh-task-dispatcher"
pm2 stop tmh-task-dispatcher
pm2 delete tmh-task-dispatcher
```

### 步骤3: 创建修复后的启动脚本
```bash
# 创建新的start.js文件
cat > start.js << 'EOF'
#!/usr/bin/env node
// TMH任务调度系统启动脚本 - Windows修复版本
process.env.NODE_ENV = 'production';
process.env.PORT = '9001';

const { spawn } = require('child_process');
const path = require('path');
const os = require('os');
const fs = require('fs');

console.log('🚀 启动TMH任务调度系统...');
console.log('环境:', process.env.NODE_ENV);
console.log('端口:', process.env.PORT);
console.log('平台:', os.platform());

// Windows平台的Next.js启动方式
let nextCommand, nextArgs;

if (os.platform() === 'win32') {
  // 方法1: 直接使用next的JavaScript入口
  const nextCliPath = path.join(__dirname, 'node_modules', 'next', 'dist', 'bin', 'next');
  if (fs.existsSync(nextCliPath)) {
    console.log('使用Next.js CLI路径:', nextCliPath);
    nextCommand = 'node';
    nextArgs = [nextCliPath, 'start'];
  } else {
    // 方法2: 使用npm start
    console.log('使用npm start启动');
    nextCommand = 'npm.cmd';
    nextArgs = ['start'];
  }
} else {
  nextCommand = 'node';
  nextArgs = [path.join(__dirname, 'node_modules', '.bin', 'next'), 'start'];
}

console.log('启动命令:', nextCommand, nextArgs.join(' '));

const child = spawn(nextCommand, nextArgs, {
  stdio: 'inherit',
  env: process.env,
  cwd: __dirname,
  shell: os.platform() === 'win32'
});

child.on('error', (error) => {
  console.error('❌ 启动失败:', error);
  console.error('尝试使用npm start...');
  
  const fallbackChild = spawn('npm', ['start'], {
    stdio: 'inherit',
    env: process.env,
    cwd: __dirname,
    shell: true
  });
  
  fallbackChild.on('error', (fallbackError) => {
    console.error('❌ 所有启动方式都失败:', fallbackError);
    process.exit(1);
  });
});

child.on('exit', (code) => {
  console.log('应用退出，代码:', code);
  process.exit(code);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭...');
  child.kill('SIGTERM');
});
EOF
```

### 步骤4: 创建修复后的PM2配置
```bash
# 创建新的ecosystem.config.js文件
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'tmh-task-dispatcher',
    script: './start.js',
    cwd: '.',
    env: {
      NODE_ENV: 'production',
      PORT: 9001
    },
    node_args: '--max-old-space-size=2048',
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '2G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF
```

### 步骤5: 创建日志目录并启动服务
```bash
# 创建日志目录
mkdir logs

# 启动PM2服务
pm2 start ecosystem.config.js
pm2 save
```

### 步骤6: 验证服务状态
```bash
# 检查PM2状态
pm2 status

# 查看日志
pm2 logs tmh-task-dispatcher

# 测试HTTP访问
curl http://localhost:9001
```

## 🔐 SSH密码认证解决方案

### 方案1: 安装PuTTY工具（推荐）
在开发机器上安装PuTTY：
```bash
# 使用Chocolatey安装
choco install putty

# 或手动下载安装
# https://www.putty.org/
```

安装后，部署脚本将自动使用plink和pscp进行密码认证。

### 方案2: 配置SSH密钥认证
```bash
# 在开发机器上运行
npm run setup:ssh
```

### 方案3: 使用WSL
```bash
# 在WSL中安装sshpass
wsl sudo apt-get update
wsl sudo apt-get install sshpass
```

## 🚀 重新部署

修复完成后，可以重新运行部署：
```bash
npm run deploy:intranet
```

## 📋 验证清单

- [ ] PM2服务正常运行
- [ ] 应用可以通过HTTP访问
- [ ] 日志文件正常生成
- [ ] SSH认证工具已安装

## 🔍 故障排除

### 如果PM2仍然启动失败
1. 检查Node.js版本：`node --version` (需要18+)
2. 检查PM2安装：`pm2 --version`
3. 手动启动测试：`node start.js`

### 如果应用无法访问
1. 检查端口是否被占用：`netstat -an | findstr 9001`
2. 检查防火墙设置
3. 查看应用日志：`pm2 logs tmh-task-dispatcher`

### 如果SSH认证仍有问题
1. 安装PuTTY工具
2. 或配置SSH密钥认证
3. 或使用WSL环境

## ✅ 完成

修复完成后，应用应该能够正常运行在：
**http://192.168.0.200:9001**
