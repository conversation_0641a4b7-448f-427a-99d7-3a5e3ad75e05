'use client';

/**
 * 懒加载提供者组件
 * 负责初始化懒加载系统
 */

import React from 'react';
import { initializeLazyLoading } from '@/core/config/lazy-loading';

interface LazyLoadingProviderProps {
  children: React.ReactNode;
}

const LazyLoadingProvider: React.FC<LazyLoadingProviderProps> = ({ children }) => {
  // 初始化懒加载系统
  React.useEffect(() => {
    initializeLazyLoading();
  }, []);

  return <>{children}</>;
};

export default LazyLoadingProvider;
