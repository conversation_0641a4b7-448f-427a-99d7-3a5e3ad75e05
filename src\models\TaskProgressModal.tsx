'use client';

/**
 * 任务进度模态框
 * 占位符组件，用于懒加载测试
 */

import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';

interface TaskProgressModalProps {
  open?: boolean;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onOpenChangeAction?: (open: boolean) => void;
  taskId?: string;
}

const TaskProgressModal: React.FC<TaskProgressModalProps> = ({
  open = false,
  isOpen = false,
  onOpenChange,
  onOpenChangeAction,
  taskId,
}) => {
  // 统一处理open状态和回调
  const modalOpen = (open || isOpen) ?? false;
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange?.(newOpen);
    onOpenChangeAction?.(newOpen);
  };

  return (
    <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
      <DialogContent className='max-w-6xl'>
        <DialogHeader>
          <DialogTitle>任务进度</DialogTitle>
        </DialogHeader>
        <div className='p-4'>
          <p>任务进度功能正在开发中...</p>
          {taskId && <p className='text-sm text-gray-500'>任务ID: {taskId}</p>}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TaskProgressModal;
