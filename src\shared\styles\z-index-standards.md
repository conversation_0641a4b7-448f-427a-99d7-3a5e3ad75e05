# Z-Index 层级规范

本文档定义了项目中所有 z-index 的使用标准，确保层级管理的一致性和可维护性。

## 层级分类

### 1. 基础层级 (1-99)
- `1-9`: 基础元素层级
- `10-19`: 表格相关基础层级
- `20-99`: 预留给其他基础组件

### 2. 内容层级 (100-699)
- `100-199`: 卡片和面板
- `200-299`: 导航和菜单
- `300-399`: 表单元素
- `400-499`: 图表和可视化组件
- `500-599`: 交互元素（按钮、链接等）
- `600-699`: 预留层级

### 3. 固定元素层级 (700-899)
- `700`: 固定列阴影效果
- `750`: 固定列表体
- `800`: 固定列表头
- `850-899`: 其他固定定位元素

### 4. 弹出层级 (900-1099)
- `900-999`: 下拉菜单和选择器
- `1000-1039`: 工具提示和气泡
- `1040-1049`: 模态框背景
- `1050-1069`: 模态框内容
- `1070-1079`: 高优先级工具提示
- `1080-1099`: 通知和消息

### 5. 系统层级 (1100+)
- `1100-1199`: 系统级弹窗
- `1200+`: 调试和开发工具

## 使用规则

### 1. 优先使用 CSS 变量
```css
:root {
  /* 基础层级 */
  --z-base: 1;
  --z-table-cell: 5;
  --z-table-header: 10;
  
  /* 固定元素层级 */
  --z-sticky-shadow: 700;
  --z-sticky-body: 750;
  --z-sticky-header: 800;
  
  /* 弹出层级 */
  --z-dropdown: 1000;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-tooltip: 1070;
  --z-toast: 1080;
}
```

### 2. TypeScript 枚举定义
```typescript
export enum ZIndexLevels {
  BASE = 1,
  TABLE_CELL = 5,
  TABLE_HEADER = 10,
  STICKY_SHADOW = 700,
  STICKY_BODY = 750,
  STICKY_HEADER = 800,
  DROPDOWN = 1000,
  MODAL_BACKDROP = 1040,
  MODAL = 1050,
  TOOLTIP = 1070,
  TOAST = 1080
}
```

### 3. 使用指南

#### ✅ 正确做法
- 使用预定义的 CSS 变量或 TypeScript 枚举
- 在同一层级内使用相对较小的差值（1-5）
- 为新组件选择合适的层级范围
- 在代码注释中说明 z-index 的用途

#### ❌ 错误做法
- 随意设置过大的 z-index 值（如 9999）
- 在不同文件中重复定义相同的 z-index
- 不考虑层级关系就设置 z-index
- 使用魔法数字而不是语义化的变量

## 层级冲突解决

### 1. 诊断步骤
1. 检查元素的 z-index 值
2. 确认父元素的 stacking context
3. 验证 position 属性设置
4. 检查是否有内联样式覆盖

### 2. 解决方案
1. 调整到正确的层级范围
2. 使用 CSS 变量统一管理
3. 避免内联样式覆盖 CSS 类
4. 考虑重构组件层级结构

## 维护建议

### 1. 定期审查
- 每月检查新增的 z-index 使用
- 清理不再使用的层级定义
- 更新文档和类型定义

### 2. 开发规范
- 新增 z-index 前先查阅此文档
- 优先使用现有的层级变量
- 在 PR 中说明 z-index 的使用理由

### 3. 工具支持
- 使用 ESLint 规则检查 z-index 使用
- 在 Storybook 中展示层级关系
- 提供层级可视化工具

## 更新记录

- 2024-12-24: 初始版本，定义基础层级规范
- 2024-12-24: 添加固定列相关层级定义
- 2024-12-24: 完善使用指南和维护建议