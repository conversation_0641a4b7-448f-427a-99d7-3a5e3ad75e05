/**
 * Dayjs 日期适配器实现
 * 将 dayjs 库适配到统一的日期接口
 */

import dayjs, { Dayjs } from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import duration from 'dayjs/plugin/duration';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import 'dayjs/locale/zh-cn';

import { DateAdapter, DateLibrary, DateUnit, UnifiedDate } from './DateAdapter';

// 初始化 dayjs 插件
dayjs.extend(relativeTime);
dayjs.extend(duration);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(customParseFormat);
dayjs.locale('zh-cn');

/**
 * Dayjs 统一日期对象实现
 */
class DayjsUnifiedDate implements UnifiedDate {
  constructor(private dayjsInstance: Dayjs) {}

  get value(): Date {
    return this.dayjsInstance.toDate();
  }

  format(pattern: string): string {
    return this.dayjsInstance.format(pattern);
  }

  add(amount: number, unit: DateUnit): UnifiedDate {
    return new DayjsUnifiedDate(this.dayjsInstance.add(amount, unit));
  }

  subtract(amount: number, unit: DateUnit): UnifiedDate {
    return new DayjsUnifiedDate(this.dayjsInstance.subtract(amount, unit));
  }

  isSame(date: UnifiedDate | Date | string, unit?: DateUnit): boolean {
    const targetDate = this.convertToDate(date);
    return this.dayjsInstance.isSame(dayjs(targetDate), unit);
  }

  isBefore(date: UnifiedDate | Date | string): boolean {
    const targetDate = this.convertToDate(date);
    return this.dayjsInstance.isBefore(dayjs(targetDate));
  }

  isAfter(date: UnifiedDate | Date | string): boolean {
    const targetDate = this.convertToDate(date);
    return this.dayjsInstance.isAfter(dayjs(targetDate));
  }

  isValid(): boolean {
    return this.dayjsInstance.isValid();
  }

  toDate(): Date {
    return this.dayjsInstance.toDate();
  }

  toISOString(): string {
    return this.dayjsInstance.toISOString();
  }

  valueOf(): number {
    return this.dayjsInstance.valueOf();
  }

  private convertToDate(date: UnifiedDate | Date | string): Date {
    if (date instanceof Date) {
      return date;
    }
    if (typeof date === 'string') {
      return new Date(date);
    }
    return date.toDate();
  }
}

/**
 * Dayjs 适配器实现
 */
export class DayjsAdapter extends DateAdapter {
  readonly library: DateLibrary = 'dayjs';

  create(input?: string | Date | number): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(input));
  }

  now(): UnifiedDate {
    return new DayjsUnifiedDate(dayjs());
  }

  today(): UnifiedDate {
    return new DayjsUnifiedDate(dayjs().startOf('day'));
  }

  format(date: Date | string, pattern: string): string {
    return dayjs(date).format(pattern);
  }

  formatDistance(date: Date | string, baseDate?: Date | string): string {
    const target = dayjs(date);
    const base = baseDate ? dayjs(baseDate) : dayjs();
    return target.from(base);
  }

  formatRelative(date: Date | string, baseDate?: Date | string): string {
    const target = dayjs(date);
    const base = baseDate ? dayjs(baseDate) : dayjs();

    // 实现相对时间格式化
    const diffInDays = target.diff(base, 'day');

    if (diffInDays === 0) {
      return '今天 ' + target.format('HH:mm');
    } else if (diffInDays === 1) {
      return '明天 ' + target.format('HH:mm');
    } else if (diffInDays === -1) {
      return '昨天 ' + target.format('HH:mm');
    } else if (diffInDays > 1 && diffInDays <= 7) {
      return target.format('dddd HH:mm');
    } else {
      return target.format('YYYY-MM-DD HH:mm');
    }
  }

  add(date: Date | string, amount: number, unit: DateUnit): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(date).add(amount, unit));
  }

  subtract(date: Date | string, amount: number, unit: DateUnit): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(date).subtract(amount, unit));
  }

  diff(dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit): number {
    return dayjs(dateLeft).diff(dayjs(dateRight), unit);
  }

  isSame(dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit): boolean {
    return dayjs(dateLeft).isSame(dayjs(dateRight), unit);
  }

  isBefore(dateLeft: Date | string, dateRight: Date | string): boolean {
    return dayjs(dateLeft).isBefore(dayjs(dateRight));
  }

  isAfter(dateLeft: Date | string, dateRight: Date | string): boolean {
    return dayjs(dateLeft).isAfter(dayjs(dateRight));
  }

  isValid(date: any): boolean {
    return dayjs(date).isValid();
  }

  parse(dateString: string, format: string): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(dateString, format));
  }

  parseISO(dateString: string): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(dateString));
  }

  startOf(date: Date | string, unit: DateUnit): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(date).startOf(unit));
  }

  endOf(date: Date | string, unit: DateUnit): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(date).endOf(unit));
  }

  getYear(date: Date | string): number {
    return dayjs(date).year();
  }

  getMonth(date: Date | string): number {
    return dayjs(date).month();
  }

  getDate(date: Date | string): number {
    return dayjs(date).date();
  }

  getHour(date: Date | string): number {
    return dayjs(date).hour();
  }

  getMinute(date: Date | string): number {
    return dayjs(date).minute();
  }

  getSecond(date: Date | string): number {
    return dayjs(date).second();
  }

  setYear(date: Date | string, year: number): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(date).year(year));
  }

  setMonth(date: Date | string, month: number): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(date).month(month));
  }

  setDate(date: Date | string, day: number): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(date).date(day));
  }

  setHour(date: Date | string, hour: number): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(date).hour(hour));
  }

  setMinute(date: Date | string, minute: number): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(date).minute(minute));
  }

  setSecond(date: Date | string, second: number): UnifiedDate {
    return new DayjsUnifiedDate(dayjs(date).second(second));
  }
}
