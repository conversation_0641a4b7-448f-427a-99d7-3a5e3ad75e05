'use client';

import React, { useState } from 'react';

import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Container,
  DollarSign,
  Droplets,
  Mountain,
  Sparkles,
  Target,
  Thermometer,
  Zap,
  Beaker,
  Layers,
  Waves,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Checkbox } from '@/shared/components/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { useToast } from '@/shared/hooks/use-toast';
import {
  aiRatioService,
  type AIRatioGenerationRequest,
} from '@/features/ratio-management/services/ratio/aiRatioService';
import type { RatioGenerationResult } from '@/features/ai/genkit';

interface AIRatioGeneratorProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (config: AIGenerationConfig) => void;
  availableMaterials: Array<{
    id: string;
    name: string;
    type: string;
    specification: string;
    available: boolean;
    amount: number;
  }>;
}

interface AIGenerationConfig {
  targetStrength: string;
  environment: string;
  costLevel: 'high' | 'medium' | 'low';
  specialRequirements: string[];
  selectedMaterials: string[];
  additionalParams: {
    slump: number;
    durability: string;
    workability: string;
    temperature: number;
  };
  generatedRatio?: RatioGenerationResult;
}

// 材料分组辅助函数
const groupMaterialsByType = (
  materials: Array<{
    id: string;
    name: string;
    type: string;
    specification: string;
    available: boolean;
    amount: number;
  }>
) => {
  return materials.reduce(
    (groups, material) => {
      const type = material.type;
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(material);
      return groups;
    },
    {} as Record<string, typeof materials>
  );
};

// 获取材料类型图标
const getMaterialTypeIcon = (type: string) => {
  switch (type) {
    case '水泥':
      return <Container className='h-4 w-4 text-gray-600' />;
    case '砂子':
    case '石子':
      return <Mountain className='h-4 w-4 text-amber-600' />;
    case '水':
      return <Droplets className='h-4 w-4 text-blue-600' />;
    case '外加剂':
      return <Beaker className='h-4 w-4 text-purple-600' />;
    case '粉煤灰':
      return <Layers className='h-4 w-4 text-gray-500' />;
    case '矿粉':
      return <Waves className='h-4 w-4 text-green-600' />;
    default:
      return <Container className='h-4 w-4 text-gray-400' />;
  }
};

// 获取材料类型标签
const getMaterialTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    水泥: '胶凝材料',
    砂子: '细骨料',
    石子: '粗骨料',
    水: '拌合用水',
    外加剂: '化学外加剂',
    粉煤灰: '矿物掺合料',
    矿粉: '矿物掺合料',
  };
  return labels[type] || type;
};

// 获取材料单位
const getMaterialUnit = (type: string) => {
  switch (type) {
    case '水':
      return 'L';
    case '外加剂':
      return 'kg';
    default:
      return 't';
  }
};

export function AIRatioGenerator({
  isOpen,
  onClose,
  onGenerate,
  availableMaterials,
}: AIRatioGeneratorProps) {
  const { toast } = useToast();
  const [config, setConfig] = useState<AIGenerationConfig>({
    targetStrength: 'C25',
    environment: 'normal',
    costLevel: 'medium',
    specialRequirements: [],
    selectedMaterials: [],
    additionalParams: {
      slump: 180,
      durability: 'normal',
      workability: 'good',
      temperature: 20,
    },
  });
  const [isGenerating, setIsGenerating] = useState(false);

  const strengthOptions = ['C15', 'C20', 'C25', 'C30', 'C35', 'C40', 'C45', 'C50', 'C55', 'C60'];

  const environmentOptions = [
    { value: 'normal', label: '一般环境', icon: <Target className='h-4 w-4' /> },
    { value: 'marine', label: '海洋环境', icon: <Droplets className='h-4 w-4' /> },
    { value: 'freeze', label: '冻融环境', icon: <Thermometer className='h-4 w-4' /> },
    { value: 'chemical', label: '化学侵蚀', icon: <AlertTriangle className='h-4 w-4' /> },
    { value: 'high_temp', label: '高温环境', icon: <Thermometer className='h-4 w-4' /> },
  ];

  const specialRequirements = [
    { id: 'early_strength', label: '早强要求', icon: <Zap className='h-4 w-4' /> },
    { id: 'low_heat', label: '低水化热', icon: <Thermometer className='h-4 w-4' /> },
    { id: 'high_durability', label: '高耐久性', icon: <CheckCircle className='h-4 w-4' /> },
    { id: 'self_compacting', label: '自密实', icon: <Droplets className='h-4 w-4' /> },
    { id: 'anti_crack', label: '抗裂要求', icon: <Mountain className='h-4 w-4' /> },
    { id: 'waterproof', label: '防水要求', icon: <Droplets className='h-4 w-4' /> },
  ];

  const handleMaterialToggle = (materialId: string, checked: boolean) => {
    setConfig(prev => ({
      ...prev,
      selectedMaterials: checked
        ? [...prev.selectedMaterials, materialId]
        : prev.selectedMaterials.filter(id => id !== materialId),
    }));
  };

  const handleRequirementToggle = (requirementId: string, checked: boolean) => {
    setConfig(prev => ({
      ...prev,
      specialRequirements: checked
        ? [...prev.specialRequirements, requirementId]
        : prev.specialRequirements.filter(id => id !== requirementId),
    }));
  };

  const handleGenerate = async () => {
    if (config.selectedMaterials.length === 0) {
      toast({
        title: '请选择材料',
        description: '至少需要选择一种可用材料',
        variant: 'destructive',
      });
      return;
    }

    setIsGenerating(true);

    try {
      // 构建AI生成请求
      const request: AIRatioGenerationRequest = {
        targetStrength: config.targetStrength,
        environment: config.environment,
        costLevel: config.costLevel,
        specialRequirements: config.specialRequirements,
        selectedMaterials: config.selectedMaterials,
        additionalParams: config.additionalParams,
        availableMaterials: availableMaterials,
      };

      // 验证请求参数
      const validation = aiRatioService.validateRequest(request);
      if (!validation.valid) {
        toast({
          title: '参数验证失败',
          description: validation.errors.join(', '),
          variant: 'destructive',
        });
        setIsGenerating(false);
        return;
      }

      // 调用AI生成配比
      const response = await aiRatioService.generateRatio(request);

      if (!response.success || !response.data) {
        throw new Error(response.message || 'AI配比生成失败');
      }

      const result = response.data;

      // 将AI生成的结果转换为组件期望的格式
      const aiConfig = {
        ...config,
        generatedRatio: result,
      };

      onGenerate(aiConfig);
      setIsGenerating(false);
      onClose();

      toast({
        title: 'AI配比生成完成',
        description: `已生成${result.ratioName}，质量评分：${result.calculationResults.qualityScore.toFixed(1)}分`,
      });
    } catch (error) {
      console.error('AI配比生成失败:', error);
      setIsGenerating(false);

      toast({
        title: 'AI配比生成失败',
        description: '请检查网络连接或稍后重试',
        variant: 'destructive',
      });
    }
  };

  const getCostLevelColor = (level: string) => {
    switch (level) {
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Sparkles className='h-6 w-6 text-purple-600' />
            AI智能配比生成
          </DialogTitle>
        </DialogHeader>

        <div className='grid grid-cols-2 gap-6'>
          {/* 左侧：基本参数 */}
          <div className='space-y-4'>
            <Card>
              <CardHeader className='pb-3'>
                <CardTitle className='text-lg'>基本参数</CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                {/* 强度等级 */}
                <div>
                  <Label className='text-sm font-medium'>目标强度等级</Label>
                  <Select
                    value={config.targetStrength}
                    onValueChange={value => setConfig(prev => ({ ...prev, targetStrength: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {strengthOptions.map(strength => (
                        <SelectItem key={strength} value={strength}>
                          {strength}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 使用环境 */}
                <div>
                  <Label className='text-sm font-medium'>使用环境</Label>
                  <Select
                    value={config.environment}
                    onValueChange={value => setConfig(prev => ({ ...prev, environment: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {environmentOptions.map(env => (
                        <SelectItem key={env.value} value={env.value}>
                          <div className='flex items-center gap-2'>
                            {env.icon}
                            {env.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 成本要求 */}
                <div>
                  <Label className='text-sm font-medium'>成本要求</Label>
                  <div className='grid grid-cols-3 gap-2 mt-2'>
                    {['low', 'medium', 'high'].map(level => (
                      <Button
                        key={level}
                        variant={config.costLevel === level ? 'default' : 'outline'}
                        size='sm'
                        onClick={() => setConfig(prev => ({ ...prev, costLevel: level as any }))}
                        className={config.costLevel === level ? getCostLevelColor(level) : ''}
                      >
                        <DollarSign className='h-3 w-3 mr-1' />
                        {level === 'low' ? '经济' : level === 'medium' ? '标准' : '优质'}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* 附加参数 */}
                <div className='grid grid-cols-2 gap-3'>
                  <div>
                    <Label className='text-xs'>坍落度 (mm)</Label>
                    <Input
                      type='number'
                      value={config.additionalParams.slump}
                      onChange={e =>
                        setConfig(prev => ({
                          ...prev,
                          additionalParams: {
                            ...prev.additionalParams,
                            slump: parseInt(e.target.value) || 180,
                          },
                        }))
                      }
                      className='h-8'
                    />
                  </div>
                  <div>
                    <Label className='text-xs'>环境温度 (°C)</Label>
                    <Input
                      type='number'
                      value={config.additionalParams.temperature}
                      onChange={e =>
                        setConfig(prev => ({
                          ...prev,
                          additionalParams: {
                            ...prev.additionalParams,
                            temperature: parseInt(e.target.value) || 20,
                          },
                        }))
                      }
                      className='h-8'
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 特殊要求 */}
            <Card>
              <CardHeader className='pb-3'>
                <CardTitle className='text-lg'>特殊要求</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='grid grid-cols-2 gap-3'>
                  {specialRequirements.map(req => (
                    <div key={req.id} className='flex items-center space-x-2'>
                      <Checkbox
                        id={req.id}
                        checked={config.specialRequirements.includes(req.id)}
                        onCheckedChange={checked => handleRequirementToggle(req.id, !!checked)}
                      />
                      <Label htmlFor={req.id} className='text-sm flex items-center gap-1'>
                        {req.icon}
                        {req.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：可用材料 */}
          <div>
            <Card>
              <CardHeader className='pb-3'>
                <CardTitle className='text-lg'>可用材料</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-3 max-h-[400px] overflow-y-auto'>
                  {/* 按材料类型分组显示 */}
                  {Object.entries(groupMaterialsByType(availableMaterials)).map(
                    ([type, materials]) => (
                      <div key={type} className='space-y-2'>
                        <div className='flex items-center gap-2 text-sm font-medium text-gray-700'>
                          {getMaterialTypeIcon(type)}
                          {getMaterialTypeLabel(type)}
                        </div>
                        <div className='space-y-1 pl-6'>
                          {materials.map(material => (
                            <div
                              key={material.id}
                              className={`flex items-center justify-between p-2 border rounded ${
                                material.available
                                  ? 'border-green-200 bg-green-50'
                                  : 'border-gray-200 bg-gray-50'
                              }`}
                            >
                              <div className='flex items-center space-x-2'>
                                <Checkbox
                                  id={material.id}
                                  checked={config.selectedMaterials.includes(material.id)}
                                  onCheckedChange={checked =>
                                    handleMaterialToggle(material.id, !!checked)
                                  }
                                  disabled={!material.available}
                                />
                                <div>
                                  <Label htmlFor={material.id} className='text-sm font-medium'>
                                    {material.name}
                                  </Label>
                                  <p className='text-xs text-muted-foreground'>
                                    {material.specification}
                                  </p>
                                </div>
                              </div>
                              <div className='text-right'>
                                <Badge variant={material.available ? 'default' : 'secondary'}>
                                  {material.available
                                    ? `${material.amount}${getMaterialUnit(type)}`
                                    : '缺货'}
                                </Badge>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 底部操作 */}
        <div className='flex justify-between items-center pt-4 border-t'>
          <div className='text-sm text-muted-foreground'>
            已选择 {config.selectedMaterials.length} 种材料
          </div>
          <div className='flex gap-2'>
            <Button variant='outline' onClick={onClose}>
              取消
            </Button>
            <Button
              onClick={handleGenerate}
              disabled={isGenerating || config.selectedMaterials.length === 0}
              className='gap-2 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700'
            >
              {isGenerating ? (
                <>
                  <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
                  AI生成中...
                </>
              ) : (
                <>
                  <Sparkles className='h-4 w-4' />
                  生成配比
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
