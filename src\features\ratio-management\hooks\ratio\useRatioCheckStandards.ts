/**
 * 配比检查标准管理Hook
 */

import { useState, useEffect, useCallback } from 'react';
import { safeDownloadFile } from '@/core/utils/dom-safe-operations';
import type {
  RatioCheckStandard,
  StandardQueryCondition,
  RatioCheckResult,
  RatioViolation,
  StandardValidationConfig,
} from '@/core/types/ratio-check-standard';
import {
  DEFAULT_RATIO_CHECK_STANDARDS,
  DEFAULT_STANDARD_CONFIG,
} from '@/infrastructure/api/mock/ratio-check-standards';

const STORAGE_KEY = 'ratio-check-standards';
const CONFIG_STORAGE_KEY = 'ratio-check-standards-config';

/**
 * 配比检查标准管理Hook
 */
export function useRatioCheckStandards() {
  const [standards, setStandards] = useState<RatioCheckStandard[]>([]);
  const [config, setConfig] = useState<StandardValidationConfig>(
    DEFAULT_STANDARD_CONFIG.validationConfig
  );
  const [loading, setLoading] = useState(true);

  // 初始化数据
  useEffect(() => {
    loadStandards();
    loadConfig();
  }, []);

  /**
   * 从localStorage加载标准
   */
  /**
   * 迁移旧数据：将数字类型的强度值转换为字符串
   */
  const migrateStandardsData = useCallback((standards: any[]): RatioCheckStandard[] => {
    return standards.map(standard => ({
      ...standard,
      strength: typeof standard.strength === 'string' ? standard.strength : `C${standard.strength}`, // 将数字转换为 C + 数字的格式
    }));
  }, []);

  const loadStandards = useCallback(() => {
    const saveToStorage = (standardsToSave: RatioCheckStandard[]) => {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(standardsToSave));
      } catch (error) {
        console.error('保存配比检查标准失败:', error);
      }
    };

    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedStandards = JSON.parse(stored);
        // 迁移数据格式
        const migratedStandards = migrateStandardsData(parsedStandards);
        setStandards(migratedStandards);

        // 如果数据被迁移了，保存新格式
        if (JSON.stringify(migratedStandards) !== JSON.stringify(parsedStandards)) {
          saveToStorage(migratedStandards);
        }
      } else {
        // 使用默认标准
        setStandards(DEFAULT_RATIO_CHECK_STANDARDS);
        saveToStorage(DEFAULT_RATIO_CHECK_STANDARDS);
      }
    } catch (error) {
      console.error('加载配比检查标准失败:', error);
      setStandards(DEFAULT_RATIO_CHECK_STANDARDS);
    } finally {
      setLoading(false);
    }
  }, [migrateStandardsData]);

  /**
   * 从localStorage加载配置
   */
  const loadConfig = useCallback(() => {
    try {
      const stored = localStorage.getItem(CONFIG_STORAGE_KEY);
      if (stored) {
        const parsedConfig = JSON.parse(stored);
        setConfig(parsedConfig);
      }
    } catch (error) {
      console.error('加载配比检查配置失败:', error);
    }
  }, []);

  /**
   * 保存标准到localStorage
   */
  const saveStandards = useCallback((standardsToSave: RatioCheckStandard[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(standardsToSave));
    } catch (error) {
      console.error('保存配比检查标准失败:', error);
    }
  }, []);

  /**
   * 保存配置到localStorage
   */
  const saveConfig = useCallback((configToSave: StandardValidationConfig) => {
    try {
      localStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(configToSave));
      setConfig(configToSave);
    } catch (error) {
      console.error('保存配比检查配置失败:', error);
    }
  }, []);

  /**
   * 添加新标准
   */
  const addStandard = useCallback(
    (standard: Omit<RatioCheckStandard, 'id' | 'createdAt' | 'updatedAt'>) => {
      const newStandard: RatioCheckStandard = {
        ...standard,
        id: `std-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const updatedStandards = [...standards, newStandard];
      setStandards(updatedStandards);
      saveStandards(updatedStandards);
      return newStandard;
    },
    [standards, saveStandards]
  );

  /**
   * 更新标准
   */
  const updateStandard = useCallback(
    (id: string, updates: Partial<RatioCheckStandard>) => {
      const updatedStandards = standards.map(standard =>
        standard.id === id
          ? { ...standard, ...updates, updatedAt: new Date().toISOString() }
          : standard
      );
      setStandards(updatedStandards);
      saveStandards(updatedStandards);
    },
    [standards, saveStandards]
  );

  /**
   * 删除标准
   */
  const deleteStandard = useCallback(
    (id: string) => {
      const updatedStandards = standards.filter(standard => standard.id !== id);
      setStandards(updatedStandards);
      saveStandards(updatedStandards);
    },
    [standards, saveStandards]
  );

  /**
   * 查询标准
   */
  const queryStandards = useCallback(
    (condition: StandardQueryCondition = {}) => {
      return standards.filter(standard => {
        if (condition.strength !== undefined) {
          // 确保强度比较时类型一致，兼容旧数据
          const standardStrength =
            typeof standard.strength === 'string' ? standard.strength : String(standard.strength);
          if (standardStrength !== condition.strength) {
            return false;
          }
        }
        if (condition.category !== undefined && standard.category !== condition.category) {
          return false;
        }
        if (condition.isActive !== undefined && standard.isActive !== condition.isActive) {
          return false;
        }
        return true;
      });
    },
    [standards]
  );

  /**
   * 获取适用的标准
   */
  const getApplicableStandard = useCallback(
    (strength: string, category?: string) => {
      const candidates = queryStandards({
        strength,
        category,
        isActive: true,
      });

      // 优先返回完全匹配的标准
      if (candidates.length > 0) {
        return candidates[0];
      }

      // 如果没有完全匹配，尝试只匹配强度
      const strengthMatches = queryStandards({
        strength,
        isActive: true,
      });

      return strengthMatches.length > 0 ? strengthMatches[0] : null;
    },
    [queryStandards]
  );

  /**
   * 验证配比是否符合标准
   */
  const validateRatio = useCallback(
    (ratioData: Record<string, number>, strength: string, category?: string): RatioCheckResult => {
      const standard = getApplicableStandard(strength, category);

      if (!standard) {
        return {
          isCompliant: true,
          violations: [],
          warnings: [],
        };
      }

      const violations: RatioViolation[] = [];

      // 检查各项限制
      const checkField = (
        field: keyof RatioCheckStandard,
        ratioField: string,
        fieldName: string
      ) => {
        const minField = field as string;
        const maxField = minField.replace('Min', 'Max');

        const minValue = standard[minField as keyof RatioCheckStandard] as number;
        const maxValue = standard[maxField as keyof RatioCheckStandard] as number;
        const currentValue = ratioData[ratioField];

        if (currentValue !== undefined) {
          if (minValue !== undefined && currentValue < minValue) {
            violations.push({
              field: ratioField,
              fieldName,
              currentValue,
              minLimit: minValue,
              severity: config.enableStrict ? 'error' : 'warning',
              message: `${fieldName}(${currentValue})低于标准最小值(${minValue})`,
            });
          }

          if (maxValue !== undefined && currentValue > maxValue) {
            violations.push({
              field: ratioField,
              fieldName,
              currentValue,
              maxLimit: maxValue,
              severity: config.enableStrict ? 'error' : 'warning',
              message: `${fieldName}(${currentValue})超过标准最大值(${maxValue})`,
            });
          }
        }
      };

      // 检查各种材料
      checkField('cementMin', 'cement', '水泥用量');
      checkField('sandMin', 'sand', '砂子用量');
      checkField('stoneMin', 'stone', '石子用量');
      checkField('additiveMin', 'additive', '外加剂用量');
      checkField('flyAshMin', 'flyAsh', '粉煤灰用量');
      checkField('mineralPowderMin', 'mineralPowder', '矿粉用量');
      checkField('waterMin', 'water', '水用量');

      return {
        isCompliant: violations.filter(v => v.severity === 'error').length === 0,
        violations,
        warnings: [],
        appliedStandard: standard,
      };
    },
    [getApplicableStandard, config.enableStrict]
  );

  /**
   * 重置为默认标准
   */
  const resetToDefaults = useCallback(() => {
    setStandards(DEFAULT_RATIO_CHECK_STANDARDS);
    saveStandards(DEFAULT_RATIO_CHECK_STANDARDS);
  }, [saveStandards]);

  /**
   * 导出标准
   */
  const exportStandards = useCallback(() => {
    const exportData = {
      version: '1.0',
      exportDate: new Date().toISOString(),
      standards,
      metadata: {
        source: 'TMH Task Dispatcher',
        description: '配比检查标准导出数据',
      },
    };

    // 使用安全的下载函数
    safeDownloadFile(
      JSON.stringify(exportData, null, 2),
      `ratio-check-standards-${new Date().toISOString().split('T')[0]}.json`,
      'application/json'
    );
  }, [standards]);

  /**
   * 导入标准
   */
  const importStandards = useCallback(
    (file: File) => {
      return new Promise<void>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => {
          try {
            const content = e.target?.result as string;
            const importData = JSON.parse(content);

            if (importData.standards && Array.isArray(importData.standards)) {
              setStandards(importData.standards);
              saveStandards(importData.standards);
              resolve();
            } else {
              reject(new Error('无效的导入文件格式'));
            }
          } catch (error) {
            reject(new Error('文件解析失败'));
          }
        };
        reader.onerror = () => reject(new Error('文件读取失败'));
        reader.readAsText(file);
      });
    },
    [saveStandards]
  );

  return {
    // 数据
    standards,
    config,
    loading,

    // 操作方法
    addStandard,
    updateStandard,
    deleteStandard,
    queryStandards,
    getApplicableStandard,
    validateRatio,

    // 配置方法
    saveConfig,
    resetToDefaults,

    // 导入导出
    exportStandards,
    importStandards,
  };
}
