/**
 * 性能优化 Hook
 * 提供全面的性能优化功能
 */

import { useEffect, useCallback, useRef, useMemo } from 'react';
import { globalCache } from '@/infrastructure/storage/cache/performance-cache';

// ==================== 类型定义 ====================

export interface PerformanceOptimizationConfig {
  enableCaching: boolean;
  enableLazyLoading: boolean;
  enableVirtualization: boolean;
  enablePreloading: boolean;
  cacheStrategy: 'memory' | 'persistent' | 'hybrid';
  preloadStrategy: 'idle' | 'hover' | 'viewport';
  virtualizationThreshold: number;
}

export interface PerformanceMetrics {
  renderTime: number;
  cacheHitRate: number;
  memoryUsage: number;
  bundleSize: number;
  networkRequests: number;
}

// ==================== 默认配置 ====================

const DEFAULT_CONFIG: PerformanceOptimizationConfig = {
  enableCaching: true,
  enableLazyLoading: true,
  enableVirtualization: true,
  enablePreloading: true,
  cacheStrategy: 'hybrid',
  preloadStrategy: 'idle',
  virtualizationThreshold: 100,
};

// ==================== 主要 Hook ====================

export function usePerformanceOptimization(
  componentName: string,
  config: Partial<PerformanceOptimizationConfig> = {}
) {
  const finalConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config]);
  const renderCountRef = useRef(0);
  const lastRenderTimeRef = useRef(0);
  const cacheHitsRef = useRef(0);
  const cacheMissesRef = useRef(0);

  // 简化的性能监控
  const startMeasurement = useCallback(
    (name: string) => {
      if (typeof window !== 'undefined' && 'performance' in window) {
        performance.mark(`${componentName}-${name}-start`);
      }
    },
    [componentName]
  );

  const endMeasurement = useCallback(
    (name: string) => {
      if (typeof window !== 'undefined' && 'performance' in window) {
        performance.mark(`${componentName}-${name}-end`);
        try {
          performance.measure(
            `${componentName}-${name}`,
            `${componentName}-${name}-start`,
            `${componentName}-${name}-end`
          );
        } catch (error) {
          // 忽略测量错误
        }
      }
    },
    [componentName]
  );

  // ==================== 缓存优化 ====================

  const cacheData = useCallback(
    <T>(key: string, data: T, ttl?: number): void => {
      if (!finalConfig.enableCaching) return;

      const cacheKey = `${componentName}.${key}`;
      const options = {
        ttl,
        persistent:
          finalConfig.cacheStrategy === 'persistent' || finalConfig.cacheStrategy === 'hybrid',
      };

      globalCache.set(cacheKey, data, options);
    },
    [componentName, finalConfig.enableCaching, finalConfig.cacheStrategy]
  );

  const getCachedData = useCallback(
    <T>(key: string): T | null => {
      if (!finalConfig.enableCaching) return null;

      const cacheKey = `${componentName}.${key}`;
      const data = globalCache.get<T>(cacheKey);

      if (data !== null) {
        cacheHitsRef.current++;
      } else {
        cacheMissesRef.current++;
      }

      return data;
    },
    [componentName, finalConfig.enableCaching]
  );

  // ==================== 渲染优化 ====================

  const measureRender = useCallback(
    (renderFn: () => void) => {
      const measurementId = `${componentName}-render-${renderCountRef.current}`;

      startMeasurement(measurementId);
      const startTime = performance.now();

      renderFn();

      const endTime = performance.now();
      const duration = endTime - startTime;

      endMeasurement(measurementId);

      renderCountRef.current++;
      lastRenderTimeRef.current = duration;

      // 如果渲染时间过长，发出警告
      if (duration > 16) {
        console.warn(`Slow render detected in ${componentName}: ${duration.toFixed(2)}ms`);
      }

      return duration;
    },
    [componentName, startMeasurement, endMeasurement]
  );

  // ==================== 预加载优化 ====================

  const preloadResource = useCallback(
    (
      resourceLoader: () => Promise<any>,
      _priority: 'high' | 'low' = 'low'
    ): (() => void) | undefined => {
      if (!finalConfig.enablePreloading) return undefined;

      const preload = () => {
        resourceLoader().catch(error => {
          console.warn(`Preload failed for ${componentName}:`, error);
        });
      };

      switch (finalConfig.preloadStrategy) {
        case 'idle':
          if ('requestIdleCallback' in window) {
            requestIdleCallback(preload, { timeout: 5000 });
          } else {
            setTimeout(preload, 100);
          }
          return undefined;
        case 'hover':
          // 这需要在组件中手动触发
          return preload;
        case 'viewport':
          // 这需要结合 Intersection Observer 使用
          return preload;
        default:
          preload();
          return undefined;
      }
    },
    [componentName, finalConfig.enablePreloading, finalConfig.preloadStrategy]
  );

  // ==================== 虚拟化优化 ====================

  const shouldUseVirtualization = useCallback(
    (itemCount: number): boolean => {
      return finalConfig.enableVirtualization && itemCount > finalConfig.virtualizationThreshold;
    },
    [finalConfig.enableVirtualization, finalConfig.virtualizationThreshold]
  );

  // ==================== 内存优化 ====================

  const optimizeMemory = useCallback(() => {
    // 清理过期缓存
    if (finalConfig.enableCaching) {
      const stats = globalCache.getStats();
      if (stats.memory.utilization > 80) {
        console.warn(`High cache utilization: ${stats.memory.utilization.toFixed(1)}%`);
      }
    }

    // 强制垃圾回收（如果可用）
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
    }
  }, [finalConfig.enableCaching]);

  // ==================== 性能指标 ====================

  const getPerformanceMetrics = useCallback((): PerformanceMetrics => {
    const cacheTotal = cacheHitsRef.current + cacheMissesRef.current;
    const cacheHitRate = cacheTotal > 0 ? (cacheHitsRef.current / cacheTotal) * 100 : 0;

    return {
      renderTime: lastRenderTimeRef.current,
      cacheHitRate,
      memoryUsage: globalCache.getStats().memory.currentSize,
      bundleSize: 0, // 需要从构建工具获取
      networkRequests: 0, // 需要从网络监控获取
    };
  }, []);

  // ==================== 自动优化 ====================

  useEffect(() => {
    // 定期清理内存
    const memoryCleanupInterval = setInterval(optimizeMemory, 60000); // 每分钟

    // 性能监控
    const performanceCheckInterval = setInterval(() => {
      const metrics = getPerformanceMetrics();

      if (metrics.renderTime > 50) {
        console.warn(
          `Component ${componentName} has slow renders: ${metrics.renderTime.toFixed(2)}ms`
        );
      }

      if (metrics.cacheHitRate < 50 && cacheHitsRef.current + cacheMissesRef.current > 10) {
        console.warn(
          `Component ${componentName} has low cache hit rate: ${metrics.cacheHitRate.toFixed(1)}%`
        );
      }
    }, 30000); // 每30秒检查

    return () => {
      clearInterval(memoryCleanupInterval);
      clearInterval(performanceCheckInterval);
    };
  }, [componentName, optimizeMemory, getPerformanceMetrics]);

  // ==================== 返回值 ====================

  return {
    // 缓存相关
    cacheData,
    getCachedData,

    // 渲染优化
    measureRender,

    // 性能监控
    startMeasurement,
    endMeasurement,

    // 预加载
    preloadResource,

    // 虚拟化
    shouldUseVirtualization,

    // 内存优化
    optimizeMemory,

    // 性能指标
    getPerformanceMetrics,

    // 配置
    config: finalConfig,

    // 统计信息
    stats: {
      renderCount: renderCountRef.current,
      lastRenderTime: lastRenderTimeRef.current,
      cacheHits: cacheHitsRef.current,
      cacheMisses: cacheMissesRef.current,
    },
  };
}

// ==================== 专用 Hooks ====================

/**
 * 数据缓存 Hook
 */
export function useDataCache<T>(
  key: string,
  fetcher: () => Promise<T> | T,
  options: { ttl?: number; enabled?: boolean } = {}
) {
  const { ttl = 5 * 60 * 1000, enabled = true } = options;

  const fetchData = useCallback(async (): Promise<T> => {
    if (!enabled) {
      return typeof fetcher === 'function' ? await fetcher() : fetcher;
    }

    // 尝试从缓存获取
    const cached = globalCache.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // 获取新数据
    const data = typeof fetcher === 'function' ? await fetcher() : fetcher;

    // 缓存数据
    globalCache.set(key, data, { ttl });

    return data;
  }, [key, fetcher, ttl, enabled]);

  return { fetchData };
}

/**
 * 组件预加载 Hook
 */
export function useComponentPreload(
  componentLoader: () => Promise<any>,
  trigger: 'idle' | 'hover' | 'mount' = 'idle'
) {
  const preloadResource = useCallback(
    (loader: () => Promise<any>) => {
      const preload = () => {
        loader().catch(error => {
          console.warn('Component preload failed:', error);
        });
      };

      switch (trigger) {
        case 'idle':
          if ('requestIdleCallback' in window) {
            requestIdleCallback(preload, { timeout: 5000 });
          } else {
            setTimeout(preload, 100);
          }
          break;
        case 'mount':
          preload();
          break;
        default:
          // hover 需要手动触发
          break;
      }
    },
    [trigger]
  );

  useEffect(() => {
    if (trigger === 'mount' || trigger === 'idle') {
      preloadResource(componentLoader);
    }
  }, [componentLoader, trigger, preloadResource]);

  const preloadOnHover = useCallback(() => {
    if (trigger === 'hover') {
      preloadResource(componentLoader);
    }
  }, [componentLoader, trigger, preloadResource]);

  return { preloadOnHover };
}
