# TMH任务调度系统 - 简单PuTTY安装脚本

Write-Host "🔧 TMH任务调度系统 - 安装PuTTY工具" -ForegroundColor Green
Write-Host "🎯 目标: 实现SSH密码自动认证" -ForegroundColor Cyan
Write-Host ""

# 检查是否已安装
$plink = Get-Command plink -ErrorAction SilentlyContinue
if ($plink) {
    Write-Host "✅ PuTTY已安装: $($plink.Source)" -ForegroundColor Green
    Write-Host "现在可以运行: npm run deploy:intranet" -ForegroundColor Cyan
    exit 0
}

Write-Host "📦 开始安装PuTTY..." -ForegroundColor Blue

# 方法1: 使用winget
try {
    Write-Host "🔄 尝试使用winget安装..." -ForegroundColor Yellow
    winget install PuTTY.PuTTY --accept-source-agreements --accept-package-agreements
    
    # 检查安装结果
    $newPlink = Get-Command plink -ErrorAction SilentlyContinue
    if ($newPlink) {
        Write-Host "✅ winget安装成功" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "⚠️ winget安装失败" -ForegroundColor Yellow
}

# 方法2: 手动下载
try {
    Write-Host "🔄 尝试手动下载安装..." -ForegroundColor Yellow
    
    $url = "https://the.earth.li/~sgtatham/putty/latest/w64/putty.zip"
    $tempFile = "$env:TEMP\putty.zip"
    $installDir = "$env:ProgramFiles\PuTTY"
    
    Write-Host "📥 下载PuTTY..." -ForegroundColor Blue
    Invoke-WebRequest -Uri $url -OutFile $tempFile -UseBasicParsing
    
    Write-Host "📦 解压到安装目录..." -ForegroundColor Blue
    if (Test-Path $installDir) {
        Remove-Item $installDir -Recurse -Force
    }
    Expand-Archive -Path $tempFile -DestinationPath $installDir -Force
    
    Write-Host "📝 添加到PATH环境变量..." -ForegroundColor Blue
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
    if ($currentPath -notlike "*$installDir*") {
        [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$installDir", "Machine")
        $env:PATH += ";$installDir"
    }
    
    # 清理临时文件
    Remove-Item $tempFile -Force
    
    # 检查安装结果
    $newPlink = Get-Command plink -ErrorAction SilentlyContinue
    if ($newPlink) {
        Write-Host "✅ 手动安装成功" -ForegroundColor Green
        Write-Host "安装位置: $installDir" -ForegroundColor White
        exit 0
    }
} catch {
    Write-Host "❌ 手动安装失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 安装失败
Write-Host ""
Write-Host "❌ PuTTY安装失败" -ForegroundColor Red
Write-Host ""
Write-Host "📋 手动安装选项:" -ForegroundColor Cyan
Write-Host "1. 访问 https://www.putty.org/ 下载安装" -ForegroundColor White
Write-Host "2. 或使用SSH密钥认证: npm run setup:ssh" -ForegroundColor White
Write-Host "3. 重启PowerShell后重试此脚本" -ForegroundColor White
Write-Host ""
Write-Host "💡 提示: 安装后重启命令行以刷新PATH" -ForegroundColor Yellow
