'use client';

import { Palette } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/dropdown-menu';
import { THEME_OPTIONS, useTheme, type Theme } from '@/shared/components/contexts/theme-provider';

import { IconButton } from '../icon-button';

export function ThemeSelector() {
  const { setTheme, theme: currentTheme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <IconButton icon={Palette} tooltipLabel='切换主题' />
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-56'>
        <DropdownMenuLabel>选择主题</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {THEME_OPTIONS.map(option => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => setTheme(option.value as Theme)} // Cast needed if Theme type is strict
            className={
              option.value === currentTheme
                ? 'bg-accent text-accent-foreground focus:bg-accent focus:text-accent-foreground'
                : ''
            }
          >
            {option.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
