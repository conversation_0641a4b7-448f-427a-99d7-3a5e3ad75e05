/**
 * 依赖关系分析工具
 * 分析和优化组件间的依赖关系，降低耦合度
 */

// ==================== 依赖关系类型定义 ====================

export interface ComponentDependency {
  from: string;
  to: string;
  type: 'import' | 'prop' | 'context' | 'store' | 'hook';
  weight: number; // 依赖强度 1-10
  isCircular?: boolean;
  isOptional?: boolean;
}

export interface ComponentInfo {
  name: string;
  path: string;
  type: 'component' | 'hook' | 'service' | 'store' | 'util';
  size: number; // 代码行数
  complexity: number; // 复杂度评分
  dependencies: ComponentDependency[];
  dependents: string[]; // 依赖此组件的其他组件
}

export interface DependencyGraph {
  components: Map<string, ComponentInfo>;
  edges: ComponentDependency[];
  circularDependencies: ComponentDependency[][];
  metrics: DependencyMetrics;
}

export interface DependencyMetrics {
  totalComponents: number;
  totalDependencies: number;
  averageDependenciesPerComponent: number;
  maxDependencies: number;
  circularDependencyCount: number;
  couplingScore: number; // 0-100，越低越好
  cohesionScore: number; // 0-100，越高越好
}

// ==================== 依赖分析器类 ====================

export class DependencyAnalyzer {
  private graph: DependencyGraph;

  constructor() {
    this.graph = {
      components: new Map(),
      edges: [],
      circularDependencies: [],
      metrics: {
        totalComponents: 0,
        totalDependencies: 0,
        averageDependenciesPerComponent: 0,
        maxDependencies: 0,
        circularDependencyCount: 0,
        couplingScore: 0,
        cohesionScore: 0,
      },
    };
  }

  /**
   * 添加组件信息
   */
  addComponent(component: ComponentInfo): void {
    this.graph.components.set(component.name, component);
    this.graph.edges.push(...component.dependencies);
  }

  /**
   * 分析依赖关系
   */
  analyze(): DependencyGraph {
    this.detectCircularDependencies();
    this.calculateMetrics();
    this.updateDependents();
    return this.graph;
  }

  /**
   * 检测循环依赖
   */
  private detectCircularDependencies(): void {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const cycles: ComponentDependency[][] = [];

    const dfs = (componentName: string, path: ComponentDependency[]): void => {
      if (recursionStack.has(componentName)) {
        // 找到循环依赖
        const cycleStart = path.findIndex(dep => dep.to === componentName);
        if (cycleStart !== -1) {
          const cycle = path.slice(cycleStart);
          cycles.push(cycle);
          // 标记循环依赖中的边
          cycle.forEach(dep => {
            dep.isCircular = true;
          });
        }
        return;
      }

      if (visited.has(componentName)) {
        return;
      }

      visited.add(componentName);
      recursionStack.add(componentName);

      const component = this.graph.components.get(componentName);
      if (component) {
        component.dependencies.forEach(dep => {
          dfs(dep.to, [...path, dep]);
        });
      }

      recursionStack.delete(componentName);
    };

    this.graph.components.forEach((_, componentName) => {
      if (!visited.has(componentName)) {
        dfs(componentName, []);
      }
    });

    this.graph.circularDependencies = cycles;
  }

  /**
   * 计算依赖指标
   */
  private calculateMetrics(): void {
    const components = Array.from(this.graph.components.values());
    const totalComponents = components.length;
    const totalDependencies = this.graph.edges.length;

    // 基本指标
    this.graph.metrics.totalComponents = totalComponents;
    this.graph.metrics.totalDependencies = totalDependencies;
    this.graph.metrics.averageDependenciesPerComponent =
      totalComponents > 0 ? totalDependencies / totalComponents : 0;
    this.graph.metrics.maxDependencies = Math.max(...components.map(c => c.dependencies.length), 0);
    this.graph.metrics.circularDependencyCount = this.graph.circularDependencies.length;

    // 耦合度评分 (0-100，越低越好)
    const maxPossibleDependencies = totalComponents * (totalComponents - 1);
    this.graph.metrics.couplingScore =
      maxPossibleDependencies > 0 ? (totalDependencies / maxPossibleDependencies) * 100 : 0;

    // 内聚度评分 (0-100，越高越好)
    this.graph.metrics.cohesionScore = this.calculateCohesionScore(components);
  }

  /**
   * 计算内聚度评分
   */
  private calculateCohesionScore(components: ComponentInfo[]): number {
    let totalCohesion = 0;

    components.forEach(component => {
      // 基于组件类型和依赖类型计算内聚度
      const internalDependencies = component.dependencies.filter(
        dep => dep.type === 'hook' || dep.type === 'prop'
      ).length;
      const externalDependencies = component.dependencies.filter(
        dep => dep.type === 'import' || dep.type === 'store'
      ).length;

      const cohesion =
        internalDependencies > 0
          ? internalDependencies / (internalDependencies + externalDependencies)
          : 0;

      totalCohesion += cohesion;
    });

    return components.length > 0 ? (totalCohesion / components.length) * 100 : 0;
  }

  /**
   * 更新依赖者信息
   */
  private updateDependents(): void {
    this.graph.components.forEach(component => {
      component.dependents = [];
    });

    this.graph.edges.forEach(edge => {
      const dependent = this.graph.components.get(edge.to);
      if (dependent) {
        dependent.dependents.push(edge.from);
      }
    });
  }

  /**
   * 获取组件的依赖深度
   */
  getDependencyDepth(componentName: string): number {
    const visited = new Set<string>();

    const dfs = (name: string, depth: number): number => {
      if (visited.has(name)) {
        return depth;
      }

      visited.add(name);
      const component = this.graph.components.get(name);

      if (!component || component.dependencies.length === 0) {
        return depth;
      }

      return Math.max(...component.dependencies.map(dep => dfs(dep.to, depth + 1)));
    };

    return dfs(componentName, 0);
  }

  /**
   * 获取高耦合组件
   */
  getHighlyCoupledComponents(threshold = 5): ComponentInfo[] {
    return Array.from(this.graph.components.values())
      .filter(component => component.dependencies.length > threshold)
      .sort((a, b) => b.dependencies.length - a.dependencies.length);
  }

  /**
   * 获取孤立组件
   */
  getIsolatedComponents(): ComponentInfo[] {
    return Array.from(this.graph.components.values()).filter(
      component => component.dependencies.length === 0 && component.dependents.length === 0
    );
  }

  /**
   * 生成依赖关系报告
   */
  generateReport(): string {
    const metrics = this.graph.metrics;
    const highlyCoupled = this.getHighlyCoupledComponents();
    const isolated = this.getIsolatedComponents();

    const report = [
      '# 依赖关系分析报告',
      '',
      '## 总体指标',
      `- 组件总数: ${metrics.totalComponents}`,
      `- 依赖关系总数: ${metrics.totalDependencies}`,
      `- 平均每组件依赖数: ${metrics.averageDependenciesPerComponent.toFixed(2)}`,
      `- 最大依赖数: ${metrics.maxDependencies}`,
      `- 循环依赖数: ${metrics.circularDependencyCount}`,
      `- 耦合度评分: ${metrics.couplingScore.toFixed(2)}/100`,
      `- 内聚度评分: ${metrics.cohesionScore.toFixed(2)}/100`,
      '',
      '## 问题分析',
      '',
    ];

    // 循环依赖
    if (this.graph.circularDependencies.length > 0) {
      report.push('### 🔴 循环依赖');
      this.graph.circularDependencies.forEach((cycle, index) => {
        const cycleStr = cycle.map(dep => `${dep.from} → ${dep.to}`).join(' → ');
        report.push(`${index + 1}. ${cycleStr}`);
      });
      report.push('');
    }

    // 高耦合组件
    if (highlyCoupled.length > 0) {
      report.push('### 🟡 高耦合组件');
      highlyCoupled.slice(0, 10).forEach(component => {
        report.push(`- ${component.name}: ${component.dependencies.length} 个依赖`);
      });
      report.push('');
    }

    // 孤立组件
    if (isolated.length > 0) {
      report.push('### 🟢 孤立组件');
      isolated.forEach(component => {
        report.push(`- ${component.name}: 无依赖关系`);
      });
      report.push('');
    }

    // 优化建议
    report.push('## 优化建议');

    if (metrics.couplingScore > 30) {
      report.push('- 🔧 耦合度过高，建议拆分大组件或使用依赖注入');
    }

    if (metrics.cohesionScore < 60) {
      report.push('- 🔧 内聚度较低，建议重新组织组件职责');
    }

    if (this.graph.circularDependencies.length > 0) {
      report.push('- 🔧 存在循环依赖，需要重构依赖关系');
    }

    return report.join('\n');
  }
}

// ==================== 依赖优化建议生成器 ====================

export class DependencyOptimizer {
  /**
   * 生成依赖优化建议
   */
  static generateOptimizationSuggestions(graph: DependencyGraph): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    // 循环依赖优化
    graph.circularDependencies.forEach(cycle => {
      suggestions.push({
        type: 'break-circular-dependency',
        priority: 'high',
        description: `打破循环依赖: ${cycle.map(d => d.from).join(' → ')}`,
        solution: '考虑使用依赖注入、事件系统或重新设计组件架构',
        affectedComponents: cycle.map(d => d.from),
      });
    });

    // 高耦合组件优化
    const highlyCoupled = Array.from(graph.components.values()).filter(
      c => c.dependencies.length > 8
    );

    highlyCoupled.forEach(component => {
      suggestions.push({
        type: 'reduce-coupling',
        priority: 'medium',
        description: `降低 ${component.name} 的耦合度 (${component.dependencies.length} 个依赖)`,
        solution: '考虑拆分组件、使用组合模式或提取共同依赖',
        affectedComponents: [component.name],
      });
    });

    // 大组件拆分建议
    const largeComponents = Array.from(graph.components.values()).filter(c => c.size > 300);

    largeComponents.forEach(component => {
      suggestions.push({
        type: 'split-large-component',
        priority: 'medium',
        description: `拆分大组件 ${component.name} (${component.size} 行)`,
        solution: '按功能职责拆分为多个小组件',
        affectedComponents: [component.name],
      });
    });

    return suggestions.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }
}

export interface OptimizationSuggestion {
  type:
    | 'break-circular-dependency'
    | 'reduce-coupling'
    | 'split-large-component'
    | 'improve-cohesion';
  priority: 'high' | 'medium' | 'low';
  description: string;
  solution: string;
  affectedComponents: string[];
}

// ==================== 工具函数 ====================

/**
 * 创建组件依赖关系
 */
export function createDependency(
  from: string,
  to: string,
  type: ComponentDependency['type'],
  weight = 1
): ComponentDependency {
  return {
    from,
    to,
    type,
    weight,
    isCircular: false,
    isOptional: false,
  };
}

/**
 * 创建组件信息
 */
export function createComponentInfo(
  name: string,
  path: string,
  type: ComponentInfo['type'],
  size = 0,
  complexity = 1
): ComponentInfo {
  return {
    name,
    path,
    type,
    size,
    complexity,
    dependencies: [],
    dependents: [],
  };
}

/**
 * 分析组件复杂度
 */
export function calculateComplexity(dependencies: ComponentDependency[], size: number): number {
  const dependencyWeight = dependencies.reduce((sum, dep) => sum + dep.weight, 0);
  const sizeWeight = Math.log10(size + 1);
  return Math.min(10, dependencyWeight * 0.3 + sizeWeight * 0.7);
}
