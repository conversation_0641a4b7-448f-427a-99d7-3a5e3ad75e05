import { createCarNumber, generateRandomTime } from '@/core/lib/utils';
import type {
  AdjustmentMaterial,
  DeliveryOrder,
  DeliveryOrderStatus,
  DispatchedVehicle,
  MaterialDensity,
  MaterialName,
  MortarCoefficient,
  Plant,
  RatioHistoryEntry,
  RatioMaterial,
  SiloMapping,
  Specification,
  StorageLocation,
  Task,
  TaskMessage,
  UserPermission,
  Vehicle,
} from '@/core/types';

export const mockPlants: Plant[] = [
  {
    id: 'plant1',
    name: '搅拌站 A',
    stats: { completedTasks: 3, totalTasks: 5 },
    productionLineCount: 2,
  },
  {
    id: 'plant2',
    name: '搅拌站 B',
    stats: { completedTasks: 7, totalTasks: 10 },
    productionLineCount: 3,
  },
  {
    id: 'plant3',
    name: '搅拌站 C',
    stats: { completedTasks: 1, totalTasks: 2 },
    productionLineCount: 1,
  },
  {
    id: 'plant4',
    name: '搅拌站 D',
    stats: { completedTasks: 0, totalTasks: 0 },
    productionLineCount: 2,
  },
];

// ==================== 配比相关数据 ====================
// 注意：以下配比相关数据已迁移到 src/data/ratio-mock-data.ts
// 这里保留是为了向后兼容，建议使用统一的配比数据文件

export const mockMaterialNames: MaterialName[] = [
  { id: 'mat_name_1', name: '饮用水', order: 1, type: 'Water' },
  { id: 'mat_name_2', name: 'P.O 42.5', order: 2, type: 'Powder' },
  { id: 'mat_name_3', name: '机制砂', order: 3, type: 'Aggregate' },
  { id: 'mat_name_4', name: '建业', order: 4, type: 'Admixture' },
];

export const mockStorageLocations: StorageLocation[] = [
  { id: 'loc_1', binId: '骨料1', binName: '1站机制砂仓', order: 1 },
  { id: 'loc_2', binId: '骨料2', binName: '1站尾矿砂仓', order: 2 },
  { id: 'loc_3', binId: '粉料1', binName: '1站1#水泥', order: 3 },
  { id: 'loc_4', binId: '水1', binName: '1站水', order: 4 },
];

export const mockSpecifications: Specification[] = [
  { id: 'spec_1', name: '清洁' },
  { id: 'spec_2', name: '普通硅酸盐' },
  { id: 'spec_3', name: '中砂' },
  { id: 'spec_4', name: '高效减水' },
];

export const mockAdjustmentMaterials: AdjustmentMaterial[] = [
  { id: 'water', name: '水' },
  { id: 'silica_fume', name: '硅灰' },
  { id: 'ultra_fine_sand', name: '超细砂' },
  { id: 'expansion_agent', name: '膨胀剂' },
  { id: 'early_strength_agent', name: '早强剂' },
  { id: 'antifreeze', name: '防冻剂' },
  { id: 'admixture', name: '外加剂' },
  { id: 'slag_powder', name: '矿渣粉' },
  { id: 'ultra_fine_powder', name: '超细粉' },
  { id: 'sand', name: '沙子' },
  { id: 'gravel', name: '石子' },
  { id: 'chips', name: '碎屑' },
  { id: 'tailings', name: '尾矿石' },
  { id: 'gangue', name: '煤矸石' },
  { id: 'cement', name: '水泥' },
  { id: 'mineral_powder', name: '矿粉' },
];

export const mockUserPermissions: UserPermission[] = [
  {
    id: 'user1',
    username: '白彬彬',
    permissions: { calculation: true, design: true, send: true, tweak: true, execute: true },
  },
  {
    id: 'user2',
    username: '李文远',
    permissions: { calculation: true, design: true, send: true, tweak: true, execute: true },
  },
  {
    id: 'user3',
    username: '申谷波',
    permissions: { calculation: true, design: true, send: false, tweak: true, execute: false },
  },
  {
    id: 'user4',
    username: '系统管',
    permissions: { calculation: true, design: false, send: false, tweak: false, execute: false },
  },
  {
    id: 'user5',
    username: '张建军',
    permissions: { calculation: true, design: true, send: true, tweak: true, execute: false },
  },
];

export const mockMortarCoefficients: MortarCoefficient[] = mockAdjustmentMaterials.map(mat => ({
  id: mat.id,
  name: mat.name,
  coefficient: 1.0, // Default coefficient
}));

export const mockMaterialDensities: MaterialDensity[] = [
  { id: 'water', name: '水', density: 1000 },
  { id: 'cement', name: '水泥', density: 3100 },
  { id: 'flyAsh', name: '粉煤灰', density: 2200 },
  { id: 'mineralPowder', name: '矿粉', density: 2900 },
  { id: 'sand', name: '砂子', density: 2650 },
  { id: 'gravel', name: '石子', density: 2700 },
  { id: 'admixture', name: '外加剂', density: 1100 },
  { id: 'silica_fume', name: '硅灰', density: 2200 },
  { id: 'ultra_fine_sand', name: '超细砂', density: 2600 },
  { id: 'expansion_agent', name: '膨胀剂', density: 2800 },
];

const dispatchStatuses: Task['dispatchStatus'][] = [
  'New',
  'ReadyToProduce',
  'RatioSet',
  'InProgress',
  'Paused',
  'Completed',
  'Cancelled',
];
const projectNames = [
  '市中心高楼',
  '跨江大桥',
  '地铁隧道项目',
  '滨海新区开发',
  '科技产业园',
  '国际会展中心',
  '高速公路扩建',
  '体育馆新建',
  '老城区改造',
  '机场T3航站楼',
];
const constructionUnits = [
  '建筑集团A',
  '路桥公司B',
  '市政建设总公司',
  '宏远地产',
  '新兴建设',
  '联合基建',
];
const strengths = ['C25', 'C30', 'C35', 'C40', 'C45', 'C50'];
const pouringMethods = ['泵送', '塔吊', '车载泵', '自卸', '溜槽'];
const deliveryOrderStatuses: DeliveryOrderStatus[] = [
  'newlyDispatched',
  'inProduction',
  'weighed',
  'shipped',
];
const vehicleProductionStatuses: Vehicle['productionStatus'][] = [
  'queued',
  'producing',
  'produced',
  'weighed',
  'ticketed',
  'shipped',
];

// 消息模板
const messageTemplates = [
  {
    content: '请尽快安排车辆，工地急需混凝土',
    type: 'urgent' as const,
    priority: 'urgent' as const,
  },
  {
    content: '今日天气预报有雨，请注意防护措施',
    type: 'warning' as const,
    priority: 'normal' as const,
  },
  { content: '工地现场准备就绪，可以开始浇筑', type: 'info' as const, priority: 'normal' as const },
  {
    content: '混凝土强度是否可以调整为C35？',
    type: 'question' as const,
    priority: 'normal' as const,
  },
  { content: '上午的混凝土质量很好，请保持', type: 'info' as const, priority: 'low' as const },
  { content: '工地入口道路施工，车辆请绕行', type: 'warning' as const, priority: 'high' as const },
  {
    content: '需要增加2车混凝土，原计划不够用',
    type: 'urgent' as const,
    priority: 'high' as const,
  },
  { content: '感谢及时供应，工程进展顺利', type: 'info' as const, priority: 'low' as const },
  {
    content: '明天是否可以提前1小时开始供应？',
    type: 'question' as const,
    priority: 'normal' as const,
  },
  {
    content: '现场检测发现坍落度偏大，请调整',
    type: 'warning' as const,
    priority: 'high' as const,
  },
];

const senderNames = ['张工', '李主管', '王队长', '陈工程师', '刘现场', '赵监理', '孙工', '周主任'];

// 生成任务消息
function generateTaskMessages(taskId: string, messageCount: number): TaskMessage[] {
  const messages: TaskMessage[] = [];

  for (let i = 0; i < messageCount; i++) {
    // 使用索引而不是随机数，确保服务器端和客户端一致
    const templateIndex = i % messageTemplates.length;
    const senderIndex = i % senderNames.length;
    const template = messageTemplates[templateIndex];
    const sender = senderNames[senderIndex];

    // 确保 template 和 sender 存在
    if (!template || !sender) {
      continue;
    }

    // 使用固定的时间偏移，避免随机
    const hoursAgo = (i % 48) + 1; // 1-48小时前
    const baseTime = new Date(2024, 0, 14, 10, 0, 0).getTime();
    const timestamp = new Date(baseTime - hoursAgo * 60 * 60 * 1000).toISOString();

    messages.push({
      id: `msg_${taskId}_${i + 1}`,
      taskId,
      content: template.content,
      sender,
      timestamp,
      isRead: i % 5 !== 0, // 每5个消息中有1个未读
      priority: template.priority,
      type: template.type,
    });
  }

  return messages.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
}

export function generateMockTasks(p0?: number): Task[] {
  const generatedTasks: Task[] = [];
  const stressTestTaskCount = 500;
  const stressTestTaskStartIndex = 0;

  for (let i = 0; i < stressTestTaskCount; i++) {
    const taskIndex = stressTestTaskStartIndex + i;
    const projectName = projectNames[i % projectNames.length] ?? '默认项目';
    const requiredVol = ((i * 7) % 200) + 50;

    let dispatchFrequencyMinutes: number;
    let countdownMs: number;
    let isDueForDispatch = false;
    let taskStatus: Task['dispatchStatus'] = 'InProgress';
    let taskScenario = '';

    if (i < 20) {
      dispatchFrequencyMinutes = [5, 15, 30][i % 3] ?? 15;
      countdownMs = -((i % 10) + 1) * 60000;
      isDueForDispatch = true;
      taskScenario = '已超时';
    } else if (i < 40) {
      dispatchFrequencyMinutes = [5, 15, 30][i % 3] ?? 15;
      countdownMs = (i % 60) * 1000; // 0-59秒
      isDueForDispatch = true;
      taskScenario = '即将发车(<1分钟)';
    } else if (i < 60) {
      dispatchFrequencyMinutes = [15, 30, 45][i % 3] ?? 30;
      countdownMs = (1 + (i % 4)) * 60000 + ((i * 13) % 59) * 1000;
      isDueForDispatch = true;
      taskScenario = '紧急提醒(1-5分钟)';
    } else if (i < 80) {
      dispatchFrequencyMinutes = [30, 45, 60][i % 3] ?? 45;
      countdownMs = (5 + (i % 10)) * 60000 + ((i * 17) % 59) * 1000;
      isDueForDispatch = false;
      taskScenario = '普通提醒(5-15分钟)';
    } else if (i < 100) {
      dispatchFrequencyMinutes = [45, 60, 90][i % 3] ?? 60;
      countdownMs = (15 + (i % 15)) * 60000 + ((i * 19) % 59) * 1000;
      isDueForDispatch = false;
      taskScenario = '提前提醒(15-30分钟)';
    } else if (i < 120) {
      dispatchFrequencyMinutes = [60, 90, 120][i % 3] ?? 90;
      countdownMs = (30 + (i % 30)) * 60000 + ((i * 23) % 59) * 1000;
      isDueForDispatch = false;
      taskScenario = '长时间任务(30-60分钟)';
    } else if (i < 140) {
      dispatchFrequencyMinutes = [90, 120, 180][i % 3] ?? 120;
      countdownMs = (60 + (i % 60)) * 60000 + ((i * 29) % 59) * 1000;
      isDueForDispatch = false;
      taskScenario = '超长时间任务(60-120分钟)';
    } else if (i < 160) {
      dispatchFrequencyMinutes = [30, 60, 90][i % 3] ?? 60;
      countdownMs = (i % 60) * 60000 + ((i * 31) % 59) * 1000;
      isDueForDispatch = false;
      taskStatus = 'Paused';
      taskScenario = '暂停任务';
    } else if (i < 180) {
      dispatchFrequencyMinutes = [30, 60, 90][i % 3] ?? 60;
      countdownMs = (i % 60) * 60000 + ((i * 37) % 59) * 1000;
      isDueForDispatch = false;
      taskStatus = 'Completed';
      taskScenario = '已完成任务';
    } else if (i < 200) {
      dispatchFrequencyMinutes = [30, 60, 90][i % 3] ?? 60;
      countdownMs = (i % 60) * 60000 + ((i * 41) % 59) * 1000;
      isDueForDispatch = false;
      taskStatus = 'Cancelled';
      taskScenario = '已取消任务';
    } else if (i < 220) {
      dispatchFrequencyMinutes = [30, 60, 90][i % 3] ?? 60;
      countdownMs = (i % 120) * 60000 + ((i * 43) % 59) * 1000;
      isDueForDispatch = false;
      taskStatus = 'ReadyToProduce';
      taskScenario = '计划发车任务';
    } else if (i < 240) {
      dispatchFrequencyMinutes = [2, 3, 4][i % 3] ?? 3;
      countdownMs = (i % 2) * 60000 + ((i * 47) % 59) * 1000;
      isDueForDispatch = countdownMs < 60000;
      taskScenario = '多频次发车任务';
    } else if (i < 260) {
      dispatchFrequencyMinutes = [180, 240, 300][i % 3] ?? 240;
      countdownMs = (i % 120) * 60000 + ((i * 53) % 59) * 1000;
      isDueForDispatch = countdownMs < 300000;
      taskScenario = '低频发车任务';
    } else {
      dispatchFrequencyMinutes = [5, 15, 30, 45, 60, 90, 120][i % 7] ?? 30;
      countdownMs = (i % 120) * 60000 + ((i * 59) % 59) * 1000;
      isDueForDispatch = countdownMs < 5 * 60000;
      taskScenario = '常规任务';
    }

    // 使用固定的基准时间避免 hydration 错误
    const baseTime = new Date(2024, 0, 15, 10, 0, 0).getTime();
    const nextScheduledTime = baseTime + countdownMs;
    const lastDispatch = new Date(
      nextScheduledTime - dispatchFrequencyMinutes * 60 * 1000
    ).toISOString();
    const nextScheduledTimeStr = new Date(nextScheduledTime).toISOString();

    const minutesToDispatch = Math.floor(countdownMs / 60000);

    // 添加消息信息 - 前30个任务有消息
    const hasMessages = i < 30;
    const messageCount = hasMessages ? (i % 8) + 1 : 0; // 1-8条消息，使用索引避免随机
    const messages = hasMessages
      ? generateTaskMessages(`task_stress_p1_${i + 1}`, messageCount)
      : [];
    const unreadCount = messages.filter(msg => !msg.isRead).length;

    generatedTasks.push({
      id: `task_stress_p1_${i + 1}`,
      plantId: 'plant1',
      deliveryStatus: 'pending',
      dispatchReminderMinutes: 5,
      freezeResistance: 'F0', // 添加抗冻等级
      impermeability: 'P6', // 添加抗渗等级

      taskNumber: `S1${String(taskIndex).padStart(4, '0')}`,
      projectName: `${taskScenario}-${projectName.substring(0, 4)} #${i + 1}`,
      projectAbbreviation: `测${i + 1}`,
      constructionUnit: constructionUnits[i % constructionUnits.length] ?? '默认施工单位',
      constructionSite: `测试工地${(i % 10) + 1}`,
      constructionLocation: `主楼${(i % 20) + 1}层`,
      customerName: `客户${(i % 50) + 1}`,
      strength: strengths[i % strengths.length] ?? 'C30',
      pouringMethod: pouringMethods[i % pouringMethods.length] ?? '泵送',
      vehicleCount: (i % 5) + 1,
      completedVolume: Math.floor((requiredVol * 0.2 * (i % 10)) / 10),
      requiredVolume: requiredVol,
      pumpTruck: '无',
      otherRequirements: `${taskScenario}：测试发车提醒功能，频率${dispatchFrequencyMinutes}分钟，倒计时${minutesToDispatch}分钟`,
      contactPhone: `132${String(Math.floor((i + 1) * 12345678))
        .toString()
        .slice(-8)
        .padStart(8, '0')}`,
      supplyTime: `${String(8 + (i % 12)).padStart(2, '0')}:${String((i * 15) % 60).padStart(2, '0')}`,
      supplyDate: '2024-01-15',
      publishDate: '2024-01-14',
      createdAt: new Date(2024, 0, 14, 10, i * 5).toISOString(),
      updatedAt: new Date(2024, 0, 14, 15, i * 3).toISOString(),
      dispatchStatus: taskStatus,
      status: taskStatus, // Add required status field
      isTicketed: true,
      dispatchFrequencyMinutes: dispatchFrequencyMinutes,
      lastDispatchTime: lastDispatch,
      nextScheduledDispatchTime: nextScheduledTimeStr,
      isDueForDispatch: isDueForDispatch,
      minutesToDispatch: minutesToDispatch,
      productionLineCount: mockPlants.find(p => p.id === 'plant1')?.productionLineCount || 2,
      // 消息相关字段
      hasNewMessages: hasMessages && unreadCount > 0, // 只有当有消息且有未读消息时才为true
      unreadMessageCount: unreadCount,
      messages: messages,
      vehicles: undefined,
      scheduledDate: undefined,
      concreteVolume: 0,
      contractNumber: undefined,

      // 发货单相关字段
      deliveryOrderNumber: `C125-${String(taskIndex).padStart(6, '0')}`,
      tareWeight: 15.28 + (i % 5) * 0.1, // 皮重 15.28-15.68
      grossWeight: 35.5 + (i % 10) * 0.5, // 毛重 35.5-40.0
      netWeight: 20.22 + (i % 8) * 0.3, // 净重 20.22-22.32
      mainOperator: ['张操作员', '李操作员', '王操作员'][i % 3],
      siteDispatcher: ['现场调度A', '现场调度B', '现场调度C'][i % 3],
      qualityInspector: ['质检员甲', '质检员乙', '质检员丙'][i % 3],
      weigher: ['司磅员1', '司磅员2', '司磅员3'][i % 3],
      dispatcher: ['调度员A', '调度员B', '调度员C'][i % 3],
      driverName: ['张师傅', '李师傅', '王师傅', '刘师傅'][i % 4],
      departureTime: new Date(baseTime + i * 30 * 60 * 1000).toISOString(),
      mixingStation: ['搅拌站1', '搅拌站2'][i % 2],
      slumpRange: ['180±20', '160±20', '200±20'][i % 3],
      notes: i % 5 === 0 ? `任务${i + 1}的特殊说明` : '',
    });
  }
  return generatedTasks;
}

export const mockMaterials = [
  {
    id: 'mat1',
    materialType: '水',
    name: '饮用水',
    spec: '清洁',
    storageBin: '1站水',
    station: '搅拌站1',
    category: 'water',
    density: 1000,
    stock: 10000,
  },
  {
    id: 'mat2',
    materialType: '水',
    name: '污水',
    spec: '处理后',
    storageBin: '1站污水',
    station: '搅拌站1',
    category: 'water',
    density: 1000,
    stock: 0,
  },
  {
    id: 'mat3',
    materialType: '水泥',
    name: 'P.O 42.5',
    spec: '普通硅酸盐',
    storageBin: '1站1#水泥',
    station: '搅拌站1',
    category: 'cement',
    density: 3100,
    stock: 5000,
  },
  {
    id: 'mat4',
    materialType: '砂子',
    name: '机制砂',
    spec: '中砂',
    storageBin: '1站机制砂仓',
    station: '搅拌站1',
    category: 'sand',
    density: 2650,
    stock: 3000,
  },
  {
    id: 'mat5',
    materialType: '砂子',
    name: '尾矿砂',
    spec: '细砂',
    storageBin: '1站尾矿砂仓',
    station: '搅拌站1',
    category: 'sand',
    density: 2650,
    stock: 2000,
  },
  {
    id: 'mat6',
    materialType: '石子',
    name: '1-2',
    spec: '碎石',
    storageBin: '1站1-2石子',
    station: '搅拌站1',
    category: 'stone',
    density: 2700,
    stock: 4000,
  },
  {
    id: 'mat7',
    materialType: '石子',
    name: '细石',
    spec: '碎石',
    storageBin: '1站细石仓',
    station: '搅拌站1',
    category: 'stone',
    density: 2700,
    stock: 3000,
  },
  {
    id: 'mat8',
    materialType: '粉煤灰',
    name: 'II级',
    spec: '无',
    storageBin: '1站1#煤灰',
    station: '搅拌站1',
    category: 'flyash',
    density: 2200,
    stock: 1500,
  },
  {
    id: 'mat9',
    materialType: '矿粉',
    name: 'S95',
    spec: '1-2',
    storageBin: '1站1#矿粉',
    station: '搅拌站1',
    category: 'mineralPowder',
    density: 2900,
    stock: 1000,
  },
  {
    id: 'mat10',
    materialType: '外加剂',
    name: '建业',
    spec: '高效减水',
    storageBin: '1站1#外加剂',
    station: '搅拌站1',
    category: 'admixture',
    density: 1100,
    stock: 500,
  },
  {
    id: 'mat11',
    materialType: '外加剂',
    name: '砂胶',
    spec: '保水',
    storageBin: '1站2#外加剂',
    station: '搅拌站1',
    category: 'admixture',
    density: 1100,
    stock: 300,
  },
  {
    id: 'mat12',
    materialType: '超细粉',
    name: '无',
    spec: '无',
    storageBin: '1站超细粉',
    station: '搅拌站1',
    category: 'ultraFinePowder',
    density: 2900,
    stock: 0,
  },
  {
    id: 'mat13',
    materialType: '水',
    name: '污水',
    spec: '处理后',
    storageBin: '2站污水',
    station: '搅拌站2',
    category: 'water',
    density: 1000,
    stock: 0,
  },
  {
    id: 'mat14',
    materialType: '水泥',
    name: 'P.O 52.5',
    spec: '高强度',
    storageBin: '2站1#水泥',
    station: '搅拌站2',
    category: 'cement',
    density: 3100,
    stock: 2000,
  },
];

export const initialTableData: RatioMaterial[] = [
  {
    id: '1',
    materialType: '水泥',
    spec: 'P.O 42.5',
    theoryAmount: 372,
    waterRatio: 0,
    stoneRatio: 0,
    actualAmount: 372,
    designValue: 372,
    binName: '1#水泥仓',
  },
  {
    id: '2',
    materialType: '水',
    spec: '饮用水',
    theoryAmount: 175,
    waterRatio: 0,
    stoneRatio: 0,
    actualAmount: 175,
    designValue: 175,
    binName: '水仓',
  },
  {
    id: '3',
    materialType: '砂子',
    spec: '机制砂',
    theoryAmount: 593,
    waterRatio: 2.5,
    stoneRatio: 0,
    actualAmount: 593 * 1.025,
    designValue: 593,
    binName: '砂仓1',
  },
  {
    id: '4',
    materialType: '石子',
    spec: '1-2碎石',
    theoryAmount: 1260,
    waterRatio: 1.0,
    stoneRatio: 0,
    actualAmount: 1260 * 1.01,
    designValue: 1260,
    binName: '石仓1',
  },
  {
    id: '5',
    materialType: '外加剂',
    spec: '高效减水',
    theoryAmount: 3.72,
    waterRatio: 0,
    stoneRatio: 0,
    actualAmount: 3.72,
    designValue: 3.72,
    binName: '剂仓1',
  },
];

export function generateMockSiloMappings(): SiloMapping[] {
  return [
    {
      id: 'silo1',
      siloType: '骨料1',
      materialName: '砂子',
      spec: '机制砂',
      storageBin: '1站机制砂仓',
      lastEntry: null,
      enabled: true,
    },
    {
      id: 'silo2',
      siloType: '骨料2',
      materialName: '砂子',
      spec: '尾矿砂',
      storageBin: '1站尾矿砂仓',
      lastEntry: null,
      enabled: true,
    },
    {
      id: 'silo3',
      siloType: '粉料1',
      materialName: '水泥',
      spec: 'P.O 42.5',
      storageBin: '1站1#水泥',
      lastEntry: '2021-10-22 03:31:24 品名: 水泥 规格: 金隅',
      enabled: true,
    },
    {
      id: 'silo4',
      siloType: '水1',
      materialName: '水',
      spec: '饮用水',
      storageBin: '1站水',
      lastEntry: null,
      enabled: true,
    },
    {
      id: 'silo5',
      siloType: '外加剂1',
      materialName: '外加剂',
      spec: '建业',
      storageBin: '1站1#外加剂',
      lastEntry: '2021-10-19 14:53:39 品名: 外加剂 规格: 彩',
      enabled: true,
    },
  ];
}

export function generateMockRatioHistory(taskId: string): RatioHistoryEntry[] {
  return [
    {
      id: 'hist1',
      taskId,
      editor: '李文远',
      timestamp: '2025-04-11 09:56:53',
      price: 450.0,
      materials: {
        cement: 425,
        flyAsh: 0,
        mineralPowder: 0,
        sand: 583,
        stone: 1240,
        water: 175,
        admixture: 4.25,
      },
    },
    {
      id: 'hist2',
      taskId,
      editor: '白彬彬',
      timestamp: '2025-04-09 13:53:09',
      price: 445.5,
      materials: {
        cement: 420,
        flyAsh: 10,
        mineralPowder: 5,
        sand: 590,
        stone: 1230,
        water: 180,
        admixture: 4.2,
      },
    },
  ];
}

export function generateMockDeliveryOrders(): DeliveryOrder[] {
  const generatedDeliveryOrders: DeliveryOrder[] = [];
  const tempTasks = generateMockTasks();
  let orderIdCounter = 1;

  mockPlants.forEach(plant => {
    for (let lineIndex = 0; lineIndex < plant.productionLineCount; lineIndex++) {
      const productionLineId = `L${lineIndex + 1}`;
      const ordersPerLine = ((lineIndex * 3) % 9) + 2;

      const plantTasks = tempTasks.filter(t => t.plantId === plant.id);
      if (plantTasks.length === 0) continue;

      for (let i = 0; i < ordersPerLine; i++) {
        const task = plantTasks[i % plantTasks.length];

        if (!task) continue;

        generatedDeliveryOrders.push({
          id: `do_gen_${plant.id}_${productionLineId}_${orderIdCounter++}`,
          plantId: plant.id,
          productionLineId: productionLineId,
          vehicleNumber: createCarNumber(),
          driver:
            ['刘一', '陈二', '张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十'][
              i % 10
            ] ?? '默认司机',
          volume: (i % 5) + 5,
          strength: task.strength,
          projectName: task.projectName,
          taskNumber: task.taskNumber,
          status: (deliveryOrderStatuses[i % deliveryOrderStatuses.length] as any) ?? 'pending',
        });
      }
    }
  });

  return generatedDeliveryOrders;
}

export function generateMockVehicles(): Vehicle[] {
  const generatedVehicles: Vehicle[] = [];
  const statusOptions: Vehicle['status'][] = ['pending', 'returned', 'outbound'];
  const operationalStatusOptions: NonNullable<Vehicle['operationalStatus']>[] = [
    'normal',
    'paused',
    'deactivated',
  ];
  const tripTypeOptions: NonNullable<Vehicle['currentTripType']>[] = ['outboundLeg', 'returnLeg'];
  const plantIds = mockPlants.map(p => p.id);

  for (let i = 1; i <= 200; i++) {
    const baseStatus = statusOptions[i % statusOptions.length] ?? 'pending';
    let opStatus: Vehicle['operationalStatus'] = 'normal';
    let tripType: Vehicle['currentTripType'] = undefined;
    let prodStatus: Vehicle['productionStatus'] = undefined;
    let allowEdit = i % 10 < 3; // 30% 可编辑
    let deliveryId: string | undefined = undefined;
    let currentPlantId = plantIds[i % plantIds.length];
    let assignedTaskId: string | undefined = undefined;
    let assignedProductionLineId: string | undefined = undefined;
    //上车是否水票 ，如果是 则重点显示，以方便调度提醒司机放水
    let lastTripWashedWithPumpWater = i % 20 === 0; // 5% 概率

    if (baseStatus === 'pending' || baseStatus === 'returned') {
      const rand = i % 10;
      if (rand < 1) opStatus = 'paused';
      else if (rand < 2) opStatus = 'deactivated';
      prodStatus = 'queued';
    } else if (baseStatus === 'outbound') {
      tripType = tripTypeOptions[i % tripTypeOptions.length];
      prodStatus = vehicleProductionStatuses[i % vehicleProductionStatuses.length];
      deliveryId = `do-v${i}`;

      const tasksForPlant = generateMockTasks().filter(
        t => t.plantId === currentPlantId && t.dispatchStatus === 'InProgress'
      );
      if (tasksForPlant.length > 0) {
        const randomTask = tasksForPlant[i % tasksForPlant.length];
        if (randomTask) {
          assignedTaskId = randomTask.id;
        }
        const plantDetails = mockPlants.find(p => p.id === currentPlantId);
        if (plantDetails && plantDetails.productionLineCount > 0) {
          assignedProductionLineId = `L${(i % plantDetails.productionLineCount) + 1}`;
        }
      }
    }

    generatedVehicles.push({
      id: i < 10 ? `00${i}` : i < 100 ? `0${i}` : `${i}`,
      isDragging: true,
      vehicleNumber: createCarNumber(),
      status: baseStatus,
      type: 'Tanker',
      operationalStatus: opStatus,
      assignedTaskId: assignedTaskId,
      assignedProductionLineId: assignedProductionLineId,
      currentTripType: tripType,
      productionStatus: prodStatus,
      allowWeighRoomEdit: allowEdit,
      deliveryOrderId: deliveryId,
      plantId: currentPlantId,
      lastTripWashedWithPumpWater: lastTripWashedWithPumpWater,
    });
  }
  return generatedVehicles;
}

/**
 * 生成已出厂车辆模拟数据 - 基于原有Vehicle数据
 */
export function generateDispatchedVehicles(count: number = 20): DispatchedVehicle[] {
  const vehicles: DispatchedVehicle[] = [];
  const mockVehicles = generateMockVehicles();
  const outboundVehicles = mockVehicles.filter(v => v.status === 'outbound');

  // 司机名称池
  const driverNames = [
    '张师傅',
    '李师傅',
    '王师傅',
    '刘师傅',
    '陈师傅',
    '杨师傅',
    '赵师傅',
    '黄师傅',
    '周师傅',
    '吴师傅',
    '徐师傅',
    '孙师傅',
    '胡师傅',
    '朱师傅',
    '高师傅',
    '林师傅',
    '何师傅',
    '郭师傅',
    '马师傅',
    '罗师傅',
    '梁师傅',
    '宋师傅',
    '郑师傅',
    '谢师傅',
  ];

  for (let i = 0; i < Math.min(count, outboundVehicles.length); i++) {
    const vehicle = outboundVehicles[i];
    if (!vehicle) continue;

    const driver = driverNames[i % driverNames.length] || `司机${i + 1}`;

    // 生成时间数据
    const baseTime = new Date();
    baseTime.setHours(8, 0, 0, 0); // 从早上8点开始

    const departureTime = new Date(baseTime.getTime() + i * 30 * 60 * 1000); // 每30分钟一辆车
    const outboundDuration = 30 + Math.floor(Math.random() * 60); // 30-90分钟
    const siteStayDuration = 45 + Math.floor(Math.random() * 90); // 45-135分钟
    const returnDuration = 25 + Math.floor(Math.random() * 50); // 25-75分钟

    const arrivalTime = new Date(departureTime.getTime() + outboundDuration * 60 * 1000);
    const returnTime = new Date(arrivalTime.getTime() + siteStayDuration * 60 * 1000);

    // 根据currentTripType确定是往程还是返程
    const isRoundTrip = vehicle.currentTripType === 'returnLeg';
    const totalDuration = isRoundTrip
      ? outboundDuration + siteStayDuration + returnDuration
      : outboundDuration + siteStayDuration;

    vehicles.push({
      id: vehicle.id,
      vehicleNumber: vehicle.id, // 使用内部编号（3位数字）
      driver,
      departureTime: departureTime.toISOString(),
      arrivalTime: arrivalTime.toISOString(),
      returnTime: isRoundTrip ? returnTime.toISOString() : undefined,
      outboundDuration,
      siteStayDuration,
      returnDuration: isRoundTrip ? returnDuration : undefined,
      totalDuration,
      deliveryOrderNumber: vehicle.deliveryOrderId || `DO${vehicle.id}`,
      isRoundTrip,
      vehicleType: vehicle.type,
      projectName: `项目${String.fromCharCode(65 + (i % 10))}`,
      taskNumber: vehicle.assignedTaskId || `T${vehicle.id}`,
      notes: Math.random() > 0.7 ? `备注信息${i + 1}` : undefined,
    });
  }

  return vehicles;
}

export const taskStatusOptions = [
  { value: 'ReadyToProduce', label: '准备生产' },
  { value: 'RatioSet', label: '已设定配比' },
  { value: 'InProgress', label: '正在进行' },
  { value: 'Paused', label: '暂停' },
  { value: 'Completed', label: '已完成' },
  { value: 'Cancelled', label: '已撤销' },
];

export const productTypeOptions = [
  { value: 'All', label: '全部产品' },
  { value: 'Concrete', label: '砼' },
  { value: 'Mortar', label: '砂浆' },
];

export const defaultVolumeOptions = [
  { value: 'Max', label: '最大方数' },
  { value: 'NoOverload', label: '不超载' },
  { value: 'Normal', label: '正常' },
];
