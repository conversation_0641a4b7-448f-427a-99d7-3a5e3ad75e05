/**
 * 时间工具函数
 * 提供时间格式化、计算和处理功能
 */

/**
 * 格式化时间为字符串
 * @param time - 时间输入（Date对象或ISO字符串）
 * @param format - 格式字符串，默认为 'HH:mm'
 * @returns 格式化后的时间字符串
 */
export function formatTime(
  time: Date | string | null | undefined,
  format: string = 'HH:mm'
): string {
  if (!time) return '--:--';

  try {
    const date = typeof time === 'string' ? new Date(time) : time;
    if (isNaN(date.getTime())) return '--:--';

    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');

    return format.replace('HH', hours).replace('mm', minutes).replace('ss', seconds);
  } catch {
    return '--:--';
  }
}

/**
 * 格式化持续时间
 * @param minutes - 分钟数
 * @param short - 是否使用简短格式
 * @returns 格式化后的持续时间字符串
 */
export function formatDuration(minutes: number, short: boolean = false): string {
  if (minutes < 0) return '0分钟';

  if (minutes < 1) {
    const seconds = Math.round(minutes * 60);
    return short ? `${seconds}s` : `${seconds}秒`;
  }

  const days = Math.floor(minutes / 1440);
  const hours = Math.floor((minutes % 1440) / 60);
  const mins = Math.floor(minutes % 60);

  if (short) {
    if (days > 0) return `${days}d${hours > 0 ? ` ${hours}h` : ''}`;
    if (hours > 0) return `${hours}h${mins > 0 ? ` ${mins}m` : ''}`;
    return `${mins}m`;
  }

  if (days > 0) {
    return `${days}天${hours > 0 ? `${hours}小时` : ''}`;
  }
  if (hours > 0) {
    return `${hours}小时${mins > 0 ? `${mins}分钟` : ''}`;
  }
  return `${mins}分钟`;
}

/**
 * 检查时间是否过期
 * @param time - 时间输入
 * @param bufferMinutes - 缓冲时间（分钟）
 * @returns 是否过期
 */
export function isOverdue(
  time: string | Date | null | undefined,
  bufferMinutes: number = 0
): boolean {
  if (!time) return false;

  try {
    const date = typeof time === 'string' ? new Date(time) : time;
    if (isNaN(date.getTime())) return false;

    const now = new Date();
    const bufferTime = new Date(date.getTime() + bufferMinutes * 60 * 1000);

    return now > bufferTime;
  } catch {
    return false;
  }
}

/**
 * 获取剩余时间
 * @param time - 目标时间
 * @returns 剩余时间信息
 */
export function getTimeRemaining(time: string | Date | null | undefined) {
  const defaultResult = {
    hours: 0,
    minutes: 0,
    total: 0,
    isOverdue: false,
  };

  if (!time) return defaultResult;

  try {
    const date = typeof time === 'string' ? new Date(time) : time;
    if (isNaN(date.getTime())) return defaultResult;

    const now = new Date();
    const diffMs = date.getTime() - now.getTime();

    if (diffMs <= 0) {
      return { ...defaultResult, isOverdue: true };
    }

    const totalMinutes = Math.floor(diffMs / (1000 * 60));
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    return {
      hours,
      minutes,
      total: totalMinutes,
      isOverdue: false,
    };
  } catch {
    return defaultResult;
  }
}

/**
 * 解析时间字符串
 * @param timeString - 时间字符串（HH:mm格式）
 * @returns 解析后的时间对象
 */
export function parseTimeString(timeString: string): { hours: number; minutes: number } {
  const defaultResult = { hours: 0, minutes: 0 };

  if (!timeString || typeof timeString !== 'string') {
    return defaultResult;
  }

  const match = timeString.match(/^(\d{1,2}):(\d{2})$/);
  if (!match || !match[1] || !match[2]) return defaultResult;

  const hours = parseInt(match[1], 10);
  const minutes = parseInt(match[2], 10);

  if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
    return defaultResult;
  }

  return { hours, minutes };
}

/**
 * 添加分钟到日期
 * @param date - 基础日期
 * @param minutes - 要添加的分钟数
 * @returns 新的日期对象
 */
export function addMinutes(date: Date, minutes: number): Date {
  const result = new Date(date);
  result.setMinutes(result.getMinutes() + minutes);
  return result;
}

/**
 * 从日期减去分钟
 * @param date - 基础日期
 * @param minutes - 要减去的分钟数
 * @returns 新的日期对象
 */
export function subtractMinutes(date: Date, minutes: number): Date {
  const result = new Date(date);
  result.setMinutes(result.getMinutes() - minutes);
  return result;
}

/**
 * 检查两个日期是否是同一天
 * @param date1 - 第一个日期
 * @param date2 - 第二个日期
 * @returns 是否是同一天
 */
export function isSameDay(date1: Date | string, date2: Date | string): boolean {
  try {
    const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
    const d2 = typeof date2 === 'string' ? new Date(date2) : date2;

    return (
      d1.getFullYear() === d2.getFullYear() &&
      d1.getMonth() === d2.getMonth() &&
      d1.getDate() === d2.getDate()
    );
  } catch {
    return false;
  }
}

/**
 * 格式化相对时间
 * @param time - 时间输入
 * @returns 相对时间字符串
 */
export function formatRelativeTime(time: Date | string): string {
  try {
    const date = typeof time === 'string' ? new Date(time) : time;
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    const absDiffMs = Math.abs(diffMs);

    // 小于1分钟
    if (absDiffMs < 60 * 1000) {
      return '刚刚';
    }

    // 分钟级别
    const minutes = Math.floor(absDiffMs / (60 * 1000));
    if (minutes < 60) {
      return diffMs > 0 ? `${minutes}分钟后` : `${minutes}分钟前`;
    }

    // 小时级别
    const hours = Math.floor(absDiffMs / (60 * 60 * 1000));
    if (hours < 24) {
      return diffMs > 0 ? `${hours}小时后` : `${hours}小时前`;
    }

    // 天级别
    const days = Math.floor(absDiffMs / (24 * 60 * 60 * 1000));
    return diffMs > 0 ? `${days}天后` : `${days}天前`;
  } catch {
    return '未知时间';
  }
}
