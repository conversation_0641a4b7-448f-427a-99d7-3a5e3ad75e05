'use client';

import React, { useState } from 'react';
import { Truck } from 'lucide-react';

import { EditableComboBox } from '@/shared/components/editable-combo-box';
import { Button } from '@/shared/components/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Label } from '@/shared/components/label';
import { useToast } from '@/shared/hooks/use-toast';

interface VehicleManagementModalProps {
  open?: boolean;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onOpenChangeAction?: (open: boolean) => void;
  onConfirm?: (data: OtherVehicleExitForm) => Promise<void> | void;
}

// 其他车辆出厂表单数据接口
interface OtherVehicleExitForm {
  vehicleNumber: string;
  driverName: string;
  reason: string;
}

// 模拟数据选项
const MOCK_VEHICLE_OPTIONS = ['苏A12345', '苏B67890', '苏C11111', '苏D22222'];
const MOCK_DRIVER_OPTIONS = ['张师傅', '李师傅', '王师傅', '赵师傅', '陈师傅'];
const MOCK_REASON_OPTIONS = ['维修保养', '加油', '年检', '临时调用', '其他'];

const VehicleManagementModal: React.FC<VehicleManagementModalProps> = ({
  open = false,
  isOpen = false,
  onOpenChange,
  onOpenChangeAction,
  onConfirm,
}) => {
  const { toast } = useToast();

  // 统一处理open状态和回调
  const modalOpen = (open || isOpen) ?? false;
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange?.(newOpen);
    onOpenChangeAction?.(newOpen);
  };

  // 表单状态
  const [formData, setFormData] = useState<OtherVehicleExitForm>({
    vehicleNumber: '',
    driverName: '',
    reason: '',
  });

  // 选项状态
  const [vehicleOptions, setVehicleOptions] = useState<string[]>(MOCK_VEHICLE_OPTIONS);
  const [driverOptions, setDriverOptions] = useState<string[]>(MOCK_DRIVER_OPTIONS);
  const [reasonOptions, setReasonOptions] = useState<string[]>(MOCK_REASON_OPTIONS);

  // 重置表单
  const resetForm = () => {
    setFormData({
      vehicleNumber: '',
      driverName: '',
      reason: '',
    });
  };

  // 处理表单字段变化
  const handleFieldChange = (field: keyof OtherVehicleExitForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // 处理选项添加
  const handleAddOption = (field: 'vehicle' | 'driver' | 'reason', option: string) => {
    switch (field) {
      case 'vehicle':
        setVehicleOptions(prev => [...prev, option]);
        break;
      case 'driver':
        setDriverOptions(prev => [...prev, option]);
        break;
      case 'reason':
        setReasonOptions(prev => [...prev, option]);
        break;
    }
  };

  // 处理选项删除
  const handleRemoveOption = (field: 'vehicle' | 'driver' | 'reason', option: string) => {
    switch (field) {
      case 'vehicle':
        setVehicleOptions(prev => prev.filter(item => item !== option));
        break;
      case 'driver':
        setDriverOptions(prev => prev.filter(item => item !== option));
        break;
      case 'reason':
        setReasonOptions(prev => prev.filter(item => item !== option));
        break;
    }
  };

  // 处理取消
  const handleCancel = () => {
    resetForm();
    handleOpenChange(false);
  };

  // 处理确定
  const handleConfirm = async () => {
    // 验证表单
    if (!formData.vehicleNumber.trim()) {
      toast({
        title: '验证失败',
        description: '请输入车号',
        variant: 'destructive',
      });
      return;
    }

    if (!formData.driverName.trim()) {
      toast({
        title: '验证失败',
        description: '请输入司机姓名',
        variant: 'destructive',
      });
      return;
    }

    if (!formData.reason.trim()) {
      toast({
        title: '验证失败',
        description: '请输入出厂原因',
        variant: 'destructive',
      });
      return;
    }

    try {
      // 调用外部确认回调
      if (onConfirm) {
        await onConfirm(formData);
      } else {
        // 默认处理逻辑
        console.log('其他车辆出厂数据:', formData);
        toast({
          title: '操作成功',
          description: `车辆 ${formData.vehicleNumber} 已登记出厂`,
        });
      }

      // 重置表单并关闭模态框
      resetForm();
      handleOpenChange(false);
    } catch (error) {
      console.error('其他车辆出厂失败:', error);
      toast({
        title: '操作失败',
        description: '车辆出厂登记失败，请重试',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Truck className='h-5 w-5 text-blue-600' />
            其他车辆出厂
          </DialogTitle>
        </DialogHeader>

        <div className='space-y-4 py-4'>
          {/* 车号字段 */}
          <div className='space-y-2'>
            <Label className='text-sm font-medium'>车号</Label>
            <EditableComboBox
              value={formData.vehicleNumber}
              onValueChange={value => handleFieldChange('vehicleNumber', value)}
              options={vehicleOptions}
              onAddOption={option => handleAddOption('vehicle', option)}
              onRemoveOption={option => handleRemoveOption('vehicle', option)}
              placeholder='请输入或选择车号'
              className='h-8 text-sm'
              allowAdd={true}
              allowRemove={true}
              addFromInput={true}
            />
          </div>

          {/* 司机字段 */}
          <div className='space-y-2'>
            <Label className='text-sm font-medium'>司机</Label>
            <EditableComboBox
              value={formData.driverName}
              onValueChange={value => handleFieldChange('driverName', value)}
              options={driverOptions}
              onAddOption={option => handleAddOption('driver', option)}
              onRemoveOption={option => handleRemoveOption('driver', option)}
              placeholder='请输入或选择司机姓名'
              className='h-8 text-sm'
              allowAdd={true}
              allowRemove={true}
              addFromInput={true}
            />
          </div>

          {/* 原因字段 */}
          <div className='space-y-2'>
            <Label className='text-sm font-medium'>原因</Label>
            <EditableComboBox
              value={formData.reason}
              onValueChange={value => handleFieldChange('reason', value)}
              options={reasonOptions}
              onAddOption={option => handleAddOption('reason', option)}
              onRemoveOption={option => handleRemoveOption('reason', option)}
              placeholder='请输入或选择出厂原因'
              className='h-8 text-sm'
              allowAdd={true}
              allowRemove={true}
              addFromInput={true}
            />
          </div>
        </div>

        {/* 底部按钮 */}
        <div className='flex items-center justify-end gap-2 pt-4 border-t'>
          <Button variant='outline' onClick={handleCancel} className='h-8 px-4'>
            取消
          </Button>
          <Button onClick={handleConfirm} className='h-8 px-4'>
            确定
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default VehicleManagementModal;
