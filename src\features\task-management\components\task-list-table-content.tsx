// src/components/sections/task-list/components/task-list-table-content.tsx
import React from 'react';

import type { ColumnDef, ColumnOrderState, OnChangeFn } from '@tanstack/react-table';

import { VirtualizedTable } from '@/shared/components/virtualized-table';
import { cn } from '@/core/lib/utils';
import type { DensityStyleValues, Task, TaskGroup, TaskGroupConfig, Vehicle, TaskListRowDisplayMode } from '@/core/types';

import { OptimizedGroupTable } from './components/optimized-group-table';
import { TaskGroupHeader } from './task-group-header';

interface TaskListTableContentProps {
  // Data
  tasks: Task[];
  taskGroups: TaskGroup[];
  tableColumns: ColumnDef<Task>[];

  // Settings
  enableZebraStriping: boolean;
  densityStyles: DensityStyleValues;
  groupConfig: TaskGroupConfig;
  rowDisplayMode?: TaskListRowDisplayMode; // 新增：行显示模式

  // Dimensions
  tableTotalWidth: number;
  estimateRowHeight: (task?: Task) => number;

  // Table State
  columnSizing: Record<string, number>;
  columnVisibility: Record<string, boolean>;
  columnOrder: string[];

  // Event Handlers
  onColumnSizingChange: (sizing: any) => void;
  onColumnOrderChange: OnChangeFn<ColumnOrderState>;
  onColumnVisibilityChange: (visibility: any) => void;
  onHeaderContextMenu: (event: React.MouseEvent, columnDef: any) => void;
  onHeaderDoubleClick: (event: React.MouseEvent, columnDef: any) => void;
  onRowContextMenu: (e: React.MouseEvent, row: any) => void;
  onRowDoubleClick: (row: any) => void;
  onDropOnProductionLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onToggleGroupCollapse: (groupKey: string) => void;
  onCancelGrouping: () => void;

  // Styling
  getColumnBackgroundProps: (
    columnId: string,
    isHeader: boolean,
    isFixed: boolean
  ) => { style: React.CSSProperties; className: string };
  getCellTextClasses?: (columnId: string) => string;

  className?: string;
}

export function TaskListTableContent({
  tasks,
  taskGroups,
  tableColumns,
  enableZebraStriping,
  densityStyles,
  groupConfig,
  rowDisplayMode = 'row', // 新增：行显示模式，默认为'row'
  tableTotalWidth,
  estimateRowHeight,
  columnSizing,
  columnVisibility,
  columnOrder,
  onColumnSizingChange,
  onColumnOrderChange,
  onColumnVisibilityChange,
  onHeaderContextMenu,
  onHeaderDoubleClick,
  onRowContextMenu,
  onRowDoubleClick,
  onDropOnProductionLine,
  onToggleGroupCollapse,
  onCancelGrouping,
  getColumnBackgroundProps,
  getCellTextClasses,
  className,
}: TaskListTableContentProps) {
  // 调试日志 - 强制输出
  console.log('📊 [TaskListTableContent] Component rendered!');
  console.log('📊 [TaskListTableContent] Received rowDisplayMode:', rowDisplayMode);
  console.log('📊 [TaskListTableContent] groupConfig?.enabled:', groupConfig?.enabled);

  if (groupConfig?.enabled) {
    return (
      <div className={cn('flex flex-col h-full min-h-0', className)}>
        <div className='flex-1 overflow-auto custom-scrollbar min-h-0'>
          <div className='space-y-2 p-2'>
            {taskGroups.map(group => (
              <div key={`group-${group.key}-${group.tasks.length}`} className='border rounded-lg'>
                <TaskGroupHeader
                  group={group}
                  groupConfig={groupConfig}
                  onToggleCollapse={onToggleGroupCollapse}
                  onCancelGrouping={onCancelGrouping}
                />
                {!group.collapsed && group.tasks.length > 0 && (
                  <div className='border-t' key={`table-${group.key}`}>
                    <OptimizedGroupTable
                      tasks={group.tasks}
                      columns={tableColumns}
                      densityStyles={densityStyles}
                      enableZebraStriping={enableZebraStriping}
                      estimateRowHeight={estimateRowHeight}
                      totalTableWidth={tableTotalWidth}
                      columnSizing={columnSizing}
                      onColumnSizingChange={onColumnSizingChange}
                      columnVisibility={columnVisibility}
                      onColumnVisibilityChange={onColumnVisibilityChange}
                      columnOrder={columnOrder}
                      onColumnOrderChange={onColumnOrderChange}
                      onHeaderContextMenu={onHeaderContextMenu}
                      onHeaderDoubleClick={onHeaderDoubleClick}
                      onRowContextMenu={onRowContextMenu}
                      onRowDoubleClick={onRowDoubleClick}
                      getColumnBackgroundProps={columnId =>
                        getColumnBackgroundProps(columnId, false, false)
                      }
                      getCellTextClasses={getCellTextClasses}
                      onDropOnProductionLine={(taskId, lineIndex) => {
                        // Note: OptimizedGroupTable expects different signature than parent
                        console.log('Drop on production line:', taskId, lineIndex);
                      }}
                      isGroupedMode={false}
                      rowDisplayMode={rowDisplayMode} /* 新增：传递行显示模式 */
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Regular table view (non-grouped)

  return (
    <div className={cn('flex flex-col h-full min-h-0', className)}>
      <div className='flex-1 custom-scrollbar overflow-auto min-h-0'>
        <VirtualizedTable
          data={tasks}
          columns={tableColumns}
          getRowId={row => row.id}
          densityStyles={densityStyles}
          enableZebraStriping={enableZebraStriping}
          estimateRowHeightAction={estimateRowHeight}
          totalTableWidth={tableTotalWidth}
          rowDisplayMode={rowDisplayMode} /* 新增：传递行显示模式 */
          columnSizing={columnSizing}
          onColumnSizingChangeAction={onColumnSizingChange}
          columnVisibility={columnVisibility}
          onColumnVisibilityChangeAction={onColumnVisibilityChange}
          columnOrder={columnOrder}
          onColumnOrderChangeAction={onColumnOrderChange}
          onHeaderContextMenuAction={onHeaderContextMenu}
          onHeaderDoubleClickAction={onHeaderDoubleClick}
          onRowContextMenuAction={onRowContextMenu}
          onRowDoubleClickAction={onRowDoubleClick}
          getColumnBackgroundPropsAction={getColumnBackgroundProps}
          getCellTextClassesAction={getCellTextClasses}
          onDropOnProductionLineAction={onDropOnProductionLine}
        />
      </div>
    </div>
  );
}
