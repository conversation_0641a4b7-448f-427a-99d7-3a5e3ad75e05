/**
 * 业务结果包装类
 * 统一的业务操作结果格式
 */

export class BusinessResult<T> {
  constructor(
    public readonly success: boolean,
    public readonly data?: T,
    public readonly error?: BusinessError,
    public readonly warnings?: BusinessWarning[]
  ) {}

  static success<T>(data: T, warnings?: BusinessWarning[]): BusinessResult<T> {
    return new BusinessResult(true, data, undefined, warnings);
  }

  static failure<T>(
    code: string,
    message: string,
    details?: any,
    warnings?: BusinessWarning[]
  ): BusinessResult<T> {
    const error = new BusinessError(code, message, details);
    return new BusinessResult(false, undefined as unknown as T, error, warnings);
  }

  static fromError<T>(error: Error, code?: string): BusinessResult<T> {
    const businessError = new BusinessError(code || 'UNKNOWN_ERROR', error.message, error);
    return new BusinessResult(false, undefined as unknown as T, businessError);
  }

  // 便捷方法
  isSuccess(): boolean {
    return this.success;
  }

  isFailure(): boolean {
    return !this.success;
  }

  hasWarnings(): boolean {
    return Boolean(this.warnings && this.warnings.length > 0);
  }

  getErrorMessage(): string {
    return this.error?.message || '';
  }

  getErrorCode(): string {
    return this.error?.code || '';
  }

  getWarningMessages(): string[] {
    return this.warnings?.map(w => w.message) || [];
  }

  // 链式操作
  map<U>(fn: (data: T) => U): BusinessResult<U> {
    if (this.success && this.data !== undefined) {
      try {
        const newData = fn(this.data);
        return BusinessResult.success(newData, this.warnings);
      } catch (error) {
        return BusinessResult.fromError(error as Error);
      }
    }
    return new BusinessResult(false, undefined as unknown as U, this.error, this.warnings);
  }

  flatMap<U>(fn: (data: T) => BusinessResult<U>): BusinessResult<U> {
    if (this.success && this.data !== undefined) {
      try {
        const result = fn(this.data);
        // 合并警告
        const combinedWarnings = [...(this.warnings || []), ...(result.warnings || [])];
        return new BusinessResult(
          result.success,
          result.data,
          result.error,
          combinedWarnings.length > 0 ? combinedWarnings : undefined
        );
      } catch (error) {
        return BusinessResult.fromError(error as Error);
      }
    }
    return new BusinessResult(false, undefined as unknown as U, this.error, this.warnings);
  }

  // 错误恢复
  recover(fn: (error: BusinessError) => T): BusinessResult<T> {
    if (this.isFailure() && this.error) {
      try {
        const data = fn(this.error);
        return BusinessResult.success(data, this.warnings);
      } catch (error) {
        return BusinessResult.fromError(error as Error);
      }
    }
    return this;
  }

  // 转换为Promise
  toPromise(): Promise<T> {
    if (this.success && this.data !== undefined) {
      return Promise.resolve(this.data);
    }
    return Promise.reject(this.error || new Error('Unknown error'));
  }
}

export class BusinessError extends Error {
  constructor(
    public readonly code: string,
    public override readonly message: string,
    public readonly details?: any,
    public readonly timestamp: string = new Date().toISOString()
  ) {
    super(message);
    this.name = 'BusinessError';
  }

  override toString(): string {
    return `BusinessError [${this.code}]: ${this.message}`;
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp,
    };
  }
}

export interface BusinessWarning {
  code: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
  field?: string;
  details?: any;
}

// 常用的业务错误代码
export const BusinessErrorCodes = {
  // 验证错误
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  INVALID_PARAMETER: 'INVALID_PARAMETER',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',

  // 数据错误
  DATA_NOT_FOUND: 'DATA_NOT_FOUND',
  DATA_CONFLICT: 'DATA_CONFLICT',
  DATA_CORRUPTION: 'DATA_CORRUPTION',

  // 业务规则错误
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',

  // 计算错误
  CALCULATION_FAILED: 'CALCULATION_FAILED',
  INVALID_CALCULATION_PARAMS: 'INVALID_CALCULATION_PARAMS',
  CALCULATION_OUT_OF_RANGE: 'CALCULATION_OUT_OF_RANGE',

  // 系统错误
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',

  // 外部服务错误
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  API_ERROR: 'API_ERROR',

  // 并发错误
  CONCURRENT_MODIFICATION: 'CONCURRENT_MODIFICATION',
  RESOURCE_LOCKED: 'RESOURCE_LOCKED',
} as const;

// 常用的警告代码
export const BusinessWarningCodes = {
  // 数据质量警告
  DATA_QUALITY_WARNING: 'DATA_QUALITY_WARNING',
  INCOMPLETE_DATA: 'INCOMPLETE_DATA',
  OUTDATED_DATA: 'OUTDATED_DATA',

  // 性能警告
  PERFORMANCE_WARNING: 'PERFORMANCE_WARNING',
  SLOW_OPERATION: 'SLOW_OPERATION',

  // 业务警告
  BUSINESS_RULE_WARNING: 'BUSINESS_RULE_WARNING',
  RECOMMENDATION_AVAILABLE: 'RECOMMENDATION_AVAILABLE',
  OPTIMIZATION_SUGGESTED: 'OPTIMIZATION_SUGGESTED',

  // 配比特定警告
  RATIO_QUALITY_WARNING: 'RATIO_QUALITY_WARNING',
  MATERIAL_COMPATIBILITY_WARNING: 'MATERIAL_COMPATIBILITY_WARNING',
  COST_WARNING: 'COST_WARNING',
  ENVIRONMENTAL_WARNING: 'ENVIRONMENTAL_WARNING',
} as const;

// 工具函数
export function createBusinessWarning(
  code: string,
  message: string,
  severity: BusinessWarning['severity'] = 'medium',
  field?: string,
  details?: any
): BusinessWarning {
  return {
    code,
    message,
    severity,
    field,
    details,
  };
}

export function combineResults<T>(results: BusinessResult<T>[]): BusinessResult<T[]> {
  const successResults: T[] = [];
  const errors: BusinessError[] = [];
  const warnings: BusinessWarning[] = [];

  for (const result of results) {
    if (result.isSuccess() && result.data !== undefined) {
      successResults.push(result.data);
    } else if (result.error) {
      errors.push(result.error);
    }

    if (result.warnings) {
      warnings.push(...result.warnings);
    }
  }

  if (errors.length > 0) {
    // 如果有错误，返回第一个错误
    return new BusinessResult(
      false,
      undefined as unknown as T[],
      errors[0],
      warnings.length > 0 ? warnings : undefined
    );
  }

  return BusinessResult.success(successResults, warnings.length > 0 ? warnings : undefined);
}

export function validateAndExecute<T>(
  validations: (() => BusinessResult<void>)[],
  operation: () => BusinessResult<T>
): BusinessResult<T> {
  // 执行所有验证
  const validationResults = validations.map(validation => validation());
  const combinedValidation = combineResults(validationResults);

  if (combinedValidation.isFailure()) {
    return new BusinessResult(
      false,
      undefined as unknown as T,
      combinedValidation.error,
      combinedValidation.warnings
    );
  }

  // 执行操作
  const operationResult = operation();

  // 合并警告
  const allWarnings = [...(combinedValidation.warnings || []), ...(operationResult.warnings || [])];

  return new BusinessResult(
    operationResult.success,
    operationResult.data,
    operationResult.error,
    allWarnings.length > 0 ? allWarnings : undefined
  );
}

// 异步版本
export async function validateAndExecuteAsync<T>(
  validations: (() => Promise<BusinessResult<void>>)[],
  operation: () => Promise<BusinessResult<T>>
): Promise<BusinessResult<T>> {
  try {
    // 执行所有验证
    const validationResults = await Promise.all(validations.map(validation => validation()));
    const combinedValidation = combineResults(validationResults);

    if (combinedValidation.isFailure()) {
      return new BusinessResult(
        false,
        undefined as unknown as T,
        combinedValidation.error,
        combinedValidation.warnings
      );
    }

    // 执行操作
    const operationResult = await operation();

    // 合并警告
    const allWarnings = [
      ...(combinedValidation.warnings || []),
      ...(operationResult.warnings || []),
    ];

    return new BusinessResult(
      operationResult.success,
      operationResult.data,
      operationResult.error,
      allWarnings.length > 0 ? allWarnings : undefined
    );
  } catch (error) {
    return BusinessResult.fromError(error as Error);
  }
}
