/**
 * 懒加载配置
 * 统一管理应用的懒加载策略
 */

import { performanceMonitor } from '@/infrastructure/monitoring/performance-monitor';

// ==================== 功能开关 ====================

export const LAZY_LOADING_CONFIG = {
  // 模块懒加载
  modules: {
    dispatch: true,
    ratio: true,
  },

  // 组件懒加载
  components: {
    modals: true,
    charts: true,
    heavyComponents: true,
  },

  // 预加载策略
  preloading: {
    enabled: true,
    onIdle: true,
    onHover: true,
    onRouteChange: true,
  },

  // 性能监控
  monitoring: {
    enabled: true,
    logLoadTimes: true,
    trackBundleSizes: true,
  },
} as const;

// ==================== 页面级懒加载 ====================

// 调度相关页面 - 暂时注释掉不存在的组件
// export const TaskListPageLazy = lazy(() =>
//   import('@/components/task-list/TaskListPage').then(module => ({
//     default: module.default
//   }))
// );

// export const VehicleDispatchPageLazy = lazy(() =>
//   import('@/components/vehicle-dispatch/VehicleDispatchPage').then(module => ({
//     default: module.default
//   }))
// );

// 配比相关页面 - 暂时注释掉不存在的组件
// export const RatioV1PageLazy = lazy(() =>
//   import('@/components/pages/ratio/RatioV1Page').then(module => ({
//     default: module.default
//   }))
// );

// export const RatioV2PageLazy = lazy(() =>
//   import('@/components/pages/ratio-v2/RatioV2Page').then(module => ({
//     default: module.default
//   }))
// );

// ==================== 模块级懒加载 ====================

// 暂时注释掉模块懒加载，避免循环依赖
// export const DispatchModuleLazy = lazy(() =>
//   import('@/modules/dispatch').then(module => ({
//     default: module.initializeDispatchModule
//   }))
// );

// export const RatioModuleLazy = lazy(() =>
//   import('@/modules/ratio').then(module => ({
//     default: module.initializeRatioModule
//   }))
// );

// ==================== 组件级懒加载 ====================

// 重型组件懒加载 - 暂时注释掉不存在的组件
// export const TaskListLazy = lazy(() => import('@/components/task-list/TaskList'));
// export const TaskCardLazy = lazy(() => import('@/components/task-list/cards/TaskCard'));
// export const VehicleCardLazy = lazy(() => import('@/components/vehicle-dispatch/DispatchableVehicleCard'));

// 设置页面懒加载 - 暂时注释掉不存在的组件
// export const SettingsPageLazy = lazy(() => import('@/components/settings/SettingsPage'));
// export const PerformanceDashboardLazy = lazy(() => import('@/components/dashboard/performance-dashboard'));

// ==================== 预加载策略 ====================

export class LazyLoadingManager {
  private static preloadedModules = new Set<string>();
  private static preloadPromises = new Map<string, Promise<any>>();
  private static isPreloadingInitialized = false;

  /**
   * 预加载模块
   */
  static async preloadModule(moduleName: 'dispatch' | 'ratio') {
    if (this.preloadedModules.has(moduleName)) {
      return this.preloadPromises.get(moduleName);
    }

    const startTime = performance.now();

    let preloadPromise: Promise<any>;

    switch (moduleName) {
      case 'dispatch':
        preloadPromise = import('@/features/vehicle-dispatch');
        break;
      case 'ratio':
        preloadPromise = import('@/features/ratio-management');
        break;
      default:
        throw new Error(`Unknown module: ${moduleName}`);
    }

    this.preloadPromises.set(moduleName, preloadPromise);

    try {
      await preloadPromise;
      this.preloadedModules.add(moduleName);

      const loadTime = performance.now() - startTime;

      // 记录性能监控数据
      performanceMonitor.recordLazyLoadPerformance(`module-${moduleName}`, loadTime, true);

      if (LAZY_LOADING_CONFIG.monitoring.logLoadTimes) {
        console.log(`✅ 模块 ${moduleName} 预加载完成，耗时: ${loadTime.toFixed(2)}ms`);
      }
    } catch (error) {
      console.error(`❌ 模块 ${moduleName} 预加载失败:`, error);

      // 记录失败的性能数据
      const loadTime = performance.now() - startTime;
      performanceMonitor.recordLazyLoadPerformance(`module-${moduleName}`, loadTime, false);

      this.preloadPromises.delete(moduleName);
    }

    return preloadPromise;
  }

  /**
   * 预加载页面
   */
  static async preloadPage(pageName: string) {
    const startTime = performance.now();

    try {
      // 暂时注释掉不存在的页面
      console.warn(`Page ${pageName} not available for preloading`);

      const loadTime = performance.now() - startTime;

      // 记录跳过的预加载
      performanceMonitor.recordLazyLoadPerformance(`page-${pageName}`, loadTime, false);

      if (LAZY_LOADING_CONFIG.monitoring.logLoadTimes) {
        console.log(`⚠️ 页面 ${pageName} 预加载跳过，耗时: ${loadTime.toFixed(2)}ms`);
      }

      return null;

      // switch (pageName) {
      //   case 'ratio-v1':
      //     pageModule = await import('@/components/pages/ratio/RatioV1Page');
      //     break;
      //   case 'ratio-v2':
      //     pageModule = await import('@/components/pages/ratio-v2/RatioV2Page');
      //     break;
      //   default:
      //     console.warn(`Page ${pageName} not available for preloading`);
      //     return null;
      // }
    } catch (error) {
      console.error(`❌ 页面 ${pageName} 预加载失败:`, error);
      throw error;
    }
  }

  /**
   * 智能预加载策略
   */
  static initializeSmartPreloading() {
    if (!LAZY_LOADING_CONFIG.preloading.enabled || this.isPreloadingInitialized) return;

    this.isPreloadingInitialized = true;

    // 空闲时预加载
    if (LAZY_LOADING_CONFIG.preloading.onIdle && 'requestIdleCallback' in window) {
      requestIdleCallback(() => {
        this.preloadCommonModules();
      });
    }

    // 鼠标悬停预加载
    if (LAZY_LOADING_CONFIG.preloading.onHover) {
      this.setupHoverPreloading();
    }

    // 路由变化预加载
    if (LAZY_LOADING_CONFIG.preloading.onRouteChange) {
      this.setupRoutePreloading();
    }
  }

  /**
   * 预加载常用模块
   */
  private static async preloadCommonModules() {
    const commonModules = ['dispatch', 'ratio'] as const;

    for (const module of commonModules) {
      try {
        await this.preloadModule(module);
      } catch (error) {
        console.warn(`预加载模块 ${module} 失败:`, error);
      }
    }
  }

  /**
   * 设置悬停预加载
   */
  private static setupHoverPreloading() {
    const preloadMap = {
      '[data-preload="task-list"]': () => this.preloadPage('task-list'),
      '[data-preload="vehicle-dispatch"]': () => this.preloadPage('vehicle-dispatch'),
      '[data-preload="ratio-v1"]': () => this.preloadPage('ratio-v1'),
      '[data-preload="ratio-v2"]': () => this.preloadPage('ratio-v2'),
    };

    Object.entries(preloadMap).forEach(([selector, preloadFn]) => {
      document.addEventListener('mouseover', e => {
        const target = e.target as Element;
        if (target.matches(selector)) {
          preloadFn().catch(console.warn);
        }
      });
    });
  }

  /**
   * 设置路由预加载
   */
  private static setupRoutePreloading() {
    // 监听路由变化，预加载相关模块
    if (typeof window !== 'undefined') {
      window.addEventListener('popstate', () => {
        const path = window.location.pathname;

        if (path.includes('ratio')) {
          this.preloadModule('ratio');
        } else if (path.includes('dispatch') || path === '/') {
          this.preloadModule('dispatch');
        }
      });
    }
  }

  /**
   * 获取预加载状态
   */
  static getPreloadStatus() {
    return {
      preloadedModules: Array.from(this.preloadedModules),
      pendingPreloads: Array.from(this.preloadPromises.keys()),
      config: LAZY_LOADING_CONFIG,
    };
  }
}

// ==================== 性能监控 ====================

export class LazyLoadingMonitor {
  private static metrics = new Map<
    string,
    {
      loadTime: number;
      size?: number;
      timestamp: number;
    }
  >();

  static recordLoadTime(componentName: string, loadTime: number, size?: number) {
    this.metrics.set(componentName, {
      loadTime,
      size,
      timestamp: Date.now(),
    });

    if (LAZY_LOADING_CONFIG.monitoring.logLoadTimes) {
      console.log(
        `📊 ${componentName} 加载时间: ${loadTime.toFixed(2)}ms${size ? `, 大小: ${(size / 1024).toFixed(2)}KB` : ''}`
      );
    }
  }

  static getMetrics() {
    return Object.fromEntries(this.metrics);
  }

  static generateReport() {
    const metrics = this.getMetrics();
    const totalComponents = Object.keys(metrics).length;
    const averageLoadTime =
      Object.values(metrics).reduce((sum, metric) => sum + metric.loadTime, 0) / totalComponents;

    return {
      totalComponents,
      averageLoadTime: averageLoadTime.toFixed(2),
      slowestComponent: Object.entries(metrics).sort(([, a], [, b]) => b.loadTime - a.loadTime)[0],
      fastestComponent: Object.entries(metrics).sort(([, a], [, b]) => a.loadTime - b.loadTime)[0],
      metrics,
    };
  }
}

// ==================== 初始化 ====================

let isInitialized = false;

export function initializeLazyLoading() {
  if (typeof window !== 'undefined' && !isInitialized) {
    isInitialized = true;
    LazyLoadingManager.initializeSmartPreloading();

    // 性能监控
    if (LAZY_LOADING_CONFIG.monitoring.enabled) {
      console.log('🚀 懒加载系统已初始化');
      console.log('📊 预加载状态:', LazyLoadingManager.getPreloadStatus());
    }
  }
}
