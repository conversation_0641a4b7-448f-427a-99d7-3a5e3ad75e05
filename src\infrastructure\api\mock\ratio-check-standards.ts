/**
 * 配比检查标准默认数据
 */

import type {
  RatioCheckStandard,
  FieldMapping,
  DefaultStandardConfig,
} from '@/core/types/ratio-check-standard';

/**
 * 字段映射配置
 */
export const FIELD_MAPPINGS: FieldMapping[] = [
  {
    field: 'cementMin',
    label: '水泥下限',
    unit: 'kg/m³',
    category: 'cement',
    required: false,
    defaultMin: 200,
    defaultMax: 600,
  },
  {
    field: 'cementMax',
    label: '水泥上限',
    unit: 'kg/m³',
    category: 'cement',
    required: false,
    defaultMin: 200,
    defaultMax: 600,
  },
  {
    field: 'cement325Min',
    label: '325下限',
    unit: 'kg/m³',
    category: 'cement',
    required: false,
    defaultMin: 150,
    defaultMax: 500,
  },
  {
    field: 'cement325Max',
    label: '325上限',
    unit: 'kg/m³',
    category: 'cement',
    required: false,
    defaultMin: 150,
    defaultMax: 500,
  },
  {
    field: 'cement425Min',
    label: '425下限',
    unit: 'kg/m³',
    category: 'cement',
    required: false,
    defaultMin: 200,
    defaultMax: 600,
  },
  {
    field: 'cement425Max',
    label: '425上限',
    unit: 'kg/m³',
    category: 'cement',
    required: false,
    defaultMin: 200,
    defaultMax: 600,
  },
  {
    field: 'sandMin',
    label: '砂子下限',
    unit: 'kg/m³',
    category: 'aggregate',
    required: false,
    defaultMin: 600,
    defaultMax: 900,
  },
  {
    field: 'sandMax',
    label: '砂子上限',
    unit: 'kg/m³',
    category: 'aggregate',
    required: false,
    defaultMin: 600,
    defaultMax: 900,
  },
  {
    field: 'stoneMin',
    label: '石子下限',
    unit: 'kg/m³',
    category: 'aggregate',
    required: false,
    defaultMin: 1000,
    defaultMax: 1300,
  },
  {
    field: 'stoneMax',
    label: '石子上限',
    unit: 'kg/m³',
    category: 'aggregate',
    required: false,
    defaultMin: 1000,
    defaultMax: 1300,
  },
  {
    field: 'additiveMin',
    label: '外加剂下限',
    unit: 'kg/m³',
    category: 'additive',
    required: false,
    defaultMin: 0,
    defaultMax: 20,
  },
  {
    field: 'additiveMax',
    label: '外加剂上限',
    unit: 'kg/m³',
    category: 'additive',
    required: false,
    defaultMin: 0,
    defaultMax: 20,
  },
  {
    field: 'flyAshMin',
    label: '粉煤灰下限',
    unit: 'kg/m³',
    category: 'additive',
    required: false,
    defaultMin: 0,
    defaultMax: 150,
  },
  {
    field: 'flyAshMax',
    label: '粉煤灰上限',
    unit: 'kg/m³',
    category: 'additive',
    required: false,
    defaultMin: 0,
    defaultMax: 150,
  },
  {
    field: 'mineralPowderMin',
    label: '矿粉下限',
    unit: 'kg/m³',
    category: 'additive',
    required: false,
    defaultMin: 0,
    defaultMax: 120,
  },
  {
    field: 'mineralPowderMax',
    label: '矿粉上限',
    unit: 'kg/m³',
    category: 'additive',
    required: false,
    defaultMin: 0,
    defaultMax: 120,
  },
  {
    field: 'waterMin',
    label: '水下限',
    unit: 'kg/m³',
    category: 'water',
    required: false,
    defaultMin: 150,
    defaultMax: 250,
  },
  {
    field: 'waterMax',
    label: '水上限',
    unit: 'kg/m³',
    category: 'water',
    required: false,
    defaultMin: 150,
    defaultMax: 250,
  },
];

/**
 * 默认配比检查标准
 */
export const DEFAULT_RATIO_CHECK_STANDARDS: RatioCheckStandard[] = [
  {
    id: 'std-c20-normal',
    strength: 'C20',
    category: '普通混凝土',
    cementMin: 260,
    cementMax: 400,
    cement325Min: 260,
    cement325Max: 400,
    cement425Min: 240,
    cement425Max: 380,
    sandMin: 650,
    sandMax: 850,
    stoneMin: 1050,
    stoneMax: 1250,
    additiveMin: 0,
    additiveMax: 15,
    flyAshMin: 0,
    flyAshMax: 100,
    mineralPowderMin: 0,
    mineralPowderMax: 80,
    waterMin: 160,
    waterMax: 200,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isActive: true,
    description: 'C20普通混凝土标准',
  },
  {
    id: 'std-c25-normal',
    strength: 'C25',
    category: '普通混凝土',
    cementMin: 280,
    cementMax: 420,
    cement325Min: 300,
    cement325Max: 440,
    cement425Min: 280,
    cement425Max: 420,
    sandMin: 650,
    sandMax: 850,
    stoneMin: 1050,
    stoneMax: 1250,
    additiveMin: 0,
    additiveMax: 18,
    flyAshMin: 0,
    flyAshMax: 120,
    mineralPowderMin: 0,
    mineralPowderMax: 100,
    waterMin: 160,
    waterMax: 200,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isActive: true,
    description: 'C25普通混凝土标准',
  },
  {
    id: 'std-c30-normal',
    strength: 'C30',
    category: '普通混凝土',
    cementMin: 300,
    cementMax: 450,
    cement325Min: 320,
    cement325Max: 470,
    cement425Min: 300,
    cement425Max: 450,
    sandMin: 650,
    sandMax: 850,
    stoneMin: 1050,
    stoneMax: 1250,
    additiveMin: 2,
    additiveMax: 20,
    flyAshMin: 0,
    flyAshMax: 130,
    mineralPowderMin: 0,
    mineralPowderMax: 110,
    waterMin: 155,
    waterMax: 195,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isActive: true,
    description: 'C30普通混凝土标准',
  },
  {
    id: 'std-c35-normal',
    strength: 'C35',
    category: '普通混凝土',
    cementMin: 320,
    cementMax: 480,
    cement325Min: 350,
    cement325Max: 510,
    cement425Min: 320,
    cement425Max: 480,
    sandMin: 640,
    sandMax: 840,
    stoneMin: 1040,
    stoneMax: 1240,
    additiveMin: 3,
    additiveMax: 22,
    flyAshMin: 0,
    flyAshMax: 140,
    mineralPowderMin: 0,
    mineralPowderMax: 120,
    waterMin: 150,
    waterMax: 190,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isActive: true,
    description: 'C35普通混凝土标准',
  },
  {
    id: 'std-c40-normal',
    strength: 'C40',
    category: '普通混凝土',
    cementMin: 350,
    cementMax: 520,
    cement325Min: 380,
    cement325Max: 550,
    cement425Min: 350,
    cement425Max: 520,
    sandMin: 630,
    sandMax: 830,
    stoneMin: 1030,
    stoneMax: 1230,
    additiveMin: 4,
    additiveMax: 25,
    flyAshMin: 0,
    flyAshMax: 150,
    mineralPowderMin: 0,
    mineralPowderMax: 130,
    waterMin: 145,
    waterMax: 185,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isActive: true,
    description: 'C40普通混凝土标准',
  },
];

/**
 * 默认配置
 */
export const DEFAULT_STANDARD_CONFIG: DefaultStandardConfig = {
  standards: DEFAULT_RATIO_CHECK_STANDARDS,
  validationConfig: {
    enableStrict: false,
    autoApplyStandard: true,
    showWarnings: true,
    allowOverride: true,
  },
};

/**
 * 获取字段的显示标签
 */
export function getFieldLabel(field: string): string {
  const mapping = FIELD_MAPPINGS.find(m => m.field === field);
  return mapping?.label || field;
}

/**
 * 获取字段的单位
 */
export function getFieldUnit(field: string): string {
  const mapping = FIELD_MAPPINGS.find(m => m.field === field);
  return mapping?.unit || '';
}

/**
 * 获取字段的分类
 */
export function getFieldCategory(field: string): string {
  const mapping = FIELD_MAPPINGS.find(m => m.field === field);
  return mapping?.category || 'other';
}
