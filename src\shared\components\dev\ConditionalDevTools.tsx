/**
 * ConditionalDevTools - 条件加载的开发工具
 * 只在开发环境加载开发工具，减少生产环境包体积
 */

'use client';

import React, { Suspense, lazy } from 'react';

// 只在开发环境动态导入开发工具
const ReactQueryDevtools = lazy(() => {
  // 暂时禁用以避免构建错误
  // if (process.env.NODE_ENV === 'development') {
  //   return import('@tanstack/react-query-devtools').then(module => ({
  //     default: module.ReactQueryDevtools
  //   }));
  // }
  // 生产环境返回空组件
  return Promise.resolve({ default: () => null });
});

/**
 * 条件加载的 React Query 开发工具
 * 只在开发环境显示
 */
export function ConditionalReactQueryDevtools() {
  // 生产环境直接返回 null
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Suspense fallback={null}>
      <ReactQueryDevtools {...({ initialIsOpen: false, buttonPosition: 'bottom-right' } as any)} />
    </Suspense>
  );
}

/**
 * 开发环境性能监控组件
 */
export function ConditionalPerformanceMonitor() {
  // 生产环境直接返回 null
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  React.useEffect(() => {
    // 只在开发环境启用性能监控
    if (typeof window !== 'undefined' && 'performance' in window) {
      const observer = new PerformanceObserver(list => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.entryType === 'measure') {
            console.log(`🔍 Performance: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
          }
        });
      });

      observer.observe({ entryTypes: ['measure', 'navigation'] });

      return () => observer.disconnect();
    }
    return undefined;
  }, []);

  return null;
}

/**
 * 开发环境内存使用监控
 */
export function ConditionalMemoryMonitor() {
  // 生产环境直接返回 null
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  React.useEffect(() => {
    if (
      typeof window !== 'undefined' &&
      'performance' in window &&
      'memory' in (window.performance as any)
    ) {
      const logMemoryUsage = () => {
        const memory = (window.performance as any).memory;
        console.log('🧠 Memory Usage:', {
          used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
          total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
          limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
        });
      };

      // 每30秒记录一次内存使用情况
      const interval = setInterval(logMemoryUsage, 30000);

      // 初始记录
      logMemoryUsage();

      return () => clearInterval(interval);
    }
    return undefined;
  }, []);

  return null;
}

/**
 * 开发环境包体积分析提示
 */
export function ConditionalBundleAnalyzer() {
  // 生产环境直接返回 null
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  React.useEffect(() => {
    // 检查是否启用了包分析
    if (process.env['ANALYZE'] === 'true') {
      console.log(
        '📦 Bundle analysis is enabled. Run `npm run analyze` to view bundle size analysis.'
      );
    } else {
      console.log(
        '💡 Tip: Run `npm run analyze` to analyze bundle size and identify optimization opportunities.'
      );
    }
  }, []);

  return null;
}

/**
 * React DevTools 错误抑制组件
 * 抑制开发环境中的 DevTools 相关错误
 */
export function DevToolsErrorSuppressor() {
  // 生产环境直接返回 null
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  React.useEffect(() => {
    // 抑制 React DevTools 相关错误
    const originalError = console.error;
    console.error = (...args) => {
      const message = args[0];

      // 过滤掉 DevTools 相关错误和DOM操作错误
      if (
        typeof message === 'string' &&
        (message.includes('Cannot add child') ||
          message.includes('parent node was not found in the Store') ||
          message.includes("Failed to execute 'removeChild' on 'Node'") ||
          message.includes('The node to be removed is not a child of this node') ||
          message.includes('DevTools') ||
          message.includes('chrome-extension://'))
      ) {
        // 静默处理这些错误
        return;
      }

      // 其他错误正常输出
      originalError.apply(console, args);
    };

    // 清理函数
    return () => {
      console.error = originalError;
    };
  }, []);

  return null;
}

/**
 * 开发工具容器组件
 * 统一管理所有开发环境工具
 */
export function DevToolsContainer() {
  return (
    <>
      <DevToolsErrorSuppressor />
      <ConditionalReactQueryDevtools />
      <ConditionalPerformanceMonitor />
      <ConditionalMemoryMonitor />
      <ConditionalBundleAnalyzer />
      {/* 缓存监控功能已合并到性能监控中心 */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className='fixed bottom-4 right-4 z-50'>
          <EventEmitterTest />
        </div>
      )} */}
    </>
  );
}

export default DevToolsContainer;
