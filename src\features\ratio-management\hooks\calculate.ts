/**
 * 配比计算API路由
 * POST /api/ratio/calculate
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { CalculateRequest, CalculateResponse, ApiErrorResponse } from '@/core/types/ratio-api';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<CalculateResponse | ApiErrorResponse>
) {
  // 只允许POST请求
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: '只允许POST请求',
      },
      timestamp: new Date().toISOString(),
    });
  }

  try {
    const request: CalculateRequest = req.body;

    // 验证请求参数
    if (!request.taskId || !request.calculationParams || !request.materials) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: '缺少必要的请求参数',
          details: {
            required: ['taskId', 'calculationParams', 'materials'],
            received: Object.keys(request),
          },
        },
        timestamp: new Date().toISOString(),
      });
    }

    // 模拟计算延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));

    // 执行配比计算（这里使用Mock数据，实际应用中调用真实的计算引擎）
    // 基于计算参数生成标准混凝土配比
    const params = request.calculationParams;

    // 标准配比计算（简化版）
    const cementContent = params.cementContent || 400; // kg/m³
    const waterContent = params.waterContent || cementContent * (params.waterCementRatio || 0.45);
    const sandContent = 650; // kg/m³ 标准砂用量
    const stoneContent = 1200; // kg/m³ 标准石子用量
    const additiveContent = (cementContent * (params.additiveRatio || 2.0)) / 100;
    const flyashContent = (cementContent * (params.flyashRatio || 15)) / 100;

    // 生成标准混凝土材料配比
    const standardMaterials = {
      水泥: cementContent,
      水: waterContent,
      砂子: sandContent,
      石子: stoneContent,
      外加剂: additiveContent,
      粉煤灰: flyashContent > 0 ? flyashContent : undefined,
      矿粉:
        params.mineralPowderRatio > 0
          ? (cementContent * params.mineralPowderRatio) / 100
          : undefined,
    };

    // 过滤掉undefined的材料
    const materials = Object.fromEntries(
      Object.entries(standardMaterials).filter(([_, value]) => value !== undefined)
    ) as Record<string, number>;

    const totalWeight = Object.values(materials).reduce((sum, amount) => sum + amount, 0);

    const calculationResults = {
      totalWeight,
      materials,
      strengthPrediction: params.strengthGrade || 30,
      qualityScore: 85 + Math.random() * 10, // 85-95分
      warnings: generateWarnings(request),
      suggestions: generateSuggestions(request),
      carbonFootprint: totalWeight * 0.3, // 模拟碳足迹
      costEstimate: totalWeight * 0.8, // 模拟成本估算
    };

    const response: CalculateResponse = {
      success: true,
      data: calculationResults,
      message: '配比计算完成',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('配比计算API错误:', error);

    const errorResponse: ApiErrorResponse = {
      success: false,
      error: {
        code: 'CALCULATION_FAILED',
        message: '配比计算失败',
        details: error instanceof Error ? error.message : String(error),
      },
      timestamp: new Date().toISOString(),
    };

    res.status(500).json(errorResponse);
  }
}

/**
 * 生成警告信息
 */
function generateWarnings(request: CalculateRequest): string[] {
  const warnings: string[] = [];

  if (request.calculationParams.waterCementRatio > 0.6) {
    warnings.push('水胶比偏高，可能影响强度');
  }

  if (request.calculationParams.sandRatio < 30) {
    warnings.push('砂率偏低，可能影响和易性');
  }

  if (request.calculationParams.slump > 200) {
    warnings.push('坍落度过大，可能影响混凝土质量');
  }

  return warnings;
}

/**
 * 生成建议信息
 */
function generateSuggestions(request: CalculateRequest): string[] {
  const suggestions: string[] = [];

  suggestions.push('建议进行试配验证');
  suggestions.push('注意控制搅拌时间');

  if (request.calculationParams.additiveRatio > 0) {
    suggestions.push('外加剂掺量适中，有利于改善工作性');
  }

  if (request.calculationParams.flyashRatio > 0) {
    suggestions.push('粉煤灰掺量有助于改善混凝土的长期性能');
  }

  return suggestions;
}
