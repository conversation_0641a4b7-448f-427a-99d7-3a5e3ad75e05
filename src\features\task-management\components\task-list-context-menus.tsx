// src/components/sections/task-list/task-list-context-menus.tsx
'use client';

import React from 'react';

import Link from 'next/link';

import {
  Activity,
  BarChart3,
  BarChartHorizontalBig,
  Calendar,
  Clock,
  Columns,
  Copy,
  Edit,
  Eye,
  FileEditIcon,
  FileText,
  FlaskConical,
  Grid3X3,
  Info,
  LayoutGrid,
  MessageSquare,
  Pause,
  PlayCircle,
  Printer,
  QrCode,
  RefreshCw,
  RotateCcw,
  Send,
  Settings,
  SlidersHorizontal,
  Smartphone,
  Square,
  StopCircle,
  Table,
  TestTube,
  Timer,
  Truck,
  Undo2,
} from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/shared/components/dropdown-menu';
import type { Task, Vehicle } from '@/core/types';

// src/components/sections/task-list/task-list-context-menus.tsx

interface TaskContextMenuProps {
  isOpen: boolean;
  position: { x: number; y: number } | null;
  taskData: { taskId: string } | null;
  onClose: () => void;
  // 第一组：任务时间和状态管理
  onOpenTimeSettingsModal: (task: Task) => void;
  onPauseTask: (task: Task) => void;
  onCompleteTask: (task: Task) => void;
  // 第二组：统计信息查看
  onOpenDispatchDetailsModal: (task: Task) => void;
  onOpenTimeStatsModal: (task: Task) => void;
  // 第三组：任务详情管理
  onOpenTaskDetailModal: (task: Task) => void;
  onOpenTaskProgressModal: (task: Task) => void;
  // 第四组：配比管理
  onOpenRatioDisplayModal: (task: Task) => void;
  onOpenRatioEditModal: (task: Task) => void;
  // 第五组：界面显示设置
  onOpenColumnSettingsModal: () => void;
  onOpenCardConfigModal: () => void;
  onSetDisplayMode: (mode: 'table' | 'card') => void;
  onResetToDefaultStyle: () => void;
  // 第六组：扩展功能
  onOpenTaskAbbreviationModal: (task: Task) => void;
  onPublishWeChatMessage: (task: Task) => void;
  onPrintQRCode: (task: Task) => void;
  filteredTasks: Task[]; // To find the actual task object
  // 当前显示模式
  currentDisplayMode: 'table' | 'card';
}

const TaskContextMenu: React.FC<TaskContextMenuProps> = ({
  isOpen,
  position,
  taskData,
  onClose,
  // 第一组：任务时间和状态管理
  onOpenTimeSettingsModal,
  onPauseTask,
  onCompleteTask,
  // 第二组：统计信息查看
  onOpenDispatchDetailsModal,
  onOpenTimeStatsModal,
  // 第三组：任务详情管理
  onOpenTaskDetailModal,
  onOpenTaskProgressModal,
  // 第四组：配比管理
  onOpenRatioDisplayModal,
  // onOpenRatioEditModal, // Replaced with navigation
  // 第五组：界面显示设置
  onOpenColumnSettingsModal,
  onOpenCardConfigModal,
  onSetDisplayMode,
  onResetToDefaultStyle,
  // 第六组：扩展功能
  onOpenTaskAbbreviationModal,
  onPublishWeChatMessage,
  onPrintQRCode,
  filteredTasks,
  currentDisplayMode,
}) => {
  if (!isOpen || !position || !taskData) return null;
  const task = filteredTasks.find(t => t.id === taskData.taskId);
  if (!task) return null;

  // 根据任务状态判断某些操作是否可用
  const canPause = task.dispatchStatus === 'InProgress';
  const canComplete = task.dispatchStatus === 'InProgress' || task.dispatchStatus === 'Paused';
  const isCompleted = task.dispatchStatus === 'Completed';

  return (
    <DropdownMenu open={isOpen} onOpenChange={onClose}>
      <DropdownMenuTrigger asChild>
        <div style={{ position: 'fixed', left: position.x, top: position.y }} />
      </DropdownMenuTrigger>
      <DropdownMenuPortal>
        <DropdownMenuContent
          sideOffset={5}
          align='start'
          className='z-50 text-xs w-56'
          onCloseAutoFocus={e => e.preventDefault()}
        >
          {/* 第一组：任务时间和状态管理 */}
          <DropdownMenuItem onClick={() => onOpenTimeSettingsModal(task)}>
            <Clock className='mr-2 h-3 w-3' />
            设定时间
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onPauseTask(task)} disabled={!canPause}>
            <Pause className='mr-2 h-3 w-3' />
            暂停任务
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onCompleteTask(task)} disabled={!canComplete}>
            <Square className='mr-2 h-3 w-3' />
            结束任务
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* 第二组：统计信息查看 */}
          <DropdownMenuItem onClick={() => onOpenDispatchDetailsModal(task)}>
            <Truck className='mr-2 h-3 w-3' />
            出车情况
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onOpenTimeStatsModal(task)}>
            <Timer className='mr-2 h-3 w-3' />
            自由时间统计
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* 第三组：任务详情管理 */}
          <DropdownMenuItem onClick={() => onOpenTaskDetailModal(task)}>
            <Eye className='mr-2 h-3 w-3' />
            任务要求
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onOpenTaskProgressModal(task)}>
            <BarChart3 className='mr-2 h-3 w-3' />
            任务进度
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* 第四组：配比管理 */}
          <DropdownMenuItem onClick={() => onOpenRatioDisplayModal(task)}>
            <TestTube className='mr-2 h-3 w-3' />
            显示配比
          </DropdownMenuItem>
          <DropdownMenuItem asChild disabled={isCompleted}>
            <Link href={`/ratio/${task.id}`}>
              <FlaskConical className='mr-2 h-3 w-3' />
              修改配比
            </Link>
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* 第五组：界面显示设置 */}
          <DropdownMenuItem
            onClick={() => {
              if (currentDisplayMode === 'table') {
                onOpenColumnSettingsModal();
              } else {
                onOpenCardConfigModal();
              }
            }}
          >
            <Columns className='mr-2 h-3 w-3' />
            设置显示列
          </DropdownMenuItem>

          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <LayoutGrid className='mr-2 h-3 w-3' />
              设置显示模式
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent>
                <DropdownMenuItem onClick={() => onSetDisplayMode('table')}>
                  <Table className='mr-2 h-3 w-3' />
                  表格模式
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onSetDisplayMode('card')}>
                  <LayoutGrid className='mr-2 h-3 w-3' />
                  卡片模式
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>

          <DropdownMenuItem onClick={() => onResetToDefaultStyle()}>
            <RefreshCw className='mr-2 h-3 w-3' />
            恢复默认样式
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* 第六组：扩展功能 */}
          <DropdownMenuItem onClick={() => onOpenTaskAbbreviationModal(task)}>
            <FileText className='mr-2 h-3 w-3' />
            任务缩写设置
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onPublishWeChatMessage(task)}>
            <Smartphone className='mr-2 h-3 w-3' />
            发布微信消息
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onPrintQRCode(task)}>
            <QrCode className='mr-2 h-3 w-3' />
            打印二维码
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenuPortal>
    </DropdownMenu>
  );
};

interface VehicleCardContextMenuProps {
  isOpen: boolean;
  position: { x: number; y: number } | null;
  contextData: { vehicle: Vehicle; task: Task } | null;
  onClose: () => void;
  onOpenDeliveryOrderDetailsModal: (vehicle: Vehicle, task: Task) => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
}

const VehicleCardContextMenu: React.FC<VehicleCardContextMenuProps> = ({
  isOpen,
  position,
  contextData,
  onClose,
  onOpenDeliveryOrderDetailsModal,
  onCancelVehicleDispatch,
}) => {
  if (!isOpen || !position || !contextData) return null;
  const { vehicle, task } = contextData;

  return (
    <DropdownMenu open={isOpen} onOpenChange={onClose}>
      <DropdownMenuTrigger asChild>
        <div style={{ position: 'fixed', left: position.x, top: position.y }} />
      </DropdownMenuTrigger>
      <DropdownMenuPortal>
        <DropdownMenuContent
          sideOffset={5}
          align='start'
          className='z-50 text-xs w-48'
          onCloseAutoFocus={e => e.preventDefault()}
        >
          <DropdownMenuItem onClick={() => onOpenDeliveryOrderDetailsModal(vehicle, task)}>
            <Info className='mr-2 h-3 w-3' />
            查看发货单详情
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => console.log('Copy vehicle info:', vehicle.id)}>
            <Copy className='mr-2 h-3 w-3' />
            复制车辆信息
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => onCancelVehicleDispatch(vehicle.id)}>
            <Undo2 className='mr-2 h-3 w-3' />
            取消调度
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => console.log('Forward vehicle to another plant:', vehicle.id)}
          >
            <Send className='mr-2 h-3 w-3' />
            转发搅拌站
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => console.log('Modify production ratio:', vehicle.id)}>
            <SlidersHorizontal className='mr-2 h-3 w-3' />
            修改生产配比
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => console.log('Request ash splitting:', vehicle.id)}>
            <FileEditIcon className='mr-2 h-3 w-3' />
            申请分灰
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenuPortal>
    </DropdownMenu>
  );
};

interface TaskListContextMenusProps {
  isTaskContextMenuOpen: boolean;
  taskContextMenuPosition: { x: number; y: number } | null;
  contextMenuTaskData: { taskId: string } | null;
  closeTaskContextMenuAction: () => void;
  filteredTasks: Task[];

  // 模态框处理函数
  openTankerNoteModal: (task: Task) => void;
  openReminderConfigModal: (task: Task) => void;

  // 第一组：任务时间和状态管理
  onOpenTimeSettingsModalAction: (task: Task) => void;
  onPauseTaskAction: (task: Task) => void;
  onCompleteTaskAction: (task: Task) => void;
  // 第二组：统计信息查看
  onOpenDispatchDetailsModalAction: (task: Task) => void;
  onOpenTimeStatsModalAction: (task: Task) => void;
  // 第三组：任务详情管理
  onOpenTaskDetailModalAction: (task: Task) => void;
  onOpenTaskProgressModalAction: (task: Task) => void;
  // 第四组：配比管理
  onOpenRatioDisplayModalAction: (task: Task) => void;
  // onOpenRatioEditModal is now handled by navigation
  // 第五组：界面显示设置
  onOpenColumnSettingsModalAction: () => void;
  onOpenCardConfigModalAction: () => void;
  onSetDisplayModeAction: (mode: 'table' | 'card') => void;
  onResetToDefaultStyleAction: () => void;
  // 第六组：扩展功能
  onOpenTaskAbbreviationModalAction: (task: Task) => void;
  onPublishWeChatMessageAction: (task: Task) => void;
  onPrintQRCodeAction: (task: Task) => void;

  isVehicleCardContextMenuOpen: boolean;
  vehicleCardContextMenuPosition: { x: number; y: number } | null;
  vehicleCardContextMenuContext: { vehicle: Vehicle; task: Task } | null;
  closeVehicleCardContextMenuAction: () => void;
  openDeliveryOrderDetailsModalAction: (vehicle: Vehicle, task: Task) => void;
  cancelVehicleDispatchAction: (vehicleId: string) => void;

  // 当前显示模式
  currentDisplayMode: 'table' | 'card';
}

export const TaskListContextMenus: React.FC<TaskListContextMenusProps> = ({
  isTaskContextMenuOpen,
  taskContextMenuPosition,
  contextMenuTaskData,
  closeTaskContextMenuAction,
  filteredTasks,
  // 模态框处理函数
  // openTankerNoteModalAction,
  // openReminderConfigModalAction,
  // 第一组：任务时间和状态管理
  onOpenTimeSettingsModalAction,
  onPauseTaskAction,
  onCompleteTaskAction,
  // 第二组：统计信息查看
  onOpenDispatchDetailsModalAction,
  onOpenTimeStatsModalAction,
  // 第三组：任务详情管理
  onOpenTaskDetailModalAction,
  onOpenTaskProgressModalAction,
  // 第四组：配比管理
  onOpenRatioDisplayModalAction,
  // 第五组：界面显示设置
  onOpenColumnSettingsModalAction,
  onOpenCardConfigModalAction,
  onSetDisplayModeAction,
  onResetToDefaultStyleAction,
  // 第六组：扩展功能
  onOpenTaskAbbreviationModalAction,
  onPublishWeChatMessageAction,
  onPrintQRCodeAction,
  isVehicleCardContextMenuOpen,
  vehicleCardContextMenuPosition,
  vehicleCardContextMenuContext,
  closeVehicleCardContextMenuAction,
  openDeliveryOrderDetailsModalAction,
  cancelVehicleDispatchAction,
  currentDisplayMode,
}) => {
  return (
    <>
      <TaskContextMenu
        isOpen={isTaskContextMenuOpen}
        position={taskContextMenuPosition}
        taskData={contextMenuTaskData}
        onClose={closeTaskContextMenuAction}
        // 第一组：任务时间和状态管理
        onOpenTimeSettingsModal={onOpenTimeSettingsModalAction}
        onPauseTask={onPauseTaskAction}
        onCompleteTask={onCompleteTaskAction}
        // 第二组：统计信息查看
        onOpenDispatchDetailsModal={onOpenDispatchDetailsModalAction}
        onOpenTimeStatsModal={onOpenTimeStatsModalAction}
        // 第三组：任务详情管理
        onOpenTaskDetailModal={onOpenTaskDetailModalAction}
        onOpenTaskProgressModal={onOpenTaskProgressModalAction}
        // 第四组：配比管理
        onOpenRatioDisplayModal={onOpenRatioDisplayModalAction}
        onOpenRatioEditModal={() => {}} // No-op, navigation is handled by Link
        // 第五组：界面显示设置
        onOpenColumnSettingsModal={onOpenColumnSettingsModalAction}
        onOpenCardConfigModal={onOpenCardConfigModalAction}
        onSetDisplayMode={onSetDisplayModeAction}
        onResetToDefaultStyle={onResetToDefaultStyleAction}
        // 第六组：扩展功能
        onOpenTaskAbbreviationModal={onOpenTaskAbbreviationModalAction}
        onPublishWeChatMessage={onPublishWeChatMessageAction}
        onPrintQRCode={onPrintQRCodeAction}
        filteredTasks={filteredTasks}
        currentDisplayMode={currentDisplayMode}
      />
      <VehicleCardContextMenu
        isOpen={isVehicleCardContextMenuOpen}
        position={vehicleCardContextMenuPosition}
        contextData={vehicleCardContextMenuContext}
        onClose={closeVehicleCardContextMenuAction}
        onOpenDeliveryOrderDetailsModal={openDeliveryOrderDetailsModalAction}
        onCancelVehicleDispatch={cancelVehicleDispatchAction}
      />
    </>
  );
};
