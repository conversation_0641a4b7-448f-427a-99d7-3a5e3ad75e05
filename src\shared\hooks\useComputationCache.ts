/**
 * 计算缓存 Hooks
 * 为常见的重复计算场景提供缓存解决方案
 */

import { useCallback, useMemo, useRef, useEffect } from 'react';
import {
  computationCache,
  cacheArrayOperation,
  cacheObjectComputation,
} from '@/infrastructure/storage/cache/computation-cache';
import type { Task, Vehicle } from '@/core/types';

// ==================== 基础计算缓存 Hook ====================

/**
 * 通用计算缓存 Hook
 */
export function useComputationCache<T>(
  key: string,
  computeFn: () => T | Promise<T>,
  dependencies: any[],
  options: { ttl?: number; enabled?: boolean } = {}
) {
  const { ttl = 10 * 60 * 1000, enabled = true } = options;
  const computeFnRef = useRef(computeFn);
  computeFnRef.current = computeFn;

  const memoizedCompute = useCallback(async () => {
    if (!enabled) {
      return computeFnRef.current();
    }

    const depKey = JSON.stringify(dependencies);
    const cacheKey = `${key}-${depKey}`;

    return computationCache.computeWithCache(cacheKey, () => computeFnRef.current(), { ttl });
  }, [key, enabled, ttl, ...dependencies]);

  return memoizedCompute;
}

// ==================== 任务相关计算缓存 ====================

/**
 * 任务过滤缓存 Hook
 */
export function useTaskFilterCache() {
  const filterTasks = useCallback(
    (tasks: Task[], filters: { plantId?: string; status?: string; search?: string }) => {
      return cacheArrayOperation(
        'task-filter',
        tasks,
        taskArray => {
          let filtered = taskArray;

          if (filters.plantId) {
            filtered = filtered.filter(task => task.plantId === filters.plantId);
          }

          if (filters.status && filters.status !== 'All') {
            filtered = filtered.filter(task => task.status === filters.status);
          }

          if (filters.search) {
            const searchLower = filters.search.toLowerCase();
            filtered = filtered.filter(
              task =>
                task.projectName?.toLowerCase().includes(searchLower) ||
                task.customerName?.toLowerCase().includes(searchLower) ||
                task.id.toLowerCase().includes(searchLower)
            );
          }

          return filtered;
        },
        {
          ttl: 5 * 60 * 1000, // 5分钟
          keyGenerator: arr => `${arr.length}-${JSON.stringify(filters)}`,
        }
      );
    },
    []
  );

  return { filterTasks };
}

/**
 * 任务分组缓存 Hook
 */
export function useTaskGroupCache() {
  const groupTasks = useCallback((tasks: Task[], groupBy: string) => {
    return cacheArrayOperation(
      'task-group',
      tasks,
      taskArray => {
        const groups = new Map<string, Task[]>();

        taskArray.forEach(task => {
          let groupKey = '';

          switch (groupBy) {
            case 'status':
              groupKey = task.status || 'Unknown';
              break;
            case 'plantId':
              groupKey = task.plantId || 'Unknown';
              break;
            case 'customerName':
              groupKey = task.customerName || 'Unknown';
              break;
            case 'projectName':
              groupKey = task.projectName || 'Unknown';
              break;
            case 'date':
              groupKey = task.scheduledDate
                ? new Date(task.scheduledDate).toDateString()
                : 'No Date';
              break;
            default:
              groupKey = 'All';
          }

          if (!groups.has(groupKey)) {
            groups.set(groupKey, []);
          }
          groups.get(groupKey)!.push(task);
        });

        return Array.from(groups.entries()).map(([key, tasks]) => ({
          key,
          tasks,
          count: tasks.length,
        }));
      },
      {
        ttl: 3 * 60 * 1000, // 3分钟
        keyGenerator: arr => `${arr.length}-${groupBy}`,
      }
    );
  }, []);

  return { groupTasks };
}

/**
 * 任务统计缓存 Hook
 */
export function useTaskStatsCache() {
  const calculateStats = useCallback((tasks: Task[]) => {
    return cacheArrayOperation(
      'task-stats',
      tasks,
      taskArray => {
        const stats = {
          total: taskArray.length,
          byStatus: {} as Record<string, number>,
          byPlant: {} as Record<string, number>,
          totalVolume: 0,
          avgVolume: 0,
          completionRate: 0,
        };

        taskArray.forEach(task => {
          // 状态统计
          const status = task.status || 'Unknown';
          stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;

          // 工厂统计
          const plantId = task.plantId || 'Unknown';
          stats.byPlant[plantId] = (stats.byPlant[plantId] || 0) + 1;

          // 体积统计
          if (task.concreteVolume) {
            stats.totalVolume += task.concreteVolume;
          }
        });

        stats.avgVolume = stats.total > 0 ? stats.totalVolume / stats.total : 0;
        stats.completionRate =
          stats.total > 0 ? ((stats.byStatus['Completed'] || 0) / stats.total) * 100 : 0;

        return stats;
      },
      {
        ttl: 2 * 60 * 1000, // 2分钟
        keyGenerator: arr =>
          `${arr.length}-${arr.reduce((sum, t) => sum + (t.concreteVolume || 0), 0)}`,
      }
    );
  }, []);

  return { calculateStats };
}

// ==================== 车辆相关计算缓存 ====================

/**
 * 车辆分组缓存 Hook
 */
export function useVehicleGroupCache() {
  const groupVehiclesByTask = useCallback((vehicles: Vehicle[]) => {
    return cacheArrayOperation(
      'vehicle-group-by-task',
      vehicles,
      vehicleArray => {
        const groups = new Map<string, Vehicle[]>();

        vehicleArray.forEach(vehicle => {
          if (vehicle.assignedTaskId) {
            if (!groups.has(vehicle.assignedTaskId)) {
              groups.set(vehicle.assignedTaskId, []);
            }
            groups.get(vehicle.assignedTaskId)!.push(vehicle);
          }
        });

        return groups;
      },
      {
        ttl: 2 * 60 * 1000, // 2分钟
        keyGenerator: arr => `${arr.length}-${arr.filter(v => v.assignedTaskId).length}`,
      }
    );
  }, []);

  const groupVehiclesByStatus = useCallback((vehicles: Vehicle[]) => {
    return cacheArrayOperation(
      'vehicle-group-by-status',
      vehicles,
      vehicleArray => {
        const groups = new Map<string, Vehicle[]>();

        vehicleArray.forEach(vehicle => {
          const status = vehicle.status || 'Unknown';
          if (!groups.has(status)) {
            groups.set(status, []);
          }
          groups.get(status)!.push(vehicle);
        });

        return groups;
      },
      {
        ttl: 1 * 60 * 1000, // 1分钟
        keyGenerator: arr => `${arr.length}-${arr.map(v => v.status).join(',')}`,
      }
    );
  }, []);

  return { groupVehiclesByTask, groupVehiclesByStatus };
}

// ==================== 配比计算缓存 ====================

/**
 * 配比计算缓存 Hook
 */
export function useRatioCalculationCache() {
  const calculateRatio = useCallback((materials: any[], params: any) => {
    return cacheObjectComputation(
      'ratio-calculation',
      { materials, params },
      async ({ materials, params }) => {
        // 模拟复杂的配比计算
        const baseRatio = {
          cement: params.targetStrength * 0.4,
          water: params.targetStrength * 0.2,
          sand: params.targetStrength * 0.3,
          gravel: params.targetStrength * 0.1,
        };

        // 根据材料调整
        materials.forEach((material: any) => {
          if (material.type === 'cement') {
            baseRatio.cement *= material.quality || 1;
          }
        });

        return {
          ...baseRatio,
          totalWeight: Object.values(baseRatio).reduce((sum, val) => sum + val, 0),
          waterCementRatio: baseRatio.water / baseRatio.cement,
          calculatedAt: new Date().toISOString(),
        };
      },
      {
        ttl: 30 * 60 * 1000, // 30分钟
        keyGenerator: obj => `${obj.materials.length}-${JSON.stringify(obj.params)}`,
      }
    );
  }, []);

  return { calculateRatio };
}

// ==================== 数据排序缓存 ====================

/**
 * 数据排序缓存 Hook
 */
export function useSortCache<T>() {
  const sortData = useCallback((data: T[], sortKey: keyof T, sortOrder: 'asc' | 'desc' = 'asc') => {
    return cacheArrayOperation(
      'data-sort',
      data,
      dataArray => {
        return [...dataArray].sort((a, b) => {
          const aVal = a[sortKey];
          const bVal = b[sortKey];

          if (aVal < bVal) return sortOrder === 'asc' ? -1 : 1;
          if (aVal > bVal) return sortOrder === 'asc' ? 1 : -1;
          return 0;
        });
      },
      {
        ttl: 5 * 60 * 1000, // 5分钟
        keyGenerator: arr => `${arr.length}-${String(sortKey)}-${sortOrder}`,
      }
    );
  }, []);

  return { sortData };
}

// ==================== 缓存管理 Hook ====================

/**
 * 缓存管理 Hook
 */
export function useCacheManagement() {
  const clearCache = useCallback((prefix?: string) => {
    if (prefix) {
      computationCache.clearByPrefix(prefix);
    } else {
      // 清理所有缓存
      console.log('Clearing all computation cache');
    }
  }, []);

  const getCacheMetrics = useCallback(() => {
    return computationCache.getMetrics();
  }, []);

  const warmupCache = useCallback(
    async (computations: Array<{ key: string; computeFn: () => any }>) => {
      await computationCache.warmup(computations);
    },
    []
  );

  return {
    clearCache,
    getCacheMetrics,
    warmupCache,
  };
}
