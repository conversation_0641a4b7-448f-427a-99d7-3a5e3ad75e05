/**
 * 时间工具函数测试
 */

import {
  addMinutes,
  formatDuration,
  formatRelativeTime,
  formatTime,
  getTimeRemaining,
  isOverdue,
  isSameDay,
  parseTimeString,
  subtractMinutes,
} from '@/core/utils/time-utils';

describe('time-utils', () => {
  describe('formatTime', () => {
    it('应该正确格式化时间字符串', () => {
      const date = new Date('2024-01-15T14:30:00Z');
      const formatted = formatTime(date.toISOString());

      expect(formatted).toMatch(/\d{2}:\d{2}/); // HH:MM格式
    });

    it('应该正确格式化Date对象', () => {
      const date = new Date('2024-01-15T14:30:00Z');
      const formatted = formatTime(date);

      expect(formatted).toMatch(/\d{2}:\d{2}/);
    });

    it('应该处理无效的时间输入', () => {
      const formatted = formatTime('invalid-date');

      expect(formatted).toBe('--:--');
    });

    it('应该支持自定义格式', () => {
      const date = new Date('2024-01-15T14:30:25Z');
      const formatted = formatTime(date, 'HH:mm:ss');

      expect(formatted).toMatch(/\d{2}:\d{2}:\d{2}/);
    });

    it('应该处理null和undefined', () => {
      expect(formatTime(null)).toBe('--:--');
      expect(formatTime(undefined)).toBe('--:--');
    });
  });

  describe('formatDuration', () => {
    it('应该正确格式化分钟数', () => {
      expect(formatDuration(30)).toBe('30分钟');
      expect(formatDuration(90)).toBe('1小时30分钟');
      expect(formatDuration(120)).toBe('2小时');
    });

    it('应该处理小于1分钟的时间', () => {
      expect(formatDuration(0.5)).toBe('30秒');
      expect(formatDuration(0)).toBe('0秒');
    });

    it('应该处理天数', () => {
      expect(formatDuration(1440)).toBe('1天'); // 24小时
      expect(formatDuration(1500)).toBe('1天1小时'); // 25小时
      expect(formatDuration(2880)).toBe('2天'); // 48小时
    });

    it('应该处理负数', () => {
      expect(formatDuration(-30)).toBe('0分钟');
    });

    it('应该支持简短格式', () => {
      expect(formatDuration(90, true)).toBe('1h 30m');
      expect(formatDuration(1440, true)).toBe('1d');
    });
  });

  describe('isOverdue', () => {
    const now = new Date('2024-01-15T14:30:00Z');

    beforeEach(() => {
      jest.useFakeTimers();
      jest.setSystemTime(now);
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('应该正确判断过期时间', () => {
      const pastTime = new Date('2024-01-15T13:30:00Z');
      const futureTime = new Date('2024-01-15T15:30:00Z');

      expect(isOverdue(pastTime.toISOString())).toBe(true);
      expect(isOverdue(futureTime.toISOString())).toBe(false);
    });

    it('应该处理当前时间', () => {
      expect(isOverdue(now.toISOString())).toBe(false);
    });

    it('应该处理无效时间', () => {
      expect(isOverdue('invalid-date')).toBe(false);
      expect(isOverdue(null)).toBe(false);
      expect(isOverdue(undefined)).toBe(false);
    });

    it('应该支持缓冲时间', () => {
      const almostOverdue = new Date('2024-01-15T14:25:00Z'); // 5分钟前

      expect(isOverdue(almostOverdue.toISOString(), 10)).toBe(false); // 10分钟缓冲
      expect(isOverdue(almostOverdue.toISOString(), 3)).toBe(true); // 3分钟缓冲
    });
  });

  describe('getTimeRemaining', () => {
    const now = new Date('2024-01-15T14:30:00Z');

    beforeEach(() => {
      jest.useFakeTimers();
      jest.setSystemTime(now);
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('应该正确计算剩余时间', () => {
      const futureTime = new Date('2024-01-15T15:30:00Z'); // 1小时后
      const remaining = getTimeRemaining(futureTime.toISOString());

      expect(remaining.hours).toBe(1);
      expect(remaining.minutes).toBe(0);
      expect(remaining.total).toBe(60); // 60分钟
    });

    it('应该处理过期时间', () => {
      const pastTime = new Date('2024-01-15T13:30:00Z'); // 1小时前
      const remaining = getTimeRemaining(pastTime.toISOString());

      expect(remaining.hours).toBe(0);
      expect(remaining.minutes).toBe(0);
      expect(remaining.total).toBe(0);
      expect(remaining.isOverdue).toBe(true);
    });

    it('应该正确计算复杂时间差', () => {
      const futureTime = new Date('2024-01-15T16:45:00Z'); // 2小时15分钟后
      const remaining = getTimeRemaining(futureTime.toISOString());

      expect(remaining.hours).toBe(2);
      expect(remaining.minutes).toBe(15);
      expect(remaining.total).toBe(135); // 135分钟
    });

    it('应该处理无效时间', () => {
      const remaining = getTimeRemaining('invalid-date');

      expect(remaining.hours).toBe(0);
      expect(remaining.minutes).toBe(0);
      expect(remaining.total).toBe(0);
      expect(remaining.isOverdue).toBe(false);
    });
  });

  describe('parseTimeString', () => {
    it('应该正确解析时间字符串', () => {
      expect(parseTimeString('14:30')).toEqual({ hours: 14, minutes: 30 });
      expect(parseTimeString('09:05')).toEqual({ hours: 9, minutes: 5 });
      expect(parseTimeString('23:59')).toEqual({ hours: 23, minutes: 59 });
    });

    it('应该处理无效格式', () => {
      expect(parseTimeString('invalid')).toEqual({ hours: 0, minutes: 0 });
      expect(parseTimeString('25:00')).toEqual({ hours: 0, minutes: 0 });
      expect(parseTimeString('12:60')).toEqual({ hours: 0, minutes: 0 });
    });

    it('应该处理边界值', () => {
      expect(parseTimeString('00:00')).toEqual({ hours: 0, minutes: 0 });
      expect(parseTimeString('12:00')).toEqual({ hours: 12, minutes: 0 });
    });
  });

  describe('addMinutes', () => {
    it('应该正确添加分钟', () => {
      const date = new Date('2024-01-15T14:30:00');
      const result = addMinutes(date, 30);

      expect(result.getMinutes()).toBe(0); // 30分钟后变成15:00
      expect(result.getHours()).toBe(15);
    });

    it('应该处理跨天的情况', () => {
      const date = new Date('2024-01-15T23:30:00');
      const result = addMinutes(date, 60);

      expect(result.getDate()).toBe(16); // 第二天
      expect(result.getHours()).toBe(0);
      expect(result.getMinutes()).toBe(30);
    });

    it('应该处理负数分钟', () => {
      const date = new Date('2024-01-15T14:30:00');
      const result = addMinutes(date, -30);

      expect(result.getHours()).toBe(14);
      expect(result.getMinutes()).toBe(0);
    });
  });

  describe('subtractMinutes', () => {
    it('应该正确减去分钟', () => {
      const date = new Date('2024-01-15T14:30:00');
      const result = subtractMinutes(date, 30);

      expect(result.getHours()).toBe(14);
      expect(result.getMinutes()).toBe(0);
    });

    it('应该处理跨天的情况', () => {
      const date = new Date('2024-01-15T00:30:00');
      const result = subtractMinutes(date, 60);

      expect(result.getDate()).toBe(14); // 前一天
      expect(result.getHours()).toBe(23);
      expect(result.getMinutes()).toBe(30);
    });
  });

  describe('isSameDay', () => {
    it('应该正确比较同一天的不同时间', () => {
      const date1 = new Date('2024-01-15T09:00:00');
      const date2 = new Date('2024-01-15T18:00:00');

      expect(isSameDay(date1, date2)).toBe(true);
    });

    it('应该正确比较不同天的时间', () => {
      const date1 = new Date('2024-01-15T23:59:00');
      const date2 = new Date('2024-01-16T00:01:00');

      expect(isSameDay(date1, date2)).toBe(false);
    });

    it('应该处理字符串输入', () => {
      const date1 = '2024-01-15T09:00:00';
      const date2 = '2024-01-15T18:00:00';

      expect(isSameDay(date1, date2)).toBe(true);
    });
  });

  describe('formatRelativeTime', () => {
    const now = new Date('2024-01-15T14:30:00Z');

    beforeEach(() => {
      jest.useFakeTimers();
      jest.setSystemTime(now);
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('应该正确格式化相对时间', () => {
      const oneHourAgo = new Date('2024-01-15T13:30:00Z');
      const oneHourLater = new Date('2024-01-15T15:30:00Z');

      expect(formatRelativeTime(oneHourAgo)).toBe('1小时前');
      expect(formatRelativeTime(oneHourLater)).toBe('1小时后');
    });

    it('应该处理分钟级别的时间差', () => {
      const thirtyMinutesAgo = new Date('2024-01-15T14:00:00Z');
      const thirtyMinutesLater = new Date('2024-01-15T15:00:00Z');

      expect(formatRelativeTime(thirtyMinutesAgo)).toBe('30分钟前');
      expect(formatRelativeTime(thirtyMinutesLater)).toBe('30分钟后');
    });

    it('应该处理天级别的时间差', () => {
      const yesterday = new Date('2024-01-14T14:30:00Z');
      const tomorrow = new Date('2024-01-16T14:30:00Z');

      expect(formatRelativeTime(yesterday)).toBe('1天前');
      expect(formatRelativeTime(tomorrow)).toBe('1天后');
    });

    it('应该处理当前时间', () => {
      expect(formatRelativeTime(now)).toBe('刚刚');
    });

    it('应该处理很小的时间差', () => {
      const fiveSecondsAgo = new Date('2024-01-15T14:29:55Z');

      expect(formatRelativeTime(fiveSecondsAgo)).toBe('刚刚');
    });
  });

  describe('性能测试', () => {
    it('应该在处理大量时间计算时保持性能', () => {
      const dates = Array.from(
        { length: 1000 },
        (_, i) => new Date(Date.now() + i * 60000) // 每分钟递增
      );

      const startTime = performance.now();

      dates.forEach(date => {
        formatTime(date);
        formatDuration(60);
        isOverdue(date.toISOString());
        getTimeRemaining(date.toISOString());
      });

      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // 100ms内完成
    });
  });
});
