/**
 * 配比检查标准类型定义
 */

/**
 * 配比检查标准接口
 */
export interface RatioCheckStandard {
  id: string;
  strength: string; // 强度等级，如"C30", "C35", "C40-P6"等，支持任意字符串
  category: string; // 类别，如"普通混凝土"、"高强混凝土"等

  // 水泥限制 (kg/m³)
  cementMin?: number;
  cementMax?: number;

  // 325水泥限制 (kg/m³)
  cement325Min?: number;
  cement325Max?: number;

  // 425水泥限制 (kg/m³)
  cement425Min?: number;
  cement425Max?: number;

  // 砂子限制 (kg/m³)
  sandMin?: number;
  sandMax?: number;

  // 石子限制 (kg/m³)
  stoneMin?: number;
  stoneMax?: number;

  // 外加剂限制 (kg/m³)
  additiveMin?: number;
  additiveMax?: number;

  // 粉煤灰限制 (kg/m³)
  flyAshMin?: number;
  flyAshMax?: number;

  // 矿粉限制 (kg/m³)
  mineralPowderMin?: number;
  mineralPowderMax?: number;

  // 水限制 (kg/m³)
  waterMin?: number;
  waterMax?: number;

  // 元数据
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  description?: string; // 标准描述
}

/**
 * 配比检查结果
 */
export interface RatioCheckResult {
  isCompliant: boolean; // 是否符合标准
  violations: RatioViolation[]; // 违规项目
  warnings: RatioWarning[]; // 警告项目
  appliedStandard?: RatioCheckStandard; // 应用的标准
}

/**
 * 配比违规项目
 */
export interface RatioViolation {
  field: string; // 违规字段
  fieldName: string; // 字段中文名
  currentValue: number; // 当前值
  minLimit?: number; // 最小限制
  maxLimit?: number; // 最大限制
  severity: 'error' | 'warning'; // 严重程度
  message: string; // 违规信息
}

/**
 * 配比警告项目
 */
export interface RatioWarning {
  field: string; // 警告字段
  fieldName: string; // 字段中文名
  currentValue: number; // 当前值
  recommendedMin?: number; // 推荐最小值
  recommendedMax?: number; // 推荐最大值
  message: string; // 警告信息
  suggestion?: string; // 改进建议
}

/**
 * 标准查询条件
 */
export interface StandardQueryCondition {
  strength?: string; // 强度等级
  category?: string; // 类别
  isActive?: boolean; // 是否激活
}

/**
 * 标准验证配置
 */
export interface StandardValidationConfig {
  enableStrict: boolean; // 是否启用严格模式
  autoApplyStandard: boolean; // 是否自动应用标准
  showWarnings: boolean; // 是否显示警告
  allowOverride: boolean; // 是否允许覆盖标准
}

/**
 * 默认标准配置
 */
export interface DefaultStandardConfig {
  standards: RatioCheckStandard[];
  validationConfig: StandardValidationConfig;
}

/**
 * 标准导入导出格式
 */
export interface StandardExportData {
  version: string;
  exportDate: string;
  standards: RatioCheckStandard[];
  metadata?: {
    source: string;
    description: string;
    [key: string]: any;
  };
}

/**
 * 字段映射配置
 */
export interface FieldMapping {
  field: string; // 字段名
  label: string; // 显示标签
  unit: string; // 单位
  category: 'cement' | 'aggregate' | 'additive' | 'water' | 'other'; // 分类
  required: boolean; // 是否必填
  defaultMin?: number; // 默认最小值
  defaultMax?: number; // 默认最大值
}
