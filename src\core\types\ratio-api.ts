/**
 * 配比API相关类型定义
 */

import type { UnifiedRatioMaterial, RatioCalculationParams, CalculationResults } from './ratio';

// ==================== API请求/响应类型 ====================

/**
 * 计算接口请求参数
 */
export interface CalculateRequest {
  taskId: string;
  calculationParams: RatioCalculationParams;
  materials: UnifiedRatioMaterial[];
  calculationMethod?: string;
}

/**
 * 计算接口响应
 */
export interface CalculateResponse {
  success: boolean;
  data: CalculationResults;
  message?: string;
  timestamp: string;
}

/**
 * 反算接口请求参数
 */
export interface ReverseCalculateRequest {
  taskId: string;
  materials: UnifiedRatioMaterial[];
  targetResults?: Partial<CalculationResults>;
  calculationMethod?: string;
}

/**
 * 反算接口响应
 */
export interface ReverseCalculateResponse {
  success: boolean;
  data: RatioCalculationParams;
  message?: string;
  timestamp: string;
}

/**
 * 配比保存请求参数
 */
export interface SaveRatioRequest {
  taskId: string;
  calculationParams: RatioCalculationParams;
  materials: UnifiedRatioMaterial[];
  calculationResults: CalculationResults;
  ratioName?: string;
  description?: string;
}

/**
 * 配比保存响应
 */
export interface SaveRatioResponse {
  success: boolean;
  data: {
    ratioId: string;
    version: number;
  };
  message?: string;
  timestamp: string;
}

/**
 * 获取配比请求参数
 */
export interface GetRatioRequest {
  taskId: string;
  ratioId?: string;
  version?: number;
}

/**
 * 获取配比响应
 */
export interface GetRatioResponse {
  success: boolean;
  data: {
    taskId: string;
    ratioId: string;
    version: number;
    calculationParams: RatioCalculationParams;
    materials: UnifiedRatioMaterial[];
    calculationResults: CalculationResults;
    ratioName?: string;
    description?: string;
    createdAt: string;
    updatedAt: string;
  };
  message?: string;
  timestamp: string;
}

/**
 * 配比历史记录
 */
export interface RatioHistoryItem {
  ratioId: string;
  version: number;
  ratioName?: string;
  description?: string;
  createdAt: string;
  createdBy: string;
  operation: 'create' | 'update' | 'calculate' | 'reverse_calculate';
  changes?: string[];
}

/**
 * 获取配比历史响应
 */
export interface GetRatioHistoryResponse {
  success: boolean;
  data: RatioHistoryItem[];
  message?: string;
  timestamp: string;
}

// ==================== API错误类型 ====================

/**
 * API错误响应
 */
export interface ApiErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

/**
 * 配比API错误代码
 */
export enum RatioApiErrorCodes {
  INVALID_PARAMETERS = 'INVALID_PARAMETERS',
  CALCULATION_FAILED = 'CALCULATION_FAILED',
  REVERSE_CALCULATION_FAILED = 'REVERSE_CALCULATION_FAILED',
  RATIO_NOT_FOUND = 'RATIO_NOT_FOUND',
  SAVE_FAILED = 'SAVE_FAILED',
  MATERIALS_INVALID = 'MATERIALS_INVALID',
  TASK_NOT_FOUND = 'TASK_NOT_FOUND',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  SERVER_ERROR = 'SERVER_ERROR',
}

// ==================== Mock数据类型 ====================

/**
 * Mock配比数据
 */
export interface MockRatioData {
  taskId: string;
  ratioId: string;
  version: number;
  calculationParams: RatioCalculationParams;
  materials: UnifiedRatioMaterial[];
  calculationResults: CalculationResults;
  ratioName?: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Mock计算结果
 */
export interface MockCalculationResult {
  totalWeight: number;
  materials: Record<string, number>;
  strengthPrediction: number;
  qualityScore: number;
  warnings: string[];
  suggestions: string[];
  carbonFootprint: number;
  costEstimate: number;
}

// ==================== API配置类型 ====================

/**
 * 配比API配置
 */
export interface RatioApiConfig {
  baseUrl: string;
  endpoints: {
    calculate: string;
    reverseCalculate: string;
    saveRatio: string;
    getRatio: string;
    getRatioHistory: string;
  };
  timeout: number;
  retries: number;
  useMock: boolean;
}

// ==================== 导出所有类型 ====================

// 所有类型已通过 export interface/enum 直接导出，无需重复导出
