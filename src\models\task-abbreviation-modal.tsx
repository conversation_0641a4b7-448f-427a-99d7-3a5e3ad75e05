'use client';

import React, { useEffect, useState } from 'react';

import { FileText } from 'lucide-react';

import { Button } from '@/shared/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import { useToast } from '@/shared/hooks/use-toast';
import type { Task } from '@/core/types';

interface TaskAbbreviationModalProps {
  isOpen: boolean;
  onOpenChangeAction: (isOpen: boolean) => void;
  task: Task | null;
  onSaveAction: (taskId: string, abbreviation: string) => Promise<void>;
}

export function TaskAbbreviationModal({
  isOpen,
  onOpenChangeAction,
  task,
  onSaveAction,
}: TaskAbbreviationModalProps) {
  const [abbreviation, setAbbreviation] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // 初始化缩写
  useEffect(() => {
    if (task && isOpen) {
      setAbbreviation(task.projectAbbreviation || '');
    }
  }, [task, isOpen]);

  const handleSave = async () => {
    if (!task || !abbreviation.trim()) return;

    setIsLoading(true);
    try {
      await onSaveAction(task.id, abbreviation.trim());
      toast({
        title: '保存成功',
        description: '任务缩写已更新',
      });
      onOpenChangeAction(false);
    } catch (error) {
      toast({
        title: '保存失败',
        description: '请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    onOpenChangeAction(false);
  };

  if (!task) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChangeAction}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            任务缩写设置
          </DialogTitle>
          <DialogDescription>设置任务的显示缩写，用于在界面中简化显示</DialogDescription>
        </DialogHeader>

        <div className='grid gap-4 py-4'>
          {/* 第一行：任务缩写设置输入框 */}
          <div className='grid gap-2'>
            <Label htmlFor='abbreviation'>任务缩写设置</Label>
            <Input
              id='abbreviation'
              value={abbreviation}
              onChange={e => setAbbreviation(e.target.value)}
              placeholder='请输入任务缩写'
              maxLength={20}
              disabled={isLoading}
            />
          </div>

          {/* 第二行：工程名称（不可编辑） */}
          <div className='grid gap-2'>
            <Label htmlFor='projectName'>工程名称</Label>
            <Input id='projectName' value={task.projectName || ''} readOnly className='bg-muted' />
          </div>

          {/* 第三行：施工部位（不可编辑） */}
          <div className='grid gap-2'>
            <Label htmlFor='constructionSite'>施工部位</Label>
            <Input
              id='constructionSite'
              value={task.constructionSite || task.constructionLocation || ''}
              readOnly
              className='bg-muted'
            />
          </div>

          {/* 第四行：施工单位（不可编辑） */}
          <div className='grid gap-2'>
            <Label htmlFor='constructionUnit'>施工单位</Label>
            <Input
              id='constructionUnit'
              value={task.constructionUnit || task.constructionCompany || ''}
              readOnly
              className='bg-muted'
            />
          </div>
        </div>

        <DialogFooter className='flex justify-end gap-2'>
          <Button variant='outline' onClick={handleCancel} disabled={isLoading}>
            取消
          </Button>
          <Button onClick={handleSave} disabled={!abbreviation.trim() || isLoading}>
            {isLoading ? '保存中...' : '确定'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
