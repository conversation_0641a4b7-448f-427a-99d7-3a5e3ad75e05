'use client';

import { <PERSON><PERSON> } from '@/shared/components/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { ScrollArea } from '@/shared/components/scroll-area';
import type { Task } from '@/core/types';
import React from 'react';

// 车辆出车记录接口
export interface VehicleDispatchRecord {
  id: string;
  vehicleNumber: string; // 车号
  driver: string; // 司机
  cargo: string; // 货物
  setting: number; // 设定
  volume: number; // 方量
  departureTime: string; // 发车时间
  returnTime: string; // 返回时间
  duration: string; // 用时
  returnConcrete: string; // 退灰
  remarks: string; // 备注
}

// 出车统计信息接口
export interface VehicleDispatchStats {
  totalVehicles: number; // 总车数
  production: number; // 生产
  signed: number; // 签收
  actualDelivery: number; // 实发
  averageTime: string; // 平均用时
}

interface VehicleDispatchModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task: Task | null;
  dispatchRecords: VehicleDispatchRecord[];
  stats: VehicleDispatchStats;
}

export const VehicleDispatchModal: React.FC<VehicleDispatchModalProps> = ({
  open,
  onOpenChange,
  task,
  dispatchRecords,
  stats,
}) => {
  if (!task) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-6xl max-h-[90vh] overflow-hidden p-0 [&>button]:hidden'>
        <DialogHeader className='px-4 py-3 border-b bg-gray-50'>
          <div className='flex items-center justify-between'>
            <DialogTitle className='text-lg font-medium'>
              单任务出车情况 - {task.projectName || '个人自建'}
            </DialogTitle>
          </div>
        </DialogHeader>

        <div className='p-4'>
          {/* 统计信息 */}
          <div className='mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200'>
            <div className='flex items-center gap-6 text-sm'>
              <span className='font-medium text-blue-800'>
                共计: <span className='text-red-600 font-bold'>{stats.totalVehicles}</span>车
              </span>
              <span className='text-blue-700'>
                生产: <span className='font-medium'>{stats.production}</span>
              </span>
              <span className='text-blue-700'>
                签收: <span className='font-medium'>{stats.signed}</span>
              </span>
              <span className='text-blue-700'>
                实发: <span className='font-medium'>{stats.actualDelivery}</span>
              </span>
              <span className='text-blue-700'>
                平均用时: <span className='font-medium text-red-600'>{stats.averageTime}</span>
              </span>
            </div>
          </div>

          {/* 车辆出车记录表格 */}
          <ScrollArea className='h-[500px] border rounded-lg'>
            <div className='min-w-full'>
              <table className='w-full text-xs border-collapse'>
                <thead className='bg-gray-100 sticky top-0 z-10'>
                  <tr>
                    <th className='border border-gray-300 px-2 py-2 text-center font-medium w-16'>
                      项目
                    </th>
                    <th className='border border-gray-300 px-2 py-2 text-center font-medium w-20'>
                      车号
                    </th>
                    <th className='border border-gray-300 px-2 py-2 text-center font-medium w-20'>
                      司机
                    </th>
                    <th className='border border-gray-300 px-2 py-2 text-center font-medium w-16'>
                      货物
                    </th>
                    <th className='border border-gray-300 px-2 py-2 text-center font-medium w-16'>
                      设定
                    </th>
                    <th className='border border-gray-300 px-2 py-2 text-center font-medium w-16'>
                      方量
                    </th>
                    <th className='border border-gray-300 px-2 py-2 text-center font-medium w-32'>
                      发车
                    </th>
                    <th className='border border-gray-300 px-2 py-2 text-center font-medium w-32'>
                      返回
                    </th>
                    <th className='border border-gray-300 px-2 py-2 text-center font-medium w-24'>
                      用时
                    </th>
                    <th className='border border-gray-300 px-2 py-2 text-center font-medium w-16'>
                      退灰
                    </th>
                    <th className='border border-gray-300 px-2 py-2 text-center font-medium w-24'>
                      备注
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {dispatchRecords.map((record, index) => (
                    <tr key={record.id} className='hover:bg-gray-50'>
                      <td className='border border-gray-300 px-2 py-1 text-center'>{index + 1}</td>
                      <td className='border border-gray-300 px-2 py-1 text-center'>
                        {record.vehicleNumber}
                      </td>
                      <td className='border border-gray-300 px-2 py-1 text-center'>
                        {record.driver}
                      </td>
                      <td className='border border-gray-300 px-2 py-1 text-center'>
                        {record.cargo}
                      </td>
                      <td className='border border-gray-300 px-2 py-1 text-center'>
                        {record.setting}
                      </td>
                      <td className='border border-gray-300 px-2 py-1 text-center'>
                        {record.volume}
                      </td>
                      <td className='border border-gray-300 px-2 py-1 text-center text-xs'>
                        {record.departureTime}
                      </td>
                      <td className='border border-gray-300 px-2 py-1 text-center text-xs'>
                        {record.returnTime}
                      </td>
                      <td className='border border-gray-300 px-2 py-1 text-center'>
                        {record.duration}
                      </td>
                      <td className='border border-gray-300 px-2 py-1 text-center'>
                        {record.returnConcrete}
                      </td>
                      <td className='border border-gray-300 px-2 py-1 text-center'>
                        {record.remarks}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </ScrollArea>

          {/* 底部按钮 */}
          <div className='flex justify-end mt-4'>
            <Button
              variant='outline'
              onClick={() => onOpenChange(false)}
              className='h-8 px-4 text-sm'
            >
              关闭
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
