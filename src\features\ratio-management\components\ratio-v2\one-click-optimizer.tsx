'use client';

import React, { useState } from 'react';

import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  ArrowRight,
  CheckCircle,
  DollarSign,
  Gauge,
  Info,
  Shield,
  TrendingDown,
  TrendingUp,
  Zap,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { <PERSON><PERSON> } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Checkbox } from '@/shared/components/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Label } from '@/shared/components/label';
import { useToast } from '@/shared/hooks/use-toast';
import { cn } from '@/core/lib/utils';

interface OptimizationAction {
  id: string;
  type: 'adjust_parameter' | 'adjust_material';
  description: string;
  parameterName?: string;
  materialName?: string;
  currentValue: number;
  suggestedValue: number;
  reason: string;
  impact: string;
  priority: 'high' | 'medium' | 'low';
}

interface OneClickOptimizerProps {
  isOpen: boolean;
  onClose: () => void;
  optimizations: OptimizationAction[];
  onApplyOptimizations: (selectedIds: string[]) => void;
  currentQualityScore: number;
}

export function OneClickOptimizer({
  isOpen,
  onClose,
  optimizations,
  onApplyOptimizations,
  currentQualityScore,
}: OneClickOptimizerProps) {
  const { toast } = useToast();
  const [selectedOptimizations, setSelectedOptimizations] = useState<string[]>([]);
  const [isApplying, setIsApplying] = useState(false);

  // 按优先级分组
  const groupedOptimizations = {
    high: optimizations.filter(opt => opt.priority === 'high'),
    medium: optimizations.filter(opt => opt.priority === 'medium'),
    low: optimizations.filter(opt => opt.priority === 'low'),
  };

  const handleOptimizationToggle = (optimizationId: string, checked: boolean) => {
    setSelectedOptimizations(prev =>
      checked ? [...prev, optimizationId] : prev.filter(id => id !== optimizationId)
    );
  };

  const handleSelectAll = (priority: 'high' | 'medium' | 'low') => {
    const priorityIds = groupedOptimizations[priority].map(opt => opt.id);
    const allSelected = priorityIds.every(id => selectedOptimizations.includes(id));

    if (allSelected) {
      setSelectedOptimizations(prev => prev.filter(id => !priorityIds.includes(id)));
    } else {
      setSelectedOptimizations(prev => [...new Set([...prev, ...priorityIds])]);
    }
  };

  const handleApplyOptimizations = async () => {
    if (selectedOptimizations.length === 0) {
      toast({
        title: '请选择优化项',
        description: '至少需要选择一个优化建议',
        variant: 'destructive',
      });
      return;
    }

    setIsApplying(true);

    // 模拟应用过程
    await new Promise(resolve => setTimeout(resolve, 1500));

    onApplyOptimizations(selectedOptimizations);
    setIsApplying(false);
    onClose();

    toast({
      title: '优化完成',
      description: `已应用 ${selectedOptimizations.length} 项优化建议`,
    });
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <AlertTriangle className='h-4 w-4 text-red-500' />;
      case 'medium':
        return <Info className='h-4 w-4 text-yellow-500' />;
      case 'low':
        return <CheckCircle className='h-4 w-4 text-green-500' />;
      default:
        return <Info className='h-4 w-4' />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-red-200 bg-red-50';
      case 'medium':
        return 'border-yellow-200 bg-yellow-50';
      case 'low':
        return 'border-green-200 bg-green-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getChangeIcon = (current: number, suggested: number) => {
    if (suggested > current) {
      return <TrendingUp className='h-4 w-4 text-green-600' />;
    } else if (suggested < current) {
      return <TrendingDown className='h-4 w-4 text-red-600' />;
    }
    return <ArrowRight className='h-4 w-4 text-gray-600' />;
  };

  const formatValue = (value: number, type: string) => {
    if (type === 'adjust_parameter') {
      return value.toFixed(2);
    }
    return Math.round(value).toString();
  };

  const getImpactIcon = (impact: string) => {
    if (impact.includes('强度')) return <Gauge className='h-4 w-4 text-blue-500' />;
    if (impact.includes('成本')) return <DollarSign className='h-4 w-4 text-green-500' />;
    if (impact.includes('耐久')) return <Shield className='h-4 w-4 text-purple-500' />;
    return <Zap className='h-4 w-4 text-orange-500' />;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-5xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Zap className='h-6 w-6 text-orange-500' />
            一键优化配比
            <Badge variant='outline' className='ml-2'>
              当前评分: {currentQualityScore}/100
            </Badge>
          </DialogTitle>
        </DialogHeader>

        {optimizations.length === 0 ? (
          <div className='text-center py-12'>
            <CheckCircle className='h-16 w-16 text-green-500 mx-auto mb-4' />
            <h3 className='text-lg font-semibold mb-2'>配比已经很优秀！</h3>
            <p className='text-muted-foreground'>当前配比质量评分较高，暂无需要优化的项目</p>
          </div>
        ) : (
          <div className='space-y-6'>
            {/* 优化概览 */}
            <Card>
              <CardHeader className='pb-3'>
                <CardTitle className='text-lg'>优化概览</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='grid grid-cols-3 gap-4 text-center'>
                  <div className='p-3 bg-red-50 rounded-lg border border-red-200'>
                    <div className='text-2xl font-bold text-red-600'>
                      {groupedOptimizations.high.length}
                    </div>
                    <div className='text-sm text-red-600'>高优先级</div>
                  </div>
                  <div className='p-3 bg-yellow-50 rounded-lg border border-yellow-200'>
                    <div className='text-2xl font-bold text-yellow-600'>
                      {groupedOptimizations.medium.length}
                    </div>
                    <div className='text-sm text-yellow-600'>中优先级</div>
                  </div>
                  <div className='p-3 bg-green-50 rounded-lg border border-green-200'>
                    <div className='text-2xl font-bold text-green-600'>
                      {groupedOptimizations.low.length}
                    </div>
                    <div className='text-sm text-green-600'>低优先级</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 优化建议列表 */}
            {(['high', 'medium', 'low'] as const).map(priority => {
              const priorityOptimizations = groupedOptimizations[priority];
              if (priorityOptimizations.length === 0) return null;

              const priorityLabels = {
                high: '高优先级建议',
                medium: '中优先级建议',
                low: '低优先级建议',
              };

              return (
                <Card key={priority}>
                  <CardHeader className='pb-3'>
                    <div className='flex items-center justify-between'>
                      <CardTitle className='text-lg flex items-center gap-2'>
                        {getPriorityIcon(priority)}
                        {priorityLabels[priority]}
                      </CardTitle>
                      <Button variant='outline' size='sm' onClick={() => handleSelectAll(priority)}>
                        {priorityOptimizations.every(opt => selectedOptimizations.includes(opt.id))
                          ? '取消全选'
                          : '全选'}
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-3'>
                      {priorityOptimizations.map(optimization => (
                        <div
                          key={optimization.id}
                          className={cn(
                            'p-4 border rounded-lg transition-all duration-200',
                            getPriorityColor(priority),
                            selectedOptimizations.includes(optimization.id) && 'ring-2 ring-primary'
                          )}
                        >
                          <div className='flex items-start space-x-3'>
                            <Checkbox
                              id={optimization.id}
                              checked={selectedOptimizations.includes(optimization.id)}
                              onCheckedChange={checked =>
                                handleOptimizationToggle(optimization.id, !!checked)
                              }
                              className='mt-1'
                            />
                            <div className='flex-1 space-y-2'>
                              <div className='flex items-center justify-between'>
                                <Label
                                  htmlFor={optimization.id}
                                  className='font-medium cursor-pointer'
                                >
                                  {optimization.description}
                                </Label>
                                <Badge variant='outline' className='text-xs'>
                                  {optimization.type === 'adjust_parameter'
                                    ? '参数调整'
                                    : '材料调整'}
                                </Badge>
                              </div>

                              <div className='flex items-center gap-4 text-sm'>
                                <div className='flex items-center gap-1'>
                                  {getChangeIcon(
                                    optimization.currentValue,
                                    optimization.suggestedValue
                                  )}
                                  <span className='text-muted-foreground'>
                                    {formatValue(optimization.currentValue, optimization.type)}
                                  </span>
                                  <ArrowRight className='h-3 w-3 text-muted-foreground' />
                                  <span className='font-medium'>
                                    {formatValue(optimization.suggestedValue, optimization.type)}
                                  </span>
                                </div>
                              </div>

                              <div className='text-sm text-muted-foreground'>
                                <p className='mb-1'>
                                  <strong>原因:</strong> {optimization.reason}
                                </p>
                                <div className='flex items-center gap-1'>
                                  {getImpactIcon(optimization.impact)}
                                  <strong>影响:</strong> {optimization.impact}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* 底部操作 */}
        {optimizations.length > 0 && (
          <div className='flex justify-between items-center pt-4 border-t'>
            <div className='text-sm text-muted-foreground'>
              已选择 {selectedOptimizations.length} 项优化建议
            </div>
            <div className='flex gap-2'>
              <Button variant='outline' onClick={onClose}>
                取消
              </Button>
              <Button
                onClick={handleApplyOptimizations}
                disabled={isApplying || selectedOptimizations.length === 0}
                className='gap-2'
              >
                {isApplying ? (
                  <>
                    <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
                    应用中...
                  </>
                ) : (
                  <>
                    <Zap className='h-4 w-4' />
                    应用优化
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
