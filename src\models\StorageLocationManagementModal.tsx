'use client';

import React, { useEffect, useState } from 'react';

import { Button } from '@/shared/components/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/table';
import { mockStorageLocations } from '@/infrastructure/api/mock/mock-data';
import type { StorageLocation } from '@/core/types';

interface StorageLocationManagementModalProps {
  isOpen: boolean;
  onOpenChangeAction: (open: boolean) => void;
}

export const StorageLocationManagementModal: React.FC<StorageLocationManagementModalProps> = ({
  isOpen,
  onOpenChangeAction,
}) => {
  const [locations, setLocations] = useState<StorageLocation[]>([]);

  useEffect(() => {
    if (isOpen) {
      setLocations(mockStorageLocations);
    }
  }, [isOpen]);

  const handleUpdate = (id: string, field: keyof StorageLocation, value: string | number) => {
    setLocations(locations.map(loc => (loc.id === id ? { ...loc, [field]: value } : loc)));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChangeAction}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <DialogTitle>存放地名称管理</DialogTitle>
        </DialogHeader>
        <div className='py-4 max-h-[60vh] overflow-y-auto'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className='w-[120px]'>罐标识</TableHead>
                <TableHead>罐名称</TableHead>
                <TableHead className='w-[100px]'>显示次序</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {locations.map(loc => (
                <TableRow key={loc.id}>
                  <TableCell>{loc.binId}</TableCell>
                  <TableCell>
                    <Input
                      value={loc.binName}
                      onChange={e => handleUpdate(loc.id, 'binName', e.target.value)}
                      className='h-8'
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type='number'
                      value={loc.order}
                      onChange={e => handleUpdate(loc.id, 'order', parseInt(e.target.value) || 0)}
                      className='h-8'
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <DialogFooter>
          <Button variant='outline' onClick={() => onOpenChangeAction(false)}>
            关闭
          </Button>
          <Button>保存</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
