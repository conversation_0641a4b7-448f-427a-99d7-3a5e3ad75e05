'use client';

/**
 * 配比Store协调器
 * 统一管理配比相关的所有Store，提供统一的接口
 */

import { useEffect } from 'react';
import { useRatioDataStore } from './ratioDataStore';
import { useRatioV1UIStore } from './ratioV1UIStore';
import type {
  RatioCalculationParams,
  ReverseCalculationParams,
  RatioResult,
  UnifiedRatioMaterial,
  RatioCalculationMethod,
  RatioActions,
} from '@/core/types/ratio';

/**
 * 配比Store协调器Hook
 * 提供统一的配比状态管理接口
 */
export function useRatioStoreOrchestrator() {
  // 获取各个Store的状态和操作
  const dataStore = useRatioDataStore();
  const uiStore = useRatioV1UIStore();

  // 数据同步：当计算参数变化时，标记表单为脏状态
  // 注意：移除 calculationParams 依赖，避免无限循环
  useEffect(() => {
    if (dataStore.isInitialized) {
      uiStore.setDirty(true);
    }
  }, [dataStore.isInitialized, uiStore.setDirty]);

  // 错误处理：当有错误时，停止加载状态
  useEffect(() => {
    if (uiStore.error) {
      uiStore.setLoading(false);
      uiStore.setCalculating(false);
      uiStore.setSaving(false);
    }
  }, [uiStore.error, uiStore.setLoading, uiStore.setCalculating, uiStore.setSaving]);

  // 统一的操作方法
  const actions: RatioActions = {
    // 初始化
    initialize: (task: any) => {
      uiStore.setLoading(true);
      uiStore.clearError();
      try {
        dataStore.initialize(task);
        uiStore.setDirty(false);
      } catch (error) {
        uiStore.setError(error instanceof Error ? error.message : '初始化失败');
      } finally {
        uiStore.setLoading(false);
      }
    },

    // 参数设置
    setCalculationParams: (params: Partial<RatioCalculationParams>) => {
      dataStore.setCalculationParams(params);
      uiStore.setDirty(true);
    },

    setReverseParam: (key: keyof ReverseCalculationParams, value: number) => {
      dataStore.setReverseParam(key, value);
      uiStore.setDirty(true);
    },

    setCalculationMethod: (method: RatioCalculationMethod) => {
      dataStore.setCalculationMethod(method);
      uiStore.setDirty(true);
    },

    // 计算操作
    calculate: async () => {
      uiStore.setCalculating(true);
      uiStore.clearError();

      try {
        // 模拟计算过程
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 基于当前参数计算配比结果
        const params = dataStore.calculationParams;
        const newProportions: RatioResult = {
          materials: {},
          totalVolume: 1,
          density: params.density,
          water: params.waterAmount,
          cement: params.cementAmount,
          flyAsh: params.flyAshAmount,
          mineralPowder: params.mineralPowderAmount,
          s105Powder: params.s105PowderAmount,
          expansionAgent: params.expansionAgentAmount,
          earlyStrengthAgent: params.earlyStrengthAgentAmount,
          sand: Math.round((params.density * params.sandRatio) / 100),
          stone: Math.round((params.density * (100 - params.sandRatio)) / 100),
          gravel: Math.round((params.density * (100 - params.sandRatio)) / 100),
          admixture: params.admixtureAmount,
          antifreeze: params.antifreezeAmount,
          ultraFineSand: params.ultraFineSandAmount,
          totalWeight: 0,
        };

        // 计算总重量
        newProportions.totalWeight =
          newProportions.water +
          newProportions.cement +
          newProportions.flyAsh +
          newProportions.mineralPowder +
          newProportions.sand +
          newProportions.stone +
          newProportions.admixture;

        dataStore.setProportions(newProportions);
        uiStore.setDirty(true);
      } catch (error) {
        uiStore.setError(error instanceof Error ? error.message : '计算失败');
      } finally {
        uiStore.setCalculating(false);
      }
    },

    reverseCalculate: async () => {
      uiStore.setCalculating(true);
      uiStore.clearError();

      try {
        // 模拟反算过程
        await new Promise(resolve => setTimeout(resolve, 800));

        // 基于反算参数调整计算参数
        const reverseParams = dataStore.reverseParams;
        const currentParams = dataStore.calculationParams;

        const adjustedParams: Partial<RatioCalculationParams> = {
          ...currentParams,
          flyAshAmount: (currentParams.cementAmount * reverseParams.cementSubstitutionRate) / 100,
          mineralPowderAmount: currentParams.cementAmount * 0.1, // 示例调整
        };

        dataStore.setCalculationParams(adjustedParams);

        // 重新计算
        await actions.calculate();
      } catch (error) {
        uiStore.setError(error instanceof Error ? error.message : '反算失败');
      } finally {
        uiStore.setCalculating(false);
      }
    },

    // 材料管理
    addMaterial: () => {
      return dataStore.addMaterial();
    },

    updateMaterial: (id: string, material: Partial<UnifiedRatioMaterial>) => {
      dataStore.updateMaterial(id, material);
      uiStore.setDirty(true);
    },

    deleteMaterial: (id: string) => {
      dataStore.deleteMaterial(id);
      uiStore.setDirty(true);
    },

    // UI操作
    setActiveModal: (modal: string | null) => {
      uiStore.setActiveModal(modal);
    },

    setLoading: (loading: boolean) => {
      uiStore.setLoading(loading);
    },

    setError: (error: string | null) => {
      uiStore.setError(error);
    },
  };

  // 保存操作
  const saveRatio = async () => {
    uiStore.setSaving(true);
    uiStore.clearError();

    try {
      // 模拟保存过程
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 保存成功后标记为非脏状态
      uiStore.setDirty(false);

      return true;
    } catch (error) {
      uiStore.setError(error instanceof Error ? error.message : '保存失败');
      return false;
    } finally {
      uiStore.setSaving(false);
    }
  };

  // 提交操作
  const submitRatio = async () => {
    if (!uiStore.isValid) {
      uiStore.setError('请检查表单数据的有效性');
      return false;
    }

    uiStore.setSaving(true);
    uiStore.clearError();

    try {
      // 先保存
      const saveSuccess = await saveRatio();
      if (!saveSuccess) {
        return false;
      }

      // 模拟提交过程
      await new Promise(resolve => setTimeout(resolve, 1000));

      return true;
    } catch (error) {
      uiStore.setError(error instanceof Error ? error.message : '提交失败');
      return false;
    } finally {
      uiStore.setSaving(false);
    }
  };

  return {
    // 数据状态
    data: {
      task: dataStore.task,
      calculationParams: dataStore.calculationParams,
      reverseParams: dataStore.reverseParams,
      proportions: dataStore.proportions,
      materials: dataStore.materials,
      calculationMethod: dataStore.calculationMethod,
      isInitialized: dataStore.isInitialized,
    },

    // UI状态
    ui: {
      activeModal: uiStore.activeModal,
      isLoading: uiStore.isLoading,
      isCalculating: uiStore.isCalculating,
      isSaving: uiStore.isSaving,
      error: uiStore.error,
      isDirty: uiStore.isDirty,
      isValid: uiStore.isValid,
      showAdvancedOptions: uiStore.showAdvancedOptions,
      showCalculationDetails: uiStore.showCalculationDetails,
    },

    // 统一操作方法
    actions,

    // 高级操作
    saveRatio,
    submitRatio,

    // 工具方法
    utils: {
      canOperate: !uiStore.isLoading && !uiStore.isCalculating && !uiStore.isSaving,
      hasError: !!uiStore.error,
      hasModalOpen: !!uiStore.activeModal,
      canSave: uiStore.isDirty && uiStore.isValid && !uiStore.isSaving,
      canSubmit: uiStore.isValid && !uiStore.isCalculating,
    },

    // Store实例（用于高级用法）
    stores: {
      data: dataStore,
      ui: uiStore,
    },
  };
}
