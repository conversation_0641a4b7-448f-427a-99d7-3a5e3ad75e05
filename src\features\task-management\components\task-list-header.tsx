// src/components/sections/task-list/components/task-list-header.tsx
'use client';

import {
  BarChart3,
  CheckCircle2,
  ChevronDown,
  Columns,
  FileDown,
  FileUp,
  Grid3X3,
  LayoutGrid,
  MoreVertical,
  Palette,
  RotateCcw,
  Rows,
  Settings,
  Settings2,
  WrapText,
  AlignLeft,
} from 'lucide-react';

import { Button } from '@/shared/components/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/shared/components/dropdown-menu';
import type {
  TaskGroupConfig,
  TaskListDensityMode,
  TaskListDisplayMode,
  VehicleDisplayMode,
  TaskListRowDisplayMode,
} from '@/core/types';

import { getGroupingOptions } from '@/core/utils/task-grouping';
import {
  getTaskColumnId,
  getTaskListDensityMode,
  getTaskListDisplayMode,
} from './task-list-type-guards';
import { ALL_TASK_COLUMNS_CONFIG } from './task-list.config';
import { TaskListHeaderToggle } from './task-list-header-toggle';

export interface TaskListHeaderProps {
  // Display mode
  displayMode: TaskListDisplayMode;
  onDisplayModeChange: (mode: TaskListDisplayMode) => void;

  // Density mode
  densityMode: Exclude<TaskListDensityMode, 'table' | 'card'>;
  onDensityModeChange: (mode: Exclude<TaskListDensityMode, 'table' | 'card'>) => void;

  // Vehicle display mode
  vehicleDisplayMode: VehicleDisplayMode;
  onVehicleDisplayModeChange: (mode: VehicleDisplayMode) => void;

  // Row display mode for table view
  rowDisplayMode?: TaskListRowDisplayMode; // 新增：行显示模式
  onRowDisplayModeChange?: (mode: TaskListRowDisplayMode) => void; // 新增：行显示模式切换处理函数

  // Grouping
  groupConfig: TaskGroupConfig;
  onToggleGrouping: () => void;
  onCancelGrouping: () => void;
  onSetGroupBy?: (groupBy: TaskGroupConfig['groupBy']) => void;

  // Column management
  columnVisibility: Record<string, boolean>;
  onColumnVisibilityChange: (columnId: string, checked: boolean) => void;

  // Actions
  onOpenStyleEditor: () => void;
  onOpenCardConfig: () => void;
  onExportData: () => void;
  onImportData: () => void;
  onResetSettings: () => void;

  // State
  isLoadingPlantInfo: boolean;
  taskCount: number;
  filteredTaskCount: number;

  // Header mode switching (optional)
  onSwitchToFloatingHeader?: () => void;
}

export function TaskListHeader({
  displayMode,
  onDisplayModeChange,
  densityMode,
  onDensityModeChange,
  vehicleDisplayMode,
  onVehicleDisplayModeChange,
  rowDisplayMode = 'row', // 默认为行内模式
  onRowDisplayModeChange,
  groupConfig,
  onToggleGrouping,
  onCancelGrouping,
  onSetGroupBy,
  columnVisibility,
  onColumnVisibilityChange,
  onOpenStyleEditor,
  onOpenCardConfig,
  onExportData,
  onImportData,
  onResetSettings,
  isLoadingPlantInfo,
  taskCount,
  filteredTaskCount,
  onSwitchToFloatingHeader,
}: TaskListHeaderProps) {
  return (
    <div className='flex items-center justify-between p-0.5 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
      {/* Left side - Display mode and grouping */}
      <div className='flex items-center gap-2'>
        {/* Display Mode Toggle */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='outline' size='sm' className='h-8'>
              {displayMode === 'table' ? (
                <Rows className='h-4 w-4' />
              ) : (
                <LayoutGrid className='h-4 w-4' />
              )}
              <ChevronDown className='h-3 w-3 ml-1' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='start'>
            <DropdownMenuLabel>显示模式</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuRadioGroup
              value={displayMode}
              onValueChange={value => onDisplayModeChange(getTaskListDisplayMode(value))}
            >
              <DropdownMenuRadioItem value='table'>
                <Rows className='h-4 w-4 mr-2' />
                表格视图
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value='card'>
                <LayoutGrid className='h-4 w-4 mr-2' />
                卡片视图
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Row Display Mode Toggle (only for table view) */}
        {displayMode === 'table' && onRowDisplayModeChange && (
          <Button 
            variant='outline' 
            size='sm' 
            className='h-8'
            onClick={() => {
              console.log('Row display mode button clicked. Current mode:', rowDisplayMode);
              const newMode = rowDisplayMode === 'row' ? 'stacked' : 'row';
              console.log('New mode will be:', newMode);
              onRowDisplayModeChange(newMode);
            }}
            title={rowDisplayMode === 'row' ? '切换到上下模式' : '切换到行内模式'}
          >
            {rowDisplayMode === 'row' ? (
              <>
                <AlignLeft className='h-4 w-4' />
                <span className='ml-1'>行内</span>
              </>
            ) : (
              <>
                <WrapText className='h-4 w-4' />
                <span className='ml-1'>上下</span>
              </>
            )}
          </Button>
        )}

        {/* Density Mode (for table view) */}
        {displayMode === 'table' && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='outline' size='sm' className='h-8'>
                <Grid3X3 className='h-4 w-4' />
                <ChevronDown className='h-3 w-3 ml-1' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='start'>
              <DropdownMenuLabel>密度</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuRadioGroup
                value={densityMode}
                onValueChange={value => onDensityModeChange(getTaskListDensityMode(value))}
              >
                <DropdownMenuRadioItem value='loose'>宽松</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value='normal'>正常</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value='compact'>紧凑</DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Grouping Controls - 卡片和表格模式都显示 */}
        {(displayMode === 'card' || displayMode === 'table') && (
          <div className='flex items-center gap-1'>
            <Button
              variant={groupConfig.enabled ? 'default' : 'outline'}
              size='sm'
              className='h-8'
              onClick={onToggleGrouping}
            >
              <CheckCircle2 className='h-4 w-4' />
              {groupConfig.enabled ? '已分组' : '分组'}
            </Button>

            {groupConfig.enabled && onSetGroupBy && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant='outline' size='sm' className='h-8'>
                    <BarChart3 className='h-4 w-4' />
                    分组字段
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='start' className='w-56'>
                  <DropdownMenuLabel>选择分组字段</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {getGroupingOptions(groupConfig).map((option: any) => (
                    <DropdownMenuCheckboxItem
                      key={option.value}
                      checked={groupConfig.groupBy === option.value}
                      onCheckedChange={() => onSetGroupBy(option.value)}
                    >
                      <div className='flex items-center gap-2'>
                        <span>{option.icon}</span>
                        <span>{option.label}</span>
                      </div>
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {groupConfig.enabled && (
              <Button variant='outline' size='sm' className='h-8' onClick={onCancelGrouping}>
                <RotateCcw className='h-4 w-4' />
                取消分组
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Center - Task count info */}
      {/* <div className='flex items-center gap-2 text-sm text-muted-foreground'>
        {isLoadingPlantInfo ? (
          <span>加载中...</span>
        ) : (
          <span>
            显示 {filteredTaskCount} / {taskCount} 个任务
          </span>
        )}
      </div> */}

      {/* Right side - Actions and settings */}
      <div className='flex items-center gap-2'>
        {/* Vehicle Display Mode (for card view) */}
        {displayMode === 'card' && (
          <Button
            variant='outline'
            size='sm'
            className='h-8'
            onClick={() =>
              onVehicleDisplayModeChange(
                vehicleDisplayMode === 'licensePlate' ? 'internalId' : 'licensePlate'
              )
            }
          >
            {vehicleDisplayMode === 'licensePlate' ? '车牌' : 'ID'}
          </Button>
        )}

        {/* Header Mode Toggle */}
        {onSwitchToFloatingHeader && (
          <TaskListHeaderToggle
            useFloatingHeader={false}
            onToggleHeaderMode={() => onSwitchToFloatingHeader()}
            inFloatingHeader={false}
          />
        )}

        {/* More Actions Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='outline' size='sm' className='h-8'>
              <MoreVertical className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuLabel>操作</DropdownMenuLabel>
            <DropdownMenuSeparator />

            {/* Column Visibility (for table view) */}
            {displayMode === 'table' && (
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <Columns className='h-4 w-4 mr-2' />
                  列显示
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                  <DropdownMenuSubContent>
                    {ALL_TASK_COLUMNS_CONFIG.map(column => (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        checked={columnVisibility[getTaskColumnId(column.id)] !== false}
                        onCheckedChange={checked =>
                          onColumnVisibilityChange(getTaskColumnId(column.id), checked)
                        }
                      >
                        {(column as any).label}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuSubContent>
                </DropdownMenuPortal>
              </DropdownMenuSub>
            )}

            {/* Style and Config */}
            <DropdownMenuItem onClick={onOpenStyleEditor}>
              <Palette className='h-4 w-4 mr-2' />
              样式编辑器
            </DropdownMenuItem>

            {displayMode === 'card' && (
              <DropdownMenuItem onClick={onOpenCardConfig}>
                <Settings className='h-4 w-4 mr-2' />
                卡片配置
              </DropdownMenuItem>
            )}

            <DropdownMenuSeparator />

            {/* Data Management */}
            <DropdownMenuItem onClick={onExportData}>
              <FileDown className='h-4 w-4 mr-2' />
              导出数据
            </DropdownMenuItem>

            <DropdownMenuItem onClick={onImportData}>
              <FileUp className='h-4 w-4 mr-2' />
              导入数据
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem onClick={onResetSettings}>
              <Settings2 className='h-4 w-4 mr-2' />
              重置设置
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
