// src/components/sections/task-list/components/task-list-toolbar.tsx

import {
  BarChart3,
  CheckCircle2,
  ChevronDown,
  Columns,
  FileDown,
  FileUp,
  Grid3X3,
  LayoutGrid,
  MoreVertical,
  <PERSON>lette,
  RotateCcw,
  <PERSON>s,
  Settings,
  Settings2,
} from 'lucide-react';

import { Button } from '@/shared/components/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/shared/components/dropdown-menu';
import type { TaskListDensityMode, TaskListDisplayMode, VehicleDisplayMode } from '@/core/types';

import {
  getTaskListDensityMode,
  getTaskListDisplayMode,
  getVehicleDisplayMode,
} from './task-list-type-guards';

interface TaskListToolbarProps {
  displayMode: TaskListDisplayMode;
  density: Exclude<TaskListDensityMode, 'table' | 'card'>;
  enableZebraStriping: boolean;
  vehicleDisplayMode: VehicleDisplayMode;
  onDisplayModeChange: (mode: TaskListDisplayMode) => void;
  onDensityChange: (density: Exclude<TaskListDensityMode, 'table' | 'card'>) => void;
  onZebraStripingChange: (enabled: boolean) => void;
  onVehicleDisplayModeChange: (mode: VehicleDisplayMode) => void;
  onOpenColumnVisibilityModal: () => void;
  onOpenStyleEditorModal: () => void;
  onOpenGroupConfigModal: () => void;
  onResetAllSettings: () => void;
  onExportSettings: () => void;
  onTriggerImport: () => void;
  className?: string;
}

export function TaskListToolbar({
  displayMode,
  density,
  enableZebraStriping,
  vehicleDisplayMode,
  onDisplayModeChange,
  onDensityChange,
  onZebraStripingChange,
  onVehicleDisplayModeChange,
  onOpenColumnVisibilityModal,
  onOpenStyleEditorModal,
  onOpenGroupConfigModal,
  onResetAllSettings,
  onExportSettings,
  onTriggerImport,
  className,
}: TaskListToolbarProps) {
  return (
    <div className={className}>
      {/* Main Settings Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant='outline' size='sm' className='h-7 gap-1'>
            <Settings className='h-3.5 w-3.5' />
            <span className='sr-only sm:not-sr-only sm:whitespace-nowrap'>设置</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end' className='w-[200px]'>
          <DropdownMenuLabel>表格设置</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {/* Display and Density Settings */}
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <LayoutGrid className='mr-2 h-3.5 w-3.5' />
              <span>显示设置</span>
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent>
                <DropdownMenuLabel>显示模式</DropdownMenuLabel>
                <DropdownMenuRadioGroup
                  value={displayMode}
                  onValueChange={value => onDisplayModeChange(getTaskListDisplayMode(value))}
                >
                  <DropdownMenuRadioItem value='table'>
                    <Rows className='mr-2 h-3.5 w-3.5' />
                    表格
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value='card'>
                    <LayoutGrid className='mr-2 h-3.5 w-3.5' />
                    卡片
                  </DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
                <DropdownMenuSeparator />
                <DropdownMenuLabel>密度</DropdownMenuLabel>
                <DropdownMenuRadioGroup
                  value={density}
                  onValueChange={value => onDensityChange(getTaskListDensityMode(value))}
                >
                  <DropdownMenuRadioItem value='compact'>紧凑</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value='normal'>标准</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value='loose'>宽松</DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
                <DropdownMenuSeparator />
                <DropdownMenuCheckboxItem
                  checked={enableZebraStriping}
                  onCheckedChange={checked => onZebraStripingChange(!!checked)}
                >
                  斑马条纹
                </DropdownMenuCheckboxItem>
                <DropdownMenuSeparator />
                <DropdownMenuLabel>车辆显示</DropdownMenuLabel>
                <DropdownMenuRadioGroup
                  value={vehicleDisplayMode}
                  onValueChange={value => onVehicleDisplayModeChange(getVehicleDisplayMode(value))}
                >
                  <DropdownMenuRadioItem value='licensePlate'>车牌号</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value='internalId'>内部编号</DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>

          {/* Column Management */}
          <DropdownMenuItem onClick={onOpenColumnVisibilityModal}>
            <Columns className='mr-2 h-3.5 w-3.5' />
            列管理
          </DropdownMenuItem>

          {/* Style Settings */}
          <DropdownMenuItem onClick={onOpenStyleEditorModal}>
            <Palette className='mr-2 h-3.5 w-3.5' />
            样式设置
          </DropdownMenuItem>

          {/* Group Settings */}
          <DropdownMenuItem onClick={onOpenGroupConfigModal}>
            <BarChart3 className='mr-2 h-3.5 w-3.5' />
            分组设置
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* Import/Export */}
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <Settings2 className='mr-2 h-3.5 w-3.5' />
              <span>配置管理</span>
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent>
                <DropdownMenuLabel>显示模式</DropdownMenuLabel>
                <DropdownMenuRadioGroup
                  value={displayMode}
                  onValueChange={value => onDisplayModeChange(getTaskListDisplayMode(value))}
                >
                  <DropdownMenuRadioItem value='table'>
                    <Rows className='mr-2 h-3.5 w-3.5' />
                    表格
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value='card'>
                    <LayoutGrid className='mr-2 h-3.5 w-3.5' />
                    卡片
                  </DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
                <DropdownMenuSeparator />
                <DropdownMenuLabel>密度</DropdownMenuLabel>
                <DropdownMenuRadioGroup
                  value={density}
                  onValueChange={value => onDensityChange(getTaskListDensityMode(value))}
                >
                  <DropdownMenuRadioItem value='compact'>紧凑</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value='normal'>标准</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value='loose'>宽松</DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
                <DropdownMenuSeparator />
                <DropdownMenuCheckboxItem
                  checked={enableZebraStriping}
                  onCheckedChange={checked => onZebraStripingChange(!!checked)}
                >
                  斑马条纹
                </DropdownMenuCheckboxItem>
                <DropdownMenuSeparator />
                <DropdownMenuLabel>车辆显示</DropdownMenuLabel>
                <DropdownMenuRadioGroup
                  value={vehicleDisplayMode}
                  onValueChange={value => onVehicleDisplayModeChange(getVehicleDisplayMode(value))}
                >
                  <DropdownMenuRadioItem value='licensePlate'>车牌号</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value='internalId'>内部编号</DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>

          <DropdownMenuItem onClick={onExportSettings}>
            <FileDown className='mr-2 h-3.5 w-3.5' />
            导出配置
          </DropdownMenuItem>

          <DropdownMenuItem onClick={onTriggerImport}>
            <FileUp className='mr-2 h-3.5 w-3.5' />
            导入配置
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem onClick={onResetAllSettings}>
            <RotateCcw className='mr-2 h-3.5 w-3.5' />
            重置设置
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Additional Action Buttons */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant='outline' size='sm' className='h-7 gap-1'>
            <MoreVertical className='h-3.5 w-3.5' />
            <span className='sr-only sm:not-sr-only sm:whitespace-nowrap'>更多</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end' className='w-[200px]'>
          <DropdownMenuLabel>快速操作</DropdownMenuLabel>
          <DropdownMenuSeparator />

          <DropdownMenuItem onClick={onOpenColumnVisibilityModal}>
            <Columns className='mr-2 h-3.5 w-3.5' />
            列管理
          </DropdownMenuItem>

          <DropdownMenuItem onClick={onOpenGroupConfigModal}>
            <Grid3X3 className='mr-2 h-3.5 w-3.5' />
            分组配置
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
