/**
 * 工具类型定义
 * 提供通用的工具类型，减少any的使用
 */

import { ReactNode, ComponentType, CSSProperties } from 'react';
import { ID, Timestamp } from './core-interfaces';

// ==================== 基础工具类型 ====================

/**
 * 可选的部分类型
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * 必需的部分类型
 */
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * 深度部分类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * 非空类型
 */
export type NonNullable<T> = T extends null | undefined ? never : T;

/**
 * 值类型提取
 */
export type ValueOf<T> = T[keyof T];

/**
 * 数组元素类型提取
 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never;

/**
 * Promise 结果类型提取
 */
export type PromiseResult<T> = T extends Promise<infer U> ? U : never;

// ==================== 函数相关类型 ====================

/**
 * 通用回调函数类型
 */
export type Callback<T = void> = (value: T) => void;

/**
 * 异步回调函数类型
 */
export type AsyncCallback<T = void> = (value: T) => Promise<void>;

/**
 * 事件处理函数类型
 */
export type EventHandler<T = Event> = (event: T) => void;

/**
 * 变更处理函数类型
 */
export type ChangeHandler<T = any> = (value: T) => void;

/**
 * 验证函数类型
 */
export type Validator<T = any> = (value: T) => boolean | string;

/**
 * 比较函数类型
 */
export type Comparator<T = any> = (a: T, b: T) => number;

/**
 * 过滤函数类型
 */
export type Filter<T = any> = (item: T) => boolean;

/**
 * 映射函数类型
 */
export type Mapper<T = any, U = any> = (item: T) => U;

/**
 * 归约函数类型
 */
export type Reducer<T = any, U = any> = (accumulator: U, current: T) => U;

// ==================== 组件相关类型 ====================

/**
 * 基础组件属性类型
 */
export interface BaseComponentProps {
  className?: string;
  style?: CSSProperties;
  children?: ReactNode;
  id?: string;
  'data-testid'?: string;
}

/**
 * 可点击组件属性类型
 */
export interface ClickableProps {
  onClick?: EventHandler<React.MouseEvent>;
  onDoubleClick?: EventHandler<React.MouseEvent>;
  disabled?: boolean;
}

/**
 * 可拖拽组件属性类型
 */
export interface DraggableProps {
  draggable?: boolean;
  onDragStart?: EventHandler<React.DragEvent>;
  onDragEnd?: EventHandler<React.DragEvent>;
  onDrag?: EventHandler<React.DragEvent>;
}

/**
 * 可放置组件属性类型
 */
export interface DroppableProps {
  onDrop?: EventHandler<React.DragEvent>;
  onDragOver?: EventHandler<React.DragEvent>;
  onDragEnter?: EventHandler<React.DragEvent>;
  onDragLeave?: EventHandler<React.DragEvent>;
}

/**
 * 表单控件属性类型
 */
export interface FormControlProps<T = any> {
  value?: T;
  defaultValue?: T;
  onChange?: ChangeHandler<T>;
  onBlur?: EventHandler<React.FocusEvent>;
  onFocus?: EventHandler<React.FocusEvent>;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  placeholder?: string;
  error?: boolean;
  helperText?: string;
}

/**
 * 加载状态组件属性类型
 */
export interface LoadingProps {
  loading?: boolean;
  loadingText?: string;
  loadingComponent?: ComponentType;
}

/**
 * 分页组件属性类型
 */
export interface PaginationProps {
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: boolean;
}

// ==================== 数据相关类型 ====================

/**
 * API 响应类型
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  code?: string | number;
  timestamp?: Timestamp;
}

/**
 * 分页数据类型
 */
export interface PaginatedData<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 排序配置类型
 */
export interface SortConfig<T = any> {
  field: keyof T;
  direction: 'asc' | 'desc';
}

/**
 * 过滤配置类型
 */
export interface FilterConfig<T = any> {
  field: keyof T;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith';
  value: any;
}

/**
 * 搜索配置类型
 */
export interface SearchConfig {
  query: string;
  fields?: string[];
  caseSensitive?: boolean;
  exactMatch?: boolean;
}

/**
 * 选择状态类型
 */
export interface SelectionState<T = any> {
  selectedItems: T[];
  selectedIds: ID[];
  isAllSelected: boolean;
  isPartiallySelected: boolean;
}

// ==================== 状态管理相关类型 ====================

/**
 * 异步状态类型
 */
export interface AsyncState<T = any> {
  data?: T;
  loading: boolean;
  error?: string | Error;
  lastUpdated?: Timestamp;
}

/**
 * 缓存状态类型
 */
export interface CacheState<T = any> {
  data: T;
  timestamp: Timestamp;
  ttl: number; // 生存时间（毫秒）
  isExpired: boolean;
}

/**
 * 表单状态类型
 */
export interface FormState<T = any> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  isDirty: boolean;
  isValid: boolean;
  isSubmitting: boolean;
}

/**
 * 模态框状态类型
 */
export interface ModalState {
  isOpen: boolean;
  title?: string;
  content?: ReactNode;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  closable?: boolean;
  maskClosable?: boolean;
}

// ==================== 配置相关类型 ====================

/**
 * 主题配置类型
 */
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'auto';
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  borderRadius: number;
  fontSize: number;
  fontFamily: string;
}

/**
 * 布局配置类型
 */
export interface LayoutConfig {
  sidebarWidth: number;
  headerHeight: number;
  footerHeight: number;
  contentPadding: number;
  sidebarCollapsed: boolean;
  showHeader: boolean;
  showFooter: boolean;
  showSidebar: boolean;
}

/**
 * 表格配置类型
 */
export interface TableConfig<T = any> {
  columns: TableColumn<T>[];
  rowKey: keyof T | ((record: T) => string);
  pagination?: PaginationProps;
  sorting?: SortConfig<T>;
  filtering?: FilterConfig<T>[];
  selection?: SelectionState<T>;
  expandable?: boolean;
  resizable?: boolean;
  virtualized?: boolean;
}

/**
 * 表格列配置类型
 */
export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex: keyof T;
  width?: number;
  minWidth?: number;
  maxWidth?: number;
  fixed?: 'left' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  resizable?: boolean;
  visible?: boolean;
  render?: (value: any, record: T, index: number) => ReactNode;
  align?: 'left' | 'center' | 'right';
}

// ==================== 错误处理类型 ====================

/**
 * 错误信息类型
 */
export interface ErrorInfo {
  code: string;
  message: string;
  details?: any;
  timestamp: Timestamp;
  stack?: string;
}

/**
 * 验证错误类型
 */
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

/**
 * 网络错误类型
 */
export interface NetworkError {
  status: number;
  statusText: string;
  url: string;
  method: string;
  timestamp: Timestamp;
}

// ==================== 类型守卫 ====================

/**
 * 检查是否为非空值
 */
export function isNonNullable<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * 检查是否为字符串
 */
export function isString(value: any): value is string {
  return typeof value === 'string';
}

/**
 * 检查是否为数字
 */
export function isNumber(value: any): value is number {
  return typeof value === 'number' && !isNaN(value);
}

/**
 * 检查是否为布尔值
 */
export function isBoolean(value: any): value is boolean {
  return typeof value === 'boolean';
}

/**
 * 检查是否为对象
 */
export function isObject(value: any): value is object {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
}

/**
 * 检查是否为数组
 */
export function isArray<T>(value: any): value is T[] {
  return Array.isArray(value);
}

/**
 * 检查是否为函数
 */
export function isFunction(value: any): value is Function {
  return typeof value === 'function';
}

/**
 * 检查是否为Promise
 */
export function isPromise<T>(value: any): value is Promise<T> {
  return value && typeof value.then === 'function';
}
