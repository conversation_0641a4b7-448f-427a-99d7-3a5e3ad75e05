# 📚 TMH车辆调度系统 - 完整文档索引

欢迎来到TMH车辆调度系统的完整文档中心！这里是所有文档的总索引。

## 🎯 文档结构概览

```
docs/
├── README.md                          # 文档中心首页
├── index.md                           # 完整文档索引 (本页面)
├── overview/                          # 项目概述
├── getting-started/                   # 快速开始
├── development/                       # 开发指南
├── features/                          # 功能模块
├── api/                              # API文档
├── ui-ux/                            # UI/UX指南
├── deployment/                        # 部署运维
├── troubleshooting/                   # 故障排除
└── reference/                         # 参考资料
```

## 📖 详细文档目录

### 🎯 项目概述 (overview/)
- [📋 项目概述](./overview/project-overview.md) - 项目背景、目标和核心功能
- [🏗️ 系统架构](./overview/system-architecture.md) - 整体架构设计和技术选型
- [🛠️ 技术栈](./overview/tech-stack.md) - 使用的技术和工具详解

### 🚀 快速开始 (getting-started/)
- [🚀 快速开始](./getting-started/quick-start.md) - 5分钟快速上手指南
- [⚙️ 环境搭建](./getting-started/environment-setup.md) - 开发环境配置详解
- [📁 项目结构](./getting-started/project-structure.md) - 目录结构和文件组织

### 🏗️ 开发指南 (development/)
- [📝 开发规范](./development/coding-standards.md) - 代码规范和最佳实践
- [🧩 组件开发](./development/component-development.md) - React组件开发指南
- [🏪 状态管理](./development/state-management.md) - Zustand状态管理详解
- [🔌 API集成](./development/api-integration.md) - API接口集成指南
- [🧪 测试指南](./development/testing-guide.md) - 单元测试和集成测试

### 🧩 功能模块 (features/)
- [📋 任务管理](./features/task-management.md) - 任务列表和管理功能详解
- [🚛 车辆调度](./features/vehicle-dispatch.md) - 车辆调度和管理功能
- [🧪 配比管理](./features/ratio-management.md) - 混凝土配比管理功能
- [⚙️ 设置中心](./features/settings.md) - 系统设置和配置功能

### 🔧 API文档 (api/)
- [📋 API概述](./api/overview.md) - API设计原则和基础信息
- [📝 任务API](./api/task-api.md) - 任务相关接口文档
- [🚛 车辆API](./api/vehicle-api.md) - 车辆相关接口文档
- [🧪 配比API](./api/ratio-api.md) - 配比相关接口文档

### 🎨 UI/UX指南 (ui-ux/)
- [🎨 设计系统](./ui-ux/design-system.md) - 设计规范和组件库
- [🌈 主题配置](./ui-ux/theming.md) - 主题和样式配置
- [📱 响应式设计](./ui-ux/responsive-design.md) - 移动端适配指南
- [👤 用户体验](./ui-ux/user-experience.md) - 交互设计指南

### 🚀 部署运维 (deployment/)
- [🏗️ 构建部署](./deployment/build-and-deploy.md) - 构建和部署流程
- [⚙️ 环境配置](./deployment/environment-config.md) - 环境变量配置
- [⚡ 性能优化](./deployment/performance-optimization.md) - 性能优化指南
- [📊 监控告警](./deployment/monitoring.md) - 监控和日志系统

### 🔍 故障排除 (troubleshooting/)
- [❓ 常见问题](./troubleshooting/common-issues.md) - 常见问题和解决方案
- [🐛 调试指南](./troubleshooting/debugging-guide.md) - 调试技巧和工具
- [⚠️ 错误处理](./troubleshooting/error-handling.md) - 错误处理机制

### 📋 参考资料 (reference/)
- [📋 更新日志](./reference/changelog.md) - 版本更新记录
- [🤝 贡献指南](./reference/contributing.md) - 如何参与项目开发
- [❓ FAQ](./reference/faq.md) - 常见问题解答
- [📖 术语表](./reference/glossary.md) - 专业术语解释

## 🔍 快速查找

### 按角色查找文档

#### 👨‍💻 开发者
- [快速开始](./getting-started/quick-start.md) - 快速上手
- [开发规范](./development/coding-standards.md) - 代码规范
- [组件开发](./development/component-development.md) - 组件开发
- [API文档](./api/overview.md) - 接口文档
- [故障排除](./troubleshooting/common-issues.md) - 问题解决

#### 🎨 设计师
- [设计系统](./ui-ux/design-system.md) - 设计规范
- [主题配置](./ui-ux/theming.md) - 主题系统
- [响应式设计](./ui-ux/responsive-design.md) - 移动端设计
- [用户体验](./ui-ux/user-experience.md) - 交互设计

#### 🚀 运维工程师
- [构建部署](./deployment/build-and-deploy.md) - 部署流程
- [环境配置](./deployment/environment-config.md) - 环境设置
- [性能优化](./deployment/performance-optimization.md) - 性能调优
- [监控告警](./deployment/monitoring.md) - 监控系统

#### 📋 产品经理
- [项目概述](./overview/project-overview.md) - 项目背景
- [功能模块](./features/task-management.md) - 功能详解
- [用户体验](./ui-ux/user-experience.md) - 用户体验
- [更新日志](./reference/changelog.md) - 版本历史

#### 🧪 测试工程师
- [测试指南](./development/testing-guide.md) - 测试方法
- [调试指南](./troubleshooting/debugging-guide.md) - 调试技巧
- [错误处理](./troubleshooting/error-handling.md) - 错误处理

### 按主题查找文档

#### 🏗️ 架构和设计
- [系统架构](./overview/system-architecture.md)
- [技术栈](./overview/tech-stack.md)
- [设计系统](./ui-ux/design-system.md)
- [项目结构](./getting-started/project-structure.md)

#### 💻 开发和编码
- [开发规范](./development/coding-standards.md)
- [组件开发](./development/component-development.md)
- [状态管理](./development/state-management.md)
- [API集成](./development/api-integration.md)

#### 🚀 部署和运维
- [构建部署](./deployment/build-and-deploy.md)
- [环境配置](./deployment/environment-config.md)
- [性能优化](./deployment/performance-optimization.md)
- [监控告警](./deployment/monitoring.md)

#### 🔧 功能和使用
- [任务管理](./features/task-management.md)
- [车辆调度](./features/vehicle-dispatch.md)
- [配比管理](./features/ratio-management.md)
- [设置中心](./features/settings.md)

#### 🆘 帮助和支持
- [常见问题](./troubleshooting/common-issues.md)
- [FAQ](./reference/faq.md)
- [调试指南](./troubleshooting/debugging-guide.md)
- [贡献指南](./reference/contributing.md)

## 📊 文档统计

### 文档数量
- **总文档数**: 25+ 个
- **代码示例**: 100+ 个
- **截图和图表**: 50+ 个
- **API接口**: 30+ 个

### 覆盖范围
- ✅ **项目概述**: 完整覆盖
- ✅ **开发指南**: 完整覆盖
- ✅ **功能文档**: 完整覆盖
- ✅ **API文档**: 完整覆盖
- ✅ **部署文档**: 完整覆盖
- ✅ **故障排除**: 完整覆盖

## 🔄 文档维护

### 更新频率
- **核心文档**: 每月更新
- **API文档**: 随版本更新
- **故障排除**: 根据问题反馈更新
- **FAQ**: 根据用户问题更新

### 质量保证
- 📝 **内容审查**: 技术准确性检查
- 🎨 **格式统一**: 统一的文档格式
- 🔗 **链接检查**: 定期检查链接有效性
- 📊 **用户反馈**: 收集和处理用户反馈

## 📞 获取帮助

### 文档问题
- 📧 **邮件**: <EMAIL>
- 🐛 **GitHub Issues**: [文档问题报告](https://github.com/your-org/tmh-task-dispatcher/issues)
- 💬 **讨论**: [GitHub Discussions](https://github.com/your-org/tmh-task-dispatcher/discussions)

### 技术支持
- 📧 **邮件**: <EMAIL>
- 📞 **电话**: +86-xxx-xxxx-xxxx
- 💬 **在线客服**: 工作日 9:00-18:00

### 社区资源
- 🌐 **官方网站**: https://tmh.com
- 📱 **微信群**: 扫码加入开发者群
- 📺 **视频教程**: [B站频道](https://space.bilibili.com/tmh)

---

**文档索引版本**: v1.0  
**最后更新**: 2025/7/16  
**维护者**: TMH 文档团队

> 💡 **提示**: 建议将此页面加入书签，方便快速查找所需文档。如果您是新用户，推荐从 [快速开始](./getting-started/quick-start.md) 开始阅读。
