/**
 * 配比页面模拟数据生成工具
 * 将分散在组件中的模拟数据生成函数集中管理
 */

import type { SiloMapping, RatioHistoryEntry } from '@/core/types';

/**
 * 生成模拟的料仓映射数据
 */
export const generateMockSiloMappings = (): SiloMapping[] => [
  {
    id: '1',
    siloType: '水泥',
    materialName: '水泥',
    spec: 'P.O 42.5',
    storageBin: '1#水泥仓',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
  {
    id: '2',
    siloType: '砂子',
    materialName: '砂子',
    spec: '中砂',
    storageBin: '1#砂仓',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
  {
    id: '3',
    siloType: '石子',
    materialName: '石子',
    spec: '5-25mm',
    storageBin: '1#石仓',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
  {
    id: '4',
    siloType: '水',
    materialName: '水',
    spec: '饮用水',
    storageBin: '水箱',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
  {
    id: '5',
    siloType: '外加剂',
    materialName: '聚羧酸减水剂',
    spec: 'PCE-40%',
    storageBin: '1#外加剂罐',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
  {
    id: '6',
    siloType: '外加剂',
    materialName: '萘系减水剂',
    spec: 'FDN-C',
    storageBin: '2#外加剂罐',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
  {
    id: '7',
    siloType: '外加剂',
    materialName: '引气剂',
    spec: 'AEA-3',
    storageBin: '3#外加剂罐',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
  {
    id: '8',
    siloType: '外加剂',
    materialName: '缓凝剂',
    spec: 'SET-R',
    storageBin: '4#外加剂罐',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
  {
    id: '9',
    siloType: '外加剂',
    materialName: '早强剂',
    spec: 'ACC-E',
    storageBin: '5#外加剂罐',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
  {
    id: '10',
    siloType: '粉煤灰',
    materialName: '粉煤灰',
    spec: 'I级',
    storageBin: '1#粉煤灰仓',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
  {
    id: '11',
    siloType: '粉煤灰',
    materialName: '粉煤灰',
    spec: 'II级',
    storageBin: '2#粉煤灰仓',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
  {
    id: '12',
    siloType: '矿粉',
    materialName: '矿粉',
    spec: 'S95',
    storageBin: '1#矿粉仓',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
  {
    id: '13',
    siloType: '矿粉',
    materialName: '矿粉',
    spec: 'S105',
    storageBin: '2#矿粉仓',
    lastEntry: '2024-01-15 14:30:00',
    enabled: true,
  },
];

/**
 * 生成模拟的配比历史记录
 */
export const generateMockRatioHistory = (taskId: string): RatioHistoryEntry[] => [
  {
    id: '1',
    taskId,
    editor: '张工程师',
    timestamp: '2024-01-15 14:30:00',
    price: 285.5,
    materials: {
      cement: 372,
      flyAsh: 0,
      mineralPowder: 0,
      sand: 593,
      stone: 1260,
      water: 175,
      admixture: 3.72,
    },
  },
  {
    id: '2',
    taskId,
    editor: '李技术员',
    timestamp: '2024-01-15 16:45:00',
    price: 288.2,
    materials: {
      cement: 375,
      flyAsh: 0,
      mineralPowder: 0,
      sand: 590,
      stone: 1255,
      water: 178,
      admixture: 3.75,
    },
  },
];

/**
 * 根据材料类型获取库存量
 */
export const getStockAmountByType = (siloType: string): number => {
  switch (siloType) {
    case '水泥':
      return 150;
    case '砂子':
      return 300;
    case '石子':
      return 400;
    case '水':
      return 1000;
    case '外加剂':
      return 20;
    case '粉煤灰':
      return 100;
    case '矿粉':
      return 80;
    default:
      return 100;
  }
};

/**
 * 生成AI配比生成器的可用材料列表
 */
export const generateAvailableMaterials = () => {
  return generateMockSiloMappings().map(silo => ({
    id: silo.id,
    name: silo.materialName,
    type: silo.siloType,
    specification: silo.spec,
    available: silo.enabled,
    amount: getStockAmountByType(silo.siloType),
  }));
};
