/**
 * 类型迁移工具
 * 帮助将现有代码从旧类型迁移到新的统一类型系统
 */

import type { Task, Vehicle, Plant } from '@/core/types';
import type { UnifiedTask, UnifiedVehicle, UnifiedPlant } from '@/core/types/unified/core';
import {
  TaskDispatchStatus,
  VehicleStatus,
  VehicleOperationalStatus,
} from '@/core/types/unified/core';
import { isString, isArray, isObject, toSafeString, toSafeNumber } from './typeCleanup';

// ==================== 类型映射配置 ====================

/**
 * 任务状态映射表
 */
const TASK_STATUS_MAPPING: Record<string, TaskDispatchStatus> = {
  New: TaskDispatchStatus.NEW,
  ReadyToProduce: TaskDispatchStatus.READY_TO_PRODUCE,
  RatioSet: TaskDispatchStatus.RATIO_SET,
  InProgress: TaskDispatchStatus.IN_PROGRESS,
  Paused: TaskDispatchStatus.PAUSED,
  Completed: TaskDispatchStatus.COMPLETED,
  Cancelled: TaskDispatchStatus.CANCELLED,
  // 兼容旧版本状态
  Pending: TaskDispatchStatus.NEW,
  Active: TaskDispatchStatus.IN_PROGRESS,
  Done: TaskDispatchStatus.COMPLETED,
};

/**
 * 车辆状态映射表
 */
const VEHICLE_STATUS_MAPPING: Record<string, VehicleStatus> = {
  pending: VehicleStatus.PENDING,
  outbound: VehicleStatus.OUTBOUND,
  returned: VehicleStatus.RETURNED,
  maintenance: VehicleStatus.MAINTENANCE,
  offline: VehicleStatus.OFFLINE,
  // 兼容旧版本状态
  dispatched: VehicleStatus.OUTBOUND,
  available: VehicleStatus.PENDING,
  busy: VehicleStatus.OUTBOUND,
  repair: VehicleStatus.MAINTENANCE,
};

/**
 * 车辆运营状态映射表
 */
const VEHICLE_OPERATIONAL_STATUS_MAPPING: Record<string, VehicleOperationalStatus> = {
  normal: VehicleOperationalStatus.NORMAL,
  paused: VehicleOperationalStatus.PAUSED,
  deactivated: VehicleOperationalStatus.DEACTIVATED,
  maintenance: VehicleOperationalStatus.MAINTENANCE,
  repair: VehicleOperationalStatus.REPAIR,
};

// ==================== 迁移函数 ====================

/**
 * 迁移任务数据到统一格式
 */
export function migrateTaskToUnified(oldTask: Task): UnifiedTask {
  // 基本验证
  if (!isObject(oldTask)) {
    throw new Error('Invalid task object provided for migration');
  }

  // 必需字段验证
  const id = toSafeString(oldTask['id']);
  const taskNumber = toSafeString(oldTask['taskNumber']);
  const projectName = toSafeString(oldTask['projectName']);

  if (!id || !taskNumber || !projectName) {
    throw new Error('Missing required fields for task migration: id, taskNumber, projectName');
  }

  // 状态转换
  const dispatchStatus =
    TASK_STATUS_MAPPING[oldTask['dispatchStatus'] as string] || TaskDispatchStatus.NEW;

  // 数量字段处理
  const vehicleCount = toSafeNumber(oldTask['vehicleCount'], 1);
  const requiredVolume = toSafeNumber(oldTask['requiredVolume'], 0);
  const completedVolume = toSafeNumber(oldTask['completedVolume'], 0);
  const completedProgress = requiredVolume > 0 ? (completedVolume / requiredVolume) * 100 : 0;

  // 时间字段处理
  const supplyDate = toSafeString(oldTask.supplyDate);
  const supplyTime = toSafeString(oldTask.supplyTime);

  // 调度信息处理
  const dispatchFrequencyMinutes = toSafeNumber(oldTask.dispatchFrequencyMinutes, 30);
  const minutesToDispatch = toSafeNumber(oldTask.minutesToDispatch, 0);
  const isDueForDispatch = Boolean(oldTask.isDueForDispatch);

  // 车辆信息处理
  let dispatchedVehicles: string[] = [];
  if (isArray(oldTask['dispatchedVehicles'])) {
    dispatchedVehicles = oldTask['dispatchedVehicles'].filter(isString);
  } else if (isArray(oldTask['vehicles'])) {
    dispatchedVehicles = oldTask['vehicles'].map((v: any) => toSafeString(v.id)).filter(Boolean);
  }

  return {
    // 基本信息
    id,
    plantId: toSafeString(oldTask.plantId, ''),
    taskNumber,
    projectName,
    projectAbbreviation: toSafeString(oldTask.projectAbbreviation),
    customerName: toSafeString(oldTask.customerName),
    constructionUnit: toSafeString(oldTask['constructionUnit']),
    constructionSite: toSafeString(oldTask['constructionSite']),
    deliveryLocation: toSafeString(oldTask['deliveryLocation']),

    // 技术规格
    strength: toSafeString(oldTask['strength'], ''),
    concreteGrade: toSafeString(oldTask['concreteGrade']),
    pouringMethod: toSafeString(oldTask['pouringMethod']),
    freezeResistance: toSafeString(oldTask['freezeResistance']),
    impermeability: toSafeString(oldTask['impermeability']),
    slump: toSafeNumber(oldTask['slump']),
    airContent: toSafeNumber(oldTask['airContent']),

    // 数量和进度
    vehicleCount,
    requiredVolume,
    completedVolume,
    completedProgress,

    // 时间信息
    supplyDate,
    supplyTime,
    publishDate: toSafeString(oldTask['publishDate']),
    timing: toSafeString(oldTask['timing']),

    // 状态管理
    dispatchStatus,
    status: toSafeString(oldTask['status']),

    // 调度信息
    dispatchFrequencyMinutes,
    lastDispatchTime: toSafeString(oldTask['lastDispatchTime']),
    nextScheduledDispatchTime: toSafeString(oldTask['nextScheduledDispatchTime']),
    isDueForDispatch,
    minutesToDispatch,

    // 车辆和设备
    dispatchedVehicles,
    vehicles: undefined, // 清除可能不兼容的 vehicles 字段
    pumpTruck: toSafeString(oldTask['pumpTruck']),

    // 联系信息
    contactPhone: toSafeString(oldTask['contactPhone']),
    contactPerson: toSafeString(oldTask['contactPerson']),

    // 其他信息
    otherRequirements: toSafeString(oldTask['otherRequirements']),
    notes: toSafeString(oldTask['notes']),
    subTheme: toSafeString(oldTask['subTheme']),
    dispatchNote: toSafeString(oldTask['dispatchNote']),
    isTicketed: Boolean(oldTask['isTicketed']),

    // 消息和提醒
    messages: undefined, // 清除可能不兼容的 messages 字段
    reminders: undefined, // 清除可能不兼容的 reminders 字段

    // 元数据
    createdAt: toSafeString(oldTask['createdAt']),
    updatedAt: toSafeString(oldTask['updatedAt']),
    createdBy: toSafeString(oldTask['createdBy']),
    updatedBy: toSafeString(oldTask['updatedBy']),
  };
}

/**
 * 迁移车辆数据到统一格式
 */
export function migrateVehicleToUnified(oldVehicle: any): UnifiedVehicle {
  // 基本验证
  if (!isObject(oldVehicle)) {
    throw new Error('Invalid vehicle object provided for migration');
  }

  // 必需字段验证
  const id = toSafeString(oldVehicle['id']);
  const vehicleNumber = toSafeString(oldVehicle['vehicleNumber']);

  if (!id || !vehicleNumber) {
    throw new Error('Missing required fields for vehicle migration: id, vehicleNumber');
  }

  // 状态转换
  const status = VEHICLE_STATUS_MAPPING[oldVehicle['status'] as string] || VehicleStatus.PENDING;
  const operationalStatus = oldVehicle['operationalStatus']
    ? VEHICLE_OPERATIONAL_STATUS_MAPPING[oldVehicle['operationalStatus'] as string]
    : undefined;

  // 容量处理
  const capacity = toSafeNumber(oldVehicle['capacity'], 0);

  return {
    // 基本信息
    id,
    vehicleNumber,
    licensePlate: toSafeString(oldVehicle['licensePlate']),
    plantId: toSafeString(oldVehicle['plantId'], ''),

    // 状态信息
    status,
    operationalStatus,
    productionStatus: undefined, // 清除可能不兼容的 productionStatus 字段

    // 分配信息
    assignedTaskId: toSafeString(oldVehicle['assignedTaskId']),
    assignedProductionLineId: toSafeString(oldVehicle['assignedProductionLineId']),
    currentTaskId: toSafeString(oldVehicle['currentTaskId']),

    // 驾驶员信息
    driver: toSafeString(oldVehicle['driver'] || oldVehicle['driverName']),
    driverName: toSafeString(oldVehicle['driverName']),
    driverPhone: toSafeString(oldVehicle['driverPhone']),

    // 车辆规格
    capacity,
    capacityUnit: toSafeString(oldVehicle['capacityUnit'], 'm³'),
    type: toSafeString(oldVehicle['type']),
    model: toSafeString(oldVehicle['model']),
    year: toSafeNumber(oldVehicle['year']),

    // 位置和时间
    currentLocation: toSafeString(oldVehicle['currentLocation'] || oldVehicle['location']),
    location: toSafeString(oldVehicle['location']),
    lastDispatchTime: toSafeString(oldVehicle['lastDispatchTime']),
    estimatedReturnTime: toSafeString(oldVehicle['estimatedReturnTime']),
    lastActivityTime: toSafeString(oldVehicle['lastActivityTime']),

    // 维护信息
    mileage: toSafeNumber(oldVehicle['mileage']),
    lastMaintenanceDate: toSafeString(oldVehicle['lastMaintenanceDate']),
    lastMaintenanceMileage: toSafeNumber(oldVehicle['lastMaintenanceMileage']),
    nextMaintenanceDate: toSafeString(oldVehicle['nextMaintenanceDate']),

    // 统计信息
    totalTrips: toSafeNumber(oldVehicle['totalTrips']),
    totalWorkingHours: toSafeNumber(oldVehicle['totalWorkingHours']),
    totalDistance: toSafeNumber(oldVehicle['totalDistance']),

    // 其他信息
    notes: toSafeString(oldVehicle['notes']),
    isDragging: Boolean(oldVehicle['isDragging']),

    // 元数据
    createdAt: toSafeString(oldVehicle['createdAt']),
    updatedAt: toSafeString(oldVehicle['updatedAt']),
  };
}

/**
 * 迁移工厂数据到统一格式
 */
export function migratePlantToUnified(oldPlant: any): UnifiedPlant {
  // 基本验证
  if (!isObject(oldPlant)) {
    throw new Error('Invalid plant object provided for migration');
  }

  // 必需字段验证
  const id = toSafeString(oldPlant['id']);
  const name = toSafeString(oldPlant['name']);

  if (!id || !name) {
    throw new Error('Missing required fields for plant migration: id, name');
  }

  const productionLineCount = toSafeNumber(oldPlant['productionLineCount'], 1);

  return {
    id,
    name,
    code: toSafeString(oldPlant['code']),
    address: toSafeString(oldPlant['address']),
    contactPhone: toSafeString(oldPlant['contactPhone']),
    contactPerson: toSafeString(oldPlant['contactPerson']),
    productionLineCount,
    capacity: toSafeNumber(oldPlant['capacity']),
    status: (['active', 'inactive', 'maintenance'].includes(oldPlant['status'] as string)
      ? oldPlant['status']
      : 'active') as 'active' | 'inactive' | 'maintenance',
    stats: undefined, // 清除可能不兼容的 stats 字段
    createdAt: toSafeString(oldPlant['createdAt']),
    updatedAt: toSafeString(oldPlant['updatedAt']),
  };
}

// ==================== 批量迁移函数 ====================

/**
 * 批量迁移任务数据
 */
export function migrateTasksToUnified(oldTasks: any[]): UnifiedTask[] {
  if (!isArray(oldTasks)) {
    throw new Error('Expected array of tasks for batch migration');
  }

  const results: UnifiedTask[] = [];
  const errors: Array<{ index: number; error: string; data: any }> = [];

  oldTasks.forEach((task, index) => {
    try {
      results.push(migrateTaskToUnified(task as any));
    } catch (error) {
      errors.push({
        index,
        error: error instanceof Error ? error.message : 'Unknown error',
        data: task,
      });
    }
  });

  if (errors.length > 0) {
    console.warn('Task migration errors:', errors);
  }

  return results;
}

/**
 * 批量迁移车辆数据
 */
export function migrateVehiclesToUnified(oldVehicles: any[]): UnifiedVehicle[] {
  if (!isArray(oldVehicles)) {
    throw new Error('Expected array of vehicles for batch migration');
  }

  const results: UnifiedVehicle[] = [];
  const errors: Array<{ index: number; error: string; data: any }> = [];

  oldVehicles.forEach((vehicle, index) => {
    try {
      results.push(migrateVehicleToUnified(vehicle));
    } catch (error) {
      errors.push({
        index,
        error: error instanceof Error ? error.message : 'Unknown error',
        data: vehicle,
      });
    }
  });

  if (errors.length > 0) {
    console.warn('Vehicle migration errors:', errors);
  }

  return results;
}

// ==================== 迁移验证工具 ====================

/**
 * 验证迁移结果
 */
export interface MigrationValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  summary: {
    totalItems: number;
    successfulMigrations: number;
    failedMigrations: number;
    successRate: number;
  };
}

/**
 * 验证任务迁移结果
 */
export function validateTaskMigration(
  original: any[],
  migrated: UnifiedTask[]
): MigrationValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查数量一致性
  if (original.length !== migrated.length) {
    errors.push(`Item count mismatch: original ${original.length}, migrated ${migrated.length}`);
  }

  // 检查关键字段
  migrated.forEach((task, index) => {
    const orig = original[index];
    if (!orig) return;

    if (task.id !== orig.id) {
      errors.push(`ID mismatch at index ${index}: ${task.id} !== ${orig.id}`);
    }

    if (task.taskNumber !== orig.taskNumber) {
      errors.push(`Task number mismatch at index ${index}`);
    }

    if (!task.dispatchStatus) {
      warnings.push(`Missing dispatch status at index ${index}`);
    }
  });

  const successfulMigrations = migrated.length;
  const failedMigrations = original.length - migrated.length;

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    summary: {
      totalItems: original.length,
      successfulMigrations,
      failedMigrations,
      successRate: original.length > 0 ? successfulMigrations / original.length : 0,
    },
  };
}

// ==================== 导出迁移报告 ====================

/**
 * 生成迁移报告
 */
export function generateMigrationReport(
  taskValidation: MigrationValidationResult,
  vehicleValidation: MigrationValidationResult
): string {
  const report = [
    '# 类型迁移报告',
    '',
    '## 任务迁移结果',
    `- 总数: ${taskValidation.summary.totalItems}`,
    `- 成功: ${taskValidation.summary.successfulMigrations}`,
    `- 失败: ${taskValidation.summary.failedMigrations}`,
    `- 成功率: ${(taskValidation.summary.successRate * 100).toFixed(2)}%`,
    '',
    '## 车辆迁移结果',
    `- 总数: ${vehicleValidation.summary.totalItems}`,
    `- 成功: ${vehicleValidation.summary.successfulMigrations}`,
    `- 失败: ${vehicleValidation.summary.failedMigrations}`,
    `- 成功率: ${(vehicleValidation.summary.successRate * 100).toFixed(2)}%`,
    '',
  ];

  if (taskValidation.errors.length > 0) {
    report.push('## 任务迁移错误');
    taskValidation.errors.forEach(error => report.push(`- ${error}`));
    report.push('');
  }

  if (vehicleValidation.errors.length > 0) {
    report.push('## 车辆迁移错误');
    vehicleValidation.errors.forEach(error => report.push(`- ${error}`));
    report.push('');
  }

  return report.join('\n');
}
