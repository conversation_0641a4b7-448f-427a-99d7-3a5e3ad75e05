'use client';

import React, { useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON>riangle,
  CheckCircle,
  Shield,
  TrendingUp,
  Lightbulb,
  Target,
  Zap,
  BarChart3,
  Settings,
  Eye,
  ThumbsUp,
  ThumbsDown,
  Info,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Progress } from '@/shared/components/progress';
import { cn } from '@/core/lib/utils';
import type { Task } from '@/core/types';

interface RatioMaterial {
  id: string;
  name: string;
  amount: number;
  category: string;
  moistureContent?: number;
  stoneContent?: number;
}

interface CalculationParams {
  density: number;
  waterRatio: number;
  waterAmount: number;
  sandRatio: number;
  cementAmount: number;
  flyashRatio: number;
  mineralPowderRatio: number;
  silicaFumeRatio: number;
  additiveRatio: number;
  antifreezeRatio: number;
  expansionRatio: number;
  earlyStrengthRatio: number;
  ultraFineSandRatio: number;
  s105Ratio: number;
  slump: number;
  airContent: number;
  strengthGrade: number;
}

interface QualityInspectionPanelProps {
  ratioMaterials: RatioMaterial[];
  calculationParams: CalculationParams;
  task?: Task | null;
  onOptimize?: (suggestions: OptimizationSuggestion[]) => void;
  onPreviewRatio?: () => void;
}

interface QualityMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  status: 'excellent' | 'good' | 'warning' | 'danger';
  description: string;
  icon: React.ReactNode;
}

interface OptimizationSuggestion {
  id: string;
  type: 'material' | 'parameter' | 'process';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  impact: string;
  action: string;
  icon: React.ReactNode;
}

export function QualityInspectionPanel({
  ratioMaterials,
  calculationParams,
  task,
  onOptimize,
  onPreviewRatio,
}: QualityInspectionPanelProps) {
  // 计算质量指标
  const qualityMetrics = useMemo((): QualityMetric[] => {
    const cementAmount = ratioMaterials.find(m => m.category === 'cement')?.amount || 0;
    const waterAmount = ratioMaterials.find(m => m.category === 'water')?.amount || 0;
    const actualWaterRatio = cementAmount > 0 ? waterAmount / cementAmount : 0;

    return [
      {
        id: 'strength',
        name: '强度预测',
        value: Math.max(
          20,
          Math.min(60, calculationParams.strengthGrade + (actualWaterRatio > 0.5 ? -5 : 5))
        ),
        unit: 'MPa',
        status: calculationParams.strengthGrade >= 30 ? 'good' : 'warning',
        description: '基于配比参数预测的28天抗压强度',
        icon: <Target className='h-4 w-4' />,
      },
      {
        id: 'workability',
        name: '工作性',
        value: Math.max(60, Math.min(100, 100 - Math.abs(calculationParams.slump - 180) / 2)),
        unit: '分',
        status:
          calculationParams.slump >= 150 && calculationParams.slump <= 200 ? 'good' : 'warning',
        description: '基于坍落度和外加剂用量评估的工作性',
        icon: <Settings className='h-4 w-4' />,
      },
      {
        id: 'durability',
        name: '耐久性',
        value: Math.max(70, Math.min(95, 85 + (actualWaterRatio < 0.45 ? 10 : -10))),
        unit: '分',
        status:
          actualWaterRatio <= 0.45 ? 'excellent' : actualWaterRatio <= 0.55 ? 'good' : 'warning',
        description: '基于水胶比和掺合料用量评估的耐久性',
        icon: <Shield className='h-4 w-4' />,
      },
      {
        id: 'economy',
        name: '经济性',
        value: Math.max(60, Math.min(90, 90 - (cementAmount - 350) / 10)),
        unit: '分',
        status: cementAmount <= 400 ? 'good' : cementAmount <= 450 ? 'warning' : 'danger',
        description: '基于材料成本和用量评估的经济性',
        icon: <TrendingUp className='h-4 w-4' />,
      },
    ];
  }, [ratioMaterials, calculationParams]);

  // 生成优化建议
  const optimizationSuggestions = useMemo((): OptimizationSuggestion[] => {
    const suggestions: OptimizationSuggestion[] = [];
    const cementAmount = ratioMaterials.find(m => m.category === 'cement')?.amount || 0;
    const waterAmount = ratioMaterials.find(m => m.category === 'water')?.amount || 0;
    const actualWaterRatio = cementAmount > 0 ? waterAmount / cementAmount : 0;

    // 水胶比优化建议
    if (actualWaterRatio > 0.5) {
      suggestions.push({
        id: 'water-ratio',
        type: 'parameter',
        priority: 'high',
        title: '水胶比偏高',
        description: `当前水胶比 ${actualWaterRatio.toFixed(2)}，建议控制在 0.45 以下`,
        impact: '提高强度和耐久性',
        action: '减少用水量或增加胶凝材料',
        icon: <AlertTriangle className='h-4 w-4' />,
      });
    }

    // 水泥用量优化建议
    if (cementAmount > 450) {
      suggestions.push({
        id: 'cement-amount',
        type: 'material',
        priority: 'medium',
        title: '水泥用量偏高',
        description: `当前水泥用量 ${cementAmount}kg/m³，可考虑部分替代`,
        impact: '降低成本和水化热',
        action: '增加粉煤灰或矿粉用量',
        icon: <Lightbulb className='h-4 w-4' />,
      });
    }

    // 坍落度优化建议
    if (calculationParams.slump < 150 || calculationParams.slump > 220) {
      suggestions.push({
        id: 'slump',
        type: 'parameter',
        priority: 'medium',
        title: '坍落度需调整',
        description: `当前坍落度 ${calculationParams.slump}mm，建议控制在 150-200mm`,
        impact: '改善施工性能',
        action: '调整外加剂用量或用水量',
        icon: <Settings className='h-4 w-4' />,
      });
    }

    // 外加剂用量建议
    if (calculationParams.additiveRatio < 0.8) {
      suggestions.push({
        id: 'additive',
        type: 'material',
        priority: 'low',
        title: '外加剂用量偏低',
        description: `当前外加剂掺量 ${calculationParams.additiveRatio}%，可适当增加`,
        impact: '改善工作性和强度',
        action: '增加减水剂用量至 1.0-1.5%',
        icon: <Zap className='h-4 w-4' />,
      });
    }

    return suggestions.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }, [ratioMaterials, calculationParams]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'good':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'danger':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-50';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50';
      case 'low':
        return 'text-blue-600 bg-blue-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // 如果没有材料，显示提示
  if (ratioMaterials.length === 0) {
    return (
      <div className='space-y-2 h-full overflow-y-auto'>
        <Card>
          <CardContent className='p-4 text-center'>
            <Eye className='h-8 w-8 mx-auto mb-2 text-muted-foreground' />
            <div className='text-sm text-muted-foreground'>暂无配比数据</div>
            <div className='text-xs text-muted-foreground mt-1'>请先添加配比材料进行质量检测</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='space-y-2 h-full overflow-y-auto'>
      {/* 质量检测指标 */}
      <Card>
        <CardHeader className='pb-2 pt-3'>
          <CardTitle className='text-sm flex items-center gap-2'>
            <BarChart3 className='h-4 w-4 text-primary' />
            质量检测
          </CardTitle>
        </CardHeader>
        <CardContent className='p-2 space-y-3'>
          {qualityMetrics.map(metric => (
            <div key={metric.id} className='space-y-1'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-2'>
                  {metric.icon}
                  <span className='text-xs font-medium'>{metric.name}</span>
                </div>
                <Badge className={cn('text-xs', getStatusColor(metric.status))}>
                  {metric.value.toFixed(0)}
                  {metric.unit}
                </Badge>
              </div>
              <Progress value={metric.value} className='h-1.5' />
              <p className='text-xs text-muted-foreground'>{metric.description}</p>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 优化建议 */}
      <Card>
        <CardHeader className='pb-2 pt-3'>
          <CardTitle className='text-sm flex items-center gap-2'>
            <Lightbulb className='h-4 w-4 text-primary' />
            优化建议
            <Badge variant='outline' className='text-xs ml-auto'>
              {optimizationSuggestions.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className='p-2 space-y-2'>
          {optimizationSuggestions.length === 0 ? (
            <div className='text-center py-4'>
              <CheckCircle className='h-6 w-6 mx-auto mb-2 text-green-500' />
              <div className='text-xs text-muted-foreground'>配比参数良好</div>
              <div className='text-xs text-muted-foreground'>暂无优化建议</div>
            </div>
          ) : (
            optimizationSuggestions.map(suggestion => (
              <div
                key={suggestion.id}
                className='p-2 border rounded-lg space-y-1 hover:bg-muted/50 transition-colors'
              >
                <div className='flex items-start gap-2'>
                  <div className='mt-0.5'>{suggestion.icon}</div>
                  <div className='flex-1 min-w-0'>
                    <div className='flex items-center gap-2 mb-1'>
                      <span className='text-xs font-medium'>{suggestion.title}</span>
                      <Badge className={cn('text-xs', getPriorityColor(suggestion.priority))}>
                        {suggestion.priority}
                      </Badge>
                    </div>
                    <p className='text-xs text-muted-foreground mb-1'>{suggestion.description}</p>
                    <div className='flex items-center gap-1 text-xs'>
                      <ThumbsUp className='h-3 w-3 text-green-500' />
                      <span className='text-green-600'>{suggestion.impact}</span>
                    </div>
                    <div className='flex items-center gap-1 text-xs mt-1'>
                      <Info className='h-3 w-3 text-blue-500' />
                      <span className='text-blue-600'>{suggestion.action}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <Card>
        <CardContent className='p-2 space-y-2'>
          <Button
            onClick={() => onOptimize?.(optimizationSuggestions)}
            size='sm'
            className='w-full text-xs h-7'
            disabled={optimizationSuggestions.length === 0}
          >
            <Zap className='h-3 w-3 mr-1' />
            应用优化建议
          </Button>
          <Button
            onClick={onPreviewRatio}
            variant='outline'
            size='sm'
            className='w-full text-xs h-7'
          >
            <Eye className='h-3 w-3 mr-1' />
            预览配比单
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
