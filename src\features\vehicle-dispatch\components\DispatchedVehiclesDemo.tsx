// src/features/vehicle-dispatch/components/DispatchedVehiclesDemo.tsx
'use client';

import React, { useState, useCallback } from 'react';
import { toast } from 'sonner';

import { generateDispatchedVehicles } from '@/infrastructure/api/mock/mock-data';
import type { DispatchedVehicle } from '@/core/types';
import { DispatchedVehiclesContainer } from './DispatchedVehiclesContainer';

/**
 * 已出厂车辆表格演示组件
 */
export const DispatchedVehiclesDemo: React.FC = () => {
  const [data, setData] = useState<DispatchedVehicle[]>(() => generateDispatchedVehicles(50));
  const [isLoading, setIsLoading] = useState(false);

  // 刷新数据
  const handleRefresh = useCallback(async () => {
    setIsLoading(true);
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      const newData = generateDispatchedVehicles(50);
      setData(newData);
      toast.success('数据刷新成功');
    } catch (error) {
      toast.error('数据刷新失败');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 导出数据
  const handleExport = useCallback(() => {
    try {
      // 转换数据为CSV格式
      const headers = [
        '车号',
        '司机',
        '出站时间',
        '到工地时间',
        '返程时间',
        '去程时长(分钟)',
        '工地时长(分钟)',
        '返程时长(分钟)',
        '总时长(分钟)',
        '发货单编号',
        '往返类型',
        '项目名称',
        '任务编号',
        '备注',
      ];

      const csvContent = [
        headers.join(','),
        ...data.map(vehicle =>
          [
            vehicle.vehicleNumber,
            vehicle.driver,
            vehicle.departureTime,
            vehicle.arrivalTime || '',
            vehicle.returnTime || '',
            vehicle.outboundDuration || '',
            vehicle.siteStayDuration || '',
            vehicle.returnDuration || '',
            vehicle.totalDuration || '',
            vehicle.deliveryOrderNumber,
            vehicle.isRoundTrip ? '往返' : '单程',
            vehicle.projectName || '',
            vehicle.taskNumber || '',
            vehicle.notes || '',
          ].join(',')
        ),
      ].join('\n');

      // 创建下载链接
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `已出厂车辆_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('数据导出成功');
    } catch (error) {
      toast.error('数据导出失败');
    }
  }, [data]);

  // 筛选数据
  const handleFilter = useCallback(() => {
    // TODO: 实现筛选功能
    toast.info('筛选功能开发中...');
  }, []);

  return (
    <div className='h-full flex flex-col p-4 bg-gray-50'>
      <div className='mb-4'>
        <h1 className='text-2xl font-bold text-gray-900'>已出厂车辆管理</h1>
        <p className='text-gray-600 mt-1'>
          查看和管理已出厂车辆的行程信息，包括出站时间、到工地时间、返程时间等详细数据。
        </p>
      </div>

      <div className='flex-1 min-h-0'>
        <DispatchedVehiclesContainer data={data} />
      </div>

      {/* 加载状态 */}
      {isLoading && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white rounded-lg p-6 flex items-center gap-3'>
            <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600'></div>
            <span>正在刷新数据...</span>
          </div>
        </div>
      )}
    </div>
  );
};
