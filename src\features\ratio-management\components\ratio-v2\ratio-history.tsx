'use client';

import React, { useState } from 'react';

import {
  <PERSON>ert<PERSON>riangle,
  Calendar,
  CheckCircle,
  Clock,
  Copy,
  Download,
  Eye,
  FileText,
  Filter,
  History,
  RotateCcw,
  Search,
  TrendingUp,
  User,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { But<PERSON> } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/tabs';
import { useToast } from '@/shared/hooks/use-toast';
import { cn } from '@/core/lib/utils';

interface RatioHistoryRecord {
  id: string;
  taskNumber: string;
  projectName: string;
  strength: string;
  createTime: string;
  operator: string;
  status: 'draft' | 'approved' | 'applied' | 'archived';
  version: number;
  materials: {
    cement: number;
    water: number;
    sand: number;
    stone: number;
    flyash?: number;
    additive?: number;
  };
  parameters: {
    waterRatio: number;
    sandRatio: number;
    density: number;
  };
  qualityScore: number;
  strengthPrediction: number;
  notes?: string;
  approver?: string;
  approveTime?: string;
}

interface RatioHistoryProps {
  isOpen: boolean;
  onClose: () => void;
  taskId: string;
  onApplyHistory: (record: RatioHistoryRecord) => void;
}

export function RatioHistory({ isOpen, onClose, taskId, onApplyHistory }: RatioHistoryProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedRecord, setSelectedRecord] = useState<RatioHistoryRecord | null>(null);
  const [activeTab, setActiveTab] = useState('list');

  // 模拟历史记录数据
  const historyRecords: RatioHistoryRecord[] = [
    {
      id: 'history-1',
      taskNumber: 'C25-20241228-001',
      projectName: '某商业综合体项目',
      strength: 'C25',
      createTime: '2024-12-28 14:30:25',
      operator: '张工程师',
      status: 'approved',
      version: 3,
      materials: {
        cement: 380,
        water: 180,
        sand: 650,
        stone: 1200,
        flyash: 60,
        additive: 4.5,
      },
      parameters: {
        waterRatio: 0.45,
        sandRatio: 35,
        density: 2.4,
      },
      qualityScore: 92,
      strengthPrediction: 28.5,
      notes: '经过AI优化后的配比方案，质量评分显著提升',
      approver: '李总工',
      approveTime: '2024-12-28 16:15:30',
    },
    {
      id: 'history-2',
      taskNumber: 'C25-20241228-002',
      projectName: '某商业综合体项目',
      strength: 'C25',
      createTime: '2024-12-28 10:15:10',
      operator: '张工程师',
      status: 'applied',
      version: 2,
      materials: {
        cement: 400,
        water: 185,
        sand: 630,
        stone: 1180,
        additive: 3.8,
      },
      parameters: {
        waterRatio: 0.46,
        sandRatio: 34,
        density: 2.38,
      },
      qualityScore: 78,
      strengthPrediction: 26.2,
      notes: '初始配比方案，已应用于生产',
    },
    {
      id: 'history-3',
      taskNumber: 'C25-20241227-001',
      projectName: '某商业综合体项目',
      strength: 'C25',
      createTime: '2024-12-27 16:45:20',
      operator: '王技术员',
      status: 'draft',
      version: 1,
      materials: {
        cement: 420,
        water: 190,
        sand: 640,
        stone: 1150,
        additive: 3.2,
      },
      parameters: {
        waterRatio: 0.48,
        sandRatio: 36,
        density: 2.35,
      },
      qualityScore: 65,
      strengthPrediction: 24.8,
      notes: '草稿版本，待进一步优化',
    },
  ];

  const filteredRecords = historyRecords.filter(record => {
    const matchesSearch =
      record.taskNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.operator.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || record.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <FileText className='h-4 w-4 text-gray-500' />;
      case 'approved':
        return <CheckCircle className='h-4 w-4 text-green-500' />;
      case 'applied':
        return <TrendingUp className='h-4 w-4 text-blue-500' />;
      case 'archived':
        return <Clock className='h-4 w-4 text-orange-500' />;
      default:
        return <FileText className='h-4 w-4' />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-700 border-gray-200';
      case 'approved':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'applied':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'archived':
        return 'bg-orange-100 text-orange-700 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft':
        return '草稿';
      case 'approved':
        return '已审批';
      case 'applied':
        return '已应用';
      case 'archived':
        return '已归档';
      default:
        return status;
    }
  };

  const handleApplyRecord = (record: RatioHistoryRecord) => {
    onApplyHistory(record);
    onClose();

    toast({
      title: '历史配比已应用',
      description: `已应用 ${record.taskNumber} 的配比方案`,
    });
  };

  const handleCopyRecord = (record: RatioHistoryRecord) => {
    const copyText = `
任务编号: ${record.taskNumber}
项目名称: ${record.projectName}
强度等级: ${record.strength}
水胶比: ${record.parameters.waterRatio}
砂率: ${record.parameters.sandRatio}%
密度: ${record.parameters.density} t/m³
水泥: ${record.materials.cement}kg
用水: ${record.materials.water}kg
砂: ${record.materials.sand}kg
石子: ${record.materials.stone}kg
${record.materials.flyash ? `粉煤灰: ${record.materials.flyash}kg` : ''}
${record.materials.additive ? `外加剂: ${record.materials.additive}kg` : ''}
质量评分: ${record.qualityScore}/100
强度预测: ${record.strengthPrediction} MPa
    `.trim();

    navigator.clipboard.writeText(copyText);

    toast({
      title: '配比信息已复制',
      description: '配比详细信息已复制到剪贴板',
    });
  };

  const handleExportRecord = (record: RatioHistoryRecord) => {
    // 模拟导出功能
    toast({
      title: '导出成功',
      description: `${record.taskNumber} 配比报告已导出`,
    });
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 z-[100] bg-black/50 flex items-center justify-center p-4'>
      <div className='bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto'>
        <div className='p-6 border-b'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <History className='h-6 w-6 text-primary' />
              <h2 className='text-xl font-semibold'>配比历史记录</h2>
              <Badge variant='outline' className='ml-2'>
                {filteredRecords.length} 条记录
              </Badge>
            </div>
            <Button variant='ghost' size='sm' onClick={onClose}>
              ✕
            </Button>
          </div>
        </div>

        <div className='p-6'>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger value='list'>历史列表</TabsTrigger>
              <TabsTrigger value='statistics'>统计分析</TabsTrigger>
            </TabsList>

            <TabsContent value='list' className='space-y-4'>
              {/* 搜索和筛选 */}
              <div className='flex gap-4'>
                <div className='flex-1 relative'>
                  <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
                  <Input
                    placeholder='搜索任务编号、项目名称或操作员...'
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    className='pl-10'
                  />
                </div>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className='w-40'>
                    <SelectValue placeholder='状态筛选' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>全部状态</SelectItem>
                    <SelectItem value='draft'>草稿</SelectItem>
                    <SelectItem value='approved'>已审批</SelectItem>
                    <SelectItem value='applied'>已应用</SelectItem>
                    <SelectItem value='archived'>已归档</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 历史记录列表 */}
              <div className='space-y-3'>
                {filteredRecords.map(record => (
                  <Card key={record.id} className='hover:shadow-md transition-shadow'>
                    <CardContent className='p-4'>
                      <div className='flex items-center justify-between'>
                        <div className='flex items-center gap-4'>
                          <div className='flex items-center gap-2'>
                            {getStatusIcon(record.status)}
                            <Badge className={getStatusColor(record.status)}>
                              {getStatusLabel(record.status)}
                            </Badge>
                          </div>

                          <div>
                            <h4 className='font-medium'>{record.taskNumber}</h4>
                            <p className='text-sm text-muted-foreground'>
                              {record.projectName} · {record.strength}
                            </p>
                          </div>

                          <div className='text-sm text-muted-foreground'>
                            <div className='flex items-center gap-1'>
                              <User className='h-3 w-3' />
                              {record.operator}
                            </div>
                            <div className='flex items-center gap-1'>
                              <Calendar className='h-3 w-3' />
                              {record.createTime}
                            </div>
                          </div>
                        </div>

                        <div className='flex items-center gap-4'>
                          <div className='text-right text-sm'>
                            <div className='font-medium'>
                              质量评分:{' '}
                              <span
                                className={cn(
                                  record.qualityScore >= 90
                                    ? 'text-green-600'
                                    : record.qualityScore >= 70
                                      ? 'text-yellow-600'
                                      : 'text-red-600'
                                )}
                              >
                                {record.qualityScore}/100
                              </span>
                            </div>
                            <div className='text-muted-foreground'>
                              强度预测: {record.strengthPrediction} MPa
                            </div>
                          </div>

                          <div className='flex gap-1'>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => setSelectedRecord(record)}
                              className='h-8 w-8 p-0'
                            >
                              <Eye className='h-4 w-4' />
                            </Button>

                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => handleCopyRecord(record)}
                              className='h-8 w-8 p-0'
                            >
                              <Copy className='h-4 w-4' />
                            </Button>

                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => handleExportRecord(record)}
                              className='h-8 w-8 p-0'
                            >
                              <Download className='h-4 w-4' />
                            </Button>

                            <Button
                              size='sm'
                              onClick={() => handleApplyRecord(record)}
                              className='gap-1'
                            >
                              <RotateCcw className='h-3 w-3' />
                              应用
                            </Button>
                          </div>
                        </div>
                      </div>

                      {record.notes && (
                        <div className='mt-3 p-2 bg-muted/50 rounded text-sm'>
                          <strong>备注:</strong> {record.notes}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredRecords.length === 0 && (
                <div className='text-center py-12'>
                  <History className='h-16 w-16 text-muted-foreground mx-auto mb-4' />
                  <h3 className='text-lg font-semibold mb-2'>暂无历史记录</h3>
                  <p className='text-muted-foreground'>
                    {searchTerm || statusFilter !== 'all'
                      ? '没有找到匹配的记录'
                      : '还没有配比历史记录'}
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value='statistics' className='space-y-4'>
              <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
                <Card>
                  <CardContent className='p-4 text-center'>
                    <div className='text-2xl font-bold text-blue-600'>{historyRecords.length}</div>
                    <div className='text-sm text-muted-foreground'>总记录数</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className='p-4 text-center'>
                    <div className='text-2xl font-bold text-green-600'>
                      {historyRecords.filter(r => r.status === 'approved').length}
                    </div>
                    <div className='text-sm text-muted-foreground'>已审批</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className='p-4 text-center'>
                    <div className='text-2xl font-bold text-blue-600'>
                      {historyRecords.filter(r => r.status === 'applied').length}
                    </div>
                    <div className='text-sm text-muted-foreground'>已应用</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className='p-4 text-center'>
                    <div className='text-2xl font-bold text-purple-600'>
                      {Math.round(
                        historyRecords.reduce((sum, r) => sum + r.qualityScore, 0) /
                          historyRecords.length
                      )}
                    </div>
                    <div className='text-sm text-muted-foreground'>平均评分</div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>

          {/* 详细信息模态框 */}
          {selectedRecord && (
            <Dialog open={!!selectedRecord} onOpenChange={() => setSelectedRecord(null)}>
              <DialogContent className='max-w-4xl'>
                <DialogHeader>
                  <DialogTitle>配比详细信息 - {selectedRecord.taskNumber}</DialogTitle>
                </DialogHeader>

                <div className='grid grid-cols-2 gap-6'>
                  <div className='space-y-4'>
                    <Card>
                      <CardHeader className='pb-3'>
                        <CardTitle className='text-base'>基本信息</CardTitle>
                      </CardHeader>
                      <CardContent className='space-y-2 text-sm'>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>任务编号:</span>
                          <span className='font-mono'>{selectedRecord.taskNumber}</span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>项目名称:</span>
                          <span>{selectedRecord.projectName}</span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>强度等级:</span>
                          <span>{selectedRecord.strength}</span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>操作员:</span>
                          <span>{selectedRecord.operator}</span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>创建时间:</span>
                          <span>{selectedRecord.createTime}</span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className='pb-3'>
                        <CardTitle className='text-base'>计算参数</CardTitle>
                      </CardHeader>
                      <CardContent className='space-y-2 text-sm'>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>水胶比:</span>
                          <span className='font-mono'>{selectedRecord.parameters.waterRatio}</span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>砂率:</span>
                          <span className='font-mono'>{selectedRecord.parameters.sandRatio}%</span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>密度:</span>
                          <span className='font-mono'>
                            {selectedRecord.parameters.density} t/m³
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className='space-y-4'>
                    <Card>
                      <CardHeader className='pb-3'>
                        <CardTitle className='text-base'>材料用量</CardTitle>
                      </CardHeader>
                      <CardContent className='space-y-2 text-sm'>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>水泥:</span>
                          <span className='font-mono'>{selectedRecord.materials.cement} kg</span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>用水:</span>
                          <span className='font-mono'>{selectedRecord.materials.water} kg</span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>砂:</span>
                          <span className='font-mono'>{selectedRecord.materials.sand} kg</span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>石子:</span>
                          <span className='font-mono'>{selectedRecord.materials.stone} kg</span>
                        </div>
                        {selectedRecord.materials.flyash && (
                          <div className='flex justify-between'>
                            <span className='text-muted-foreground'>粉煤灰:</span>
                            <span className='font-mono'>{selectedRecord.materials.flyash} kg</span>
                          </div>
                        )}
                        {selectedRecord.materials.additive && (
                          <div className='flex justify-between'>
                            <span className='text-muted-foreground'>外加剂:</span>
                            <span className='font-mono'>
                              {selectedRecord.materials.additive} kg
                            </span>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className='pb-3'>
                        <CardTitle className='text-base'>质量评估</CardTitle>
                      </CardHeader>
                      <CardContent className='space-y-2 text-sm'>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>质量评分:</span>
                          <span
                            className={cn(
                              'font-mono font-bold',
                              selectedRecord.qualityScore >= 90
                                ? 'text-green-600'
                                : selectedRecord.qualityScore >= 70
                                  ? 'text-yellow-600'
                                  : 'text-red-600'
                            )}
                          >
                            {selectedRecord.qualityScore}/100
                          </span>
                        </div>
                        <div className='flex justify-between'>
                          <span className='text-muted-foreground'>强度预测:</span>
                          <span className='font-mono'>{selectedRecord.strengthPrediction} MPa</span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <div className='flex justify-end gap-2 pt-4 border-t'>
                  <Button variant='outline' onClick={() => setSelectedRecord(null)}>
                    关闭
                  </Button>
                  <Button onClick={() => handleApplyRecord(selectedRecord)}>应用此配比</Button>
                </div>
              </DialogContent>
            </Dialog>
          )}

          <div className='flex justify-end pt-4 border-t'>
            <Button onClick={onClose}>关闭</Button>
          </div>
        </div>
      </div>
    </div>
  );
}
