# TMH任务调度系统 - 安装PuTTY工具
# 用于Windows环境下的SSH密码自动认证

param(
    [switch]$Force
)

Write-Host "🔧 TMH任务调度系统 - 安装PuTTY工具" -ForegroundColor Green
Write-Host "🎯 目标: 安装PuTTY实现SSH密码自动认证" -ForegroundColor Cyan
Write-Host ""

# 检查是否已安装PuTTY
$hasPlink = Get-Command plink -ErrorAction SilentlyContinue
$hasPscp = Get-Command pscp -ErrorAction SilentlyContinue

if ($hasPlink -and $hasPscp -and -not $Force) {
    Write-Host "✅ PuTTY工具已安装" -ForegroundColor Green
    Write-Host "  plink: $($hasPlink.Source)" -ForegroundColor White
    Write-Host "  pscp: $($hasPscp.Source)" -ForegroundColor White
    Write-Host "使用 -Force 参数强制重新安装" -ForegroundColor Yellow
    exit 0
}

Write-Host "📦 开始安装PuTTY工具..." -ForegroundColor Blue

# 方法1: 使用Chocolatey安装
try {
    $chocoVersion = choco -v 2>&1
    Write-Host "✅ Chocolatey已安装: $chocoVersion" -ForegroundColor Green
    
    Write-Host "📦 使用Chocolatey安装PuTTY..." -ForegroundColor Blue
    choco install putty -y
    
    # 验证安装
    $newPlink = Get-Command plink -ErrorAction SilentlyContinue
    $newPscp = Get-Command pscp -ErrorAction SilentlyContinue
    
    if ($newPlink -and $newPscp) {
        Write-Host "✅ PuTTY安装成功" -ForegroundColor Green
        Write-Host "  plink: $($newPlink.Source)" -ForegroundColor White
        Write-Host "  pscp: $($newPscp.Source)" -ForegroundColor White
        exit 0
    } else {
        Write-Host "⚠️ Chocolatey安装可能不完整" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Chocolatey不可用: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 方法2: 手动下载安装
Write-Host "📥 尝试手动下载PuTTY..." -ForegroundColor Blue
try {
    $downloadUrl = "https://the.earth.li/~sgtatham/putty/latest/w64/putty.zip"
    $tempDir = "$env:TEMP\putty-install"
    $zipFile = "$tempDir\putty.zip"
    $installDir = "$env:ProgramFiles\PuTTY"
    
    # 创建临时目录
    if (Test-Path $tempDir) {
        Remove-Item $tempDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
    
    Write-Host "📥 下载PuTTY..." -ForegroundColor Blue
    Invoke-WebRequest -Uri $downloadUrl -OutFile $zipFile -UseBasicParsing
    
    Write-Host "📦 解压文件..." -ForegroundColor Blue
    Expand-Archive -Path $zipFile -DestinationPath $tempDir -Force
    
    # 创建安装目录
    if (-not (Test-Path $installDir)) {
        New-Item -ItemType Directory -Path $installDir -Force | Out-Null
    }
    
    # 复制文件到安装目录
    $puttyFiles = Get-ChildItem -Path $tempDir -Recurse -Name "*.exe"
    foreach ($file in $puttyFiles) {
        $sourcePath = Join-Path $tempDir $file
        $destPath = Join-Path $installDir (Split-Path $file -Leaf)
        Copy-Item $sourcePath $destPath -Force
        Write-Host "  复制: $(Split-Path $file -Leaf)" -ForegroundColor White
    }
    
    # 添加到PATH环境变量
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
    if ($currentPath -notlike "*$installDir*") {
        Write-Host "📝 添加到PATH环境变量..." -ForegroundColor Blue
        [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$installDir", "Machine")
        
        # 更新当前会话的PATH
        $env:PATH += ";$installDir"
    }
    
    # 验证安装
    $newPlink = Get-Command plink -ErrorAction SilentlyContinue
    $newPscp = Get-Command pscp -ErrorAction SilentlyContinue
    
    if ($newPlink -and $newPscp) {
        Write-Host "✅ PuTTY手动安装成功" -ForegroundColor Green
        Write-Host "  安装目录: $installDir" -ForegroundColor White
        Write-Host "  plink: $($newPlink.Source)" -ForegroundColor White
        Write-Host "  pscp: $($newPscp.Source)" -ForegroundColor White
    } else {
        Write-Host "⚠️ 手动安装可能不完整，请重启PowerShell后重试" -ForegroundColor Yellow
    }
    
    # 清理临时文件
    Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue
    
} catch {
    Write-Host "❌ 手动下载安装失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 方法3: 使用winget安装
try {
    $wingetVersion = winget --version 2>&1
    Write-Host "✅ winget可用: $wingetVersion" -ForegroundColor Green
    
    Write-Host "📦 使用winget安装PuTTY..." -ForegroundColor Blue
    winget install PuTTY.PuTTY
    
    # 验证安装
    $newPlink = Get-Command plink -ErrorAction SilentlyContinue
    $newPscp = Get-Command pscp -ErrorAction SilentlyContinue
    
    if ($newPlink -and $newPscp) {
        Write-Host "✅ PuTTY winget安装成功" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "⚠️ winget不可用或安装失败" -ForegroundColor Yellow
}

# 最终检查
$finalPlink = Get-Command plink -ErrorAction SilentlyContinue
$finalPscp = Get-Command pscp -ErrorAction SilentlyContinue

if ($finalPlink -and $finalPscp) {
    Write-Host ""
    Write-Host "🎉 PuTTY安装完成!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 现在可以使用:" -ForegroundColor Cyan
    Write-Host "1. 自动部署: npm run deploy:intranet" -ForegroundColor White
    Write-Host "2. 测试连接: plink -ssh administrator@************* echo 'test'" -ForegroundColor White
    Write-Host ""
    Write-Host "✅ SSH密码将自动处理，无需手动输入" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "❌ PuTTY安装失败" -ForegroundColor Red
    Write-Host ""
    Write-Host "📋 手动安装选项:" -ForegroundColor Cyan
    Write-Host "1. 下载PuTTY: https://www.putty.org/" -ForegroundColor White
    Write-Host "2. 安装Chocolatey: https://chocolatey.org/install" -ForegroundColor White
    Write-Host "3. 使用SSH密钥认证: npm run setup:ssh" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 建议: 重启PowerShell后重试" -ForegroundColor Yellow
}
