#!/usr/bin/env node

/**
 * 修复代码分包调整后的导入路径问题
 * 自动扫描和修复常见的路径错误
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 路径映射规则
const PATH_MAPPINGS = {
  // 组件路径映射
  '@/components/ui/': '@/shared/components/',
  '@/components/layout/': '@/shared/components/layout/',
  '@/components/common/': '@/shared/components/common/',
  '@/components/task-list/': '@/features/task-management/components/',
  '@/components/vehicle-dispatch/': '@/features/vehicle-dispatch/components/',
  '@/components/ratio/': '@/features/ratio-management/components/',
  '@/components/settings/': '@/features/system-settings/components/',

  // Hook路径映射
  '@/hooks/task-list/': '@/features/task-management/hooks/',
  '@/hooks/vehicle-dispatch/': '@/features/vehicle-dispatch/hooks/',
  '@/hooks/ratio/': '@/features/ratio-management/hooks/',
  '@/hooks/common/': '@/shared/hooks/',
  '@/hooks/use-toast': '@/shared/hooks/use-toast',

  // 服务路径映射
  '@/services/api/': '@/core/api/',
  '@/services/business/': '@/shared/services/',
  '@/services/ratio/': '@/features/ratio-management/services/',

  // 工具路径映射
  '@/utils/common/': '@/shared/utils/',
  '@/utils/type-safety/': '@/shared/utils/',
  '@/utils/performance/': '@/shared/utils/',
  '@/lib/utils': '@/core/lib/utils',

  // 类型路径映射
  '@/types/': '@/core/types/',
  '@/types/taskCardConfig': '@/core/types/taskCardConfig',

  // 存储路径映射
  '@/store/': '@/core/store/',
  '@/store/appStore': '@/infrastructure/storage/stores/appStore',
  '@/store/groupingStore': '@/features/task-management/store/groupingStore',

  // 常量路径映射
  '@/constants/': '@/core/constants/',
  '@/constants/dndItemTypes': '@/core/constants/dndItemTypes',

  // 上下文路径映射
  '@/contexts/': '@/core/contexts/',
};

// 特殊文件路径映射
const SPECIAL_MAPPINGS = {
  '@/utils/persistenceManager': '@/infrastructure/storage/persistence/persistenceManager',
  '@/utils/performance-cache': '@/infrastructure/storage/cache/performance-cache',
  '@/utils/task-grouping': '@/core/utils/task-grouping',
  '@/utils/dom-safe-operations': '@/core/utils/dom-safe-operations',
};

/**
 * 扫描所有TypeScript和TSX文件
 */
function scanFiles() {
  const patterns = [
    'src/**/*.ts',
    'src/**/*.tsx',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts',
    '!src/**/*.test.tsx',
  ];

  const files = [];
  patterns.forEach(pattern => {
    const matches = glob.sync(pattern, { cwd: process.cwd() });
    files.push(...matches);
  });

  return [...new Set(files)]; // 去重
}

/**
 * 修复文件中的导入路径
 */
function fixImportsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // 应用特殊映射
    Object.entries(SPECIAL_MAPPINGS).forEach(([oldPath, newPath]) => {
      const regex = new RegExp(
        `from ['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`,
        'g'
      );
      if (regex.test(content)) {
        content = content.replace(regex, `from '${newPath}'`);
        hasChanges = true;
        console.log(`  ✓ ${oldPath} → ${newPath}`);
      }
    });

    // 应用通用映射
    Object.entries(PATH_MAPPINGS).forEach(([oldPath, newPath]) => {
      const regex = new RegExp(
        `from ['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}([^'"]*?)['"]`,
        'g'
      );
      const matches = content.match(regex);
      if (matches) {
        content = content.replace(regex, (match, suffix) => {
          const result = `from '${newPath}${suffix}'`;
          console.log(`  ✓ ${oldPath}${suffix} → ${newPath}${suffix}`);
          return result;
        });
        hasChanges = true;
      }
    });

    // 保存文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath}`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 开始修复导入路径...\n');

  const files = scanFiles();
  console.log(`📁 找到 ${files.length} 个文件\n`);

  let fixedFiles = 0;
  let totalFixes = 0;

  files.forEach(file => {
    console.log(`🔍 检查: ${file}`);
    const fixed = fixImportsInFile(file);
    if (fixed) {
      fixedFiles++;
      console.log(`✅ 已修复: ${file}\n`);
    } else {
      console.log(`⏭️  无需修复: ${file}\n`);
    }
  });

  console.log('📊 修复完成!');
  console.log(`   修复文件数: ${fixedFiles}`);
  console.log(`   总文件数: ${files.length}`);
  console.log(`   修复率: ${((fixedFiles / files.length) * 100).toFixed(1)}%`);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { fixImportsInFile, PATH_MAPPINGS, SPECIAL_MAPPINGS };
