/**
 * 任务列表专用Store
 * 管理任务列表相关的状态和操作
 */

import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { produce } from 'immer';
import type { Task, Vehicle, TaskGroupConfig } from '@/core/types';

export interface TaskListState {
  // 数据状态
  tasks: Task[];
  vehicles: Vehicle[];
  filteredTasks: Task[];
  selectedTaskIds: string[];

  // UI状态
  displayMode: 'table' | 'card' | 'grid';
  density: 'compact' | 'normal' | 'comfortable';
  groupConfig: TaskGroupConfig | null;
  isLoading: boolean;
  error: string | null;

  // 头部模式
  headerMode: 'fixed' | 'floating';
  floatingHeaderPosition: { x: number; y: number };

  // 拖拽状态
  dragState: {
    isDragging: boolean;
    draggedItem: any | null;
    dropTarget: string | null;
    dragType: 'task' | 'vehicle' | null;
  };

  // 模态框状态
  modals: {
    settings: boolean;
    columnVisibility: boolean;
    groupConfig: boolean;
    taskProgress: boolean;
    vehicleDispatch: boolean;
    qrCode: boolean;
    abbreviation: boolean;
    tankerNote: boolean;
    reminderConfig: boolean;
    deliveryOrderDetails: boolean;
  };

  // 选中的任务/车辆信息
  selectedTask: Task | null;
  selectedVehicle: Vehicle | null;

  // 操作状态
  operationStatus: {
    dispatching: boolean;
    reordering: boolean;
    updating: boolean;
  };
}

export interface TaskListActions {
  // 数据操作
  setTasks: (tasks: Task[]) => void;
  setVehicles: (vehicles: Vehicle[]) => void;
  updateTask: (taskId: string, updates: Partial<Task>) => void;
  updateVehicle: (vehicleId: string, updates: Partial<Vehicle>) => void;
  setFilteredTasks: (tasks: Task[]) => void;

  // 选择操作
  selectTask: (taskId: string) => void;
  selectMultipleTasks: (taskIds: string[]) => void;
  clearTaskSelection: () => void;
  toggleTaskSelection: (taskId: string) => void;
  setSelectedTask: (task: Task | null) => void;
  setSelectedVehicle: (vehicle: Vehicle | null) => void;

  // UI状态操作
  setDisplayMode: (mode: 'table' | 'card' | 'grid') => void;
  setDensity: (density: 'compact' | 'normal' | 'comfortable') => void;
  setGroupConfig: (config: TaskGroupConfig | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // 头部模式操作
  setHeaderMode: (mode: 'fixed' | 'floating') => void;
  setFloatingHeaderPosition: (position: { x: number; y: number }) => void;

  // 拖拽操作
  startDrag: (item: any, type: 'task' | 'vehicle') => void;
  updateDragTarget: (target: string | null) => void;
  endDrag: () => void;

  // 模态框操作
  openModal: (modalName: keyof TaskListState['modals']) => void;
  closeModal: (modalName: keyof TaskListState['modals']) => void;
  closeAllModals: () => void;

  // 操作状态
  setOperationStatus: (operation: keyof TaskListState['operationStatus'], status: boolean) => void;

  // 重置状态
  reset: () => void;
}

const initialState: TaskListState = {
  // 数据状态
  tasks: [],
  vehicles: [],
  filteredTasks: [],
  selectedTaskIds: [],

  // UI状态
  displayMode: 'table',
  density: 'normal',
  groupConfig: null,
  isLoading: false,
  error: null,

  // 头部模式
  headerMode: 'fixed',
  floatingHeaderPosition: { x: 20, y: 100 },

  // 拖拽状态
  dragState: {
    isDragging: false,
    draggedItem: null,
    dropTarget: null,
    dragType: null,
  },

  // 模态框状态
  modals: {
    settings: false,
    columnVisibility: false,
    groupConfig: false,
    taskProgress: false,
    vehicleDispatch: false,
    qrCode: false,
    abbreviation: false,
    tankerNote: false,
    reminderConfig: false,
    deliveryOrderDetails: false,
  },

  // 选中的任务/车辆信息
  selectedTask: null,
  selectedVehicle: null,

  // 操作状态
  operationStatus: {
    dispatching: false,
    reordering: false,
    updating: false,
  },
};

export const useTaskListStore = create<TaskListState & TaskListActions>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      ...initialState,

      // 数据操作
      setTasks: tasks => set({ tasks }),
      setVehicles: vehicles => set({ vehicles }),

      updateTask: (taskId, updates) =>
        set(
          produce(draft => {
            const taskIndex = draft.tasks.findIndex((t: any) => t.id === taskId);
            if (taskIndex !== -1) {
              Object.assign(draft.tasks[taskIndex], updates);
            }
            // 同时更新过滤后的任务列表
            const filteredIndex = draft.filteredTasks.findIndex((t: any) => t.id === taskId);
            if (filteredIndex !== -1) {
              Object.assign(draft.filteredTasks[filteredIndex], updates);
            }
          })
        ),

      updateVehicle: (vehicleId, updates) =>
        set(
          produce(draft => {
            const vehicleIndex = draft.vehicles.findIndex((v: any) => v.id === vehicleId);
            if (vehicleIndex !== -1) {
              Object.assign(draft.vehicles[vehicleIndex], updates);
            }
          })
        ),

      setFilteredTasks: tasks => set({ filteredTasks: tasks }),

      // 选择操作
      selectTask: taskId => set({ selectedTaskIds: [taskId] }),
      selectMultipleTasks: taskIds => set({ selectedTaskIds: taskIds }),
      clearTaskSelection: () => set({ selectedTaskIds: [] }),

      toggleTaskSelection: taskId =>
        set(
          produce(draft => {
            const index = draft.selectedTaskIds.indexOf(taskId);
            if (index > -1) {
              draft.selectedTaskIds.splice(index, 1);
            } else {
              draft.selectedTaskIds.push(taskId);
            }
          })
        ),

      setSelectedTask: task => set({ selectedTask: task }),
      setSelectedVehicle: vehicle => set({ selectedVehicle: vehicle }),

      // UI状态操作
      setDisplayMode: mode => set({ displayMode: mode }),
      setDensity: density => set({ density }),
      setGroupConfig: config => set({ groupConfig: config }),
      setLoading: loading => set({ isLoading: loading }),
      setError: error => set({ error }),

      // 头部模式操作
      setHeaderMode: mode => set({ headerMode: mode }),
      setFloatingHeaderPosition: position => set({ floatingHeaderPosition: position }),

      // 拖拽操作
      startDrag: (item, type) =>
        set({
          dragState: {
            isDragging: true,
            draggedItem: item,
            dropTarget: null,
            dragType: type,
          },
        }),

      updateDragTarget: target =>
        set(
          produce(draft => {
            draft.dragState.dropTarget = target;
          })
        ),

      endDrag: () =>
        set({
          dragState: {
            isDragging: false,
            draggedItem: null,
            dropTarget: null,
            dragType: null,
          },
        }),

      // 模态框操作
      openModal: modalName =>
        set(
          produce(draft => {
            draft.modals[modalName] = true;
          })
        ),

      closeModal: modalName =>
        set(
          produce(draft => {
            draft.modals[modalName] = false;
          })
        ),

      closeAllModals: () =>
        set({
          modals: {
            settings: false,
            columnVisibility: false,
            groupConfig: false,
            taskProgress: false,
            vehicleDispatch: false,
            qrCode: false,
            abbreviation: false,
            tankerNote: false,
            reminderConfig: false,
            deliveryOrderDetails: false,
          },
        }),

      // 操作状态
      setOperationStatus: (operation, status) =>
        set(
          produce(draft => {
            draft.operationStatus[operation] = status;
          })
        ),

      // 重置状态
      reset: () => set(initialState),
    })),
    {
      name: 'task-list-store',
    }
  )
);
