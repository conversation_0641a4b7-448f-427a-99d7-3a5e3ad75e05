/**
 * 通用模态框管理Store
 * 提供全局的模态框状态管理
 */

import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { produce } from 'immer';

export interface ModalData {
  [key: string]: any;
}

export interface ModalState {
  id: string;
  isOpen: boolean;
  data?: ModalData;
  options?: {
    closable?: boolean;
    maskClosable?: boolean;
    keyboard?: boolean;
    centered?: boolean;
    width?: number | string;
    height?: number | string;
    zIndex?: number;
  };
}

export interface GlobalModalState {
  // 当前打开的模态框
  openModals: Record<string, ModalState>;

  // 模态框栈（用于管理层级）
  modalStack: string[];

  // 全局设置
  globalSettings: {
    defaultClosable: boolean;
    defaultMaskClosable: boolean;
    defaultKeyboard: boolean;
    defaultCentered: boolean;
    baseZIndex: number;
  };

  // 操作状态
  isTransitioning: boolean;

  // 历史记录
  history: Array<{
    modalId: string;
    action: 'open' | 'close';
    timestamp: number;
    data?: ModalData;
  }>;
}

export interface GlobalModalActions {
  // 基本操作
  openModal: (modalId: string, data?: ModalData, options?: ModalState['options']) => void;
  closeModal: (modalId: string) => void;
  toggleModal: (modalId: string, data?: ModalData, options?: ModalState['options']) => void;

  // 批量操作
  closeAllModals: () => void;
  closeModalsExcept: (modalIds: string[]) => void;

  // 状态查询
  isModalOpen: (modalId: string) => boolean;
  getModalData: (modalId: string) => ModalData | undefined;
  getTopModal: () => string | null;
  hasOpenModals: () => boolean;

  // 栈操作
  bringToFront: (modalId: string) => void;
  sendToBack: (modalId: string) => void;

  // 设置操作
  updateGlobalSettings: (settings: Partial<GlobalModalState['globalSettings']>) => void;
  updateModalOptions: (modalId: string, options: Partial<ModalState['options']>) => void;

  // 历史操作
  clearHistory: () => void;
  getHistory: () => GlobalModalState['history'];

  // 重置
  reset: () => void;
}

const initialState: GlobalModalState = {
  openModals: {},
  modalStack: [],
  globalSettings: {
    defaultClosable: true,
    defaultMaskClosable: true,
    defaultKeyboard: true,
    defaultCentered: true,
    baseZIndex: 1000,
  },
  isTransitioning: false,
  history: [],
};

export const useModalStore = create<GlobalModalState & GlobalModalActions>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      ...initialState,

      // 基本操作
      openModal: (modalId, data, options) => {
        const { globalSettings } = get();

        set(
          produce(draft => {
            // 创建模态框状态
            draft.openModals[modalId] = {
              id: modalId,
              isOpen: true,
              data,
              options: {
                closable: globalSettings.defaultClosable,
                maskClosable: globalSettings.defaultMaskClosable,
                keyboard: globalSettings.defaultKeyboard,
                centered: globalSettings.defaultCentered,
                zIndex: globalSettings.baseZIndex + draft.modalStack.length,
                ...options,
              },
            };

            // 添加到栈顶
            const existingIndex = draft.modalStack.indexOf(modalId);
            if (existingIndex > -1) {
              draft.modalStack.splice(existingIndex, 1);
            }
            draft.modalStack.push(modalId);

            // 记录历史
            draft.history.push({
              modalId,
              action: 'open',
              timestamp: Date.now(),
              data,
            });

            // 限制历史记录数量
            if (draft.history.length > 100) {
              draft.history.splice(0, draft.history.length - 100);
            }
          })
        );
      },

      closeModal: modalId => {
        set(
          produce(draft => {
            if (draft.openModals[modalId]) {
              // 记录历史
              draft.history.push({
                modalId,
                action: 'close',
                timestamp: Date.now(),
                data: draft.openModals[modalId].data,
              });

              // 从状态中移除
              delete draft.openModals[modalId];

              // 从栈中移除
              const stackIndex = draft.modalStack.indexOf(modalId);
              if (stackIndex > -1) {
                draft.modalStack.splice(stackIndex, 1);
              }

              // 更新其他模态框的zIndex
              draft.modalStack.forEach((id: string, index: number) => {
                if (draft.openModals[id]) {
                  draft.openModals[id].options!.zIndex = draft.globalSettings.baseZIndex + index;
                }
              });
            }
          })
        );
      },

      toggleModal: (modalId, data, options) => {
        const { isModalOpen, openModal, closeModal } = get();
        if (isModalOpen(modalId)) {
          closeModal(modalId);
        } else {
          openModal(modalId, data, options);
        }
      },

      // 批量操作
      closeAllModals: () => {
        set(
          produce(draft => {
            // 记录所有关闭操作
            Object.keys(draft.openModals).forEach(modalId => {
              draft.history.push({
                modalId,
                action: 'close',
                timestamp: Date.now(),
                data: draft.openModals[modalId].data,
              });
            });

            draft.openModals = {};
            draft.modalStack = [];
          })
        );
      },

      closeModalsExcept: modalIds => {
        set(
          produce(draft => {
            Object.keys(draft.openModals).forEach(modalId => {
              if (!modalIds.includes(modalId)) {
                draft.history.push({
                  modalId,
                  action: 'close',
                  timestamp: Date.now(),
                  data: draft.openModals[modalId].data,
                });
                delete draft.openModals[modalId];
              }
            });

            draft.modalStack = draft.modalStack.filter((id: string) => modalIds.includes(id));
          })
        );
      },

      // 状态查询
      isModalOpen: modalId => {
        return !!get().openModals[modalId]?.isOpen;
      },

      getModalData: modalId => {
        return get().openModals[modalId]?.data;
      },

      getTopModal: (): string | null => {
        const { modalStack } = get();
        return modalStack.length > 0 ? modalStack[modalStack.length - 1] || null : null;
      },

      hasOpenModals: () => {
        return Object.keys(get().openModals).length > 0;
      },

      // 栈操作
      bringToFront: modalId => {
        set(
          produce(draft => {
            const index = draft.modalStack.indexOf(modalId);
            if (index > -1) {
              draft.modalStack.splice(index, 1);
              draft.modalStack.push(modalId);

              // 更新zIndex
              draft.modalStack.forEach((id: string, idx: number) => {
                if (draft.openModals[id]) {
                  draft.openModals[id].options!.zIndex = draft.globalSettings.baseZIndex + idx;
                }
              });
            }
          })
        );
      },

      sendToBack: modalId => {
        set(
          produce(draft => {
            const index = draft.modalStack.indexOf(modalId);
            if (index > -1) {
              draft.modalStack.splice(index, 1);
              draft.modalStack.unshift(modalId);

              // 更新zIndex
              draft.modalStack.forEach((id: string, idx: number) => {
                if (draft.openModals[id]) {
                  draft.openModals[id].options!.zIndex = draft.globalSettings.baseZIndex + idx;
                }
              });
            }
          })
        );
      },

      // 设置操作
      updateGlobalSettings: settings => {
        set(
          produce(draft => {
            Object.assign(draft.globalSettings, settings);
          })
        );
      },

      updateModalOptions: (modalId, options) => {
        set(
          produce(draft => {
            if (draft.openModals[modalId]) {
              Object.assign(draft.openModals[modalId].options!, options);
            }
          })
        );
      },

      // 历史操作
      clearHistory: () => {
        set({ history: [] });
      },

      getHistory: () => {
        return get().history;
      },

      // 重置
      reset: () => set(initialState),
    })),
    {
      name: 'modal-store',
    }
  )
);
