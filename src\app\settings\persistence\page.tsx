'use client';

import { useState, useEffect, Suspense, lazy } from 'react';
import { Button } from '@/shared/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/card';
import { useToast } from '@/shared/hooks/use-toast';
import { Download, Upload, RotateCcw, Shield, Clock, FileText, Settings } from 'lucide-react';
import { SkeletonLoader } from '@/shared/components/enhanced-loading';
import { ConfigBackupManager } from '@/core/utils/configBackup';

// 懒加载重型组件
const PersistenceManagementPanel = lazy(() =>
  import('@/shared/components/settings/PersistenceManagementPanel').then(module => ({
    default: module.PersistenceManagementPanel,
  }))
);

export default function PersistenceSettingsPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // 客户端初始化
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 导出完整配置
  const handleExportFull = async () => {
    try {
      setIsLoading(true);
      const backup = ConfigBackupManager.createFullBackup('手动完整备份');
      ConfigBackupManager.exportToFile(backup);

      toast({
        title: '导出成功',
        description: '完整配置已导出到文件',
      });
    } catch (error) {
      toast({
        title: '导出失败',
        description: '配置导出过程中发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 导入配置
  const handleImportConfig = async () => {
    try {
      setIsLoading(true);
      const backup = await ConfigBackupManager.importFromFile();
      const success = ConfigBackupManager.restoreBackup(backup);

      if (success) {
        toast({
          title: '导入成功',
          description: '配置已成功导入，请刷新页面以应用更改',
        });
      } else {
        // throw new Error('导入失败');
      }
    } catch (error) {
      toast({
        title: '导入失败',
        description: error instanceof Error ? error.message : '配置导入过程中发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 恢复默认设置
  const handleResetToDefaults = () => {
    if (!confirm('确定要恢复所有默认设置吗？此操作将清除所有自定义配置！')) {
      return;
    }

    try {
      setIsLoading(true);

      // 创建备份
      const backup = ConfigBackupManager.createFullBackup('重置前自动备份');
      const backupKey = `reset_backup_${Date.now()}`;
      localStorage.setItem(backupKey, JSON.stringify(backup));

      // 清除所有配置
      const {
        PersistenceManager,
      } = require('@/infrastructure/storage/persistence/persistenceManager');
      PersistenceManager.clearAll();

      toast({
        title: '重置成功',
        description: `所有配置已重置为默认值，备份已保存为 ${backupKey}`,
      });

      // 建议刷新页面
      setTimeout(() => {
        if (confirm('配置已重置，是否立即刷新页面以应用更改？')) {
          window.location.reload();
        }
      }, 1000);
    } catch (error) {
      toast({
        title: '重置失败',
        description: '重置配置过程中发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 设置自动备份
  const handleSetupAutoBackup = () => {
    try {
      const cleanup = ConfigBackupManager.setupAutoBackup(24); // 每24小时备份一次

      // 保存清理函数到全局，以便后续清理
      (window as any).__autoBackupCleanup = cleanup;

      toast({
        title: '自动备份已启用',
        description: '系统将每24小时自动创建配置备份',
      });
    } catch (error) {
      toast({
        title: '自动备份设置失败',
        description: '启用自动备份过程中发生错误',
        variant: 'destructive',
      });
    }
  };

  // 清理旧备份
  const handleCleanupOldBackups = () => {
    try {
      const cleanedCount = ConfigBackupManager.cleanupOldBackups(30); // 清理30天前的备份

      toast({
        title: '清理完成',
        description: `已清理 ${cleanedCount} 个旧备份文件`,
      });
    } catch (error) {
      toast({
        title: '清理失败',
        description: '清理旧备份过程中发生错误',
        variant: 'destructive',
      });
    }
  };

  // 如果还没有加载客户端，显示加载状态
  if (!isClient) {
    return (
      <div className='container mx-auto p-6 space-y-6'>
        <div className='mb-6'>
          <h1 className='text-3xl font-bold'>配置持久化管理</h1>
          <p className='text-muted-foreground mt-2'>正在加载客户端...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto p-6 space-y-6'>
      <div className='mb-6'>
        <h1 className='text-3xl font-bold'>配置持久化管理</h1>
        <p className='text-muted-foreground mt-2'>
          管理应用的所有配置，包括样式、主题、布局等设置的备份和恢复
        </p>
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Settings className='w-5 h-5' />
            快速操作
          </CardTitle>
          <CardDescription>常用的配置管理操作</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
            <Button
              onClick={handleExportFull}
              disabled={isLoading}
              className='h-auto p-4 flex flex-col items-center gap-2'
            >
              <Download className='w-6 h-6' />
              <div className='text-center'>
                <div className='font-medium'>导出配置</div>
                <div className='text-xs opacity-70'>备份所有设置</div>
              </div>
            </Button>

            <Button
              onClick={handleImportConfig}
              disabled={isLoading}
              variant='outline'
              className='h-auto p-4 flex flex-col items-center gap-2'
            >
              <Upload className='w-6 h-6' />
              <div className='text-center'>
                <div className='font-medium'>导入配置</div>
                <div className='text-xs opacity-70'>恢复备份设置</div>
              </div>
            </Button>

            <Button
              onClick={handleResetToDefaults}
              disabled={isLoading}
              variant='outline'
              className='h-auto p-4 flex flex-col items-center gap-2'
            >
              <RotateCcw className='w-6 h-6' />
              <div className='text-center'>
                <div className='font-medium'>重置默认</div>
                <div className='text-xs opacity-70'>恢复出厂设置</div>
              </div>
            </Button>

            <Button
              onClick={handleSetupAutoBackup}
              disabled={isLoading}
              variant='outline'
              className='h-auto p-4 flex flex-col items-center gap-2'
            >
              <Clock className='w-6 h-6' />
              <div className='text-center'>
                <div className='font-medium'>自动备份</div>
                <div className='text-xs opacity-70'>定期备份配置</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 高级操作 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Shield className='w-5 h-5' />
            高级操作
          </CardTitle>
          <CardDescription>高级配置管理功能</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex flex-wrap gap-2'>
            <Button onClick={handleCleanupOldBackups} variant='outline' size='sm'>
              <FileText className='w-4 h-4 mr-2' />
              清理旧备份
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 详细管理面板 */}
      <Suspense fallback={<SkeletonLoader type='card' />}>
        <PersistenceManagementPanel />
      </Suspense>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div>
            <h4 className='font-medium mb-2'>配置类型说明：</h4>
            <ul className='text-sm space-y-1 text-muted-foreground'>
              <li>
                • <strong>任务列表设置</strong>：包括显示模式、密度、列配置等
              </li>
              <li>
                • <strong>任务卡片配置</strong>：卡片样式、布局、字段显示等
              </li>
              <li>
                • <strong>主题设置</strong>：应用主题、颜色方案等
              </li>
              <li>
                • <strong>表格配置</strong>：列宽、列顺序、列样式等
              </li>
              <li>
                • <strong>UI偏好</strong>：界面位置、折叠状态等
              </li>
            </ul>
          </div>

          <div>
            <h4 className='font-medium mb-2'>备份建议：</h4>
            <ul className='text-sm space-y-1 text-muted-foreground'>
              <li>• 在进行重大配置更改前，建议先导出当前配置</li>
              <li>• 定期使用自动备份功能，避免配置丢失</li>
              <li>• 导入配置后需要刷新页面以应用更改</li>
              <li>• 重置操作会自动创建备份，可在需要时恢复</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
