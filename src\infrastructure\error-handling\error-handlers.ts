/**
 * 全局错误处理器
 * 处理各种类型的错误，包括音频、网络、资源加载等
 */

export interface AppError extends Error {
  code?: string;
  type?: 'network' | 'audio' | 'resource' | 'permission' | 'unknown';
  context?: Record<string, any>;
}

// 创建应用错误
export function createAppError(
  message: string,
  type: AppError['type'] = 'unknown',
  code?: string,
  context?: Record<string, any>
): AppError {
  const error = new Error(message) as AppError;
  error.type = type;
  error.code = code;
  error.context = context;
  return error;
}

// 音频错误处理器
export function handleAudioError(error: any, context?: Record<string, any>): void {
  const audioError = createAppError('音频播放失败', 'audio', error?.code || 'AUDIO_PLAY_FAILED', {
    originalError: error,
    ...context,
  });

  // 记录错误但不抛出，避免影响应用运行
  console.warn('[Audio Error]', audioError.message, audioError.context);

  // 可以在这里添加错误上报逻辑
  // reportError(audioError);
}

// 网络错误处理器
export function handleNetworkError(error: any, context?: Record<string, any>): AppError {
  const networkError = createAppError('网络请求失败', 'network', error?.code || 'NETWORK_ERROR', {
    originalError: error,
    ...context,
  });

  console.error('[Network Error]', networkError.message, networkError.context);
  return networkError;
}

// 资源加载错误处理器
export function handleResourceError(error: any, resourceUrl?: string): AppError {
  const resourceError = createAppError(
    `资源加载失败${resourceUrl ? `: ${resourceUrl}` : ''}`,
    'resource',
    error?.code || 'RESOURCE_LOAD_FAILED',
    { originalError: error, resourceUrl }
  );

  console.error('[Resource Error]', resourceError.message, resourceError.context);
  return resourceError;
}

// 权限错误处理器
export function handlePermissionError(error: any, permission?: string): AppError {
  const permissionError = createAppError(
    `权限被拒绝${permission ? `: ${permission}` : ''}`,
    'permission',
    error?.code || 'PERMISSION_DENIED',
    { originalError: error, permission }
  );

  console.warn('[Permission Error]', permissionError.message, permissionError.context);
  return permissionError;
}

// 通用错误处理器
export function handleGenericError(error: any, context?: Record<string, any>): AppError {
  const genericError = createAppError(
    error?.message || '未知错误',
    'unknown',
    error?.code || 'UNKNOWN_ERROR',
    { originalError: error, ...context }
  );

  console.error('[Generic Error]', genericError.message, genericError.context);
  return genericError;
}

// 错误分类器
export function classifyError(error: any): AppError['type'] {
  if (!error) return 'unknown';

  const message = error.message?.toLowerCase() || '';
  const name = error.name?.toLowerCase() || '';

  if (message.includes('audio') || message.includes('sound') || name.includes('audio')) {
    return 'audio';
  }

  if (message.includes('network') || message.includes('fetch') || name.includes('network')) {
    return 'network';
  }

  if (message.includes('permission') || name.includes('permission')) {
    return 'permission';
  }

  if (message.includes('load') || message.includes('resource') || name.includes('load')) {
    return 'resource';
  }

  return 'unknown';
}

// 安全的错误处理包装器
export function safeExecute<T>(
  fn: () => T,
  fallback?: T,
  errorHandler?: (error: any) => void
): T | undefined {
  try {
    return fn();
  } catch (error) {
    const errorType = classifyError(error);

    if (errorHandler) {
      errorHandler(error);
    } else {
      // 根据错误类型选择合适的处理器
      switch (errorType) {
        case 'audio':
          handleAudioError(error);
          break;
        case 'network':
          handleNetworkError(error);
          break;
        case 'resource':
          handleResourceError(error);
          break;
        case 'permission':
          handlePermissionError(error);
          break;
        default:
          handleGenericError(error);
      }
    }

    return fallback;
  }
}

// 异步安全执行
export async function safeExecuteAsync<T>(
  fn: () => Promise<T>,
  fallback?: T,
  errorHandler?: (error: any) => void
): Promise<T | undefined> {
  try {
    return await fn();
  } catch (error) {
    const errorType = classifyError(error);

    if (errorHandler) {
      errorHandler(error);
    } else {
      // 根据错误类型选择合适的处理器
      switch (errorType) {
        case 'audio':
          handleAudioError(error);
          break;
        case 'network':
          handleNetworkError(error);
          break;
        case 'resource':
          handleResourceError(error);
          break;
        case 'permission':
          handlePermissionError(error);
          break;
        default:
          handleGenericError(error);
      }
    }

    return fallback;
  }
}

// 全局错误监听器
export function setupGlobalErrorHandlers(): void {
  // 处理未捕获的 Promise 拒绝
  window.addEventListener('unhandledrejection', event => {
    const error = event.reason;
    const errorType = classifyError(error);

    // 对于音频错误，阻止默认行为（不在控制台显示错误）
    if (errorType === 'audio') {
      event.preventDefault();
      handleAudioError(error, { source: 'unhandledrejection' });
    } else {
      handleGenericError(error, { source: 'unhandledrejection' });
    }
  });

  // 处理全局错误
  window.addEventListener('error', event => {
    const error = event.error;
    const errorType = classifyError(error);

    // 对于资源加载错误，特殊处理
    if (event.target !== window) {
      const target = event.target as HTMLElement;
      if (target.tagName === 'AUDIO' || target.tagName === 'SOURCE') {
        event.preventDefault();
        handleAudioError(error, {
          source: 'resource_load_error',
          element: target.tagName,
          src: (target as any).src,
        });
        return;
      }
    }

    handleGenericError(error, { source: 'global_error' });
  });
}

// 在应用启动时调用
if (typeof window !== 'undefined') {
  setupGlobalErrorHandlers();
}
