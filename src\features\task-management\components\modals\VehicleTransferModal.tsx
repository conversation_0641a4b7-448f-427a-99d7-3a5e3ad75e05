'use client';

import React, { useState, useCallback } from 'react';
import { X, Truck, FileText, ArrowRight } from 'lucide-react';

import { Button } from '@/shared/components/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import { RadioGroup, RadioGroupItem } from '@/shared/components/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { Textarea } from '@/shared/components/textarea';
import { useToast } from '@/shared/hooks/use-toast';
import type { Vehicle, Task } from '@/core/types';

interface VehicleTransferModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  vehicle: Vehicle | null;
  sourceTask: Task | null;
  targetTask: Task | null;
  targetLineId: string | null;
  onConfirm: (transferData: VehicleTransferData) => void;
}

export interface VehicleTransferData {
  vehicle: Vehicle;
  sourceTask: Task;
  targetTask: Task;
  targetLineId: string;
  transferType: 'partial' | 'full';
  newTicketNumber: string;
  newQuantity: number;
  oldQuantity: number;
  transferReason: string;
  notes: string;
  mixingStation: string;
  departureTime: string;
  qualityOfficer: string;
  dispatcher: string;
}

/**
 * 车辆转发模态框组件
 * 当车辆从一个任务拖拽到另一个任务的生产线时显示
 */
export const VehicleTransferModal: React.FC<VehicleTransferModalProps> = ({
  open,
  onOpenChange,
  vehicle,
  sourceTask,
  targetTask,
  targetLineId,
  onConfirm,
}) => {
  const { toast } = useToast();

  // 表单状态
  const [transferType, setTransferType] = useState<'partial' | 'full'>('full');
  const [newTicketNumber, setNewTicketNumber] = useState('');
  const [newQuantity, setNewQuantity] = useState<number>(12);
  const [oldQuantity, setOldQuantity] = useState<number>(12);
  const [transferReason, setTransferReason] = useState('工地加急');
  const [notes, setNotes] = useState('转发');
  const [mixingStation, setMixingStation] = useState('搅拌站1');
  const [departureTime, setDepartureTime] = useState('');
  const [qualityOfficer, setQualityOfficer] = useState('任建');
  const [dispatcher, setDispatcher] = useState('刘三狗');

  // 初始化表单数据
  React.useEffect(() => {
    if (open && vehicle && sourceTask && targetTask) {
      // 生成新的发货单编号
      const now = new Date();
      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
      const timeStr = now.toTimeString().slice(0, 5).replace(':', '');
      setNewTicketNumber(
        `C125-${dateStr.slice(2)}-${Math.floor(Math.random() * 1000)
          .toString()
          .padStart(3, '0')}`
      );

      // 设置出车时间
      setDepartureTime(`${dateStr} ${timeStr}:${now.getSeconds().toString().padStart(2, '0')}`);
    }
  }, [open, vehicle, sourceTask, targetTask]);

  // 处理确认转发
  const handleConfirm = useCallback(() => {
    if (!vehicle || !sourceTask || !targetTask || !targetLineId) {
      toast({
        title: '错误',
        description: '缺少必要的转发信息',
        variant: 'destructive',
      });
      return;
    }

    const transferData: VehicleTransferData = {
      vehicle,
      sourceTask,
      targetTask,
      targetLineId,
      transferType,
      newTicketNumber,
      newQuantity,
      oldQuantity,
      transferReason,
      notes,
      mixingStation,
      departureTime,
      qualityOfficer,
      dispatcher,
    };

    onConfirm(transferData);
    onOpenChange(false);

    toast({
      title: '转发成功',
      description: `车辆 ${vehicle.vehicleNumber} 已成功转发到 ${targetTask.taskNumber}`,
    });
  }, [
    vehicle,
    sourceTask,
    targetTask,
    targetLineId,
    transferType,
    newTicketNumber,
    newQuantity,
    oldQuantity,
    transferReason,
    notes,
    mixingStation,
    departureTime,
    qualityOfficer,
    dispatcher,
    onConfirm,
    onOpenChange,
    toast,
  ]);

  // 处理取消
  const handleCancel = useCallback(() => {
    onOpenChange(false);
  }, [onOpenChange]);

  if (!vehicle || !sourceTask || !targetTask) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto p-0'>
        {/* 标题栏 */}
        <div className='bg-blue-500 text-white p-3 rounded-t-lg'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-white rounded-full'></div>
              <Truck className='w-4 h-4' />
              <span className='font-semibold'>车辆转发</span>
            </div>
            <Button
              variant='ghost'
              size='sm'
              onClick={handleCancel}
              className='text-white hover:bg-blue-600 h-6 w-6 p-0'
            >
              <X className='w-4 h-4' />
            </Button>
          </div>
        </div>

        <div className='p-4 space-y-4'>
          {/* 转发信息提示 */}
          <div className='bg-blue-50 border border-blue-200 rounded-lg p-3'>
            <div className='flex items-center gap-2 text-blue-800'>
              <ArrowRight className='w-4 h-4' />
              <span className='font-medium'>此车未过磅，不存在实物票据。</span>
            </div>
          </div>

          <div className='grid grid-cols-3 gap-6'>
            {/* 原发货单信息 */}
            <div className='space-y-4'>
              <div className='flex items-center gap-2 text-sm font-medium border-b pb-2'>
                <FileText className='w-4 h-4' />
                原发货单信息
              </div>

              <div className='space-y-3 text-sm'>
                <div className='grid grid-cols-2 gap-2'>
                  <Label className='text-gray-600'>发货单编号:</Label>
                  <span>{sourceTask.deliveryOrderNumber}</span>
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <Label className='text-gray-600'>工程名称:</Label>
                  <span>{sourceTask.projectName}</span>
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <Label className='text-gray-600'>建设单位:</Label>
                  <span>{sourceTask.constructionUnit}</span>
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <Label className='text-gray-600'>施工单位:</Label>
                  <span>{sourceTask.constructionSite}</span>
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <Label className='text-gray-600'>施工部位:</Label>
                  <span>{sourceTask.constructionSite}</span>
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <Label className='text-gray-600'>出机坍落度:</Label>
                  <span>{sourceTask.slumpRange || '180±20'}</span>
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <Label className='text-gray-600'>在强度等级:</Label>
                  <span>{sourceTask.strength}</span>
                </div>

                <div className='space-y-2'>
                  <div className='grid grid-cols-3 gap-2 text-xs'>
                    <span>毛重 {sourceTask.grossWeight || ''}</span>
                    <span>皮重 {sourceTask.tareWeight || '15.28'}</span>
                    <span>净重 {sourceTask.netWeight || ''}</span>
                  </div>
                  <div className='grid grid-cols-3 gap-2 text-xs'>
                    <span>
                      方量 {sourceTask.requiredVolume || sourceTask.concreteVolume || '12'}
                    </span>
                    <span></span>
                    <span></span>
                  </div>
                </div>

                <div className='grid grid-cols-2 gap-2 text-xs'>
                  <span>搅拌站 {sourceTask.mixingStation || '搅拌站1'}</span>
                  <span>出车时间 {sourceTask.departureTime || new Date().toLocaleString()}</span>
                </div>

                <div className='space-y-1 text-xs'>
                  <div className='grid grid-cols-3 gap-2'>
                    <span>货物类型 砼</span>
                    <span>主机操作 {sourceTask.mainOperator || '主机操作'}</span>
                    <span>现场调度 {sourceTask.siteDispatcher || '现场调度'}</span>
                  </div>
                  <div className='grid grid-cols-3 gap-2'>
                    <span>车号 {vehicle.vehicleNumber}</span>
                    <span>司机 {sourceTask.driverName || '刘三狗'}</span>
                    <span>泵车号 {sourceTask.pumpTruck || ''}</span>
                  </div>
                  <div className='grid grid-cols-3 gap-2'>
                    <span>质检员 {sourceTask.qualityInspector || '质检员'}</span>
                    <span>司磅员 {sourceTask.weigher || '司磅员'}</span>
                    <span>调度员 {sourceTask.dispatcher || '徐明哲'}</span>
                  </div>
                </div>

                <div className='mt-2'>
                  <Label className='text-xs text-gray-600'>注释</Label>
                  <div className='border rounded p-1 text-xs min-h-[40px]'>
                    {sourceTask.notes || sourceTask.remarks || ''}
                  </div>
                </div>
              </div>
            </div>

            {/* 转发方式选择 */}
            <div className='space-y-4'>
              <div className='text-sm font-medium border-b pb-2'>请选择转发方式</div>

              <RadioGroup
                value={transferType}
                onValueChange={(value: 'partial' | 'full') => setTransferType(value)}
              >
                <div className='space-y-3'>
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='partial' id='partial' />
                    <Label htmlFor='partial' className='text-sm'>
                      部分转发
                    </Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='full' id='full' />
                    <Label htmlFor='full' className='text-sm'>
                      整车转发
                    </Label>
                  </div>
                </div>
              </RadioGroup>

              <div className='space-y-3'>
                <div>
                  <Label className='text-sm'>原票处理</Label>
                  <Select defaultValue='累计车数'>
                    <SelectTrigger className='h-8'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='累计车数'>累计车数</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <div>
                    <Label className='text-sm'>新票签收</Label>
                    <Input
                      value={newQuantity}
                      onChange={e => setNewQuantity(Number(e.target.value))}
                      className='h-8'
                      type='number'
                    />
                    <span className='text-xs text-gray-500'>万</span>
                  </div>
                  <div>
                    <Label className='text-sm'>旧票签收</Label>
                    <Input
                      value={oldQuantity}
                      onChange={e => setOldQuantity(Number(e.target.value))}
                      className='h-8'
                      type='number'
                    />
                    <span className='text-xs text-gray-500'>万</span>
                  </div>
                </div>
              </div>

              <div>
                <Label className='text-sm'>转发原因</Label>
                <Select value={transferReason} onValueChange={setTransferReason}>
                  <SelectTrigger className='h-8'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='工地加急'>工地加急</SelectItem>
                    <SelectItem value='调度安排'>调度安排</SelectItem>
                    <SelectItem value='其他原因'>其他原因</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 新发货单信息 */}
            <div className='space-y-4'>
              <div className='text-sm font-medium border-b pb-2'>新发货单信息</div>

              <div className='space-y-3 text-sm'>
                <div>
                  <Label className='text-sm'>任务编号:</Label>
                  <Select defaultValue={targetTask.id}>
                    <SelectTrigger className='h-8'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={targetTask.id}>{targetTask.taskNumber}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <div>
                    <Label className='text-sm'>出车时间:</Label>
                    <Input
                      value={departureTime}
                      onChange={e => setDepartureTime(e.target.value)}
                      className='h-8 text-xs'
                    />
                  </div>
                  <div>
                    <Label className='text-sm'>搅拌站:</Label>
                    <Select value={mixingStation} onValueChange={setMixingStation}>
                      <SelectTrigger className='h-8'>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='搅拌站1'>搅拌站1</SelectItem>
                        <SelectItem value='搅拌站2'>搅拌站2</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <div>
                    <Label className='text-sm'>泵车号:</Label>
                    <Input className='h-8' defaultValue='70泵泵' />
                  </div>
                  <div>
                    <Label className='text-sm'>现场调度:</Label>
                    <Select defaultValue='闫抱'>
                      <SelectTrigger className='h-8'>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='闫抱'>闫抱</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <div>
                    <Label className='text-sm'>浇注方式:</Label>
                    <Select defaultValue='自卸'>
                      <SelectTrigger className='h-8'>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='自卸'>自卸</SelectItem>
                        <SelectItem value='泵送'>泵送</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className='text-sm'>质检员:</Label>
                    <Select value={qualityOfficer} onValueChange={setQualityOfficer}>
                      <SelectTrigger className='h-8'>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='任建'>任建</SelectItem>
                        <SelectItem value='李燕燕'>李燕燕</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <div>
                    <Label className='text-sm'>罐车司机:</Label>
                    <Select value={dispatcher} onValueChange={setDispatcher}>
                      <SelectTrigger className='h-8'>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='刘三狗'>刘三狗</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className='text-sm'>司磅员:</Label>
                    <Select defaultValue='李燕燕'>
                      <SelectTrigger className='h-8'>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='李燕燕'>李燕燕</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label className='text-sm'>注释:</Label>
                  <Textarea
                    value={notes}
                    onChange={e => setNotes(e.target.value)}
                    className='h-16 text-xs'
                    placeholder='转发'
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 底部按钮 */}
          <div className='flex justify-center gap-4 pt-4 border-t'>
            <Button onClick={handleCancel} variant='outline' className='px-8'>
              取消
            </Button>
            <Button onClick={handleConfirm} className='px-8 bg-blue-500 hover:bg-blue-600'>
              保存
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
