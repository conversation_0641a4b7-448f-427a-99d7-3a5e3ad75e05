'use client';

import { Button } from '@/shared/components/button';
import { Input } from '@/shared/components/input';
import { cn } from '@/core/lib/utils';
import { useDateTimePicker } from '@/core/adapters/hooks/useUnifiedDate';
import type { UnifiedDate } from '@/core/adapters/DateAdapter';
import { Calendar, ChevronLeft, ChevronRight, Clock } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

interface CustomDateTimePickerProps {
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  showTime?: boolean;
  format?: string;
}

export const CustomDateTimePicker: React.FC<CustomDateTimePickerProps> = ({
  value,
  onChange,
  disabled = false,
  placeholder = '选择日期时间',
  className,
  showTime = true,
  format = 'YYYY-MM-DD HH:mm',
}) => {
  // 使用日期适配器
  const dateAdapter = useDateTimePicker();

  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<UnifiedDate | null>(() => {
    if (!value) return null;
    try {
      const parsed = dateAdapter.create(value);
      return parsed;
    } catch {
      return null;
    }
  });
  const [currentMonth, setCurrentMonth] = useState(() => {
    if (value) {
      try {
        const parsed = dateAdapter.create(value);
        return parsed;
      } catch {
        return dateAdapter.now();
      }
    }
    return dateAdapter.now();
  });
  const [timeValue, setTimeValue] = useState(() => {
    if (value) {
      try {
        const parsed = dateAdapter.create(value);
        return {
          hour: dateAdapter.getHour(parsed.value),
          minute: dateAdapter.getMinute(parsed.value),
        };
      } catch {
        return { hour: 0, minute: 0 };
      }
    }
    return { hour: 0, minute: 0 };
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 处理外部 value 变化
  useEffect(() => {
    if (value) {
      try {
        const parsed = dateAdapter.create(value);
        setSelectedDate(parsed);
        setCurrentMonth(parsed);
        setTimeValue({
          hour: dateAdapter.getHour(parsed.value),
          minute: dateAdapter.getMinute(parsed.value),
        });
      } catch {
        setSelectedDate(null);
      }
    } else {
      setSelectedDate(null);
    }
  }, [value, dateAdapter]);

  // 关闭弹窗
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // 生成日历天数
  const generateCalendarDays = () => {
    const startOfMonth = dateAdapter.startOf(currentMonth.value, 'month');
    const endOfMonth = dateAdapter.endOf(currentMonth.value, 'month');
    const startOfWeek = dateAdapter.startOf(startOfMonth.value, 'day'); // 简化为天开始
    const endOfWeek = dateAdapter.endOf(endOfMonth.value, 'day'); // 简化为天结束

    const days = [];
    let current = startOfWeek;

    // 生成一个月的天数（简化版本）
    const currentMonthStart = dateAdapter.startOf(currentMonth.value, 'month');
    const currentMonthEnd = dateAdapter.endOf(currentMonth.value, 'month');

    for (let i = 1; i <= 31; i++) {
      const day = dateAdapter.setDate(currentMonthStart.value, i);
      if (dateAdapter.getMonth(day.value) === dateAdapter.getMonth(currentMonth.value)) {
        days.push(day);
      }
    }

    return days;
  };

  const handleDateSelect = (date: UnifiedDate) => {
    // 如果是第一次选择日期且显示时间，使用当前时间作为默认值
    let finalHour = timeValue.hour;
    let finalMinute = timeValue.minute;

    if (!selectedDate && showTime) {
      const now = dateAdapter.now();
      finalHour = dateAdapter.getHour(now.value);
      finalMinute = dateAdapter.getMinute(now.value);
      setTimeValue({ hour: finalHour, minute: finalMinute });
    }

    let newDate = dateAdapter.setHour(date.value, finalHour);
    newDate = dateAdapter.setMinute(newDate.value, finalMinute);
    setSelectedDate(newDate);

    if (!showTime) {
      onChange?.(newDate.format(format));
      setIsOpen(false);
    }
  };

  const handleTimeChange = (type: 'hour' | 'minute', value: number) => {
    const newTimeValue = { ...timeValue, [type]: value };
    setTimeValue(newTimeValue);

    if (selectedDate) {
      let newDate = dateAdapter.setHour(selectedDate.value, newTimeValue.hour);
      newDate = dateAdapter.setMinute(newDate.value, newTimeValue.minute);
      setSelectedDate(newDate);
    }
  };

  const handleConfirm = () => {
    if (selectedDate) {
      let finalDate = dateAdapter.setHour(selectedDate.value, timeValue.hour);
      finalDate = dateAdapter.setMinute(finalDate.value, timeValue.minute);
      onChange?.(finalDate.format(format));
    }
    setIsOpen(false);
  };

  const handleToday = () => {
    const today = dateAdapter.now();
    setCurrentMonth(today);
    setSelectedDate(today);
    setTimeValue({
      hour: dateAdapter.getHour(today.value),
      minute: dateAdapter.getMinute(today.value),
    });
  };

  const displayValue = selectedDate ? selectedDate.format(format) : '';

  return (
    <div ref={containerRef} className={cn('relative', className)}>
      {/* 输入框 */}
      <div className='relative'>
        <Input
          ref={inputRef}
          value={displayValue}
          placeholder={placeholder}
          readOnly
          disabled={disabled}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          className={cn(
            'h-6 text-xs pr-7 border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors cursor-pointer',
            disabled && 'bg-gray-100 cursor-not-allowed'
          )}
        />
        <div
          className={cn(
            'absolute right-0 top-0 h-full w-7 flex items-center justify-center cursor-pointer',
            disabled && 'cursor-not-allowed'
          )}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <Calendar
            className={cn(
              'w-3 h-3 transition-colors',
              disabled ? 'text-gray-400' : 'text-slate-500'
            )}
          />
        </div>
      </div>

      {/* 弹出面板 */}
      {isOpen && (
        <div className='absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-50 p-3 min-w-[300px] max-w-[320px]'>
          {/* 月份导航 */}
          <div className='flex items-center justify-between mb-3'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => {
                const prevMonth = dateAdapter.subtract(currentMonth.value, 1, 'month');
                setCurrentMonth(prevMonth);
              }}
              className='h-6 w-6 p-0'
            >
              <ChevronLeft className='h-3 w-3' />
            </Button>
            <span className='text-sm font-medium'>{currentMonth.format('YYYY年MM月')}</span>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => {
                const nextMonth = dateAdapter.add(currentMonth.value, 1, 'month');
                setCurrentMonth(nextMonth);
              }}
              className='h-6 w-6 p-0'
            >
              <ChevronRight className='h-3 w-3' />
            </Button>
          </div>

          {/* 星期标题 */}
          <div className='grid grid-cols-7 gap-1 mb-2'>
            {['日', '一', '二', '三', '四', '五', '六'].map(day => (
              <div key={day} className='text-xs text-gray-500 text-center py-1'>
                {day}
              </div>
            ))}
          </div>

          {/* 日期网格 */}
          <div className='grid grid-cols-7 gap-1 mb-3'>
            {generateCalendarDays().map((day, index) => {
              const isCurrentMonth =
                dateAdapter.getMonth(day.value) === dateAdapter.getMonth(currentMonth.value);
              const isSelected = selectedDate && day.isSame(selectedDate.value, 'day');
              const today = dateAdapter.now();
              const isToday = day.isSame(today.value, 'day');

              return (
                <button
                  key={index}
                  onClick={() => handleDateSelect(day)}
                  className={cn(
                    'h-7 w-7 text-xs rounded-md transition-all duration-200 font-medium',
                    isCurrentMonth ? 'text-gray-900' : 'text-gray-400',
                    isSelected && 'bg-blue-500 text-white shadow-sm',
                    isToday && !isSelected && 'bg-blue-100 text-blue-700 font-semibold',
                    !isSelected && 'hover:bg-blue-50 hover:text-blue-600',
                    isSelected && 'hover:bg-blue-600'
                  )}
                >
                  {dateAdapter.getDate(day.value)}
                </button>
              );
            })}
          </div>

          {/* 时间选择 */}
          {showTime && (
            <div className='border-t pt-3 mb-3'>
              <div className='flex items-center gap-2 mb-2'>
                <Clock className='h-3 w-3 text-gray-500' />
                <span className='text-xs text-gray-600'>时间</span>
              </div>
              <div className='flex items-center gap-2 justify-center'>
                <select
                  value={timeValue.hour}
                  onChange={e => handleTimeChange('hour', parseInt(e.target.value))}
                  className='text-xs border border-gray-200 rounded-md px-2 py-1.5 w-16 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                >
                  {Array.from({ length: 24 }, (_, i) => (
                    <option key={i} value={i}>
                      {i.toString().padStart(2, '0')}
                    </option>
                  ))}
                </select>
                <span className='text-sm text-gray-500 font-medium'>:</span>
                <select
                  value={timeValue.minute}
                  onChange={e => handleTimeChange('minute', parseInt(e.target.value))}
                  className='text-xs border border-gray-200 rounded-md px-2 py-1.5 w-16 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                >
                  {Array.from({ length: 60 }, (_, i) => (
                    <option key={i} value={i}>
                      {i.toString().padStart(2, '0')}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}

          {/* 底部按钮 */}
          <div className='flex items-center justify-between border-t pt-3'>
            <Button
              variant='ghost'
              size='sm'
              onClick={handleToday}
              className='h-7 text-xs px-3 text-blue-600 hover:text-blue-700 hover:bg-blue-50'
            >
              今天
            </Button>
            <div className='flex gap-2'>
              <Button
                variant='ghost'
                size='sm'
                onClick={() => setIsOpen(false)}
                className='h-7 text-xs px-3 hover:bg-gray-100'
              >
                取消
              </Button>
              <Button
                size='sm'
                onClick={handleConfirm}
                className='h-7 text-xs px-4 bg-blue-500 hover:bg-blue-600'
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
