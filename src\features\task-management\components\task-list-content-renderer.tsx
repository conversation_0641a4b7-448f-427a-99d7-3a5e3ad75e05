// src/components/sections/task-list/components/task-list-content-renderer.tsx
import React from 'react';

import type { ColumnDef } from '@tanstack/react-table';

import type {
  CustomColumnDefinition,
  DensityStyleValues,
  Task,
  TaskGroup,
  TaskGroupConfig,
  TaskListStoredSettings,
  Vehicle,
  VehicleDisplayMode,
  TaskListRowDisplayMode,
} from '@/core/types';
import { TaskCardConfig } from '@/core/types/taskCardConfig';

import { TaskListCardContent } from './task-list-card-content';
import { TaskListTableContent } from './components/task-list-table-content';

interface TaskListContentRendererProps {
  // Display mode
  displayMode: 'table' | 'card';
  rowDisplayMode?: TaskListRowDisplayMode;

  // Data
  filteredTasks: Task[];
  tasksWithVehicles: Task[];
  taskGroups: TaskGroup[];
  allVehicles: Vehicle[];
  tableColumns: ColumnDef<Task>[];

  // Settings
  settings: TaskListStoredSettings;
  groupConfig?: TaskGroupConfig; // 独立的分组配置，优先级高于 settings.groupConfig
  densityStyles: DensityStyleValues;
  vehicleDisplayMode: VehicleDisplayMode;
  taskStatusFilter: string;
  taskCardConfig: TaskCardConfig;

  // Dimensions and calculations
  tableTotalWidth: number;
  estimateRowHeight: (task?: Task) => number;
  cardGridContainerClasses: string;

  // Drag and Drop State
  dragOverTaskId: string | null;
  setDragOverTaskId: (taskId: string | null) => void;
  dragOverProductionLineId: string | null;
  setDragOverProductionLineId: (lineId: string | null) => void;

  // Event Handlers - Table
  onColumnSizingChange: (sizing: any) => void;
  onColumnOrderChange: (newOrder: string[]) => void;
  onColumnVisibilityChange: (visibility: any) => void;
  onHeaderContextMenu: (event: React.MouseEvent, columnDef: CustomColumnDefinition) => void;
  onHeaderDoubleClick: (event: React.MouseEvent, columnDef: CustomColumnDefinition) => void;
  onRowContextMenu: (e: React.MouseEvent, row: any) => void;
  onRowDoubleClick: (row: any) => void;

  // Event Handlers - General
  onDropOnProductionLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onToggleGroupCollapse: (groupKey: string) => void;
  onCancelGrouping: () => void;
  onTaskContextMenu: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick: (task: Task) => void;

  // Event Handlers - Vehicle/Card
  onVehicleDrop: (vehicle: Vehicle, taskId: string) => void;
  onOpenVehicleCardContextMenu: (e: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onOpenDeliveryOrderDetailsForVehicle: (vehicleId: string, taskId: string) => void;
  onOpenStyleEditor: () => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onVehicleDispatchedToLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onDropVehicleFromPanelOnTaskCard: (vehicle: Vehicle, taskId: string) => void;
  onDropVehicleOnLine: (
    vehicle: Vehicle,
    taskId: string,
    lineId: string,
    sourceTaskId?: string
  ) => void;
  onTaskCardConfigChange: (config: TaskCardConfig) => void;
  onOpenCardConfigModal: () => void;

  // Styling
  getColumnBackgroundProps: (
    columnId: string,
    isHeader: boolean,
    isFixed: boolean
  ) => { style: React.CSSProperties; className: string };
  getCellTextClasses?: (columnId: string) => string;
  getStatusLabelProps: (status: string) => { label: string; variant: string };
}

export function TaskListContentRenderer({
  displayMode,
  rowDisplayMode = 'row', // 新增：行显示模式，默认为'row'
  filteredTasks,
  tasksWithVehicles,
  taskGroups,
  allVehicles,
  tableColumns,
  settings,
  groupConfig,
  densityStyles,
  vehicleDisplayMode,
  taskStatusFilter,
  taskCardConfig,
  tableTotalWidth,
  estimateRowHeight,
  cardGridContainerClasses,
  dragOverTaskId,
  setDragOverTaskId,
  dragOverProductionLineId,
  setDragOverProductionLineId,
  onColumnSizingChange,
  onColumnOrderChange,
  onColumnVisibilityChange,
  onHeaderContextMenu,
  onHeaderDoubleClick,
  onRowContextMenu,
  onRowDoubleClick,
  onDropOnProductionLine,
  onToggleGroupCollapse,
  onCancelGrouping,
  onTaskContextMenu,
  onTaskDoubleClick,
  onVehicleDrop,
  onOpenVehicleCardContextMenu,
  onOpenDeliveryOrderDetailsForVehicle,
  onOpenStyleEditor,
  onCancelVehicleDispatch,
  onVehicleDispatchedToLine,
  onDropVehicleFromPanelOnTaskCard,
  onDropVehicleOnLine,
  onTaskCardConfigChange,
  onOpenCardConfigModal,
  getColumnBackgroundProps,
  getCellTextClasses,
  getStatusLabelProps,
}: TaskListContentRendererProps) {
  // 调试日志
  console.log('🎨 [TaskListContentRenderer] Received rowDisplayMode:', rowDisplayMode);

  // 使用独立的分组配置，如果没有提供则使用 settings 中的配置
  const effectiveGroupConfig = groupConfig || settings.groupConfig;

  if (displayMode === 'table') {
    return (
      <TaskListTableContent
        tasks={tasksWithVehicles}
        taskGroups={taskGroups}
        tableColumns={tableColumns}
        enableZebraStriping={settings.enableZebraStriping}
        densityStyles={densityStyles}
        groupConfig={effectiveGroupConfig}
        rowDisplayMode={rowDisplayMode} /* 新增：传递行显示模式 */
        tableTotalWidth={tableTotalWidth}
        estimateRowHeight={estimateRowHeight}
        columnSizing={settings.columnWidths}
        columnVisibility={settings.columnVisibility}
        columnOrder={settings.columnOrder}
        onColumnSizingChange={onColumnSizingChange}
        onColumnOrderChange={updater => {
          if (typeof updater === 'function') {
            const newOrder = updater([]);
            onColumnOrderChange(newOrder);
          } else {
            onColumnOrderChange(updater);
          }
        }}
        onColumnVisibilityChange={onColumnVisibilityChange}
        onHeaderContextMenu={onHeaderContextMenu}
        onHeaderDoubleClick={onHeaderDoubleClick}
        onRowContextMenu={onRowContextMenu}
        onRowDoubleClick={onRowDoubleClick}
        onDropOnProductionLine={onDropOnProductionLine}
        onToggleGroupCollapse={onToggleGroupCollapse}
        onCancelGrouping={onCancelGrouping}
        getColumnBackgroundProps={getColumnBackgroundProps}
        getCellTextClasses={getCellTextClasses}
      />
    );
  }

  if (displayMode === 'card') {
    // 为卡片模式创建带有有效分组配置的设置对象
    const effectiveSettings = {
      ...settings,
      groupConfig: effectiveGroupConfig,
    };

    return (
      <div className={`h-full flex-1 min-h-0 ${cardGridContainerClasses}`}>
        <TaskListCardContent
          key={`card-content-${effectiveGroupConfig.enabled}-${effectiveGroupConfig.groupBy}-${taskGroups.length}`}
          filteredTasks={tasksWithVehicles}
          vehicles={allVehicles}
          taskGroups={taskGroups}
          settings={effectiveSettings}
          vehicleDisplayMode={vehicleDisplayMode}
          taskStatusFilter={taskStatusFilter}
          taskCardConfig={taskCardConfig}
          dragOverTaskId={dragOverTaskId}
          setDragOverTaskId={setDragOverTaskId}
          dragOverProductionLineId={dragOverProductionLineId}
          setDragOverProductionLineId={setDragOverProductionLineId}
          handleVehicleDrop={onVehicleDrop}
          handleTaskContextMenu={onTaskContextMenu}
          handleRowDoubleClick={onTaskDoubleClick}
          onOpenVehicleCardContextMenu={onOpenVehicleCardContextMenu}
          onOpenDeliveryOrderDetailsForVehicle={onOpenDeliveryOrderDetailsForVehicle}
          onOpenStyleEditor={onOpenStyleEditor}
          onCancelVehicleDispatch={onCancelVehicleDispatch}
          onVehicleDispatchedToLine={onVehicleDispatchedToLine}
          onDropVehicleFromPanelOnTaskCard={onDropVehicleFromPanelOnTaskCard}
          onDropVehicleOnLine={onDropVehicleOnLine}
          onToggleGroupCollapse={onToggleGroupCollapse}
          onCancelGrouping={onCancelGrouping}
          onTaskCardConfigChange={onTaskCardConfigChange}
          onOpenCardConfigModal={onOpenCardConfigModal}
          getStatusLabelProps={getStatusLabelProps}
        />
      </div>
    );
  }

  return null;
}
