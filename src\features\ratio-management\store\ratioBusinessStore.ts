/**
 * 配比业务状态管理
 * 管理配比相关的业务数据和状态
 */

import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import type {
  RatioModel,
  RatioMaterialModel,
  RatioTemplateModel,
  RatioHistoryModel,
} from '@/models/RatioModel';
import type { MaterialModel } from '@/models/MaterialModel';
import { BusinessResult } from '@/core/domain/BusinessResult';
import type {
  CalculationParams,
  CalculationResult,
  QualityAnalysis,
  CostAnalysis,
  EnvironmentalAnalysis,
  OptimizationRecommendation,
} from '@/features/ratio-management/services/RatioBusinessService';

export interface RatioBusinessState {
  // 当前配比数据
  currentRatio: RatioModel | null;

  // 材料数据
  availableMaterials: MaterialModel[];
  selectedMaterials: RatioMaterialModel[];

  // 计算相关
  calculationParams: CalculationParams | null;
  calculationResult: CalculationResult | null;
  isCalculating: boolean;

  // 分析结果
  qualityAnalysis: QualityAnalysis | null;
  costAnalysis: CostAnalysis | null;
  environmentalAnalysis: EnvironmentalAnalysis | null;

  // 优化建议
  optimizationRecommendations: OptimizationRecommendation[];

  // 历史记录
  ratioHistory: RatioHistoryModel[];

  // 模板
  ratioTemplates: RatioTemplateModel[];

  // 操作状态
  operationStatus: {
    saving: boolean;
    loading: boolean;
    calculating: boolean;
    analyzing: boolean;
    optimizing: boolean;
  };

  // 错误状态
  errors: {
    load: string | null;
    save: string | null;
    calculate: string | null;
    analyze: string | null;
    optimize: string | null;
  };

  // 缓存状态
  cache: {
    lastUpdated: string | null;
    version: number;
    isDirty: boolean;
  };
}

export interface RatioBusinessActions {
  // 配比管理
  loadRatio: (taskId: string) => Promise<BusinessResult<RatioModel>>;
  saveRatio: (ratio: RatioModel) => Promise<BusinessResult<RatioModel>>;
  updateRatio: (updates: Partial<RatioModel>) => Promise<BusinessResult<RatioModel>>;
  clearCurrentRatio: () => void;

  // 材料管理
  loadAvailableMaterials: () => Promise<BusinessResult<MaterialModel[]>>;
  addMaterial: (material: RatioMaterialModel) => void;
  removeMaterial: (materialId: string) => void;
  updateMaterial: (materialId: string, updates: Partial<RatioMaterialModel>) => void;
  clearSelectedMaterials: () => void;

  // 计算操作
  setCalculationParams: (params: CalculationParams) => void;
  updateCalculationParams: (updates: Partial<CalculationParams>) => void;
  calculateRatio: () => Promise<BusinessResult<CalculationResult>>;
  reverseCalculate: () => Promise<BusinessResult<CalculationParams>>;
  clearCalculationResult: () => void;

  // 分析操作
  analyzeQuality: () => Promise<BusinessResult<QualityAnalysis>>;
  analyzeCost: () => Promise<BusinessResult<CostAnalysis>>;
  analyzeEnvironmental: () => Promise<BusinessResult<EnvironmentalAnalysis>>;
  clearAnalyses: () => void;

  // 优化操作
  getOptimizationRecommendations: () => Promise<BusinessResult<OptimizationRecommendation[]>>;
  applyOptimization: (
    recommendation: OptimizationRecommendation
  ) => Promise<BusinessResult<RatioModel>>;
  clearOptimizationRecommendations: () => void;

  // 历史管理
  loadRatioHistory: (taskId: string) => Promise<BusinessResult<RatioHistoryModel[]>>;
  addHistoryRecord: (
    record: Omit<RatioHistoryModel, 'id'>
  ) => Promise<BusinessResult<RatioHistoryModel>>;
  clearHistory: () => void;

  // 模板管理
  loadRatioTemplates: () => Promise<BusinessResult<RatioTemplateModel[]>>;
  saveAsTemplate: (
    name: string,
    description: string
  ) => Promise<BusinessResult<RatioTemplateModel>>;
  applyTemplate: (templateId: string) => Promise<BusinessResult<RatioModel>>;
  deleteTemplate: (templateId: string) => Promise<BusinessResult<void>>;

  // 状态管理
  setOperationStatus: (
    operation: keyof RatioBusinessState['operationStatus'],
    status: boolean
  ) => void;
  setError: (type: keyof RatioBusinessState['errors'], error: string | null) => void;
  clearErrors: () => void;
  markDirty: () => void;
  markClean: () => void;

  // 重置操作
  resetBusinessState: () => void;
  resetCalculationData: () => void;
  resetAnalysisData: () => void;
}

const initialState: RatioBusinessState = {
  currentRatio: null,
  availableMaterials: [],
  selectedMaterials: [],
  calculationParams: null,
  calculationResult: null,
  isCalculating: false,
  qualityAnalysis: null,
  costAnalysis: null,
  environmentalAnalysis: null,
  optimizationRecommendations: [],
  ratioHistory: [],
  ratioTemplates: [],
  operationStatus: {
    saving: false,
    loading: false,
    calculating: false,
    analyzing: false,
    optimizing: false,
  },
  errors: {
    load: null,
    save: null,
    calculate: null,
    analyze: null,
    optimize: null,
  },
  cache: {
    lastUpdated: null,
    version: 0,
    isDirty: false,
  },
};

export const useRatioBusinessStore = create<RatioBusinessState & RatioBusinessActions>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      ...initialState,

      // 配比管理
      loadRatio: async (taskId: string) => {
        set(state => ({
          operationStatus: { ...state.operationStatus, loading: true },
          errors: { ...state.errors, load: null },
        }));

        try {
          // 这里需要注入业务服务
          // const result = await ratioBusinessService.getRatio(taskId);

          // 模拟实现
          const mockRatio: RatioModel = {
            id: `ratio-${taskId}`,
            taskId,
            ratioNumber: `R-${taskId}-001`,
            version: 1,
            taskInfo: {
              taskNumber: taskId,
              projectName: '测试项目',
              customerName: '测试客户',
              strengthGrade: 'C30',
              usagePart: '测试部位',
              constructionUnit: '测试施工单位',
              mixingStation: '搅拌站A',
            },
            calculationParams: {
              targetStrength: 30,
              slump: 180,
              maxAggregateSize: 25,
              exposureClass: 'XC1',
              ambientTemperature: 20,
              relativeHumidity: 60,
              cementTemperature: 20,
              aggregateTemperature: 20,
              selectedMaterials: [],
              cementType: 'P.O 42.5',
              aggregateType: '碎石',
              waterType: '自来水',
              waterCementRatio: 0.5,
              sandRatio: 0.35,
              cementContent: 350,
              waterContent: 175,
              additiveRatio: 0,
              flyashRatio: 0,
              mineralPowderRatio: 0,
              silicaFumeRatio: 0,
              antifreezeRatio: 0,
              expansionRatio: 0,
              cementAmount: 350,
              waterAmount: 175,
              density: 2400,
              airContent: 2,
              strengthGrade: 30,
              ultraFineSandRatio: 0,
              earlyStrengthRatio: 0,
              s105Ratio: 0,
              placementMethod: '泵送',
              finishingRequirement: '普通',
              cureConditions: {
                temperature: 20,
                humidity: 95,
                duration: 28,
              },
            },
            materials: [],
            calculationResults: {
              totalWeight: 2400,
              materials: {},
              strengthPrediction: 30,
              qualityScore: 85,
              warnings: [],
              suggestions: [],
              carbonFootprint: 0,
              costEstimate: 0,
            },
            qualityAssessment: {
              overallScore: 85,
              warnings: [],
              recommendations: [],
              complianceStatus: 'compliant',
            },
            metadata: {
              createdAt: new Date().toISOString(),
              createdBy: 'system',
              updatedAt: new Date().toISOString(),
              updatedBy: 'system',
              status: 'draft',
            },
          };

          const mockResult = BusinessResult.success(mockRatio);

          if (mockResult.success && mockResult.data) {
            // 转换 RatioModel 的 calculationParams 为业务服务的 CalculationParams
            const businessCalculationParams: CalculationParams = {
              targetStrength: mockResult.data!.calculationParams.targetStrength,
              slump: mockResult.data!.calculationParams.slump,
              maxAggregateSize: mockResult.data!.calculationParams.maxAggregateSize,
              exposureClass: mockResult.data!.calculationParams.exposureClass.toString(),
              ambientTemperature: mockResult.data!.calculationParams.ambientTemperature,
              relativeHumidity: mockResult.data!.calculationParams.relativeHumidity,
              cementTemperature: mockResult.data!.calculationParams.cementTemperature,
              aggregateTemperature: mockResult.data!.calculationParams.aggregateTemperature,
              waterCementRatio: mockResult.data!.calculationParams.waterCementRatio,
              sandRatio: mockResult.data!.calculationParams.sandRatio,
              cementContent: mockResult.data!.calculationParams.cementContent,
              waterContent: mockResult.data!.calculationParams.waterContent,
              additiveRatio: mockResult.data!.calculationParams.additiveRatio,
              flyashRatio: mockResult.data!.calculationParams.flyashRatio,
              mineralPowderRatio: mockResult.data!.calculationParams.mineralPowderRatio,
              silicaFumeRatio: mockResult.data!.calculationParams.silicaFumeRatio,
              antifreezeRatio: mockResult.data!.calculationParams.antifreezeRatio,
              expansionRatio: mockResult.data!.calculationParams.expansionRatio,
              ultraFineSandRatio: mockResult.data!.calculationParams.ultraFineSandRatio,
              earlyStrengthRatio: mockResult.data!.calculationParams.earlyStrengthRatio,
              s105Ratio: 0, // 这个字段在RatioCalculationParams中不存在，使用默认值
              cementAmount: mockResult.data!.calculationParams.cementAmount,
              waterAmount: mockResult.data!.calculationParams.waterAmount,
              density: mockResult.data!.calculationParams.density,
              airContent: mockResult.data!.calculationParams.airContent,
              strengthGrade: mockResult.data!.calculationParams.strengthGrade,
              selectedMaterials: mockResult.data!.calculationParams.selectedMaterials,
              cementType: mockResult.data!.calculationParams.cementType,
              aggregateType: mockResult.data!.calculationParams.aggregateType,
              waterType: mockResult.data!.calculationParams.waterType,
              placementMethod: mockResult.data!.calculationParams.placementMethod.toString(),
              finishingRequirement:
                mockResult.data!.calculationParams.finishingRequirement.toString(),
              cureConditions: {
                temperature: mockResult.data!.calculationParams.cureConditions.temperature,
                humidity: mockResult.data!.calculationParams.cureConditions.humidity,
                duration: mockResult.data!.calculationParams.cureConditions.duration,
              },
            };

            // 转换 RatioModel 的 calculationResults 为业务服务的 CalculationResult
            const businessCalculationResult: CalculationResult = {
              totalWeight: mockResult.data!.calculationResults.totalWeight,
              materials: mockResult.data!.calculationResults.materials,
              strengthPrediction: mockResult.data!.calculationResults.strengthPrediction,
              qualityScore: mockResult.data!.calculationResults.qualityScore,
              warnings: mockResult.data!.calculationResults.warnings,
              suggestions: mockResult.data!.calculationResults.suggestions,
              carbonFootprint: mockResult.data!.calculationResults.carbonFootprint,
              costEstimate: mockResult.data!.calculationResults.costEstimate,
              durabilityIndex: 85,
              workabilityIndex: 80,
              economyIndex: 75,
              metadata: {
                engineVersion: '1.0.0',
                calculationTime: 150,
                complexity: 'standard' as const,
                confidence: 85,
                method: 'mock',
                timestamp: new Date().toISOString(),
              },
            };

            set(state => ({
              currentRatio: mockResult.data!,
              selectedMaterials: mockResult.data!.materials || [],
              calculationParams: businessCalculationParams,
              calculationResult: businessCalculationResult,
              operationStatus: { ...state.operationStatus, loading: false },
              cache: {
                lastUpdated: new Date().toISOString(),
                version: state.cache.version + 1,
                isDirty: false,
              },
            }));
          } else {
            set(state => ({
              operationStatus: { ...state.operationStatus, loading: false },
              errors: { ...state.errors, load: mockResult.error?.message || '加载失败' },
            }));
          }

          return mockResult;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '未知错误';
          set(state => ({
            operationStatus: { ...state.operationStatus, loading: false },
            errors: { ...state.errors, load: errorMessage },
          }));
          throw error;
        }
      },

      saveRatio: async (ratio: RatioModel) => {
        set(state => ({
          operationStatus: { ...state.operationStatus, saving: true },
          errors: { ...state.errors, save: null },
        }));

        try {
          // 这里需要注入业务服务
          // const result = await ratioBusinessService.saveRatio(ratio);

          // 模拟实现
          const mockResult: BusinessResult<RatioModel> = {
            success: true,
            data: ratio,
          } as any;

          if (mockResult.success && mockResult.data) {
            set(state => ({
              currentRatio: mockResult.data!,
              operationStatus: { ...state.operationStatus, saving: false },
              cache: {
                lastUpdated: new Date().toISOString(),
                version: state.cache.version + 1,
                isDirty: false,
              },
            }));
          } else {
            set(state => ({
              operationStatus: { ...state.operationStatus, saving: false },
              errors: { ...state.errors, save: mockResult.error?.message || '保存失败' },
            }));
          }

          return mockResult;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '未知错误';
          set(state => ({
            operationStatus: { ...state.operationStatus, saving: false },
            errors: { ...state.errors, save: errorMessage },
          }));
          throw error;
        }
      },

      updateRatio: async (updates: Partial<RatioModel>) => {
        const currentRatio = get().currentRatio;
        if (!currentRatio) {
          throw new Error('没有当前配比数据');
        }

        const updatedRatio: RatioModel = {
          ...currentRatio,
          ...updates,
          metadata: {
            ...currentRatio.metadata,
            ...updates.metadata,
            updatedAt: new Date().toISOString(),
          },
        };

        return get().saveRatio(updatedRatio);
      },

      clearCurrentRatio: () =>
        set({
          currentRatio: null,
          selectedMaterials: [],
          calculationParams: null,
          calculationResult: null,
          qualityAnalysis: null,
          costAnalysis: null,
          environmentalAnalysis: null,
          optimizationRecommendations: [],
        }),

      // 材料管理
      loadAvailableMaterials: async () => {
        try {
          // 这里需要注入材料服务
          // const result = await materialService.getAvailableMaterials();

          // 模拟实现
          const mockResult: BusinessResult<MaterialModel[]> = {
            success: true,
            data: [],
          } as any;

          if (mockResult.success && mockResult.data) {
            set({ availableMaterials: mockResult.data });
          }

          return mockResult;
        } catch (error) {
          throw error;
        }
      },

      addMaterial: (material: RatioMaterialModel) => {
        set(state => ({
          selectedMaterials: [...state.selectedMaterials, material],
          cache: { ...state.cache, isDirty: true },
        }));
      },

      removeMaterial: (materialId: string) => {
        set(state => ({
          selectedMaterials: state.selectedMaterials.filter(m => m.id !== materialId),
          cache: { ...state.cache, isDirty: true },
        }));
      },

      updateMaterial: (materialId: string, updates: Partial<RatioMaterialModel>) => {
        set(state => ({
          selectedMaterials: state.selectedMaterials.map(m =>
            m.id === materialId ? { ...m, ...updates } : m
          ),
          cache: { ...state.cache, isDirty: true },
        }));
      },

      clearSelectedMaterials: () =>
        set({
          selectedMaterials: [],
        }),

      // 计算操作
      setCalculationParams: (params: CalculationParams) => {
        set(state => ({
          calculationParams: params,
          cache: { ...state.cache, isDirty: true },
        }));
      },

      updateCalculationParams: (updates: Partial<CalculationParams>) => {
        set(state => ({
          calculationParams: state.calculationParams
            ? { ...state.calculationParams, ...updates }
            : null,
          cache: { ...state.cache, isDirty: true },
        }));
      },

      calculateRatio: async () => {
        const { calculationParams } = get();
        if (!calculationParams) {
          throw new Error('缺少计算参数');
        }

        set(state => ({
          operationStatus: { ...state.operationStatus, calculating: true },
          errors: { ...state.errors, calculate: null },
        }));

        try {
          // 这里需要注入业务服务
          // const result = await ratioBusinessService.calculateRatio(calculationParams);

          // 模拟实现
          const mockResult: BusinessResult<CalculationResult> = {
            success: true,
            data: {} as CalculationResult,
          } as any;

          if (mockResult.success && mockResult.data) {
            set(state => ({
              calculationResult: mockResult.data!,
              operationStatus: { ...state.operationStatus, calculating: false },
              cache: { ...state.cache, isDirty: true },
            }));
          } else {
            set(state => ({
              operationStatus: { ...state.operationStatus, calculating: false },
              errors: { ...state.errors, calculate: mockResult.error?.message || '计算失败' },
            }));
          }

          return mockResult;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '未知错误';
          set(state => ({
            operationStatus: { ...state.operationStatus, calculating: false },
            errors: { ...state.errors, calculate: errorMessage },
          }));
          throw error;
        }
      },

      reverseCalculate: async () => {
        const { selectedMaterials } = get();
        if (selectedMaterials.length === 0) {
          throw new Error('没有选择材料');
        }

        try {
          // 这里需要注入业务服务
          // const result = await ratioBusinessService.reverseCalculate(selectedMaterials);

          // 模拟实现
          const mockResult: BusinessResult<CalculationParams> = {
            success: true,
            data: {} as CalculationParams,
          } as any;

          if (mockResult.success && mockResult.data) {
            set(state => ({
              calculationParams: mockResult.data!,
              cache: { ...state.cache, isDirty: true },
            }));
          }

          return mockResult;
        } catch (error) {
          throw error;
        }
      },

      clearCalculationResult: () =>
        set({
          calculationResult: null,
        }),

      // 分析操作
      analyzeQuality: async () => {
        const { currentRatio } = get();
        if (!currentRatio) {
          throw new Error('没有当前配比数据');
        }

        set(state => ({
          operationStatus: { ...state.operationStatus, analyzing: true },
          errors: { ...state.errors, analyze: null },
        }));

        try {
          // 这里需要注入业务服务
          // const result = await ratioBusinessService.analyzeQuality(currentRatio);

          // 模拟实现
          const mockResult: BusinessResult<QualityAnalysis> = {
            success: true,
            data: {} as QualityAnalysis,
          } as any;

          if (mockResult.success && mockResult.data) {
            set(state => ({
              qualityAnalysis: mockResult.data!,
              operationStatus: { ...state.operationStatus, analyzing: false },
            }));
          } else {
            set(state => ({
              operationStatus: { ...state.operationStatus, analyzing: false },
              errors: { ...state.errors, analyze: mockResult.error?.message || '分析失败' },
            }));
          }

          return mockResult;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '未知错误';
          set(state => ({
            operationStatus: { ...state.operationStatus, analyzing: false },
            errors: { ...state.errors, analyze: errorMessage },
          }));
          throw error;
        }
      },

      analyzeCost: async () => {
        const { currentRatio } = get();
        if (!currentRatio) {
          throw new Error('没有当前配比数据');
        }

        try {
          // 这里需要注入业务服务
          // const result = await ratioBusinessService.analyzeCost(currentRatio);

          // 模拟实现
          const mockResult: BusinessResult<CostAnalysis> = {
            success: true,
            data: {} as CostAnalysis,
          } as any;

          if (mockResult.success && mockResult.data) {
            set({ costAnalysis: mockResult.data });
          }

          return mockResult;
        } catch (error) {
          throw error;
        }
      },

      analyzeEnvironmental: async () => {
        const { currentRatio } = get();
        if (!currentRatio) {
          throw new Error('没有当前配比数据');
        }

        try {
          // 这里需要注入业务服务
          // const result = await ratioBusinessService.analyzeEnvironmental(currentRatio);

          // 模拟实现
          const mockResult: BusinessResult<EnvironmentalAnalysis> = {
            success: true,
            data: {} as EnvironmentalAnalysis,
          } as any;

          if (mockResult.success && mockResult.data) {
            set({ environmentalAnalysis: mockResult.data });
          }

          return mockResult;
        } catch (error) {
          throw error;
        }
      },

      clearAnalyses: () =>
        set({
          qualityAnalysis: null,
          costAnalysis: null,
          environmentalAnalysis: null,
        }),

      // 优化操作
      getOptimizationRecommendations: async () => {
        const { currentRatio } = get();
        if (!currentRatio) {
          throw new Error('没有当前配比数据');
        }

        set(state => ({
          operationStatus: { ...state.operationStatus, optimizing: true },
          errors: { ...state.errors, optimize: null },
        }));

        try {
          // 这里需要注入业务服务
          // const result = await ratioBusinessService.recommendOptimizations(currentRatio);

          // 模拟实现
          const mockResult: BusinessResult<OptimizationRecommendation[]> = {
            success: true,
            data: [],
          } as any;

          if (mockResult.success && mockResult.data) {
            set(state => ({
              optimizationRecommendations: mockResult.data!,
              operationStatus: { ...state.operationStatus, optimizing: false },
            }));
          } else {
            set(state => ({
              operationStatus: { ...state.operationStatus, optimizing: false },
              errors: { ...state.errors, optimize: mockResult.error?.message || '优化失败' },
            }));
          }

          return mockResult;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '未知错误';
          set(state => ({
            operationStatus: { ...state.operationStatus, optimizing: false },
            errors: { ...state.errors, optimize: errorMessage },
          }));
          throw error;
        }
      },

      applyOptimization: async (recommendation: OptimizationRecommendation) => {
        const { currentRatio } = get();
        if (!currentRatio) {
          throw new Error('没有当前配比数据');
        }

        try {
          // 这里需要实现优化应用逻辑
          // const result = await ratioBusinessService.applyOptimization(currentRatio, recommendation);

          // 模拟实现
          const mockResult: BusinessResult<RatioModel> = {
            success: true,
            data: currentRatio,
          } as any;

          if (mockResult.success && mockResult.data) {
            set(state => ({
              currentRatio: mockResult.data!,
              cache: { ...state.cache, isDirty: true },
            }));
          }

          return mockResult;
        } catch (error) {
          throw error;
        }
      },

      clearOptimizationRecommendations: () =>
        set({
          optimizationRecommendations: [],
        }),

      // 历史管理
      loadRatioHistory: async (taskId: string) => {
        try {
          // 这里需要注入仓库服务
          // const result = await ratioRepository.getRatioHistory(taskId);

          // 模拟实现
          const mockResult: BusinessResult<RatioHistoryModel[]> = {
            success: true,
            data: [],
          } as any;

          if (mockResult.success && mockResult.data) {
            set({ ratioHistory: mockResult.data });
          }

          return mockResult;
        } catch (error) {
          throw error;
        }
      },

      addHistoryRecord: async (record: Omit<RatioHistoryModel, 'id'>) => {
        try {
          // 这里需要注入仓库服务
          // const result = await ratioRepository.addHistoryRecord(record);

          // 模拟实现
          const mockResult: BusinessResult<RatioHistoryModel> = {
            success: true,
            data: { ...record, id: Date.now().toString() } as RatioHistoryModel,
          } as any;

          if (mockResult.success && mockResult.data) {
            set(state => ({
              ratioHistory: [...state.ratioHistory, mockResult.data!],
            }));
          }

          return mockResult;
        } catch (error) {
          throw error;
        }
      },

      clearHistory: () =>
        set({
          ratioHistory: [],
        }),

      // 模板管理
      loadRatioTemplates: async () => {
        try {
          // 这里需要注入仓库服务
          // const result = await ratioRepository.getRatioTemplates();

          // 模拟实现
          const mockResult: BusinessResult<RatioTemplateModel[]> = {
            success: true,
            data: [],
          } as any;

          if (mockResult.success && mockResult.data) {
            set({ ratioTemplates: mockResult.data });
          }

          return mockResult;
        } catch (error) {
          throw error;
        }
      },

      saveAsTemplate: async (name: string, description: string) => {
        const { currentRatio } = get();
        if (!currentRatio) {
          throw new Error('没有当前配比数据');
        }

        try {
          // 这里需要实现模板保存逻辑
          const template: Omit<RatioTemplateModel, 'id'> = {
            name,
            description,
            category: 'custom',
            strengthGrade: currentRatio.taskInfo.strengthGrade,
            applicableConditions: {
              minStrength: currentRatio.calculationParams.targetStrength - 5,
              maxStrength: currentRatio.calculationParams.targetStrength + 5,
              exposureClasses: [currentRatio.calculationParams.exposureClass.toString()],
              placementMethods: [currentRatio.calculationParams.placementMethod.toString()],
            },
            defaultParams: currentRatio.calculationParams,
            defaultMaterials: currentRatio.materials,
            tags: [],
            isPublic: false,
            createdBy: currentRatio.metadata.createdBy,
            createdAt: new Date().toISOString(),
            usageCount: 0,
            rating: 0,
          };

          // const result = await ratioRepository.saveRatioTemplate(template);

          // 模拟实现
          const mockResult: BusinessResult<RatioTemplateModel> = {
            success: true,
            data: { ...template, id: Date.now().toString() } as RatioTemplateModel,
          } as any;

          if (mockResult.success && mockResult.data) {
            set(state => ({
              ratioTemplates: [...state.ratioTemplates, mockResult.data!],
            }));
          }

          return mockResult;
        } catch (error) {
          throw error;
        }
      },

      applyTemplate: async (templateId: string) => {
        const { ratioTemplates } = get();
        const template = ratioTemplates.find(t => t.id === templateId);

        if (!template) {
          throw new Error('模板不存在');
        }

        try {
          // 应用模板到当前配比
          const appliedRatio: RatioModel = {
            id: Date.now().toString(),
            taskId: '', // 需要从上下文获取
            ratioNumber: '',
            version: 1,
            taskInfo: {
              taskNumber: '',
              projectName: '',
              customerName: '',
              strengthGrade: template.strengthGrade,
              usagePart: '',
              constructionUnit: '',
              mixingStation: '',
            },
            calculationParams: template.defaultParams,
            materials: template.defaultMaterials,
            calculationResults: {
              totalWeight: 0,
              materials: {},
              strengthPrediction: 0,
              qualityScore: 0,
              warnings: [],
              suggestions: [],
              carbonFootprint: 0,
              costEstimate: 0,
            },
            qualityAssessment: {
              overallScore: 0,
              warnings: [],
              recommendations: [],
              complianceStatus: 'warning' as const,
            },
            metadata: {
              status: 'draft' as const,
              createdBy: '',
              createdAt: new Date().toISOString(),
              updatedBy: '',
              updatedAt: new Date().toISOString(),
              notes: `基于模板 "${template.name}" 创建`,
            },
          };

          set(state => ({
            currentRatio: appliedRatio,
            selectedMaterials: appliedRatio.materials,
            calculationParams: appliedRatio.calculationParams,
            calculationResult: null,
            cache: { ...state.cache, isDirty: true },
          }));

          const mockResult: BusinessResult<RatioModel> = {
            success: true,
            data: appliedRatio,
          } as any;

          return mockResult;
        } catch (error) {
          throw error;
        }
      },

      deleteTemplate: async (templateId: string) => {
        try {
          // 这里需要注入仓库服务
          // const result = await ratioRepository.deleteRatioTemplate(templateId);

          // 模拟实现
          const mockResult: BusinessResult<void> = {
            success: true,
          } as any;

          if (mockResult.success) {
            set(state => ({
              ratioTemplates: state.ratioTemplates.filter(t => t.id !== templateId),
            }));
          }

          return mockResult;
        } catch (error) {
          throw error;
        }
      },

      // 状态管理
      setOperationStatus: (operation, status) => {
        set(state => ({
          operationStatus: {
            ...state.operationStatus,
            [operation]: status,
          },
        }));
      },

      setError: (type, error) => {
        set(state => ({
          errors: {
            ...state.errors,
            [type]: error,
          },
        }));
      },

      clearErrors: () =>
        set({
          errors: {
            load: null,
            save: null,
            calculate: null,
            analyze: null,
            optimize: null,
          },
        }),

      markDirty: () =>
        set(state => ({
          cache: { ...state.cache, isDirty: true },
        })),

      markClean: () =>
        set(state => ({
          cache: { ...state.cache, isDirty: false },
        })),

      // 重置操作
      resetBusinessState: () => set(initialState),

      resetCalculationData: () =>
        set({
          calculationParams: null,
          calculationResult: null,
        }),

      resetAnalysisData: () =>
        set({
          qualityAnalysis: null,
          costAnalysis: null,
          environmentalAnalysis: null,
          optimizationRecommendations: [],
        }),
    }))
  )
);
