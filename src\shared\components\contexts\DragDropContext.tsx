'use client';

import React, { createContext, ReactNode, useCallback, useContext, useState } from 'react';

import { DndContext, DragEndEvent, DragOverEvent, DragStartEvent } from '@dnd-kit/core';

interface DragDropContextType {
  // Drag state
  isDragging: boolean;
  draggedItem: any | null;
  dragType: string | null;
  state: {
    isDragging: boolean;
    draggedItem: any | null;
    dragType: string | null;
    draggedVehicleId: string | null;
    targetTaskId: string | null;
    targetProductionLineId: string | null;
  };

  // Drag handlers
  handleDragStart: (event: DragStartEvent) => void;
  handleDragEnd: (event: DragEndEvent) => void;
  handleDragOver: (event: DragOverEvent) => void;

  // Utility functions
  setDraggedItem: (item: any) => void;
  clearDragState: () => void;
}

const DragDropContextValue = createContext<DragDropContextType | undefined>(undefined);

interface DragDropProviderProps {
  children: ReactNode;
}

export const DragDropProvider: React.FC<DragDropProviderProps> = ({ children }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [draggedItem, setDraggedItem] = useState<any | null>(null);
  const [dragType, setDragType] = useState<string | null>(null);
  const [draggedVehicleId, setDraggedVehicleId] = useState<string | null>(null);
  const [targetTaskId, setTargetTaskId] = useState<string | null>(null);
  const [targetProductionLineId, setTargetProductionLineId] = useState<string | null>(null);

  const handleDragStart = useCallback((event: DragStartEvent) => {
    const dragData = event.active.data.current;
    setIsDragging(true);
    setDraggedItem(dragData);
    setDragType(dragData?.['type'] || null);
    setDraggedVehicleId(dragData?.['vehicleId'] || null);
    setTargetTaskId(dragData?.['taskId'] || null);
    setTargetProductionLineId(dragData?.['productionLineId'] || null);
  }, []);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    // 使用 requestAnimationFrame 延迟状态清理，避免闪烁
    requestAnimationFrame(() => {
      setIsDragging(false);
      setDraggedItem(null);
      setDragType(null);
      setDraggedVehicleId(null);
      setTargetTaskId(null);
      setTargetProductionLineId(null);
    });
  }, []);

  const handleDragOver = useCallback((event: DragOverEvent) => {
    const overData = event.over?.data.current;
    if (overData) {
      setTargetTaskId(overData['taskId'] || null);
      setTargetProductionLineId(overData['productionLineId'] || null);
    }
  }, []);

  const clearDragState = useCallback(() => {
    setIsDragging(false);
    setDraggedItem(null);
    setDragType(null);
    setDraggedVehicleId(null);
    setTargetTaskId(null);
    setTargetProductionLineId(null);
  }, []);

  const value = {
    isDragging,
    draggedItem,
    dragType,
    state: {
      isDragging,
      draggedItem,
      dragType,
      draggedVehicleId,
      targetTaskId,
      targetProductionLineId,
    },
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    setDraggedItem,
    clearDragState,
  };

  return (
    <DragDropContextValue.Provider value={value}>
      <DndContext
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragOver={handleDragOver}
      >
        {children}
      </DndContext>
    </DragDropContextValue.Provider>
  );
};

export const useDragDropContext = () => {
  const context = useContext(DragDropContextValue);
  if (context === undefined) {
    throw new Error('useDragDropContext must be used within a DragDropProvider');
  }
  return context;
};
