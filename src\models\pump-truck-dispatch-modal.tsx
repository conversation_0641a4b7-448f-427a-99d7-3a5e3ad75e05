'use client';

import React, { useMemo, useCallback, useState } from 'react';

import { Save, Truck, X } from 'lucide-react';

import { Button } from '@/shared/components/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import type { Task } from '@/core/types';

interface PumpTruckDispatchModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task?: Partial<Task>;
  onConfirm: (dispatchData: PumpTruckDispatchData) => void;
}

export interface PumpTruckDispatchData {
  // 基本信息
  dispatchNumber: string;
  pumpTruckNumber: string;
  status: string;
  taskNumber: string;

  // 操作员信息
  operator1: string;
  operator2: string;
  operator3: string;
  dispatcher: string;

  // 目的地信息
  destination: string;

  // 工程信息
  constructionUnit: string;
  constructionSite: string;
  projectName: string;
  strengthGrade: string;
  quantity: number;
}

export const PumpTruckDispatchModal: React.FC<PumpTruckDispatchModalProps> = ({
  open,
  onOpenChange,
  task,
  onConfirm,
}) => {
  const [formData, setFormData] = useState<PumpTruckDispatchData>({
    // 基本信息
    dispatchNumber: '',
    pumpTruckNumber: '',
    status: '',
    taskNumber: task?.taskNumber || 'C125-00009',

    // 操作员信息
    operator1: '',
    operator2: '',
    operator3: '',
    dispatcher: '系统管理员',

    // 目的地信息
    destination: '去工地',

    // 工程信息
    constructionUnit: task?.constructionUnit || '长治亚夏建筑有限公司',
    constructionSite: task?.constructionSite || '长治亚夏建筑有限公司',
    projectName: task?.projectName || '个人自建',
    strengthGrade: task?.strength || 'C30细石',
    quantity: task?.requiredVolume || 12,
  });

  const updateField = (field: keyof PumpTruckDispatchData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleConfirm = () => {
    onConfirm(formData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl max-h-[90vh] overflow-y-auto p-0'>
        <div className='p-0 px-4 py-0.5 bg-blue-500 text-white rounded-t-lg'>
          <div className='flex items-center justify-between text-base font-semibold'>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-white rounded-full'></div>
              <Truck className='w-4 h-4' />
              <span>泵车发车单</span>
            </div>
          </div>
        </div>

        <div className='p-3 space-y-3'>
          {/* 第一行：单号、泵车号、状态 */}
          <div className='grid grid-cols-3 gap-3'>
            <div>
              <Label className='text-sm text-gray-700'>单号：</Label>
              <Input
                value={formData.dispatchNumber}
                onChange={e => updateField('dispatchNumber', e.target.value)}
                className='h-8 text-sm'
                placeholder='请输入单号'
              />
            </div>
            <div>
              <Label className='text-sm text-gray-700'>状态：</Label>
              <Input
                value={formData.status}
                onChange={e => updateField('status', e.target.value)}
                className='h-8 text-sm'
                placeholder='请输入状态'
              />
            </div>
          </div>

          {/* 第二行：泵车号、任务编号 */}
          <div className='grid grid-cols-2 gap-3'>
            <div>
              <Label className='text-sm text-gray-700'>泵车号：</Label>
              <Select
                value={formData.pumpTruckNumber}
                onValueChange={v => updateField('pumpTruckNumber', v)}
              >
                <SelectTrigger className='h-8 text-sm'>
                  <SelectValue placeholder='请选择泵车号' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='泵车001'>泵车001</SelectItem>
                  <SelectItem value='泵车002'>泵车002</SelectItem>
                  <SelectItem value='泵车003'>泵车003</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className='text-sm text-gray-700'>任务编号：</Label>
              <Select value={formData.taskNumber} onValueChange={v => updateField('taskNumber', v)}>
                <SelectTrigger className='h-8 text-sm'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='C125-00009'>C125-00009</SelectItem>
                  <SelectItem value='C125-00010'>C125-00010</SelectItem>
                  <SelectItem value='C125-00011'>C125-00011</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 第三行：建设单位、任务单位 */}
          <div className='grid grid-cols-2 gap-3'>
            <div>
              <Label className='text-sm text-gray-700'>建设单位：</Label>
              <Input
                value={formData.constructionUnit}
                onChange={e => updateField('constructionUnit', e.target.value)}
                className='h-8 text-sm'
                readOnly
              />
            </div>
            <div>
              <Label className='text-sm text-gray-700'>施工单位：</Label>
              <Input
                value={formData.constructionSite}
                onChange={e => updateField('constructionSite', e.target.value)}
                className='h-8 text-sm'
                readOnly
              />
            </div>
          </div>

          {/* 第四行：工程名称、施工部位 */}
          <div className='grid grid-cols-2 gap-3'>
            <div>
              <Label className='text-sm text-gray-700'>工程名称：</Label>
              <Input
                value={formData.projectName}
                onChange={e => updateField('projectName', e.target.value)}
                className='h-8 text-sm'
                readOnly
              />
            </div>
            <div>
              <Label className='text-sm text-gray-700'>施工部位：</Label>
              <Input value='潍坊18-28号楼基础基础垫层' className='h-8 text-sm' readOnly />
            </div>
          </div>

          {/* 第五行：强度等级、数量 */}
          <div className='grid grid-cols-2 gap-3'>
            <div>
              <Label className='text-sm text-gray-700'>强度等级：</Label>
              <Input
                value={formData.strengthGrade}
                onChange={e => updateField('strengthGrade', e.target.value)}
                className='h-8 text-sm'
                readOnly
              />
            </div>
            <div>
              <Label className='text-sm text-gray-700'>数量：</Label>
              <Input
                type='number'
                value={formData.quantity}
                onChange={e => updateField('quantity', Number(e.target.value))}
                className='h-8 text-sm'
              />
            </div>
          </div>

          {/* 操作员信息 */}
          <div className='grid grid-cols-4 gap-3'>
            <div>
              <Label className='text-sm text-gray-700'>司机：</Label>
              <Select value={formData.operator1} onValueChange={v => updateField('operator1', v)}>
                <SelectTrigger className='h-8 text-sm'>
                  <SelectValue placeholder='请选择' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='司机A'>司机A</SelectItem>
                  <SelectItem value='司机B'>司机B</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className='text-sm text-gray-700'>操作员1：</Label>
              <Select value={formData.operator2} onValueChange={v => updateField('operator2', v)}>
                <SelectTrigger className='h-8 text-sm'>
                  <SelectValue placeholder='请选择' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='操作员1'>操作员1</SelectItem>
                  <SelectItem value='操作员2'>操作员2</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className='text-sm text-gray-700'>操作员2：</Label>
              <Select value={formData.operator3} onValueChange={v => updateField('operator3', v)}>
                <SelectTrigger className='h-8 text-sm'>
                  <SelectValue placeholder='请选择' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='操作员3'>操作员3</SelectItem>
                  <SelectItem value='操作员4'>操作员4</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className='text-sm text-gray-700'>操作员3：</Label>
              <Select value={formData.operator3} onValueChange={v => updateField('operator3', v)}>
                <SelectTrigger className='h-8 text-sm'>
                  <SelectValue placeholder='请选择' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='操作员5'>操作员5</SelectItem>
                  <SelectItem value='操作员6'>操作员6</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 派单人和出车目的 */}
          <div className='grid grid-cols-2 gap-3'>
            <div>
              <Label className='text-sm text-gray-700'>派单人：</Label>
              <Input
                value={formData.dispatcher}
                onChange={e => updateField('dispatcher', e.target.value)}
                className='h-8 text-sm'
                readOnly
              />
            </div>
            <div>
              <Label className='text-sm text-gray-700'>出车目的：</Label>
              <Select
                value={formData.destination}
                onValueChange={v => updateField('destination', v)}
              >
                <SelectTrigger className='h-8 text-sm'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='去工地'>去工地</SelectItem>
                  <SelectItem value='返回站点'>返回站点</SelectItem>
                  <SelectItem value='维修保养'>维修保养</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className='flex justify-center gap-3 px-3 py-3 border-t bg-gray-50'>
          <Button
            onClick={handleConfirm}
            className='flex items-center gap-1 h-8 px-6 text-sm bg-blue-600 hover:bg-blue-700'
          >
            <Save className='w-4 h-4' />
            保存
          </Button>
          <Button
            variant='outline'
            onClick={handleCancel}
            className='flex items-center gap-1 h-8 px-6 text-sm'
          >
            <X className='w-4 h-4' />
            退出
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
