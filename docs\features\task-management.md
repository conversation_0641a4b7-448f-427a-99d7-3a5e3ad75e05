# 📋 任务管理模块

## 🎯 模块概述

任务管理模块是TMH车辆调度系统的核心功能，负责混凝土生产任务的创建、管理、调度和监控。

## ✨ 核心功能

### 📝 任务创建与编辑
- **快速创建**: 支持模板化快速创建任务
- **详细编辑**: 完整的任务信息编辑功能
- **批量操作**: 支持批量创建和编辑任务
- **数据验证**: 实时数据验证和错误提示

### 📊 任务列表管理
- **多视图模式**: 支持表格视图和卡片视图
- **智能筛选**: 多维度筛选和搜索功能
- **灵活排序**: 支持多字段排序
- **分组显示**: 按状态、优先级等分组

### 🚛 车辆调度集成
- **拖拽调度**: 直观的拖拽式车辆调度
- **智能匹配**: 基于车辆类型和任务需求的智能匹配
- **实时状态**: 车辆和任务状态实时同步
- **冲突检测**: 自动检测和解决调度冲突

## 🏗️ 技术架构

### 组件结构
```
src/components/task-list/
├── TaskListContainer.tsx          # 主容器组件
├── components/
│   ├── task-list-card-content.tsx    # 卡片视图内容
│   ├── task-list-table-content.tsx   # 表格视图内容
│   ├── task-group-header.tsx         # 分组头部
│   └── floating-task-list-header.tsx # 浮动头部
├── cards/
│   ├── ConfigurableTaskCard.tsx      # 可配置任务卡片
│   ├── TaskFieldRenderer.tsx         # 字段渲染器
│   └── ProductionLinePanel.tsx       # 生产线面板
├── cells/
│   ├── TaskProgressCell.tsx          # 进度单元格
│   ├── MessageCell.tsx               # 消息单元格
│   └── DispatchedVehiclesCell.tsx    # 已调度车辆单元格
└── modals/
    ├── TaskProgressModal.tsx         # 任务进度模态框
    ├── TaskMessageModal.tsx          # 任务消息模态框
    └── VehicleDispatchModal.tsx      # 车辆调度模态框
```

### 状态管理
```typescript
// 任务列表状态
interface TaskListState {
  // 数据状态
  tasks: Task[];
  filteredTasks: Task[];
  selectedTasks: string[];
  
  // UI状态
  displayMode: 'table' | 'card';
  loading: boolean;
  error: string | null;
  
  // 筛选和排序
  filters: TaskFilters;
  sortConfig: SortConfig;
  groupConfig: GroupConfig;
  
  // 操作方法
  fetchTasks: () => Promise<void>;
  createTask: (task: CreateTaskData) => Promise<void>;
  updateTask: (id: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
  
  // UI操作
  setDisplayMode: (mode: 'table' | 'card') => void;
  selectTask: (taskId: string) => void;
  selectMultipleTasks: (taskIds: string[]) => void;
  clearSelection: () => void;
}
```

## 🎨 用户界面

### 任务列表视图

#### 表格视图
- **紧凑显示**: 在有限空间内显示更多任务
- **列自定义**: 用户可自定义显示列和顺序
- **内联编辑**: 支持单元格内联编辑
- **批量选择**: 支持多选和批量操作

```typescript
// 表格列配置示例
const tableColumns = [
  { key: 'taskNumber', label: '任务编号', width: 120, sortable: true },
  { key: 'projectName', label: '项目名称', width: 200, sortable: true },
  { key: 'strength', label: '强度等级', width: 100, sortable: true },
  { key: 'quantity', label: '方量', width: 80, sortable: true },
  { key: 'status', label: '状态', width: 100, filterable: true },
  { key: 'priority', label: '优先级', width: 100, filterable: true },
  { key: 'dueDate', label: '交付时间', width: 150, sortable: true },
];
```

#### 卡片视图
- **信息丰富**: 显示更多任务详细信息
- **视觉直观**: 通过颜色和图标快速识别状态
- **拖拽友好**: 更适合拖拽操作
- **响应式布局**: 自适应不同屏幕尺寸

```typescript
// 卡片配置示例
const cardConfig = {
  layout: 'compact',
  showVehicles: true,
  showProgress: true,
  fieldsPerRow: 2,
  maxVehicleRows: 2,
  enableDragDrop: true,
};
```

### 任务详情

#### 基本信息
- **任务编号**: 系统自动生成的唯一标识
- **项目信息**: 项目名称、地址、联系人
- **混凝土规格**: 强度等级、坍落度、方量
- **时间要求**: 开始时间、交付时间、持续时间

#### 生产信息
- **配比方案**: 关联的混凝土配比
- **生产线**: 指定的生产线
- **质量要求**: 特殊质量要求和检测标准
- **备注信息**: 额外的生产注意事项

#### 调度信息
- **已调度车辆**: 当前分配的车辆列表
- **调度历史**: 车辆调度变更记录
- **运输状态**: 车辆运输实时状态
- **异常记录**: 调度异常和处理记录

## 🔧 核心功能实现

### 任务筛选
```typescript
interface TaskFilters {
  status?: TaskStatus[];
  priority?: TaskPriority[];
  projectName?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  strength?: string[];
  assignedVehicles?: boolean;
}

// 筛选逻辑实现
function filterTasks(tasks: Task[], filters: TaskFilters): Task[] {
  return tasks.filter(task => {
    if (filters.status && !filters.status.includes(task.status)) {
      return false;
    }
    
    if (filters.priority && !filters.priority.includes(task.priority)) {
      return false;
    }
    
    if (filters.projectName && !task.projectName.includes(filters.projectName)) {
      return false;
    }
    
    if (filters.dateRange) {
      const taskDate = new Date(task.dueDate);
      if (taskDate < filters.dateRange.start || taskDate > filters.dateRange.end) {
        return false;
      }
    }
    
    return true;
  });
}
```

### 任务分组
```typescript
interface GroupConfig {
  enabled: boolean;
  groupBy: TaskColumnId | 'none';
  sortOrder: 'asc' | 'desc';
  collapsible: boolean;
  defaultCollapsed: string[];
  showGroupStats: boolean;
}

// 分组逻辑实现
function groupTasks(tasks: Task[], groupConfig: GroupConfig): TaskGroup[] {
  if (!groupConfig.enabled || groupConfig.groupBy === 'none') {
    return [{ key: 'all', label: '全部任务', tasks, count: tasks.length }];
  }
  
  const groups = tasks.reduce((acc, task) => {
    const groupKey = task[groupConfig.groupBy] || 'unknown';
    if (!acc[groupKey]) {
      acc[groupKey] = [];
    }
    acc[groupKey].push(task);
    return acc;
  }, {} as Record<string, Task[]>);
  
  return Object.entries(groups).map(([key, tasks]) => ({
    key,
    label: getGroupLabel(key, groupConfig.groupBy),
    tasks,
    count: tasks.length,
    collapsed: groupConfig.defaultCollapsed.includes(key),
  }));
}
```

### 拖拽调度
```typescript
// 拖拽处理逻辑
function handleVehicleDrop(vehicleId: string, taskId: string) {
  const vehicle = getVehicleById(vehicleId);
  const task = getTaskById(taskId);
  
  // 验证调度可行性
  if (!canDispatchVehicle(vehicle, task)) {
    showError('车辆不符合任务要求');
    return;
  }
  
  // 检查冲突
  const conflicts = checkDispatchConflicts(vehicle, task);
  if (conflicts.length > 0) {
    showConflictDialog(conflicts);
    return;
  }
  
  // 执行调度
  dispatchVehicleToTask(vehicleId, taskId);
  
  // 更新状态
  updateTaskStatus(taskId, 'in-progress');
  updateVehicleStatus(vehicleId, 'dispatched');
  
  // 记录日志
  logDispatchAction(vehicleId, taskId, 'dispatch');
}
```

## 📊 数据模型

### 任务数据结构
```typescript
interface Task {
  // 基本信息
  id: string;
  taskNumber: string;
  projectName: string;
  customerName: string;
  deliveryAddress: string;
  
  // 混凝土规格
  strength: string;
  slump: number;
  quantity: number;
  additives?: string[];
  
  // 时间信息
  createdAt: Date;
  scheduledStartTime: Date;
  dueDate: Date;
  estimatedDuration: number;
  
  // 状态信息
  status: TaskStatus;
  priority: TaskPriority;
  progress: number;
  
  // 生产信息
  productionLine?: string;
  ratioId?: string;
  qualityRequirements?: string[];
  
  // 调度信息
  assignedVehicles: string[];
  dispatchHistory: DispatchRecord[];
  
  // 其他信息
  notes?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

type TaskStatus = 'pending' | 'in-progress' | 'completed' | 'cancelled' | 'on-hold';
type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';
```

### 调度记录
```typescript
interface DispatchRecord {
  id: string;
  taskId: string;
  vehicleId: string;
  action: 'dispatch' | 'recall' | 'reassign';
  timestamp: Date;
  operator: string;
  reason?: string;
  metadata?: Record<string, any>;
}
```

## 🎛️ 配置选项

### 显示配置
```typescript
interface TaskListDisplayConfig {
  // 视图模式
  defaultDisplayMode: 'table' | 'card';
  allowModeSwitch: boolean;
  
  // 表格配置
  tableConfig: {
    defaultPageSize: number;
    allowColumnResize: boolean;
    allowColumnReorder: boolean;
    stickyHeader: boolean;
  };
  
  // 卡片配置
  cardConfig: {
    cardsPerRow: number;
    showVehicleArea: boolean;
    maxVehicleRows: number;
    enableDragDrop: boolean;
  };
  
  // 分组配置
  groupConfig: {
    enabled: boolean;
    defaultGroupBy: TaskColumnId;
    allowGroupChange: boolean;
    showGroupStats: boolean;
  };
}
```

### 功能配置
```typescript
interface TaskListFeatureConfig {
  // 操作权限
  permissions: {
    canCreate: boolean;
    canEdit: boolean;
    canDelete: boolean;
    canDispatch: boolean;
    canBulkEdit: boolean;
  };
  
  // 功能开关
  features: {
    enableDragDrop: boolean;
    enableBulkOperations: boolean;
    enableRealTimeUpdates: boolean;
    enableNotifications: boolean;
  };
  
  // 性能配置
  performance: {
    virtualScrolling: boolean;
    lazyLoading: boolean;
    cacheSize: number;
    refreshInterval: number;
  };
}
```

## 🔌 API 集成

### 任务 API
```typescript
// 获取任务列表
GET /api/tasks?page=1&limit=50&status=pending&sort=dueDate

// 创建任务
POST /api/tasks
{
  "projectName": "示例项目",
  "strength": "C30",
  "quantity": 100,
  "dueDate": "2024-01-15T10:00:00Z"
}

// 更新任务
PUT /api/tasks/:id
{
  "status": "in-progress",
  "assignedVehicles": ["vehicle-1", "vehicle-2"]
}

// 删除任务
DELETE /api/tasks/:id

// 批量操作
POST /api/tasks/bulk
{
  "action": "update",
  "taskIds": ["task-1", "task-2"],
  "updates": { "priority": "high" }
}
```

### 调度 API
```typescript
// 车辆调度
POST /api/dispatch
{
  "vehicleId": "vehicle-1",
  "taskId": "task-1",
  "scheduledTime": "2024-01-15T08:00:00Z"
}

// 取消调度
DELETE /api/dispatch/:dispatchId

// 调度历史
GET /api/tasks/:id/dispatch-history
```

## 🧪 测试策略

### 单元测试
- 组件渲染测试
- 状态管理测试
- 工具函数测试
- Hook 功能测试

### 集成测试
- 任务 CRUD 操作
- 筛选和排序功能
- 拖拽调度流程
- API 集成测试

### E2E 测试
- 完整的任务管理流程
- 用户交互场景
- 跨浏览器兼容性
- 性能基准测试

---

**文档版本**: v1.0  
**最后更新**: 2025/7/16  
**负责人**: 任务管理团队
