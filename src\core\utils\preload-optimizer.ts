/**
 * 预加载策略优化器
 * 基于用户行为和网络状况智能调整预加载策略
 */

// ==================== 网络状况检测 ====================

export interface NetworkInfo {
  effectiveType: '2g' | '3g' | '4g' | 'slow-2g';
  downlink: number;
  rtt: number;
  saveData: boolean;
}

export function getNetworkInfo(): NetworkInfo | null {
  if (typeof navigator === 'undefined' || !('connection' in navigator)) {
    return null;
  }

  const connection = (navigator as any).connection;
  return {
    effectiveType: connection.effectiveType || '4g',
    downlink: connection.downlink || 10,
    rtt: connection.rtt || 100,
    saveData: connection.saveData || false,
  };
}

// ==================== 用户行为分析 ====================

export class UserBehaviorAnalyzer {
  private static visitHistory: string[] = [];
  private static interactionPatterns: Map<string, number> = new Map();
  private static sessionStartTime = Date.now();

  /**
   * 记录页面访问
   */
  static recordPageVisit(path: string) {
    this.visitHistory.push(path);

    // 只保留最近50次访问记录
    if (this.visitHistory.length > 50) {
      this.visitHistory = this.visitHistory.slice(-50);
    }

    // 更新交互模式
    this.updateInteractionPattern(path);
  }

  /**
   * 更新交互模式
   */
  private static updateInteractionPattern(path: string) {
    const current = this.interactionPatterns.get(path) || 0;
    this.interactionPatterns.set(path, current + 1);
  }

  /**
   * 预测下一个可能访问的页面
   */
  static predictNextPages(currentPath: string): string[] {
    const patterns = this.getNavigationPatterns();
    const predictions = patterns.get(currentPath) || [];

    // 按访问频率排序
    return predictions
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 3)
      .map(p => p.path);
  }

  /**
   * 获取导航模式
   */
  private static getNavigationPatterns(): Map<string, Array<{ path: string; frequency: number }>> {
    const patterns = new Map();

    for (let i = 0; i < this.visitHistory.length - 1; i++) {
      const current = this.visitHistory[i];
      const next = this.visitHistory[i + 1];

      if (!patterns.has(current)) {
        patterns.set(current, []);
      }

      const nextPages = patterns.get(current);
      const existing = nextPages.find((p: any) => p.path === next);

      if (existing) {
        existing.frequency++;
      } else {
        nextPages.push({ path: next, frequency: 1 });
      }
    }

    return patterns;
  }

  /**
   * 获取用户活跃度
   */
  static getUserActivity(): 'high' | 'medium' | 'low' {
    const sessionDuration = Date.now() - this.sessionStartTime;
    const pagesPerMinute = this.visitHistory.length / (sessionDuration / 60000);

    if (pagesPerMinute > 2) return 'high';
    if (pagesPerMinute > 0.5) return 'medium';
    return 'low';
  }

  /**
   * 清理历史数据
   */
  static clearHistory() {
    this.visitHistory = [];
    this.interactionPatterns.clear();
    this.sessionStartTime = Date.now();
  }
}

// ==================== 预加载策略优化器 ====================

export class PreloadOptimizer {
  private static strategies = new Map<string, PreloadStrategy>();

  /**
   * 获取优化的预加载策略
   */
  static getOptimizedStrategy(moduleName: string, currentPath: string): PreloadStrategy {
    const networkInfo = getNetworkInfo();
    const userActivity = UserBehaviorAnalyzer.getUserActivity();
    const predictedPages = UserBehaviorAnalyzer.predictNextPages(currentPath);

    // 基础策略
    let strategy: PreloadStrategy = {
      enabled: true,
      priority: 'medium',
      timing: 'idle',
      maxConcurrent: 2,
    };

    // 网络状况调整
    if (networkInfo) {
      if (
        networkInfo.saveData ||
        networkInfo.effectiveType === 'slow-2g' ||
        networkInfo.effectiveType === '2g'
      ) {
        strategy = {
          enabled: false,
          priority: 'low',
          timing: 'manual',
          maxConcurrent: 1,
        };
      } else if (networkInfo.effectiveType === '3g') {
        strategy.maxConcurrent = 1;
        strategy.timing = 'hover';
      } else if (networkInfo.effectiveType === '4g' && networkInfo.downlink > 5) {
        strategy.maxConcurrent = 3;
        strategy.timing = 'immediate';
      }
    }

    // 用户行为调整
    if (userActivity === 'high') {
      strategy.priority = 'high';
      strategy.maxConcurrent = Math.min(strategy.maxConcurrent + 1, 4);
    } else if (userActivity === 'low') {
      strategy.priority = 'low';
      strategy.timing = 'hover';
    }

    // 预测页面调整
    if (predictedPages.includes(moduleName)) {
      strategy.priority = 'high';
      strategy.timing = 'immediate';
    }

    this.strategies.set(moduleName, strategy);
    return strategy;
  }

  /**
   * 动态调整策略
   */
  static adjustStrategy(moduleName: string, loadTime: number, success: boolean) {
    const strategy = this.strategies.get(moduleName);
    if (!strategy) return;

    if (!success) {
      // 加载失败，降低优先级
      strategy.priority = 'low';
      strategy.maxConcurrent = Math.max(strategy.maxConcurrent - 1, 1);
    } else if (loadTime > 3000) {
      // 加载时间过长，调整策略
      strategy.timing = 'hover';
      strategy.maxConcurrent = Math.max(strategy.maxConcurrent - 1, 1);
    } else if (loadTime < 500) {
      // 加载很快，可以更积极
      strategy.maxConcurrent = Math.min(strategy.maxConcurrent + 1, 4);
    }

    this.strategies.set(moduleName, strategy);
  }

  /**
   * 获取所有策略
   */
  static getAllStrategies(): Map<string, PreloadStrategy> {
    return new Map(this.strategies);
  }

  /**
   * 重置策略
   */
  static resetStrategies() {
    this.strategies.clear();
  }
}

// ==================== 预加载策略类型 ====================

export interface PreloadStrategy {
  enabled: boolean;
  priority: 'high' | 'medium' | 'low';
  timing: 'immediate' | 'idle' | 'hover' | 'manual';
  maxConcurrent: number;
}

// ==================== 智能预加载管理器 ====================

export class SmartPreloadManager {
  private static loadQueue: Array<{
    moduleName: string;
    importFn: () => Promise<any>;
    strategy: PreloadStrategy;
  }> = [];

  private static activeLoads = new Set<string>();
  private static loadResults = new Map<string, { success: boolean; loadTime: number }>();

  /**
   * 添加预加载任务
   */
  static addPreloadTask(moduleName: string, importFn: () => Promise<any>, currentPath: string) {
    if (this.activeLoads.has(moduleName)) return;

    const strategy = PreloadOptimizer.getOptimizedStrategy(moduleName, currentPath);

    if (!strategy.enabled) return;

    const task = { moduleName, importFn, strategy };

    // 按优先级插入队列
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    const insertIndex = this.loadQueue.findIndex(
      t => priorityOrder[t.strategy.priority] > priorityOrder[strategy.priority]
    );

    if (insertIndex === -1) {
      this.loadQueue.push(task);
    } else {
      this.loadQueue.splice(insertIndex, 0, task);
    }

    this.processQueue();
  }

  /**
   * 处理预加载队列
   */
  private static async processQueue() {
    const concurrentLimit = this.getCurrentConcurrentLimit();

    while (this.activeLoads.size < concurrentLimit && this.loadQueue.length > 0) {
      const task = this.loadQueue.shift();
      if (!task) break;

      this.executePreload(task);
    }
  }

  /**
   * 执行预加载
   */
  private static async executePreload(task: {
    moduleName: string;
    importFn: () => Promise<any>;
    strategy: PreloadStrategy;
  }) {
    const { moduleName, importFn, strategy } = task;

    this.activeLoads.add(moduleName);
    const startTime = performance.now();

    try {
      // 根据策略决定何时开始加载
      if (strategy.timing === 'idle') {
        await this.waitForIdle();
      } else if (strategy.timing === 'hover') {
        // hover 策略在实际悬停时才执行，这里跳过
        this.activeLoads.delete(moduleName);
        return;
      }

      await importFn();

      const loadTime = performance.now() - startTime;
      this.loadResults.set(moduleName, { success: true, loadTime });

      // 调整策略
      PreloadOptimizer.adjustStrategy(moduleName, loadTime, true);

      console.log(`⚡ 智能预加载完成: ${moduleName} - ${loadTime.toFixed(2)}ms`);
    } catch (error) {
      const loadTime = performance.now() - startTime;
      this.loadResults.set(moduleName, { success: false, loadTime });

      // 调整策略
      PreloadOptimizer.adjustStrategy(moduleName, loadTime, false);

      console.warn(`❌ 智能预加载失败: ${moduleName}`, error);
    } finally {
      this.activeLoads.delete(moduleName);

      // 继续处理队列
      setTimeout(() => this.processQueue(), 100);
    }
  }

  /**
   * 等待空闲时间
   */
  private static waitForIdle(): Promise<void> {
    return new Promise(resolve => {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => resolve());
      } else {
        setTimeout(() => resolve(), 100);
      }
    });
  }

  /**
   * 获取当前并发限制
   */
  private static getCurrentConcurrentLimit(): number {
    const networkInfo = getNetworkInfo();

    if (!networkInfo) return 2;

    if (networkInfo.saveData || networkInfo.effectiveType === 'slow-2g') return 1;
    if (networkInfo.effectiveType === '2g') return 1;
    if (networkInfo.effectiveType === '3g') return 2;
    return 3;
  }

  /**
   * 获取预加载统计
   */
  static getStats() {
    const results = Array.from(this.loadResults.values());
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const avgLoadTime =
      results.length > 0 ? results.reduce((sum, r) => sum + r.loadTime, 0) / results.length : 0;

    return {
      total: results.length,
      successful,
      failed,
      successRate:
        results.length > 0 ? ((successful / results.length) * 100).toFixed(2) + '%' : '0%',
      averageLoadTime: avgLoadTime.toFixed(2) + 'ms',
      activeLoads: this.activeLoads.size,
      queueLength: this.loadQueue.length,
    };
  }

  /**
   * 清理数据
   */
  static cleanup() {
    this.loadQueue = [];
    this.activeLoads.clear();
    this.loadResults.clear();
  }
}

// ==================== 导出 ====================

export default {
  UserBehaviorAnalyzer,
  PreloadOptimizer,
  SmartPreloadManager,
  getNetworkInfo,
};
