'use client';

import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';

interface ConfirmationModalProps {
  open?: boolean;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onOpenChangeAction?: (open: boolean) => void;
  title?: string;
  message?: string;
  onConfirm?: () => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  open = false,
  isOpen = false,
  onOpenChange,
  onOpenChangeAction,
  title = '确认',
  message = '确定要执行此操作吗？',
  onConfirm,
}) => {
  // 统一处理open状态和回调
  const modalOpen = (open || isOpen) ?? false;
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange?.(newOpen);
    onOpenChangeAction?.(newOpen);
  };

  return (
    <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className='p-4'>
          <p>{message}</p>
          <div className='flex justify-end space-x-2 mt-4'>
            <button onClick={() => onOpenChange?.(false)}>取消</button>
            <button onClick={onConfirm}>确认</button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmationModal;
