/**
 * Bundle Optimization Utilities
 * 包体积优化相关工具函数
 */

/**
 * 动态导入工具函数
 * 提供统一的动态导入接口和错误处理
 */
export class DynamicImportManager {
  private static cache = new Map<string, Promise<any>>();

  /**
   * 带缓存的动态导入
   */
  static async importWithCache<T>(
    importFn: () => Promise<{ default: T }>,
    cacheKey: string
  ): Promise<T> {
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const promise = importFn().then(module => module.default);
    this.cache.set(cacheKey, promise);

    try {
      return await promise;
    } catch (error) {
      // 导入失败时清除缓存
      this.cache.delete(cacheKey);
      throw error;
    }
  }

  /**
   * 预加载组件
   */
  static preload(importFn: () => Promise<any>, cacheKey: string): void {
    if (!this.cache.has(cacheKey)) {
      this.cache.set(
        cacheKey,
        importFn().then(module => module.default)
      );
    }
  }

  /**
   * 清除缓存
   */
  static clearCache(cacheKey?: string): void {
    if (cacheKey) {
      this.cache.delete(cacheKey);
    } else {
      this.cache.clear();
    }
  }
}

/**
 * 包体积监控工具
 */
export class BundleSizeMonitor {
  private static readonly STORAGE_KEY = 'bundle-size-metrics';

  /**
   * 记录包加载时间
   */
  static recordLoadTime(chunkName: string, startTime: number): void {
    if (typeof window === 'undefined') return;

    const loadTime = performance.now() - startTime;
    const metrics = this.getMetrics();

    metrics[chunkName] = {
      loadTime,
      timestamp: Date.now(),
    };

    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(metrics));

    if (process.env.NODE_ENV === 'development') {
      console.log(`📦 Chunk "${chunkName}" loaded in ${loadTime.toFixed(2)}ms`);
    }
  }

  /**
   * 获取加载指标
   */
  static getMetrics(): Record<string, { loadTime: number; timestamp: number }> {
    if (typeof window === 'undefined') return {};

    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  }

  /**
   * 分析包体积性能
   */
  static analyzePerformance(): {
    slowChunks: string[];
    averageLoadTime: number;
    totalChunks: number;
  } {
    const metrics = this.getMetrics();
    const loadTimes = Object.values(metrics).map(m => m.loadTime);

    if (loadTimes.length === 0) {
      return { slowChunks: [], averageLoadTime: 0, totalChunks: 0 };
    }

    const averageLoadTime = loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length;
    const slowThreshold = averageLoadTime * 1.5;

    const slowChunks = Object.entries(metrics)
      .filter(([, metric]) => metric.loadTime > slowThreshold)
      .map(([chunkName]) => chunkName);

    return {
      slowChunks,
      averageLoadTime,
      totalChunks: loadTimes.length,
    };
  }
}

/**
 * 条件导入工具
 * 根据条件决定是否导入某个模块
 */
export class ConditionalImporter {
  /**
   * 基于特性检测的条件导入
   */
  static async importIfSupported<T>(
    importFn: () => Promise<{ default: T }>,
    featureCheck: () => boolean,
    fallback?: T
  ): Promise<T | undefined> {
    if (!featureCheck()) {
      return fallback;
    }

    try {
      const module = await importFn();
      return module.default;
    } catch (error) {
      console.warn('Conditional import failed:', error);
      return fallback;
    }
  }

  /**
   * 基于环境的条件导入
   */
  static async importIfDev<T>(importFn: () => Promise<{ default: T }>): Promise<T | null> {
    if (process.env.NODE_ENV !== 'development') {
      return null;
    }

    try {
      const module = await importFn();
      return module.default;
    } catch (error) {
      console.warn('Development import failed:', error);
      return null;
    }
  }

  /**
   * 基于用户代理的条件导入
   */
  static async importIfMobile<T>(
    mobileImportFn: () => Promise<{ default: T }>,
    desktopImportFn: () => Promise<{ default: T }>
  ): Promise<T> {
    const isMobile =
      typeof window !== 'undefined' &&
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    const importFn = isMobile ? mobileImportFn : desktopImportFn;
    const module = await importFn();
    return module.default;
  }
}

/**
 * 资源预加载管理器
 */
export class ResourcePreloader {
  private static preloadedResources = new Set<string>();

  /**
   * 预加载关键资源
   */
  static preloadCriticalResources(): void {
    if (typeof window === 'undefined') return;

    /**
     * 预加载关键懒加载组件的资源列表
     * 包含需要优先加载的懒加载组件，用于优化首屏性能
     */
    const criticalResources = [
      // 预加载关键的懒加载组件
      () => import('@/shared/components/lazy/LazyChart'),
      () => import('@/shared/components/lazy/LazyQRCodeModal'),
    ];

    criticalResources.forEach((importFn, index) => {
      const resourceKey = `critical-${index}`;
      if (!this.preloadedResources.has(resourceKey)) {
        importFn().catch(error => {
          console.warn(`Failed to preload critical resource ${resourceKey}:`, error);
        });
        this.preloadedResources.add(resourceKey);
      }
    });
  }

  /**
   * 智能预加载
   * 基于用户行为预测需要的资源
   */
  static smartPreload(userAction: string): void {
    const preloadMap: Record<string, () => Promise<any>> = {
      'hover-chart': () => import('@/shared/components/lazy/LazyChart'),
      'hover-qr': () => import('@/shared/components/lazy/LazyQRCodeModal'),
      'hover-config': () => import('@/shared/components/lazy/LazyConfigManagement'),
      'hover-ai': () => import('@/shared/components/lazy/LazyAIComponents'),
    };

    const importFn = preloadMap[userAction];
    if (importFn && !this.preloadedResources.has(userAction)) {
      importFn().catch(error => {
        console.warn(`Smart preload failed for ${userAction}:`, error);
      });
      this.preloadedResources.add(userAction);
    }
  }
}

/**
 * 包体积优化建议生成器
 */
export class OptimizationSuggester {
  /**
   * 分析并生成优化建议
   */
  static generateSuggestions(): {
    suggestions: string[];
    priority: 'high' | 'medium' | 'low';
    estimatedSavings: string;
  }[] {
    const suggestions = [];
    const performance = BundleSizeMonitor.analyzePerformance();

    if (performance.slowChunks.length > 0) {
      suggestions.push({
        suggestions: [
          `发现 ${performance.slowChunks.length} 个加载较慢的代码块`,
          '建议进一步拆分这些代码块或优化其内容',
          `慢速代码块: ${performance.slowChunks.join(', ')}`,
        ],
        priority: 'high' as const,
        estimatedSavings: '10-20% 加载时间减少',
      });
    }

    if (performance.averageLoadTime > 1000) {
      suggestions.push({
        suggestions: [
          '平均代码块加载时间超过1秒',
          '建议启用更多的代码分割和预加载策略',
          '考虑使用 Service Worker 缓存策略',
        ],
        priority: 'high' as const,
        estimatedSavings: '15-30% 加载时间减少',
      });
    }

    return suggestions;
  }
}

// 导出所有工具类
export {
  DynamicImportManager as DynamicImport,
  BundleSizeMonitor as BundleMonitor,
  ConditionalImporter as ConditionalImport,
  ResourcePreloader as Preloader,
  OptimizationSuggester as OptimizationSuggest,
};
