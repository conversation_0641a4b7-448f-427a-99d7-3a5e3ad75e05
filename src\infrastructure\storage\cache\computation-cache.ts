/**
 * 计算缓存系统
 * 专门针对重复计算场景的高性能缓存解决方案
 */

import { globalCache } from './performance-cache';

// ==================== 计算缓存配置 ====================

export interface ComputationCacheConfig {
  enabled: boolean;
  maxCacheSize: number;
  defaultTTL: number;
  enableMetrics: boolean;
  enableDebug: boolean;
}

const DEFAULT_CONFIG: ComputationCacheConfig = {
  enabled: true,
  maxCacheSize: 1000,
  defaultTTL: 10 * 60 * 1000, // 10分钟
  enableMetrics: true,
  enableDebug: process.env.NODE_ENV === 'development',
};

// ==================== 计算缓存管理器 ====================

export class ComputationCache {
  private config: ComputationCacheConfig;
  private metrics: Map<string, { hits: number; misses: number; computeTime: number }>;

  constructor(config: Partial<ComputationCacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.metrics = new Map();
  }

  /**
   * 缓存计算结果
   */
  async computeWithCache<T>(
    key: string,
    computeFn: () => T | Promise<T>,
    options: { ttl?: number; forceRefresh?: boolean } = {}
  ): Promise<T> {
    const { ttl = this.config.defaultTTL, forceRefresh = false } = options;

    if (!this.config.enabled || forceRefresh) {
      return this.executeComputation(key, computeFn);
    }

    // 尝试从缓存获取
    const cached = globalCache.get<T>(key);
    if (cached !== null) {
      this.recordHit(key);
      if (this.config.enableDebug) {
        console.log(`💾 Cache HIT: ${key}`);
      }
      return cached;
    }

    // 缓存未命中，执行计算
    this.recordMiss(key);
    const result = await this.executeComputation(key, computeFn);

    // 缓存结果
    globalCache.set(key, result, { ttl });

    if (this.config.enableDebug) {
      console.log(`🔄 Cache MISS: ${key} - computed and cached`);
    }

    return result;
  }

  /**
   * 执行计算并记录性能指标
   */
  private async executeComputation<T>(key: string, computeFn: () => T | Promise<T>): Promise<T> {
    const startTime = performance.now();

    try {
      const result = await computeFn();
      const computeTime = performance.now() - startTime;

      this.recordComputeTime(key, computeTime);

      if (this.config.enableDebug && computeTime > 10) {
        console.warn(`⚠️ Slow computation: ${key} took ${computeTime.toFixed(2)}ms`);
      }

      return result;
    } catch (error) {
      console.error(`❌ Computation failed for ${key}:`, error);
      throw error;
    }
  }

  /**
   * 记录缓存命中
   */
  private recordHit(key: string): void {
    if (!this.config.enableMetrics) return;

    const metric = this.metrics.get(key) || { hits: 0, misses: 0, computeTime: 0 };
    metric.hits++;
    this.metrics.set(key, metric);
  }

  /**
   * 记录缓存未命中
   */
  private recordMiss(key: string): void {
    if (!this.config.enableMetrics) return;

    const metric = this.metrics.get(key) || { hits: 0, misses: 0, computeTime: 0 };
    metric.misses++;
    this.metrics.set(key, metric);
  }

  /**
   * 记录计算时间
   */
  private recordComputeTime(key: string, time: number): void {
    if (!this.config.enableMetrics) return;

    const metric = this.metrics.get(key) || { hits: 0, misses: 0, computeTime: 0 };
    metric.computeTime = (metric.computeTime + time) / 2; // 移动平均
    this.metrics.set(key, metric);
  }

  /**
   * 获取缓存统计信息
   */
  getMetrics() {
    const stats = Array.from(this.metrics.entries()).map(([key, metric]) => ({
      key,
      hitRate: (metric.hits / (metric.hits + metric.misses)) * 100,
      totalRequests: metric.hits + metric.misses,
      avgComputeTime: metric.computeTime,
      ...metric,
    }));

    return {
      totalKeys: stats.length,
      overallHitRate: stats.reduce((sum, s) => sum + s.hitRate, 0) / stats.length || 0,
      stats: stats.sort((a, b) => b.totalRequests - a.totalRequests),
    };
  }

  /**
   * 清理特定前缀的缓存
   */
  clearByPrefix(prefix: string): void {
    // 这里需要扩展 globalCache 来支持前缀清理
    console.log(`Clearing cache with prefix: ${prefix}`);
  }

  /**
   * 预热缓存
   */
  async warmup<T>(
    computations: Array<{ key: string; computeFn: () => T | Promise<T> }>
  ): Promise<void> {
    console.log(`🔥 Warming up ${computations.length} computations...`);

    const promises = computations.map(({ key, computeFn }) =>
      this.computeWithCache(key, computeFn).catch(error => {
        console.warn(`Failed to warmup ${key}:`, error);
      })
    );

    await Promise.all(promises);
    console.log('✅ Cache warmup completed');
  }
}

// ==================== 全局计算缓存实例 ====================

export const computationCache = new ComputationCache({
  enabled: true,
  maxCacheSize: 2000,
  defaultTTL: 15 * 60 * 1000, // 15分钟
  enableMetrics: true,
  enableDebug: process.env.NODE_ENV === 'development',
});

// ==================== 专用缓存函数 ====================

/**
 * 缓存数组处理结果
 */
export async function cacheArrayOperation<T, R>(
  key: string,
  array: T[],
  operation: (arr: T[]) => R | Promise<R>,
  options: { ttl?: number; keyGenerator?: (arr: T[]) => string } = {}
): Promise<R> {
  const { keyGenerator, ...cacheOptions } = options;
  const cacheKey = keyGenerator
    ? `${key}-${keyGenerator(array)}`
    : `${key}-${array.length}-${JSON.stringify(array).slice(0, 100)}`;

  return computationCache.computeWithCache(cacheKey, () => operation(array), cacheOptions);
}

/**
 * 缓存对象计算结果
 */
export async function cacheObjectComputation<T, R>(
  key: string,
  obj: T,
  computation: (obj: T) => R | Promise<R>,
  options: { ttl?: number; keyGenerator?: (obj: T) => string } = {}
): Promise<R> {
  const { keyGenerator, ...cacheOptions } = options;
  const cacheKey = keyGenerator ? `${key}-${keyGenerator(obj)}` : `${key}-${JSON.stringify(obj)}`;

  return computationCache.computeWithCache(cacheKey, () => computation(obj), cacheOptions);
}

/**
 * 缓存数值计算结果
 */
export async function cacheNumericComputation(
  key: string,
  params: Record<string, number>,
  computation: (params: Record<string, number>) => number | Promise<number>,
  options: { ttl?: number; precision?: number } = {}
): Promise<number> {
  const { precision = 2, ...cacheOptions } = options;
  const paramKey = Object.entries(params)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([k, v]) => `${k}:${v.toFixed(precision)}`)
    .join(',');

  const cacheKey = `${key}-${paramKey}`;

  return computationCache.computeWithCache(cacheKey, () => computation(params), cacheOptions);
}

// ==================== 装饰器 ====================

/**
 * 方法缓存装饰器
 */
export function CacheComputation(ttl = 10 * 60 * 1000, keyGenerator?: (...args: any[]) => string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const cacheKey = keyGenerator
        ? keyGenerator(...args)
        : `${target.constructor.name}.${propertyKey}.${JSON.stringify(args)}`;

      return computationCache.computeWithCache(cacheKey, () => originalMethod.apply(this, args), {
        ttl,
      });
    };

    return descriptor;
  };
}

// ==================== 缓存监控 ====================

/**
 * 缓存性能监控
 */
export function startCacheMonitoring(intervalMs = 30000): () => void {
  const interval = setInterval(() => {
    const metrics = computationCache.getMetrics();

    if (metrics.totalKeys > 0) {
      console.log(`📊 Cache Metrics:`, {
        totalKeys: metrics.totalKeys,
        hitRate: `${metrics.overallHitRate.toFixed(1)}%`,
        topKeys: metrics.stats.slice(0, 5).map(s => ({
          key: s.key.slice(0, 30),
          hitRate: `${s.hitRate.toFixed(1)}%`,
          requests: s.totalRequests,
        })),
      });
    }
  }, intervalMs);

  return () => clearInterval(interval);
}
