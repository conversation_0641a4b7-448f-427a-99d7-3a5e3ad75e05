'use client';

import React, { useState } from 'react';

import {
  Activity,
  ArrowLeft,
  BarChart3,
  Brain,
  Calculator,
  Container,
  FileText,
  History,
  Layers,
  RotateCcw,
  Save,
  Send,
  Settings,
  Sparkles,
  TestTube2,
  Zap,
  Download,
  Share,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Clock,
  Target,
  Users,
  Database,
  Shield,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import { Card, CardContent } from '@/shared/components/card';

import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { Switch } from '@/shared/components/switch';
import { Separator } from '@/shared/components/separator';
import { cn } from '@/core/lib/utils';
import type { Task } from '@/core/types';

// 搅拌站状态接口
interface Station {
  id: string;
  name: string;
  isActive: boolean;
  currentTask?: string;
  efficiency?: number;
  status: 'running' | 'idle' | 'maintenance' | 'error';
  lastUpdate?: string;
}

interface ModernRatioHeaderProps {
  task: Task | null;
  onSave: () => void;
  canSave: boolean;
  isSaving: boolean;
  isDirty: boolean;
  onSwitchToOldVersionAction?: () => void;
  onAIGenerateAction?: () => void;
  onAIRecommendationAction?: () => void;
  onPerformanceDashboardAction?: () => void;
  onMortarRatioAction?: () => void;
  onSiloManagementAction?: () => void;
  onHistoryViewAction?: () => void;
  onTemplateViewAction?: () => void;
  onSettingsViewAction?: () => void;
  onCheckStandardAction?: () => void;
}

export function ModernRatioHeader({
  task,
  onSave,
  canSave,
  isSaving,
  isDirty,
  onSwitchToOldVersionAction,
  onAIGenerateAction,
  onAIRecommendationAction,
  onPerformanceDashboardAction,
  onMortarRatioAction,
  onSiloManagementAction,
  onHistoryViewAction,
  onTemplateViewAction,
  onSettingsViewAction,
  onCheckStandardAction,
}: ModernRatioHeaderProps) {
  const [selectedStation, setSelectedStation] = useState('station-1');
  const [unifiedRatio, setUnifiedRatio] = useState(false);

  // 模拟搅拌站数据
  const stations: Station[] = [
    {
      id: 'station-1',
      name: '搅拌站1',
      isActive: true,
      status: 'running',
      efficiency: 95,
      currentTask: 'C30-001',
      lastUpdate: '2分钟前',
    },
    {
      id: 'station-2',
      name: '搅拌站2',
      isActive: true,
      status: 'idle',
      efficiency: 88,
      lastUpdate: '5分钟前',
    },
    {
      id: 'station-3',
      name: '搅拌站3',
      isActive: false,
      status: 'maintenance',
      efficiency: 0,
      lastUpdate: '1小时前',
    },
  ];

  const currentStation = stations.find(s => s.id === selectedStation);

  // 获取状态颜色
  const getStatusColor = (status: Station['status']) => {
    switch (status) {
      case 'running':
        return 'text-green-600 bg-green-100';
      case 'idle':
        return 'text-yellow-600 bg-yellow-100';
      case 'maintenance':
        return 'text-blue-600 bg-blue-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: Station['status']) => {
    switch (status) {
      case 'running':
        return <Activity className='h-3 w-3' />;
      case 'idle':
        return <Clock className='h-3 w-3' />;
      case 'maintenance':
        return <Settings className='h-3 w-3' />;
      case 'error':
        return <AlertTriangle className='h-3 w-3' />;
      default:
        return <Container className='h-3 w-3' />;
    }
  };

  return (
    <Card className='m-1 shadow-sm border bg-white/95 backdrop-blur-sm'>
      <CardContent className='p-2'>
        {/* 第一行：核心信息和状态 */}
        <div className='flex items-center justify-between mb-2'>
          {/* 左侧：任务信息 */}
          <div className='flex items-center gap-3'>
            <Button
              variant='ghost'
              size='sm'
              onClick={onSwitchToOldVersionAction || (() => {})}
              className='gap-1 text-xs text-muted-foreground hover:text-foreground p-1 h-7'
            >
              <ArrowLeft className='h-3 w-3' />
              经典版
            </Button>

            <Separator orientation='vertical' className='h-6' />

            {/* 任务核心信息 */}
            <div className='flex items-center gap-2'>
              <TestTube2 className='h-4 w-4 text-primary' />
              <div className='text-sm'>
                {task ? (
                  <>
                    <span className='font-bold text-primary'>{task.taskNumber}</span>
                    <span className='mx-1 text-muted-foreground'>|</span>
                    <span className='font-medium'>{task.strength}</span>
                    <span className='mx-1 text-muted-foreground'>|</span>
                    <span
                      className='text-muted-foreground truncate max-w-32'
                      title={task.projectName}
                    >
                      {task.projectName}
                    </span>
                  </>
                ) : (
                  <span className='text-muted-foreground'>加载中...</span>
                )}
              </div>
            </div>

            <Separator orientation='vertical' className='h-6' />

            {/* 搅拌站选择和状态 */}
            <div className='flex items-center gap-2'>
              <Select value={selectedStation} onValueChange={setSelectedStation}>
                <SelectTrigger className='w-32 h-7 text-xs'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {stations.map(station => (
                    <SelectItem key={station.id} value={station.id} className='text-xs'>
                      <div className='flex items-center gap-2'>
                        <div
                          className={cn('w-2 h-2 rounded-full', getStatusColor(station.status))}
                        />
                        {station.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {currentStation && (
                <Badge
                  variant='outline'
                  className={cn('text-xs px-1 py-0', getStatusColor(currentStation.status))}
                >
                  {getStatusIcon(currentStation.status)}
                  <span className='ml-1'>
                    {currentStation.status === 'running'
                      ? '运行中'
                      : currentStation.status === 'idle'
                        ? '空闲'
                        : currentStation.status === 'maintenance'
                          ? '维护'
                          : '故障'}
                  </span>
                  {currentStation.efficiency !== undefined && currentStation.efficiency > 0 && (
                    <span className='ml-1'>({currentStation.efficiency}%)</span>
                  )}
                </Badge>
              )}
            </div>
          </div>

          {/* 右侧：快捷操作 */}
          <div className='flex items-center gap-1'>
            {/* AI操作组 */}
            <div className='flex items-center gap-1'>
              <Button
                onClick={onAIGenerateAction || (() => {})}
                size='sm'
                className='gap-1 h-7 px-2 text-xs bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700'
              >
                <Sparkles className='h-3 w-3' />
                AI生成
              </Button>

              <Button
                onClick={onAIRecommendationAction || (() => {})}
                variant='outline'
                size='sm'
                className='gap-1 h-7 px-2 text-xs border-purple-200 text-purple-600 hover:bg-purple-50'
              >
                <Brain className='h-3 w-3' />
                推荐
              </Button>
            </div>

            <Separator orientation='vertical' className='h-5 mx-1' />

            {/* 展开的功能按钮组 */}
            <div className='flex items-center gap-1'>
              <Button
                onClick={onPerformanceDashboardAction || (() => {})}
                variant='ghost'
                size='sm'
                className='gap-1 h-7 px-2 text-xs hover:bg-blue-50 hover:text-blue-600'
              >
                <BarChart3 className='h-3 w-3' />
                性能监控
              </Button>

              <Button
                onClick={onMortarRatioAction || (() => {})}
                variant='ghost'
                size='sm'
                className='gap-1 h-7 px-2 text-xs hover:bg-green-50 hover:text-green-600'
              >
                <Layers className='h-3 w-3' />
                砂浆配比
              </Button>

              <Button
                onClick={onHistoryViewAction || (() => {})}
                variant='ghost'
                size='sm'
                className='gap-1 h-7 px-2 text-xs hover:bg-orange-50 hover:text-orange-600'
              >
                <History className='h-3 w-3' />
                历史记录
              </Button>

              <Button
                onClick={onTemplateViewAction || (() => {})}
                variant='ghost'
                size='sm'
                className='gap-1 h-7 px-2 text-xs hover:bg-indigo-50 hover:text-indigo-600'
              >
                <FileText className='h-3 w-3' />
                配比模板
              </Button>

              <Button
                onClick={onSiloManagementAction || (() => {})}
                variant='ghost'
                size='sm'
                className='gap-1 h-7 px-2 text-xs hover:bg-teal-50 hover:text-teal-600'
              >
                <Database className='h-3 w-3' />
                料仓管理
              </Button>

              <Button
                onClick={onCheckStandardAction || (() => {})}
                variant='ghost'
                size='sm'
                className='gap-1 h-7 px-2 text-xs hover:bg-emerald-50 hover:text-emerald-600'
              >
                <Shield className='h-3 w-3' />
                检查标准
              </Button>

              <Button
                onClick={onSettingsViewAction || (() => {})}
                variant='ghost'
                size='sm'
                className='gap-1 h-7 px-2 text-xs hover:bg-gray-50 hover:text-gray-600'
              >
                <Settings className='h-3 w-3' />
                系统设置
              </Button>
            </div>
          </div>
        </div>

        {/* 第二行：详细信息和设置 */}
        <div className='flex items-center justify-between text-xs text-muted-foreground'>
          {/* 左侧：详细信息 */}
          <div className='flex items-center gap-4'>
            {task && (
              <>
                <div className='flex items-center gap-2'>
                  <Target className='h-3 w-3' />
                  <span>施工部位: {task.constructionSite}</span>
                </div>

                <div className='flex items-center gap-2'>
                  <Users className='h-3 w-3' />
                  <span>设计方: {(task as any).designUnit || '未指定'}</span>
                </div>
              </>
            )}

            {currentStation && currentStation.currentTask && (
              <div className='flex items-center gap-2'>
                <Activity className='h-3 w-3' />
                <span>当前任务: {currentStation.currentTask}</span>
              </div>
            )}

            {currentStation && currentStation.lastUpdate && (
              <div className='flex items-center gap-2'>
                <Clock className='h-3 w-3' />
                <span>更新: {currentStation.lastUpdate}</span>
              </div>
            )}
          </div>

          {/* 右侧：设置和状态 */}
          <div className='flex items-center gap-3'>
            {/* 统一配比开关 */}
            <div className='flex items-center gap-2'>
              <Label htmlFor='unified-ratio' className='text-xs'>
                统一配比
              </Label>
              <Switch
                id='unified-ratio'
                checked={unifiedRatio}
                onCheckedChange={setUnifiedRatio}
                className='scale-75'
              />
            </div>

            {/* 快捷保存和分享 */}
            <div className='flex items-center gap-1'>
              <Button
                variant='ghost'
                size='sm'
                className='h-6 px-1 text-xs'
                onClick={onSave}
                disabled={!canSave || isSaving}
                title={isDirty ? '保存配比' : '无更改'}
              >
                {isSaving ? (
                  <RefreshCw className='h-3 w-3 animate-spin' />
                ) : (
                  <Save className='h-3 w-3' />
                )}
              </Button>
              <Button variant='ghost' size='sm' className='h-6 px-1 text-xs'>
                <Download className='h-3 w-3' />
              </Button>
              <Button variant='ghost' size='sm' className='h-6 px-1 text-xs'>
                <Share className='h-3 w-3' />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
