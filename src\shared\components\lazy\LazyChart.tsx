/**
 * LazyChart - 图表组件的动态导入包装器
 * 只在需要时加载 recharts 库，减少初始包体积
 */

'use client';

import React, { Suspense, lazy } from 'react';
import { LoadingSpinner } from '@/shared/components/LoadingSpinner';
import { Card, CardContent } from '@/shared/components/card';

// 动态导入图表相关组件
const ChartContainer = lazy(() =>
  import('@/shared/components/chart').then(module => ({
    default: module.ChartContainer,
  }))
);

const ChartTooltip = lazy(() =>
  import('@/shared/components/chart').then(module => ({
    default: module.ChartTooltip,
  }))
);

const ChartTooltipContent = lazy(() =>
  import('@/shared/components/chart').then(module => ({
    default: module.ChartTooltipContent,
  }))
);

// 动态导入 recharts 组件
const BarChart = lazy(() =>
  import('recharts').then(module => ({
    default: module.BarChart,
  }))
);

const LineChart = lazy(() =>
  import('recharts').then(module => ({
    default: module.LineChart,
  }))
);

const PieChart = lazy(() =>
  import('recharts').then(module => ({
    default: module.PieChart,
  }))
);

const AreaChart = lazy(() =>
  import('recharts').then(module => ({
    default: module.AreaChart,
  }))
);

const Bar = lazy(() =>
  import('recharts').then(module => ({
    default: module.Bar as any,
  }))
) as any;

const Line = lazy(() =>
  import('recharts').then(module => ({
    default: module.Line,
  }))
);

const Area = lazy(() =>
  import('recharts').then(module => ({
    default: module.Area as any,
  }))
) as any;

const XAxis = lazy(() =>
  import('recharts').then(module => ({
    default: module.XAxis,
  }))
);

const YAxis = lazy(() =>
  import('recharts').then(module => ({
    default: module.YAxis,
  }))
);

const CartesianGrid = lazy(() =>
  import('recharts').then(module => ({
    default: module.CartesianGrid,
  }))
);

const ResponsiveContainer = lazy(() =>
  import('recharts').then(module => ({
    default: module.ResponsiveContainer,
  }))
);

/**
 * 图表加载占位符
 */
function ChartLoadingFallback() {
  return (
    <Card>
      <CardContent className='flex items-center justify-center p-8'>
        <LoadingSpinner />
        <span className='ml-2 text-sm text-muted-foreground'>加载图表组件...</span>
      </CardContent>
    </Card>
  );
}

/**
 * 懒加载的柱状图组件
 */
export function LazyBarChart({ children, ...props }: any) {
  return (
    <Suspense fallback={<ChartLoadingFallback />}>
      <BarChart {...props}>{children}</BarChart>
    </Suspense>
  );
}

/**
 * 懒加载的折线图组件
 */
export function LazyLineChart({ children, ...props }: any) {
  return (
    <Suspense fallback={<ChartLoadingFallback />}>
      <LineChart {...props}>{children}</LineChart>
    </Suspense>
  );
}

/**
 * 懒加载的饼图组件
 */
export function LazyPieChart({ children, ...props }: any) {
  return (
    <Suspense fallback={<ChartLoadingFallback />}>
      <PieChart {...props}>{children}</PieChart>
    </Suspense>
  );
}

/**
 * 懒加载的面积图组件
 */
export function LazyAreaChart({ children, ...props }: any) {
  return (
    <Suspense fallback={<ChartLoadingFallback />}>
      <AreaChart {...props}>{children}</AreaChart>
    </Suspense>
  );
}

/**
 * 懒加载的图表容器
 */
export function LazyChartContainer({ children, ...props }: any) {
  return (
    <Suspense fallback={<ChartLoadingFallback />}>
      <ChartContainer {...props}>{children}</ChartContainer>
    </Suspense>
  );
}

// 导出所有懒加载的图表组件
export {
  BarChart as LazyBar,
  LineChart as LazyLine,
  AreaChart as LazyArea,
  XAxis as LazyXAxis,
  YAxis as LazyYAxis,
  CartesianGrid as LazyCartesianGrid,
  ResponsiveContainer as LazyResponsiveContainer,
  ChartTooltip as LazyChartTooltip,
  ChartTooltipContent as LazyChartTooltipContent,
};

export default {
  BarChart: LazyBarChart,
  LineChart: LazyLineChart,
  PieChart: LazyPieChart,
  AreaChart: LazyAreaChart,
  ChartContainer: LazyChartContainer,
};
