'use client';

import React, { useState } from 'react';

import { Clipboard, Copy, Lock, Save, X } from 'lucide-react';

import { EditableComboBox } from '@/shared/components/editable-combo-box';
import { Button } from '@/shared/components/button';
import { Checkbox } from '@/shared/components/checkbox';
import { CustomDateTimePicker } from '@/shared/components/custom-datetime-picker';
import { Dialog, DialogContent, DialogTitle } from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import type { Task } from '@/core/types';

interface TaskEditModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task?: Partial<Task>;
  onConfirm: (taskData: TaskEditData) => void;
}

export interface TaskEditData {
  // 第一行（编号信息）
  taskNumber: string; // 只读
  bookTaskNumber: string; // 可编辑
  ratioNumber: string; // 配比号，只读
  publishTime: string; // 只读
  status: string; // 只读

  // 合同内容分区
  contractNumber: string;
  distance: string;
  newTaskStatus: string;
  businessPerson: string;
  productType: string;
  ticketStyle: string; // 票据样式
  constructionUnit: string;
  constructionSite: string;
  vehicleRental: boolean; // 车辆外租（单选）
  projectName: string;
  constructionLocation: string;

  // 具体信息（可编辑下拉+手动输入）
  specificConstructionUnit: string;
  specificConstructionSite: string;
  specificProjectName: string;
  specificConstructionLocation: string;
  constructionPart: string;
  supplyTime: string; // 供砼时间
  lockTime: boolean; // 锁定时间按钮
  strengthGrade: string;
  finishTime: string;
  estimatedPumpTruck: string; // 预计泵车
  waterproofGrade: string; // 抗渗等级
  pourMethod: string; // 浇筑方式
  transportDistance: string; // 运距
  increment: string; // 增量
  tareWeight: string; // 皮重减重
  antiFreezeGrade: string; // 抗冻等级
  slump: string; // 坍落度±
  constructionOpinion: string; // 施工科意见
  contactPhone: string;
  concreteQuantity: string; // 砼数量
  otherRequirements: string;
  route: string;
  notes: string;

  // 简称信息
  constructionUnitAbbr: string;
  constructionSiteAbbr: string;
  projectNameAbbr: string;
  constructionLocationAbbr: string;
  transportDistanceAbbr: string;
  constructionPartAbbr: string;

  // 货物和选项
  cargoType: string; // 货物类（砼、砂浆、水票）
  flowNumber: string;
  taskForceCard: boolean; // 任务强制卡方
  siteWeighingMark: boolean; // 工地过磅标志

  // 归属信息
  belongingStation: string; // 归属主站（不可编辑）
  destinationCompanies: string[]; // 发往公司名称（多选）

  // 价格信息
  concreteUnitPrice: string;
  mortarUnitPrice: string; // 砂浆单价
  pumpUnitPrice: string;
  platformFee: string;
  lockPrice: boolean;
  priceDescription: string;

  // 创建信息
  createDate: string;
  creator: string;
  createSource: string; // 电脑、移动端
}

export const TaskEditModal: React.FC<TaskEditModalProps> = ({
  open,
  onOpenChange,
  task,
  onConfirm,
}) => {
  const [formData, setFormData] = useState<TaskEditData>({
    // 第一行（编号信息）
    taskNumber: task?.taskNumber || 'C125-00003',
    bookTaskNumber: 'C125-00003',
    ratioNumber: 'R001',
    publishTime: '2025-04-09 13:34:38',
    status: '正在进行',

    // 合同内容分区
    contractNumber: 'C125-00002',
    distance: '15',
    newTaskStatus: '',
    businessPerson: '',
    productType: '砼',
    ticketStyle: '',
    constructionUnit: task?.constructionUnit || '长治市郊善堂洗煤工程有限公司',
    constructionSite: task?.constructionSite || '长治市郊善堂洗煤工程有限公司',
    vehicleRental: false,
    projectName: task?.projectName || '个人自建',
    constructionLocation: '西南山',

    // 具体信息
    specificConstructionUnit: '长治市郊善堂洗煤工程有限公司',
    specificConstructionSite: '长治市郊善堂洗煤工程有限公司',
    specificProjectName: '个人自建',
    specificConstructionLocation: '西南山',
    constructionPart: '垫层',
    supplyTime: '2025-04-08 14:00:04',
    lockTime: false,
    strengthGrade: task?.strength || 'C30',
    finishTime: '',
    estimatedPumpTruck: '',
    waterproofGrade: '',
    pourMethod: '自卸',
    transportDistance: '15',
    increment: '6',
    tareWeight: '',
    antiFreezeGrade: '',
    slump: '',
    constructionOpinion: '',
    contactPhone: '14503551443',
    concreteQuantity: '15',
    otherRequirements: '',
    route: '',
    notes: '',

    // 简称信息
    constructionUnitAbbr: '',
    constructionSiteAbbr: '',
    projectNameAbbr: '',
    constructionLocationAbbr: '',
    transportDistanceAbbr: '',
    constructionPartAbbr: '',

    // 货物和选项
    cargoType: '砼',
    flowNumber: '',
    taskForceCard: false,
    siteWeighingMark: false,

    // 归属信息
    belongingStation: '主站1',
    destinationCompanies: [],

    // 价格信息
    concreteUnitPrice: '无单价',
    mortarUnitPrice: '无单价',
    pumpUnitPrice: '无单价',
    platformFee: '无单价',
    lockPrice: false,
    priceDescription: '',

    // 创建信息
    createDate: '2025-04-09',
    creator: '张三',
    createSource: '电脑端',
  });

  // 选项管理状态
  const [fieldOptions, setFieldOptions] = useState({
    specificConstructionUnit: ['长治市郊善堂洗煤工程有限公司', '其他建设单位'],
    specificConstructionSite: ['施工单位A', '施工单位B', '施工单位C'],
    specificProjectName: ['个人自建', '商业项目', '住宅项目'],
    specificConstructionLocation: ['西南山', '东北区', '市中心'],
    constructionPart: ['垫层', '基础', '主体', '装修'],
    estimatedPumpTruck: ['泵车A', '泵车B', '泵车C'],
    pourMethod: ['自卸', '泵送', '人工'],
    slump: ['180±20', '160±20', '140±20'],
    constructionOpinion: ['同意', '需要调整', '暂缓'],
    contactPhone: ['14503551443', '13800138000'],
    otherRequirements: ['无特殊要求', '注意路况', '加急处理'],
    route: ['路线1', '路线2', '路线3'],
  });

  const updateField = (field: keyof TaskEditData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // 添加选项
  const addOption = (fieldName: keyof typeof fieldOptions, option: string) => {
    setFieldOptions(prev => ({
      ...prev,
      [fieldName]: [...prev[fieldName], option],
    }));
  };

  // 删除选项
  const removeOption = (fieldName: keyof typeof fieldOptions, option: string) => {
    setFieldOptions(prev => ({
      ...prev,
      [fieldName]: prev[fieldName].filter(item => item !== option),
    }));
  };

  const handleConfirm = () => {
    onConfirm(formData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleCopy = () => {
    // 复制任务数据到剪贴板
    const taskDetails = JSON.stringify(formData, null, 2);
    navigator.clipboard.writeText(taskDetails).then(() => {
      console.log('任务详情已复制到剪贴板');
    });
  };

  const handlePaste = async () => {
    // 从剪贴板粘贴任务详情
    try {
      const clipboardText = await navigator.clipboard.readText();
      const pastedData = JSON.parse(clipboardText);
      setFormData(prev => ({ ...prev, ...pastedData }));
      console.log('已粘贴任务详情');
    } catch (error) {
      console.error('粘贴失败:', error);
    }
  };

  const handleLockTime = () => {
    updateField('lockTime', !formData.lockTime);
  };

  const handleGetContractPrice = () => {
    // 取合同价逻辑
    console.log('获取合同价格');
  };

  // 移除不再使用的日期选择器 ref

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-6xl max-h-[96vh] overflow-hidden p-0 pb-1 [&>button]:hidden'>
        <DialogTitle className='sr-only'>任务编辑</DialogTitle>
        <div className='p-0 px-4 py-0.5 bg-blue-500 text-white rounded-t-lg'>
          <div className='flex items-center justify-between text-base font-semibold'>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-white rounded-full'></div>
              <span>任务编辑</span>
            </div>
          </div>
        </div>

        <div className='max-h-[calc(96vh-80px)] overflow-y-auto px-4 space-y-2 bg-gradient-to-b from-transparent to-slate-50/50'>
          {/* 基本信息卡片 */}
          <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-2'>
            <div className='grid grid-cols-6 gap-1 items-center'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs font-medium text-slate-600 whitespace-nowrap w-20'>
                  任务编号：
                </Label>
                <Input
                  value={formData.taskNumber}
                  className='h-6 text-xs bg-slate-50 border-slate-200 text-slate-700 flex-1'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>
                  书面任务编号：
                </Label>
                <Input
                  value={formData.bookTaskNumber}
                  onChange={e => updateField('bookTaskNumber', e.target.value)}
                  className='h-6 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors flex-1'
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-12'>配比号：</Label>
                <Input
                  value={formData.ratioNumber}
                  className='h-6 text-xs bg-gray-100 flex-1'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>发布时间：</Label>
                <Input
                  value={formData.publishTime}
                  className='h-6 text-xs bg-gray-100 flex-1'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <span className='text-xs text-blue-600 font-medium'>{formData.status}</span>
              </div>
              <div className='flex items-center gap-1'>
                <Button
                  onClick={handlePaste}
                  variant='outline'
                  size='sm'
                  className='h-6 px-2 text-xs'
                >
                  <Clipboard className='w-3 h-3 mr-1' />
                  粘贴
                </Button>
              </div>
            </div>
          </div>

          {/* 合同内容卡片 */}
          <div className='bg-white rounded-lg border border-blue-200 shadow-sm p-2'>
            {/* 第一行：合同基本信息 */}
            <div className='grid grid-cols-6 gap-2 items-center mb-2'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>合同编号：</Label>
                <Input
                  value={formData.contractNumber}
                  className='h-6 text-xs bg-gray-100 flex-1'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>运距：　　</Label>
                <Input
                  value={formData.distance}
                  className='h-6 text-xs bg-gray-100 flex-1'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>新任务状态：</Label>
                <Select
                  value={formData.newTaskStatus}
                  onValueChange={v => updateField('newTaskStatus', v)}
                  disabled
                >
                  <SelectTrigger className='h-6 text-xs bg-gray-100 flex-1'>
                    <SelectValue placeholder='请选择' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='新建'>新建</SelectItem>
                    <SelectItem value='进行中'>进行中</SelectItem>
                    <SelectItem value='已完成'>已完成</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>业务员：　</Label>
                <Select
                  value={formData.businessPerson}
                  onValueChange={v => updateField('businessPerson', v)}
                  disabled
                >
                  <SelectTrigger className='h-6 text-xs bg-gray-100 flex-1'>
                    <SelectValue placeholder='请选择' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='业务员A'>业务员A</SelectItem>
                    <SelectItem value='业务员B'>业务员B</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>产品种类：</Label>
                <Select
                  value={formData.productType}
                  onValueChange={v => updateField('productType', v)}
                  disabled
                >
                  <SelectTrigger className='h-6 text-xs bg-gray-100 flex-1'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='砼'>砼</SelectItem>
                    <SelectItem value='砂浆'>砂浆</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>票据样式：</Label>
                <Select
                  value={formData.ticketStyle}
                  onValueChange={v => updateField('ticketStyle', v)}
                  disabled
                >
                  <SelectTrigger className='h-6 text-xs bg-gray-100'>
                    <SelectValue placeholder='请选择' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='样式A'>样式A</SelectItem>
                    <SelectItem value='样式B'>样式B</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 第二行：建设单位信息 */}
            <div className='grid grid-cols-3 gap-2 items-center mb-2'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>建设单位：</Label>
                <Input
                  value={formData.constructionUnit}
                  className='h-6 text-xs flex-1 bg-gray-100'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>施工单位：</Label>
                <Input
                  value={formData.constructionSite}
                  className='h-6 text-xs flex-1 bg-gray-100'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Checkbox
                  id='vehicleRental'
                  checked={formData.vehicleRental}
                  onCheckedChange={(checked: boolean) => updateField('vehicleRental', checked)}
                  disabled
                />
                <Label htmlFor='vehicleRental' className='text-xs text-gray-500'>
                  车辆外租
                </Label>
              </div>
            </div>

            {/* 第三行：工程信息 */}
            <div className='grid grid-cols-2 gap-2 items-center '>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>工程名称：</Label>
                <Input
                  value={formData.projectName}
                  className='h-6 text-xs flex-1 bg-gray-100'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>施工地点：</Label>
                <Input
                  value={formData.constructionLocation}
                  className='h-6 text-xs flex-1 bg-gray-100'
                  readOnly
                />
              </div>
            </div>
          </div>

          {/* 具体信息卡片 */}
          <div className='bg-white rounded-lg border border-green-200 shadow-sm p-2'>
            {/* 具体建设单位信息 */}
            <div className='grid grid-cols-2 gap-2 items-center mb-2'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>
                  具体建设单位：
                </Label>
                <EditableComboBox
                  value={formData.specificConstructionUnit}
                  onValueChange={v => updateField('specificConstructionUnit', v)}
                  options={fieldOptions.specificConstructionUnit}
                  onAddOption={option => addOption('specificConstructionUnit', option)}
                  onRemoveOption={option => removeOption('specificConstructionUnit', option)}
                  className='h-6 text-xs flex-1'
                  placeholder='选择或输入建设单位'
                  allowAdd={true}
                  allowRemove={true}
                  addFromInput={true}
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>
                  具体施工单位：
                </Label>
                <EditableComboBox
                  value={formData.specificConstructionSite}
                  onValueChange={v => updateField('specificConstructionSite', v)}
                  options={fieldOptions.specificConstructionSite}
                  onAddOption={option => addOption('specificConstructionSite', option)}
                  onRemoveOption={option => removeOption('specificConstructionSite', option)}
                  className='h-6 text-xs flex-1'
                  placeholder='选择或输入施工单位'
                  allowAdd={true}
                  allowRemove={true}
                  addFromInput={true}
                />
              </div>
            </div>

            {/* 具体工程信息 */}
            <div className='grid grid-cols-2 gap-2 items-center mb-2'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>
                  具体工程名称：
                </Label>
                <EditableComboBox
                  value={formData.specificProjectName}
                  onValueChange={v => updateField('specificProjectName', v)}
                  options={fieldOptions.specificProjectName}
                  onAddOption={option => addOption('specificProjectName', option)}
                  onRemoveOption={option => removeOption('specificProjectName', option)}
                  className='h-6 text-xs flex-1'
                  placeholder='选择或输入工程名称'
                  allowAdd={true}
                  allowRemove={true}
                  addFromInput={true}
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>
                  具体施工地点：
                </Label>
                <EditableComboBox
                  value={formData.specificConstructionLocation}
                  onValueChange={v => updateField('specificConstructionLocation', v)}
                  options={fieldOptions.specificConstructionLocation}
                  onAddOption={option => addOption('specificConstructionLocation', option)}
                  onRemoveOption={option => removeOption('specificConstructionLocation', option)}
                  className='h-6 text-xs flex-1'
                  placeholder='选择或输入施工地点'
                  allowAdd={true}
                  allowRemove={true}
                  addFromInput={true}
                />
              </div>
            </div>

            {/* 施工部位和时间 */}
            <div className='grid grid-cols-3 gap-2 items-center'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>施工部位：</Label>
                <EditableComboBox
                  value={formData.constructionPart}
                  onValueChange={v => updateField('constructionPart', v)}
                  options={fieldOptions.constructionPart}
                  onAddOption={option => addOption('constructionPart', option)}
                  onRemoveOption={option => removeOption('constructionPart', option)}
                  className='h-6 text-xs flex-1'
                  placeholder='选择或输入施工部位'
                  allowAdd={true}
                  allowRemove={true}
                  addFromInput={true}
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-20'>供砼时间：</Label>
                <div className='flex-1'>
                  <CustomDateTimePicker
                    value={formData.supplyTime}
                    onChange={value => updateField('supplyTime', value)}
                    disabled={formData.lockTime}
                    placeholder='选择供砼时间'
                    format='YYYY-MM-DD HH:mm'
                    showTime={true}
                  />
                </div>
              </div>
              <div className='flex items-center gap-1'>
                <Button
                  onClick={handleLockTime}
                  variant={formData.lockTime ? 'default' : 'outline'}
                  size='sm'
                  className='h-6 px-2 text-xs'
                >
                  <Lock className='w-3 h-3 mr-1' />
                  {formData.lockTime ? '已锁定' : '锁定时间'}
                </Button>
              </div>
            </div>
          </div>

          {/* 技术参数卡片 */}
          <div className='bg-white rounded-lg border border-purple-200 shadow-sm p-2'>
            {/* 强度等级、结束时间、预计泵车 */}
            <div className='grid grid-cols-3 gap-2 items-center mb-2'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>强度等级：</Label>
                <Input
                  value={formData.strengthGrade}
                  className='h-6 text-xs bg-gray-100'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>结束时间：</Label>
                <div className='flex-1'>
                  <CustomDateTimePicker
                    value={formData.finishTime}
                    onChange={(value: string) => updateField('finishTime', value)}
                    placeholder='选择结束时间'
                    format='YYYY-MM-DD HH:mm'
                    showTime={true}
                  />
                </div>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>预计泵车：</Label>
                <EditableComboBox
                  value={formData.estimatedPumpTruck}
                  onValueChange={v => updateField('estimatedPumpTruck', v)}
                  options={fieldOptions.estimatedPumpTruck}
                  onAddOption={option => addOption('estimatedPumpTruck', option)}
                  onRemoveOption={option => removeOption('estimatedPumpTruck', option)}
                  className='h-6 text-xs'
                  placeholder='选择或输入泵车'
                  allowAdd={true}
                  allowRemove={true}
                  addFromInput={true}
                />
              </div>
            </div>

            {/* 抗渗等级、浇筑方式、运距、增量、皮重减重 */}
            <div className='grid grid-cols-5 gap-2 items-center mb-2'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>抗渗等级：</Label>
                <Input
                  value={formData.waterproofGrade}
                  className='h-6 text-xs bg-gray-100'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>浇筑方式：</Label>
                <EditableComboBox
                  value={formData.pourMethod}
                  onValueChange={v => updateField('pourMethod', v)}
                  options={fieldOptions.pourMethod}
                  onAddOption={option => addOption('pourMethod', option)}
                  onRemoveOption={option => removeOption('pourMethod', option)}
                  className='h-6 text-xs'
                  placeholder='选择或输入浇筑方式'
                  allowAdd={true}
                  allowRemove={true}
                  addFromInput={true}
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>运距：</Label>
                <div className='flex items-center gap-1 flex-1'>
                  <Input
                    value={formData.transportDistance}
                    onChange={e => updateField('transportDistance', e.target.value)}
                    className='h-6 text-xs flex-1'
                  />
                  <span className='text-xs text-gray-500'>公里</span>
                </div>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>增量：</Label>
                <Input
                  value={formData.increment}
                  onChange={e => updateField('increment', e.target.value)}
                  className='h-6 text-xs flex-1'
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>皮重减重：</Label>
                <Input
                  value={formData.tareWeight}
                  onChange={e => updateField('tareWeight', e.target.value)}
                  className='h-6 text-xs flex-1'
                />
              </div>
            </div>

            {/* 抗冻等级、坍落度、施工科意见 */}
            <div className='grid grid-cols-3 gap-2 items-center mb-2'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>抗冻等级：</Label>
                <Input
                  value={formData.antiFreezeGrade}
                  className='h-6 text-xs bg-gray-100'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>坍落度±：</Label>
                <EditableComboBox
                  value={formData.slump}
                  onValueChange={v => updateField('slump', v)}
                  options={fieldOptions.slump}
                  onAddOption={option => addOption('slump', option)}
                  onRemoveOption={option => removeOption('slump', option)}
                  className='h-6 text-xs'
                  placeholder='选择或输入坍落度'
                  allowAdd={true}
                  allowRemove={true}
                  addFromInput={true}
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>施工科意见：</Label>
                <EditableComboBox
                  value={formData.constructionOpinion}
                  onValueChange={v => updateField('constructionOpinion', v)}
                  options={fieldOptions.constructionOpinion}
                  onAddOption={option => addOption('constructionOpinion', option)}
                  onRemoveOption={option => removeOption('constructionOpinion', option)}
                  className='h-6 text-xs'
                  placeholder='选择或输入意见'
                  allowAdd={true}
                  allowRemove={true}
                  addFromInput={true}
                />
              </div>
            </div>

            {/* 联系人、砼数量、其他要求 */}
            <div className='grid grid-cols-3 gap-2 items-center mb-2'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>联 系 人 ：</Label>
                <EditableComboBox
                  value={formData.contactPhone}
                  onValueChange={v => updateField('contactPhone', v)}
                  options={fieldOptions.contactPhone}
                  onAddOption={option => addOption('contactPhone', option)}
                  onRemoveOption={option => removeOption('contactPhone', option)}
                  className='h-6 text-xs'
                  placeholder='选择或输入联系方式'
                  allowAdd={true}
                  allowRemove={true}
                  addFromInput={true}
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>砼数量：</Label>
                <Input
                  value={formData.concreteQuantity}
                  onChange={e => updateField('concreteQuantity', e.target.value)}
                  className='h-6 text-xs flex-1'
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>其他要求：</Label>
                <EditableComboBox
                  value={formData.otherRequirements}
                  onValueChange={v => updateField('otherRequirements', v)}
                  options={fieldOptions.otherRequirements}
                  onAddOption={option => addOption('otherRequirements', option)}
                  onRemoveOption={option => removeOption('otherRequirements', option)}
                  className='h-6 text-xs'
                  placeholder='选择或输入要求'
                  allowAdd={true}
                  allowRemove={true}
                  addFromInput={true}
                />
              </div>
            </div>

            {/* 路线、备注 */}
            <div className='grid grid-cols-2 gap-2 items-center'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap w-14'>路 线 ： </Label>
                <EditableComboBox
                  value={formData.route}
                  onValueChange={v => updateField('route', v)}
                  options={fieldOptions.route}
                  onAddOption={option => addOption('route', option)}
                  onRemoveOption={option => removeOption('route', option)}
                  className='h-6 text-xs'
                  placeholder='选择或输入路线'
                  allowAdd={true}
                  allowRemove={true}
                  addFromInput={true}
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>备注：</Label>
                <Input
                  value={formData.notes}
                  onChange={v => updateField('notes', v)}
                  className='h-6 text-xs resize-none flex-1'
                  placeholder='备注信息'
                />
              </div>
            </div>
          </div>

          {/* 简称信息卡片 */}
          <div className='bg-white rounded-lg border border-orange-200 shadow-sm p-2'>
            <div className='grid grid-cols-3 gap-2 items-center mb-2'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>建设单位简称：</Label>
                <Input
                  value={formData.constructionUnitAbbr}
                  className='h-6 text-xs flex-1 bg-gray-100'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>施工单位简称：</Label>
                <Input
                  value={formData.constructionSiteAbbr}
                  className='h-6 text-xs flex-1 bg-gray-100'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>工程名称简称：</Label>
                <Input
                  value={formData.projectNameAbbr}
                  className='h-6 text-xs flex-1 bg-gray-100'
                  readOnly
                />
              </div>
            </div>
            <div className='grid grid-cols-3 gap-2 items-center'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>施工地点简称：</Label>
                <Input
                  value={formData.constructionLocationAbbr}
                  className='h-6 text-xs flex-1 bg-gray-100'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>运输距离简称：</Label>
                <Input
                  value={formData.transportDistanceAbbr}
                  className='h-6 text-xs flex-1 bg-gray-100'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>施工部位简称：</Label>
                <Input
                  value={formData.constructionPartAbbr}
                  className='h-6 text-xs flex-1 bg-gray-100'
                  readOnly
                />
              </div>
            </div>
          </div>

          {/* 货物信息卡片 */}
          <div className='bg-white rounded-lg border border-indigo-200 shadow-sm p-2'>
            <div className='grid grid-cols-4 gap-2 items-center'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>货物类型：</Label>
                <Select value={formData.cargoType} onValueChange={v => updateField('cargoType', v)}>
                  <SelectTrigger className='h-6 text-xs flex-1'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='砼'>砼</SelectItem>
                    <SelectItem value='砂浆'>砂浆</SelectItem>
                    <SelectItem value='水票'>水票</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>流水号：</Label>
                <Input
                  value={formData.flowNumber}
                  onChange={e => updateField('flowNumber', e.target.value)}
                  className='h-6 text-xs flex-1'
                />
              </div>
              <div className='flex items-center gap-1'>
                <Checkbox
                  id='taskForceCard'
                  checked={formData.taskForceCard}
                  onCheckedChange={(checked: boolean) => updateField('taskForceCard', checked)}
                />
                <Label htmlFor='taskForceCard' className='text-xs'>
                  任务强制卡方
                </Label>
              </div>
              <div className='flex items-center gap-1'>
                <Checkbox
                  id='siteWeighingMark'
                  checked={formData.siteWeighingMark}
                  onCheckedChange={(checked: boolean) => updateField('siteWeighingMark', checked)}
                />
                <Label htmlFor='siteWeighingMark' className='text-xs'>
                  工地过磅标志
                </Label>
              </div>
            </div>
          </div>

          {/* 归属信息卡片 */}
          <div className='bg-white rounded-lg border border-teal-200 shadow-sm p-2'>
            <div className='grid grid-cols-2 gap-2 items-center'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>归属主站：</Label>
                <Select
                  value={formData.belongingStation}
                  onValueChange={v => updateField('belongingStation', v)}
                  disabled
                >
                  <SelectTrigger className='h-6 text-xs flex-1 bg-gray-100'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='主站1'>主站1</SelectItem>
                    <SelectItem value='主站2'>主站2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>发往公司名称：</Label>
                <div className='flex items-center gap-1 flex-1'>
                  <span className='text-xs text-gray-500'>多选项功能</span>
                </div>
              </div>
            </div>
          </div>

          {/* 价格信息卡片 */}
          <div className='bg-white rounded-lg border border-red-200 shadow-sm p-2'>
            <div className='grid grid-cols-7 gap-2 items-center mb-2'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>砼单价：</Label>
                <Input
                  value={formData.concreteUnitPrice}
                  onChange={e => updateField('concreteUnitPrice', e.target.value)}
                  className='h-6 text-xs flex-1'
                  placeholder='无单价'
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>砂浆单价：</Label>
                <Input
                  value={formData.mortarUnitPrice}
                  onChange={e => updateField('mortarUnitPrice', e.target.value)}
                  className='h-6 text-xs flex-1'
                  placeholder='无单价'
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>泵送单价：</Label>
                <Input
                  value={formData.pumpUnitPrice}
                  onChange={e => updateField('pumpUnitPrice', e.target.value)}
                  className='h-6 text-xs flex-1'
                  placeholder='无单价'
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>台班费：</Label>
                <Input
                  value={formData.platformFee}
                  onChange={e => updateField('platformFee', e.target.value)}
                  className='h-6 text-xs flex-1'
                  placeholder='无单价'
                />
              </div>
              <div className='flex items-center gap-1'>
                <Checkbox
                  id='lockPrice'
                  checked={formData.lockPrice}
                  onCheckedChange={(checked: boolean) => updateField('lockPrice', checked)}
                />
                <Label htmlFor='lockPrice' className='text-xs'>
                  锁定价格
                </Label>
              </div>
              <div className='flex items-center gap-1'>
                <Button
                  onClick={handleGetContractPrice}
                  variant='outline'
                  size='sm'
                  className='h-6 px-2 text-xs'
                >
                  取合同价
                </Button>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>价格说明：</Label>
                <Input
                  value={formData.priceDescription}
                  onChange={e => updateField('priceDescription', e.target.value)}
                  className='h-6 text-xs flex-1'
                  placeholder='Tips'
                />
              </div>
            </div>
            <div className='mt-2 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg'>
              <div className='flex items-center gap-2 mb-2 pb-2 border-b border-blue-200'>
                <div className='w-1 h-4 bg-blue-500 rounded-full'></div>
                <span className='text-sm font-medium text-blue-700'>重要信息</span>
              </div>
              <div className='grid grid-cols-4 gap-2 items-center'>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600'>强度：</Label>
                  <span className='text-xs font-bold text-blue-600 bg-blue-50 px-2 py-1 rounded'>
                    {formData.strengthGrade}
                  </span>
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600'>砼单价：</Label>
                  <span
                    className={`text-xs font-bold px-2 py-1 rounded ${
                      formData.concreteUnitPrice === '无单价'
                        ? 'text-red-600 bg-red-50'
                        : 'text-green-600 bg-green-50'
                    }`}
                  >
                    {formData.concreteUnitPrice}
                  </span>
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600'>浇筑方式：</Label>
                  <span className='text-xs font-bold text-purple-600 bg-purple-50 px-2 py-1 rounded'>
                    {formData.pourMethod}
                  </span>
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600'>泵送单价：</Label>
                  <span
                    className={`text-xs font-bold px-2 py-1 rounded ${
                      formData.pumpUnitPrice === '无单价'
                        ? 'text-red-600 bg-red-50'
                        : 'text-green-600 bg-green-50'
                    }`}
                  >
                    {formData.pumpUnitPrice}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部区域 */}
        <div className='flex items-center justify-between px-6 py-3 bg-gradient-to-r from-slate-50 to-white border-t border-slate-200'>
          {/* 左侧：创建信息 */}
          <div className='text-xs text-slate-600'>
            任务创建日期：{formData.createDate} | 创建人：{formData.creator}（
            {formData.createSource}）
          </div>

          {/* 右侧：操作按钮 */}
          <div className='flex items-center gap-3'>
            <Button
              variant='outline'
              onClick={handleCancel}
              className='flex items-center gap-2 h-8 px-4 text-sm border-slate-300 hover:border-red-400 hover:bg-red-50 hover:text-red-600 transition-all duration-200'
            >
              <X className='w-3 h-3' />
              退出
            </Button>
            <Button
              onClick={handleCopy}
              variant='outline'
              className='flex items-center gap-2 h-8 px-4 text-sm border-slate-300 hover:border-slate-400 hover:bg-slate-50 transition-all duration-200'
            >
              <Copy className='w-3 h-3' />
              复制
            </Button>
            <Button
              onClick={handleConfirm}
              className='flex items-center gap-2 h-8 px-6 text-sm bg-blue-600 hover:from-blue-700 hover:to-blue-800 text-white shadow-sm hover:shadow-md transition-all duration-200'
            >
              <Save className='w-3 h-3' />
              保存
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
