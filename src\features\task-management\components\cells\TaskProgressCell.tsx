// src/components/sections/task-list/cells/TaskProgressCell.tsx
'use client';

import React, { memo } from 'react';

import { Progress } from '@/shared/components/progress';
import { cn } from '@/core/lib/utils';
import type { Task } from '@/core/types';

// src/components/sections/task-list/cells/TaskProgressCell.tsx

interface TaskProgressCellProps {
  progressValue: number;
  textClassName?: string;
}

const TaskProgressCellComponent: React.FC<TaskProgressCellProps> = ({
  progressValue,
  textClassName,
}) => {
  return (
    <div className='flex flex-col items-center justify-center h-full'>
      <Progress value={progressValue} className='w-full h-2.5' />
      <span className={cn('text-[9px] mt-0.5', textClassName)}>{Math.round(progressValue)}%</span>
    </div>
  );
};

export const TaskProgressCell = memo(TaskProgressCellComponent);
