/**
 * 配比页面模态框管理器组件
 * 集中管理所有配比相关的模态框渲染
 */

import React from 'react';
import type { Task } from '@/core/types';
import type { RatioModalsState } from '@/features/ratio-management/hooks/ratio/useRatioModals';

// 模态框组件导入
import { MortarRatioModal } from '@/models/MortarRatioModal';
import { RatioHistoryModal } from '@/models/RatioHistoryModal';
import { RatioSelectionModal } from '@/models/RatioSelectionModal';
import { SiloManagementModal } from '@/models/SiloManagementModal';
import { RatioSettingsModal } from '@/models/RatioSettingsModal';
import {
  PerformanceModal,
  TemplateModal,
  PreviewRatioModal,
} from '@/features/ratio-management/components/ratio-v2/modals/ratio-modals';
import { RatioCheckStandardModal } from '@/features/ratio-management/components/ratio-v2/ratio-check-standard-modal';
import { AIRatioGenerator } from '@/features/ratio-management/components/ratio-v2/ai-ratio-generator';
import { RatioRecommendationModal } from '@/features/ratio-management/components/ratio-v2/ratio-recommendation-modal';
import { RatioNotificationModal } from '@/features/ratio-management/components/ratio-v2/ratio-notification-modal';
import { SaveBackupRatioModal } from '@/features/ratio-management/components/ratio-v2/save-backup-ratio-modal';
import { BackupRatioSelectionModal } from '@/features/ratio-management/components/ratio-v2/backup-ratio-selection-modal';

// 数据导入
import { generateMockRatioSelectionRecords } from '@/infrastructure/api/mock/ratio-mock-data';
import {
  generateMockSiloMappings,
  generateMockRatioHistory,
  generateAvailableMaterials,
} from '@/core/utils/ratio-mock-data';

interface RatioModalManagerProps {
  // 模态框状态
  modals: RatioModalsState;
  onModalChange: (modalName: keyof RatioModalsState, open: boolean) => void;

  // 基础数据
  taskId: string;
  task: Task | null;
  selectedMaterials: any[];
  calculationResult: any | null;

  // 事件处理器
  onAIGenerate: (config: any) => void;
  onAIRecommendation: (params: any, materials: any[]) => void;
  onApplyNotification: (result: any) => void;
  onSaveBackupRatio: (request: any) => Promise<any>;
  onApplyBackupRatio: (ratioId: string) => Promise<boolean>;
  onGenerateDefaultBackupName: () => string;
  onGenerateRatioSummary: () => any;
}

/**
 * 配比页面模态框管理器组件
 */
export const RatioModalManager: React.FC<RatioModalManagerProps> = ({
  modals,
  onModalChange,
  taskId,
  task,
  selectedMaterials,
  calculationResult,
  onAIGenerate,
  onAIRecommendation,
  onApplyNotification,
  onSaveBackupRatio,
  onApplyBackupRatio,
  onGenerateDefaultBackupName,
  onGenerateRatioSummary,
}) => {
  return (
    <>
      {/* 性能仪表板模态框 */}
      <PerformanceModal
        open={modals.performance}
        onOpenChange={open => onModalChange('performance', open)}
      />

      {/* 砂浆配比模态框 */}
      <MortarRatioModal
        isOpen={modals.mortarRatio}
        onOpenChangeAction={(open: boolean) => onModalChange('mortarRatio', open)}
      />

      {/* 配比历史模态框 */}
      <RatioHistoryModal
        isOpen={modals.history}
        onOpenChangeAction={(open: boolean) => onModalChange('history', open)}
        task={task}
        history={task ? generateMockRatioHistory(task.id) : []}
      />

      {/* 模板管理模态框 */}
      <TemplateModal
        open={modals.template}
        onOpenChange={open => onModalChange('template', open)}
      />

      {/* 料仓管理模态框 */}
      <SiloManagementModal
        isOpen={modals.siloManagement}
        onOpenChangeAction={(open: boolean) => onModalChange('siloManagement', open)}
        siloMappings={generateMockSiloMappings()}
      />

      {/* 配比设置模态框 */}
      <RatioSettingsModal
        isOpen={modals.settings}
        onOpenChangeAction={(open: boolean) => onModalChange('settings', open)}
      />

      {/* 配比预览模态框 */}
      <PreviewRatioModal
        open={modals.previewRatio}
        onOpenChange={open => onModalChange('previewRatio', open)}
        task={task}
        materials={selectedMaterials}
        calculationResults={calculationResult}
      />

      {/* 标准检查模态框 */}
      <RatioCheckStandardModal
        isOpen={modals.checkStandard}
        onClose={() => onModalChange('checkStandard', false)}
      />

      {/* 配比选择模态框 */}
      <RatioSelectionModal
        isOpen={modals.ratioSelection}
        onOpenChange={(open: boolean) => onModalChange('ratioSelection', open)}
        onSelectRatio={(ratio: any) => {
          console.log('应用配比:', ratio);
          // 这里可以添加配比应用逻辑
        }}
        ratioRecords={generateMockRatioSelectionRecords()}
        currentTaskId={taskId}
      />

      {/* AI配比生成器 */}
      <AIRatioGenerator
        isOpen={modals.aiGenerate}
        onClose={() => onModalChange('aiGenerate', false)}
        onGenerate={config => {
          onAIGenerate(config);
          onModalChange('aiGenerate', false);
        }}
        availableMaterials={generateAvailableMaterials()}
      />

      {/* 配比推荐模态框 */}
      <RatioRecommendationModal
        isOpen={modals.ratioRecommendation}
        onClose={() => onModalChange('ratioRecommendation', false)}
        onApply={(params, materials) => {
          onAIRecommendation(params, materials);
          onModalChange('ratioRecommendation', false);
        }}
      />

      {/* 配比通知单模态框 */}
      <RatioNotificationModal
        isOpen={modals.ratioNotification}
        onClose={() => onModalChange('ratioNotification', false)}
        onApply={onApplyNotification}
      />

      {/* 存为备选配比模态框 */}
      <SaveBackupRatioModal
        isOpen={modals.saveBackupRatio}
        onClose={() => onModalChange('saveBackupRatio', false)}
        onSave={onSaveBackupRatio}
        defaultName={onGenerateDefaultBackupName()}
        ratioSummary={onGenerateRatioSummary()}
      />

      {/* 备选配比选择模态框 */}
      <BackupRatioSelectionModal
        isOpen={modals.backupRatioSelection}
        onClose={() => onModalChange('backupRatioSelection', false)}
        onSelect={async (ratioId: string) => {
          const success = await onApplyBackupRatio(ratioId);
          if (success) {
            onModalChange('backupRatioSelection', false);
          }
        }}
        taskId={taskId}
      />
    </>
  );
};
