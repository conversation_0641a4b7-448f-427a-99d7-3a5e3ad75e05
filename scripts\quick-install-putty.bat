@echo off
chcp 65001 >nul
echo 🔧 TMH任务调度系统 - 快速安装PuTTY
echo.

echo 📋 PuTTY工具用于SSH密码自动认证
echo 🎯 安装后可实现完全自动化部署
echo.

REM 检查是否已安装
where plink >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ PuTTY已安装
    plink -V
    echo.
    echo 现在可以运行: npm run deploy:intranet
    pause
    exit /b 0
)

echo 📦 开始安装PuTTY...
echo.

REM 方法1: 使用winget安装
echo 🔄 尝试使用winget安装...
winget install PuTTY.PuTTY --accept-source-agreements --accept-package-agreements >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ winget安装成功
    goto :verify
)

REM 方法2: 使用Chocolatey安装
echo 🔄 尝试使用Chocolatey安装...
choco install putty -y >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Chocolatey安装成功
    goto :verify
)

REM 方法3: 手动下载安装
echo 🔄 尝试手动下载安装...
powershell -Command "& {
    try {
        $url = 'https://the.earth.li/~sgtatham/putty/latest/w64/putty.zip'
        $temp = '$env:TEMP\putty.zip'
        $dest = '$env:ProgramFiles\PuTTY'
        
        Write-Host '📥 下载PuTTY...'
        Invoke-WebRequest -Uri $url -OutFile $temp -UseBasicParsing
        
        Write-Host '📦 解压安装...'
        Expand-Archive -Path $temp -DestinationPath $dest -Force
        
        Write-Host '📝 添加到PATH...'
        $currentPath = [Environment]::GetEnvironmentVariable('PATH', 'Machine')
        if ($currentPath -notlike '*$dest*') {
            [Environment]::SetEnvironmentVariable('PATH', '$currentPath;$dest', 'Machine')
        }
        
        Remove-Item $temp -Force
        Write-Host '✅ 手动安装完成'
        exit 0
    } catch {
        Write-Host '❌ 手动安装失败'
        exit 1
    }
}"

if %errorlevel% == 0 (
    echo ✅ 手动安装成功
    goto :verify
)

echo ❌ 所有自动安装方法都失败
echo.
echo 📋 请手动安装PuTTY:
echo 1. 访问: https://www.putty.org/
echo 2. 下载Windows版本
echo 3. 安装到系统PATH中
echo.
echo 或者使用SSH密钥认证: npm run setup:ssh
pause
exit /b 1

:verify
echo.
echo 🔍 验证安装...
refreshenv >nul 2>&1

REM 刷新PATH环境变量
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "PATH=%%b"

where plink >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ PuTTY安装验证成功
    plink -V
    echo.
    echo 🎉 安装完成! 现在可以使用:
    echo    npm run deploy:intranet
    echo.
    echo ✅ SSH密码将自动处理，无需手动输入
) else (
    echo ⚠️ 安装可能不完整，请重启命令行后重试
    echo 或手动将PuTTY添加到PATH环境变量
)

echo.
pause
