import React from 'react';

import { cn } from '@/core/lib/utils';

interface SectionContainerProps {
  title: string;
  actionButtons?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  contentClassName?: string;
  containerRef?: React.Ref<HTMLDivElement>;
  fabRender?: React.ReactNode;
  hideHeader?: boolean;
}

export function SectionContainer({
  title,
  actionButtons,
  children,
  className,
  contentClassName,
  containerRef,
  fabRender,
  hideHeader,
}: SectionContainerProps) {
  // const defaultContentClasses = "flex-grow";
  // const paddingClasses = contentClassName ? "" : "p-1"; // If contentClassName is provided, it should handle its own padding or use "p-0" etc.

  return (
    <div
      ref={containerRef}
      className={cn('relative flex flex-col h-full border bg-card rounded-lg shadow-sm', className)}
    >
      {!hideHeader && (
        <div
          className='border-b py-0.5 px-2 flex-shrink-0'
          style={{ backgroundColor: 'hsl(var(--block-title))' }}
        >
          <div className='flex ml-2 items-center justify-between'>
            <h6 className='text-sm font-medium'>{title}</h6>
            {actionButtons && <div className='flex items-center space-x-0.5'>{actionButtons}</div>}
          </div>
        </div>
      )}
      <div className={cn('flex-1 min-h-0', 'overflow-auto', contentClassName)}>{children}</div>

      {fabRender}
    </div>
  );
}
