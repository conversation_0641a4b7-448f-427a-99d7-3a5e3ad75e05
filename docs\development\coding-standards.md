# 📝 开发规范

## 🎯 规范目标

本文档定义了TMH车辆调度系统的代码规范和最佳实践，旨在：
- 提高代码质量和可维护性
- 确保团队开发的一致性
- 减少代码审查时间
- 降低新成员上手难度

## 📁 文件和目录命名

### 目录命名
- 使用 `kebab-case` (短横线分隔)
- 名称要简洁明了，体现功能

```
✅ 正确示例
src/components/task-list/
src/hooks/vehicle-dispatch/
src/utils/performance/

❌ 错误示例
src/components/TaskList/
src/hooks/vehicleDispatch/
src/utils/Performance/
```

### 文件命名
- **组件文件**: 使用 `PascalCase`
- **Hook文件**: 使用 `camelCase`，以 `use` 开头
- **工具文件**: 使用 `camelCase`
- **类型文件**: 使用 `camelCase`，以 `.types.ts` 结尾

```typescript
✅ 正确示例
TaskListContainer.tsx        // 组件
useTaskListSettings.ts       // Hook
taskGrouping.ts             // 工具函数
task.types.ts               // 类型定义

❌ 错误示例
task-list-container.tsx
UseTaskListSettings.ts
TaskGrouping.ts
TaskTypes.ts
```

## 🧩 组件开发规范

### 组件结构
```typescript
// 1. 导入语句（按顺序）
import React from 'react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useTaskList } from '@/hooks/useTaskList';
import { cn } from '@/core/lib/utils';
import type { Task } from '@/types';

// 2. 类型定义
interface TaskCardProps {
  task: Task;
  onSelect?: (task: Task) => void;
  className?: string;
}

// 3. 主组件
export function TaskCard({ task, onSelect, className }: TaskCardProps) {
  // 3.1 状态定义
  const [isSelected, setIsSelected] = useState(false);
  
  // 3.2 Hook调用
  const { updateTask } = useTaskList();
  
  // 3.3 副作用
  useEffect(() => {
    // 副作用逻辑
  }, []);
  
  // 3.4 事件处理函数
  const handleClick = () => {
    setIsSelected(!isSelected);
    onSelect?.(task);
  };
  
  // 3.5 渲染逻辑
  return (
    <div className={cn('task-card', className)}>
      {/* 组件内容 */}
    </div>
  );
}

// 4. 默认导出
export default TaskCard;
```

### 组件命名规范
- 组件名使用 `PascalCase`
- 文件名与组件名保持一致
- 避免使用缩写，使用完整的描述性名称

```typescript
✅ 正确示例
TaskListContainer
VehicleDispatchCard
RatioManagementPanel

❌ 错误示例
TLContainer
VDCard
RMPanel
```

### Props 设计原则
- 使用 TypeScript 接口定义 Props
- 提供合理的默认值
- 使用可选属性时添加 `?` 标记

```typescript
interface ComponentProps {
  // 必需属性
  id: string;
  title: string;
  
  // 可选属性
  className?: string;
  disabled?: boolean;
  
  // 事件处理
  onClick?: (event: MouseEvent) => void;
  onSubmit?: (data: FormData) => Promise<void>;
}
```

## 🪝 Hook 开发规范

### Hook 命名
- 以 `use` 开头
- 使用 `camelCase`
- 名称要体现功能

```typescript
✅ 正确示例
useTaskList
useVehicleDispatch
usePerformanceMonitor

❌ 错误示例
taskListHook
getTaskList
TaskListHook
```

### Hook 结构
```typescript
import { useState, useEffect, useCallback } from 'react';
import type { Task } from '@/types';

interface UseTaskListOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseTaskListReturn {
  tasks: Task[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  addTask: (task: Omit<Task, 'id'>) => Promise<void>;
  updateTask: (id: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
}

export function useTaskList(options: UseTaskListOptions = {}): UseTaskListReturn {
  const { autoRefresh = false, refreshInterval = 30000 } = options;
  
  // 状态定义
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 方法定义
  const refresh = useCallback(async () => {
    // 刷新逻辑
  }, []);
  
  const addTask = useCallback(async (task: Omit<Task, 'id'>) => {
    // 添加任务逻辑
  }, []);
  
  // 副作用
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(refresh, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, refresh]);
  
  return {
    tasks,
    loading,
    error,
    refresh,
    addTask,
    updateTask,
    deleteTask,
  };
}
```

## 🏪 状态管理规范

### Zustand Store 结构
```typescript
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { Task } from '@/types';

interface TaskListState {
  // 状态数据
  tasks: Task[];
  selectedTasks: string[];
  loading: boolean;
  error: string | null;
  
  // 同步操作
  setTasks: (tasks: Task[]) => void;
  selectTask: (taskId: string) => void;
  deselectTask: (taskId: string) => void;
  clearSelection: () => void;
  
  // 异步操作
  fetchTasks: () => Promise<void>;
  createTask: (task: Omit<Task, 'id'>) => Promise<void>;
  updateTask: (id: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
}

export const useTaskListStore = create<TaskListState>()(
  immer((set, get) => ({
    // 初始状态
    tasks: [],
    selectedTasks: [],
    loading: false,
    error: null,
    
    // 同步操作
    setTasks: (tasks) => set((state) => {
      state.tasks = tasks;
    }),
    
    selectTask: (taskId) => set((state) => {
      if (!state.selectedTasks.includes(taskId)) {
        state.selectedTasks.push(taskId);
      }
    }),
    
    // 异步操作
    fetchTasks: async () => {
      set((state) => {
        state.loading = true;
        state.error = null;
      });
      
      try {
        const tasks = await taskService.fetchTasks();
        set((state) => {
          state.tasks = tasks;
          state.loading = false;
        });
      } catch (error) {
        set((state) => {
          state.error = error.message;
          state.loading = false;
        });
      }
    },
  }))
);
```

## 🎨 样式规范

### Tailwind CSS 使用规范
- 优先使用 Tailwind 原子类
- 复杂样式使用 `cn()` 函数组合
- 避免内联样式

```typescript
import { cn } from '@/core/lib/utils';

// ✅ 正确使用
<div className={cn(
  'flex items-center justify-between',
  'p-4 rounded-lg border',
  'hover:bg-gray-50 transition-colors',
  isSelected && 'bg-blue-50 border-blue-200',
  className
)}>

// ❌ 避免使用
<div style={{ 
  display: 'flex', 
  padding: '16px',
  backgroundColor: isSelected ? '#eff6ff' : 'transparent'
}}>
```

### CSS 变量使用
```css
/* 使用 CSS 变量定义主题色彩 */
:root {
  --primary: 222.2 84% 4.9%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
}

/* 在组件中使用 */
.custom-component {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}
```

## 📝 注释规范

### 组件注释
```typescript
/**
 * 任务卡片组件
 * 
 * 用于显示单个任务的详细信息，支持选择、编辑等操作
 * 
 * @param task - 任务数据
 * @param onSelect - 选择任务时的回调函数
 * @param className - 额外的CSS类名
 * 
 * @example
 * ```tsx
 * <TaskCard 
 *   task={task} 
 *   onSelect={(task) => console.log('Selected:', task)}
 *   className="mb-4"
 * />
 * ```
 */
export function TaskCard({ task, onSelect, className }: TaskCardProps) {
  // 组件实现
}
```

### 函数注释
```typescript
/**
 * 计算任务的优先级分数
 * 
 * 基于任务的紧急程度、重要性和截止时间计算优先级分数
 * 分数越高表示优先级越高
 * 
 * @param task - 任务对象
 * @returns 优先级分数 (0-100)
 */
function calculateTaskPriority(task: Task): number {
  // 计算逻辑
}
```

## 🔧 工具函数规范

### 纯函数优先
```typescript
// ✅ 纯函数 - 推荐
export function formatTaskDate(date: Date): string {
  return date.toLocaleDateString('zh-CN');
}

// ❌ 有副作用的函数 - 避免
export function updateTaskInDOM(taskId: string, data: Task): void {
  const element = document.getElementById(taskId);
  element.textContent = data.title;
}
```

### 类型安全
```typescript
// ✅ 类型安全的工具函数
export function groupTasksByStatus<T extends Task>(
  tasks: T[]
): Record<TaskStatus, T[]> {
  return tasks.reduce((groups, task) => {
    const status = task.status;
    if (!groups[status]) {
      groups[status] = [];
    }
    groups[status].push(task);
    return groups;
  }, {} as Record<TaskStatus, T[]>);
}
```

## 🧪 测试规范

### 测试文件命名
```
src/components/TaskCard.tsx
src/components/__tests__/TaskCard.test.tsx

src/hooks/useTaskList.ts
src/hooks/__tests__/useTaskList.test.ts
```

### 测试结构
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { TaskCard } from '../TaskCard';
import type { Task } from '@/types';

describe('TaskCard', () => {
  const mockTask: Task = {
    id: '1',
    title: '测试任务',
    status: 'pending',
    priority: 'high',
  };

  it('应该正确渲染任务信息', () => {
    render(<TaskCard task={mockTask} />);
    
    expect(screen.getByText('测试任务')).toBeInTheDocument();
    expect(screen.getByText('高优先级')).toBeInTheDocument();
  });

  it('应该在点击时触发选择回调', () => {
    const onSelect = jest.fn();
    render(<TaskCard task={mockTask} onSelect={onSelect} />);
    
    fireEvent.click(screen.getByRole('button'));
    
    expect(onSelect).toHaveBeenCalledWith(mockTask);
  });
});
```

## 📋 代码审查清单

### 提交前检查
- [ ] 代码通过 ESLint 检查
- [ ] 代码通过 TypeScript 类型检查
- [ ] 代码格式符合 Prettier 规范
- [ ] 添加了必要的测试用例
- [ ] 更新了相关文档

### 审查要点
- [ ] 组件职责单一，逻辑清晰
- [ ] 状态管理合理，避免过度设计
- [ ] 性能考虑，避免不必要的重渲染
- [ ] 错误处理完善
- [ ] 可访问性支持

---

**文档版本**: v1.0  
**最后更新**: 2025/7/16  
**维护者**: TMH开发团队
