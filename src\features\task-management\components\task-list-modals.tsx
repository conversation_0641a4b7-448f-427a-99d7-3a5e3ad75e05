// src/components/sections/task-list/task-list-modals.tsx
'use client';

import React, { FC } from 'react';

import { ColumnSpecificStyleModal } from '@/models/column-specific-style-modal';
import { ColumnVisibilityModal } from '@/models/column-visibility-modal';
import { DeliveryOrderDetailsModal } from '@/models/delivery-order-details-modal';
import { QRCodeModal } from '@/models/qr-code-modal';
import { TankerDispatchNoteModal } from '@/models/tanker-dispatch-note-modal';
import { TaskAbbreviationModal } from '@/models/task-abbreviation-modal';
import { TaskReminderConfigModal } from '@/models/task-reminder-config-modal';
import { VehicleCardStylerModal } from '@/models/vehicle-card-styler-modal';
import {
  ColumnTextStyle,
  CustomColumnDefinition,
  InTaskVehicleCardStyle,
  StyleableColumnId,
  Task,
  TaskListStoredSettings,
  Vehicle,
} from '@/core/types';

// src/components/sections/task-list/task-list-modals.tsx

interface TaskListModalsProps {
  isTankerNoteModalOpen: boolean;
  closeTankerNoteModalAction: () => void;
  selectedTaskForTankerNote: Task | null;
  isColumnVisibilityModalOpen: boolean;
  closeColumnVisibilityModalAction: () => void;
  allColumns: CustomColumnDefinition[];
  columnVisibility: Record<string, boolean>;
  handleColumnVisibilityChangeAction: (columnId: string, checked: boolean) => void;
  currentOrder: string[];
  handleColumnOrderChangeAction: (newOrder: string[]) => void;
  isColumnSpecificStyleModalOpen: boolean;
  closeColumnSpecificStyleModalAction: () => void;
  editingColumnDef: CustomColumnDefinition | null;
  columnTextStyles: Record<StyleableColumnId, ColumnTextStyle | undefined>;
  columnBackgrounds: Record<string, string>;
  handleColumnTextStyleChangeAction: (
    columnId: StyleableColumnId,
    property: keyof ColumnTextStyle,
    value: string
  ) => void;
  handleColumnBackgroundChangeAction: (
    columnId: StyleableColumnId,
    backgroundColor: string
  ) => void;
  isStyleEditorModalOpen: boolean;
  closeStyleEditorModalAction: () => void;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  updateSettingAction: <K extends keyof TaskListStoredSettings>(
    key: K,
    value: TaskListStoredSettings[K]
  ) => void;
  onVehiclesPerRowChangeAction?: (vehiclesPerRow: 2 | 3 | 4 | 5 | 6 | 7 | 8) => void;
  isDeliveryOrderDetailsModalOpen: boolean;
  closeDeliveryOrderDetailsModalAction: () => void;
  selectedVehicleForDeliveryOrder: Vehicle | null;
  selectedTaskForDeliveryOrder: Task | null;
  isReminderConfigModalOpen: boolean;
  closeReminderConfigModalAction: () => void;
  selectedTaskForReminderConfig: Task | null;
  isQRCodeModalOpen: boolean;
  closeQRCodeModalAction: () => void;
  selectedTaskForQRCode: Task | null;
  isTaskAbbreviationModalOpen: boolean;
  closeTaskAbbreviationModalAction: () => void;
  selectedTaskForAbbreviation: Task | null;
  handleSaveAbbreviationAction: (taskId: string, abbreviation: string) => Promise<void>;
  updateSetting?: <K extends keyof TaskListStoredSettings>(
    key: K,
    value: TaskListStoredSettings[K]
  ) => void;
}
export const TaskListModals: FC<TaskListModalsProps> = ({
  isTankerNoteModalOpen,
  closeTankerNoteModalAction,
  selectedTaskForTankerNote,
  isColumnVisibilityModalOpen,
  closeColumnVisibilityModalAction,
  allColumns,
  columnVisibility,
  handleColumnVisibilityChangeAction,
  currentOrder,
  handleColumnOrderChangeAction,
  isColumnSpecificStyleModalOpen,
  closeColumnSpecificStyleModalAction,
  editingColumnDef,
  columnTextStyles,
  columnBackgrounds,
  handleColumnTextStyleChangeAction,
  handleColumnBackgroundChangeAction,
  isStyleEditorModalOpen,
  closeStyleEditorModalAction,
  inTaskVehicleCardStyles,
  updateSettingAction,
  isDeliveryOrderDetailsModalOpen,
  closeDeliveryOrderDetailsModalAction,
  selectedVehicleForDeliveryOrder,
  selectedTaskForDeliveryOrder,
  isReminderConfigModalOpen,
  closeReminderConfigModalAction,
  selectedTaskForReminderConfig,
  isQRCodeModalOpen,
  closeQRCodeModalAction,
  selectedTaskForQRCode,
  isTaskAbbreviationModalOpen,
  closeTaskAbbreviationModalAction,
  selectedTaskForAbbreviation,
  handleSaveAbbreviationAction,
  onVehiclesPerRowChangeAction,
}: TaskListModalsProps) => {
  return (
    <>
      {/* 各种模态窗口 */}
      <TankerDispatchNoteModal
        isOpen={isTankerNoteModalOpen}
        onOpenChangeAction={closeTankerNoteModalAction}
        task={selectedTaskForTankerNote}
      />

      <ColumnVisibilityModal
        isOpen={isColumnVisibilityModalOpen}
        onOpenChangeAction={closeColumnVisibilityModalAction}
        allColumns={allColumns}
        columnVisibility={columnVisibility}
        onColumnVisibilityChangeAction={handleColumnVisibilityChangeAction}
        currentOrder={currentOrder}
        onOrderChangeAction={handleColumnOrderChangeAction}
      />

      <ColumnSpecificStyleModal
        isOpen={isColumnSpecificStyleModalOpen}
        onOpenChangeAction={closeColumnSpecificStyleModalAction}
        columnDef={editingColumnDef}
        currentTextStyles={
          editingColumnDef ? columnTextStyles[editingColumnDef.id as StyleableColumnId] : undefined
        }
        currentBackgroundSetting={
          editingColumnDef ? columnBackgrounds[editingColumnDef.id] : undefined
        }
        onTextStyleChangeAction={(property, value) => {
          if (editingColumnDef) {
            handleColumnTextStyleChangeAction(
              editingColumnDef.id as StyleableColumnId,
              property,
              value
            );
          }
        }}
        onBackgroundColorChangeAction={value => {
          if (editingColumnDef) {
            handleColumnBackgroundChangeAction(editingColumnDef.id as StyleableColumnId, value);
          }
        }}
      />

      <VehicleCardStylerModal
        isOpen={isStyleEditorModalOpen}
        onOpenChangeAction={closeStyleEditorModalAction}
        currentStyles={inTaskVehicleCardStyles}
        onStylesChangeAction={newStyles => {
          updateSettingAction('inTaskVehicleCardStyles', newStyles);
        }}
        onVehiclesPerRowChangeAction={onVehiclesPerRowChangeAction}
      />

      <DeliveryOrderDetailsModal
        isOpen={isDeliveryOrderDetailsModalOpen}
        onOpenChangeAction={closeDeliveryOrderDetailsModalAction}
        vehicle={selectedVehicleForDeliveryOrder}
        task={selectedTaskForDeliveryOrder}
      />

      <TaskReminderConfigModal
        isOpen={isReminderConfigModalOpen}
        onOpenChangeAction={closeReminderConfigModalAction}
        task={selectedTaskForReminderConfig}
      />

      <QRCodeModal
        isOpen={isQRCodeModalOpen}
        onOpenChangeAction={closeQRCodeModalAction}
        task={selectedTaskForQRCode}
      />

      <TaskAbbreviationModal
        isOpen={isTaskAbbreviationModalOpen}
        onOpenChangeAction={closeTaskAbbreviationModalAction}
        task={selectedTaskForAbbreviation}
        onSaveAction={handleSaveAbbreviationAction}
      />
    </>
  );
};
