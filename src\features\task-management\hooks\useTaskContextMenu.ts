// src/hooks/useTaskContextMenu.ts
'use client';

import { useCallback, useState } from 'react';

import type { Task } from '@/core/types';

// src/hooks/useTaskContextMenu.ts

interface TaskContextMenuData {
  taskId: string;
}

interface UseTaskContextMenuReturn {
  // Context Menu state
  isTaskContextMenuOpen: boolean;
  taskContextMenuPosition: { x: number; y: number } | null;
  contextMenuTaskData: TaskContextMenuData | null; // Renamed for clarity
  openTaskContextMenu: (event: React.MouseEvent, taskId: string) => void;
  closeTaskContextMenu: () => void;

  // Tanker Dispatch Note Modal state
  isTankerNoteModalOpen: boolean;
  selectedTaskForTankerNote: Task | null;
  openTankerNoteModal: (task: Task) => void;
  closeTankerNoteModal: () => void;

  // 新增: 提醒设置模态窗口状态
  isReminderConfigModalOpen: boolean;
  selectedTaskForReminderConfig: Task | null;
  openReminderConfigModal: (task: Task) => void;
  closeReminderConfigModal: () => void;
}

export function useTaskContextMenu(): UseTaskContextMenuReturn {
  const [isTaskContextMenuOpen, setIsTaskContextMenuOpen] = useState(false);
  const [taskContextMenuPosition, setTaskContextMenuPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [contextMenuTaskData, setContextMenuTaskData] = useState<TaskContextMenuData | null>(null);

  const [isTankerNoteModalOpen, setIsTankerNoteModalOpen] = useState(false);
  const [selectedTaskForTankerNote, setSelectedTaskForTankerNote] = useState<Task | null>(null);

  // 新增: 提醒设置模态窗口状态
  const [isReminderConfigModalOpen, setIsReminderConfigModalOpen] = useState(false);
  const [selectedTaskForReminderConfig, setSelectedTaskForReminderConfig] = useState<Task | null>(
    null
  );

  const openTaskContextMenu = useCallback((event: React.MouseEvent, taskId: string) => {
    event.preventDefault();
    event.stopPropagation();
    setTaskContextMenuPosition({ x: event.clientX, y: event.clientY });
    setContextMenuTaskData({ taskId });
    setIsTaskContextMenuOpen(true);
  }, []);

  const closeTaskContextMenu = useCallback(() => {
    setIsTaskContextMenuOpen(false);
    // Position and data will be reset on next open
  }, []);

  const openTankerNoteModal = useCallback((task: Task) => {
    setSelectedTaskForTankerNote(task);
    setIsTankerNoteModalOpen(true);
  }, []);

  const closeTankerNoteModal = useCallback(() => {
    setIsTankerNoteModalOpen(false);
    setSelectedTaskForTankerNote(null); // Clear selected task when closing
  }, []);

  // 新增: 提醒设置相关方法
  const openReminderConfigModal = useCallback((task: Task) => {
    setSelectedTaskForReminderConfig(task);
    setIsReminderConfigModalOpen(true);
  }, []);

  const closeReminderConfigModal = useCallback(() => {
    setIsReminderConfigModalOpen(false);
    setSelectedTaskForReminderConfig(null); // Clear selected task when closing
  }, []);

  return {
    isTaskContextMenuOpen,
    taskContextMenuPosition,
    contextMenuTaskData,
    openTaskContextMenu,
    closeTaskContextMenu,
    isTankerNoteModalOpen,
    selectedTaskForTankerNote,
    openTankerNoteModal,
    closeTankerNoteModal,
    // 新增: 提醒设置相关返回值
    isReminderConfigModalOpen,
    selectedTaskForReminderConfig,
    openReminderConfigModal,
    closeReminderConfigModal,
  };
}
