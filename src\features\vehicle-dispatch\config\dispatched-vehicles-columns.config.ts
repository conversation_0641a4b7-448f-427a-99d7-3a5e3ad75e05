// src/features/vehicle-dispatch/config/dispatched-vehicles-columns.config.ts

/**
 * 已出厂车辆列ID类型
 */
export type DispatchedVehicleColumnId =
  | 'vehicleNumberWithStatus'
  | 'driver'
  | 'departureTime'
  | 'arrivalTime'
  | 'siteStayDuration'
  | 'returnTime'
  | 'outboundDuration'
  | 'returnDuration'
  | 'totalDuration'
  | 'deliveryOrderNumber';

/**
 * 已出厂车辆列配置接口
 */
export interface DispatchedVehicleColumnDefinition {
  id: DispatchedVehicleColumnId;
  label: string;
  defaultVisible: boolean;
  isReorderable: boolean;
  isResizable: boolean;
  defaultWidth: number;
  minWidth: number;
  maxWidth?: number;
  isStyleable: boolean;
  isMandatory?: boolean;
  order: number;
  densityStyles?: {
    compact?: {
      fontSize?: string;
      padding?: string;
      cellFontSize?: string;
      cellPadding?: string;
    };
    normal?: {
      fontSize?: string;
      padding?: string;
      cellFontSize?: string;
      cellPadding?: string;
    };
    comfortable?: {
      fontSize?: string;
      padding?: string;
      cellFontSize?: string;
      cellPadding?: string;
    };
  };
}

/**
 * 已出厂车辆表格列配置
 * 包含车号（往返状态图标+车号）、司机、出站时间、到达时间、工地时长、返程时间、去程时长、返程时长、总时长、发货单编号
 */
export const DISPATCHED_VEHICLES_COLUMNS_CONFIG: DispatchedVehicleColumnDefinition[] = [
  {
    id: 'vehicleNumberWithStatus',
    label: '车号',
    defaultVisible: true,
    isReorderable: true,
    isResizable: true,
    defaultWidth: 64, // w-16 equivalent
    minWidth: 64,
    maxWidth: 64,
    isStyleable: true,
    isMandatory: true,
    order: 1,
    densityStyles: {
      compact: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      normal: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      comfortable: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
    },
  },
  {
    id: 'driver',
    label: '司机',
    defaultVisible: true,
    isReorderable: true,
    isResizable: true,
    defaultWidth: 64,
    minWidth: 64,
    maxWidth: 64,
    isStyleable: true,
    order: 2,
    densityStyles: {
      compact: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      normal: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      comfortable: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
    },
  },
  {
    id: 'departureTime',
    label: '出站时间',
    defaultVisible: true,
    isReorderable: true,
    isResizable: true,
    defaultWidth: 64,
    minWidth: 64,
    maxWidth: 64,
    isStyleable: true,
    order: 3,
    densityStyles: {
      compact: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      normal: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      comfortable: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
    },
  },
  {
    id: 'arrivalTime',
    label: '到达时间',
    defaultVisible: true,
    isReorderable: true,
    isResizable: true,
    defaultWidth: 64,
    minWidth: 64,
    maxWidth: 64,
    isStyleable: true,
    order: 4,
    densityStyles: {
      compact: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      normal: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      comfortable: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
    },
  },
  {
    id: 'siteStayDuration',
    label: '工地时长',
    defaultVisible: true,
    isReorderable: true,
    isResizable: true,
    defaultWidth: 64,
    minWidth: 64,
    maxWidth: 64,
    isStyleable: true,
    order: 5,
    densityStyles: {
      compact: { fontSize: 'text-xs', padding: 'py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      normal: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      comfortable: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
    },
  },
  {
    id: 'returnTime',
    label: '返程时间',
    defaultVisible: true,
    isReorderable: true,
    isResizable: true,
    defaultWidth: 64,
    minWidth: 64,
    maxWidth: 64,
    isStyleable: true,
    order: 6,
    densityStyles: {
      compact: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      normal: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      comfortable: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
    },
  },
  {
    id: 'outboundDuration',
    label: '去程时长',
    defaultVisible: true,
    isReorderable: true,
    isResizable: true,
    defaultWidth: 64,
    minWidth: 64,
    maxWidth: 64,
    isStyleable: true,
    order: 7,
    densityStyles: {
      compact: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      normal: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      comfortable: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
    },
  },
  {
    id: 'returnDuration',
    label: '返程时长',
    defaultVisible: true,
    isReorderable: true,
    isResizable: true,
    defaultWidth: 64,
    minWidth: 64,
    maxWidth: 64,
    isStyleable: true,
    order: 8,
    densityStyles: {
      compact: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      normal: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      comfortable: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
    },
  },
  {
    id: 'totalDuration',
    label: '总时长',
    defaultVisible: true,
    isReorderable: true,
    isResizable: true,
    defaultWidth: 64,
    minWidth: 64,
    maxWidth: 64,
    isStyleable: true,
    order: 9,
    densityStyles: {
      compact: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      normal: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      comfortable: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
    },
  },
  {
    id: 'deliveryOrderNumber',
    label: '发货单编号',
    defaultVisible: true,
    isReorderable: true,
    isResizable: true,
    defaultWidth: 64,
    minWidth: 64,
    maxWidth: 64,
    isStyleable: true,
    order: 10,
    densityStyles: {
      compact: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      normal: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
      comfortable: { fontSize: 'text-xs', padding: 'px-1 py-0.5', cellFontSize: 'text-xs', cellPadding: 'px-1 py-0.5' },
    },
  },
];

/**
 * 获取默认列顺序
 */
export const getDefaultDispatchedVehiclesColumnOrder = (): string[] => {
  return DISPATCHED_VEHICLES_COLUMNS_CONFIG
    .sort((a, b) => a.order - b.order)
    .map(col => col.id);
};

/**
 * 获取默认列可见性
 */
export const getDefaultDispatchedVehiclesColumnVisibility = (): Record<string, boolean> => {
  return DISPATCHED_VEHICLES_COLUMNS_CONFIG.reduce((acc, col) => {
    acc[col.id] = col.defaultVisible;
    return acc;
  }, {} as Record<string, boolean>);
};

/**
 * 获取默认列宽度
 */
export const getDefaultDispatchedVehiclesColumnWidths = (): Record<string, number> => {
  return DISPATCHED_VEHICLES_COLUMNS_CONFIG.reduce((acc, col) => {
    acc[col.id] = col.defaultWidth || 100;
    return acc;
  }, {} as Record<string, number>);
};
