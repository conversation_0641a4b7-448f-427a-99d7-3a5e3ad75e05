'use client';

import React, { useState } from 'react';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BarChart3,
  CheckCircle,
  Container,
  Download,
  Droplets,
  Eye,
  Lightbulb,
  Mountain,
  Settings,
  Share,
  Target,
  TrendingUp,
  Zap,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { But<PERSON> } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Progress } from '@/shared/components/progress';
import type { Task } from '@/core/types';
import type { CalculationResults, OptimizationAction } from '@/core/types/ratio';

import { OneClickOptimizer } from './one-click-optimizer';

interface RatioResultsPanelProps {
  calculationResults: CalculationResults | null;
  isCalculating: boolean;
  task?: Task | null;
  onOptimize?: (selectedIds: string[]) => void;
  onPreviewRatio?: () => void;
}

function MaterialChart({ materials }: { materials: Record<string, number> }) {
  const materialData = Object.entries(materials || {}).map(([name, amount]) => ({
    name,
    amount,
    percentage: 0, // 将在组件中计算
  }));

  const total = materialData.reduce((sum, item) => sum + item.amount, 0);
  materialData.forEach(item => {
    item.percentage = total > 0 ? (item.amount / total) * 100 : 0;
  });

  const getIcon = (name: string) => {
    if (name.includes('水泥')) return <Container className='h-4 w-4 text-gray-600' />;
    if (name.includes('水')) return <Droplets className='h-4 w-4 text-blue-600' />;
    if (name.includes('砂')) return <Mountain className='h-4 w-4 text-yellow-600' />;
    if (name.includes('石')) return <Mountain className='h-4 w-4 text-stone-600' />;
    return <Container className='h-4 w-4 text-gray-600' />;
  };

  return (
    <div className='space-y-3'>
      {materialData.map((item, index) => (
        <div key={index} className='space-y-1'>
          <div className='flex items-center justify-between text-sm'>
            <div className='flex items-center gap-2'>
              {getIcon(item.name)}
              <span className='font-medium'>{item.name}</span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='font-mono'>{(item.amount || 0).toFixed(1)}kg</span>
              <Badge variant='outline' className='text-xs'>
                {(item.percentage || 0).toFixed(1)}%
              </Badge>
            </div>
          </div>
          <Progress value={item.percentage} className='h-2' />
        </div>
      ))}
    </div>
  );
}

export function RatioResultsPanel({
  calculationResults,
  isCalculating,
  task,
  onOptimize,
  onPreviewRatio,
}: RatioResultsPanelProps) {
  const [isOptimizerOpen, setIsOptimizerOpen] = useState(false);

  const handleOptimize = (selectedIds: string[]) => {
    if (onOptimize) {
      onOptimize(selectedIds);
    }
    setIsOptimizerOpen(false);
  };
  // 如果正在计算，显示加载状态
  if (isCalculating) {
    return (
      <div className='space-y-2 h-full overflow-y-auto'>
        <Card>
          <CardContent className='p-4 text-center'>
            <div className='text-sm text-muted-foreground'>计算中...</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 如果没有计算结果，显示空状态
  if (!calculationResults) {
    return (
      <div className='space-y-2 h-full overflow-y-auto'>
        <Card>
          <CardContent className='p-4 text-center'>
            <div className='text-sm text-muted-foreground'>暂无计算结果</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='space-y-2 h-full overflow-y-auto'>
      {/* 紧凑任务信息 */}
      {task && (
        <Card>
          <CardHeader className='pb-2 pt-2'>
            <CardTitle className='text-sm flex items-center gap-2 font-medium'>
              <Target className='h-4 w-4 text-primary' />
              任务概览
            </CardTitle>
          </CardHeader>
          <CardContent className='p-2'>
            <div className='grid grid-cols-2 gap-2 text-xs'>
              <div className='flex justify-between'>
                <span className='text-muted-foreground'>编号:</span>
                <span className='font-mono font-medium'>{task.taskNumber}</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-muted-foreground'>强度:</span>
                <span className='font-medium'>{task.strength}</span>
              </div>
              <div className='flex justify-between col-span-2'>
                <span className='text-muted-foreground'>工程:</span>
                <span className='font-medium truncate ml-2' title={task.projectName}>
                  {task.projectName}
                </span>
              </div>
              <div className='flex justify-between col-span-2'>
                <span className='text-muted-foreground'>部位:</span>
                <span className='font-medium'>{task.constructionSite}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 紧凑计算结果 */}
      <Card>
        <CardHeader className='pb-2 pt-2'>
          <CardTitle className='text-sm flex items-center gap-2 font-medium'>
            <BarChart3 className='h-4 w-4 text-primary' />
            计算结果
          </CardTitle>
        </CardHeader>
        <CardContent className='p-2 space-y-2'>
          {/* 关键指标 - 更紧凑 */}
          <div className='grid grid-cols-3 gap-2'>
            <div className='p-2 bg-blue-50 dark:bg-blue-950/20 rounded border border-blue-200 dark:border-blue-800'>
              <div className='flex items-center gap-1 mb-1'>
                <TrendingUp className='h-3 w-3 text-blue-600' />
                <span className='text-xs font-medium text-blue-700 dark:text-blue-300'>总重量</span>
              </div>
              <p className='text-sm font-bold text-blue-700 dark:text-blue-300 font-mono'>
                {(calculationResults.totalWeight || 0).toFixed(0)}
              </p>
              <p className='text-xs text-blue-600'>kg/m³</p>
            </div>

            <div className='p-2 bg-green-50 dark:bg-green-950/20 rounded border border-green-200 dark:border-green-800'>
              <div className='flex items-center gap-1 mb-1'>
                <Zap className='h-3 w-3 text-green-600' />
                <span className='text-xs font-medium text-green-700 dark:text-green-300'>
                  预测强度
                </span>
              </div>
              <p className='text-sm font-bold text-green-700 dark:text-green-300 font-mono'>
                {(calculationResults.strengthPrediction || 0).toFixed(1)}
              </p>
              <p className='text-xs text-green-600'>MPa</p>
            </div>
          </div>

          {/* 紧凑材料组成 */}
          <div>
            <h4 className='text-xs font-medium mb-2 flex items-center gap-1 text-muted-foreground'>
              <BarChart3 className='h-3 w-3' />
              材料组成
            </h4>
            <MaterialChart materials={calculationResults.materials} />
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <Card>
        <CardContent className='p-4'>
          <div className='space-y-2'>
            <Button className='w-full gap-2' onClick={onPreviewRatio}>
              <Eye className='h-4 w-4' />
              预览配比单
            </Button>

            <Button variant='outline' className='w-full gap-2'>
              <Download className='h-4 w-4' />
              导出报告
            </Button>

            <Button variant='outline' className='w-full gap-2'>
              <Share className='h-4 w-4' />
              分享配比
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 一键优化模态框 */}
      <OneClickOptimizer
        isOpen={isOptimizerOpen}
        onClose={() => setIsOptimizerOpen(false)}
        optimizations={(calculationResults.optimizations || []).map((opt, index) => ({
          id: `opt-${index}`,
          type: opt.type === 'material_substitution' ? 'adjust_material' : 'adjust_parameter',
          description: opt.description,
          parameterName: opt.type === 'ratio_adjustment' ? '配比参数' : undefined,
          materialName: opt.type === 'material_substitution' ? '材料替换' : undefined,
          currentValue: 0, // 需要从实际数据中获取
          suggestedValue: 0, // 需要从实际数据中获取
          reason: `置信度: ${Math.round(opt.confidence * 100)}%`,
          impact: `成本影响: ${opt.impact.cost || 0}${opt.impact.strength ? `, 强度影响: ${opt.impact.strength}` : ''}${opt.impact.workability ? `, 和易性影响: ${opt.impact.workability}` : ''}`,
          priority: opt.confidence > 0.8 ? 'high' : opt.confidence > 0.6 ? 'medium' : 'low',
        }))}
        onApplyOptimizations={handleOptimize}
        currentQualityScore={calculationResults.qualityScore}
      />
    </div>
  );
}
