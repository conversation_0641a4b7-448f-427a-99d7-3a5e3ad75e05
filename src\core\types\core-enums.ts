/**
 * 核心枚举类型定义
 * 统一管理所有系统中的枚举类型，避免重复定义
 */

// ==================== 材料相关枚举 ====================

/**
 * 材料类别枚举（统一版本）
 */
export enum MaterialCategory {
  // 胶凝材料
  CEMENTITIOUS = 'cementitious',
  // 骨料
  AGGREGATE = 'aggregate',
  // 外加剂
  ADMIXTURE = 'admixture',
  // 掺合料
  SUPPLEMENTARY = 'supplementary',
  // 纤维
  FIBER = 'fiber',
  // 特殊添加剂
  SPECIAL_ADDITIVE = 'special_additive',
  // 水
  WATER = 'water',
}

/**
 * 材料类型枚举
 */
export enum MaterialType {
  // 胶凝材料
  PORTLAND_CEMENT = 'portland_cement',
  SULFOALUMINATE_CEMENT = 'sulfoaluminate_cement',
  WHITE_CEMENT = 'white_cement',
  RAPID_HARDENING_CEMENT = 'rapid_hardening_cement',
  LOW_HEAT_CEMENT = 'low_heat_cement',

  // 粗骨料
  CRUSHED_STONE = 'crushed_stone',
  GRAVEL = 'gravel',
  RECYCLED_AGGREGATE = 'recycled_aggregate',
  LIGHTWEIGHT_AGGREGATE = 'lightweight_aggregate',
  HEAVYWEIGHT_AGGREGATE = 'heavyweight_aggregate',

  // 细骨料
  RIVER_SAND = 'river_sand',
  MACHINE_SAND = 'machine_sand',
  SEA_SAND = 'sea_sand',
  DESERT_SAND = 'desert_sand',
  RECYCLED_SAND = 'recycled_sand',

  // 掺合料
  FLY_ASH = 'fly_ash',
  SLAG_POWDER = 'slag_powder',
  SILICA_FUME = 'silica_fume',
  METAKAOLIN = 'metakaolin',
  LIMESTONE_POWDER = 'limestone_powder',

  // 外加剂
  SUPERPLASTICIZER = 'superplasticizer',
  RETARDER = 'retarder',
  ACCELERATOR = 'accelerator',
  AIR_ENTRAINING = 'air_entraining',
  WATERPROOFING = 'waterproofing',

  // 水
  POTABLE_WATER = 'potable_water',
  RECYCLED_WATER = 'recycled_water',
  SEAWATER = 'seawater',
}

/**
 * 暴露等级枚举（统一版本）
 */
export enum ExposureClass {
  // 碳化环境
  XC1 = 'XC1', // 干燥或永久湿润
  XC2 = 'XC2', // 湿润，很少干燥
  XC3 = 'XC3', // 中等湿度
  XC4 = 'XC4', // 干湿交替

  // 氯化物环境
  XD1 = 'XD1', // 中等湿度
  XD2 = 'XD2', // 湿润，很少干燥
  XD3 = 'XD3', // 干湿交替

  // 海水环境
  XS1 = 'XS1', // 海洋环境中的空气传播盐分
  XS2 = 'XS2', // 永久浸没
  XS3 = 'XS3', // 潮汐、飞溅和喷雾区域

  // 冻融环境
  XF1 = 'XF1', // 中等水饱和，无除冰剂
  XF2 = 'XF2', // 中等水饱和，有除冰剂
  XF3 = 'XF3', // 高度水饱和，无除冰剂
  XF4 = 'XF4', // 高度水饱和，有除冰剂或海水

  // 化学侵蚀环境
  XA1 = 'XA1', // 轻微化学侵蚀
  XA2 = 'XA2', // 中等化学侵蚀
  XA3 = 'XA3', // 严重化学侵蚀
}

/**
 * 浇筑方法枚举（统一版本）
 */
export enum PlacementMethod {
  MANUAL = 'manual', // 人工浇筑
  PUMP = 'pump', // 泵送
  CRANE = 'crane', // 吊斗
  CONVEYOR = 'conveyor', // 输送带
  DIRECT = 'direct', // 直接浇筑
}

// ==================== 状态相关枚举 ====================

/**
 * 任务调度状态枚举
 */
export enum TaskDispatchStatus {
  NEW = 'New',
  READY_TO_PRODUCE = 'ReadyToProduce',
  RATIO_SET = 'RatioSet',
  IN_PROGRESS = 'InProgress',
  PAUSED = 'Paused',
  COMPLETED = 'Completed',
  CANCELLED = 'Cancelled',
}

/**
 * 车辆状态枚举
 */
export enum VehicleStatus {
  PENDING = 'pending',
  OUTBOUND = 'outbound',
  RETURNED = 'returned',
  MAINTENANCE = 'maintenance',
  OFFLINE = 'offline',
}

/**
 * 车辆运营状态枚举
 */
export enum VehicleOperationalStatus {
  NORMAL = 'normal',
  PAUSED = 'paused',
  DEACTIVATED = 'deactivated',
  MAINTENANCE = 'maintenance',
  REPAIR = 'repair',
}

/**
 * 车辆生产状态枚举
 */
export enum VehicleProductionStatus {
  QUEUED = 'queued',
  PRODUCING = 'producing',
  PRODUCED = 'produced',
  WEIGHED = 'weighed',
  TICKETED = 'ticketed',
  SHIPPED = 'shipped',
}

/**
 * 优先级枚举
 */
export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

/**
 * 可用性状态枚举
 */
export enum AvailabilityStatus {
  AVAILABLE = 'available',
  LIMITED = 'limited',
  OUT_OF_STOCK = 'out_of_stock',
  SEASONAL = 'seasonal',
  CUSTOM_ORDER = 'custom_order',
}

// ==================== 配置相关枚举 ====================

/**
 * 主题模式枚举
 */
export enum ThemeMode {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto',
}

/**
 * 密度模式枚举
 */
export enum DensityMode {
  COMPACT = 'compact',
  COMFORTABLE = 'comfortable',
  SPACIOUS = 'spacious',
}

/**
 * 显示模式枚举
 */
export enum DisplayMode {
  TABLE = 'table',
  CARD = 'card',
  LIST = 'list',
}

// ==================== 类型映射 ====================

/**
 * 枚举值到显示文本的映射
 */
export const ENUM_DISPLAY_NAMES = {
  MaterialCategory: {
    [MaterialCategory.CEMENTITIOUS]: '胶凝材料',
    [MaterialCategory.AGGREGATE]: '骨料',
    [MaterialCategory.ADMIXTURE]: '外加剂',
    [MaterialCategory.SUPPLEMENTARY]: '掺合料',
    [MaterialCategory.FIBER]: '纤维',
    [MaterialCategory.SPECIAL_ADDITIVE]: '特殊添加剂',
    [MaterialCategory.WATER]: '水',
  },
  ExposureClass: {
    [ExposureClass.XC1]: 'XC1 - 干燥或永久湿润',
    [ExposureClass.XC2]: 'XC2 - 湿润，很少干燥',
    [ExposureClass.XC3]: 'XC3 - 中等湿度',
    [ExposureClass.XC4]: 'XC4 - 干湿交替',
    [ExposureClass.XD1]: 'XD1 - 中等湿度，氯化物',
    [ExposureClass.XD2]: 'XD2 - 湿润，氯化物',
    [ExposureClass.XD3]: 'XD3 - 干湿交替，氯化物',
    [ExposureClass.XS1]: 'XS1 - 海洋环境，空气传播盐分',
    [ExposureClass.XS2]: 'XS2 - 海洋环境，永久浸没',
    [ExposureClass.XS3]: 'XS3 - 海洋环境，潮汐区',
    [ExposureClass.XF1]: 'XF1 - 中等水饱和，无除冰剂',
    [ExposureClass.XF2]: 'XF2 - 中等水饱和，有除冰剂',
    [ExposureClass.XF3]: 'XF3 - 高度水饱和，无除冰剂',
    [ExposureClass.XF4]: 'XF4 - 高度水饱和，有除冰剂',
    [ExposureClass.XA1]: 'XA1 - 轻微化学侵蚀',
    [ExposureClass.XA2]: 'XA2 - 中等化学侵蚀',
    [ExposureClass.XA3]: 'XA3 - 严重化学侵蚀',
  },
  PlacementMethod: {
    [PlacementMethod.MANUAL]: '人工浇筑',
    [PlacementMethod.PUMP]: '泵送',
    [PlacementMethod.CRANE]: '吊斗',
    [PlacementMethod.CONVEYOR]: '输送带',
    [PlacementMethod.DIRECT]: '直接浇筑',
  },
  VehicleStatus: {
    [VehicleStatus.PENDING]: '待发车',
    [VehicleStatus.OUTBOUND]: '在途',
    [VehicleStatus.RETURNED]: '已返回',
    [VehicleStatus.MAINTENANCE]: '维护中',
    [VehicleStatus.OFFLINE]: '离线',
  },
  Priority: {
    [Priority.LOW]: '低',
    [Priority.MEDIUM]: '中',
    [Priority.HIGH]: '高',
    [Priority.URGENT]: '紧急',
  },
} as const;

/**
 * 获取枚举显示名称的工具函数
 */
export function getEnumDisplayName<T extends keyof typeof ENUM_DISPLAY_NAMES>(
  enumType: T,
  value: keyof (typeof ENUM_DISPLAY_NAMES)[T]
): string {
  const displayName = ENUM_DISPLAY_NAMES[enumType][value];
  return (displayName as string) || String(value);
}

/**
 * 获取枚举所有选项
 */
export function getEnumOptions<T extends keyof typeof ENUM_DISPLAY_NAMES>(
  enumType: T
): Array<{ value: keyof (typeof ENUM_DISPLAY_NAMES)[T]; label: string }> {
  return Object.entries(ENUM_DISPLAY_NAMES[enumType]).map(([value, label]) => ({
    value: value as keyof (typeof ENUM_DISPLAY_NAMES)[T],
    label: label as string,
  }));
}

/**
 * 验证枚举值是否有效
 */
export function isValidEnumValue<T extends Record<string, string>>(
  enumObject: T,
  value: any
): value is T[keyof T] {
  return Object.values(enumObject).includes(value);
}
