'use client';

// Added Vehicle and Task
import { Button } from '@/shared/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import type { DeliveryOrder, Task, Vehicle } from '@/core/types';

interface DeliveryOrderDetailsModalProps {
  isOpen: boolean;
  onOpenChangeAction: (isOpen: boolean) => void;
  order?: DeliveryOrder | null; // Made optional
  vehicle?: Vehicle | null; // Added
  task?: Task | null; // Added
}

export function DeliveryOrderDetailsModal({
  isOpen,
  onOpenChangeAction,
  order,
  vehicle,
  task,
}: DeliveryOrderDetailsModalProps) {
  // Prioritize order data if available, otherwise use vehicle/task context
  const displayVehicleNumber = order?.vehicleNumber || vehicle?.vehicleNumber || 'N/A';
  const displayProjectName = order?.projectName || task?.projectName || 'N/A';
  const displayTaskNumber = order?.taskNumber || task?.taskNumber || 'N/A';
  const displayStrength = order?.strength || task?.strength || 'N/A';
  const displayPlantId = order?.plantId || task?.plantId || vehicle?.plantId || 'N/A';
  const displayProductionLineId =
    order?.productionLineId || vehicle?.assignedProductionLineId || 'N/A';

  if (!isOpen) return null; // Don't render if not open
  if (!order && !vehicle && !task) return null; // If no data context, don't render

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChangeAction}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>发货单详情 - {displayVehicleNumber}</DialogTitle>
          <DialogDescription>查看发货单的详细信息。</DialogDescription>
        </DialogHeader>
        <div className='grid gap-3 py-4 text-sm'>
          <div className='flex justify-between'>
            <span className='text-muted-foreground'>车号:</span>
            <span>{displayVehicleNumber}</span>
          </div>
          <div className='flex justify-between'>
            <span className='text-muted-foreground'>工程名称:</span>
            <span>{displayProjectName}</span>
          </div>
          <div className='flex justify-between'>
            <span className='text-muted-foreground'>任务编号:</span>
            <span>{displayTaskNumber}</span>
          </div>
          {order?.driver && (
            <div className='flex justify-between'>
              <span className='text-muted-foreground'>司机:</span>
              <span>{order.driver}</span>
            </div>
          )}
          {order?.volume !== undefined && (
            <div className='flex justify-between'>
              <span className='text-muted-foreground'>方量 (m³):</span>
              <span>{order.volume}</span>
            </div>
          )}
          <div className='flex justify-between'>
            <span className='text-muted-foreground'>强度:</span>
            <span>{displayStrength}</span>
          </div>
          <div className='flex justify-between'>
            <span className='text-muted-foreground'>搅拌站:</span>
            <span>{displayPlantId}</span>
          </div>
          <div className='flex justify-between'>
            <span className='text-muted-foreground'>生产线:</span>
            <span>{displayProductionLineId}</span>
          </div>
          {order?.status && (
            <div className='flex justify-between'>
              <span className='text-muted-foreground'>发货单状态:</span>
              <span>{order.status}</span>
            </div>
          )}
          {vehicle?.productionStatus && (
            <div className='flex justify-between'>
              <span className='text-muted-foreground'>车辆生产状态:</span>
              <span>{vehicle.productionStatus}</span>
            </div>
          )}
          {task?.dispatchStatus && (
            <div className='flex justify-between'>
              <span className='text-muted-foreground'>任务调度状态:</span>
              <span>{task.dispatchStatus}</span>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button type='button' onClick={() => onOpenChangeAction(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
