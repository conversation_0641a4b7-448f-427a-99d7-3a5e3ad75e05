'use client';

import { produce } from 'immer';
import { create } from 'zustand';

// 导入统一的配比数据
import { initialTableData } from '@/infrastructure/api/mock/ratio-mock-data';
import type {
  RatioCalculationParams,
  RatioMaterial,
  RatioResult,
  ReverseCalculationParams,
  Task,
  ExposureClass,
  PlacementMethod,
  FinishingRequirement,
} from '@/core/types';

// --- Utility Functions (Internal to the store) ---

function calculateProportions(params: RatioCalculationParams): RatioResult {
  const water = params.waterContent || params.waterAmount;
  const cement = params.cementContent || params.cementAmount;
  const totalBinder = cement / (1 - params.flyashRatio / 100 - params.mineralPowderRatio / 100);
  const flyAsh = totalBinder * (params.flyashRatio / 100);
  const mineralPowder = totalBinder * (params.mineralPowderRatio / 100);
  const admixture = totalBinder * (params.additiveRatio / 100);
  const totalAggregate = params.density - (water + cement + flyAsh + mineralPowder + admixture);
  const sand = totalAggregate * (params.sandRatio / 100);
  const gravel = totalAggregate * (1 - params.sandRatio / 100);
  const totalWeight = water + cement + flyAsh + mineralPowder + sand + gravel + admixture;
  const result: RatioResult = {
    materials: {
      water: water || 0,
      cement: cement || 0,
      flyAsh: flyAsh || 0,
      mineralPowder: mineralPowder || 0,
      sand: sand || 0,
      gravel: gravel || 0,
      admixture: admixture || 0,
    },
    totalVolume: totalWeight || 0,
    density: params.density || 2400,
    water: water || 0,
    cement: cement || 0,
    flyAsh: flyAsh || 0,
    mineralPowder: mineralPowder || 0,
    sand: sand || 0,
    gravel: gravel || 0,
    admixture: admixture || 0,
    totalWeight: totalWeight || 0,
    s105Powder: 0,
    expansionAgent: 0,
    earlyStrengthAgent: 0,
    stone: 0,
    antifreeze: 0,
    ultraFineSand: 0,
  };
  return result;
}

function reverseCalculate(materials: RatioMaterial[]): {
  params: Partial<RatioCalculationParams>;
  proportions: RatioResult;
} {
  const getAmount = (type: string): number =>
    materials.find(m => m.materialType === type)?.theoryAmount || 0;

  const water = getAmount('水');
  const cement = getAmount('水泥');
  const flyAsh = getAmount('粉煤灰');
  const mineralPowder = getAmount('矿粉');
  const s105 = getAmount('S105');
  const expansionAgent = getAmount('膨胀剂');
  const earlyStrengthAgent = getAmount('早强剂');
  const sand = getAmount('砂子');
  const gravel = getAmount('石子');
  const admixture = getAmount('外加剂');
  const antifreeze = getAmount('防冻剂');
  const ultraFineSand = getAmount('超细砂');

  const totalBinder = cement + flyAsh + mineralPowder + s105 + expansionAgent + earlyStrengthAgent;
  const totalAggregate = sand + gravel + ultraFineSand;
  const totalWeight = totalBinder + totalAggregate + water + admixture + antifreeze;

  const calculatedParams: Partial<RatioCalculationParams> =
    totalBinder > 0 && totalAggregate > 0
      ? {
          density: totalWeight,
          waterAmount: water,
          waterCementRatio: parseFloat((water / totalBinder).toFixed(4)),
          sandRatio: parseFloat((((sand + ultraFineSand) / totalAggregate) * 100).toFixed(2)),
          additiveRatio: parseFloat(((admixture / totalBinder) * 100).toFixed(2)),
          flyashRatio: parseFloat(((flyAsh / totalBinder) * 100).toFixed(2)),
          mineralPowderRatio: parseFloat(((mineralPowder / totalBinder) * 100).toFixed(2)),
          silicaFumeRatio: 0,
          antifreezeRatio: parseFloat(((antifreeze / totalBinder) * 100).toFixed(2)),
          expansionRatio: parseFloat(((expansionAgent / totalBinder) * 100).toFixed(2)),
          earlyStrengthRatio: parseFloat(((earlyStrengthAgent / totalBinder) * 100).toFixed(2)),
          ultraFineSandRatio: parseFloat(((ultraFineSand / totalAggregate) * 100).toFixed(2)),
        }
      : {};

  const proportions: RatioResult = {
    materials: {
      water,
      cement,
      flyAsh,
      mineralPowder,
      sand,
      gravel,
      admixture,
    },
    totalVolume: totalWeight,
    density: 2400,
    water,
    cement,
    flyAsh,
    mineralPowder,
    sand,
    gravel,
    admixture,
    totalWeight,
    s105Powder: 0,
    expansionAgent: 0,
    earlyStrengthAgent: 0,
    stone: 0,
    antifreeze: 0,
    ultraFineSand: 0,
  };

  return { params: calculatedParams, proportions };
}

// --- Zustand Store Definition ---

const defaultCalculationParams: RatioCalculationParams = {
  // 基本参数
  targetStrength: 30, // C30
  slump: 180,
  maxAggregateSize: 25,
  exposureClass: 'XC1' as ExposureClass,

  // 环境参数
  ambientTemperature: 20,
  relativeHumidity: 60,
  cementTemperature: 20,
  aggregateTemperature: 20,

  // 材料选择
  selectedMaterials: [],
  cementType: 'P.O 42.5',
  aggregateType: '碎石',
  waterType: '饮用水',

  // 配比参数
  waterCementRatio: 0.45,
  sandRatio: 35,
  cementContent: 400,
  waterContent: 180,

  // 外加剂参数
  additiveRatio: 0,
  flyashRatio: 15,
  mineralPowderRatio: 10,
  silicaFumeRatio: 0,
  antifreezeRatio: 0,
  expansionRatio: 0,

  // 数量参数
  cementAmount: 400,
  waterAmount: 180,
  density: 2400,
  airContent: 4,
  strengthGrade: 30,
  ultraFineSandRatio: 0,
  earlyStrengthRatio: 0,

  // 施工参数
  placementMethod: 'PUMPING' as PlacementMethod,
  finishingRequirement: 'SMOOTH' as FinishingRequirement,

  // 养护条件
  cureConditions: {
    method: 'STANDARD',
    duration: 28,
    temperature: 20,
    humidity: 95,
  },
  admixtureAmount: 0,
  antifreezeAmount: 0,
  flyAshAmount: 0,
  mineralPowderAmount: 0,
  s105PowderAmount: 0,
  expansionAgentAmount: 0,
  earlyStrengthAgentAmount: 0,
  ultraFineSandAmount: 0,
};

const defaultReverseParams: ReverseCalculationParams = {
  targetVolume: 1,
  currentRatio: defaultCalculationParams,
  cementSubstitutionRate: 1.0,
  flyAshFactor: 1.2,
  flyAshDensity: 2.3,
  sandDensity: 2.65,
  flyAshCementFactor: 0.8,
  silicaFumeCementFactor: 2.5,
};

const defaultProportions: RatioResult = {
  materials: {},
  totalVolume: 0,
  density: 2400,
  water: 0,
  cement: 0,
  flyAsh: 0,
  mineralPowder: 0,
  sand: 0,
  gravel: 0,
  admixture: 0,
  totalWeight: 0,
  s105Powder: 0,
  expansionAgent: 0,
  earlyStrengthAgent: 0,
  stone: 0,
  antifreeze: 0,
  ultraFineSand: 0,
};

interface RatioState {
  task: Task | null;
  materials: RatioMaterial[];
  calculationParams: RatioCalculationParams;
  proportions: RatioResult;
  calculationMethod: string;
  reverseParams: ReverseCalculationParams;
  isInitialized: boolean;
  actions: {
    initialize: (task: Task) => void;
    setCalculationParams: (params: Partial<RatioCalculationParams>) => void;
    setReverseParam: <K extends keyof ReverseCalculationParams>(
      key: K,
      value: ReverseCalculationParams[K]
    ) => void;
    setCalculationMethod: (method: string) => void;
    calculate: () => void;
    reverseCalculate: () => void;
    addMaterial: () => boolean;
    updateMaterial: (id: string, newMaterial: Partial<RatioMaterial>) => void;
    deleteMaterial: (id: string) => void;
  };
}

export const useRatioStore = create<RatioState>((set, get) => ({
  task: null,
  materials: [],
  calculationParams: defaultCalculationParams,
  proportions: defaultProportions,
  calculationMethod: 'method1',
  reverseParams: defaultReverseParams,
  isInitialized: false,
  actions: {
    initialize: task =>
      set({
        task,
        materials: initialTableData,
        isInitialized: true,
      }),
    setCalculationParams: params =>
      set(
        produce((draft: RatioState) => {
          Object.assign(draft.calculationParams, params);
        })
      ),
    setReverseParam: (key, value) =>
      set(
        produce((draft: RatioState) => {
          draft.reverseParams[key] = value;
        })
      ),
    setCalculationMethod: method => set({ calculationMethod: method }),
    calculate: () => {
      const params = get().calculationParams;
      const result = calculateProportions(params);
      set({ proportions: result });

      set(
        produce((draft: RatioState) => {
          const updateOrAdd = (type: string, amount: number) => {
            const existing = draft.materials.find(m => m.materialType === type);
            if (existing) {
              existing.theoryAmount = amount;
              existing.actualAmount = amount * (1 + (existing.waterRatio || 0) / 100);
              existing.designValue = amount;
            }
          };
          updateOrAdd('水', result.water);
          updateOrAdd('水泥', result.cement);
          updateOrAdd('粉煤灰', result.flyAsh);
          updateOrAdd('矿粉', result.mineralPowder);
          updateOrAdd('砂子', result.sand);
          updateOrAdd('石子', result.gravel);
          updateOrAdd('外加剂', result.admixture);
        })
      );
    },
    reverseCalculate: () => {
      const materials = get().materials;
      const { params, proportions } = reverseCalculate(materials);
      set({
        calculationParams: { ...defaultCalculationParams, ...params },
        proportions: { ...defaultProportions, ...proportions },
      });
    },
    addMaterial: () => {
      const hasEmptyRow = get().materials.some(row => !row.materialType);
      if (hasEmptyRow) {
        return false;
      }
      set(
        produce((draft: RatioState) => {
          draft.materials.push({
            id: `new_${Date.now()}`,
            materialType: '',
            spec: '',
            theoryAmount: 0,
            waterRatio: 0,
            stoneRatio: 0,
            actualAmount: 0,
            designValue: 0,
            binName: '',
          });
        })
      );
      return true;
    },
    updateMaterial: (id, newMaterial) =>
      set(
        produce((draft: RatioState) => {
          const mat = draft.materials.find(m => m.id === id);
          if (mat) {
            Object.assign(mat, newMaterial);
            mat.actualAmount = mat.theoryAmount * (1 + (mat.waterRatio || 0) / 100);
            mat.designValue = mat.theoryAmount;
          }
        })
      ),
    deleteMaterial: id =>
      set(
        produce((draft: RatioState) => {
          draft.materials = draft.materials.filter(m => m.id !== id);
        })
      ),
  },
}));
