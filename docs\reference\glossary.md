# 📖 术语表

## 🏗️ 业务术语

### 混凝土相关

**混凝土 (Concrete)**
由水泥、砂、石子、水按一定比例混合而成的建筑材料。

**强度等级 (Strength Grade)**
混凝土的抗压强度等级，如 C20、C30、C40 等，数字表示标准立方体试件 28 天抗压强度的标准值（MPa）。

**坍落度 (Slump)**
衡量新拌混凝土流动性和粘聚性的指标，单位为毫米（mm）。

**配比 (Mix Ratio)**
混凝土中各种材料（水泥、砂、石子、水、外加剂）的重量比例。

**外加剂 (Admixture)**
在混凝土拌制过程中掺入的用以改善混凝土性能的材料，如减水剂、缓凝剂等。

**方量 (Volume)**
混凝土的体积量，通常以立方米（m³）为单位。

### 生产相关

**搅拌站 (Mixing Plant)**
生产混凝土的工厂设备，包括配料、搅拌、控制等系统。

**生产线 (Production Line)**
搅拌站中的一条完整生产流水线，包括配料机、搅拌机等设备。

**配料 (Batching)**
按照配比要求称量各种原材料的过程。

**搅拌 (Mixing)**
将各种原材料在搅拌机中混合均匀的过程。

**出料 (Discharge)**
将搅拌好的混凝土从搅拌机中卸出的过程。

### 运输相关

**混凝土运输车 (Concrete Mixer Truck)**
专门用于运输混凝土的车辆，通常配有搅拌罐。

**罐车 (Tank Truck)**
混凝土运输车的另一种称呼。

**泵车 (Concrete Pump Truck)**
配有混凝土泵送设备的专用车辆，用于将混凝土输送到浇筑位置。

**车辆调度 (Vehicle Dispatch)**
根据生产计划和运输需求，合理安排车辆的使用和路线。

**运输半径 (Transport Radius)**
从搅拌站到施工现场的运输距离范围。

## 🖥️ 技术术语

### 前端技术

**React**
用于构建用户界面的 JavaScript 库，采用组件化开发模式。

**Next.js**
基于 React 的全栈框架，提供服务端渲染、路由等功能。

**TypeScript**
JavaScript 的超集，添加了静态类型检查功能。

**Zustand**
轻量级的 React 状态管理库。

**Tailwind CSS**
实用优先的 CSS 框架，提供原子化的样式类。

**shadcn/ui**
基于 Radix UI 和 Tailwind CSS 的 React 组件库。

### 开发概念

**组件 (Component)**
React 中可复用的 UI 单元，封装了特定的功能和样式。

**Hook**
React 中用于在函数组件中使用状态和其他 React 特性的函数。

**状态管理 (State Management)**
管理应用程序中数据状态的机制和模式。

**响应式设计 (Responsive Design)**
使网页在不同设备和屏幕尺寸上都能良好显示的设计方法。

**虚拟化 (Virtualization)**
只渲染可见区域的列表项，提高大数据量列表的性能。

**懒加载 (Lazy Loading)**
按需加载资源，减少初始加载时间。

### 架构模式

**MVC (Model-View-Controller)**
将应用程序分为模型、视图和控制器三个部分的架构模式。

**组件化 (Component-Based)**
将 UI 拆分为独立、可复用组件的开发方法。

**模块化 (Modular)**
将代码组织成独立模块的开发方式。

**单向数据流 (Unidirectional Data Flow)**
数据只能从父组件流向子组件的数据流模式。

## 🎯 系统功能

### 任务管理

**任务 (Task)**
系统中的基本工作单元，包含混凝土生产和交付的相关信息。

**任务状态 (Task Status)**
任务当前的处理状态，如待处理、进行中、已完成等。

**优先级 (Priority)**
任务的重要程度等级，影响处理顺序。

**批量操作 (Batch Operation)**
同时对多个任务执行相同操作的功能。

**筛选 (Filter)**
根据特定条件显示任务子集的功能。

**排序 (Sort)**
按照指定字段对任务进行排列的功能。

**分组 (Group)**
按照某个字段将任务归类显示的功能。

### 车辆调度

**调度 (Dispatch)**
将车辆分配给特定任务的过程。

**车辆状态 (Vehicle Status)**
车辆当前的工作状态，如空闲、已调度、运输中等。

**拖拽调度 (Drag and Drop Dispatch)**
通过拖拽操作进行车辆调度的交互方式。

**调度历史 (Dispatch History)**
记录车辆调度变更的历史信息。

**冲突检测 (Conflict Detection)**
检查调度操作是否会产生冲突的功能。

### 用户界面

**视图模式 (View Mode)**
不同的数据展示方式，如表格视图、卡片视图。

**主题 (Theme)**
界面的整体色彩和样式方案，如浅色主题、深色主题。

**布局 (Layout)**
界面元素的排列和组织方式。

**工具栏 (Toolbar)**
包含常用操作按钮的界面区域。

**模态框 (Modal)**
覆盖在主界面上的弹出窗口。

**面板 (Panel)**
界面中的功能区域，如左侧面板、右侧面板。

## 🔧 技术实现

### 性能优化

**代码分割 (Code Splitting)**
将代码拆分成多个包，按需加载的优化技术。

**Tree Shaking**
移除未使用代码的优化技术。

**缓存 (Cache)**
临时存储数据以提高访问速度的机制。

**防抖 (Debounce)**
延迟执行函数调用，避免频繁触发的优化技术。

**节流 (Throttle)**
限制函数执行频率的优化技术。

### 数据处理

**持久化 (Persistence)**
将数据保存到持久存储介质的过程。

**序列化 (Serialization)**
将对象转换为可存储或传输格式的过程。

**反序列化 (Deserialization)**
将序列化数据还原为对象的过程。

**数据验证 (Data Validation)**
检查数据是否符合预期格式和规则的过程。

### 开发工具

**热重载 (Hot Reload)**
在开发过程中自动更新页面而不丢失状态的功能。

**类型检查 (Type Checking)**
验证代码中类型使用是否正确的过程。

**代码检查 (Linting)**
检查代码风格和潜在问题的工具。

**单元测试 (Unit Testing)**
测试单个函数或组件的测试方法。

**集成测试 (Integration Testing)**
测试多个组件协作的测试方法。

**端到端测试 (E2E Testing)**
模拟用户完整操作流程的测试方法。

## 📊 数据和API

### 数据格式

**JSON (JavaScript Object Notation)**
轻量级的数据交换格式。

**REST (Representational State Transfer)**
一种 Web API 设计风格。

**GraphQL**
一种 API 查询语言和运行时。

**WebSocket**
提供全双工通信的网络协议。

### 状态码

**2xx 成功状态码**
表示请求成功处理的 HTTP 状态码。

**4xx 客户端错误**
表示客户端请求错误的 HTTP 状态码。

**5xx 服务器错误**
表示服务器处理错误的 HTTP 状态码。

## 🔐 安全术语

**认证 (Authentication)**
验证用户身份的过程。

**授权 (Authorization)**
确定用户访问权限的过程。

**JWT (JSON Web Token)**
一种用于安全传输信息的令牌格式。

**CORS (Cross-Origin Resource Sharing)**
跨域资源共享机制。

**CSP (Content Security Policy)**
内容安全策略，防止 XSS 攻击。

**XSS (Cross-Site Scripting)**
跨站脚本攻击。

**CSRF (Cross-Site Request Forgery)**
跨站请求伪造攻击。

## 📱 移动端术语

**PWA (Progressive Web App)**
渐进式 Web 应用。

**响应式断点 (Responsive Breakpoints)**
不同屏幕尺寸的分界点。

**触摸事件 (Touch Events)**
移动设备上的触摸交互事件。

**手势 (Gesture)**
用户在触摸屏上的操作模式。

---

**文档版本**: v1.0  
**最后更新**: 2025/7/16  
**维护者**: TMH 文档团队

> 💡 **提示**: 如果您发现遗漏的术语或需要补充说明，请通过 GitHub Issues 或邮件联系我们。
