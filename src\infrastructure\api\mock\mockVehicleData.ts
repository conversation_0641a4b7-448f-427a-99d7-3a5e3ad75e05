/**
 * Mock车辆数据生成器
 * 为车辆调度容器提供测试数据
 */

import type { Vehicle } from '@/core/types';

// 车牌号前缀
const LICENSE_PREFIXES = [
  '京A',
  '京B',
  '京C',
  '京D',
  '京E',
  '京F',
  '沪A',
  '沪B',
  '沪C',
  '沪D',
  '粤A',
  '粤B',
  '粤C',
  '粤D',
  '苏A',
  '苏B',
  '苏C',
  '苏D',
  '浙A',
  '浙B',
  '浙C',
  '浙D',
];

// 车辆状态
const VEHICLE_STATUSES: Array<'pending' | 'returned' | 'outbound'> = [
  'pending',
  'returned',
  'outbound',
];

// 车辆类型
const VEHICLE_TYPES: Array<'Tanker' | 'Pump' | 'Other'> = ['Tanker', 'Pump', 'Other'];

// 运营状态
const OPERATIONAL_STATUSES: Array<'normal' | 'paused' | 'deactivated'> = [
  'normal',
  'paused',
  'deactivated',
];

// Note: Driver-related constants removed as they're not part of the Vehicle interface

/**
 * 生成随机车牌号
 */
function generateLicensePlate(): string {
  const prefix = LICENSE_PREFIXES[Math.floor(Math.random() * LICENSE_PREFIXES.length)];
  const numbers = Math.floor(Math.random() * 90000) + 10000; // 10000-99999
  return `${prefix}${numbers}`;
}

// Note: generatePhoneNumber function removed as it's not used

/**
 * 生成随机任务ID
 */
function generateTaskId(): string {
  return `task-${Math.floor(Math.random() * 1000) + 1}`;
}

/**
 * 生成随机生产线ID
 */
function generateProductionLineId(): string {
  return `line-${Math.floor(Math.random() * 10) + 1}`;
}

/**
 * 生成单个车辆数据
 */
function generateVehicle(id: string, status?: 'pending' | 'returned' | 'outbound'): Vehicle {
  const vehicleStatus: 'pending' | 'returned' | 'outbound' =
    status || VEHICLE_STATUSES[Math.floor(Math.random() * VEHICLE_STATUSES.length)] || 'pending';
  const vehicleType: 'Tanker' | 'Pump' | 'Other' =
    VEHICLE_TYPES[Math.floor(Math.random() * VEHICLE_TYPES.length)] || 'Tanker';
  const operationalStatus =
    OPERATIONAL_STATUSES[Math.floor(Math.random() * OPERATIONAL_STATUSES.length)] || 'normal';

  return {
    id,
    vehicleNumber: generateLicensePlate(),
    status: vehicleStatus,
    type: vehicleType,
    operationalStatus,
    assignedTaskId: Math.random() > 0.3 ? generateTaskId() : undefined,
    assignedProductionLineId: Math.random() > 0.5 ? generateProductionLineId() : undefined,
    // Note: Only including fields that are part of the Vehicle interface in src/types/index.ts
    isDragging: false,
  };
}

/**
 * 生成车辆列表
 */
export function generateMockVehicles(count: number = 20): Vehicle[] {
  const vehicles: Vehicle[] = [];

  // 生成不同状态的车辆
  const pendingCount = Math.floor(count * 0.4); // 40% 待发车辆
  const outboundCount = Math.floor(count * 0.35); // 35% 出车中车辆
  const returnedCount = count - pendingCount - outboundCount; // 剩余为已返回车辆

  // 生成待发车辆
  for (let i = 0; i < pendingCount; i++) {
    vehicles.push(generateVehicle(`pending-${i + 1}`, 'pending'));
  }

  // 生成出车中车辆
  for (let i = 0; i < outboundCount; i++) {
    vehicles.push(generateVehicle(`outbound-${i + 1}`, 'outbound'));
  }

  // 生成已返回车辆
  for (let i = 0; i < returnedCount; i++) {
    vehicles.push(generateVehicle(`returned-${i + 1}`, 'returned'));
  }

  return vehicles;
}

/**
 * 生成特定状态的车辆列表
 */
export function generateMockVehiclesByStatus(
  status: 'pending' | 'returned' | 'outbound',
  count: number = 10
): Vehicle[] {
  const vehicles: Vehicle[] = [];

  for (let i = 0; i < count; i++) {
    vehicles.push(generateVehicle(`${status}-${i + 1}`, status));
  }

  return vehicles;
}

/**
 * 获取车辆统计信息
 */
export function getMockVehicleStats(vehicles: Vehicle[]) {
  const stats = {
    total: vehicles.length,
    pending: vehicles.filter(v => v.status === 'pending').length,
    outbound: vehicles.filter(v => v.status === 'outbound').length,
    returned: vehicles.filter(v => v.status === 'returned').length,
    lastUpdated: new Date().toISOString(),
  };

  return stats;
}

/**
 * 默认的mock车辆数据
 */
export const DEFAULT_MOCK_VEHICLES = generateMockVehicles(30);
