'use client';

/**
 * 任务编辑模态框
 * 占位符组件，用于懒加载测试
 */

import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';

interface TaskEditModalProps {
  open?: boolean;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onOpenChangeAction?: (open: boolean) => void;
  taskId?: string;
}

const TaskEditModal: React.FC<TaskEditModalProps> = ({
  open = false,
  isOpen = false,
  onOpenChange,
  onOpenChangeAction,
  taskId,
}) => {
  // 统一处理open状态和回调
  const modalOpen = open || isOpen;
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange?.(newOpen);
    onOpenChangeAction?.(newOpen);
  };

  return (
    <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <DialogTitle>编辑任务</DialogTitle>
        </DialogHeader>
        <div className='p-4'>
          <p>任务编辑功能正在开发中...</p>
          {taskId && <p className='text-sm text-gray-500'>任务ID: {taskId}</p>}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TaskEditModal;
