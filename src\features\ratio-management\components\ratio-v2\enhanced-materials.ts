/**
 * 增强的物料类型定义
 * 支持更多种类的建筑材料和添加剂
 *
 * 注意：类型定义已迁移到 src/types/ratio.ts
 * 此文件现在主要包含材料数据和相关函数
 */

import {
  AvailabilityStatus,
  type ChemicalComposition,
  type EnhancedMaterial,
  MaterialCategory,
  MaterialType,
  type PhysicalProperties,
  type StorageRequirements,
  type UsageRecommendation,
} from '@/core/types/ratio';

// 枚举和接口类型已迁移到 src/types/ratio.ts
// 这里重新导出以保持向后兼容性
export { MaterialCategory, MaterialType, AvailabilityStatus } from '@/core/types/ratio';

// 预定义的增强材料库
export const ENHANCED_MATERIALS: EnhancedMaterial[] = [
  // 胶凝材料
  {
    id: 'cement_p_o_42_5',
    name: 'P.O 42.5 普通硅酸盐水泥',
    category: MaterialCategory.CEMENTITIOUS,
    type: MaterialType.PORTLAND_CEMENT,
    density: 3100,
    specificGravity: 3.1,
    strength: 42.5,
    chemicalComposition: {
      SiO2: 21.5,
      Al2O3: 5.2,
      Fe2O3: 3.1,
      CaO: 64.2,
      MgO: 2.8,
      SO3: 2.5,
    },
    physicalProperties: {
      surfaceArea: 350,
      bulkDensity: 1300,
    },
    qualityStandard: 'GB 175-2007',
    cost: 450,
    carbonFootprint: 820,
    availability: AvailabilityStatus.AVAILABLE,
    storageRequirements: {
      humidity: { max: 80 },
      shelfLife: 90,
      specialConditions: ['防潮', '通风'],
    },
    compatibilityMatrix: ['fly_ash_ii', 'slag_powder_s95', 'superplasticizer_pce'],
    recommendedUsage: [
      {
        application: '普通混凝土',
        dosage: { min: 280, max: 450, unit: 'kg/m³' },
        notes: '适用于一般工程',
      },
    ],
  },

  // 高性能外加剂
  {
    id: 'superplasticizer_pce',
    name: 'PCE聚羧酸高效减水剂',
    category: MaterialCategory.ADMIXTURE,
    type: MaterialType.SUPERPLASTICIZER,
    density: 1050,
    specificGravity: 1.05,
    qualityStandard: 'GB 8076-2008',
    cost: 2800,
    carbonFootprint: 1.2,
    availability: AvailabilityStatus.AVAILABLE,
    storageRequirements: {
      temperature: { min: 5, max: 35 },
      shelfLife: 365,
      specialConditions: ['避免冰冻', '密封保存'],
    },
    compatibilityMatrix: ['cement_p_o_42_5', 'fly_ash_ii', 'slag_powder_s95'],
    recommendedUsage: [
      {
        application: '高强混凝土',
        dosage: { min: 0.8, max: 2.5, unit: '%' },
        notes: '按胶凝材料重量计',
      },
      {
        application: '自密实混凝土',
        dosage: { min: 1.5, max: 3.0, unit: '%' },
        notes: '需配合粘度调节剂使用',
      },
    ],
  },

  // 新型掺合料
  {
    id: 'silica_fume',
    name: '硅灰（微硅粉）',
    category: MaterialCategory.SUPPLEMENTARY,
    type: MaterialType.SILICA_FUME,
    density: 2200,
    specificGravity: 2.2,
    chemicalComposition: {
      SiO2: 95.0,
      Al2O3: 0.5,
      Fe2O3: 0.8,
      CaO: 0.3,
      MgO: 0.8,
    },
    physicalProperties: {
      surfaceArea: 20000,
      particleSize: { d50: 0.15 },
    },
    qualityStandard: 'GB/T 27690-2011',
    cost: 1200,
    carbonFootprint: 28,
    availability: AvailabilityStatus.LIMITED,
    storageRequirements: {
      humidity: { max: 70 },
      shelfLife: 180,
      specialConditions: ['防潮', '防静电'],
    },
    compatibilityMatrix: ['cement_p_o_42_5', 'superplasticizer_pce'],
    recommendedUsage: [
      {
        application: '超高强混凝土',
        dosage: { min: 5, max: 15, unit: '%' },
        notes: '替代部分水泥',
      },
    ],
  },

  // 纤维材料
  {
    id: 'steel_fiber_hooked',
    name: '端钩型钢纤维',
    category: MaterialCategory.FIBER,
    type: MaterialType.STEEL_FIBER,
    density: 7850,
    specificGravity: 7.85,
    physicalProperties: {
      particleSize: { d50: 35000 }, // 长度35mm
    },
    qualityStandard: 'YB/T 151-1999',
    cost: 8500,
    carbonFootprint: 2100,
    availability: AvailabilityStatus.AVAILABLE,
    storageRequirements: {
      specialConditions: ['防锈', '干燥存放'],
    },
    compatibilityMatrix: ['cement_p_o_42_5', 'superplasticizer_pce'],
    recommendedUsage: [
      {
        application: '钢纤维混凝土',
        dosage: { min: 20, max: 80, unit: 'kg/m³' },
        notes: '提高抗拉强度和韧性',
      },
    ],
  },

  // 环保材料
  {
    id: 'recycled_aggregate_coarse',
    name: '再生粗骨料',
    category: MaterialCategory.AGGREGATE,
    type: MaterialType.RECYCLED_AGGREGATE,
    density: 2400,
    specificGravity: 2.4,
    absorptionRate: 5.5,
    physicalProperties: {
      crushingValue: 18,
      abrasionValue: 28,
    },
    qualityStandard: 'GB/T 25177-2010',
    cost: 45,
    carbonFootprint: 8,
    availability: AvailabilityStatus.SEASONAL,
    storageRequirements: {
      specialConditions: ['分级堆放', '防止污染'],
    },
    compatibilityMatrix: ['cement_p_o_42_5', 'fly_ash_ii'],
    recommendedUsage: [
      {
        application: '绿色混凝土',
        dosage: { min: 30, max: 100, unit: '%' },
        notes: '替代天然骨料',
      },
    ],
  },

  // 特殊功能材料
  {
    id: 'expansion_agent_uea',
    name: 'UEA膨胀剂',
    category: MaterialCategory.SPECIAL_ADDITIVE,
    type: MaterialType.EXPANSION_AGENT,
    density: 3000,
    specificGravity: 3.0,
    chemicalComposition: {
      CaO: 45,
      Al2O3: 15,
      SO3: 35,
    },
    qualityStandard: 'GB 23439-2009',
    cost: 680,
    carbonFootprint: 450,
    availability: AvailabilityStatus.AVAILABLE,
    storageRequirements: {
      humidity: { max: 75 },
      shelfLife: 120,
      specialConditions: ['密封保存', '防潮'],
    },
    compatibilityMatrix: ['cement_p_o_42_5'],
    recommendedUsage: [
      {
        application: '补偿收缩混凝土',
        dosage: { min: 8, max: 12, unit: '%' },
        notes: '按水泥重量计',
      },
    ],
  },

  // 水类材料
  {
    id: 'potable_water',
    name: '饮用水',
    category: MaterialCategory.WATER,
    type: MaterialType.POTABLE_WATER,
    density: 1000,
    specificGravity: 1.0,
    qualityStandard: 'JGJ 63-2006',
    cost: 3,
    carbonFootprint: 0.1,
    availability: AvailabilityStatus.AVAILABLE,
    storageRequirements: {
      specialConditions: ['清洁', '无污染'],
    },
    compatibilityMatrix: ['cement_p_o_42_5', 'superplasticizer_pce', 'silica_fume'],
    recommendedUsage: [
      {
        application: '混凝土拌合',
        dosage: { min: 150, max: 200, unit: 'kg/m³' },
        notes: '符合饮用水标准',
      },
    ],
  },

  // 基本骨料
  {
    id: 'river_sand',
    name: '河砂',
    category: MaterialCategory.AGGREGATE,
    type: MaterialType.RIVER_SAND,
    density: 2650,
    specificGravity: 2.65,
    absorptionRate: 1.2,
    physicalProperties: {
      particleSize: { d50: 0.5 },
      bulkDensity: 1600,
      voidRatio: 0.4,
    },
    qualityStandard: 'GB/T 14684-2011',
    cost: 80,
    carbonFootprint: 5,
    availability: AvailabilityStatus.AVAILABLE,
    storageRequirements: {
      specialConditions: ['分级堆放', '防止污染'],
    },
    compatibilityMatrix: ['cement_p_o_42_5', 'crushed_stone'],
    recommendedUsage: [
      {
        application: '普通混凝土',
        dosage: { min: 600, max: 800, unit: 'kg/m³' },
        notes: '细骨料',
      },
    ],
  },

  {
    id: 'crushed_stone',
    name: '碎石',
    category: MaterialCategory.AGGREGATE,
    type: MaterialType.CRUSHED_STONE,
    density: 2700,
    specificGravity: 2.7,
    absorptionRate: 0.8,
    physicalProperties: {
      crushingValue: 12,
      abrasionValue: 15,
    },
    qualityStandard: 'GB/T 14685-2011',
    cost: 60,
    carbonFootprint: 8,
    availability: AvailabilityStatus.AVAILABLE,
    storageRequirements: {
      specialConditions: ['分级堆放', '防止污染'],
    },
    compatibilityMatrix: ['cement_p_o_42_5', 'river_sand'],
    recommendedUsage: [
      {
        application: '普通混凝土',
        dosage: { min: 1000, max: 1200, unit: 'kg/m³' },
        notes: '粗骨料',
      },
    ],
  },
];

// 材料兼容性检查
export function checkMaterialCompatibility(materialIds: string[]): {
  compatible: boolean;
  conflicts: string[];
  recommendations: string[];
} {
  const materials = materialIds
    .map(id => ENHANCED_MATERIALS.find(m => m.id === id))
    .filter(Boolean) as EnhancedMaterial[];

  const conflicts: string[] = [];
  const recommendations: string[] = [];

  // 检查化学兼容性
  for (let i = 0; i < materials.length; i++) {
    for (let j = i + 1; j < materials.length; j++) {
      const mat1 = materials[i];
      const mat2 = materials[j];

      if (!mat1 || !mat2) continue;

      // 检查兼容性矩阵
      if (mat1.compatibilityMatrix && !mat1.compatibilityMatrix.includes(mat2.id)) {
        conflicts.push(`${mat1.name} 与 ${mat2.name} 可能不兼容`);
      }

      // 特殊化学反应检查
      if (mat1.chemicalComposition?.SO3 && mat2.chemicalComposition?.Al2O3) {
        if (mat1.chemicalComposition.SO3 + mat2.chemicalComposition.Al2O3 > 8) {
          conflicts.push(`硫酸盐含量过高，可能导致延迟钙矾石形成`);
        }
      }
    }
  }

  // 生成推荐
  const hasHighStrengthCement = materials.some(
    m => m.category === MaterialCategory.CEMENTITIOUS && (m.strength || 0) >= 42.5
  );

  const hasSuperplasticizer = materials.some(m => m.type === MaterialType.SUPERPLASTICIZER);

  if (hasHighStrengthCement && !hasSuperplasticizer) {
    recommendations.push('建议添加高效减水剂以提高工作性');
  }

  const hasSupplementary = materials.some(m => m.category === MaterialCategory.SUPPLEMENTARY);

  if (!hasSupplementary) {
    recommendations.push('建议添加掺合料以改善混凝土性能和降低成本');
  }

  return {
    compatible: conflicts.length === 0,
    conflicts,
    recommendations,
  };
}

// 材料成本计算
export function calculateMaterialCost(materialUsage: { materialId: string; amount: number }[]): {
  totalCost: number;
  breakdown: {
    materialId: string;
    name: string;
    amount: number;
    unitCost: number;
    totalCost: number;
  }[];
  carbonFootprint: number;
} {
  const breakdown = materialUsage.map(usage => {
    const material = ENHANCED_MATERIALS.find(m => m.id === usage.materialId);
    if (!material) {
      throw new Error(`Material not found: ${usage.materialId}`);
    }

    const unitCost = material.cost || 0;
    const totalCost = (usage.amount / 1000) * unitCost; // 转换为吨

    return {
      materialId: usage.materialId,
      name: material.name,
      amount: usage.amount,
      unitCost,
      totalCost,
    };
  });

  const totalCost = breakdown.reduce((sum, item) => sum + item.totalCost, 0);

  const carbonFootprint = materialUsage.reduce((sum, usage) => {
    const material = ENHANCED_MATERIALS.find(m => m.id === usage.materialId);
    return sum + ((material?.carbonFootprint || 0) * usage.amount) / 1000;
  }, 0);

  return {
    totalCost,
    breakdown,
    carbonFootprint,
  };
}
