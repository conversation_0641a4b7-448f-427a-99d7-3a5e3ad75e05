# SonarCloud 配置
sonar.projectKey=lxfeng_tmh-task-dispatcher
sonar.organization=lxfeng

# 项目信息
sonar.projectName=TMH Task Dispatcher
sonar.projectVersion=1.0

# 源代码路径
sonar.sources=src
sonar.tests=src
sonar.test.inclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx
sonar.exclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx,**/node_modules/**,**/.next/**,**/coverage/**,**/dist/**,**/build/**

# TypeScript 配置
sonar.typescript.lcov.reportPaths=coverage/lcov.info

# JavaScript/TypeScript 文件扩展名
sonar.javascript.file.suffixes=.js,.jsx
sonar.typescript.file.suffixes=.ts,.tsx

# 代码覆盖率
sonar.coverage.exclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx,**/node_modules/**,**/.next/**

# 其他设置
sonar.sourceEncoding=UTF-8
