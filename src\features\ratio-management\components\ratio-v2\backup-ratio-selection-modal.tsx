'use client';

import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  CheckCircle,
  Clock,
  Target,
  Droplets,
  Package,
  Star,
  Tag,
  Calendar,
  Trash2,
  Eye,
  X,
} from 'lucide-react';

import { <PERSON><PERSON> } from '@/shared/components/button';
import { Input } from '@/shared/components/input';
import { Badge } from '@/shared/components/badge';
import { Card, CardContent } from '@/shared/components/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';

import type {
  BackupRatioListItem,
  BackupRatioFilter,
  BackupRatioSortBy,
  BackupRatioSortOrder,
} from '@/core/types/ratio-backup';
import { ratioBackupService } from '@/features/ratio-management/services/ratio-backup';

interface BackupRatioSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (ratioId: string) => Promise<void>;
  taskId?: string;
}

export function BackupRatioSelectionModal({
  isOpen,
  onClose,
  onSelect,
  taskId,
}: BackupRatioSelectionModalProps) {
  const [ratios, setRatios] = useState<BackupRatioListItem[]>([]);
  const [filteredRatios, setFilteredRatios] = useState<BackupRatioListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRatioId, setSelectedRatioId] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');
  const [sortBy, setSortBy] = useState<BackupRatioSortBy>('createdAt');
  const [sortOrder, setSortOrder] = useState<BackupRatioSortOrder>('desc');
  const [strengthFilter, setStrengthFilter] = useState<string>('');

  // 加载备选配比列表
  const loadRatios = async () => {
    setLoading(true);
    try {
      const filter: BackupRatioFilter = {
        taskId,
        searchText: searchText || undefined,
        targetStrength: strengthFilter && strengthFilter !== 'all' ? [strengthFilter] : undefined,
      };

      const ratioList = await ratioBackupService.getBackupRatios(filter, sortBy, sortOrder);
      setRatios(ratioList);
      setFilteredRatios(ratioList);
    } catch (error) {
      console.error('加载备选配比失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和依赖更新
  useEffect(() => {
    if (isOpen) {
      loadRatios();
    }
  }, [isOpen, taskId, sortBy, sortOrder]);

  // 搜索和过滤
  useEffect(() => {
    let filtered = ratios;

    // 搜索文本过滤
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(
        ratio =>
          ratio.name.toLowerCase().includes(searchLower) ||
          ratio.description?.toLowerCase().includes(searchLower) ||
          ratio.tags?.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // 强度等级过滤
    if (strengthFilter && strengthFilter !== 'all') {
      filtered = filtered.filter(ratio => ratio.targetStrength === strengthFilter);
    }

    setFilteredRatios(filtered);
  }, [ratios, searchText, strengthFilter]);

  // 选择配比
  const handleSelectRatio = async () => {
    if (!selectedRatioId) return;

    try {
      await onSelect(selectedRatioId);
      onClose();
    } catch (error) {
      console.error('应用备选配比失败:', error);
    }
  };

  // 删除配比
  const handleDeleteRatio = async (ratioId: string, e: React.MouseEvent) => {
    e.stopPropagation();

    if (!confirm('确定要删除这个备选配比吗？此操作不可撤销。')) {
      return;
    }

    try {
      await ratioBackupService.deleteBackupRatio(ratioId);
      await loadRatios(); // 重新加载列表
      if (selectedRatioId === ratioId) {
        setSelectedRatioId(null);
      }
    } catch (error) {
      console.error('删除备选配比失败:', error);
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return '今天';
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hour = String(date.getHours()).padStart(2, '0');
      const minute = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hour}:${minute}`;
    }
  };

  // 获取所有强度等级选项
  const strengthOptions = Array.from(
    new Set(ratios.map(r => r.targetStrength).filter(s => s && s.trim() !== ''))
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-[800px] max-h-[80vh] flex flex-col'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <CheckCircle className='h-5 w-5 text-green-600' />
            选择备选配比
          </DialogTitle>
          <DialogDescription>从已保存的备选配比中选择一个应用到当前设计</DialogDescription>
        </DialogHeader>

        {/* 搜索和过滤区域 */}
        <div className='flex gap-2 py-2 border-b'>
          <div className='flex-1 relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
            <Input
              placeholder='搜索配比名称、描述或标签...'
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              className='pl-10'
            />
          </div>

          <Select value={strengthFilter} onValueChange={setStrengthFilter}>
            <SelectTrigger className='w-32'>
              <SelectValue placeholder='强度等级' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>全部强度</SelectItem>
              {strengthOptions
                .map(
                  strength =>
                    strength && (
                      <SelectItem key={strength} value={strength}>
                        {strength}
                      </SelectItem>
                    )
                )
                .filter(Boolean)}
            </SelectContent>
          </Select>

          <Select
            value={`${sortBy}-${sortOrder}`}
            onValueChange={value => {
              const [newSortBy, newSortOrder] = value.split('-') as [
                BackupRatioSortBy,
                BackupRatioSortOrder,
              ];
              setSortBy(newSortBy);
              setSortOrder(newSortOrder);
            }}
          >
            <SelectTrigger className='w-40'>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='createdAt-desc'>最新创建</SelectItem>
              <SelectItem value='createdAt-asc'>最早创建</SelectItem>
              <SelectItem value='name-asc'>名称 A-Z</SelectItem>
              <SelectItem value='name-desc'>名称 Z-A</SelectItem>
              <SelectItem value='qualityScore-desc'>质量评分高</SelectItem>
              <SelectItem value='qualityScore-asc'>质量评分低</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 配比列表 */}
        <div className='flex-1 overflow-y-auto space-y-2 py-2'>
          {loading ? (
            <div className='flex items-center justify-center py-8'>
              <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600' />
              <span className='ml-2 text-gray-600'>加载中...</span>
            </div>
          ) : filteredRatios.length === 0 ? (
            <div className='text-center py-8 text-gray-500'>
              {ratios.length === 0 ? '暂无备选配比' : '没有找到匹配的配比'}
            </div>
          ) : (
            filteredRatios.map(ratio => (
              <Card
                key={ratio.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedRatioId === ratio.id
                    ? 'ring-2 ring-blue-500 bg-blue-50'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setSelectedRatioId(ratio.id)}
              >
                <CardContent className='p-3'>
                  <div className='flex items-start justify-between'>
                    <div className='flex-1 space-y-2'>
                      {/* 配比名称和基本信息 */}
                      <div className='flex items-center gap-2'>
                        <h4 className='font-medium text-sm'>{ratio.name}</h4>
                        {ratio.qualityScore && (
                          <Badge variant='secondary' className='text-xs'>
                            <Star className='h-3 w-3 mr-1' />
                            {ratio.qualityScore.toFixed(1)}分
                          </Badge>
                        )}
                      </div>

                      {/* 配比参数 */}
                      <div className='grid grid-cols-4 gap-2 text-xs text-gray-600'>
                        <div className='flex items-center gap-1'>
                          <Target className='h-3 w-3' />
                          {ratio.targetStrength || 'N/A'}
                        </div>
                        <div className='flex items-center gap-1'>
                          <Droplets className='h-3 w-3' />
                          {ratio.waterCementRatio?.toFixed(3) || 'N/A'}
                        </div>
                        <div className='flex items-center gap-1'>
                          <Package className='h-3 w-3' />
                          {ratio.totalMaterials}种材料
                        </div>
                        <div className='flex items-center gap-1'>
                          <Calendar className='h-3 w-3' />
                          {formatDate(ratio.createdAt)}
                        </div>
                      </div>

                      {/* 描述 */}
                      {ratio.description && (
                        <p className='text-xs text-gray-600 line-clamp-2'>{ratio.description}</p>
                      )}

                      {/* 标签 */}
                      {ratio.tags && ratio.tags.length > 0 && (
                        <div className='flex flex-wrap gap-1'>
                          {ratio.tags.map(tag => (
                            <Badge key={tag} variant='outline' className='text-xs'>
                              <Tag className='h-2 w-2 mr-1' />
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* 操作按钮 */}
                    <div className='flex items-center gap-1 ml-2'>
                      <Button
                        variant='ghost'
                        size='sm'
                        className='h-6 w-6 p-0 text-red-600 hover:bg-red-50'
                        onClick={e => handleDeleteRatio(ratio.id, e)}
                        title='删除配比'
                      >
                        <Trash2 className='h-3 w-3' />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* 底部操作区域 */}
        <div className='flex items-center justify-between pt-2 border-t'>
          <div className='text-sm text-gray-500'>
            {filteredRatios.length > 0 && <>共 {filteredRatios.length} 个配比</>}
          </div>

          <div className='flex gap-2'>
            <Button variant='outline' onClick={onClose}>
              取消
            </Button>
            <Button onClick={handleSelectRatio} disabled={!selectedRatioId}>
              <CheckCircle className='h-4 w-4 mr-2' />
              应用配比
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
