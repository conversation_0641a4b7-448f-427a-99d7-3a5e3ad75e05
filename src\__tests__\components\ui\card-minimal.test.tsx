/**
 * 最简化Card组件测试
 */

describe('最简化Card组件导入测试', () => {
  it('应该能够导入所有Card组件', () => {
    // 动态导入测试
    const cardModule = require('@/components/ui/card-minimal');

    console.log('Minimal Card module keys:', Object.keys(cardModule));
    console.log('Card:', typeof cardModule.Card);
    console.log('CardHeader:', typeof cardModule.CardHeader);
    console.log('CardTitle:', typeof cardModule.CardTitle);
    console.log('CardDescription:', typeof cardModule.CardDescription);
    console.log('CardContent:', typeof cardModule.CardContent);
    console.log('CardFooter:', typeof cardModule.CardFooter);

    // 检查每个组件是否存在
    expect(cardModule.Card).toBeDefined();
    expect(cardModule.CardHeader).toBeDefined();
    expect(cardModule.CardTitle).toBeDefined();
    expect(cardModule.CardDescription).toBeDefined();
    expect(cardModule.CardContent).toBeDefined();
    expect(cardModule.CardFooter).toBeDefined();
  });
});
