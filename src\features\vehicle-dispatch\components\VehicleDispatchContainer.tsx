/**
 * 重构后的车辆调度容器组件
 * 采用分层架构：UI组件只负责展示，业务逻辑通过Hook封装
 * 支持可移动、尺寸缩小、mock数据和API切换功能
 */

'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useTaskListSettings } from '@/features/task-management/hooks/useTaskListSettings';
import { useVehicleDispatchLogic } from '@/features/vehicle-dispatch/hooks/useVehicleDispatchLogic';
import { useVehicleContextMenu } from '@/features/vehicle-dispatch/hooks/useVehicleContextMenu';
import { Card, CardContent } from '@/shared/components/card';
import { Button } from '@/shared/components/button';
import { Badge } from '@/shared/components/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
} from '@/shared/components/dropdown-menu';
import { VehicleList } from './VehicleList';
import { DataSourceSwitcher } from '@/core/utils/dataSourceSwitcher';
import type { Vehicle } from '@/core/types';
import { Database, Cloud, Settings } from 'lucide-react';
import { cn } from '@/core/lib/utils';
import { DispatchedVehiclesContainer } from './DispatchedVehiclesContainer';
import { generateDispatchedVehicles } from '@/infrastructure/api/mock/mock-data';

// 可移动车辆调度容器的接口
interface MovableVehicleDispatchContainerProps {
  initialPosition?: { x: number; y: number };
  initialSize?: { width: number; height: number };
  gridColumns?: 2 | 3 | 4 | 5 | 6;
  onClose?: () => void;
}

/**
 * 可移动的车辆调度容器组件
 */
export function MovableVehicleDispatchContainer({
  initialPosition = { x: 100, y: 100 },
  initialSize = { width: 350, height: 500 },
  gridColumns = 4,
  onClose,
}: MovableVehicleDispatchContainerProps) {
  // 位置和尺寸状态
  const [position, setPosition] = useState(initialPosition);
  const [size, setSize] = useState(initialSize);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  // 数据源状态
  const [dataSource, setDataSource] = useState<'mock' | 'api'>(() =>
    DataSourceSwitcher.getCurrentMode()
  );

  // 引用
  const containerRef = useRef<HTMLDivElement>(null);

  // 业务逻辑Hook
  const dispatchLogic = useVehicleDispatchLogic();
  const contextMenu = useVehicleContextMenu();

  // 设置Hook
  const { settings } = useTaskListSettings();
  const inTaskVehicleCardStyles = settings.inTaskVehicleCardStyles;
  const density = settings.density;

  // 获取车辆统计信息
  const vehicleStats = dispatchLogic.getVehicleStats();

  // 拖拽事件处理
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget || (e.target as HTMLElement).closest('.drag-handle')) {
      setIsDragging(true);
      const rect = containerRef.current?.getBoundingClientRect();
      if (rect) {
        setDragOffset({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top,
        });
      }
    }
  };

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isDragging) {
        setPosition({
          x: e.clientX - dragOffset.x,
          y: e.clientY - dragOffset.y,
        });
      }
    },
    [isDragging, dragOffset.x, dragOffset.y]
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 数据源切换
  const handleDataSourceToggle = () => {
    const newMode = DataSourceSwitcher.toggleMode();
    setDataSource(newMode);
    // 刷新页面以应用新的数据源
    window.location.reload();
  };

  // 最小化/最大化切换
  const handleMinimizeToggle = () => {
    setIsMinimized(!isMinimized);
  };

  // 监听全局鼠标事件
  useEffect(() => {
    if (isDragging) {
      const mouseMove = handleMouseMove as EventListener;
      const mouseUp = handleMouseUp as EventListener;
      document.addEventListener('mousemove', mouseMove);
      document.addEventListener('mouseup', mouseUp);
      return () => {
        document.removeEventListener('mousemove', mouseMove);
        document.removeEventListener('mouseup', mouseUp);
      };
    }
    return undefined;
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div
      ref={containerRef}
      className={cn('fixed ', isMinimized ? 'h-auto' : '')}
      style={{
        left: position.x,
        top: position.y,
        width: isMinimized ? 'auto' : size.width,
        height: isMinimized ? 'auto' : size.height,
        minWidth: 280,
        minHeight: isMinimized ? 'auto' : 350,
      }}
    >
      {/* 标题栏 */}
      <div className='drag-handle flex items-center justify-between p-2 bg-gray-50 border-b rounded-t-lg'>
        <div className='flex items-center gap-2'>
          <span className='text-sm font-medium'>车辆调度</span>
        </div>
      </div>

      {/* 内容区域 */}
      {!isMinimized && (
        <div className='flex-1 overflow-hidden'>
          <div className='p-2'>
            <div className='h-[85vh] flex flex-col gap-2'>
              {/* 待发车辆 - 占1/3高度 */}
              <Card className='flex-1 flex flex-col min-h-0'>
                <div className='p-2 text-xs'>待发车辆 ({dispatchLogic.pendingVehicles.length})</div>
                <CardContent className='pt-0 flex-1 overflow-hidden'>
                  <div className='h-full overflow-y-auto'>
                    <VehicleList
                      title=''
                      vehicles={dispatchLogic.pendingVehicles}
                      listType='pending'
                      globalDispatchActive={dispatchLogic.globalDispatchActive}
                      displayMode={dispatchLogic.vehicleDisplayMode}
                      inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                      density={density}
                      columns={gridColumns}
                      onContextMenu={contextMenu.openContextMenu}
                      onVisualMove={dispatchLogic.handleVisualMove}
                      onCommitReorder={dispatchLogic.handleCommitReorder}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* 退货车辆 - 占1/3高度 */}
              <Card className='flex-1 flex flex-col min-h-0'>
                <div className='p-2 text-sm'>退货车辆</div>
                <CardContent className='pt-0 flex-1 overflow-hidden'>
                  <div className='h-full overflow-y-auto '>
                    <VehicleList
                      title=''
                      vehicles={dispatchLogic.outboundVehicles}
                      listType='outbound'
                      globalDispatchActive={dispatchLogic.globalDispatchActive}
                      displayMode={dispatchLogic.vehicleDisplayMode}
                      inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                      density={density}
                      columns={gridColumns}
                      onContextMenu={contextMenu.openContextMenu}
                      onVisualMove={dispatchLogic.handleVisualMove}
                      onCommitReorder={dispatchLogic.handleCommitReorder}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* 已出厂车辆 - 占1/3高度，使用表格 */}
              <Card className='flex-1 flex flex-col min-h-0'>
                <div className='p-2 text-sm'>已出厂车辆</div>
                <CardContent className='pt-0 flex-1 overflow-hidden'>
                  <div className='h-full overflow-y-auto'>
                    <table className='w-full border-collapse'>
                      <thead className='sticky top-0 bg-white'>
                        <tr className='bg-gray-50 h-7'>
                          <th className='text-xs font-medium text-left px-2 py-1 border-b h-7'>
                            车牌号
                          </th>
                          <th className='text-xs font-medium text-left px-2 py-1 border-b h-7'>
                            状态
                          </th>
                          <th className='text-xs font-medium text-left px-2 py-1 border-b h-7'>
                            出厂时间
                          </th>
                          <th className='text-xs font-medium text-left px-2 py-1 border-b h-7'>
                            操作
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {dispatchLogic.returnedVehicles.map(vehicle => (
                          <tr
                            key={vehicle.id}
                            className='border-t hover:bg-gray-50 h-8'
                            onContextMenu={e => {
                              e.preventDefault();
                              contextMenu.openContextMenu(e, vehicle);
                            }}
                          >
                            <td className='px-2 py-1 text-xs h-8'>{vehicle.vehicleNumber}</td>
                            <td className='px-2 py-1 text-xs h-8'>
                              <span className='inline-flex items-center px-1 py-0.5 rounded-full text-xs bg-green-100 text-green-800'>
                                {vehicle.status}
                              </span>
                            </td>
                            <td className='px-2 py-1 text-xs text-gray-500 h-8'>
                              {new Date().toLocaleString()}
                            </td>
                            <td className='px-2 py-1 text-xs h-8'>
                              <button
                                className='text-xs text-blue-600 hover:text-blue-800 underline'
                                onClick={e => contextMenu.openContextMenu(e, vehicle)}
                              >
                                详情
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      )}

      {/* 右键菜单 */}
      {contextMenu.contextMenuOpen &&
        contextMenu.contextMenuPosition &&
        contextMenu.contextMenuVehicle && (
          <DropdownMenu
            open={contextMenu.contextMenuOpen}
            onOpenChange={contextMenu.closeContextMenu}
          >
            <DropdownMenuPortal>
              <DropdownMenuContent
                style={{
                  position: 'fixed',
                  left: contextMenu.contextMenuPosition.x,
                  top: contextMenu.contextMenuPosition.y,
                }}
                onCloseAutoFocus={e => e.preventDefault()}
              >
                {contextMenu
                  .getAvailableActions(contextMenu.contextMenuVehicle)
                  .map((action, index) => (
                    <React.Fragment key={action.id}>
                      <DropdownMenuItem
                        onClick={() =>
                          contextMenu.handleVehicleAction(
                            action.id,
                            contextMenu.contextMenuVehicle!
                          )
                        }
                      >
                        {action.label}
                      </DropdownMenuItem>
                      {index <
                        contextMenu.getAvailableActions(contextMenu.contextMenuVehicle!).length -
                          1 && <DropdownMenuSeparator />}
                    </React.Fragment>
                  ))}
              </DropdownMenuContent>
            </DropdownMenuPortal>
          </DropdownMenu>
        )}
    </div>
  );
}

/**
 * 固定版本的车辆调度容器组件
 * 用于右侧面板，固定在任务列表右侧
 */
export function FixedVehicleDispatchContainer({
  gridColumns = 4,
  onOpenSettings,
  onVehicleDispatchToTask,
}: {
  gridColumns?: 2 | 3 | 4 | 5 | 6;
  onOpenSettings?: () => void;
  onVehicleDispatchToTask?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
}) {
  // 业务逻辑Hook
  const dispatchLogic = useVehicleDispatchLogic();
  const contextMenu = useVehicleContextMenu();

  // 设置Hook
  const { settings } = useTaskListSettings();
  const inTaskVehicleCardStyles = settings.inTaskVehicleCardStyles;
  const density = settings.density;

  // 数据源状态
  const [dataSource, setDataSource] = useState<'mock' | 'api'>(() =>
    DataSourceSwitcher.getCurrentMode()
  );

  // 已出厂车辆数据
  const [dispatchedVehiclesData] = useState(() => generateDispatchedVehicles(20));

  // 数据源切换
  const handleDataSourceToggle = () => {
    const newMode = DataSourceSwitcher.toggleMode();
    setDataSource(newMode);
    // 刷新页面以应用新的数据源
    window.location.reload();
  };

  return (
    <div className='h-full flex flex-col bg-white border border-gray-200 rounded-lg'>
      {/* 标题栏 */}
      <div
        className='flex items-center justify-between p-1 flex-shrink-0'
        style={{ backgroundColor: 'hsl(var(--block-title))' }}
      >
        <div className='flex items-center gap-2'>
          <span className='text-sm font-medium'>车辆调度</span>
          <Badge variant={dataSource === 'mock' ? 'secondary' : 'default'} className='text-xs'>
            {dataSource === 'mock' ? 'Mock' : 'API'}
          </Badge>
        </div>

        <div className='flex items-center gap-1'>
          {/* 设置按钮 */}
          {onOpenSettings && (
            <Button
              variant='ghost'
              size='sm'
              onClick={onOpenSettings}
              className='h-6 w-6 p-0'
              title='设置'
            >
              <Settings className='w-3 h-3' />
            </Button>
          )}

          {/* 数据源切换按钮 */}
          <Button
            variant='ghost'
            size='sm'
            onClick={handleDataSourceToggle}
            className='h-6 w-6 p-0'
            title={`切换到${dataSource === 'mock' ? 'API' : 'Mock'}数据`}
          >
            {dataSource === 'mock' ? (
              <Database className='w-3 h-3' />
            ) : (
              <Cloud className='w-3 h-3' />
            )}
          </Button>
        </div>
      </div>

      {/* 内容区域 */}
      <div className='flex-1 overflow-hidden'>
        <div className='h-full flex flex-col gap-2'>
          {/* 待发车辆 - 占1/4高度 */}
          <Card className='flex-1 flex flex-col min-h-0'>
            <div className='p-1 text-xs'>待发车辆 ({dispatchLogic.pendingVehicles.length})</div>
            <CardContent className='pt-0 p-1 flex-1 overflow-hidden'>
              <div className='h-full overflow-y-auto'>
                <VehicleList
                  title=''
                  vehicles={dispatchLogic.pendingVehicles}
                  listType='pending'
                  globalDispatchActive={dispatchLogic.globalDispatchActive}
                  displayMode={dispatchLogic.vehicleDisplayMode}
                  inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                  density={density}
                  columns={gridColumns}
                  onContextMenu={contextMenu.openContextMenu}
                  onVisualMove={dispatchLogic.handleVisualMove}
                  onCommitReorder={dispatchLogic.handleCommitReorder}
                />
              </div>
            </CardContent>
          </Card>

          {/* 退货车辆 - 占1/4高度 */}
          <Card className='flex-1 flex flex-col min-h-0'>
            <div className='p-1 text-xs'>退货车辆 ({dispatchLogic.returnedVehicles.length})</div>
            <CardContent className='pt-0 p-1 flex-1 overflow-hidden'>
              <div className='h-full overflow-y-auto'>
                <VehicleList
                  title=''
                  vehicles={dispatchLogic.returnedVehicles}
                  listType='returned'
                  globalDispatchActive={dispatchLogic.globalDispatchActive}
                  displayMode={dispatchLogic.vehicleDisplayMode}
                  inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                  density={density}
                  columns={gridColumns}
                  onContextMenu={contextMenu.openContextMenu}
                  onVisualMove={dispatchLogic.handleVisualMove}
                  onCommitReorder={dispatchLogic.handleCommitReorder}
                />
              </div>
            </CardContent>
          </Card>

          {/* 已出厂车辆 - 占1/3高度，使用新表格显示 */}
          <Card className='flex-1 flex flex-col min-h-0'>
            <div className='p-1 text-xs'>已出厂车辆 ({dispatchedVehiclesData.length})</div>
            <CardContent className='pt-0 flex-1 overflow-hidden p-1'>
              <DispatchedVehiclesContainer data={dispatchedVehiclesData} className='h-full' />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 右键菜单 */}
      {contextMenu.contextMenuOpen &&
        contextMenu.contextMenuPosition &&
        contextMenu.contextMenuVehicle && (
          <DropdownMenu
            open={contextMenu.contextMenuOpen}
            onOpenChange={contextMenu.closeContextMenu}
          >
            <DropdownMenuPortal>
              <DropdownMenuContent
                style={{
                  position: 'fixed',
                  left: contextMenu.contextMenuPosition?.x || 0,
                  top: contextMenu.contextMenuPosition?.y || 0,
                }}
                onCloseAutoFocus={e => e.preventDefault()}
              >
                {contextMenu.contextMenuVehicle &&
                  contextMenu
                    .getAvailableActions(contextMenu.contextMenuVehicle)
                    .map((action, index) => (
                      <React.Fragment key={action.id}>
                        <DropdownMenuItem
                          onClick={() =>
                            contextMenu.handleVehicleAction(
                              action.id,
                              contextMenu.contextMenuVehicle!
                            )
                          }
                        >
                          {action.label}
                        </DropdownMenuItem>
                        {index <
                          contextMenu.getAvailableActions(contextMenu.contextMenuVehicle!).length -
                            1 && <DropdownMenuSeparator />}
                      </React.Fragment>
                    ))}
              </DropdownMenuContent>
            </DropdownMenuPortal>
          </DropdownMenu>
        )}
    </div>
  );
}

/**
 * 原始的车辆调度容器组件（保持向后兼容）
 * 默认使用固定版本，适合右侧面板
 */
export function VehicleDispatchContainer() {
  return <FixedVehicleDispatchContainer gridColumns={4} />;
}
