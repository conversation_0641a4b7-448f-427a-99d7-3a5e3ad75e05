/**
 * 拖拽性能监控Hook
 * 用于监控和优化拖拽操作的性能
 */

import { useCallback, useRef } from 'react';

interface DragPerformanceMetrics {
  frameCount: number;
  averageFrameTime: number;
  maxFrameTime: number;
  minFrameTime: number;
  droppedFrames: number;
}

interface UseDragPerformanceOptions {
  /** 是否启用性能监控 */
  enabled?: boolean;
  /** 性能报告回调 */
  onPerformanceReport?: (metrics: DragPerformanceMetrics) => void;
  /** 目标帧率 */
  targetFPS?: number;
}

export function useDragPerformance({
  enabled = false,
  onPerformanceReport,
  targetFPS = 60,
}: UseDragPerformanceOptions = {}) {
  const metricsRef = useRef<{
    startTime: number;
    lastFrameTime: number;
    frameCount: number;
    frameTimes: number[];
    droppedFrames: number;
  }>({
    startTime: 0,
    lastFrameTime: 0,
    frameCount: 0,
    frameTimes: [],
    droppedFrames: 0,
  });

  const targetFrameTime = 1000 / targetFPS;

  const startMonitoring = useCallback(() => {
    if (!enabled) return;

    const now = performance.now();
    metricsRef.current = {
      startTime: now,
      lastFrameTime: now,
      frameCount: 0,
      frameTimes: [],
      droppedFrames: 0,
    };
  }, [enabled]);

  const recordFrame = useCallback(() => {
    if (!enabled) return;

    const now = performance.now();
    const frameTime = now - metricsRef.current.lastFrameTime;

    metricsRef.current.frameCount++;
    metricsRef.current.frameTimes.push(frameTime);

    // 检测掉帧
    if (frameTime > targetFrameTime * 1.5) {
      metricsRef.current.droppedFrames++;
    }

    metricsRef.current.lastFrameTime = now;
  }, [enabled, targetFrameTime]);

  const stopMonitoring = useCallback(() => {
    if (!enabled || !onPerformanceReport) return;

    const metrics = metricsRef.current;
    const frameTimes = metrics.frameTimes;

    if (frameTimes.length === 0) return;

    const totalTime = frameTimes.reduce((sum, time) => sum + time, 0);
    const averageFrameTime = totalTime / frameTimes.length;
    const maxFrameTime = Math.max(...frameTimes);
    const minFrameTime = Math.min(...frameTimes);

    const performanceMetrics: DragPerformanceMetrics = {
      frameCount: metrics.frameCount,
      averageFrameTime,
      maxFrameTime,
      minFrameTime,
      droppedFrames: metrics.droppedFrames,
    };

    onPerformanceReport(performanceMetrics);
  }, [enabled, onPerformanceReport]);

  return {
    startMonitoring,
    recordFrame,
    stopMonitoring,
  };
}

/**
 * 拖拽性能优化工具函数
 */
export const dragPerformanceUtils = {
  /**
   * 创建优化的事件监听器选项
   */
  getOptimizedEventOptions: () => ({
    passive: false,
    capture: false,
  }),

  /**
   * 节流函数 - 使用 requestAnimationFrame
   */
  throttleRAF: <T extends (...args: any[]) => void>(fn: T): T => {
    let rafId: number | null = null;
    let lastArgs: Parameters<T>;

    return ((...args: Parameters<T>) => {
      lastArgs = args;

      if (rafId === null) {
        rafId = requestAnimationFrame(() => {
          fn(...lastArgs);
          rafId = null;
        });
      }
    }) as T;
  },

  /**
   * 防抖函数
   */
  debounce: <T extends (...args: any[]) => void>(fn: T, delay: number): T => {
    let timeoutId: NodeJS.Timeout;

    return ((...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => fn(...args), delay);
    }) as T;
  },

  /**
   * 检查是否支持硬件加速
   */
  supportsHardwareAcceleration: (): boolean => {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    return !!gl;
  },

  /**
   * 获取优化的CSS样式
   */
  getOptimizedStyles: (isDragging: boolean) => ({
    willChange: isDragging ? 'transform' : 'auto',
    transform: 'translateZ(0)', // 强制硬件加速
    backfaceVisibility: 'hidden' as const,
    perspective: 1000,
  }),

  /**
   * 性能监控装饰器
   */
  withPerformanceMonitoring: <T extends (...args: any[]) => void>(fn: T, label: string): T => {
    return ((...args: Parameters<T>) => {
      const start = performance.now();
      const result = fn(...args);
      const end = performance.now();

      if (end - start > 16.67) {
        // 超过一帧的时间
        console.warn(`Performance warning: ${label} took ${(end - start).toFixed(2)}ms`);
      }

      return result;
    }) as T;
  },
};
