# 🛠️ 技术栈

## 📋 技术栈概览

TMH车辆调度系统采用现代化的前端技术栈，注重开发效率、性能优化和用户体验。

## 🎯 核心技术

### 前端框架
| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|----------|
| **React** | 18.x | UI框架 | 成熟稳定、生态丰富、组件化开发 |
| **Next.js** | 15.x | 全栈框架 | SSR/SSG支持、路由系统、性能优化 |
| **TypeScript** | 5.x | 类型系统 | 类型安全、开发体验、代码质量 |

### 状态管理
| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|----------|
| **Zustand** | 4.x | 状态管理 | 轻量级、简单易用、TypeScript友好 |
| **Immer** | 10.x | 不可变数据 | 简化状态更新、性能优化 |

### 样式和UI
| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|----------|
| **Tailwind CSS** | 3.x | CSS框架 | 原子化CSS、快速开发、一致性 |
| **shadcn/ui** | Latest | 组件库 | 现代设计、可定制、TypeScript支持 |
| **Lucide React** | Latest | 图标库 | 轻量级、一致性、可定制 |

## 🔧 开发工具

### 构建和打包
| 工具 | 版本 | 用途 | 特点 |
|------|------|------|------|
| **Turbopack** | Latest | 构建工具 | 极速构建、热重载、Rust驱动 |
| **Webpack** | 5.x | 模块打包 | 成熟稳定、插件丰富、配置灵活 |
| **SWC** | Latest | 编译器 | 快速编译、Rust实现、Next.js集成 |

### 代码质量
| 工具 | 版本 | 用途 | 配置 |
|------|------|------|------|
| **ESLint** | 8.x | 代码检查 | 严格规则、自动修复、团队规范 |
| **Prettier** | 3.x | 代码格式化 | 统一格式、自动格式化、团队一致性 |
| **Husky** | 8.x | Git钩子 | 提交前检查、代码质量保证 |

### 测试框架
| 工具 | 版本 | 用途 | 特点 |
|------|------|------|------|
| **Jest** | 29.x | 测试框架 | 零配置、快照测试、覆盖率报告 |
| **Testing Library** | 14.x | 组件测试 | 用户行为测试、最佳实践 |
| **MSW** | 2.x | API模拟 | 网络请求拦截、真实环境模拟 |

## 📦 依赖管理

### 包管理器
```json
{
  "packageManager": "npm@10.x",
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=9.0.0"
  }
}
```

### 核心依赖
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "next": "^15.2.4",
    "typescript": "^5.3.0",
    "zustand": "^4.4.7",
    "immer": "^10.0.3",
    "tailwindcss": "^3.4.0",
    "@radix-ui/react-*": "^1.0.0",
    "lucide-react": "^0.300.0"
  }
}
```

### 开发依赖
```json
{
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/node": "^20.0.0",
    "eslint": "^8.56.0",
    "prettier": "^3.1.0",
    "jest": "^29.7.0",
    "@testing-library/react": "^14.1.0",
    "husky": "^8.0.3"
  }
}
```

## 🎨 UI/UX技术

### 设计系统
| 组件 | 技术 | 特点 |
|------|------|------|
| **基础组件** | shadcn/ui | 可访问性、可定制、现代设计 |
| **图标系统** | Lucide React | 一致性、轻量级、SVG图标 |
| **主题系统** | CSS变量 | 动态主题、深色模式、品牌定制 |

### 响应式设计
```css
/* Tailwind CSS 断点 */
sm: 640px   /* 小屏设备 */
md: 768px   /* 平板设备 */
lg: 1024px  /* 笔记本电脑 */
xl: 1280px  /* 桌面显示器 */
2xl: 1536px /* 大屏显示器 */
```

### 动画和交互
| 技术 | 用途 | 特点 |
|------|------|------|
| **CSS Transitions** | 基础动画 | 性能优秀、简单易用 |
| **Framer Motion** | 复杂动画 | 声明式、手势支持、布局动画 |
| **React Spring** | 物理动画 | 基于物理、流畅自然 |

## 🚀 性能优化

### 代码优化
| 技术 | 用途 | 效果 |
|------|------|------|
| **代码分割** | 按需加载 | 减少初始包大小 |
| **Tree Shaking** | 死代码消除 | 减少最终包大小 |
| **懒加载** | 延迟加载 | 提升首屏性能 |

### 渲染优化
```typescript
// React 性能优化技术
React.memo()           // 组件记忆化
useMemo()             // 值记忆化
useCallback()         // 函数记忆化
React.lazy()          // 组件懒加载
Suspense             // 异步组件边界
```

### 网络优化
| 技术 | 用途 | 效果 |
|------|------|------|
| **HTTP/2** | 多路复用 | 减少网络延迟 |
| **Gzip压缩** | 资源压缩 | 减少传输大小 |
| **CDN** | 内容分发 | 提升加载速度 |

## 🔍 监控和调试

### 开发工具
| 工具 | 用途 | 特点 |
|------|------|------|
| **React DevTools** | 组件调试 | 组件树、状态检查、性能分析 |
| **Redux DevTools** | 状态调试 | 状态变化、时间旅行、调试 |
| **Chrome DevTools** | 性能调试 | 网络、性能、内存分析 |

### 错误监控
```typescript
// 错误边界组件
class ErrorBoundary extends React.Component {
  componentDidCatch(error, errorInfo) {
    // 错误上报
    console.error('Error caught:', error, errorInfo);
  }
}
```

### 性能监控
```typescript
// 性能指标收集
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    // 性能数据上报
    console.log(entry.name, entry.duration);
  }
});
```

## 📱 移动端技术

### 响应式适配
| 技术 | 用途 | 特点 |
|------|------|------|
| **Viewport Meta** | 视口设置 | 移动端适配基础 |
| **Flexible Layout** | 弹性布局 | 自适应不同屏幕 |
| **Touch Events** | 触摸交互 | 移动端手势支持 |

### PWA支持
```json
{
  "name": "TMH车辆调度系统",
  "short_name": "TMH调度",
  "display": "standalone",
  "start_url": "/",
  "theme_color": "#000000",
  "background_color": "#ffffff"
}
```

## 🔒 安全技术

### 前端安全
| 技术 | 用途 | 防护 |
|------|------|------|
| **CSP** | 内容安全策略 | XSS攻击防护 |
| **HTTPS** | 传输加密 | 中间人攻击防护 |
| **SameSite Cookie** | Cookie安全 | CSRF攻击防护 |

### 数据验证
```typescript
// 输入验证示例
import { z } from 'zod';

const TaskSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1).max(100),
  priority: z.enum(['low', 'medium', 'high']),
  dueDate: z.date()
});
```

## 🌐 国际化

### i18n技术栈
| 技术 | 用途 | 特点 |
|------|------|------|
| **next-i18next** | 国际化框架 | Next.js集成、SSR支持 |
| **react-i18next** | React集成 | 组件级翻译、动态加载 |
| **ICU MessageFormat** | 消息格式 | 复数、日期、数字格式化 |

## 📊 数据可视化

### 图表库
| 库 | 用途 | 特点 |
|------|------|------|
| **Recharts** | 基础图表 | React原生、响应式、可定制 |
| **D3.js** | 复杂可视化 | 强大灵活、自定义程度高 |
| **Chart.js** | 简单图表 | 轻量级、易用、动画效果 |

---

**文档版本**: v1.0  
**最后更新**: 2025/7/16  
**技术负责人**: TMH技术团队
