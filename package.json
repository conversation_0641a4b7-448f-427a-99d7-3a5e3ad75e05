{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "build:no-lint": "cross-env ESLINT_NO_DEV_ERRORS=true next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:unit": "jest --testPathPatterns=__tests__", "test:integration": "jest --testPathPatterns=integration", "test:performance": "jest --testNamePattern='性能测试|performance'", "format": "prettier --write .", "format:check": "prettier --check .", "format:staged": "prettier --write --cache --ignore-unknown", "analyze": "cross-env ANALYZE=true npm run build", "pre-commit": "node scripts/setup-pre-commit.js", "pre-commit:fix": "npx prettier --write \"src/**/*.{ts,tsx}\"", "pre-commit:npm": "npm run typecheck && npm run lint && npm run format:check", "pre-commit:fix:npm": "npm run lint -- --fix && npm run format && npm run typecheck", "quality:check": "npm run pre-commit:check", "quality:fix": "npm run pre-commit:fix", "prepare": "npx husky || echo '<PERSON><PERSON> not found, skipping git hooks setup'", "pre-commit:check": "npx prettier --check \"src/**/*.{ts,tsx}\"", "quality:optimize": "node scripts/code-quality-optimizer.js", "package:optimize": "node scripts/package-optimization.js", "package:analyze": "cross-env ANALYZE=true npm run build && npm run package:report", "package:report": "npx @next/bundle-analyzer .next/static/chunks/", "deploy:intranet": "node scripts/deploy-intranet.js", "setup:ssh": "node scripts/setup-ssh.js", "scripts:cleanup": "node scripts/cleanup-scripts.js", "scripts:cleanup:force": "node scripts/cleanup-scripts.js --force", "optimize:code": "node scripts/code-quality-optimizer.js", "build:docs": "node scripts/build-docs.js", "start:prod": "cross-env NODE_ENV=production npm run build && npm run start"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@genkit-ai/googleai": "^1.8.0", "@genkit-ai/next": "^1.8.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@tanstack/react-table": "^8.19.3", "@tanstack/react-virtual": "^3.8.0", "@types/qrcode": "^1.5.5", "babel-plugin-react-compiler": "^19.1.0-rc.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "firebase": "^11.8.1", "genkit": "^1.8.0", "idb": "^8.0.3", "immer": "^10.1.1", "lucide-react": "^0.475.0", "next": "15.2.4", "patch-package": "^8.0.0", "qrcode": "^1.5.4", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.54.2", "react-hooks-worker": "^1.0.3", "react-virtuoso": "^4.12.8", "recharts": "^2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.72", "zustand": "^4.5.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@jest/globals": "^30.0.4", "@next/bundle-analyzer": "^15.3.5", "@opentelemetry/exporter-jaeger": "^2.0.1", "@playwright/test": "^1.53.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "babel-jest": "^30.0.4", "cross-env": "^7.0.3", "eslint": "^9.30.1", "eslint-config-next": "^15.3.5", "genkit-cli": "^1.8.0", "husky": "^9.1.7", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "node-mocks-http": "^1.17.2", "null-loader": "^4.0.1", "postcss": "^8", "prettier": "^3.6.2", "tailwindcss": "^3.4.1", "typescript": "5.8.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}