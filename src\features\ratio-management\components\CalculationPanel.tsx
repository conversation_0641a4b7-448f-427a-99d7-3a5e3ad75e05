/**
 * 计算面板组件
 * 包含配比计算参数和结果显示
 */

import React, { useCallback, useMemo } from 'react';
import { Calculator, BookCopy } from 'lucide-react';
import { Controller } from 'react-hook-form';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { useRatioForm } from '@/features/ratio-management/hooks/ratio/useRatioForm';
import type { CalculationPanelProps } from '@/core/types/ratio';

/**
 * 计算面板组件
 */
export const CalculationPanel = React.memo<CalculationPanelProps>(function CalculationPanel({
  calculationParams,
  proportions,
  calculationMethod,
  reverseParams,
  onParamsChange,
  onReverseParamChange,
  onMethodChange,
  onCalculate,
  onReverseCalculate,
}) {
  const { form } = useRatioForm();
  const { control } = form;

  // 材料表头
  const materialHeaders = [
    '水',
    '水泥',
    '粉煤灰',
    '矿粉',
    'S105',
    '膨胀剂',
    '早强剂',
    '砂子',
    '石子',
    '外加剂',
    '防冻剂',
    '超细砂',
  ];

  // 配比结果键值映射 - 使用固定的键值数组，避免依赖 proportions 对象
  const proportionKeys = useMemo(
    () =>
      [
        'water',
        'cement',
        'flyAsh',
        'mineralPowder',
        's105Powder',
        'expansionAgent',
        'earlyStrengthAgent',
        'sand',
        'stone',
        'admixture',
        'antifreeze',
        'ultraFineSand',
      ] as const,
    []
  );

  // 分配比例选项
  const enabledDistributionMaterials = ['水', '砂子', '石子', '外加剂'];
  const waterDistributionOptions = ['1:1', '2:8', '3:7', '4:6', '1:9'];
  const otherDistributionOptions = ['1:1:1', '1:1', '2:8', '3:7', '4:6', '1:9'];

  // 渲染分配比例行
  const renderAllocationRow = () => {
    const cells: React.ReactNode[] = [];
    let disabledCellCount = 0;

    materialHeaders.forEach((header, index) => {
      const isEnabled = enabledDistributionMaterials.includes(header);

      if (isEnabled) {
        if (disabledCellCount > 0) {
          cells.push(
            <td
              key={`disabled-group-${index}`}
              colSpan={disabledCellCount}
              className='p-1 border bg-muted/20'
            />
          );
          disabledCellCount = 0;
        }

        const options = header === '水' ? waterDistributionOptions : otherDistributionOptions;
        cells.push(
          <td key={header} className='p-1 border'>
            <Select>
              <SelectTrigger className='h-6 w-full p-1 text-xs'>
                <SelectValue placeholder='-' />
              </SelectTrigger>
              <SelectContent>
                {options.map(opt => (
                  <SelectItem key={opt} value={opt}>
                    {opt}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </td>
        );
      } else {
        disabledCellCount++;
      }
    });

    if (disabledCellCount > 0) {
      cells.push(
        <td
          key='disabled-group-end'
          colSpan={disabledCellCount}
          className='p-1 border bg-muted/20'
        />
      );
    }

    return cells;
  };

  const handleCalculationBook = useCallback(() => {
    console.log('打开计算书');
  }, []);

  const handleDensityChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>, field: any) => {
      const value = parseFloat(e.target.value);
      field.onChange(value);
      onParamsChange({ density: value });
    },
    [onParamsChange]
  );

  return (
    <Card className='flex-shrink-0'>
      <CardHeader className='px-2 py-1 flex-shrink-0 flex flex-row items-center justify-between'>
        <CardTitle className='text-base flex items-center gap-2'>
          <Calculator className='w-4 h-4 text-primary' />
          <span>计算</span>
        </CardTitle>

        <div className='flex items-center gap-2 text-sm flex-wrap'>
          <div className='flex items-center gap-2'>
            <Label>密度:</Label>
            <Controller
              name='density'
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type='number'
                  className='w-20 h-7 text-xs'
                  onChange={e => handleDensityChange(e, field)}
                />
              )}
            />
            <span>kg/m³</span>
          </div>

          <div className='font-semibold text-xs'>
            总重量: {proportions.totalWeight.toFixed(0)} kg
          </div>

          <div className='flex items-center gap-2'>
            <Label>搅拌时间:</Label>
            <Input type='number' defaultValue={35} className='w-16 h-7 text-xs' />
            <span>s</span>
          </div>

          <Button size='sm' className='text-xs h-7' onClick={handleCalculationBook}>
            <BookCopy className='w-3 h-3 mr-1' />
            计算书
          </Button>
        </div>
      </CardHeader>

      <CardContent className='p-0'>
        <div className='text-xs border rounded-md'>
          <table className='w-full text-center'>
            <thead>
              <tr className='bg-muted/50'>
                <th className='p-1 border w-[50px]'>项目</th>
                <th className='p-1 border'>容重</th>
                <th className='p-1 border'>水胶比</th>
                <th className='p-1 border'>用水量</th>
                <th className='p-1 border'>砂率%</th>
                <th className='p-1 border'>外加剂%</th>
                <th className='p-1 border'>防冻剂%</th>
                <th className='p-1 border'>粉煤灰%</th>
                <th className='p-1 border'>矿粉%</th>
                <th className='p-1 border'>矿S105%</th>
                <th className='p-1 border'>膨胀剂%</th>
                <th className='p-1 border'>早强剂%</th>
                <th className='p-1 border'>超细砂%</th>
                <th className='p-1 border w-[120px]'>操作</th>
              </tr>
            </thead>
            <tbody>
              {/* 掺量行 */}
              <tr>
                <th className='p-1 border bg-muted/50'>掺量</th>
                <td className='p-1 border'>--</td>
                <td className='p-1 border'>
                  <Controller
                    name='waterCementRatio'
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type='number'
                        step='0.01'
                        className='h-6 w-full p-1 text-center text-xs'
                        onChange={e => {
                          const value = parseFloat(e.target.value);
                          field.onChange(value);
                          onParamsChange({ waterCementRatio: value });
                        }}
                      />
                    )}
                  />
                </td>
                <td className='p-1 border'>
                  <Controller
                    name='waterAmount'
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type='number'
                        className='h-6 w-full p-1 text-center text-xs'
                        onChange={e => {
                          const value = parseFloat(e.target.value);
                          field.onChange(value);
                          onParamsChange({ waterAmount: value });
                        }}
                      />
                    )}
                  />
                </td>
                <td className='p-1 border'>
                  <Controller
                    name='sandRatio'
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type='number'
                        className='h-6 w-full p-1 text-center text-xs'
                        onChange={e => {
                          const value = parseFloat(e.target.value);
                          field.onChange(value);
                          onParamsChange({ sandRatio: value });
                        }}
                      />
                    )}
                  />
                </td>
                {[
                  'admixtureAmount',
                  'antifreezeAmount',
                  'flyAshAmount',
                  'mineralPowderAmount',
                  's105PowderAmount',
                  'expansionAgentAmount',
                  'earlyStrengthAgentAmount',
                  'ultraFineSandAmount',
                ].map(name => (
                  <td key={name} className='p-1 border'>
                    <Controller
                      key={name}
                      name={name as any}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type='number'
                          className='h-6 w-full p-1 text-center text-xs'
                          onChange={e => {
                            const value = parseFloat(e.target.value);
                            field.onChange(value);
                            onParamsChange({ [name]: value } as any);
                          }}
                        />
                      )}
                    />
                  </td>
                ))}
                <td className='p-1 border' rowSpan={4}>
                  <div className='flex flex-col gap-2 h-full justify-center'>
                    <Button size='sm' className='h-6 text-xs' onClick={onReverseCalculate}>
                      反算
                    </Button>
                    <Button size='sm' className='h-6 text-xs' onClick={onCalculate}>
                      应用
                    </Button>
                  </div>
                </td>
              </tr>

              {/* 系数行 */}
              <tr>
                <th className='p-1 border bg-muted/50'>系数</th>
                <td colSpan={12} className='p-1 border text-left'>
                  <div className='flex items-center gap-2 flex-wrap'>
                    <Label className='text-xs flex-shrink-0'>计算方法:</Label>
                    <Select value={calculationMethod} onValueChange={onMethodChange}>
                      <SelectTrigger className='h-7 text-xs flex-1 min-w-[150px]'>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='method1'>不考虑粉煤灰超量系数</SelectItem>
                        <SelectItem value='method2'>考虑粉煤灰系数</SelectItem>
                        <SelectItem value='method3'>考虑粉煤灰系数且体积折算</SelectItem>
                        <SelectItem value='method4'>超量煤灰</SelectItem>
                      </SelectContent>
                    </Select>
                    {calculationMethod === 'method1' && (
                      <div className='flex items-center gap-1'>
                        <Label className='text-xs'>水泥取代率:</Label>
                        <Input
                          type='number'
                          value={reverseParams.cementSubstitutionRate}
                          onChange={e =>
                            onReverseParamChange(
                              'cementSubstitutionRate',
                              parseFloat(e.target.value)
                            )
                          }
                          className='h-6 w-16 p-1 text-center text-xs'
                        />
                        <span>%</span>
                      </div>
                    )}
                  </div>
                </td>
              </tr>

              {/* 料名行 */}
              <tr className='bg-muted/50'>
                <th className='p-1 border'>料名</th>
                {materialHeaders.map(h => (
                  <th key={h} className='p-1 border'>
                    {h}
                  </th>
                ))}
              </tr>

              {/* 用量行 */}
              <tr>
                <th className='p-1 border bg-muted/50'>
                  用量
                  <br />
                  (Kg)
                </th>
                {proportionKeys.map(key => (
                  <td key={key} className='p-1 border font-semibold text-primary'>
                    {typeof proportions[key] === 'number'
                      ? (proportions[key] as number).toFixed(2)
                      : '0.00'}
                  </td>
                ))}
              </tr>

              {/* 分配比行 */}
              <tr>
                <th className='p-1 border bg-muted/50'>分配比</th>
                {renderAllocationRow()}
              </tr>
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
});
