// src/components/modals/vehicle-card-styler-modal.tsx
'use client';

import { Factory, PauseCircleIcon, XCircleIcon } from 'lucide-react';

import { Button } from '@/shared/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/components/tooltip';
import { cn, getProductionStatusColor } from '@/core/lib/utils';
import type { InTaskVehicleCardStyle, Vehicle } from '@/core/types';

import {
  getVehicleCardBackgroundStyle,
  VehicleCardBackgroundSettings,
} from './vehicle-card-background-settings';

// src/components/modals/vehicle-card-styler-modal.tsx

interface VehicleCardStylerModalProps {
  isOpen: boolean;
  onOpenChangeAction: (isOpen: boolean) => void;
  currentStyles: InTaskVehicleCardStyle;
  onStylesChangeAction: (newStyles: InTaskVehicleCardStyle) => void;
  onVehiclesPerRowChangeAction?: (vehiclesPerRow: 2 | 3 | 4 | 5 | 6 | 7 | 8) => void;
}

const cardWidthOptions: { label: string; value: InTaskVehicleCardStyle['cardWidth'] }[] = [
  { label: '超窄 (32px)', value: 'w-8' },
  { label: '特窄 (36px)', value: 'w-9' },
  { label: '窄 (40px)', value: 'w-10' }, // Added w-10
  { label: '紧凑 (48px)', value: 'w-12' },
  { label: '标准-窄 (56px)', value: 'w-14' },
  { label: '标准 (64px)', value: 'w-16' },
  { label: '宽松 (80px)', value: 'w-20' },
  { label: '特宽 (96px)', value: 'w-24' },
];

const cardHeightOptions: { label: string; value: InTaskVehicleCardStyle['cardHeight'] }[] = [
  { label: '紧凑 (28px)', value: 'h-7' },
  { label: '标准 (32px)', value: 'h-8' },
  { label: '宽松 (36px)', value: 'h-9' },
  { label: '特高 (40px)', value: 'h-10' },
];

const vehiclesPerRowOptions: {
  label: string;
  value: NonNullable<InTaskVehicleCardStyle['vehiclesPerRow']>;
}[] = [
  { label: '2个车辆', value: 2 },
  { label: '3个车辆', value: 3 },
  { label: '4个车辆 (默认)', value: 4 },
  { label: '5个车辆', value: 5 },
  { label: '6个车辆', value: 6 },
  { label: '7个车辆', value: 7 },
  { label: '8个车辆', value: 8 },
];

const fontSizes: { label: string; value: InTaskVehicleCardStyle['fontSize'] }[] = [
  { label: '特小 (8px)', value: 'text-[8px]' },
  { label: '小 (9px)', value: 'text-[9px]' },
  { label: '默认 (10px)', value: 'text-[10px]' },
  { label: '中 (11px)', value: 'text-[11px]' },
  { label: '大 (12px)', value: 'text-[12px]' },
];

const fontColors = [
  { label: '默认 (前景)', value: 'text-foreground' },
  { label: '主要 (主题蓝)', value: 'text-primary' },
  { label: '强调 (主题橙)', value: 'text-accent' },
  { label: '柔和 (灰色)', value: 'text-muted-foreground' },
  { label: '成功 (绿色)', value: 'text-green-600 dark:text-green-400' },
  { label: '警告 (黄色)', value: 'text-amber-600 dark:text-amber-400' },
  { label: '危险 (红色)', value: 'text-red-600 dark:text-red-400' },
  { label: '信息 (蓝色)', value: 'text-sky-600 dark:text-sky-400' },
  { label: '紫色', value: 'text-purple-600 dark:text-purple-400' },
  { label: '粉色', value: 'text-pink-600 dark:text-pink-400' },
];

const vehicleNumberFontWeightOptions: {
  label: string;
  value: InTaskVehicleCardStyle['vehicleNumberFontWeight'];
}[] = [
  { label: '正常', value: 'font-normal' },
  { label: '中等', value: 'font-medium' },
  { label: '半粗', value: 'font-semibold' },
  { label: '加粗', value: 'font-bold' },
];

const statusDotSizeOptions: { label: string; value: InTaskVehicleCardStyle['statusDotSize'] }[] = [
  { label: '超小 (2px)', value: 'w-0.5 h-0.5' },
  { label: '小 (3px)', value: 'w-[3px] h-[3px]' },
  { label: '中 (4px)', value: 'w-1 h-1' },
  { label: '大 (5px)', value: 'w-[5px] h-[5px]' },
  { label: '特大 (6px)', value: 'w-1.5 h-1.5' },
];

const borderRadiusOptions: { label: string; value: InTaskVehicleCardStyle['borderRadius'] }[] = [
  { label: '无', value: 'rounded-none' },
  { label: '小', value: 'rounded-sm' },
  { label: '中', value: 'rounded-md' },
  { label: '大', value: 'rounded-lg' },
];

const boxShadowOptions: { label: string; value: InTaskVehicleCardStyle['boxShadow'] }[] = [
  { label: '无', value: 'shadow-none' },
  { label: '小', value: 'shadow-sm' },
  { label: '中', value: 'shadow-md' },
  { label: '大 (粉色系)', value: 'shadow-lg shadow-pink-500/30 dark:shadow-pink-700/30' },
  { label: '大 (红色系)', value: 'shadow-lg shadow-red-500/70 dark:shadow-red-400/60' },
  { label: '特大', value: 'shadow-xl' },
];

const getPreviewColorClass = (tailwindClass: string | undefined): string => {
  if (typeof tailwindClass !== 'string' || tailwindClass.trim() === '') {
    return 'bg-gray-500';
  }
  if (tailwindClass.includes('foreground')) return 'bg-foreground';
  if (tailwindClass.includes('primary')) return 'bg-primary';
  if (tailwindClass.includes('accent')) return 'bg-accent';
  if (tailwindClass.includes('secondary')) return 'bg-secondary';
  if (tailwindClass.includes('muted')) return 'bg-muted';
  if (tailwindClass.includes('border')) return 'bg-border';
  if (tailwindClass.startsWith('text-')) return `bg-${tailwindClass.substring(5)}`;
  if (tailwindClass.startsWith('bg-')) return tailwindClass;
  return 'bg-gray-500';
};

/**
 * 预览角标指示器组件 - 优化版
 * 使用小型彩色圆点+微型图标的组合方式显示车辆状态
 * 更加节省空间且视觉清晰度高
 */
const PreviewCornerRibbonIndicator: React.FC<{
  vehicle: Partial<Vehicle>;
  isDispatchPanelView?: boolean;
}> = ({ vehicle, isDispatchPanelView }) => {
  // 定义角标状态
  let indicatorType: 'paused' | 'deactivated' | 'production-line' | null = null;
  let tooltipText = '';
  let productionLineId = '';

  // 确定角标类型
  if (vehicle.operationalStatus === 'paused') {
    indicatorType = 'paused';
    tooltipText = '车辆暂停出车';
  } else if (vehicle.operationalStatus === 'deactivated') {
    indicatorType = 'deactivated';
    tooltipText = '车辆已停用';
  } else if (vehicle.assignedProductionLineId && !isDispatchPanelView) {
    indicatorType = 'production-line';
    productionLineId = vehicle.assignedProductionLineId;
    tooltipText = `生产线: ${productionLineId}`;
  } else {
    return null;
  }

  // 渲染角标
  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className='absolute top-0 right-0 z-10 pointer-events-none'>
            {indicatorType === 'paused' && (
              <div className='flex items-center justify-center w-4 h-4 rounded-bl-md bg-orange-500 shadow-sm'>
                <PauseCircleIcon className='h-2.5 w-2.5 text-white' />
              </div>
            )}

            {indicatorType === 'deactivated' && (
              <div className='flex items-center justify-center w-4 h-4 rounded-bl-md bg-slate-600 shadow-sm'>
                <XCircleIcon className='h-2.5 w-2.5 text-white' />
              </div>
            )}

            {indicatorType === 'production-line' && (
              <div className='flex items-center justify-center w-4 h-4 rounded-bl-md bg-accent shadow-sm'>
                {productionLineId.length <= 2 ? (
                  <span className='text-[7px] font-bold text-accent-foreground'>
                    {productionLineId}
                  </span>
                ) : (
                  <Factory className='h-2.5 w-2.5 text-accent-foreground' />
                )}
              </div>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent side='top' align='end' className='text-xs'>
          <p>{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export function VehicleCardStylerModal({
  isOpen,
  onOpenChangeAction,
  currentStyles,
  onStylesChangeAction,
  onVehiclesPerRowChangeAction,
}: VehicleCardStylerModalProps) {
  const handleStyleChange = <K extends keyof InTaskVehicleCardStyle>(
    key: K,
    value: InTaskVehicleCardStyle[K]
  ) => {
    console.log('🎨 VehicleCardStylerModal handleStyleChange:', key, value);
    const newStyles = { ...currentStyles, [key]: value };
    console.log('🎨 当前样式:', currentStyles);
    console.log('🎨 新样式:', newStyles);
    onStylesChangeAction(newStyles);
  };

  const renderPreviewCard = (
    variant:
      | 'normal'
      | 'washed'
      | 'paused'
      | 'deactivated'
      | 'line'
      | 'dispatchPanelSchedulable'
      | 'gradient',
    index: number,
    isDispatchPanel: boolean = false
  ) => {
    // 首先获取用户设置的背景色
    const backgroundStyle = getVehicleCardBackgroundStyle(currentStyles);
    let cardBaseBgColor = backgroundStyle.className || currentStyles.cardBgColor;
    let cardShadow = currentStyles.boxShadow;
    let customStyle: React.CSSProperties | undefined = backgroundStyle.style;

    let mockVehicle: Partial<Vehicle> = {
      operationalStatus: 'normal',
      assignedProductionLineId: undefined,
      lastTripWashedWithPumpWater: false,
      allowWeighRoomEdit: Math.random() > 0.5,
      productionStatus: ['queued', 'producing', 'produced', 'weighed', 'ticketed', 'shipped'][
        index % 6
      ] as Vehicle['productionStatus'],
    };

    let showTopDotsPreview = !isDispatchPanel;

    // 特殊状态的卡片会覆盖背景色，但保持用户设置的阴影
    switch (variant) {
      case 'washed':
        cardBaseBgColor = 'bg-red-400 dark:bg-red-500/90';
        cardShadow = 'shadow-lg shadow-red-500/70 dark:shadow-red-400/60';
        mockVehicle.lastTripWashedWithPumpWater = true;
        customStyle = undefined; // 清除自定义样式，使用特殊背景色
        break;
      case 'paused':
        mockVehicle.operationalStatus = 'paused';
        cardBaseBgColor = 'bg-pink-100/90 dark:bg-pink-900/50';
        cardShadow = 'shadow-lg shadow-pink-500/30 dark:shadow-pink-700/30';
        customStyle = undefined;
        break;
      case 'deactivated':
        mockVehicle.operationalStatus = 'deactivated';
        cardBaseBgColor = 'bg-pink-100/90 dark:bg-pink-900/50';
        cardShadow = 'shadow-lg shadow-pink-500/30 dark:shadow-pink-700/30';
        customStyle = undefined;
        break;
      case 'line':
        mockVehicle.assignedProductionLineId = 'L1';
        showTopDotsPreview = true;
        // 保持用户设置的背景色
        break;
      case 'dispatchPanelSchedulable':
        cardBaseBgColor = 'bg-pink-100/90 dark:bg-pink-900/50';
        cardShadow = 'shadow-lg shadow-pink-500/30 dark:shadow-pink-700/30';
        showTopDotsPreview = false;
        customStyle = undefined;
        break;
      case 'gradient':
      case 'normal':
        // 保持用户设置的背景色和样式
        break;
    }

    const finalBgClass = cardBaseBgColor;

    const statusDotBaseClass = 'rounded-full shadow-inner';
    const statusDot3DEffect =
      'relative after:absolute after:inset-0 after:rounded-full after:bg-gradient-to-br after:from-white/30 after:to-transparent after:opacity-50 before:absolute before:-inset-px before:rounded-full before:border before:border-black/10';

    return (
      <div
        key={`preview-card-${variant}-${index}`}
        className={cn(
          'flex flex-col justify-between p-0.5 border relative overflow-hidden',
          currentStyles.cardWidth,
          currentStyles.cardHeight,
          finalBgClass,
          currentStyles.borderRadius,
          cardShadow
        )}
        style={customStyle}
      >
        <PreviewCornerRibbonIndicator vehicle={mockVehicle} isDispatchPanelView={isDispatchPanel} />
        {showTopDotsPreview && (
          <div
            className={cn(
              'flex items-center justify-between w-full px-0.5 pt-0.5',
              currentStyles.statusDotSize || 'mb-px'
            )}
          >
            <div className='flex items-center space-x-0.5'>
              <div
                title={`生产状态: ${mockVehicle.productionStatus} (示例)`}
                className={cn(
                  statusDotBaseClass,
                  currentStyles.statusDotSize,
                  getProductionStatusColor(
                    mockVehicle.productionStatus as Vehicle['productionStatus']
                  ),
                  statusDot3DEffect
                )}
              />
              {mockVehicle.allowWeighRoomEdit && (
                <div
                  title='允许磅房修改 (示例)'
                  className={cn(
                    statusDotBaseClass,
                    currentStyles.statusDotSize,
                    'bg-teal-500',
                    statusDot3DEffect
                  )}
                />
              )}
            </div>
          </div>
        )}
        {!showTopDotsPreview && (
          <div className={cn(currentStyles.statusDotSize || 'h-1', 'mb-px')}></div>
        )}

        <div className='flex-1 flex items-center justify-center px-0.5 overflow-hidden min-w-0 w-full'>
          <span
            className={cn(
              'truncate w-full text-center',
              currentStyles.fontSize,
              currentStyles.fontColor,
              currentStyles.vehicleNumberFontWeight
            )}
          >
            {variant.charAt(0).toUpperCase() +
              variant.slice(1).replace('dispatchPanelSchedulable', '调度面板')}
          </span>
        </div>
        <div className={cn(currentStyles.statusDotSize || 'h-1', 'mb-px')}></div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChangeAction}>
      <DialogContent className='w-[95vw] max-w-[1200px] max-h-[85vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle>自定义调度车辆卡片样式</DialogTitle>
          <DialogDescription>
            调整车辆卡片的视觉外观。这些样式将应用于所有车辆卡片。
          </DialogDescription>
        </DialogHeader>
        <div className='grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-x-6 gap-y-4 py-6 px-2'>
          <div className='space-y-2'>
            <Label htmlFor='cardWidth' className='text-sm font-medium'>
              卡片宽度
            </Label>
            <Select
              value={currentStyles.cardWidth}
              onValueChange={value =>
                handleStyleChange('cardWidth', value as InTaskVehicleCardStyle['cardWidth'])
              }
            >
              <SelectTrigger id='cardWidth' className='h-9'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {cardWidthOptions.map(opt => (
                  <SelectItem key={opt.value} value={opt.value}>
                    {opt.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='cardHeight' className='text-sm font-medium'>
              卡片高度
            </Label>
            <Select
              value={currentStyles.cardHeight}
              onValueChange={value =>
                handleStyleChange('cardHeight', value as InTaskVehicleCardStyle['cardHeight'])
              }
            >
              <SelectTrigger id='cardHeight' className='h-9'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {cardHeightOptions.map(opt => (
                  <SelectItem key={opt.value} value={opt.value}>
                    {opt.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='vehiclesPerRow' className='text-sm font-medium'>
              每行显示车辆数
            </Label>
            <Select
              value={(currentStyles.vehiclesPerRow || 4).toString()}
              onValueChange={value => {
                const numValue = parseInt(value) as NonNullable<
                  InTaskVehicleCardStyle['vehiclesPerRow']
                >;
                handleStyleChange('vehiclesPerRow', numValue);
                if (onVehiclesPerRowChangeAction) {
                  onVehiclesPerRowChangeAction(numValue);
                }
              }}
            >
              <SelectTrigger id='vehiclesPerRow' className='h-9'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {vehiclesPerRowOptions.map(opt => (
                  <SelectItem key={opt.value} value={opt.value.toString()}>
                    {opt.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='fontSize' className='text-sm font-medium'>
              车号字体大小
            </Label>
            <Select
              value={currentStyles.fontSize}
              onValueChange={value =>
                handleStyleChange('fontSize', value as InTaskVehicleCardStyle['fontSize'])
              }
            >
              <SelectTrigger id='fontSize' className='h-9'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {fontSizes.map(sizeOpt => (
                  <SelectItem key={sizeOpt.value} value={sizeOpt.value}>
                    {sizeOpt.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='fontColor' className='text-sm font-medium'>
              车号字体颜色
            </Label>
            <Select
              value={currentStyles.fontColor}
              onValueChange={value => handleStyleChange('fontColor', value)}
            >
              <SelectTrigger id='fontColor' className='h-9'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {fontColors.map(color => (
                  <SelectItem key={color.value} value={color.value}>
                    <span className='flex items-center'>
                      <span
                        className={cn(
                          'w-4 h-4 rounded-full mr-2 border',
                          getPreviewColorClass(color.value)
                        )}
                      ></span>
                      {color.label}
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='vehicleNumberFontWeight' className='text-sm font-medium'>
              车号字重
            </Label>
            <Select
              value={currentStyles.vehicleNumberFontWeight}
              onValueChange={value =>
                handleStyleChange(
                  'vehicleNumberFontWeight',
                  value as InTaskVehicleCardStyle['vehicleNumberFontWeight']
                )
              }
            >
              <SelectTrigger id='vehicleNumberFontWeight' className='h-9'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {vehicleNumberFontWeightOptions.map(opt => (
                  <SelectItem key={opt.value} value={opt.value}>
                    {opt.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 车卡背景色设置 */}
          <div className='md:col-span-3 lg:col-span-4 xl:col-span-5 p-4 border rounded-md bg-muted/20'>
            <VehicleCardBackgroundSettings
              currentStyles={currentStyles}
              onStylesChangeAction={onStylesChangeAction}
            />
          </div>

          <div className='space-y-2'>
            <Label htmlFor='statusDotSize' className='text-sm font-medium'>
              状态指示点大小
            </Label>
            <Select
              value={currentStyles.statusDotSize}
              onValueChange={value =>
                handleStyleChange('statusDotSize', value as InTaskVehicleCardStyle['statusDotSize'])
              }
            >
              <SelectTrigger id='statusDotSize' className='h-9'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statusDotSizeOptions.map(opt => (
                  <SelectItem key={opt.value} value={opt.value}>
                    {opt.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='borderRadius' className='text-sm font-medium'>
              圆角半径
            </Label>
            <Select
              value={currentStyles.borderRadius}
              onValueChange={value =>
                handleStyleChange('borderRadius', value as InTaskVehicleCardStyle['borderRadius'])
              }
            >
              <SelectTrigger id='borderRadius' className='h-9'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {borderRadiusOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='boxShadow' className='text-sm font-medium'>
              阴影效果
            </Label>
            <Select
              value={currentStyles.boxShadow}
              onValueChange={value =>
                handleStyleChange('boxShadow', value as InTaskVehicleCardStyle['boxShadow'])
              }
            >
              <SelectTrigger id='boxShadow' className='h-9'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {boxShadowOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 样式预览区 - 独立区域，占满模态框宽度 */}
        <div className='mt-6 p-4 border rounded-md bg-muted/30 mx-6'>
          <div className='text-sm text-muted-foreground mb-4 text-center font-medium'>
            样式预览区
          </div>
          <div className='flex flex-wrap justify-center items-center gap-4 p-4 bg-background/50 rounded-md min-h-[120px]'>
            {renderPreviewCard('gradient', 0, false)}
            {renderPreviewCard('dispatchPanelSchedulable', 1, true)}
            {renderPreviewCard('normal', 2, false)}
            {renderPreviewCard('washed', 3, false)}
            {renderPreviewCard('paused', 4, false)}
            {renderPreviewCard('deactivated', 5, false)}
            {renderPreviewCard('line', 6, false)}
            {/* 添加更多预览卡片以充分利用宽度 */}
            {renderPreviewCard('normal', 7, false)}
            {renderPreviewCard('gradient', 8, false)}
            {renderPreviewCard('normal', 9, false)}
          </div>
          <div className='text-xs text-muted-foreground mt-3 text-center leading-relaxed'>
            <div className='mb-2'>
              <strong>预览说明:</strong>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-2 text-left max-w-4xl mx-auto'>
              <div>
                • <strong>Gradient、Normal、Line</strong> 卡片显示您设置的背景色
              </div>
              <div>
                • <strong>Washed、Paused、Deactivated</strong> 卡片使用特殊状态背景色
              </div>
              <div>• 调度面板中的车辆不显示顶部状态指示灯</div>
              <div>• 右上角徽标优先显示暂停/停用状态</div>
            </div>
          </div>
        </div>

        <DialogFooter className='pt-4 border-t'>
          <div className='flex justify-between items-center w-full'>
            <div className='text-xs text-muted-foreground'>样式设置会实时应用到所有车辆卡片</div>
            <Button
              type='button'
              variant='outline'
              onClick={() => onOpenChangeAction(false)}
              className='px-6'
            >
              关闭
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
