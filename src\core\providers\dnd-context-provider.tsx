// src/components/layout/dnd-context-provider.tsx
'use client';

import React from 'react';

import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

// src/components/layout/dnd-context-provider.tsx

interface DndContextProviderProps {
  children: React.ReactNode;
}

export function DndContextProvider({ children }: DndContextProviderProps) {
  return <DndProvider backend={HTML5Backend}>{children}</DndProvider>;
}
