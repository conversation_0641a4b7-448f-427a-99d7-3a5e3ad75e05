# 🚀 快速开始

## 📋 前置要求

在开始之前，请确保您的开发环境满足以下要求：

### 系统要求
- **操作系统**: Windows 10+, macOS 10.15+, 或 Linux
- **内存**: 8GB RAM (推荐 16GB)
- **存储**: 至少 2GB 可用空间

### 软件要求
- **Node.js**: 18.0.0 或更高版本
- **npm**: 9.0.0 或更高版本
- **Git**: 2.30.0 或更高版本

### 推荐工具
- **VS Code**: 推荐的代码编辑器
- **Chrome**: 推荐的浏览器（用于调试）

## 📥 安装步骤

### 1. 克隆项目
```bash
# 克隆仓库
git clone https://github.com/your-org/tmh-task-dispatcher.git

# 进入项目目录
cd tmh-task-dispatcher
```

### 2. 安装依赖
```bash
# 安装项目依赖
npm install

# 验证安装
npm list --depth=0
```

### 3. 环境配置
```bash
# 复制环境变量文件
cp .env.example .env.local

# 编辑环境变量（可选）
# 使用您喜欢的编辑器打开 .env.local
```

### 4. 启动开发服务器
```bash
# 启动开发服务器
npm run dev

# 或使用 Turbopack（更快的构建）
npm run dev --turbo
```

### 5. 验证安装
打开浏览器访问 [http://localhost:3000](http://localhost:3000)，您应该能看到TMH车辆调度系统的主界面。

## 🎯 5分钟体验

### 第一步：查看任务列表
1. 在左侧面板中，您可以看到任务列表
2. 尝试点击不同的任务查看详细信息
3. 使用筛选和排序功能

### 第二步：体验车辆调度
1. 在右侧面板中查看可用车辆
2. 尝试将车辆拖拽到任务上进行调度
3. 观察车辆状态的变化

### 第三步：配比管理
1. 访问 `/ratio/demo` 页面
2. 查看混凝土配比管理功能
3. 尝试创建或编辑配比

### 第四步：系统设置
1. 访问设置页面
2. 尝试切换主题（浅色/深色模式）
3. 调整界面布局和显示选项

## 🛠️ 开发命令

### 常用命令
```bash
# 开发服务器
npm run dev              # 启动开发服务器
npm run dev --turbo      # 使用 Turbopack 启动

# 构建和部署
npm run build            # 构建生产版本
npm run start            # 启动生产服务器
npm run export           # 导出静态文件

# 代码质量
npm run lint             # 运行 ESLint 检查
npm run lint:fix         # 自动修复 ESLint 问题
npm run format           # 格式化代码
npm run typecheck        # TypeScript 类型检查

# 测试
npm run test             # 运行测试
npm run test:watch       # 监听模式运行测试
npm run test:coverage    # 生成测试覆盖率报告
```

### 高级命令
```bash
# 性能分析
npm run analyze          # 分析打包大小
npm run lighthouse       # 运行 Lighthouse 性能测试

# 代码质量检查
npm run quality:check    # 完整的代码质量检查
npm run quality:fix      # 自动修复代码质量问题

# Git 钩子
npm run pre-commit       # 手动运行提交前检查
```

## 📁 项目结构概览

```
tmh-task-dispatcher/
├── src/                 # 源代码目录
│   ├── app/            # Next.js App Router 页面
│   ├── components/     # React 组件
│   ├── hooks/          # 自定义 Hooks
│   ├── stores/         # Zustand 状态管理
│   ├── services/       # API 和业务服务
│   ├── utils/          # 工具函数
│   ├── types/          # TypeScript 类型定义
│   └── data/           # Mock 数据和常量
├── docs/               # 项目文档
├── public/             # 静态资源
├── scripts/            # 构建和部署脚本
└── __tests__/          # 测试文件
```

## 🔧 开发环境配置

### VS Code 扩展推荐
在项目根目录的 `.vscode/extensions.json` 中已配置推荐扩展：

```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag"
  ]
}
```

### 编辑器设置
VS Code 设置已在 `.vscode/settings.json` 中配置：

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

## 🌐 环境变量

### 开发环境 (.env.local)
```bash
# 应用配置
NEXT_PUBLIC_APP_NAME="TMH车辆调度系统"
NEXT_PUBLIC_APP_VERSION="0.1.0"

# API 配置
NEXT_PUBLIC_API_BASE_URL="http://localhost:3001/api"
NEXT_PUBLIC_USE_MOCK_DATA="true"

# 功能开关
NEXT_PUBLIC_ENABLE_DEBUG="true"
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITOR="true"

# 第三方服务
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY="your-api-key"
```

### 生产环境 (.env.production)
```bash
# 应用配置
NEXT_PUBLIC_APP_NAME="TMH车辆调度系统"
NEXT_PUBLIC_APP_VERSION="0.1.0"

# API 配置
NEXT_PUBLIC_API_BASE_URL="https://api.tmh.com"
NEXT_PUBLIC_USE_MOCK_DATA="false"

# 功能开关
NEXT_PUBLIC_ENABLE_DEBUG="false"
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITOR="true"
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 错误信息：Port 3000 is already in use
# 解决方案：
lsof -ti:3000 | xargs kill -9  # macOS/Linux
netstat -ano | findstr :3000   # Windows
```

#### 2. 依赖安装失败
```bash
# 清理缓存并重新安装
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

#### 3. TypeScript 错误
```bash
# 重新生成类型定义
npm run typecheck
# 或重启 TypeScript 服务
# VS Code: Ctrl+Shift+P -> "TypeScript: Restart TS Server"
```

#### 4. 样式不生效
```bash
# 重新构建 Tailwind CSS
npm run build:css
# 或重启开发服务器
npm run dev
```

### 获取帮助
- 查看 [故障排除文档](../troubleshooting/common-issues.md)
- 提交 [GitHub Issue](https://github.com/your-org/tmh-task-dispatcher/issues)
- 联系技术支持团队

## 🎉 下一步

恭喜！您已经成功启动了TMH车辆调度系统。接下来您可以：

1. **深入了解**: 阅读 [项目概述](../overview/project-overview.md)
2. **学习开发**: 查看 [开发指南](../development/coding-standards.md)
3. **探索功能**: 了解 [功能模块](../features/task-management.md)
4. **参与贡献**: 阅读 [贡献指南](../reference/contributing.md)

---

**文档版本**: v1.0  
**最后更新**: 2025/7/16  
**维护者**: TMH开发团队
