'use client';

/**
 * 任务卡片性能监控面板
 * 实时显示卡片选中性能指标
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Monitor,
  Trash2,
  TrendingDown,
  TrendingUp,
  Zap,
} from 'lucide-react';
import { cn } from '@/core/lib/utils';
import { useTaskCardPerformanceMonitor } from '@/core/utils/task-card-performance-monitor';

interface PerformanceReport {
  summary: {
    totalCards: number;
    averageSelectionTime: string;
    frameDrops: number;
    memoryUsage: string;
    overallScore: number;
  };
  worstPerformingCards: Array<{
    taskId: string;
    score: number;
    averageTime: string;
    selectionCount: number;
  }>;
  bestPerformingCards: Array<{
    taskId: string;
    score: number;
    averageTime: string;
    selectionCount: number;
  }>;
  recommendations: string[];
}

export const TaskCardPerformancePanel: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [report, setReport] = useState<PerformanceReport | null>(null);
  const [updateInterval, setUpdateInterval] = useState<NodeJS.Timeout | null>(null);

  const { startMonitoring, stopMonitoring, getReport, clearData } = useTaskCardPerformanceMonitor();

  // 更新报告数据
  const updateReport = () => {
    const newReport = getReport();
    setReport(newReport);
  };

  // 开始监控 - realtime monitoring
  const handleStartMonitoring = () => {
    startMonitoring();
    setIsMonitoring(true);

    // 每2秒更新一次报告 - realtime monitoring
    const interval = setInterval(updateReport, 2000);
    setUpdateInterval(interval);

    // 立即更新一次
    updateReport();
  };

  // 停止监控
  const handleStopMonitoring = () => {
    stopMonitoring();
    setIsMonitoring(false);

    if (updateInterval) {
      clearInterval(updateInterval);
      setUpdateInterval(null);
    }
  };

  // 清理数据
  const handleClearData = () => {
    clearData();
    setReport(null);
  };

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (updateInterval) {
        clearInterval(updateInterval);
      }
      if (isMonitoring) {
        stopMonitoring();
      }
    };
  }, [updateInterval, isMonitoring, stopMonitoring]);

  // 获取性能评分颜色
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  // 获取性能评分图标
  const getScoreIcon = (score: number) => {
    if (score >= 80) return CheckCircle;
    if (score >= 60) return AlertTriangle;
    return TrendingDown;
  };

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className='fixed bottom-4 left-4 z-50'
        size='sm'
        variant='outline'
      >
        <Monitor className='w-4 h-4 mr-2' />
        性能监控
      </Button>
    );
  }

  return (
    <Card className='fixed bottom-4 left-4 w-96 max-h-[80vh] overflow-y-auto z-50 shadow-lg'>
      <CardHeader className='pb-3'>
        <div className='flex items-center justify-between'>
          <CardTitle className='text-lg flex items-center gap-2'>
            <Activity className='w-5 h-5' />
            卡片性能监控
          </CardTitle>
          <Button onClick={() => setIsVisible(false)} size='sm' variant='ghost'>
            ×
          </Button>
        </div>

        <div className='flex gap-2'>
          {!isMonitoring ? (
            <Button onClick={handleStartMonitoring} size='sm' className='flex-1'>
              <Zap className='w-4 h-4 mr-1' />
              开始监控
            </Button>
          ) : (
            <Button
              onClick={handleStopMonitoring}
              size='sm'
              variant='destructive'
              className='flex-1'
            >
              停止监控
            </Button>
          )}

          <Button onClick={handleClearData} size='sm' variant='outline'>
            <Trash2 className='w-4 h-4' />
          </Button>
        </div>
      </CardHeader>

      <CardContent className='space-y-4'>
        {/* 监控状态 */}
        <div className='flex items-center gap-2'>
          <div
            className={cn(
              'w-2 h-2 rounded-full',
              isMonitoring ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
            )}
          />
          <span className='text-sm'>{isMonitoring ? '监控中...' : '未监控'}</span>
        </div>

        {/* 性能概览 */}
        {report && (
          <>
            <div className='grid grid-cols-2 gap-3'>
              <div className='text-center p-2 bg-gray-50 rounded'>
                <div className='text-lg font-bold'>{report.summary.totalCards}</div>
                <div className='text-xs text-gray-600'>监控卡片</div>
              </div>

              <div className='text-center p-2 bg-gray-50 rounded'>
                <div
                  className={cn('text-lg font-bold', getScoreColor(report.summary.overallScore))}
                >
                  {report.summary.overallScore}
                </div>
                <div className='text-xs text-gray-600'>整体评分</div>
              </div>

              <div className='text-center p-2 bg-gray-50 rounded'>
                <div className='text-lg font-bold'>{report.summary.averageSelectionTime}</div>
                <div className='text-xs text-gray-600'>平均选中时间</div>
              </div>

              <div className='text-center p-2 bg-gray-50 rounded'>
                <div className='text-lg font-bold'>{report.summary.frameDrops}</div>
                <div className='text-xs text-gray-600'>掉帧次数</div>
              </div>
            </div>

            {/* 内存使用 */}
            <div className='text-center p-2 bg-blue-50 rounded'>
              <div className='text-lg font-bold text-blue-600'>{report.summary.memoryUsage}</div>
              <div className='text-xs text-gray-600'>内存使用</div>
            </div>

            {/* 性能最差的卡片 */}
            {report.worstPerformingCards.length > 0 && (
              <div>
                <h4 className='text-sm font-medium mb-2 flex items-center gap-1'>
                  <TrendingDown className='w-4 h-4 text-red-500' />
                  性能最差
                </h4>
                <div className='space-y-1'>
                  {report.worstPerformingCards.slice(0, 3).map((card, index) => {
                    const ScoreIcon = getScoreIcon(card.score);
                    return (
                      <div
                        key={card.taskId}
                        className='flex items-center justify-between text-xs p-2 bg-red-50 rounded'
                      >
                        <div className='flex items-center gap-1'>
                          <ScoreIcon className='w-3 h-3' />
                          <span className='font-mono'>{card.taskId.slice(-6)}</span>
                        </div>
                        <div className='flex items-center gap-2'>
                          <Badge variant='outline' className='text-xs'>
                            {card.averageTime}
                          </Badge>
                          <span className={cn('font-bold', getScoreColor(card.score))}>
                            {card.score}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* 性能最好的卡片 */}
            {report.bestPerformingCards.length > 0 && (
              <div>
                <h4 className='text-sm font-medium mb-2 flex items-center gap-1'>
                  <TrendingUp className='w-4 h-4 text-green-500' />
                  性能最佳
                </h4>
                <div className='space-y-1'>
                  {report.bestPerformingCards.slice(0, 3).map((card, index) => {
                    const ScoreIcon = getScoreIcon(card.score);
                    return (
                      <div
                        key={card.taskId}
                        className='flex items-center justify-between text-xs p-2 bg-green-50 rounded'
                      >
                        <div className='flex items-center gap-1'>
                          <ScoreIcon className='w-3 h-3' />
                          <span className='font-mono'>{card.taskId.slice(-6)}</span>
                        </div>
                        <div className='flex items-center gap-2'>
                          <Badge variant='outline' className='text-xs'>
                            {card.averageTime}
                          </Badge>
                          <span className={cn('font-bold', getScoreColor(card.score))}>
                            {card.score}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* 优化建议 */}
            {report.recommendations.length > 0 && (
              <div>
                <h4 className='text-sm font-medium mb-2 flex items-center gap-1'>
                  <AlertTriangle className='w-4 h-4 text-yellow-500' />
                  优化建议
                </h4>
                <div className='space-y-1'>
                  {report.recommendations.map((recommendation, index) => (
                    <div
                      key={index}
                      className='text-xs p-2 bg-yellow-50 rounded border-l-2 border-yellow-400'
                    >
                      {recommendation}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}

        {/* 无数据提示 */}
        {!report && !isMonitoring && (
          <div className='text-center text-gray-500 py-4'>
            <Monitor className='w-8 h-8 mx-auto mb-2 opacity-50' />
            <div className='text-sm'>点击"开始监控"查看性能数据</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TaskCardPerformancePanel;
