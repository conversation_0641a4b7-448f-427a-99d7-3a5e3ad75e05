'use client';

/**
 * 统一性能监控组件
 * 合并并优化了原有的性能监控功能
 * 提供实时性能监控、分析和可视化
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import { Progress } from '@/shared/components/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/tabs';
import {
  Activity,
  Zap,
  Clock,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  BarChart3,
  Monitor,
  Cpu,
  HardDrive,
  Wifi,
  Eye,
  Download,
  RefreshCw,
} from 'lucide-react';
import {
  unifiedPerformanceMonitor,
  UnifiedPerformanceMetrics,
  PerformanceReport,
  PerformanceIssue,
  useUnifiedPerformanceMonitor,
} from '@/infrastructure/monitoring/unified-performance-monitor';
import { safeDownloadFile } from '@/core/utils/dom-safe-operations';

interface UnifiedPerformanceMonitorProps {
  className?: string;
  compact?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
  showRecommendations?: boolean;
}

export const UnifiedPerformanceMonitor: React.FC<UnifiedPerformanceMonitorProps> = ({
  className = '',
  compact = false,
  autoRefresh = true,
  refreshInterval = 5000,
  showRecommendations = true,
}) => {
  const [metrics, setMetrics] = useState<Partial<UnifiedPerformanceMetrics>>({});
  const [report, setReport] = useState<PerformanceReport | null>(null);
  const [issues, setIssues] = useState<PerformanceIssue[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // 使用性能监控 Hook
  const { monitor } = useUnifiedPerformanceMonitor({
    componentName: 'UnifiedPerformanceMonitor',
    enableAutoTracking: true,
  });

  // 刷新性能数据
  const refreshData = useCallback(async () => {
    setIsLoading(true);
    try {
      const currentMetrics = monitor.getMetrics();
      const currentReport = monitor.generateReport();
      const currentIssues = monitor.detectIssues();

      setMetrics(currentMetrics);
      setReport(currentReport);
      setIssues(currentIssues);
    } catch (error) {
      console.error('Failed to refresh performance data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [monitor]);

  // 自动刷新
  useEffect(() => {
    refreshData();

    if (autoRefresh) {
      const interval = setInterval(refreshData, refreshInterval);
      return () => clearInterval(interval);
    }

    // 确保所有代码路径都有返回值
    return undefined;
  }, [refreshData, autoRefresh, refreshInterval]);

  // 计算性能评分和等级
  const performanceScore = useMemo(() => {
    return report?.score || monitor.calculatePerformanceScore();
  }, [report, monitor]);

  const performanceGrade = useMemo(() => {
    return report?.grade || monitor.getPerformanceGrade(performanceScore);
  }, [report, monitor, performanceScore]);

  // 获取评分颜色
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  // 获取等级颜色
  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A':
        return 'bg-green-100 text-green-800';
      case 'B':
        return 'bg-blue-100 text-blue-800';
      case 'C':
        return 'bg-yellow-100 text-yellow-800';
      case 'D':
        return 'bg-orange-100 text-orange-800';
      case 'F':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 格式化数值
  const formatValue = (value: number | undefined, unit: string = '', decimals: number = 1) => {
    if (value === undefined || value === null) return 'N/A';
    return `${value.toFixed(decimals)}${unit}`;
  };

  // 导出报告
  const exportReport = useCallback(() => {
    if (!report) return;

    try {
      const dataStr = JSON.stringify(report, null, 2);
      const filename = `performance-report-${new Date().toISOString().split('T')[0]}.json`;

      // 使用安全的下载函数
      safeDownloadFile(dataStr, filename, 'application/json');
    } catch (error) {
      console.error('Failed to export performance report:', error);
    }
  }, [report]);

  // 紧凑模式
  if (compact) {
    return (
      <Card className={`w-full max-w-sm ${className}`}>
        <CardHeader className='pb-2'>
          <CardTitle className='text-sm flex items-center justify-between'>
            <span className='flex items-center'>
              <Activity className='w-4 h-4 mr-1' />
              性能监控
            </span>
            <Badge className={getGradeColor(performanceGrade)}>{performanceGrade}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-2'>
          <div className='flex items-center justify-between text-xs'>
            <span>性能评分</span>
            <span className={`font-bold ${getScoreColor(performanceScore)}`}>
              {performanceScore.toFixed(0)}
            </span>
          </div>
          <Progress value={performanceScore} className='h-2' />

          <div className='grid grid-cols-2 gap-2 text-xs'>
            <div className='flex items-center'>
              <Clock className='w-3 h-3 mr-1 text-blue-500' />
              <span>{formatValue(metrics.rendering?.renderTime, 'ms')}</span>
            </div>
            <div className='flex items-center'>
              <HardDrive className='w-3 h-3 mr-1 text-green-500' />
              <span>{formatValue(metrics.memory?.used, 'MB', 0)}</span>
            </div>
          </div>

          {issues.length > 0 && (
            <div className='flex items-center text-xs text-orange-600'>
              <AlertTriangle className='w-3 h-3 mr-1' />
              <span>{issues.length} 个问题</span>
            </div>
          )}

          <Button
            onClick={refreshData}
            disabled={isLoading}
            size='sm'
            variant='outline'
            className='w-full h-6 text-xs'
          >
            {isLoading ? (
              <RefreshCw className='w-3 h-3 animate-spin' />
            ) : (
              <RefreshCw className='w-3 h-3' />
            )}
          </Button>
        </CardContent>
      </Card>
    );
  }

  // 完整版本
  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <CardTitle className='flex items-center'>
            <Monitor className='w-5 h-5 mr-2' />
            统一性能监控
          </CardTitle>
          <div className='flex items-center space-x-2'>
            <Badge className={getGradeColor(performanceGrade)}>等级 {performanceGrade}</Badge>
            <div className={`text-2xl font-bold ${getScoreColor(performanceScore)}`}>
              {performanceScore.toFixed(0)}
            </div>
            <Button onClick={refreshData} disabled={isLoading} size='sm' variant='outline'>
              {isLoading ? (
                <RefreshCw className='w-4 h-4 animate-spin' />
              ) : (
                <RefreshCw className='w-4 h-4' />
              )}
            </Button>
            <Button onClick={exportReport} disabled={!report} size='sm' variant='outline'>
              <Download className='w-4 h-4' />
            </Button>
          </div>
        </div>
        <Progress value={performanceScore} className='mt-2' />
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className='grid w-full grid-cols-5'>
            <TabsTrigger value='overview'>概览</TabsTrigger>
            <TabsTrigger value='webvitals'>Web Vitals</TabsTrigger>
            <TabsTrigger value='rendering'>渲染</TabsTrigger>
            <TabsTrigger value='resources'>资源</TabsTrigger>
            <TabsTrigger value='issues'>问题</TabsTrigger>
          </TabsList>

          <TabsContent value='overview' className='space-y-4'>
            <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
              <div className='text-center p-3 bg-blue-50 rounded'>
                <Clock className='w-6 h-6 mx-auto mb-1 text-blue-600' />
                <div className='text-lg font-bold text-blue-600'>
                  {formatValue(metrics.rendering?.renderTime, 'ms')}
                </div>
                <div className='text-xs text-gray-600'>渲染时间</div>
              </div>

              <div className='text-center p-3 bg-green-50 rounded'>
                <HardDrive className='w-6 h-6 mx-auto mb-1 text-green-600' />
                <div className='text-lg font-bold text-green-600'>
                  {formatValue(metrics.memory?.used, 'MB', 0)}
                </div>
                <div className='text-xs text-gray-600'>内存使用</div>
              </div>

              <div className='text-center p-3 bg-purple-50 rounded'>
                <Wifi className='w-6 h-6 mx-auto mb-1 text-purple-600' />
                <div className='text-lg font-bold text-purple-600'>
                  {formatValue(metrics.network?.averageTime, 'ms', 0)}
                </div>
                <div className='text-xs text-gray-600'>网络延迟</div>
              </div>

              <div className='text-center p-3 bg-orange-50 rounded'>
                <Cpu className='w-6 h-6 mx-auto mb-1 text-orange-600' />
                <div className='text-lg font-bold text-orange-600'>
                  {metrics.rendering?.componentCount || 0}
                </div>
                <div className='text-xs text-gray-600'>组件数量</div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value='webvitals' className='space-y-4'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <div className='flex justify-between'>
                  <span className='text-sm'>FCP (首次内容绘制)</span>
                  <span className='text-sm font-mono'>
                    {formatValue(metrics.webVitals?.fcp, 'ms', 0)}
                  </span>
                </div>
                <Progress
                  value={Math.min(100, (metrics.webVitals?.fcp || 0) / 18)}
                  className='h-2'
                />
              </div>

              <div className='space-y-2'>
                <div className='flex justify-between'>
                  <span className='text-sm'>LCP (最大内容绘制)</span>
                  <span className='text-sm font-mono'>
                    {formatValue(metrics.webVitals?.lcp, 'ms', 0)}
                  </span>
                </div>
                <Progress
                  value={Math.min(100, (metrics.webVitals?.lcp || 0) / 25)}
                  className='h-2'
                />
              </div>

              <div className='space-y-2'>
                <div className='flex justify-between'>
                  <span className='text-sm'>FID (首次输入延迟)</span>
                  <span className='text-sm font-mono'>
                    {formatValue(metrics.webVitals?.fid, 'ms')}
                  </span>
                </div>
                <Progress
                  value={Math.min(100, (metrics.webVitals?.fid || 0) / 1)}
                  className='h-2'
                />
              </div>

              <div className='space-y-2'>
                <div className='flex justify-between'>
                  <span className='text-sm'>CLS (累积布局偏移)</span>
                  <span className='text-sm font-mono'>
                    {formatValue(metrics.webVitals?.cls, '', 3)}
                  </span>
                </div>
                <Progress
                  value={Math.min(100, (metrics.webVitals?.cls || 0) * 400)}
                  className='h-2'
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value='rendering' className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <div className='text-sm font-medium'>渲染性能</div>
                <div className='space-y-1'>
                  <div className='flex justify-between text-xs'>
                    <span>渲染时间</span>
                    <span>{formatValue(metrics.rendering?.renderTime, 'ms')}</span>
                  </div>
                  <div className='flex justify-between text-xs'>
                    <span>挂载时间</span>
                    <span>{formatValue(metrics.rendering?.mountTime, 'ms')}</span>
                  </div>
                  <div className='flex justify-between text-xs'>
                    <span>更新时间</span>
                    <span>{formatValue(metrics.rendering?.updateTime, 'ms')}</span>
                  </div>
                </div>
              </div>

              <div className='space-y-2'>
                <div className='text-sm font-medium'>渲染统计</div>
                <div className='space-y-1'>
                  <div className='flex justify-between text-xs'>
                    <span>组件数量</span>
                    <span>{metrics.rendering?.componentCount || 0}</span>
                  </div>
                  <div className='flex justify-between text-xs'>
                    <span>重渲染次数</span>
                    <span>{metrics.rendering?.rerenderCount || 0}</span>
                  </div>
                  <div className='flex justify-between text-xs'>
                    <span>无效渲染</span>
                    <span>{metrics.rendering?.wastedRenders || 0}</span>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value='resources' className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <div className='text-sm font-medium'>资源大小</div>
                <div className='space-y-1'>
                  <div className='flex justify-between text-xs'>
                    <span>JavaScript</span>
                    <span>{formatValue(metrics.resources?.jsSize, 'KB', 0)}</span>
                  </div>
                  <div className='flex justify-between text-xs'>
                    <span>CSS</span>
                    <span>{formatValue(metrics.resources?.cssSize, 'KB', 0)}</span>
                  </div>
                  <div className='flex justify-between text-xs'>
                    <span>图片</span>
                    <span>{formatValue(metrics.resources?.imageSize, 'KB', 0)}</span>
                  </div>
                  <div className='flex justify-between text-xs'>
                    <span>字体</span>
                    <span>{formatValue(metrics.resources?.fontSize, 'KB', 0)}</span>
                  </div>
                </div>
              </div>

              <div className='space-y-2'>
                <div className='text-sm font-medium'>网络性能</div>
                <div className='space-y-1'>
                  <div className='flex justify-between text-xs'>
                    <span>请求数量</span>
                    <span>{metrics.network?.requestCount || 0}</span>
                  </div>
                  <div className='flex justify-between text-xs'>
                    <span>平均延迟</span>
                    <span>{formatValue(metrics.network?.averageTime, 'ms', 0)}</span>
                  </div>
                  <div className='flex justify-between text-xs'>
                    <span>失败请求</span>
                    <span>{metrics.network?.failedCount || 0}</span>
                  </div>
                  <div className='flex justify-between text-xs'>
                    <span>总大小</span>
                    <span>{formatValue(metrics.network?.totalSize, 'KB', 0)}</span>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value='issues' className='space-y-4'>
            {issues.length === 0 ? (
              <div className='text-center py-8'>
                <CheckCircle className='w-12 h-12 mx-auto mb-2 text-green-500' />
                <div className='text-lg font-medium text-green-600'>性能良好</div>
                <div className='text-sm text-gray-500'>未检测到性能问题</div>
              </div>
            ) : (
              <div className='space-y-3'>
                {issues.map((issue, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded border-l-4 ${
                      issue.type === 'critical'
                        ? 'bg-red-50 border-red-500'
                        : issue.type === 'warning'
                          ? 'bg-yellow-50 border-yellow-500'
                          : 'bg-blue-50 border-blue-500'
                    }`}
                  >
                    <div className='flex items-start justify-between'>
                      <div className='flex-1'>
                        <div className='flex items-center'>
                          {issue.type === 'critical' ? (
                            <XCircle className='w-4 h-4 mr-2 text-red-500' />
                          ) : issue.type === 'warning' ? (
                            <AlertTriangle className='w-4 h-4 mr-2 text-yellow-500' />
                          ) : (
                            <Eye className='w-4 h-4 mr-2 text-blue-500' />
                          )}
                          <span className='font-medium text-sm'>{issue.message}</span>
                        </div>
                        <div className='text-xs text-gray-600 mt-1'>
                          {issue.metric}: {formatValue(issue.value)} (阈值:{' '}
                          {formatValue(issue.threshold)})
                        </div>
                        {showRecommendations && (
                          <div className='text-xs text-gray-700 mt-2 bg-white p-2 rounded'>
                            💡 {issue.recommendation}
                          </div>
                        )}
                      </div>
                      <Badge
                        variant='outline'
                        className={`ml-2 ${
                          issue.impact === 'high'
                            ? 'border-red-300 text-red-700'
                            : issue.impact === 'medium'
                              ? 'border-yellow-300 text-yellow-700'
                              : 'border-blue-300 text-blue-700'
                        }`}
                      >
                        {issue.impact}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default UnifiedPerformanceMonitor;
