/**
 * Card组件简化测试 - 专注于基本功能而非样式
 */

import React from 'react';
import { screen } from '@testing-library/react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/shared/components/card';
import { render } from '../../utils/test-utils.helper';

describe('Card组件 - 简化测试', () => {
  describe('基本渲染测试', () => {
    it('应该正确渲染Card组件', () => {
      render(<Card data-testid='card'>卡片内容</Card>);

      const card = screen.getByTestId('card');
      expect(card).toBeInTheDocument();
      expect(card).toHaveTextContent('卡片内容');
    });

    it('应该正确渲染CardHeader组件', () => {
      render(<CardHeader data-testid='card-header'>头部内容</CardHeader>);

      const header = screen.getByTestId('card-header');
      expect(header).toBeInTheDocument();
      expect(header).toHaveTextContent('头部内容');
    });

    it('应该正确渲染CardTitle组件', () => {
      render(<CardTitle data-testid='card-title'>卡片标题</CardTitle>);

      const title = screen.getByTestId('card-title');
      expect(title).toBeInTheDocument();
      expect(title).toHaveTextContent('卡片标题');
    });

    it('应该正确渲染CardDescription组件', () => {
      render(<CardDescription data-testid='card-description'>卡片描述</CardDescription>);

      const description = screen.getByTestId('card-description');
      expect(description).toBeInTheDocument();
      expect(description).toHaveTextContent('卡片描述');
    });

    it('应该正确渲染CardContent组件', () => {
      render(<CardContent data-testid='card-content'>卡片内容</CardContent>);

      const content = screen.getByTestId('card-content');
      expect(content).toBeInTheDocument();
      expect(content).toHaveTextContent('卡片内容');
    });

    it('应该正确渲染CardFooter组件', () => {
      render(<CardFooter data-testid='card-footer'>卡片底部</CardFooter>);

      const footer = screen.getByTestId('card-footer');
      expect(footer).toBeInTheDocument();
      expect(footer).toHaveTextContent('卡片底部');
    });
  });

  describe('组合使用测试', () => {
    it('应该正确渲染完整的卡片结构', () => {
      render(
        <Card data-testid='card'>
          <CardHeader data-testid='header'>
            <CardTitle data-testid='title'>卡片标题</CardTitle>
            <CardDescription data-testid='description'>卡片描述</CardDescription>
          </CardHeader>
          <CardContent data-testid='content'>卡片内容</CardContent>
          <CardFooter data-testid='footer'>卡片底部</CardFooter>
        </Card>
      );

      // 检查所有组件都存在
      expect(screen.getByTestId('card')).toBeInTheDocument();
      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('title')).toBeInTheDocument();
      expect(screen.getByTestId('description')).toBeInTheDocument();
      expect(screen.getByTestId('content')).toBeInTheDocument();
      expect(screen.getByTestId('footer')).toBeInTheDocument();

      // 检查文本内容
      expect(screen.getByText('卡片标题')).toBeInTheDocument();
      expect(screen.getByText('卡片描述')).toBeInTheDocument();
      expect(screen.getByText('卡片内容')).toBeInTheDocument();
      expect(screen.getByText('卡片底部')).toBeInTheDocument();
    });
  });

  describe('属性传递测试', () => {
    it('应该支持自定义className', () => {
      render(
        <Card className='custom-card' data-testid='card'>
          内容
        </Card>
      );

      const card = screen.getByTestId('card');
      expect(card).toHaveClass('custom-card');
    });

    it('应该支持所有HTML div属性', () => {
      render(
        <Card data-testid='card' id='test-card' role='region' aria-label='测试卡片'>
          内容
        </Card>
      );

      const card = screen.getByTestId('card');
      expect(card).toHaveAttribute('id', 'test-card');
      expect(card).toHaveAttribute('role', 'region');
      expect(card).toHaveAttribute('aria-label', '测试卡片');
    });
  });

  describe('ref转发测试', () => {
    it('应该正确转发ref', () => {
      const ref = React.createRef<HTMLDivElement>();
      render(
        <Card ref={ref} data-testid='card'>
          内容
        </Card>
      );

      expect(ref.current).toBeInstanceOf(HTMLDivElement);
      expect(ref.current).toBe(screen.getByTestId('card'));
    });
  });
});
