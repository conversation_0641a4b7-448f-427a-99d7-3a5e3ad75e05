'use client';

import { Edit, Plus, Trash2, Settings, Info } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuPortal,
} from '@/shared/components/dropdown-menu';

interface SiloData {
  id: string;
  name: string;
  materialType: 'cement' | 'water' | 'sand' | 'stone' | 'additive' | 'powder' | 'flyash';
  materialName: string;
  specification: string;
  capacity: number;
  currentAmount: number;
  status: 'normal' | 'warning' | 'critical' | 'empty';
  lastUpdated: string;
}

interface SiloContextMenuProps {
  isOpen: boolean;
  position: { x: number; y: number } | null;
  silo: SiloData | null;
  onClose: () => void;
  onSendToRatioPanel: (silo: SiloData) => void;
  onEditSilo: (silo: SiloData) => void;
  onDeleteSilo: (siloId: string) => void;
  onViewSiloInfo: (silo: SiloData) => void;
  onModifySiloStatus: (silo: SiloData) => void;
}

export function SiloContextMenu({
  isOpen,
  position,
  silo,
  onClose,
  onSendToRatioPanel,
  onEditSilo,
  onDeleteSilo,
  onViewSiloInfo,
  onModifySiloStatus,
}: SiloContextMenuProps) {
  if (!isOpen || !position || !silo) return null;

  return (
    <DropdownMenu open={isOpen} onOpenChange={onClose}>
      <DropdownMenuTrigger asChild>
        <div style={{ position: 'fixed', left: position.x, top: position.y }} />
      </DropdownMenuTrigger>
      <DropdownMenuPortal>
        <DropdownMenuContent
          sideOffset={5}
          align='start'
          className='z-50 text-xs w-48'
          onCloseAutoFocus={e => e.preventDefault()}
        >
          {/* 发送到配比面板 */}
          <DropdownMenuItem onClick={() => onSendToRatioPanel(silo)}>
            <Plus className='mr-2 h-3 w-3' />
            发送到配比面板
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* 料仓信息查看 */}
          <DropdownMenuItem onClick={() => onViewSiloInfo(silo)}>
            <Info className='mr-2 h-3 w-3' />
            查看料仓信息
          </DropdownMenuItem>

          {/* 编辑料仓信息 */}
          <DropdownMenuItem onClick={() => onEditSilo(silo)}>
            <Edit className='mr-2 h-3 w-3' />
            编辑料仓信息
          </DropdownMenuItem>

          {/* 修改料仓状态 */}
          <DropdownMenuItem onClick={() => onModifySiloStatus(silo)}>
            <Settings className='mr-2 h-3 w-3' />
            修改料仓状态
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* 删除料仓 */}
          <DropdownMenuItem
            onClick={() => onDeleteSilo(silo.id)}
            className='text-red-600 hover:text-red-700 hover:bg-red-50'
          >
            <Trash2 className='mr-2 h-3 w-3' />
            删除料仓
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenuPortal>
    </DropdownMenu>
  );
}
