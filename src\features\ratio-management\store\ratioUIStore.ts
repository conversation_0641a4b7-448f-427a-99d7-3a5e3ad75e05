/**
 * 配比UI状态管理
 * 只管理UI相关的状态，不包含业务数据
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface RatioUIState {
  // 页面状态
  currentView: 'design' | 'calculation' | 'history' | 'templates';
  isLoading: boolean;
  error: string | null;

  // 设计面板状态
  designPanel: {
    isVisible: boolean;
    selectedMaterials: string[];
    draggedMaterial: string | null;
    dropZone: string | null;
    isCalculating: boolean;
  };

  // 料仓状态
  siloPanel: {
    selectedSilos: string[];
    contextMenu: {
      isVisible: boolean;
      position: { x: number; y: number };
      targetSiloId: string | null;
    };
    dragState: {
      isDragging: boolean;
      draggedSiloId: string | null;
      dragOffset: { x: number; y: number };
    };
  };

  // 计算结果状态
  calculationResults: {
    isVisible: boolean;
    activeTab: 'summary' | 'details' | 'analysis' | 'recommendations';
    expandedSections: string[];
  };

  // 模态框状态
  modals: {
    materialSelector: {
      isOpen: boolean;
      category: string | null;
      selectedMaterials: string[];
    };
    ratioHistory: {
      isOpen: boolean;
      selectedVersion: number | null;
    };
    templateManager: {
      isOpen: boolean;
      mode: 'view' | 'create' | 'edit';
      selectedTemplate: string | null;
    };
    calculationSettings: {
      isOpen: boolean;
      activeSection: 'basic' | 'advanced' | 'environmental';
    };
  };

  // 表单状态
  forms: {
    calculationParams: {
      isDirty: boolean;
      errors: Record<string, string>;
      touched: Record<string, boolean>;
    };
    materialProperties: {
      isDirty: boolean;
      errors: Record<string, string>;
      touched: Record<string, boolean>;
    };
  };

  // 布局状态
  layout: {
    sidebarCollapsed: boolean;
    panelSizes: {
      left: number;
      center: number;
      right: number;
    };
    activePanel: 'materials' | 'design' | 'results';
  };

  // 用户偏好
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    language: 'zh-CN' | 'en-US';
    autoSave: boolean;
    showTooltips: boolean;
    animationsEnabled: boolean;
  };
}

export interface RatioUIActions {
  // 页面状态操作
  setCurrentView: (view: RatioUIState['currentView']) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // 设计面板操作
  toggleDesignPanel: () => void;
  setSelectedMaterials: (materials: string[]) => void;
  addSelectedMaterial: (materialId: string) => void;
  removeSelectedMaterial: (materialId: string) => void;
  setDraggedMaterial: (materialId: string | null) => void;
  setDropZone: (zone: string | null) => void;
  setCalculating: (calculating: boolean) => void;

  // 料仓操作
  setSelectedSilos: (silos: string[]) => void;
  addSelectedSilo: (siloId: string) => void;
  removeSelectedSilo: (siloId: string) => void;
  showSiloContextMenu: (position: { x: number; y: number }, siloId: string) => void;
  hideSiloContextMenu: () => void;
  startSiloDrag: (siloId: string, offset: { x: number; y: number }) => void;
  updateSiloDrag: (offset: { x: number; y: number }) => void;
  endSiloDrag: () => void;

  // 计算结果操作
  toggleCalculationResults: () => void;
  setCalculationResultsTab: (tab: RatioUIState['calculationResults']['activeTab']) => void;
  toggleResultSection: (section: string) => void;

  // 模态框操作
  openMaterialSelector: (category?: string) => void;
  closeMaterialSelector: () => void;
  setMaterialSelectorCategory: (category: string | null) => void;
  setMaterialSelectorSelection: (materials: string[]) => void;

  openRatioHistory: () => void;
  closeRatioHistory: () => void;
  setSelectedHistoryVersion: (version: number | null) => void;

  openTemplateManager: (mode: 'view' | 'create' | 'edit', templateId?: string) => void;
  closeTemplateManager: () => void;

  openCalculationSettings: (section?: 'basic' | 'advanced' | 'environmental') => void;
  closeCalculationSettings: () => void;
  setCalculationSettingsSection: (section: 'basic' | 'advanced' | 'environmental') => void;

  // 表单操作
  setFormDirty: (form: keyof RatioUIState['forms'], dirty: boolean) => void;
  setFormError: (form: keyof RatioUIState['forms'], field: string, error: string | null) => void;
  setFormTouched: (form: keyof RatioUIState['forms'], field: string, touched: boolean) => void;
  clearFormErrors: (form: keyof RatioUIState['forms']) => void;
  resetForm: (form: keyof RatioUIState['forms']) => void;

  // 布局操作
  toggleSidebar: () => void;
  setPanelSizes: (sizes: { left: number; center: number; right: number }) => void;
  setActivePanel: (panel: RatioUIState['layout']['activePanel']) => void;

  // 用户偏好操作
  setTheme: (theme: RatioUIState['preferences']['theme']) => void;
  setLanguage: (language: RatioUIState['preferences']['language']) => void;
  setAutoSave: (autoSave: boolean) => void;
  setShowTooltips: (show: boolean) => void;
  setAnimationsEnabled: (enabled: boolean) => void;

  // 重置操作
  resetUIState: () => void;
  resetDesignPanel: () => void;
  resetSiloPanel: () => void;
  resetModals: () => void;
}

const initialState: RatioUIState = {
  currentView: 'design',
  isLoading: false,
  error: null,

  designPanel: {
    isVisible: true,
    selectedMaterials: [],
    draggedMaterial: null,
    dropZone: null,
    isCalculating: false,
  },

  siloPanel: {
    selectedSilos: [],
    contextMenu: {
      isVisible: false,
      position: { x: 0, y: 0 },
      targetSiloId: null,
    },
    dragState: {
      isDragging: false,
      draggedSiloId: null,
      dragOffset: { x: 0, y: 0 },
    },
  },

  calculationResults: {
    isVisible: false,
    activeTab: 'summary',
    expandedSections: [],
  },

  modals: {
    materialSelector: {
      isOpen: false,
      category: null,
      selectedMaterials: [],
    },
    ratioHistory: {
      isOpen: false,
      selectedVersion: null,
    },
    templateManager: {
      isOpen: false,
      mode: 'view',
      selectedTemplate: null,
    },
    calculationSettings: {
      isOpen: false,
      activeSection: 'basic',
    },
  },

  forms: {
    calculationParams: {
      isDirty: false,
      errors: {},
      touched: {},
    },
    materialProperties: {
      isDirty: false,
      errors: {},
      touched: {},
    },
  },

  layout: {
    sidebarCollapsed: false,
    panelSizes: {
      left: 25,
      center: 50,
      right: 25,
    },
    activePanel: 'design',
  },

  preferences: {
    theme: 'light',
    language: 'zh-CN',
    autoSave: true,
    showTooltips: true,
    animationsEnabled: true,
  },
};

export const useRatioUIStore = create<RatioUIState & RatioUIActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // 页面状态操作
      setCurrentView: view => set({ currentView: view }),
      setLoading: loading => set({ isLoading: loading }),
      setError: error => set({ error }),

      // 设计面板操作
      toggleDesignPanel: () =>
        set(state => ({
          designPanel: {
            ...state.designPanel,
            isVisible: !state.designPanel.isVisible,
          },
        })),

      setSelectedMaterials: materials =>
        set(state => ({
          designPanel: {
            ...state.designPanel,
            selectedMaterials: materials,
          },
        })),

      addSelectedMaterial: materialId =>
        set(state => ({
          designPanel: {
            ...state.designPanel,
            selectedMaterials: [...state.designPanel.selectedMaterials, materialId],
          },
        })),

      removeSelectedMaterial: materialId =>
        set(state => ({
          designPanel: {
            ...state.designPanel,
            selectedMaterials: state.designPanel.selectedMaterials.filter(id => id !== materialId),
          },
        })),

      setDraggedMaterial: materialId =>
        set(state => ({
          designPanel: {
            ...state.designPanel,
            draggedMaterial: materialId,
          },
        })),

      setDropZone: zone =>
        set(state => ({
          designPanel: {
            ...state.designPanel,
            dropZone: zone,
          },
        })),

      setCalculating: calculating =>
        set(state => ({
          designPanel: {
            ...state.designPanel,
            isCalculating: calculating,
          },
        })),

      // 料仓操作
      setSelectedSilos: silos =>
        set(state => ({
          siloPanel: {
            ...state.siloPanel,
            selectedSilos: silos,
          },
        })),

      addSelectedSilo: siloId =>
        set(state => ({
          siloPanel: {
            ...state.siloPanel,
            selectedSilos: [...state.siloPanel.selectedSilos, siloId],
          },
        })),

      removeSelectedSilo: siloId =>
        set(state => ({
          siloPanel: {
            ...state.siloPanel,
            selectedSilos: state.siloPanel.selectedSilos.filter(id => id !== siloId),
          },
        })),

      showSiloContextMenu: (position, siloId) =>
        set(state => ({
          siloPanel: {
            ...state.siloPanel,
            contextMenu: {
              isVisible: true,
              position,
              targetSiloId: siloId,
            },
          },
        })),

      hideSiloContextMenu: () =>
        set(state => ({
          siloPanel: {
            ...state.siloPanel,
            contextMenu: {
              isVisible: false,
              position: { x: 0, y: 0 },
              targetSiloId: null,
            },
          },
        })),

      startSiloDrag: (siloId, offset) =>
        set(state => ({
          siloPanel: {
            ...state.siloPanel,
            dragState: {
              isDragging: true,
              draggedSiloId: siloId,
              dragOffset: offset,
            },
          },
        })),

      updateSiloDrag: offset =>
        set(state => ({
          siloPanel: {
            ...state.siloPanel,
            dragState: {
              ...state.siloPanel.dragState,
              dragOffset: offset,
            },
          },
        })),

      endSiloDrag: () =>
        set(state => ({
          siloPanel: {
            ...state.siloPanel,
            dragState: {
              isDragging: false,
              draggedSiloId: null,
              dragOffset: { x: 0, y: 0 },
            },
          },
        })),

      // 计算结果操作
      toggleCalculationResults: () =>
        set(state => ({
          calculationResults: {
            ...state.calculationResults,
            isVisible: !state.calculationResults.isVisible,
          },
        })),

      setCalculationResultsTab: tab =>
        set(state => ({
          calculationResults: {
            ...state.calculationResults,
            activeTab: tab,
          },
        })),

      toggleResultSection: section =>
        set(state => ({
          calculationResults: {
            ...state.calculationResults,
            expandedSections: state.calculationResults.expandedSections.includes(section)
              ? state.calculationResults.expandedSections.filter(s => s !== section)
              : [...state.calculationResults.expandedSections, section],
          },
        })),

      // 模态框操作
      openMaterialSelector: category =>
        set(state => ({
          modals: {
            ...state.modals,
            materialSelector: {
              isOpen: true,
              category: category || null,
              selectedMaterials: [],
            },
          },
        })),

      closeMaterialSelector: () =>
        set(state => ({
          modals: {
            ...state.modals,
            materialSelector: {
              isOpen: false,
              category: null,
              selectedMaterials: [],
            },
          },
        })),

      setMaterialSelectorCategory: category =>
        set(state => ({
          modals: {
            ...state.modals,
            materialSelector: {
              ...state.modals.materialSelector,
              category,
            },
          },
        })),

      setMaterialSelectorSelection: materials =>
        set(state => ({
          modals: {
            ...state.modals,
            materialSelector: {
              ...state.modals.materialSelector,
              selectedMaterials: materials,
            },
          },
        })),

      openRatioHistory: () =>
        set(state => ({
          modals: {
            ...state.modals,
            ratioHistory: {
              isOpen: true,
              selectedVersion: null,
            },
          },
        })),

      closeRatioHistory: () =>
        set(state => ({
          modals: {
            ...state.modals,
            ratioHistory: {
              isOpen: false,
              selectedVersion: null,
            },
          },
        })),

      setSelectedHistoryVersion: version =>
        set(state => ({
          modals: {
            ...state.modals,
            ratioHistory: {
              ...state.modals.ratioHistory,
              selectedVersion: version,
            },
          },
        })),

      openTemplateManager: (mode, templateId) =>
        set(state => ({
          modals: {
            ...state.modals,
            templateManager: {
              isOpen: true,
              mode,
              selectedTemplate: templateId || null,
            },
          },
        })),

      closeTemplateManager: () =>
        set(state => ({
          modals: {
            ...state.modals,
            templateManager: {
              isOpen: false,
              mode: 'view',
              selectedTemplate: null,
            },
          },
        })),

      openCalculationSettings: section =>
        set(state => ({
          modals: {
            ...state.modals,
            calculationSettings: {
              isOpen: true,
              activeSection: section || 'basic',
            },
          },
        })),

      closeCalculationSettings: () =>
        set(state => ({
          modals: {
            ...state.modals,
            calculationSettings: {
              isOpen: false,
              activeSection: 'basic',
            },
          },
        })),

      setCalculationSettingsSection: section =>
        set(state => ({
          modals: {
            ...state.modals,
            calculationSettings: {
              ...state.modals.calculationSettings,
              activeSection: section,
            },
          },
        })),

      // 表单操作
      setFormDirty: (form, dirty) =>
        set(state => ({
          forms: {
            ...state.forms,
            [form]: {
              ...state.forms[form],
              isDirty: dirty,
            },
          },
        })),

      setFormError: (form, field, error) =>
        set(state => ({
          forms: {
            ...state.forms,
            [form]: {
              ...state.forms[form],
              errors: error
                ? { ...state.forms[form].errors, [field]: error }
                : Object.fromEntries(
                    Object.entries(state.forms[form].errors).filter(([k]) => k !== field)
                  ),
            },
          },
        })),

      setFormTouched: (form, field, touched) =>
        set(state => ({
          forms: {
            ...state.forms,
            [form]: {
              ...state.forms[form],
              touched: { ...state.forms[form].touched, [field]: touched },
            },
          },
        })),

      clearFormErrors: form =>
        set(state => ({
          forms: {
            ...state.forms,
            [form]: {
              ...state.forms[form],
              errors: {},
            },
          },
        })),

      resetForm: form =>
        set(state => ({
          forms: {
            ...state.forms,
            [form]: {
              isDirty: false,
              errors: {},
              touched: {},
            },
          },
        })),

      // 布局操作
      toggleSidebar: () =>
        set(state => ({
          layout: {
            ...state.layout,
            sidebarCollapsed: !state.layout.sidebarCollapsed,
          },
        })),

      setPanelSizes: sizes =>
        set(state => ({
          layout: {
            ...state.layout,
            panelSizes: sizes,
          },
        })),

      setActivePanel: panel =>
        set(state => ({
          layout: {
            ...state.layout,
            activePanel: panel,
          },
        })),

      // 用户偏好操作
      setTheme: theme =>
        set(state => ({
          preferences: {
            ...state.preferences,
            theme,
          },
        })),

      setLanguage: language =>
        set(state => ({
          preferences: {
            ...state.preferences,
            language,
          },
        })),

      setAutoSave: autoSave =>
        set(state => ({
          preferences: {
            ...state.preferences,
            autoSave,
          },
        })),

      setShowTooltips: show =>
        set(state => ({
          preferences: {
            ...state.preferences,
            showTooltips: show,
          },
        })),

      setAnimationsEnabled: enabled =>
        set(state => ({
          preferences: {
            ...state.preferences,
            animationsEnabled: enabled,
          },
        })),

      // 重置操作
      resetUIState: () => set(initialState),

      resetDesignPanel: () =>
        set(state => ({
          designPanel: initialState.designPanel,
        })),

      resetSiloPanel: () =>
        set(state => ({
          siloPanel: initialState.siloPanel,
        })),

      resetModals: () =>
        set(state => ({
          modals: initialState.modals,
        })),
    }),
    {
      name: 'ratio-ui-store',
      partialize: (state: RatioUIState & RatioUIActions) => ({
        preferences: state.preferences,
        layout: {
          sidebarCollapsed: state.layout.sidebarCollapsed,
          panelSizes: state.layout.panelSizes,
        },
      }),
    }
  )
);
