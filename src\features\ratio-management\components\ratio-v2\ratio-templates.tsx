'use client';

import React, { useState } from 'react';

import {
  <PERSON><PERSON><PERSON>,
  Container,
  Copy,
  Download,
  Droplets,
  Edit,
  Eye,
  FileText,
  Gauge,
  Mountain,
  Plus,
  Search,
  Star,
  Target,
  Trash2,
  Zap,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
// 移除 Dialog 导入，使用自定义模态框
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Input } from '@/shared/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/shared/components/tabs';
import { useToast } from '@/shared/hooks/use-toast';
import { cn } from '@/core/lib/utils';

interface RatioTemplate {
  id: string;
  name: string;
  strength: string;
  category: 'standard' | 'special' | 'custom';
  environment: 'normal' | 'marine' | 'freeze' | 'chemical';
  description: string;
  materials: {
    cement: number;
    water: number;
    sand: number;
    stone: number;
    flyash?: number;
    additive?: number;
    mineralPowder?: number;
  };
  parameters: {
    waterRatio: number;
    sandRatio: number;
    density: number;
    slump: number;
  };
  performance: {
    strengthPrediction: number;
    durability: string;
    workability: string;
    economy: string;
  };
  usage: {
    applications: string[];
    advantages: string[];
    notes: string;
  };
  isStarred: boolean;
  createTime: string;
  author: string;
  usageCount: number;
}

interface RatioTemplatesProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyTemplate: (template: RatioTemplate) => void;
}

export function RatioTemplates({ isOpen, onClose, onApplyTemplate }: RatioTemplatesProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [strengthFilter, setStrengthFilter] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<RatioTemplate | null>(null);
  const [activeTab, setActiveTab] = useState('templates');
  const [starredOnly, setStarredOnly] = useState(false);

  // 模拟模板数据
  const templates: RatioTemplate[] = [
    {
      id: 'template-1',
      name: 'C25普通混凝土标准配比',
      strength: 'C25',
      category: 'standard',
      environment: 'normal',
      description: '适用于一般建筑结构的C25混凝土标准配比',
      materials: {
        cement: 380,
        water: 180,
        sand: 650,
        stone: 1200,
        flyash: 60,
        additive: 4.5,
      },
      parameters: {
        waterRatio: 0.45,
        sandRatio: 35,
        density: 2.4,
        slump: 180,
      },
      performance: {
        strengthPrediction: 28.5,
        durability: '良好',
        workability: '优秀',
        economy: '经济',
      },
      usage: {
        applications: ['框架结构', '剪力墙', '楼板', '梁柱'],
        advantages: ['工作性好', '成本经济', '强度稳定'],
        notes: '适用于大部分民用建筑，施工性能优良',
      },
      isStarred: true,
      createTime: '2024-01-15',
      author: '技术部',
      usageCount: 156,
    },
    {
      id: 'template-2',
      name: 'C30高性能混凝土配比',
      strength: 'C30',
      category: 'special',
      environment: 'normal',
      description: '高强度、高耐久性的C30混凝土配比',
      materials: {
        cement: 420,
        water: 165,
        sand: 630,
        stone: 1180,
        flyash: 80,
        additive: 6.2,
        mineralPowder: 40,
      },
      parameters: {
        waterRatio: 0.38,
        sandRatio: 34,
        density: 2.42,
        slump: 200,
      },
      performance: {
        strengthPrediction: 35.2,
        durability: '优秀',
        workability: '良好',
        economy: '中等',
      },
      usage: {
        applications: ['高层建筑', '桥梁工程', '预制构件'],
        advantages: ['强度高', '耐久性好', '收缩小'],
        notes: '适用于重要结构工程，质量要求较高',
      },
      isStarred: false,
      createTime: '2024-02-20',
      author: '李总工',
      usageCount: 89,
    },
  ];

  const filteredTemplates = templates.filter(template => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.strength.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || template.category === categoryFilter;
    const matchesStrength = strengthFilter === 'all' || template.strength === strengthFilter;
    const matchesStarred = !starredOnly || template.isStarred;

    return matchesSearch && matchesCategory && matchesStrength && matchesStarred;
  });

  const handleApplyTemplate = (template: RatioTemplate) => {
    onApplyTemplate(template);
    onClose();

    toast({
      title: '模板已应用',
      description: `已应用 ${template.name} 配比模板`,
    });
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'standard':
        return <BookOpen className='h-4 w-4 text-blue-500' />;
      case 'special':
        return <Target className='h-4 w-4 text-purple-500' />;
      case 'custom':
        return <Edit className='h-4 w-4 text-green-500' />;
      default:
        return <FileText className='h-4 w-4' />;
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'standard':
        return '标准配比';
      case 'special':
        return '特殊配比';
      case 'custom':
        return '自定义';
      default:
        return category;
    }
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 z-[100] bg-black/50 flex items-center justify-center p-4'>
      <div className='bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto'>
        <div className='p-6 border-b'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <FileText className='h-6 w-6 text-primary' />
              <h2 className='text-xl font-semibold'>配比模板库</h2>
              <Badge variant='outline' className='ml-2'>
                {filteredTemplates.length} 个模板
              </Badge>
            </div>
            <Button variant='ghost' size='sm' onClick={onClose}>
              ✕
            </Button>
          </div>
        </div>

        <div className='p-6'>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger value='templates'>模板列表</TabsTrigger>
              <TabsTrigger value='categories'>分类浏览</TabsTrigger>
            </TabsList>

            <TabsContent value='templates' className='space-y-4'>
              {/* 搜索和筛选 */}
              <div className='flex gap-4'>
                <div className='flex-1 relative'>
                  <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
                  <Input
                    placeholder='搜索模板名称、强度等级或描述...'
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    className='pl-10'
                  />
                </div>

                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className='w-40'>
                    <SelectValue placeholder='分类筛选' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>全部分类</SelectItem>
                    <SelectItem value='standard'>标准配比</SelectItem>
                    <SelectItem value='special'>特殊配比</SelectItem>
                    <SelectItem value='custom'>自定义</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant={starredOnly ? 'default' : 'outline'}
                  onClick={() => setStarredOnly(!starredOnly)}
                  className='gap-2'
                >
                  <Star className={cn('h-4 w-4', starredOnly && 'fill-current')} />
                  收藏
                </Button>
              </div>

              {/* 模板网格 */}
              <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-4'>
                {filteredTemplates.map(template => (
                  <Card key={template.id} className='hover:shadow-lg transition-all duration-200'>
                    <CardHeader className='pb-3'>
                      <div className='flex items-start justify-between'>
                        <div className='flex items-center gap-2'>
                          {getCategoryIcon(template.category)}
                          <Badge variant='outline'>{getCategoryLabel(template.category)}</Badge>
                        </div>

                        <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
                          <Star
                            className={cn(
                              'h-4 w-4',
                              template.isStarred
                                ? 'fill-yellow-400 text-yellow-400'
                                : 'text-gray-400'
                            )}
                          />
                        </Button>
                      </div>

                      <div>
                        <CardTitle className='text-base'>{template.name}</CardTitle>
                        <p className='text-sm text-muted-foreground mt-1'>{template.description}</p>
                      </div>
                    </CardHeader>

                    <CardContent className='space-y-4'>
                      <div className='flex items-center justify-between'>
                        <Badge className='bg-gradient-to-r from-blue-500 to-purple-600 text-white'>
                          {template.strength}
                        </Badge>
                      </div>

                      <div className='flex gap-2'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => setSelectedTemplate(template)}
                          className='flex-1 gap-1'
                        >
                          <Eye className='h-3 w-3' />
                          详情
                        </Button>

                        <Button
                          size='sm'
                          onClick={() => handleApplyTemplate(template)}
                          className='flex-1 gap-1'
                        >
                          <Copy className='h-3 w-3' />
                          应用
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredTemplates.length === 0 && (
                <div className='text-center py-12'>
                  <FileText className='h-16 w-16 text-muted-foreground mx-auto mb-4' />
                  <h3 className='text-lg font-semibold mb-2'>暂无匹配的模板</h3>
                  <p className='text-muted-foreground'>请尝试调整搜索条件或筛选器</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value='categories' className='space-y-4'>
              <div className='text-center py-12'>
                <FileText className='h-16 w-16 text-muted-foreground mx-auto mb-4' />
                <h3 className='text-lg font-semibold mb-2'>分类浏览</h3>
                <p className='text-muted-foreground'>功能开发中...</p>
              </div>
            </TabsContent>
          </Tabs>

          <div className='flex justify-end pt-4 border-t'>
            <Button onClick={onClose}>关闭</Button>
          </div>
        </div>
      </div>
    </div>
  );
}
