/**
 * V1配比页面 - 使用BaseRatioPage重构版本
 * 展示如何使用新的基础架构重构现有页面
 */

'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Button } from '@/shared/components/button';
import { TestTube, Save, RefreshCw } from 'lucide-react';

// 使用新的基础架构
import {
  BaseRatioPage,
  V1_CONFIG,
  type BaseRatioPageRenderProps,
} from '@/features/ratio-management/components/ratio/BaseRatioPage';
import { useBaseRatioManagement } from '@/features/ratio-management/hooks/ratio/useBaseRatioManagement';

import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/table';
import { Badge } from '@/shared/components/badge';
import { Separator } from '@/shared/components/separator';
import { Calculator, Plus, Trash2, Settings } from 'lucide-react';

/**
 * 任务信息面板组件
 */
function TaskInfoPanel({
  task,
  calculationParams,
  onUpdateParams,
}: {
  task: any;
  calculationParams: any;
  onUpdateParams: (params: any) => void;
}) {
  return (
    <Card>
      <CardHeader className='pb-2'>
        <CardTitle className='text-sm'>任务信息</CardTitle>
      </CardHeader>
      <CardContent className='space-y-3'>
        <div className='grid grid-cols-2 gap-3 text-sm'>
          <div>
            <Label className='text-xs text-muted-foreground'>项目名称</Label>
            <div className='font-medium'>{task?.projectName || '未知项目'}</div>
          </div>
          <div>
            <Label className='text-xs text-muted-foreground'>任务编号</Label>
            <div className='font-medium'>{task?.taskNumber || '未知任务'}</div>
          </div>
          <div>
            <Label className='text-xs text-muted-foreground'>强度等级</Label>
            <Select
              value={calculationParams?.strength || 'C30'}
              onValueChange={value => onUpdateParams({ ...calculationParams, strength: value })}
            >
              <SelectTrigger className='h-7'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='C20'>C20</SelectItem>
                <SelectItem value='C25'>C25</SelectItem>
                <SelectItem value='C30'>C30</SelectItem>
                <SelectItem value='C35'>C35</SelectItem>
                <SelectItem value='C40'>C40</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label className='text-xs text-muted-foreground'>坍落度</Label>
            <Select
              value={calculationParams?.slump || '120-160mm'}
              onValueChange={value => onUpdateParams({ ...calculationParams, slump: value })}
            >
              <SelectTrigger className='h-7'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='30-50mm'>30-50mm</SelectItem>
                <SelectItem value='60-90mm'>60-90mm</SelectItem>
                <SelectItem value='120-160mm'>120-160mm</SelectItem>
                <SelectItem value='180-220mm'>180-220mm</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 计算面板组件
 */
function CalculationPanel({
  calculationParams,
  calculationResults,
  isCalculating,
  onUpdateParams,
  onCalculate,
  canCalculate,
}: {
  calculationParams: any;
  calculationResults: any;
  isCalculating: boolean;
  onUpdateParams: (params: any) => void;
  onCalculate: () => void;
  canCalculate: boolean;
}) {
  return (
    <Card>
      <CardHeader className='pb-2'>
        <CardTitle className='text-sm flex items-center gap-2'>
          <Calculator className='w-4 h-4' />
          计算参数与结果
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        {/* 计算参数 */}
        <div className='grid grid-cols-3 gap-3'>
          <div>
            <Label className='text-xs text-muted-foreground'>水胶比</Label>
            <Input
              type='number'
              step='0.01'
              value={calculationParams?.waterCementRatio || 0.45}
              onChange={e =>
                onUpdateParams({
                  ...calculationParams,
                  waterCementRatio: Number(e.target.value),
                })
              }
              className='h-7 text-sm'
            />
          </div>
          <div>
            <Label className='text-xs text-muted-foreground'>砂率 (%)</Label>
            <Input
              type='number'
              value={calculationParams?.sandRatio || 35}
              onChange={e =>
                onUpdateParams({
                  ...calculationParams,
                  sandRatio: Number(e.target.value),
                })
              }
              className='h-7 text-sm'
            />
          </div>
          <div>
            <Label className='text-xs text-muted-foreground'>密度 (kg/m³)</Label>
            <Input
              type='number'
              value={calculationParams?.density || 2400}
              onChange={e =>
                onUpdateParams({
                  ...calculationParams,
                  density: Number(e.target.value),
                })
              }
              className='h-7 text-sm'
            />
          </div>
        </div>

        <Separator />

        {/* 计算结果 */}
        <div className='grid grid-cols-4 gap-3 text-sm'>
          <div className='text-center'>
            <Label className='text-xs text-muted-foreground'>水泥用量</Label>
            <div className='text-lg font-mono font-bold text-primary'>
              {calculationResults?.cementAmount || 350}
            </div>
            <div className='text-xs text-muted-foreground'>kg/m³</div>
          </div>
          <div className='text-center'>
            <Label className='text-xs text-muted-foreground'>砂用量</Label>
            <div className='text-lg font-mono font-bold text-primary'>
              {calculationResults?.sandAmount || 650}
            </div>
            <div className='text-xs text-muted-foreground'>kg/m³</div>
          </div>
          <div className='text-center'>
            <Label className='text-xs text-muted-foreground'>石用量</Label>
            <div className='text-lg font-mono font-bold text-primary'>
              {calculationResults?.stoneAmount || 1200}
            </div>
            <div className='text-xs text-muted-foreground'>kg/m³</div>
          </div>
          <div className='text-center'>
            <Label className='text-xs text-muted-foreground'>水用量</Label>
            <div className='text-lg font-mono font-bold text-primary'>
              {calculationResults?.waterAmount || 175}
            </div>
            <div className='text-xs text-muted-foreground'>kg/m³</div>
          </div>
        </div>

        <div className='flex gap-2'>
          <Button
            size='sm'
            onClick={onCalculate}
            disabled={!canCalculate || isCalculating}
            className='flex-1'
          >
            <Calculator className='w-4 h-4 mr-1' />
            {isCalculating ? '计算中...' : '重新计算'}
          </Button>
          <Button size='sm' variant='outline'>
            反算参数
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 配比表格组件
 */
function ProportioningTable({
  materials,
  onOpenModal,
  onAddMaterial,
  onRemoveMaterial,
  onUpdateMaterial,
  isLoading,
}: {
  materials: any[];
  onOpenModal: (modalName: string) => void;
  onAddMaterial: () => void;
  onRemoveMaterial: (id: string) => void;
  onUpdateMaterial: (id: string, updates: any) => void;
  isLoading: boolean;
}) {
  const mockMaterials = materials?.length
    ? materials
    : [
        { id: '1', name: '水泥', type: 'cement', amount: 350, unit: 'kg/m³', siloNumber: '1#' },
        { id: '2', name: '砂', type: 'sand', amount: 650, unit: 'kg/m³', siloNumber: '2#' },
        { id: '3', name: '石', type: 'stone', amount: 1200, unit: 'kg/m³', siloNumber: '3#' },
        { id: '4', name: '水', type: 'water', amount: 175, unit: 'kg/m³', siloNumber: '4#' },
      ];

  return (
    <Card>
      <CardHeader className='pb-2'>
        <div className='flex items-center justify-between'>
          <CardTitle className='text-sm'>配比表格</CardTitle>
          <div className='flex gap-1'>
            <Button size='sm' variant='outline' onClick={onAddMaterial}>
              <Plus className='w-4 h-4 mr-1' />
              添加材料
            </Button>
            <Button size='sm' variant='outline' onClick={() => onOpenModal('silo')}>
              <Settings className='w-4 h-4 mr-1' />
              料仓设置
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className='w-16'>序号</TableHead>
              <TableHead>材料名称</TableHead>
              <TableHead className='w-20'>料仓</TableHead>
              <TableHead className='w-24'>用量</TableHead>
              <TableHead className='w-20'>单位</TableHead>
              <TableHead className='w-20'>状态</TableHead>
              <TableHead className='w-20'>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mockMaterials.map((material, index) => (
              <TableRow key={material.id}>
                <TableCell className='text-center'>{index + 1}</TableCell>
                <TableCell>
                  <Input
                    value={material.name}
                    onChange={e => onUpdateMaterial(material.id, { name: e.target.value })}
                    className='h-7 border-none bg-transparent p-0'
                  />
                </TableCell>
                <TableCell>
                  <Badge variant='outline' className='text-xs'>
                    {material.siloNumber}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Input
                    type='number'
                    value={material.amount}
                    onChange={e =>
                      onUpdateMaterial(material.id, { amount: Number(e.target.value) })
                    }
                    className='h-7 text-right'
                  />
                </TableCell>
                <TableCell className='text-xs text-muted-foreground'>{material.unit}</TableCell>
                <TableCell>
                  <Badge variant='secondary' className='text-xs'>
                    正常
                  </Badge>
                </TableCell>
                <TableCell>
                  <Button
                    size='sm'
                    variant='ghost'
                    onClick={() => onRemoveMaterial(material.id)}
                    className='h-6 w-6 p-0'
                  >
                    <Trash2 className='w-3 h-3' />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}

/**
 * 材料侧边栏组件
 */
function MaterialsSidebar({
  materials,
  onAddMaterial,
  onUpdateMaterial,
}: {
  materials: any[];
  onAddMaterial: () => void;
  onUpdateMaterial: (id: string, updates: any) => void;
}) {
  const materialTypes = [
    { type: 'cement', name: '水泥', color: 'bg-gray-500' },
    { type: 'sand', name: '砂', color: 'bg-yellow-500' },
    { type: 'stone', name: '石', color: 'bg-gray-700' },
    { type: 'water', name: '水', color: 'bg-blue-500' },
    { type: 'admixture', name: '外加剂', color: 'bg-green-500' },
  ];

  return (
    <Card className='h-full'>
      <CardHeader className='pb-2'>
        <CardTitle className='text-sm'>材料库</CardTitle>
      </CardHeader>
      <CardContent className='space-y-3'>
        {/* 材料类型 */}
        <div className='space-y-2'>
          <Label className='text-xs text-muted-foreground'>材料类型</Label>
          <div className='grid gap-2'>
            {materialTypes.map(type => (
              <div
                key={type.type}
                className='flex items-center gap-2 p-2 rounded border cursor-pointer hover:bg-muted/50'
                onClick={onAddMaterial}
              >
                <div className={`w-3 h-3 rounded ${type.color}`} />
                <span className='text-sm'>{type.name}</span>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* 快速操作 */}
        <div className='space-y-2'>
          <Label className='text-xs text-muted-foreground'>快速操作</Label>
          <div className='space-y-1'>
            <Button variant='outline' size='sm' className='w-full justify-start'>
              <Plus className='w-4 h-4 mr-2' />
              添加自定义材料
            </Button>
            <Button variant='outline' size='sm' className='w-full justify-start'>
              <Settings className='w-4 h-4 mr-2' />
              材料属性设置
            </Button>
          </div>
        </div>

        <Separator />

        {/* 配比信息 */}
        <div className='space-y-2'>
          <Label className='text-xs text-muted-foreground'>配比信息</Label>
          <div className='space-y-2 text-xs'>
            <div className='flex justify-between'>
              <span>总用量:</span>
              <span className='font-mono'>2375 kg/m³</span>
            </div>
            <div className='flex justify-between'>
              <span>胶凝材料:</span>
              <span className='font-mono'>350 kg/m³</span>
            </div>
            <div className='flex justify-between'>
              <span>骨料:</span>
              <span className='font-mono'>1850 kg/m³</span>
            </div>
            <div className='flex justify-between'>
              <span>用水量:</span>
              <span className='font-mono'>175 kg/m³</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * V1配比页面头部组件
 */
function V1RatioHeader({
  task,
  onOpenModal,
}: {
  task: any;
  onOpenModal: (modalName: string) => void;
}) {
  return (
    <Card className='flex items-center justify-between p-1 flex-wrap gap-1 flex-shrink-0'>
      <div className='flex items-center gap-2 flex-wrap'>
        <h1 className='text-xl font-bold text-primary flex items-center gap-2'>
          <TestTube className='w-6 h-6' />
          <span>砼配比</span>
        </h1>
        <div className='text-sm text-muted-foreground'>任务: {task?.taskNumber || '未知'}</div>
      </div>

      <div className='flex items-center gap-1'>
        <Button
          variant='ghost'
          size='sm'
          className='gap-1 h-7 text-xs'
          onClick={() => onOpenModal('silo')}
        >
          料仓管理
        </Button>
        <Button
          variant='ghost'
          size='sm'
          className='gap-1 h-7 text-xs'
          onClick={() => onOpenModal('history')}
        >
          历史记录
        </Button>
        <Button
          variant='ghost'
          size='sm'
          className='gap-1 h-7 text-xs'
          onClick={() => onOpenModal('mortar')}
        >
          砂浆配比
        </Button>
        <Button
          variant='ghost'
          size='sm'
          className='gap-1 h-7 text-xs'
          onClick={() => onOpenModal('settings')}
        >
          设置
        </Button>
        <Button
          variant='ghost'
          size='sm'
          className='gap-1 h-7 text-xs'
          onClick={() => onOpenModal('ratioSelection')}
        >
          选用配比
        </Button>
        <Button variant='ghost' size='sm' className='gap-1 h-7 text-xs'>
          <RefreshCw className='w-4 h-4' />
          刷新料仓
        </Button>
      </div>
    </Card>
  );
}

/**
 * V1配比页面底部组件
 */
function V1RatioFooter({
  onSave,
  canSave,
  isSaving,
}: {
  onSave: () => Promise<void>;
  canSave: boolean;
  isSaving: boolean;
}) {
  return (
    <Card className='flex items-center justify-between p-2 flex-shrink-0'>
      <div className='text-sm text-muted-foreground'>V1 经典配比设计界面</div>
      <div className='flex items-center gap-2'>
        <Button size='sm' onClick={onSave} disabled={!canSave || isSaving}>
          <Save className='w-4 h-4 mr-1' />
          {isSaving ? '保存中...' : '保存'}
        </Button>
        <Button size='sm'>审核-发送</Button>
        <Button size='sm' variant='secondary'>
          退出
        </Button>
      </div>
    </Card>
  );
}

/**
 * V1配比页面主要内容
 */
function V1RatioContent({ renderProps }: { renderProps: BaseRatioPageRenderProps }) {
  const { task, taskId, openModal } = renderProps;

  // 使用基础配比管理Hook
  const ratioManagement = useBaseRatioManagement({
    taskId,
    version: 'v1',
    autoLoad: true,
    autoSave: false, // V1版本手动保存
  });

  const {
    currentRatio,
    materials,
    calculationParams,
    calculationResults,
    isLoading,
    isCalculating,
    isSaving,
    error,
    isDirty,
    loadRatio,
    saveRatio,
    clearRatio,
    addMaterial,
    removeMaterial,
    updateMaterial,
    updateCalculationParams,
    calculate,
    canSave,
    canCalculate,
    hasUnsavedChanges,
  } = ratioManagement;

  // 页面回调函数
  const handleSave = async () => {
    try {
      await saveRatio();
      renderProps.showToast({
        title: '保存成功',
        description: '配比数据已保存',
      });
    } catch (error) {
      renderProps.showToast({
        title: '保存失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      });
    }
  };

  const handleCalculate = async () => {
    try {
      await calculate();
      renderProps.showToast({
        title: '计算完成',
        description: '配比计算已完成',
      });
    } catch (error) {
      renderProps.showToast({
        title: '计算失败',
        description: error instanceof Error ? error.message : '计算错误',
        variant: 'destructive',
      });
    }
  };

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <div className='h-screen w-screen bg-muted/20 p-2 flex items-center justify-center'>
        <Card className='p-6'>
          <CardHeader>
            <CardTitle className='text-destructive'>配比数据加载失败</CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-muted-foreground mb-4'>{error}</p>
            <Button onClick={() => loadRatio(taskId)}>重试</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='h-screen w-screen bg-muted/20 p-2 flex flex-col gap-2'>
      {/* 页面头部 */}
      <V1RatioHeader task={task} onOpenModal={openModal} />

      {/* 主要内容区域 */}
      <main className='flex-1 grid grid-cols-[3fr_1fr] gap-2 overflow-hidden'>
        <div className='flex flex-col gap-2 overflow-y-auto custom-thin-scrollbar'>
          <TaskInfoPanel
            task={task}
            calculationParams={calculationParams}
            onUpdateParams={updateCalculationParams}
          />
          <CalculationPanel
            calculationParams={calculationParams}
            calculationResults={calculationResults}
            isCalculating={isCalculating}
            onUpdateParams={updateCalculationParams}
            onCalculate={handleCalculate}
            canCalculate={canCalculate()}
          />
          <ProportioningTable
            materials={materials}
            onOpenModal={openModal}
            onAddMaterial={() =>
              addMaterial({ name: '新材料', type: 'other', amount: 0, unit: 'kg/m³' })
            }
            onRemoveMaterial={removeMaterial}
            onUpdateMaterial={updateMaterial}
            isLoading={isLoading}
          />
        </div>
        <div className='overflow-y-auto custom-thin-scrollbar'>
          <MaterialsSidebar
            materials={materials}
            onAddMaterial={() =>
              addMaterial({ name: '新材料', type: 'other', amount: 0, unit: 'kg/m³' })
            }
            onUpdateMaterial={updateMaterial}
          />
        </div>
      </main>

      {/* 页面底部 */}
      <V1RatioFooter onSave={handleSave} canSave={canSave()} isSaving={isSaving} />
    </div>
  );
}

/**
 * V1配比页面 - 重构版本
 *
 * 使用BaseRatioPage提供的基础架构：
 * 1. 统一的任务加载逻辑
 * 2. 统一的模态框管理
 * 3. 统一的错误处理
 * 4. 统一的生命周期管理
 */
export default function V1RatioPageRefactored() {
  return (
    <BaseRatioPage
      config={V1_CONFIG}
      callbacks={{
        onTaskLoad: task => {
          console.log('V1页面: 任务加载完成', task);
        },
        onTaskLoadError: error => {
          console.error('V1页面: 任务加载失败', error);
        },
        onModalOpen: modalName => {
          console.log('V1页面: 打开模态框', modalName);
        },
        onModalClose: modalName => {
          console.log('V1页面: 关闭模态框', modalName);
        },
        onBeforeUnload: () => {
          // 检查是否有未保存的更改
          // 这里应该从ratioManagement获取状态，但由于作用域限制，暂时返回false
          return false;
        },
        onNavigateAway: targetPath => {
          console.log('V1页面: 导航到', targetPath);
        },
      }}
    >
      {renderProps => <V1RatioContent renderProps={renderProps} />}
    </BaseRatioPage>
  );
}
