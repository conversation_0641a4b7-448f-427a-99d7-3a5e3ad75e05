'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

interface DragPerformanceMetrics {
  dragStartTime: number;
  dragEndTime: number;
  totalDragDuration: number;
  frameDrops: number;
  averageFPS: number;
  memoryUsage: number;
  renderCount: number;
}

interface DragPerformanceConfig {
  enableMonitoring: boolean;
  logToConsole: boolean;
  trackMemory: boolean;
  fpsThreshold: number; // 低于此值认为性能不佳
  maxRenderCount: number; // 最大渲染次数警告阈值
}

const DEFAULT_CONFIG: DragPerformanceConfig = {
  enableMonitoring: true,
  logToConsole: false,
  trackMemory: true,
  fpsThreshold: 50,
  maxRenderCount: 100,
};

export const useDragPerformanceMonitor = (config: Partial<DragPerformanceConfig> = {}) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const [metrics, setMetrics] = useState<DragPerformanceMetrics | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);

  const metricsRef = useRef<Partial<DragPerformanceMetrics>>({});
  const frameCountRef = useRef(0);
  const lastFrameTimeRef = useRef(0);
  const renderCountRef = useRef(0);
  const animationFrameRef = useRef<number>();

  // FPS 监控
  const measureFPS = useCallback(() => {
    if (!isMonitoring) return;

    const now = performance.now();
    frameCountRef.current++;

    if (lastFrameTimeRef.current) {
      const delta = now - lastFrameTimeRef.current;
      if (delta < 16.67) {
        // 低于 60fps
        metricsRef.current.frameDrops = (metricsRef.current.frameDrops || 0) + 1;
      }
    }

    lastFrameTimeRef.current = now;
    animationFrameRef.current = requestAnimationFrame(measureFPS);
  }, [isMonitoring]);

  // 开始监控
  const startMonitoring = useCallback(() => {
    if (!finalConfig.enableMonitoring) return;

    setIsMonitoring(true);
    metricsRef.current = {
      dragStartTime: performance.now(),
      frameDrops: 0,
      renderCount: 0,
    };
    frameCountRef.current = 0;
    renderCountRef.current = 0;
    lastFrameTimeRef.current = 0;

    // 开始 FPS 监控
    measureFPS();

    if (finalConfig.logToConsole) {
      console.log('🚀 拖拽性能监控开始');
    }
  }, [finalConfig.enableMonitoring, finalConfig.logToConsole, measureFPS]);

  // 停止监控
  const stopMonitoring = useCallback(() => {
    if (!isMonitoring) return;

    setIsMonitoring(false);

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    const endTime = performance.now();
    const totalDuration = endTime - (metricsRef.current.dragStartTime || 0);
    const averageFPS = frameCountRef.current / (totalDuration / 1000);

    // 获取内存使用情况
    let memoryUsage = 0;
    if (finalConfig.trackMemory && 'memory' in performance) {
      memoryUsage = (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }

    const finalMetrics: DragPerformanceMetrics = {
      dragStartTime: metricsRef.current.dragStartTime || 0,
      dragEndTime: endTime,
      totalDragDuration: totalDuration,
      frameDrops: metricsRef.current.frameDrops || 0,
      averageFPS,
      memoryUsage,
      renderCount: renderCountRef.current,
    };

    setMetrics(finalMetrics);

    // 性能警告
    if (averageFPS < finalConfig.fpsThreshold) {
      console.warn('⚠️ 拖拽性能警告: FPS 过低', {
        averageFPS: averageFPS.toFixed(2),
        threshold: finalConfig.fpsThreshold,
      });
    }

    if (renderCountRef.current > finalConfig.maxRenderCount) {
      console.warn('⚠️ 拖拽性能警告: 渲染次数过多', {
        renderCount: renderCountRef.current,
        threshold: finalConfig.maxRenderCount,
      });
    }

    if (finalConfig.logToConsole) {
      console.log('📊 拖拽性能报告', finalMetrics);
    }
  }, [isMonitoring, finalConfig]);

  // 记录渲染
  const recordRender = useCallback(() => {
    if (isMonitoring) {
      renderCountRef.current++;
    }
  }, [isMonitoring]);

  // 清理
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  return {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    recordRender,
    // 性能状态
    isPerformanceGood: metrics ? metrics.averageFPS >= finalConfig.fpsThreshold : true,
    hasFrameDrops: metrics ? metrics.frameDrops > 0 : false,
    hasExcessiveRenders: metrics ? metrics.renderCount > finalConfig.maxRenderCount : false,
  };
};

// 拖拽性能优化建议
export const getDragPerformanceRecommendations = (metrics: DragPerformanceMetrics) => {
  const recommendations: string[] = [];

  if (metrics.averageFPS < 30) {
    recommendations.push('考虑减少拖拽时的动画效果');
    recommendations.push('启用 CSS will-change 属性');
    recommendations.push('使用 transform3d 启用硬件加速');
  }

  if (metrics.frameDrops > 10) {
    recommendations.push('优化拖拽事件处理器');
    recommendations.push('减少拖拽过程中的 DOM 操作');
  }

  if (metrics.renderCount > 50) {
    recommendations.push('使用 React.memo 优化组件');
    recommendations.push('优化状态管理，减少不必要的重新渲染');
  }

  if (metrics.memoryUsage > 100) {
    recommendations.push('检查内存泄漏');
    recommendations.push('优化事件监听器的清理');
  }

  return recommendations;
};
