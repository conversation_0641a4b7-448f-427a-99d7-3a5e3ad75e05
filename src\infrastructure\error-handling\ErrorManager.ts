/**
 * 全局错误管理器
 * 统一处理应用中的所有错误，提供错误恢复、报告和监控功能
 */

import { errorBoundaryLogger } from '@/core/lib/logger';
import {
  AppError,
  BaseError,
  ErrorCategory,
  ErrorContext,
  ErrorHandlerConfig,
  ErrorRecoveryConfig,
  ErrorRecoveryStrategy,
  ErrorReport,
  ErrorSeverity,
  ErrorStats,
  ErrorHandler,
  ErrorMetrics,
} from '@/core/types/error';

/**
 * 错误管理器类
 */
export class ErrorManager {
  private static instance: ErrorManager;
  private errorHandlers: Map<string, ErrorHandlerConfig> = new Map();
  private errorHistory: BaseError[] = [];
  private errorStats: ErrorStats = {
    totalErrors: 0,
    errorsByCategory: {} as Record<ErrorCategory, number>,
    errorsBySeverity: {} as Record<ErrorSeverity, number>,
    errorsByCode: {},
    recentErrors: [],
    errorTrends: {
      hourly: new Array(24).fill(0),
      daily: new Array(7).fill(0),
    },
  };
  private maxHistorySize = 1000;
  private reportingEndpoint?: string;
  private onErrorCallbacks: Array<(error: AppError) => void> = [];

  private constructor() {
    this.initializeDefaultHandlers();
    this.setupGlobalErrorHandlers();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ErrorManager {
    if (!ErrorManager.instance) {
      ErrorManager.instance = new ErrorManager();
    }
    return ErrorManager.instance;
  }

  /**
   * 初始化默认错误处理器
   */
  private initializeDefaultHandlers(): void {
    // 网络错误处理器
    this.registerHandler('network', {
      category: ErrorCategory.NETWORK,
      severity: [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL],
      recovery: {
        strategy: ErrorRecoveryStrategy.RETRY,
        maxRetries: 3,
        retryDelay: 1000,
      },
      shouldReport: true,
      shouldNotify: true,
    });

    // API错误处理器
    this.registerHandler('api', {
      category: ErrorCategory.API,
      severity: [ErrorSeverity.MEDIUM, ErrorSeverity.HIGH],
      recovery: {
        strategy: ErrorRecoveryStrategy.FALLBACK,
        maxRetries: 2,
        retryDelay: 500,
      },
      shouldReport: true,
      shouldNotify: false,
    });

    // 组件错误处理器
    this.registerHandler('component', {
      category: ErrorCategory.COMPONENT,
      severity: [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL],
      recovery: {
        strategy: ErrorRecoveryStrategy.RELOAD,
      },
      shouldReport: true,
      shouldNotify: true,
    });

    // 验证错误处理器
    this.registerHandler('validation', {
      category: ErrorCategory.VALIDATION,
      severity: [ErrorSeverity.LOW, ErrorSeverity.MEDIUM],
      recovery: {
        strategy: ErrorRecoveryStrategy.USER_ACTION,
      },
      shouldReport: false,
      shouldNotify: true,
    });
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // 只在浏览器环境中设置全局错误处理器
    if (typeof window === 'undefined') {
      return;
    }

    // 处理未捕获的Promise rejection
    window.addEventListener('unhandledrejection', event => {
      const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));

      this.handleError(
        AppError.system(`未处理的Promise rejection: ${error.message}`, {
          originalError: error,
          promise: event.promise,
        })
      );

      event.preventDefault();
    });

    // 处理全局JavaScript错误
    window.addEventListener('error', event => {
      // 过滤掉DOM操作相关的错误，这些错误通常是无害的
      if (
        event.message &&
        (event.message.includes("Failed to execute 'removeChild' on 'Node'") ||
          event.message.includes('The node to be removed is not a child of this node') ||
          event.message.includes('Cannot add child') ||
          event.message.includes('parent node was not found in the Store'))
      ) {
        console.debug('DOM manipulation error filtered:', event.message);
        return;
      }

      this.handleError(
        AppError.system(`全局JavaScript错误: ${event.message}`, {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error,
        })
      );
    });

    // 处理资源加载错误
    window.addEventListener(
      'error',
      event => {
        if (event.target !== window) {
          const target = event.target as any;
          this.handleError(
            AppError.network(`资源加载失败: ${target.src || target.href}`, {
              tagName: target.tagName,
              src: target.src,
              href: target.href,
            })
          );
        }
      },
      true
    );
  }

  /**
   * 注册错误处理器
   */
  registerHandler(name: string, config: ErrorHandlerConfig): void {
    this.errorHandlers.set(name, config);
  }

  /**
   * 处理错误
   */
  async handleError(error: AppError, context?: ErrorContext): Promise<void> {
    try {
      // 记录错误
      this.recordError(error);

      // 更新统计信息
      this.updateStats(error);

      // 记录日志
      errorBoundaryLogger.error(error.message, error, {
        context,
        errorId: error.id,
        category: error.category,
        severity: error.severity,
      });

      // 查找匹配的处理器
      const handler = this.findHandler(error);

      if (handler) {
        // 执行自定义处理器
        if (handler.customHandler) {
          handler.customHandler(error);
        }

        // 执行恢复策略
        await this.executeRecoveryStrategy(error, handler.recovery, context);

        // 报告错误
        if (handler.shouldReport) {
          await this.reportError(error, context);
        }

        // 通知用户
        if (handler.shouldNotify) {
          this.notifyUser(error);
        }
      }

      // 触发回调
      this.onErrorCallbacks.forEach(callback => {
        try {
          callback(error);
        } catch (callbackError) {
          console.error('Error in error callback:', callbackError);
        }
      });
    } catch (handlingError) {
      console.error('Error while handling error:', handlingError);
      // 防止错误处理器本身出错导致的无限循环
    }
  }

  /**
   * 查找匹配的错误处理器
   */
  private findHandler(error: AppError): ErrorHandlerConfig | undefined {
    for (const [, handler] of this.errorHandlers) {
      if (handler.category === error.category && handler.severity.includes(error.severity)) {
        return handler;
      }
    }
    return undefined;
  }

  /**
   * 执行恢复策略
   */
  private async executeRecoveryStrategy(
    error: AppError,
    recovery: ErrorRecoveryConfig,
    context?: ErrorContext
  ): Promise<void> {
    switch (recovery.strategy) {
      case ErrorRecoveryStrategy.RETRY:
        await this.executeRetry(error, recovery);
        break;

      case ErrorRecoveryStrategy.FALLBACK:
        this.executeFallback(error, recovery);
        break;

      case ErrorRecoveryStrategy.RELOAD:
        this.executeReload();
        break;

      case ErrorRecoveryStrategy.REDIRECT:
        this.executeRedirect(recovery.redirectUrl);
        break;

      case ErrorRecoveryStrategy.USER_ACTION:
        // 用户操作策略通常由UI组件处理
        break;

      case ErrorRecoveryStrategy.IGNORE:
        // 忽略错误，不执行任何操作
        break;
    }

    if (recovery.onRecovery) {
      recovery.onRecovery(error);
    }
  }

  /**
   * 执行重试策略
   */
  private async executeRetry(error: AppError, recovery: ErrorRecoveryConfig): Promise<void> {
    const maxRetries = recovery.maxRetries || 3;
    const retryDelay = recovery.retryDelay || 1000;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // 等待重试延迟
        if (attempt > 1) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        }

        // 这里需要根据具体的错误类型执行重试逻辑
        // 由于这是通用的错误管理器，具体的重试逻辑应该由调用方提供
        errorBoundaryLogger.info(`重试操作 ${attempt}/${maxRetries}`, {
          errorId: error.id,
          attempt,
        });

        // 重试成功，跳出循环
        break;
      } catch (retryError) {
        if (attempt === maxRetries) {
          // 所有重试都失败了
          errorBoundaryLogger.error(`重试失败，已达到最大重试次数`, retryError as Error, {
            errorId: error.id,
            maxRetries,
          });
        }
      }
    }
  }

  /**
   * 执行回退策略
   */
  private executeFallback(error: AppError, recovery: ErrorRecoveryConfig): void {
    if (recovery.fallbackData) {
      // 使用回退数据
      errorBoundaryLogger.info('使用回退数据', {
        errorId: error.id,
        fallbackData: recovery.fallbackData,
      });
    }
  }

  /**
   * 执行重新加载
   */
  private executeReload(): void {
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  }

  /**
   * 执行重定向
   */
  private executeRedirect(url?: string): void {
    if (typeof window !== 'undefined' && url) {
      window.location.href = url;
    }
  }

  /**
   * 记录错误
   */
  private recordError(error: AppError): void {
    this.errorHistory.push(error.toJSON());

    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(error: AppError): void {
    this.errorStats.totalErrors++;

    // 按类别统计
    this.errorStats.errorsByCategory[error.category] =
      (this.errorStats.errorsByCategory[error.category] || 0) + 1;

    // 按严重程度统计
    this.errorStats.errorsBySeverity[error.severity] =
      (this.errorStats.errorsBySeverity[error.severity] || 0) + 1;

    // 按错误代码统计
    this.errorStats.errorsByCode[error.code] = (this.errorStats.errorsByCode[error.code] || 0) + 1;

    // 更新最近错误
    this.errorStats.recentErrors.unshift(error.toJSON());
    if (this.errorStats.recentErrors.length > 50) {
      this.errorStats.recentErrors = this.errorStats.recentErrors.slice(0, 50);
    }

    // 更新趋势数据
    const now = new Date();
    const hourIndex = now.getHours();
    const dayIndex = now.getDay();

    if (this.errorStats.errorTrends.hourly[hourIndex] !== undefined) {
      this.errorStats.errorTrends.hourly[hourIndex]++;
    }
    if (this.errorStats.errorTrends.daily[dayIndex] !== undefined) {
      this.errorStats.errorTrends.daily[dayIndex]++;
    }
  }

  /**
   * 报告错误
   */
  private async reportError(error: AppError, context?: ErrorContext): Promise<void> {
    if (!this.reportingEndpoint) {
      return;
    }

    try {
      const report: ErrorReport = {
        error: error.toJSON(),
        context: context || {},
        browserInfo: {
          userAgent: navigator.userAgent,
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight,
          },
          language: navigator.language,
          cookieEnabled: navigator.cookieEnabled,
        },
      };

      // 添加性能信息
      if ('memory' in performance) {
        report.performanceInfo = {
          memory: (performance as any).memory,
        };
      }

      await fetch(this.reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(report),
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  /**
   * 通知用户
   */
  private notifyUser(error: AppError): void {
    // 这里可以集成toast通知、模态框等用户通知机制
    // 暂时使用console.warn作为占位符
    console.warn('User notification:', error.message);
  }

  /**
   * 添加错误回调
   */
  onError(callback: (error: AppError) => void): () => void {
    this.onErrorCallbacks.push(callback);

    // 返回取消订阅函数
    return () => {
      const index = this.onErrorCallbacks.indexOf(callback);
      if (index > -1) {
        this.onErrorCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * 获取错误统计
   */
  getStats(): ErrorStats {
    return { ...this.errorStats };
  }

  /**
   * 获取错误历史
   */
  getHistory(limit?: number): BaseError[] {
    return limit ? this.errorHistory.slice(-limit) : [...this.errorHistory];
  }

  /**
   * 清除错误历史
   */
  clearHistory(): void {
    this.errorHistory = [];
    this.errorStats = {
      totalErrors: 0,
      errorsByCategory: {} as Record<ErrorCategory, number>,
      errorsBySeverity: {} as Record<ErrorSeverity, number>,
      errorsByCode: {},
      recentErrors: [],
      errorTrends: {
        hourly: new Array(24).fill(0),
        daily: new Array(7).fill(0),
      },
    };
  }

  /**
   * 设置错误报告端点
   */
  setReportingEndpoint(endpoint: string): void {
    this.reportingEndpoint = endpoint;
  }
}

// 导出单例实例
export const errorManager = ErrorManager.getInstance();
