/**
 * 可调度车辆卡片组件
 * 重构后的车辆卡片，分离了拖拽逻辑和UI渲染
 */

import React from 'react';
import { cn } from '@/core/lib/utils';
import { useVehicleCardDrag } from '@/features/vehicle-dispatch/hooks/useVehicleCardDrag';
import { InTaskVehicleCard } from '@/features/task-management/components/in-task-vehicle-card';
import type {
  Vehicle,
  VehicleDisplayMode,
  InTaskVehicleCardStyle,
  TaskListDensityMode,
} from '@/core/types';

interface DispatchableVehicleCardProps {
  vehicle: Vehicle;
  index: number;
  listType: 'pending' | 'returned';
  globalDispatchActive: boolean;
  displayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  density: Exclude<TaskListDensityMode, '' | 'card'>;
  onContextMenu: (event: React.MouseEvent, vehicle: Vehicle) => void;
  onVisualMove: (draggedId: string, hoverId: string, statusList: 'pending' | 'returned') => void;
  onCommitReorder: (statusList: 'pending' | 'returned') => void;
}

/**
 * 可调度车辆卡片组件
 */
export const DispatchableVehicleCard = React.memo<DispatchableVehicleCardProps>(
  ({
    vehicle,
    index,
    listType,
    globalDispatchActive,
    displayMode,
    inTaskVehicleCardStyles,
    density,
    onContextMenu,
    onVisualMove,
    onCommitReorder,
  }) => {
    // 使用拖拽Hook
    const { ref, isDragging } = useVehicleCardDrag({
      vehicle,
      index,
      listType,
      globalDispatchActive,
      onVisualMove,
      onCommitReorder,
    });

    return (
      <div
        ref={ref}
        className={cn(isDragging && 'opacity-40', 'relative z-10')}
        onContextMenu={e => {
          e.preventDefault();
          onContextMenu(e, vehicle);
        }}
      >
        <InTaskVehicleCard
          vehicle={vehicle}
          vehicleDisplayMode={displayMode}
          inTaskVehicleCardStyles={inTaskVehicleCardStyles}
          density={density}
          onOpenContextMenu={e => onContextMenu(e, vehicle)}
          isDragging={isDragging}
          // 调度面板视图的特殊配置
          isDispatchPanelView={true}
          listeners={undefined}
          attributes={undefined}
        />
      </div>
    );
  }
);

DispatchableVehicleCard.displayName = 'DispatchableVehicleCard';
