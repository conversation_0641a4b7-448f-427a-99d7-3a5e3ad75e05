/**
 * 任务卡片性能监控工具
 * 专门监控选中状态导致的性能问题
 */

interface PerformanceMetrics {
  selectionTime: number;
  renderTime: number;
  memoryUsage: number;
  frameDrops: number;
  timestamp: number;
}

interface CardPerformanceData {
  taskId: string;
  selectionCount: number;
  averageSelectionTime: number;
  totalRenderTime: number;
  lastSelectionTime: number;
  performanceScore: number;
}

class TaskCardPerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private cardData: Map<string, CardPerformanceData> = new Map();
  private observer: PerformanceObserver | null = null;
  private isMonitoring = false;
  private frameDropCounter = 0;
  private lastFrameTime = 0;

  constructor() {
    this.initializePerformanceObserver();
  }

  /**
   * 初始化性能观察器
   */
  private initializePerformanceObserver() {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    try {
      this.observer = new PerformanceObserver(list => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.entryType === 'measure' && entry.name.startsWith('task-card-selection')) {
            this.recordSelectionMetric(entry);
          }
        });
      });

      this.observer.observe({ entryTypes: ['measure'] });
    } catch (error) {
      console.warn('Performance monitoring not available:', error);
    }
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.startFrameDropMonitoring();
    console.log('🔍 任务卡片性能监控已启动');
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.isMonitoring = false;
    this.stopFrameDropMonitoring();
    console.log('⏹️ 任务卡片性能监控已停止');
  }

  /**
   * 记录卡片选中开始时间
   */
  markSelectionStart(taskId: string) {
    if (!this.isMonitoring) return;

    performance.mark(`task-card-selection-start-${taskId}`);
  }

  /**
   * 记录卡片选中结束时间并计算性能指标
   */
  markSelectionEnd(taskId: string) {
    if (!this.isMonitoring) return;

    const endMark = `task-card-selection-end-${taskId}`;
    const startMark = `task-card-selection-start-${taskId}`;
    const measureName = `task-card-selection-${taskId}-${Date.now()}`;

    performance.mark(endMark);

    try {
      performance.measure(measureName, startMark, endMark);

      // 清理标记
      performance.clearMarks(startMark);
      performance.clearMarks(endMark);
    } catch (error) {
      console.warn('Performance measurement failed:', error);
    }
  }

  /**
   * 记录选中性能指标
   */
  private recordSelectionMetric(entry: PerformanceEntry) {
    const taskId = this.extractTaskIdFromMeasureName(entry.name);
    if (!taskId) return;

    const selectionTime = entry.duration;
    const currentTime = performance.now();

    // 记录全局指标
    const metric: PerformanceMetrics = {
      selectionTime,
      renderTime: this.estimateRenderTime(),
      memoryUsage: this.getMemoryUsage(),
      frameDrops: this.frameDropCounter,
      timestamp: currentTime,
    };

    this.metrics.push(metric);

    // 只保留最近100条记录
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }

    // 更新卡片特定数据
    this.updateCardData(taskId, selectionTime);

    // 检查性能问题
    this.checkPerformanceIssues(taskId, selectionTime);
  }

  /**
   * 从测量名称中提取任务ID
   */
  private extractTaskIdFromMeasureName(name: string): string | null {
    const match = name.match(/task-card-selection-(.+)-\d+$/);
    return match && match[1] ? match[1] : null;
  }

  /**
   * 估算渲染时间
   */
  private estimateRenderTime(): number {
    // 简化的渲染时间估算
    const now = performance.now();
    const renderTime = now - this.lastFrameTime;
    this.lastFrameTime = now;
    return renderTime;
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    return 0;
  }

  /**
   * 更新卡片性能数据
   */
  private updateCardData(taskId: string, selectionTime: number) {
    const existing = this.cardData.get(taskId) || {
      taskId,
      selectionCount: 0,
      averageSelectionTime: 0,
      totalRenderTime: 0,
      lastSelectionTime: 0,
      performanceScore: 100,
    };

    existing.selectionCount++;
    existing.lastSelectionTime = selectionTime;
    existing.totalRenderTime += selectionTime;
    existing.averageSelectionTime = existing.totalRenderTime / existing.selectionCount;

    // 计算性能评分 (0-100)
    existing.performanceScore = this.calculatePerformanceScore(existing.averageSelectionTime);

    this.cardData.set(taskId, existing);
  }

  /**
   * 计算性能评分
   */
  private calculatePerformanceScore(averageTime: number): number {
    // 基于平均选中时间计算评分
    if (averageTime <= 16) return 100; // 60fps
    if (averageTime <= 33) return 80; // 30fps
    if (averageTime <= 50) return 60; // 20fps
    if (averageTime <= 100) return 40; // 10fps
    return 20; // 低于10fps
  }

  /**
   * 检查性能问题
   */
  private checkPerformanceIssues(taskId: string, selectionTime: number) {
    const cardData = this.cardData.get(taskId);
    if (!cardData) return;

    // 选中时间过长警告
    if (selectionTime > 50) {
      console.warn(`⚠️ 任务卡片 ${taskId} 选中时间过长: ${selectionTime.toFixed(2)}ms`);
    }

    // 平均性能下降警告
    if (cardData.averageSelectionTime > 33 && cardData.selectionCount > 5) {
      console.warn(
        `⚠️ 任务卡片 ${taskId} 平均选中性能下降: ${cardData.averageSelectionTime.toFixed(2)}ms`
      );
    }

    // 性能评分过低警告
    if (cardData.performanceScore < 60) {
      console.warn(`⚠️ 任务卡片 ${taskId} 性能评分过低: ${cardData.performanceScore}`);
    }
  }

  /**
   * 开始帧率监控 - frameDropMonitoring
   */
  private startFrameDropMonitoring() {
    let lastTime = performance.now();
    let frameCount = 0;

    const checkFrameRate = () => {
      if (!this.isMonitoring) return;

      const currentTime = performance.now();
      const deltaTime = currentTime - lastTime;

      frameCount++;

      // 每秒检查一次帧率
      if (deltaTime >= 1000) {
        const fps = frameCount / (deltaTime / 1000);

        if (fps < 30) {
          this.frameDropCounter++;
        }

        frameCount = 0;
        lastTime = currentTime;
      }

      requestAnimationFrame(checkFrameRate);
    };

    requestAnimationFrame(checkFrameRate);
  }

  /**
   * 停止帧率监控
   */
  private stopFrameDropMonitoring() {
    // 帧率监控会在下次 requestAnimationFrame 时自动停止
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const recentMetrics = this.metrics.slice(-20);
    const averageSelectionTime =
      recentMetrics.length > 0
        ? recentMetrics.reduce((sum, m) => sum + m.selectionTime, 0) / recentMetrics.length
        : 0;

    const worstPerformingCards = Array.from(this.cardData.values())
      .sort((a, b) => a.performanceScore - b.performanceScore)
      .slice(0, 5);

    const bestPerformingCards = Array.from(this.cardData.values())
      .sort((a, b) => b.performanceScore - a.performanceScore)
      .slice(0, 5);

    return {
      summary: {
        totalCards: this.cardData.size,
        averageSelectionTime: averageSelectionTime.toFixed(2) + 'ms',
        frameDrops: this.frameDropCounter,
        memoryUsage: this.getMemoryUsage().toFixed(2) + 'MB',
        overallScore: this.calculateOverallScore(),
      },
      worstPerformingCards: worstPerformingCards.map(card => ({
        taskId: card.taskId,
        score: card.performanceScore,
        averageTime: card.averageSelectionTime.toFixed(2) + 'ms',
        selectionCount: card.selectionCount,
      })),
      bestPerformingCards: bestPerformingCards.map(card => ({
        taskId: card.taskId,
        score: card.performanceScore,
        averageTime: card.averageSelectionTime.toFixed(2) + 'ms',
        selectionCount: card.selectionCount,
      })),
      recommendations: this.generateRecommendations(),
    };
  }

  /**
   * 计算整体性能评分
   */
  private calculateOverallScore(): number {
    if (this.cardData.size === 0) return 100;

    const totalScore = Array.from(this.cardData.values()).reduce(
      (sum, card) => sum + card.performanceScore,
      0
    );

    return Math.round(totalScore / this.cardData.size);
  }

  /**
   * 生成性能优化建议
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const overallScore = this.calculateOverallScore();

    if (overallScore < 60) {
      recommendations.push('整体性能较低，建议启用性能优化模式');
    }

    if (this.frameDropCounter > 10) {
      recommendations.push('检测到频繁掉帧，建议减少动画效果');
    }

    const slowCards = Array.from(this.cardData.values()).filter(
      card => card.averageSelectionTime > 50
    );

    if (slowCards.length > 0) {
      recommendations.push(`${slowCards.length} 个卡片选中响应较慢，建议检查渲染逻辑`);
    }

    if (this.getMemoryUsage() > 100) {
      recommendations.push('内存使用较高，建议启用虚拟化渲染');
    }

    return recommendations;
  }

  /**
   * 清理性能数据
   */
  clearData() {
    this.metrics = [];
    this.cardData.clear();
    this.frameDropCounter = 0;
    console.log('🧹 性能监控数据已清理');
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.stopMonitoring();
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    this.clearData();
  }
}

// 单例实例
export const taskCardPerformanceMonitor = new TaskCardPerformanceMonitor();

// 便捷的钩子函数
export const useTaskCardPerformanceMonitor = () => {
  return {
    startMonitoring: () => taskCardPerformanceMonitor.startMonitoring(),
    stopMonitoring: () => taskCardPerformanceMonitor.stopMonitoring(),
    markSelectionStart: (taskId: string) => taskCardPerformanceMonitor.markSelectionStart(taskId),
    markSelectionEnd: (taskId: string) => taskCardPerformanceMonitor.markSelectionEnd(taskId),
    getReport: () => taskCardPerformanceMonitor.getPerformanceReport(),
    clearData: () => taskCardPerformanceMonitor.clearData(),
  };
};

export default TaskCardPerformanceMonitor;
