/**
 * 配比页面专用Mock数据
 * 统一管理所有配比相关的模拟数据
 */

import {
  type AdjustmentMaterial,
  type DraggedMaterial,
  type LegacyRatioMaterial,
  type MaterialDensity,
  type MaterialName,
  type MaterialSpec,
  type MixingStation,
  type MortarCoefficient,
  type RatioCalculationParams,
  type RatioHistoryEntry,
  type RatioSelectionRecord,
  type Silo,
  type SiloMapping,
  type Specification,
  type StorageLocation,
  type TableRowData,
  type UserPermission,
  ExposureClass,
  FinishingRequirement,
  PlacementMethod,
} from '@/core/types/ratio';

// ==================== 基础材料数据 ====================

export const mockMaterialNames: MaterialName[] = [
  { id: 'mat_name_1', name: '饮用水', order: 1, type: 'Water' },
  { id: 'mat_name_2', name: 'P.O 42.5', order: 2, type: 'Powder' },
  { id: 'mat_name_3', name: '机制砂', order: 3, type: 'Aggregate' },
  { id: 'mat_name_4', name: '建业', order: 4, type: 'Admixture' },
];

export const mockSpecifications: Specification[] = [
  { id: 'spec1', name: 'P.O 42.5' },
  { id: 'spec2', name: '机制砂' },
  { id: 'spec3', name: '1-2碎石' },
  { id: 'spec4', name: '饮用水' },
  { id: 'spec5', name: '建业减水剂' },
];

export const mockStorageLocations: StorageLocation[] = [
  { id: 'storage1', binId: 'bin1', binName: '1#水泥仓', order: 1 },
  { id: 'storage2', binId: 'bin2', binName: '水仓', order: 2 },
  { id: 'storage3', binId: 'bin3', binName: '砂仓1', order: 3 },
  { id: 'storage4', binId: 'bin4', binName: '石仓1', order: 4 },
  { id: 'storage5', binId: 'bin5', binName: '外加剂仓', order: 5 },
];

export const mockAdjustmentMaterials: AdjustmentMaterial[] = [
  { id: 'water', name: '水' },
  { id: 'silica_fume', name: '硅灰' },
  { id: 'ultra_fine_sand', name: '超细砂' },
  { id: 'expansion_agent', name: '膨胀剂' },
  { id: 'early_strength_agent', name: '早强剂' },
  { id: 'antifreeze', name: '防冻剂' },
  { id: 'admixture', name: '外加剂' },
  { id: 'slag_powder', name: '矿渣粉' },
  { id: 'ultra_fine_powder', name: '超细粉' },
  { id: 'sand', name: '沙子' },
  { id: 'gravel', name: '石子' },
  { id: 'chips', name: '碎屑' },
  { id: 'tailings', name: '尾矿石' },
  { id: 'gangue', name: '煤矸石' },
  { id: 'cement', name: '水泥' },
  { id: 'mineral_powder', name: '矿粉' },
];

export const mockMortarCoefficients: MortarCoefficient[] = mockAdjustmentMaterials.map(mat => ({
  id: mat.id,
  name: mat.name,
  coefficient: 1.0, // Default coefficient
}));

export const mockMaterialDensities: MaterialDensity[] = [
  { id: 'water', name: '水', density: 1000 },
  { id: 'cement', name: '水泥', density: 3100 },
  { id: 'flyAsh', name: '粉煤灰', density: 2200 },
  { id: 'mineralPowder', name: '矿粉', density: 2900 },
  { id: 'sand', name: '砂子', density: 2650 },
  { id: 'gravel', name: '石子', density: 2700 },
  { id: 'admixture', name: '外加剂', density: 1100 },
  { id: 'silica_fume', name: '硅灰', density: 2200 },
  { id: 'ultra_fine_sand', name: '超细砂', density: 2600 },
  { id: 'expansion_agent', name: '膨胀剂', density: 2800 },
];

// ==================== 配比材料数据 ====================

export const initialTableData: LegacyRatioMaterial[] = [
  {
    id: '1',
    materialType: '水泥',
    spec: 'P.O 42.5',
    theoryAmount: 372,
    waterRatio: 0,
    stoneRatio: 0,
    actualAmount: 372,
    designValue: 372,
    binName: '1#水泥仓',
  },
  {
    id: '2',
    materialType: '水',
    spec: '饮用水',
    theoryAmount: 175,
    waterRatio: 0,
    stoneRatio: 0,
    actualAmount: 175,
    designValue: 175,
    binName: '水仓',
  },
  {
    id: '3',
    materialType: '砂子',
    spec: '机制砂',
    theoryAmount: 593,
    waterRatio: 2.5,
    stoneRatio: 0,
    actualAmount: 593 * 1.025,
    designValue: 593,
    binName: '砂仓1',
  },
  {
    id: '4',
    materialType: '石子',
    spec: '1-2碎石',
    theoryAmount: 1260,
    waterRatio: 1.0,
    stoneRatio: 0,
    actualAmount: 1260 * 1.01,
    designValue: 1260,
    binName: '石仓1',
  },
  {
    id: '5',
    materialType: '外加剂',
    spec: '建业',
    theoryAmount: 3.72,
    waterRatio: 0,
    stoneRatio: 0,
    actualAmount: 3.72,
    designValue: 3.72,
    binName: '外加剂仓',
  },
];

export const mockMortarTableData: TableRowData[] = [
  {
    id: '1',
    materialName: '水泥',
    spec: '无',
    theoryAmount: 110,
    waterContent: 0,
    designValue: 110,
    actualAmount: 110,
  },
  {
    id: '2',
    materialName: '粉煤灰',
    spec: '无',
    theoryAmount: 95,
    waterContent: 0,
    designValue: 95,
    actualAmount: 95,
  },
  {
    id: '3',
    materialName: '超细粉',
    spec: '无',
    theoryAmount: 90,
    waterContent: 0,
    designValue: 90,
    actualAmount: 90,
  },
  {
    id: '4',
    materialName: '砂子',
    spec: '机制砂',
    theoryAmount: 1070,
    waterContent: 0,
    designValue: 1070,
    actualAmount: 1070,
  },
  {
    id: '5',
    materialName: '水',
    spec: '饮用水',
    theoryAmount: 180,
    waterContent: 0,
    designValue: 180,
    actualAmount: 180,
  },
  {
    id: '6',
    materialName: '外加剂',
    spec: '建业',
    theoryAmount: 5.6,
    waterContent: 0,
    designValue: 5.6,
    actualAmount: 5.6,
  },
];

// ==================== 仓储管理数据 ====================

export const mockSilos: Silo[] = [
  {
    id: 'silo-1',
    name: 'P.O 42.5水泥',
    type: 'cement',
    capacity: 200,
    currentAmount: 150,
    unit: 't',
    density: 3100,
    supplier: '海螺水泥',
    location: '1号仓',
    status: 'normal',
    lastUpdated: '2024-01-15 14:30',
    image: '/images/materials/cement.jpg',
    mixingStation: '搅拌站A',
    materialName: 'P.O 42.5',
    storageLocation: '1号水泥仓',
    specification: '42.5R',
  },
  {
    id: 'silo-2',
    name: '粉煤灰',
    type: 'flyash',
    capacity: 150,
    currentAmount: 80,
    unit: 't',
    density: 2200,
    supplier: '华能电厂',
    location: '2号仓',
    status: 'low',
    lastUpdated: '2024-01-15 12:15',
    image: '/images/materials/flyash.jpg',
    mixingStation: '搅拌站A',
    materialName: '粉煤灰',
    storageLocation: '2号粉煤灰仓',
    specification: 'II级',
  },
  {
    id: 'silo-3',
    name: '中砂',
    type: 'sand',
    capacity: 300,
    currentAmount: 250,
    unit: 't',
    density: 2650,
    supplier: '本地砂场',
    location: '3号仓',
    status: 'normal',
    lastUpdated: '2024-01-15 16:45',
    image: '/images/materials/sand.jpg',
    mixingStation: '搅拌站A',
    materialName: '中砂',
    storageLocation: '3号砂仓',
    specification: '2.3-3.0',
  },
  {
    id: 'silo-4',
    name: '碎石',
    type: 'stone',
    capacity: 400,
    currentAmount: 320,
    unit: 't',
    density: 2700,
    supplier: '石料厂',
    location: '4号仓',
    status: 'normal',
    lastUpdated: '2024-01-15 15:20',
    image: '/images/materials/stone.jpg',
    mixingStation: '搅拌站A',
    materialName: '碎石',
    storageLocation: '4号石仓',
    specification: '5-25mm',
  },
  {
    id: 'silo-5',
    name: '生产用水',
    type: 'water',
    capacity: 100,
    currentAmount: 45,
    unit: 't',
    density: 1000,
    supplier: '自来水公司',
    location: '水池',
    status: 'low',
    lastUpdated: '2024-01-15 17:00',
    image: '/images/materials/water.jpg',
    mixingStation: '搅拌站A',
    materialName: '生产用水',
    storageLocation: '水池',
    specification: '自来水',
  },
  {
    id: 'silo-6',
    name: '减水剂',
    type: 'additive',
    capacity: 20,
    currentAmount: 15,
    unit: 't',
    density: 1100,
    supplier: '建业化工',
    location: '外加剂仓',
    status: 'normal',
    lastUpdated: '2024-01-15 13:30',
    image: '/images/materials/additive.jpg',
    mixingStation: '搅拌站A',
    materialName: '减水剂',
    storageLocation: '外加剂仓',
    specification: 'PC-1',
  },
];

export const mockSiloMappings: SiloMapping[] = [
  {
    id: 'mapping1',
    siloType: '水泥',
    materialName: 'P.O 42.5',
    spec: '42.5R',
    storageBin: '1#水泥仓',
    lastEntry: '2024-01-15 14:30',
    enabled: true,
  },
  {
    id: 'mapping2',
    siloType: '粉煤灰',
    materialName: '粉煤灰',
    spec: 'II级',
    storageBin: '2#粉煤灰仓',
    lastEntry: '2024-01-15 12:15',
    enabled: true,
  },
  {
    id: 'mapping3',
    siloType: '砂子',
    materialName: '中砂',
    spec: '2.3-3.0',
    storageBin: '3#砂仓',
    lastEntry: '2024-01-15 16:45',
    enabled: true,
  },
  {
    id: 'mapping4',
    siloType: '石子',
    materialName: '碎石',
    spec: '5-25mm',
    storageBin: '4#石仓',
    lastEntry: '2024-01-15 15:20',
    enabled: true,
  },
  {
    id: 'mapping5',
    siloType: '水',
    materialName: '生产用水',
    spec: '自来水',
    storageBin: '水池',
    lastEntry: '2024-01-15 17:00',
    enabled: true,
  },
  {
    id: 'mapping6',
    siloType: '外加剂',
    materialName: '减水剂',
    spec: 'PC-1',
    storageBin: '外加剂仓',
    lastEntry: '2024-01-15 13:30',
    enabled: true,
  },
];

export const mockMaterialSpecs: MaterialSpec[] = [
  {
    id: 'spec_cement_1',
    materialType: '水泥',
    specName: 'P.O 42.5',
    description: '普通硅酸盐水泥42.5级',
  },
  {
    id: 'spec_sand_1',
    materialType: '砂子',
    specName: '机制砂',
    description: '人工制砂，粒径0.15-4.75mm',
  },
  {
    id: 'spec_stone_1',
    materialType: '石子',
    specName: '1-2碎石',
    description: '碎石，粒径5-25mm',
  },
  {
    id: 'spec_water_1',
    materialType: '水',
    specName: '饮用水',
    description: '符合饮用水标准的清洁水',
  },
  {
    id: 'spec_additive_1',
    materialType: '外加剂',
    specName: '建业减水剂',
    description: '聚羧酸高效减水剂',
  },
];

export const mockMixingStations: MixingStation[] = [
  {
    id: 'station_1',
    name: '搅拌站A',
    location: '厂区东侧',
  },
  {
    id: 'station_2',
    name: '搅拌站B',
    location: '厂区西侧',
  },
  {
    id: 'station_3',
    name: '搅拌站C',
    location: '厂区南侧',
  },
];

// ==================== 拖拽材料数据 ====================

export const mockDraggedMaterials: DraggedMaterial[] = [
  {
    id: 'silo-1',
    name: 'P.O 42.5水泥',
    type: 'cement',
    siloId: 'silo-1',
    capacity: 200,
    currentAmount: 150,
    specification: '42.5R',
  },
  {
    id: 'silo-3',
    name: '粉煤灰',
    type: 'powder',
    siloId: 'silo-3',
    capacity: 150,
    currentAmount: 120,
    specification: 'II级',
  },
  {
    id: 'silo-4',
    name: '中砂',
    type: 'sand',
    siloId: 'silo-4',
    capacity: 300,
    currentAmount: 250,
    specification: '2.3-3.0',
  },
  {
    id: 'silo-6',
    name: '碎石',
    type: 'stone',
    siloId: 'silo-6',
    capacity: 400,
    currentAmount: 320,
    specification: '5-25mm',
  },
  {
    id: 'silo-7',
    name: '生产用水',
    type: 'water',
    siloId: 'silo-7',
    capacity: 100,
    currentAmount: 45,
    specification: '自来水',
  },
  {
    id: 'silo-8',
    name: '减水剂',
    type: 'additive',
    siloId: 'silo-8',
    capacity: 20,
    currentAmount: 15,
    specification: 'PC-1',
  },
];

// ==================== 计算参数数据 ====================

export const defaultCalculationParams: RatioCalculationParams = {
  // 基本参数
  targetStrength: 30,
  slump: 180,
  maxAggregateSize: 25,
  exposureClass: ExposureClass.XC1,

  // 环境参数
  ambientTemperature: 20,
  relativeHumidity: 60,
  cementTemperature: 20,
  aggregateTemperature: 20,

  // 材料选择
  selectedMaterials: ['cement_p_o_42_5', 'potable_water', 'river_sand', 'crushed_stone'],
  cementType: 'cement_p_o_42_5',
  aggregateType: 'crushed_stone',
  waterType: 'potable_water',

  // 配比参数
  waterCementRatio: 0.47,
  sandRatio: 32,
  cementContent: 372,
  waterContent: 175,

  // 外加剂参数
  additiveRatio: 2.0,
  flyashRatio: 15.0,
  mineralPowderRatio: 0,
  silicaFumeRatio: 0,
  antifreezeRatio: 0,
  expansionRatio: 0,

  // 数量参数
  cementAmount: 372,
  waterAmount: 175,
  density: 2400,
  airContent: 4.5,
  strengthGrade: 30,
  ultraFineSandRatio: 0,
  earlyStrengthRatio: 0,

  // 施工参数
  placementMethod: PlacementMethod.PUMP,
  finishingRequirement: FinishingRequirement.SMOOTH,
  cureConditions: {
    method: '自然养护',
    duration: 28,
    temperature: 20,
    humidity: 95,
  },

  // 外加剂用量（具体数值）
  admixtureAmount: 3.72,
  antifreezeAmount: 0,
  flyAshAmount: 0,
  mineralPowderAmount: 0,
  s105PowderAmount: 0,
  expansionAgentAmount: 0,
  earlyStrengthAgentAmount: 0,
  ultraFineSandAmount: 0,
};

// ==================== 历史记录数据 ====================

export function generateMockRatioHistory(taskId: string): RatioHistoryEntry[] {
  return [
    {
      id: 'hist1',
      taskId,
      editor: '李文远',
      timestamp: '2025-04-11 09:56:53',
      price: 450.0,
      materials: {
        cement: 425,
        flyAsh: 0,
        mineralPowder: 0,
        sand: 583,
        stone: 1240,
        water: 175,
        admixture: 4.25,
      },
    },
    {
      id: 'hist2',
      taskId,
      editor: '白彬彬',
      timestamp: '2025-04-09 13:53:09',
      price: 445.5,
      materials: {
        cement: 420,
        flyAsh: 10,
        mineralPowder: 5,
        sand: 590,
        stone: 1230,
        water: 180,
        admixture: 4.2,
      },
    },
    {
      id: 'hist3',
      taskId,
      editor: '张工',
      timestamp: '2025-04-08 15:22:17',
      price: 440.0,
      materials: {
        cement: 415,
        flyAsh: 15,
        mineralPowder: 10,
        sand: 595,
        stone: 1225,
        water: 185,
        admixture: 4.15,
      },
    },
  ];
}

// ==================== 权限数据 ====================

export const mockUserPermissions: UserPermission[] = [
  {
    id: 'user1',
    username: '李文远',
    permissions: {
      calculation: true,
      design: true,
      send: true,
      tweak: true,
      execute: true,
    },
  },
  {
    id: 'user2',
    username: '白彬彬',
    permissions: {
      calculation: true,
      design: true,
      send: false,
      tweak: true,
      execute: false,
    },
  },
  {
    id: 'user3',
    username: '张工',
    permissions: {
      calculation: true,
      design: false,
      send: false,
      tweak: false,
      execute: false,
    },
  },
];

// ==================== 选项数据 ====================

export const taskStatusOptions = [
  { value: 'ReadyToProduce', label: '准备生产' },
  { value: 'RatioSet', label: '已设定配比' },
  { value: 'InProgress', label: '正在进行' },
  { value: 'Paused', label: '暂停' },
  { value: 'Completed', label: '已完成' },
  { value: 'Cancelled', label: '已撤销' },
];

export const productTypeOptions = [
  { value: 'All', label: '全部产品' },
  { value: 'Concrete', label: '砼' },
  { value: 'Mortar', label: '砂浆' },
];

export const defaultVolumeOptions = [
  { value: 'Max', label: '最大方数' },
  { value: 'NoOverload', label: '不超载' },
  { value: 'Normal', label: '正常' },
];

export const waterDistributionOptions = ['1:1', '2:8', '3:7', '4:6', '1:9'];
export const otherDistributionOptions = ['1:1:1', '1:1', '2:8', '3:7', '4:6', '1:9'];

export const materialHeaders = [
  '水',
  '水泥',
  '粉煤灰',
  '矿粉',
  'S105',
  '膨胀剂',
  '早强剂',
  '砂子',
  '石子',
  '外加剂',
  '防冻剂',
  '超细砂',
];

export const enabledDistributionMaterials = ['水', '砂子', '石子', '外加剂'];

// ==================== 配比选择数据 ====================

export function generateMockRatioSelectionRecords(): RatioSelectionRecord[] {
  return [
    {
      id: 'ratio-001',
      ratioNumber: 'C30-001',
      strengthGrade: 'C30',
      slump: 180,
      materials: {
        cement: 425,
        sand: 583,
        stone: 1240,
        water: 175,
        admixture: 4.25,
        flyAsh: 0,
        mineralPowder: 0,
      },
      parameters: {
        waterRatio: 0.41,
        sandRatio: 32,
        density: 2427,
      },
      createTime: '2024-12-15 09:30:00',
      creator: '张工程师',
      status: 'active',
      usageCount: 156,
      description: 'C30标准配比，适用于一般结构工程',
      tags: ['标准', '经济'],
      approver: '李总工',
      approveTime: '2024-12-15 14:20:00',
    },
    {
      id: 'ratio-002',
      ratioNumber: 'C35-002',
      strengthGrade: 'C35',
      slump: 160,
      materials: {
        cement: 450,
        sand: 570,
        stone: 1220,
        water: 170,
        admixture: 4.5,
        flyAsh: 50,
        mineralPowder: 0,
      },
      parameters: {
        waterRatio: 0.38,
        sandRatio: 31,
        density: 2460,
      },
      createTime: '2024-12-10 14:15:00',
      creator: '王技术员',
      status: 'active',
      usageCount: 89,
      description: 'C35高强配比，含粉煤灰，适用于高层建筑',
      tags: ['高强', '耐久'],
      approver: '李总工',
      approveTime: '2024-12-10 16:45:00',
    },
    {
      id: 'ratio-003',
      ratioNumber: 'C25-003',
      strengthGrade: 'C25',
      slump: 200,
      materials: {
        cement: 380,
        sand: 650,
        stone: 1200,
        water: 180,
        admixture: 3.8,
        flyAsh: 60,
        mineralPowder: 0,
      },
      parameters: {
        waterRatio: 0.47,
        sandRatio: 35,
        density: 2374,
      },
      createTime: '2024-12-08 11:20:00',
      creator: '李工程师',
      status: 'active',
      usageCount: 234,
      description: 'C25经济配比，工作性良好，适用于民用建筑',
      tags: ['经济', '工作性好'],
      approver: '张总工',
      approveTime: '2024-12-08 15:30:00',
    },
    {
      id: 'ratio-004',
      ratioNumber: 'C40-004',
      strengthGrade: 'C40',
      slump: 140,
      materials: {
        cement: 480,
        sand: 550,
        stone: 1200,
        water: 165,
        admixture: 5.2,
        flyAsh: 80,
        mineralPowder: 40,
      },
      parameters: {
        waterRatio: 0.34,
        sandRatio: 29,
        density: 2520,
      },
      createTime: '2024-12-05 16:45:00',
      creator: '赵高工',
      status: 'active',
      usageCount: 67,
      description: 'C40超高强配比，双掺技术，适用于重要结构',
      tags: ['超高强', '双掺', '重要结构'],
      approver: '李总工',
      approveTime: '2024-12-06 09:15:00',
    },
    {
      id: 'ratio-005',
      ratioNumber: 'C30-005',
      strengthGrade: 'C30',
      slump: 160,
      materials: {
        cement: 400,
        sand: 590,
        stone: 1250,
        water: 172,
        admixture: 4.0,
        flyAsh: 100,
        mineralPowder: 50,
      },
      parameters: {
        waterRatio: 0.43,
        sandRatio: 32,
        density: 2462,
      },
      createTime: '2024-12-03 13:10:00',
      creator: '孙工程师',
      status: 'active',
      usageCount: 123,
      description: 'C30绿色配比，大掺量粉煤灰，环保经济',
      tags: ['绿色', '环保', '大掺量'],
      approver: '王总工',
      approveTime: '2024-12-03 17:25:00',
    },
    {
      id: 'ratio-006',
      ratioNumber: 'C20-006',
      strengthGrade: 'C20',
      slump: 220,
      materials: {
        cement: 320,
        sand: 680,
        stone: 1180,
        water: 185,
        admixture: 3.2,
        flyAsh: 80,
        mineralPowder: 0,
      },
      parameters: {
        waterRatio: 0.58,
        sandRatio: 37,
        density: 2268,
      },
      createTime: '2024-11-28 10:30:00',
      creator: '陈技术员',
      status: 'active',
      usageCount: 178,
      description: 'C20基础配比，流动性好，适用于基础工程',
      tags: ['基础', '流动性好'],
      approver: '张总工',
      approveTime: '2024-11-28 14:50:00',
    },
  ];
}
