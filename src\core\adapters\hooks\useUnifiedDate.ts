/**
 * 统一日期处理 Hook
 * 提供业务代码使用的统一日期处理接口，隐藏底层库差异
 */

import { useMemo } from 'react';
import { AdapterManager, AdapterType, DateLibrary } from '../AdapterManager';
import { DateAdapter, DateUnit, UnifiedDate } from '../DateAdapter';

/**
 * 日期操作方法接口
 */
export interface UnifiedDateMethods {
  // 创建日期
  create(input?: string | Date | number): UnifiedDate;
  now(): UnifiedDate;
  today(): UnifiedDate;

  // 格式化
  format(date: Date | string, pattern: string): string;
  formatDistance(date: Date | string, baseDate?: Date | string): string;
  formatRelative(date: Date | string, baseDate?: Date | string): string;

  // 计算
  add(date: Date | string, amount: number, unit: DateUnit): UnifiedDate;
  subtract(date: Date | string, amount: number, unit: DateUnit): UnifiedDate;
  diff(dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit): number;

  // 比较
  isSame(dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit): boolean;
  isBefore(dateLeft: Date | string, dateRight: Date | string): boolean;
  isAfter(dateLeft: Date | string, dateRight: Date | string): boolean;
  isValid(date: any): boolean;

  // 解析
  parse(dateString: string, format: string): UnifiedDate;
  parseISO(dateString: string): UnifiedDate;

  // 开始/结束
  startOf(date: Date | string, unit: DateUnit): UnifiedDate;
  endOf(date: Date | string, unit: DateUnit): UnifiedDate;

  // 获取/设置
  getYear(date: Date | string): number;
  getMonth(date: Date | string): number;
  getDate(date: Date | string): number;
  getHour(date: Date | string): number;
  getMinute(date: Date | string): number;
  getSecond(date: Date | string): number;

  setYear(date: Date | string, year: number): UnifiedDate;
  setMonth(date: Date | string, month: number): UnifiedDate;
  setDate(date: Date | string, day: number): UnifiedDate;
  setHour(date: Date | string, hour: number): UnifiedDate;
  setMinute(date: Date | string, minute: number): UnifiedDate;
  setSecond(date: Date | string, second: number): UnifiedDate;
}

/**
 * 基础统一日期 Hook
 * @param library 指定使用的日期库，如果不指定则使用功能映射
 * @param feature 功能特性，用于自动选择最适合的日期库
 */
export function useUnifiedDate(library?: DateLibrary, feature?: string): UnifiedDateMethods {
  return useMemo(() => {
    const adapterManager = AdapterManager.getInstance();
    const adapter = adapterManager.getAdapter<DateAdapter>(AdapterType.DATE, feature);

    if (!adapter) {
      console.warn('⚠️ 未找到日期适配器，使用默认实现');
      // 返回一个基本的实现作为后备
      return createFallbackDateMethods();
    }

    return {
      create: (input?: string | Date | number) => adapter.create(input),
      now: () => adapter.now(),
      today: () => adapter.today(),

      format: (date: Date | string, pattern: string) => adapter.format(date, pattern),
      formatDistance: (date: Date | string, baseDate?: Date | string) =>
        adapter.formatDistance(date, baseDate),
      formatRelative: (date: Date | string, baseDate?: Date | string) =>
        adapter.formatRelative(date, baseDate),

      add: (date: Date | string, amount: number, unit: DateUnit) => adapter.add(date, amount, unit),
      subtract: (date: Date | string, amount: number, unit: DateUnit) =>
        adapter.subtract(date, amount, unit),
      diff: (dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit) =>
        adapter.diff(dateLeft, dateRight, unit),

      isSame: (dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit) =>
        adapter.isSame(dateLeft, dateRight, unit),
      isBefore: (dateLeft: Date | string, dateRight: Date | string) =>
        adapter.isBefore(dateLeft, dateRight),
      isAfter: (dateLeft: Date | string, dateRight: Date | string) =>
        adapter.isAfter(dateLeft, dateRight),
      isValid: (date: any) => adapter.isValid(date),

      parse: (dateString: string, format: string) => adapter.parse(dateString, format),
      parseISO: (dateString: string) => adapter.parseISO(dateString),

      startOf: (date: Date | string, unit: DateUnit) => adapter.startOf(date, unit),
      endOf: (date: Date | string, unit: DateUnit) => adapter.endOf(date, unit),

      getYear: (date: Date | string) => adapter.getYear(date),
      getMonth: (date: Date | string) => adapter.getMonth(date),
      getDate: (date: Date | string) => adapter.getDate(date),
      getHour: (date: Date | string) => adapter.getHour(date),
      getMinute: (date: Date | string) => adapter.getMinute(date),
      getSecond: (date: Date | string) => adapter.getSecond(date),

      setYear: (date: Date | string, year: number) => adapter.setYear(date, year),
      setMonth: (date: Date | string, month: number) => adapter.setMonth(date, month),
      setDate: (date: Date | string, day: number) => adapter.setDate(date, day),
      setHour: (date: Date | string, hour: number) => adapter.setHour(date, hour),
      setMinute: (date: Date | string, minute: number) => adapter.setMinute(date, minute),
      setSecond: (date: Date | string, second: number) => adapter.setSecond(date, second),
    };
  }, [library, feature]);
}

/**
 * 特定功能的日期 Hook
 */

// 日期时间选择器专用
export function useDateTimePicker() {
  return useUnifiedDate(undefined, 'datetime-picker');
}

// 任务调度专用
export function useTaskScheduling() {
  return useUnifiedDate(undefined, 'task-scheduling');
}

// 倒计时专用
export function useCountdown() {
  return useUnifiedDate(undefined, 'countdown');
}

// 日历专用
export function useCalendar() {
  return useUnifiedDate(undefined, 'calendar');
}

// 时间格式化专用
export function useTimeFormatting() {
  return useUnifiedDate(undefined, 'time-formatting');
}

// 相对时间专用
export function useRelativeTime() {
  return useUnifiedDate(undefined, 'relative-time');
}

/**
 * 后备日期方法实现（使用原生 Date）
 */
function createFallbackDateMethods(): UnifiedDateMethods {
  // 简单的 UnifiedDate 实现
  class FallbackUnifiedDate implements UnifiedDate {
    constructor(private date: Date) {}

    get value(): Date {
      return this.date;
    }
    format(pattern: string): string {
      return this.date.toISOString();
    }
    add(amount: number, unit: DateUnit): UnifiedDate {
      const newDate = new Date(this.date);
      switch (unit) {
        case 'year':
          newDate.setFullYear(newDate.getFullYear() + amount);
          break;
        case 'month':
          newDate.setMonth(newDate.getMonth() + amount);
          break;
        case 'day':
          newDate.setDate(newDate.getDate() + amount);
          break;
        case 'hour':
          newDate.setHours(newDate.getHours() + amount);
          break;
        case 'minute':
          newDate.setMinutes(newDate.getMinutes() + amount);
          break;
        case 'second':
          newDate.setSeconds(newDate.getSeconds() + amount);
          break;
        case 'millisecond':
          newDate.setMilliseconds(newDate.getMilliseconds() + amount);
          break;
      }
      return new FallbackUnifiedDate(newDate);
    }
    subtract(amount: number, unit: DateUnit): UnifiedDate {
      return this.add(-amount, unit);
    }
    isSame(date: UnifiedDate | Date | string, unit?: DateUnit): boolean {
      const other = date instanceof Date ? date : new Date(date.toString());
      return this.date.getTime() === other.getTime();
    }
    isBefore(date: UnifiedDate | Date | string): boolean {
      const other = date instanceof Date ? date : new Date(date.toString());
      return this.date.getTime() < other.getTime();
    }
    isAfter(date: UnifiedDate | Date | string): boolean {
      const other = date instanceof Date ? date : new Date(date.toString());
      return this.date.getTime() > other.getTime();
    }
    isValid(): boolean {
      return !isNaN(this.date.getTime());
    }
    toDate(): Date {
      return this.date;
    }
    toISOString(): string {
      return this.date.toISOString();
    }
    valueOf(): number {
      return this.date.valueOf();
    }
  }

  return {
    create: (input?: string | Date | number) =>
      new FallbackUnifiedDate(new Date(input || Date.now())),
    now: () => new FallbackUnifiedDate(new Date()),
    today: () => {
      const date = new Date();
      date.setHours(0, 0, 0, 0);
      return new FallbackUnifiedDate(date);
    },
    format: (date: Date | string, pattern: string) => new Date(date).toISOString(),
    formatDistance: (date: Date | string, baseDate?: Date | string) => 'fallback distance',
    formatRelative: (date: Date | string, baseDate?: Date | string) => 'fallback relative',
    add: (date: Date | string, amount: number, unit: DateUnit) =>
      new FallbackUnifiedDate(new Date(date)).add(amount, unit),
    subtract: (date: Date | string, amount: number, unit: DateUnit) =>
      new FallbackUnifiedDate(new Date(date)).subtract(amount, unit),
    diff: (dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit) =>
      new Date(dateLeft).getTime() - new Date(dateRight).getTime(),
    isSame: (dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit) =>
      new Date(dateLeft).getTime() === new Date(dateRight).getTime(),
    isBefore: (dateLeft: Date | string, dateRight: Date | string) =>
      new Date(dateLeft).getTime() < new Date(dateRight).getTime(),
    isAfter: (dateLeft: Date | string, dateRight: Date | string) =>
      new Date(dateLeft).getTime() > new Date(dateRight).getTime(),
    isValid: (date: any) => !isNaN(new Date(date).getTime()),
    parse: (dateString: string, format: string) => new FallbackUnifiedDate(new Date(dateString)),
    parseISO: (dateString: string) => new FallbackUnifiedDate(new Date(dateString)),
    startOf: (date: Date | string, unit: DateUnit) => new FallbackUnifiedDate(new Date(date)),
    endOf: (date: Date | string, unit: DateUnit) => new FallbackUnifiedDate(new Date(date)),
    getYear: (date: Date | string) => new Date(date).getFullYear(),
    getMonth: (date: Date | string) => new Date(date).getMonth(),
    getDate: (date: Date | string) => new Date(date).getDate(),
    getHour: (date: Date | string) => new Date(date).getHours(),
    getMinute: (date: Date | string) => new Date(date).getMinutes(),
    getSecond: (date: Date | string) => new Date(date).getSeconds(),
    setYear: (date: Date | string, year: number) => {
      const d = new Date(date);
      d.setFullYear(year);
      return new FallbackUnifiedDate(d);
    },
    setMonth: (date: Date | string, month: number) => {
      const d = new Date(date);
      d.setMonth(month);
      return new FallbackUnifiedDate(d);
    },
    setDate: (date: Date | string, day: number) => {
      const d = new Date(date);
      d.setDate(day);
      return new FallbackUnifiedDate(d);
    },
    setHour: (date: Date | string, hour: number) => {
      const d = new Date(date);
      d.setHours(hour);
      return new FallbackUnifiedDate(d);
    },
    setMinute: (date: Date | string, minute: number) => {
      const d = new Date(date);
      d.setMinutes(minute);
      return new FallbackUnifiedDate(d);
    },
    setSecond: (date: Date | string, second: number) => {
      const d = new Date(date);
      d.setSeconds(second);
      return new FallbackUnifiedDate(d);
    },
  };
}
