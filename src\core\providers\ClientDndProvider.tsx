'use client';

import React, { useEffect, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

interface ClientDndProviderProps {
  children: React.ReactNode;
}

export function ClientDndProvider({ children }: ClientDndProviderProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Always render DndProvider, but handle SSR gracefully
  return (
    <DndProvider backend={HTML5Backend}>{isClient ? children : <div>Loading...</div>}</DndProvider>
  );
}
