# 🔧 API 概述

## 📋 API 设计原则

TMH车辆调度系统的API设计遵循RESTful架构风格，注重一致性、可扩展性和开发者体验。

### 设计原则
- **RESTful**: 遵循REST架构风格
- **一致性**: 统一的命名规范和响应格式
- **版本控制**: 支持API版本管理
- **错误处理**: 标准化的错误响应
- **文档完整**: 详细的API文档和示例

## 🌐 基础信息

### 基础URL
```
开发环境: http://localhost:3001/api
测试环境: https://api-test.tmh.com
生产环境: https://api.tmh.com
```

### API版本
```
当前版本: v1
版本格式: /api/v1/...
```

### 认证方式
```
类型: Bearer Token
头部: Authorization: Bearer <token>
```

## 📊 响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req-123456"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": [
      {
        "field": "email",
        "message": "邮箱格式不正确"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req-123456"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [
      // 数据项
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

## 🔍 HTTP 状态码

### 成功状态码
| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | OK | 请求成功 |
| 201 | Created | 资源创建成功 |
| 204 | No Content | 删除成功，无返回内容 |

### 客户端错误
| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未认证 |
| 403 | Forbidden | 无权限 |
| 404 | Not Found | 资源不存在 |
| 409 | Conflict | 资源冲突 |
| 422 | Unprocessable Entity | 数据验证失败 |

### 服务器错误
| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 500 | Internal Server Error | 服务器内部错误 |
| 502 | Bad Gateway | 网关错误 |
| 503 | Service Unavailable | 服务不可用 |

## 📝 请求规范

### 请求头
```http
Content-Type: application/json
Authorization: Bearer <token>
X-Request-ID: req-123456
X-Client-Version: 1.0.0
```

### 查询参数
```
分页参数:
- page: 页码 (默认: 1)
- limit: 每页数量 (默认: 20, 最大: 100)

排序参数:
- sort: 排序字段
- order: 排序方向 (asc/desc)

筛选参数:
- filter[field]: 筛选条件
- search: 搜索关键词

时间参数:
- start_date: 开始日期 (ISO 8601)
- end_date: 结束日期 (ISO 8601)
```

### 请求体格式
```json
{
  "data": {
    // 请求数据
  },
  "options": {
    // 可选配置
  }
}
```

## 🔐 认证和授权

### 认证流程
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Auth

    Client->>API: 请求登录
    API->>Auth: 验证凭据
    Auth-->>API: 返回Token
    API-->>Client: 返回Token
    Client->>API: 携带Token请求
    API->>Auth: 验证Token
    Auth-->>API: 验证结果
    API-->>Client: 返回数据
```

### Token 格式
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "refresh_token_here",
  "scope": "read write"
}
```

### 权限控制
```json
{
  "user": {
    "id": "user-123",
    "roles": ["dispatcher", "manager"],
    "permissions": [
      "task:read",
      "task:write",
      "vehicle:read",
      "vehicle:dispatch"
    ]
  }
}
```

## 📋 API 模块

### 核心模块
| 模块 | 路径 | 描述 |
|------|------|------|
| 任务管理 | `/api/v1/tasks` | 任务CRUD和管理 |
| 车辆管理 | `/api/v1/vehicles` | 车辆信息和状态 |
| 调度管理 | `/api/v1/dispatch` | 车辆调度操作 |
| 配比管理 | `/api/v1/ratios` | 混凝土配比管理 |
| 用户管理 | `/api/v1/users` | 用户和权限管理 |

### 辅助模块
| 模块 | 路径 | 描述 |
|------|------|------|
| 文件上传 | `/api/v1/upload` | 文件上传服务 |
| 通知服务 | `/api/v1/notifications` | 消息通知 |
| 系统配置 | `/api/v1/config` | 系统配置管理 |
| 日志审计 | `/api/v1/audit` | 操作日志 |

## 🔄 批量操作

### 批量请求格式
```json
{
  "operations": [
    {
      "method": "POST",
      "path": "/tasks",
      "data": { /* 任务数据 */ }
    },
    {
      "method": "PUT",
      "path": "/tasks/123",
      "data": { /* 更新数据 */ }
    }
  ]
}
```

### 批量响应格式
```json
{
  "success": true,
  "results": [
    {
      "success": true,
      "data": { /* 结果数据 */ }
    },
    {
      "success": false,
      "error": { /* 错误信息 */ }
    }
  ]
}
```

## 📊 限流和配额

### 限流规则
```
用户级别:
- 普通用户: 100 请求/分钟
- VIP用户: 500 请求/分钟
- 管理员: 1000 请求/分钟

IP级别:
- 单IP: 1000 请求/小时

API级别:
- 批量操作: 10 请求/分钟
- 文件上传: 50 请求/小时
```

### 限流响应
```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "请求频率超出限制",
    "retryAfter": 60
  }
}
```

## 🔍 错误代码

### 业务错误代码
| 代码 | 含义 | HTTP状态码 |
|------|------|------------|
| VALIDATION_ERROR | 数据验证失败 | 422 |
| RESOURCE_NOT_FOUND | 资源不存在 | 404 |
| DUPLICATE_RESOURCE | 资源重复 | 409 |
| INSUFFICIENT_PERMISSION | 权限不足 | 403 |
| BUSINESS_RULE_VIOLATION | 业务规则违反 | 400 |

### 系统错误代码
| 代码 | 含义 | HTTP状态码 |
|------|------|------------|
| INTERNAL_ERROR | 内部服务器错误 | 500 |
| SERVICE_UNAVAILABLE | 服务不可用 | 503 |
| TIMEOUT_ERROR | 请求超时 | 504 |
| DATABASE_ERROR | 数据库错误 | 500 |

## 📈 性能优化

### 缓存策略
```http
# 响应头
Cache-Control: public, max-age=3600
ETag: "abc123"
Last-Modified: Mon, 15 Jan 2024 10:00:00 GMT

# 条件请求
If-None-Match: "abc123"
If-Modified-Since: Mon, 15 Jan 2024 10:00:00 GMT
```

### 压缩支持
```http
# 请求头
Accept-Encoding: gzip, deflate, br

# 响应头
Content-Encoding: gzip
```

### 字段选择
```
# 只返回指定字段
GET /api/v1/tasks?fields=id,title,status

# 排除指定字段
GET /api/v1/tasks?exclude=metadata,notes
```

## 🧪 测试和调试

### API 测试工具
- **Postman**: 推荐的API测试工具
- **curl**: 命令行测试
- **Insomnia**: 替代的GUI工具

### 调试信息
```json
{
  "debug": {
    "requestId": "req-123456",
    "executionTime": "150ms",
    "queryCount": 3,
    "cacheHit": true
  }
}
```

### 健康检查
```
GET /api/health
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": "72h30m",
  "dependencies": {
    "database": "healthy",
    "redis": "healthy",
    "external_api": "degraded"
  }
}
```

## 📚 SDK 和工具

### JavaScript SDK
```javascript
import { TMHApiClient } from '@tmh/api-client';

const client = new TMHApiClient({
  baseURL: 'https://api.tmh.com',
  apiKey: 'your-api-key'
});

// 使用示例
const tasks = await client.tasks.list({
  page: 1,
  limit: 20,
  status: 'pending'
});
```

### 代码生成工具
- **OpenAPI Generator**: 基于OpenAPI规范生成客户端代码
- **Swagger Codegen**: 多语言客户端生成
- **GraphQL Code Generator**: GraphQL客户端生成

---

**文档版本**: v1.0  
**最后更新**: 2025/7/16  
**API负责人**: TMH后端团队
