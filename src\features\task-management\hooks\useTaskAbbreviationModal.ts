// src/hooks/useTaskAbbreviationModal.ts
'use client';

import { useCallback, useState } from 'react';

import { TaskAbbreviationService } from '@/features/task-management/services/taskAbbreviationService';
import type { Task } from '@/core/types';

// src/hooks/useTaskAbbreviationModal.ts

interface UseTaskAbbreviationModalReturn {
  // Task Abbreviation Modal state
  isTaskAbbreviationModalOpen: boolean;
  selectedTaskForAbbreviation: Task | null;
  openTaskAbbreviationModal: (task: Task) => void;
  closeTaskAbbreviationModal: () => void;
  handleSaveAbbreviation: (taskId: string, abbreviation: string) => Promise<void>;
}

export function useTaskAbbreviationModal(): UseTaskAbbreviationModalReturn {
  const [isTaskAbbreviationModalOpen, setIsTaskAbbreviationModalOpen] = useState(false);
  const [selectedTaskForAbbreviation, setSelectedTaskForAbbreviation] = useState<Task | null>(null);

  const openTaskAbbreviationModal = useCallback((task: Task) => {
    setSelectedTaskForAbbreviation(task);
    setIsTaskAbbreviationModalOpen(true);
  }, []);

  const closeTaskAbbreviationModal = useCallback(() => {
    setIsTaskAbbreviationModalOpen(false);
    setSelectedTaskForAbbreviation(null); // Clear selected task when closing
  }, []);

  const handleSaveAbbreviation = useCallback(async (taskId: string, abbreviation: string) => {
    await TaskAbbreviationService.updateAbbreviation(taskId, abbreviation);
  }, []);

  return {
    isTaskAbbreviationModalOpen,
    selectedTaskForAbbreviation,
    openTaskAbbreviationModal,
    closeTaskAbbreviationModal,
    handleSaveAbbreviation,
  };
}
