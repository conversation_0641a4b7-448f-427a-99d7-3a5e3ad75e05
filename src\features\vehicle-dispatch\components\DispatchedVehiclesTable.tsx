// src/features/vehicle-dispatch/components/DispatchedVehiclesTable.tsx
'use client';

import React, { useRef } from 'react';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';

import { cn } from '@/core/lib/utils';
import type { DispatchedVehicle, DensityStyleValues } from '@/core/types';
import { ZIndexLevels } from '@/core/types/sticky-columns';
import { ColumnDragDropProvider } from '@/shared/components/column-drag-drop-provider';
import { DraggableHeader } from '@/shared/components/draggable-header';
import { useTableScrollState } from '@/shared/hooks/useTableScrollState';

interface DispatchedVehiclesTableProps {
  data: DispatchedVehicle[];
  columns: ColumnDef<DispatchedVehicle>[];
  columnOrder: string[];
  columnVisibility: Record<string, boolean>;
  columnWidths: Record<string, number>;
  enableZebraStriping: boolean;
  densityStyles: DensityStyleValues & { density: 'compact' | 'normal' | 'comfortable' };
  totalTableWidth: number;
  onColumnOrderChange: (newOrder: string[]) => void;
  onColumnVisibilityChange: (visibility: Record<string, boolean>) => void;
  onColumnSizingChange: (sizing: Record<string, number>) => void;
  className?: string;
}

/**
 * 已出厂车辆表格组件
 */
export const DispatchedVehiclesTable: React.FC<DispatchedVehiclesTableProps> = ({
  data,
  columns,
  columnOrder,
  columnVisibility,
  columnWidths,
  enableZebraStriping,
  densityStyles,
  totalTableWidth,
  onColumnOrderChange,
  onColumnVisibilityChange,
  onColumnSizingChange,
  className,
}) => {
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const scrollState = useTableScrollState(tableContainerRef);

  // 创建表格实例
  const table = useReactTable({
    data,
    columns,
    getRowId: row => row.id,
    state: {
      columnSizing: columnWidths,
      columnVisibility,
      columnOrder,
    },
    onColumnSizingChange: updater => {
      const newSizing = typeof updater === 'function' ? updater(columnWidths) : updater;
      onColumnSizingChange(newSizing);
    },
    onColumnVisibilityChange: updater => {
      const newVisibility = typeof updater === 'function' ? updater(columnVisibility) : updater;
      onColumnVisibilityChange(newVisibility);
    },
    onColumnOrderChange: updater => {
      const newOrder = typeof updater === 'function' ? updater(columnOrder) : updater;
      onColumnOrderChange(newOrder);
    },
    getCoreRowModel: getCoreRowModel(),
    columnResizeMode: 'onChange',
    enableColumnResizing: true,
    meta: {
      densityStyles,
    },
  });

  const headerGroups = table.getHeaderGroups();

  return (
    <ColumnDragDropProvider
      tableInstance={table}
      initialColumnOrder={columnOrder}
      onColumnOrderChange={updater => {
        const newOrder = typeof updater === 'function' ? updater(columnOrder) : updater;
        onColumnOrderChange(newOrder);
      }}
      disableDragDrop={false}
    >
      <div
        ref={tableContainerRef}
        className={cn('h-full overflow-auto', 'border border-gray-300', className)}
        style={{
          minHeight: 0,
          flex: '1 1 0%',
          position: 'relative',
        }}
      >
        <table
          style={{
            width: '100%',
            minWidth: Math.max(totalTableWidth, 640), // 10列 * 64px = 640px
            tableLayout: 'fixed',
            borderCollapse: 'collapse',
          }}
          className='text-xs'
        >
          {/* 表头 */}
          <thead
            className='sticky top-0 bg-blue-100 border-b border-gray-300'
            style={{ zIndex: ZIndexLevels.TABLE_THEAD_STICKY }}
          >
            {headerGroups.map(headerGroup => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header, headerIndex) => (
                  <DraggableHeader
                    key={header.id}
                    header={header}
                    index={headerIndex}
                    tableInstance={table}
                    scrollState={scrollState}
                  />
                ))}
              </tr>
            ))}
          </thead>

          {/* 表体 */}
          <tbody>
            {table.getRowModel().rows.map((row, index) => (
              <tr
                key={row.id}
                className={cn(
                  'border-b border-gray-200 hover:bg-gray-10',
                  enableZebraStriping && index % 2 === 1 ? 'bg-blue-50/30' : 'bg-blue-10'
                )}
              >
                {row.getVisibleCells().map(cell => (
                  <td
                    key={cell.id}
                    style={{ width: '64px', minWidth: '64px', maxWidth: '64px' }}
                    className='w-16 px-1 py-1 text-xs border-r border-blue-200 last:border-r-0 overflow-hidden'
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>

        {/* 空状态 */}
        {data.length === 0 && (
          <div className='flex items-center justify-center h-32 text-gray-500'>
            <div className='text-center'>
              <p className='text-sm'>暂无已出厂车辆数据</p>
            </div>
          </div>
        )}
      </div>
    </ColumnDragDropProvider>
  );
};
