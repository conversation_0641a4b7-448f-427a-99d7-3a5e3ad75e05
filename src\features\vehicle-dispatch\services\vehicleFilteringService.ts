// src/services/vehicleFilteringService.ts
import type { Vehicle } from '@/core/types';

/**
 * Filters an array of vehicles to get those with 'pending' status.
 * @param vehicles - The array of all vehicles.
 * @returns An array of vehicles with 'pending' status.
 */
export function getPendingVehicles(vehicles: Vehicle[]): Vehicle[] {
  if (!Array.isArray(vehicles)) return [];
  return vehicles.filter(v => v.status === 'pending');
}

/**
 * Filters an array of vehicles to get those with 'returned' status.
 * @param vehicles - The array of all vehicles.
 * @returns An array of vehicles with 'returned' status.
 */
export function getReturnedVehicles(vehicles: Vehicle[]): Vehicle[] {
  if (!Array.isArray(vehicles)) return [];
  return vehicles.filter(v => v.status === 'returned');
}

/**
 * Filters an array of vehicles to get those with 'outbound' status.
 * @param vehicles - The array of all vehicles.
 * @returns An array of vehicles with 'outbound' status.
 */
export function getOutboundVehicles(vehicles: Vehicle[]): Vehicle[] {
  if (!Array.isArray(vehicles)) return [];
  return vehicles.filter(v => v.status === 'outbound');
}
