// src/hooks/useColumnDragDrop.ts
'use client';

import { useEffect, useRef } from 'react';

import { useDrag, useDrop } from 'react-dnd';

import { ItemTypes } from '@/core/constants/dndItemTypes';
import { useColumnDragDropContext } from '@/shared/components/contexts/ColumnDragDropContext';

// src/hooks/useColumnDragDrop.ts

/**
 * @interface DraggableHeaderItem
 * @description 定义 react-dnd 的可拖拽表头项结构
 */
interface DraggableHeaderItem {
  id: string;
  index: number;
  originalIndex: number;
  type: typeof ItemTypes.TABLE_HEADER;
}

/**
 * @interface ColumnDragDropProps
 * @description useColumnDragDrop 钩子的参数
 */
interface ColumnDragDropProps {
  id: string;
  index: number;
}

/**
 * @interface ColumnDragDropResult
 * @description useColumnDragDrop 钩子的返回值
 */
interface ColumnDragDropResult {
  ref: React.RefObject<HTMLTableCellElement>;
  isDragging: boolean;
  isOver: boolean;
}

/**
 * @function useColumnDragDrop
 * @description 管理表格列的拖放功能的自定义钩子
 * @param {ColumnDragDropProps} props - 钩子的参数
 * @returns {ColumnDragDropResult} 钩子的返回值
 */
export const useColumnDragDrop = ({ id, index }: ColumnDragDropProps): ColumnDragDropResult => {
  const ref = useRef<HTMLTableCellElement>(null);
  // 用于节流的上次移动时间
  const lastMoveTimeRef = useRef<number>(0);
  // 节流间隔（毫秒）
  const throttleInterval = 50;

  // 使用列拖拽上下文
  const {
    isDragging: contextIsDragging,
    draggedColumnId,
    dragOverColumnId,
    setDraggedColumnId,
    setDragOverColumnId,
    setIsDragging,
    isColumnReorderable,
    updateColumnOrder,
    previewColumnOrder,
    setPreviewColumnOrder,
    columnOrder,
  } = useColumnDragDropContext();

  const isReorderable = isColumnReorderable(id);

  // 优化拖动体验，避免频繁更新导致卡顿
  const [{ isDragging }, drag] = useDrag<DraggableHeaderItem, unknown, { isDragging: boolean }>(
    () => ({
      type: ItemTypes.TABLE_HEADER,
      item: () => {
        setDraggedColumnId(id);
        setIsDragging(true);
        return {
          id,
          index,
          originalIndex: index,
          type: ItemTypes.TABLE_HEADER,
        };
      },
      collect: monitor => ({
        isDragging: monitor.isDragging(),
      }),
      canDrag: () => isReorderable,
      end: () => {
        setDraggedColumnId(null);
        setDragOverColumnId(null);
        setIsDragging(false);
        setPreviewColumnOrder(null);
      },
    }),
    [id, index, isReorderable, setDraggedColumnId, setIsDragging, setPreviewColumnOrder]
  );

  const [{ isOver }, drop] = useDrop<DraggableHeaderItem, void, { isOver: boolean }>(
    () => ({
      accept: ItemTypes.TABLE_HEADER,
      collect(monitor) {
        return {
          isOver: monitor.isOver(),
        };
      },
      hover: (item, monitor) => {
        if (!ref.current || !isReorderable) {
          return;
        }

        const dragIndex = item.originalIndex;
        const hoverIndex = index;

        // 不替换自己
        if (dragIndex === hoverIndex) {
          return;
        }

        // 添加/移除悬停样式以指示潜在的放置目标
        if (monitor.isOver({ shallow: true })) {
          ref.current.classList.add('column-drag-over');
          setDragOverColumnId(id);
        } else {
          ref.current.classList.remove('column-drag-over');
          setDragOverColumnId(null);
        }

        // 确定屏幕上的矩形
        const hoverBoundingRect = ref.current.getBoundingClientRect();

        // 获取水平中点
        const hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;

        // 确定鼠标位置
        const clientOffset = monitor.getClientOffset();
        if (!clientOffset) return;

        // 获取左侧像素
        const hoverClientX = clientOffset.x - hoverBoundingRect.left;

        // 只有当鼠标越过项目宽度的一半时才执行移动
        // 向右拖动时，仅当光标在 50% 之后时才移动
        // 向左拖动时，仅当光标在 50% 之前时才移动

        // 向右拖动
        if (dragIndex < hoverIndex && hoverClientX < hoverMiddleX) {
          return;
        }

        // 向左拖动
        if (dragIndex > hoverIndex && hoverClientX > hoverMiddleX) {
          return;
        }

        // 应用节流逻辑
        const now = Date.now();
        if (now - lastMoveTimeRef.current < throttleInterval) {
          return;
        }
        lastMoveTimeRef.current = now;

        // 创建预览列顺序
        const newOrder = [...columnOrder];
        newOrder.splice(dragIndex, 1);
        newOrder.splice(hoverIndex, 0, draggedColumnId ?? '');
        setPreviewColumnOrder(newOrder);

        // 更新拖拽项的索引
        item.originalIndex = hoverIndex;
        item.index = hoverIndex;

        // 优化拖动体验，减少不必要的状态更新
        if (draggedColumnId !== id) {
          setDragOverColumnId(id);
        }
        item.index = hoverIndex;
      },
      drop: item => {
        // 添加动画类并清理任何悬停样式
        const element = ref.current;
        if (element) {
          // 首先移除任何悬停样式
          element.classList.remove('column-drag-over');

          // 添加完成动画
          element.classList.add('column-drop-complete');

          // 动画完成后移除动画类
          setTimeout(() => {
            if (element) {
              element.classList.remove('column-drop-complete');
            }
          }, 200); // 缩短动画持续时间，提升响应速度
        }

        // 确保应用最终的列顺序
        // 只有当拖动索引和悬停索引不同时才更新列顺序
        if (item.originalIndex !== index) {
          const draggedColumnId = columnOrder[item.originalIndex];
          if (draggedColumnId) {
            const newOrder = [...columnOrder];
            newOrder.splice(item.originalIndex, 1);
            newOrder.splice(index, 0, draggedColumnId);
            updateColumnOrder(newOrder);
          }
        }

        // 清理拖拽状态
        setDraggedColumnId(null);
        setDragOverColumnId(null);
        setIsDragging(false);
        setPreviewColumnOrder(null);
      },
    }),
    [
      id,
      index,
      isReorderable,
      columnOrder,
      updateColumnOrder,
      setDragOverColumnId,
      setPreviewColumnOrder,
    ]
  );

  // 清理函数，确保在组件卸载时移除所有样式
  useEffect(() => {
    return () => {
      if (ref.current) {
        ref.current.classList.remove('column-drag-over');
        ref.current.classList.remove('column-drop-complete');
      }
    };
  }, []);

  drag(drop(ref));

  return { ref, isDragging, isOver };
};

// Remove drag and drop logic from the header
// Update the description to reflect the new sorting method
// Remove GripVertical icon and related logic
// Update the UI to include sorting buttons instead of drag handles
// Add sorting buttons
