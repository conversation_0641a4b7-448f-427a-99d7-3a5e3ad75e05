'use client';

import React, { useState } from 'react';
import { X, Save, Tag, AlertCircle } from 'lucide-react';

import { Button } from '@/shared/components/button';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import { Textarea } from '@/shared/components/textarea';
import { Badge } from '@/shared/components/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';

import type { CreateBackupRatioRequest } from '@/core/types/ratio-backup';

interface SaveBackupRatioModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (request: CreateBackupRatioRequest) => Promise<void>;
  defaultName?: string;
  ratioSummary?: {
    targetStrength: string;
    slump: number;
    waterCementRatio: number;
    materialCount: number;
    qualityScore?: number;
  };
}

export function SaveBackupRatioModal({
  isOpen,
  onClose,
  onSave,
  defaultName = '',
  ratioSummary,
}: SaveBackupRatioModalProps) {
  const [formData, setFormData] = useState({
    name: defaultName,
    description: '',
    tags: [] as string[],
  });
  const [newTag, setNewTag] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: defaultName,
      description: '',
      tags: [],
    });
    setNewTag('');
    setErrors({});
    setIsSaving(false);
  };

  // 关闭模态框
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors['name'] = '配比名称不能为空';
    } else if (formData.name.trim().length < 2) {
      newErrors['name'] = '配比名称至少需要2个字符';
    } else if (formData.name.trim().length > 50) {
      newErrors['name'] = '配比名称不能超过50个字符';
    }

    if (formData.description.length > 200) {
      newErrors['description'] = '描述不能超过200个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 添加标签
  const handleAddTag = () => {
    const tag = newTag.trim();
    if (tag && !formData.tags.includes(tag) && formData.tags.length < 5) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag],
      }));
      setNewTag('');
    }
  };

  // 删除标签
  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  // 保存配比
  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      const request: Omit<
        CreateBackupRatioRequest,
        'taskId' | 'materials' | 'calculationParams' | 'calculationResults'
      > = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        tags: formData.tags.length > 0 ? formData.tags : undefined,
      };

      await onSave(request as CreateBackupRatioRequest);
      handleClose();
    } catch (error) {
      console.error('保存备选配比失败:', error);
      setErrors({
        submit: error instanceof Error ? error.message : '保存失败，请重试',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // 处理回车键
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='sm:max-w-[500px]' onKeyDown={handleKeyPress}>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Save className='h-5 w-5 text-orange-600' />
            存为备选配比
          </DialogTitle>
          <DialogDescription>将当前配比保存为备选方案，以便后续快速应用</DialogDescription>
        </DialogHeader>

        <div className='space-y-4 py-4'>
          {/* 配比摘要信息 */}
          {ratioSummary && (
            <div className='bg-gray-50 rounded-lg p-3 space-y-2'>
              <div className='text-sm font-medium text-gray-700'>配比摘要</div>
              <div className='grid grid-cols-2 gap-2 text-xs'>
                <div className='flex justify-between'>
                  <span className='text-gray-600'>强度等级:</span>
                  <span className='font-medium'>{ratioSummary.targetStrength}</span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-gray-600'>坍落度:</span>
                  <span className='font-medium'>{ratioSummary.slump}mm</span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-gray-600'>水胶比:</span>
                  <span className='font-medium'>{ratioSummary.waterCementRatio.toFixed(3)}</span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-gray-600'>材料数量:</span>
                  <span className='font-medium'>{ratioSummary.materialCount}种</span>
                </div>
                {ratioSummary.qualityScore && (
                  <div className='flex justify-between col-span-2'>
                    <span className='text-gray-600'>质量评分:</span>
                    <span className='font-medium text-green-600'>
                      {ratioSummary.qualityScore.toFixed(1)}分
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 配比名称 */}
          <div className='space-y-2'>
            <Label htmlFor='ratio-name'>
              配比名称 <span className='text-red-500'>*</span>
            </Label>
            <Input
              id='ratio-name'
              value={formData.name}
              onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder='请输入配比名称，如：C30泵送混凝土-标准配比'
              className={errors['name'] ? 'border-red-500' : ''}
            />
            {errors['name'] && (
              <div className='flex items-center gap-1 text-sm text-red-600'>
                <AlertCircle className='h-3 w-3' />
                {errors['name']}
              </div>
            )}
          </div>

          {/* 配比描述 */}
          <div className='space-y-2'>
            <Label htmlFor='ratio-description'>配比描述</Label>
            <Textarea
              id='ratio-description'
              value={formData.description}
              onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder='请输入配比描述，如：适用于夏季施工，具有良好的工作性能...'
              rows={3}
              className={errors['description'] ? 'border-red-500' : ''}
            />
            {errors['description'] && (
              <div className='flex items-center gap-1 text-sm text-red-600'>
                <AlertCircle className='h-3 w-3' />
                {errors['description']}
              </div>
            )}
            <div className='text-xs text-gray-500'>{formData.description.length}/200 字符</div>
          </div>

          {/* 标签管理 */}
          <div className='space-y-2'>
            <Label htmlFor='ratio-tags'>
              <Tag className='h-3 w-3 inline mr-1' />
              标签 (最多5个)
            </Label>
            <div className='flex gap-2'>
              <Input
                id='ratio-tags'
                value={newTag}
                onChange={e => setNewTag(e.target.value)}
                placeholder='输入标签，如：夏季、泵送、高强度'
                onKeyPress={e => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddTag();
                  }
                }}
                disabled={formData.tags.length >= 5}
              />
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={handleAddTag}
                disabled={
                  !newTag.trim() ||
                  formData.tags.includes(newTag.trim()) ||
                  formData.tags.length >= 5
                }
              >
                添加
              </Button>
            </div>
            {formData.tags.length > 0 && (
              <div className='flex flex-wrap gap-1'>
                {formData.tags.map(tag => (
                  <Badge
                    key={tag}
                    variant='secondary'
                    className='text-xs cursor-pointer hover:bg-red-100'
                    onClick={() => handleRemoveTag(tag)}
                  >
                    {tag}
                    <X className='h-3 w-3 ml-1' />
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* 提交错误 */}
          {errors['submit'] && (
            <div className='flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg'>
              <AlertCircle className='h-4 w-4 text-red-600' />
              <span className='text-sm text-red-600'>{errors['submit']}</span>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleClose} disabled={isSaving}>
            取消
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <div className='animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2' />
                保存中...
              </>
            ) : (
              <>
                <Save className='h-3 w-3 mr-2' />
                保存备选
              </>
            )}
          </Button>
        </DialogFooter>

        {/* 快捷键提示 */}
        <div className='text-xs text-gray-500 text-center border-t pt-2'>
          提示：按 Ctrl+Enter 快速保存
        </div>
      </DialogContent>
    </Dialog>
  );
}
