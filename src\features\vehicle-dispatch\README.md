# 已出厂车辆表格

## 功能概述

已出厂车辆表格已成功集成到任务列表页面的车辆调度模块中，用于管理和查看已出厂车辆的详细信息，包括行程时间、司机信息、发货单编号等。

## 集成位置

已出厂车辆表格现在位于：
- **主页面**: `http://localhost:3000` 右侧车辆调度面板
- **测试页面**: `http://localhost:3000/test-dispatched-vehicles`
- **独立页面**: `http://localhost:3000/dispatched-vehicles`

## 主要功能

### 📊 表格功能
- **列拖拽调整**: 支持拖拽调整列的顺序
- **列宽度调整**: 支持拖拽调整列的宽度
- **横向滚动**: 当列显示不全时支持横向滚动
- **虚拟化渲染**: 支持大量数据的高性能渲染
- **斑马纹显示**: 可选的行间隔色彩显示

### 📋 数据列（按您的要求重新设计）
1. **车号（往返状态）** - 车辆编号 + 往返状态图标（🔄往返 / →单程）
2. **司机** - 司机姓名
3. **出站时间** - 车辆离开搅拌站的时间
4. **到达时间** - 车辆到达工地的时间
5. **工地时长** - 在工地停留的时间
6. **返程时间** - 车辆开始返程的时间
7. **去程时长** - 从出站到到达工地的时间
8. **返程时长** - 从工地返回的时间
9. **总时长** - 整个行程的总时间
10. **发货单编号** - 对应的发货单编号

### ⚙️ 设置功能
- **密度调整**: 支持紧凑、正常、舒适三种显示密度
- **列显示控制**: 可以控制哪些列显示或隐藏
- **设置持久化**: 用户设置自动保存到本地存储

### 🔧 工具栏功能
- **刷新数据**: 重新加载最新数据
- **数据导出**: 导出为CSV格式
- **数据筛选**: 筛选特定条件的数据
- **表格设置**: 打开设置面板

## 技术特性

### 🏗️ 架构设计
- **组件化**: 采用模块化组件设计，易于维护和扩展
- **Hook驱动**: 使用自定义Hook管理状态和逻辑
- **类型安全**: 完整的TypeScript类型定义
- **响应式**: 支持不同屏幕尺寸的自适应显示

### 📦 核心依赖
- **@tanstack/react-table**: 表格核心功能
- **@tanstack/react-virtual**: 虚拟化渲染
- **react-dnd**: 拖拽功能
- **date-fns**: 时间格式化
- **lucide-react**: 图标组件

### 🎨 样式系统
- **Tailwind CSS**: 原子化CSS框架
- **动态样式**: 基于密度和主题的动态样式计算
- **响应式设计**: 支持移动端和桌面端

## 使用方法

### 基本使用

```tsx
import { DispatchedVehiclesContainer } from '@/features/vehicle-dispatch';

function MyPage() {
  const [data, setData] = useState<DispatchedVehicle[]>([]);

  const handleRefresh = () => {
    // 刷新数据逻辑
  };

  const handleExport = () => {
    // 导出数据逻辑
  };

  return (
    <DispatchedVehiclesContainer
      data={data}
      onRefresh={handleRefresh}
      onExport={handleExport}
    />
  );
}
```

### 数据格式

```tsx
interface DispatchedVehicle {
  id: string;
  vehicleNumber: string;
  driver: string;
  departureTime: string;
  arrivalTime?: string;
  returnTime?: string;
  outboundDuration?: number;
  siteStayDuration?: number;
  returnDuration?: number;
  totalDuration?: number;
  deliveryOrderNumber: string;
  isRoundTrip: boolean;
  // ... 其他可选字段
}
```

## 车辆调度模块布局

车辆调度模块现在包含三个区域：

```
┌─────────────────────────────────┐
│        待发车辆 (1/4高度)        │
├─────────────────────────────────┤
│        退货车辆 (1/4高度)        │
├─────────────────────────────────┤
│     已出厂车辆表格 (1/2高度)     │
│  ✅ 车号（往返图标+车号）        │
│  ✅ 司机、出站时间、到达时间     │
│  ✅ 工地时长、返程时间          │
│  ✅ 去程时长、返程时长、总时长   │
│  ✅ 发货单编号                 │
│  ✅ 列拖拽调整顺序              │
│  ✅ 横向滚动支持               │
└─────────────────────────────────┘
```

## 文件结构

```
src/features/vehicle-dispatch/
├── components/
│   ├── VehicleDispatchContainer.tsx       # 车辆调度容器（已更新）
│   ├── DispatchedVehiclesContainer.tsx    # 已出厂车辆容器组件
│   ├── DispatchedVehiclesTable.tsx        # 已出厂车辆表格组件
│   └── DispatchedVehiclesDemo.tsx         # 演示组件
├── hooks/
│   ├── use-dispatched-vehicles-settings.ts # 设置管理Hook
│   └── use-dispatched-vehicles-columns.tsx # 列配置Hook
├── config/
│   └── dispatched-vehicles-columns.config.ts # 列配置
└── index.ts                               # 导出文件
```

## 访问方式

1. **主页面集成**: `http://localhost:3000` - 右侧车辆调度面板
2. **测试页面**: `http://localhost:3000/test-dispatched-vehicles` - 专门的测试页面
3. **独立演示**: `http://localhost:3000/dispatched-vehicles` - 完整功能演示

## 扩展性

该组件设计时考虑了扩展性，可以轻松添加：
- 新的数据列
- 自定义筛选器
- 批量操作功能
- 数据统计面板
- 实时数据更新

## 性能优化

- **虚拟化渲染**: 只渲染可见行，支持大量数据
- **记忆化计算**: 使用useMemo优化计算密集型操作
- **懒加载**: 按需加载组件和数据
- **防抖处理**: 用户输入和设置变更的防抖处理
