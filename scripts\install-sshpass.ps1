# TMH任务调度系统 - 安装sshpass工具
# 用于Windows环境下的SSH密码自动认证

param(
    [switch]$Force
)

Write-Host "🔧 TMH任务调度系统 - 安装sshpass" -ForegroundColor Green
Write-Host "🎯 目标: 为Windows安装SSH密码认证工具" -ForegroundColor Cyan
Write-Host ""

# 检查是否已安装
try {
    $sshpassVersion = sshpass -V 2>&1
    if (-not $Force) {
        Write-Host "✅ sshpass已安装: $sshpassVersion" -ForegroundColor Green
        Write-Host "使用 -Force 参数强制重新安装" -ForegroundColor Yellow
        exit 0
    }
} catch {
    Write-Host "📦 sshpass未安装，开始安装..." -ForegroundColor Blue
}

# 方法1: 尝试使用Chocolatey
Write-Host "🍫 尝试使用Chocolatey安装..." -ForegroundColor Blue
try {
    $chocoVersion = choco -v 2>&1
    Write-Host "✅ Chocolatey已安装: $chocoVersion" -ForegroundColor Green
    
    Write-Host "📦 安装sshpass..." -ForegroundColor Blue
    choco install sshpass -y
    
    # 验证安装
    $sshpassVersion = sshpass -V 2>&1
    Write-Host "✅ sshpass安装成功: $sshpassVersion" -ForegroundColor Green
    exit 0
} catch {
    Write-Host "⚠️ Chocolatey不可用或安装失败" -ForegroundColor Yellow
}

# 方法2: 下载预编译版本
Write-Host "📥 尝试下载预编译版本..." -ForegroundColor Blue
try {
    $downloadUrl = "https://github.com/PowerShell/Win32-OpenSSH/releases/latest/download/OpenSSH-Win64.zip"
    $tempDir = "$env:TEMP\openssh-install"
    $zipFile = "$tempDir\openssh.zip"
    $installDir = "C:\Program Files\OpenSSH"
    
    # 创建临时目录
    if (Test-Path $tempDir) {
        Remove-Item $tempDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
    
    Write-Host "📥 下载OpenSSH..." -ForegroundColor Blue
    Invoke-WebRequest -Uri $downloadUrl -OutFile $zipFile -UseBasicParsing
    
    Write-Host "📦 解压文件..." -ForegroundColor Blue
    Expand-Archive -Path $zipFile -DestinationPath $tempDir -Force
    
    # 查找sshpass或类似工具
    $sshpassPath = Get-ChildItem -Path $tempDir -Recurse -Name "*ssh*" | Where-Object { $_ -like "*sshpass*" -or $_ -like "*ssh.exe" }
    
    if ($sshpassPath) {
        Write-Host "✅ 找到SSH工具" -ForegroundColor Green
        # 这里可以添加安装逻辑
    } else {
        Write-Host "⚠️ 未找到sshpass工具" -ForegroundColor Yellow
    }
    
    # 清理临时文件
    Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue
    
} catch {
    Write-Host "❌ 下载安装失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 方法3: 使用Git Bash中的sshpass
Write-Host "🔍 检查Git Bash中的sshpass..." -ForegroundColor Blue
try {
    $gitBashPath = Get-Command "bash" -ErrorAction SilentlyContinue
    if ($gitBashPath) {
        $bashSshpass = bash -c "which sshpass" 2>$null
        if ($bashSshpass) {
            Write-Host "✅ 在Git Bash中找到sshpass: $bashSshpass" -ForegroundColor Green
            Write-Host "可以使用: bash -c 'sshpass ...' 来执行命令" -ForegroundColor Cyan
            exit 0
        }
    }
} catch {
    Write-Host "⚠️ Git Bash不可用" -ForegroundColor Yellow
}

# 方法4: 使用WSL
Write-Host "🐧 检查WSL中的sshpass..." -ForegroundColor Blue
try {
    $wslVersion = wsl --version 2>$null
    if ($wslVersion) {
        Write-Host "✅ WSL可用" -ForegroundColor Green
        
        # 在WSL中安装sshpass
        Write-Host "📦 在WSL中安装sshpass..." -ForegroundColor Blue
        wsl sudo apt-get update
        wsl sudo apt-get install -y sshpass
        
        # 验证安装
        $wslSshpass = wsl sshpass -V 2>$null
        if ($wslSshpass) {
            Write-Host "✅ WSL中sshpass安装成功" -ForegroundColor Green
            Write-Host "可以使用: wsl sshpass ... 来执行命令" -ForegroundColor Cyan
            exit 0
        }
    }
} catch {
    Write-Host "⚠️ WSL不可用或安装失败" -ForegroundColor Yellow
}

# 如果所有方法都失败
Write-Host ""
Write-Host "❌ 自动安装失败" -ForegroundColor Red
Write-Host ""
Write-Host "📋 手动安装选项:" -ForegroundColor Cyan
Write-Host "1. 安装Chocolatey后运行: choco install sshpass" -ForegroundColor White
Write-Host "2. 安装Git for Windows (包含bash环境)" -ForegroundColor White
Write-Host "3. 启用WSL并安装Ubuntu" -ForegroundColor White
Write-Host "4. 使用SSH密钥认证 (推荐): npm run setup:ssh" -ForegroundColor White
Write-Host ""
Write-Host "🔗 相关链接:" -ForegroundColor Cyan
Write-Host "  Chocolatey: https://chocolatey.org/install" -ForegroundColor White
Write-Host "  Git for Windows: https://git-scm.com/download/win" -ForegroundColor White
Write-Host "  WSL: https://docs.microsoft.com/en-us/windows/wsl/install" -ForegroundColor White
Write-Host ""
Write-Host "💡 建议: 使用SSH密钥认证更安全便捷" -ForegroundColor Yellow
