/**
 * 配比数据仓库
 * 统一的配比数据访问接口
 */

import type {
  RatioModel,
  RatioHistoryModel,
  RatioTemplateModel,
  RatioValidationResult,
  CalculationResultModel,
  OptimizationResultModel,
} from '@/models/RatioModel';
import { BusinessResult } from '@/core/domain/BusinessResult';

export interface IRatioRepository {
  // 配比CRUD操作
  getRatio(taskId: string): Promise<RatioModel | null>;
  getRatioById(ratioId: string): Promise<RatioModel | null>;
  saveRatio(ratio: RatioModel): Promise<BusinessResult<RatioModel>>;
  updateRatio(ratioId: string, updates: Partial<RatioModel>): Promise<BusinessResult<RatioModel>>;
  deleteRatio(ratioId: string): Promise<BusinessResult<void>>;

  // 配比历史
  getRatioHistory(taskId: string): Promise<RatioHistoryModel[]>;
  addHistoryRecord(
    record: Omit<RatioHistoryModel, 'id'>
  ): Promise<BusinessResult<RatioHistoryModel>>;

  // 配比模板
  getRatioTemplates(filters?: TemplateFilters): Promise<RatioTemplateModel[]>;
  getRatioTemplate(templateId: string): Promise<RatioTemplateModel | null>;
  saveRatioTemplate(
    template: Omit<RatioTemplateModel, 'id'>
  ): Promise<BusinessResult<RatioTemplateModel>>;
  updateRatioTemplate(
    templateId: string,
    updates: Partial<RatioTemplateModel>
  ): Promise<BusinessResult<RatioTemplateModel>>;
  deleteRatioTemplate(templateId: string): Promise<BusinessResult<void>>;

  // 计算结果
  saveCalculationResult(
    result: Omit<CalculationResultModel, 'id'>
  ): Promise<BusinessResult<CalculationResultModel>>;
  getCalculationHistory(ratioId: string): Promise<CalculationResultModel[]>;

  // 优化结果
  saveOptimizationResult(
    result: Omit<OptimizationResultModel, 'id'>
  ): Promise<BusinessResult<OptimizationResultModel>>;
  getOptimizationHistory(ratioId: string): Promise<OptimizationResultModel[]>;

  // 验证
  validateRatio(ratio: RatioModel): Promise<RatioValidationResult>;

  // 搜索和查询
  searchRatios(criteria: RatioSearchCriteria): Promise<RatioModel[]>;
  getRatiosByStrengthGrade(strengthGrade: string): Promise<RatioModel[]>;
  getRatiosByMixingStation(stationId: string): Promise<RatioModel[]>;

  // 统计
  getRatioStatistics(filters?: StatisticsFilters): Promise<RatioStatistics>;
}

export interface TemplateFilters {
  category?: string;
  strengthGrade?: string;
  exposureClass?: string;
  isPublic?: boolean;
  createdBy?: string;
  tags?: string[];
}

export interface RatioSearchCriteria {
  taskNumber?: string;
  projectName?: string;
  strengthGrade?: string;
  mixingStation?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  status?: string[];
  createdBy?: string;
  limit?: number;
  offset?: number;
}

export interface StatisticsFilters {
  dateRange?: {
    start: string;
    end: string;
  };
  mixingStation?: string;
  strengthGrade?: string;
}

export interface RatioStatistics {
  totalRatios: number;
  ratiosByStrength: Record<string, number>;
  ratiosByStation: Record<string, number>;
  ratiosByStatus: Record<string, number>;
  averageQualityScore: number;
  mostUsedMaterials: {
    materialId: string;
    materialName: string;
    usageCount: number;
  }[];
  costTrends: {
    date: string;
    averageCost: number;
  }[];
  qualityTrends: {
    date: string;
    averageScore: number;
  }[];
}

/**
 * 配比数据仓库实现
 */
export class RatioRepository implements IRatioRepository {
  constructor(
    private apiService: IRatioApiService,
    private cacheService: ICacheService,
    private logger: ILogger
  ) {}

  async getRatio(taskId: string): Promise<RatioModel | null> {
    try {
      // 先从缓存获取
      const cacheKey = `ratio:task:${taskId}`;
      const cached = await this.cacheService.get<RatioModel>(cacheKey);
      if (cached) {
        this.logger.debug('Ratio loaded from cache', { taskId });
        return cached;
      }

      // 从API获取
      const ratio = await this.apiService.getRatioByTaskId(taskId);
      if (ratio) {
        // 缓存结果
        await this.cacheService.set(cacheKey, ratio, 300); // 5分钟缓存
        this.logger.info('Ratio loaded from API', { taskId, ratioId: ratio.id });
      }

      return ratio;
    } catch (error) {
      this.logger.error('Failed to get ratio', error as Error, { taskId });
      throw error;
    }
  }

  async getRatioById(ratioId: string): Promise<RatioModel | null> {
    try {
      const cacheKey = `ratio:${ratioId}`;
      const cached = await this.cacheService.get<RatioModel>(cacheKey);
      if (cached) {
        return cached;
      }

      const ratio = await this.apiService.getRatio(ratioId);
      if (ratio) {
        await this.cacheService.set(cacheKey, ratio, 300);
      }

      return ratio;
    } catch (error) {
      this.logger.error('Failed to get ratio by id', error as Error, { ratioId });
      throw error;
    }
  }

  async saveRatio(ratio: RatioModel): Promise<BusinessResult<RatioModel>> {
    try {
      // 验证配比数据
      const validation = await this.validateRatio(ratio);
      if (!validation.isValid) {
        return BusinessResult.failure('VALIDATION_FAILED', '配比数据验证失败', validation.errors);
      }

      // 保存到API
      const savedRatio = await this.apiService.saveRatio(ratio);

      // 更新缓存
      const cacheKey = `ratio:${savedRatio.id}`;
      await this.cacheService.set(cacheKey, savedRatio, 300);

      // 清除相关缓存
      await this.cacheService.delete(`ratio:task:${savedRatio.taskId}`);

      this.logger.info('Ratio saved successfully', {
        ratioId: savedRatio.id,
        taskId: savedRatio.taskId,
      });

      return BusinessResult.success(savedRatio);
    } catch (error) {
      this.logger.error('Failed to save ratio', error as Error, {
        ratioId: ratio.id,
        taskId: ratio.taskId,
      });
      return BusinessResult.failure('SAVE_FAILED', '保存配比失败', error);
    }
  }

  async updateRatio(
    ratioId: string,
    updates: Partial<RatioModel>
  ): Promise<BusinessResult<RatioModel>> {
    try {
      const updatedRatio = await this.apiService.updateRatio(ratioId, updates);

      // 更新缓存
      const cacheKey = `ratio:${ratioId}`;
      await this.cacheService.set(cacheKey, updatedRatio, 300);

      // 清除相关缓存
      await this.cacheService.delete(`ratio:task:${updatedRatio.taskId}`);

      this.logger.info('Ratio updated successfully', { ratioId });

      return BusinessResult.success(updatedRatio);
    } catch (error) {
      this.logger.error('Failed to update ratio', error as Error, { ratioId });
      return BusinessResult.failure('UPDATE_FAILED', '更新配比失败', error);
    }
  }

  async deleteRatio(ratioId: string): Promise<BusinessResult<void>> {
    try {
      await this.apiService.deleteRatio(ratioId);

      // 清除缓存
      await this.cacheService.delete(`ratio:${ratioId}`);

      this.logger.info('Ratio deleted successfully', { ratioId });

      return BusinessResult.success(undefined);
    } catch (error) {
      this.logger.error('Failed to delete ratio', error as Error, { ratioId });
      return BusinessResult.failure('DELETE_FAILED', '删除配比失败', error);
    }
  }

  async getRatioHistory(taskId: string): Promise<RatioHistoryModel[]> {
    try {
      const cacheKey = `ratio:history:${taskId}`;
      const cached = await this.cacheService.get<RatioHistoryModel[]>(cacheKey);
      if (cached) {
        return cached;
      }

      const history = await this.apiService.getRatioHistory(taskId);
      await this.cacheService.set(cacheKey, history, 180); // 3分钟缓存

      return history;
    } catch (error) {
      this.logger.error('Failed to get ratio history', error as Error, { taskId });
      throw error;
    }
  }

  async addHistoryRecord(
    record: Omit<RatioHistoryModel, 'id'>
  ): Promise<BusinessResult<RatioHistoryModel>> {
    try {
      const savedRecord = await this.apiService.addHistoryRecord(record);

      // 清除历史缓存
      await this.cacheService.delete(`ratio:history:${record.taskId}`);

      this.logger.info('History record added', {
        taskId: record.taskId,
        changeType: record.changeType,
      });

      return BusinessResult.success(savedRecord);
    } catch (error) {
      this.logger.error('Failed to add history record', error as Error, {
        taskId: record.taskId,
      });
      return BusinessResult.failure('HISTORY_SAVE_FAILED', '保存历史记录失败', error);
    }
  }

  async getRatioTemplates(filters?: TemplateFilters): Promise<RatioTemplateModel[]> {
    try {
      const cacheKey = `templates:${JSON.stringify(filters || {})}`;
      const cached = await this.cacheService.get<RatioTemplateModel[]>(cacheKey);
      if (cached) {
        return cached;
      }

      const templates = await this.apiService.getRatioTemplates(filters);
      await this.cacheService.set(cacheKey, templates, 600); // 10分钟缓存

      return templates;
    } catch (error) {
      this.logger.error('Failed to get ratio templates', error as Error, { filters });
      throw error;
    }
  }

  async getRatioTemplate(templateId: string): Promise<RatioTemplateModel | null> {
    try {
      const cacheKey = `template:${templateId}`;
      const cached = await this.cacheService.get<RatioTemplateModel>(cacheKey);
      if (cached) {
        return cached;
      }

      const template = await this.apiService.getRatioTemplate(templateId);
      if (template) {
        await this.cacheService.set(cacheKey, template, 600);
      }

      return template;
    } catch (error) {
      this.logger.error('Failed to get ratio template', error as Error, { templateId });
      throw error;
    }
  }

  async saveRatioTemplate(
    template: Omit<RatioTemplateModel, 'id'>
  ): Promise<BusinessResult<RatioTemplateModel>> {
    try {
      const savedTemplate = await this.apiService.saveRatioTemplate(template);

      // 清除模板列表缓存
      await this.cacheService.deletePattern('templates:*');

      this.logger.info('Template saved successfully', {
        templateId: savedTemplate.id,
        name: savedTemplate.name,
      });

      return BusinessResult.success(savedTemplate);
    } catch (error) {
      this.logger.error('Failed to save template', error as Error, {
        name: template.name,
      });
      return BusinessResult.failure('TEMPLATE_SAVE_FAILED', '保存模板失败', error);
    }
  }

  async updateRatioTemplate(
    templateId: string,
    updates: Partial<RatioTemplateModel>
  ): Promise<BusinessResult<RatioTemplateModel>> {
    try {
      const updatedTemplate = await this.apiService.updateRatioTemplate(templateId, updates);

      // 更新缓存
      const cacheKey = `template:${templateId}`;
      await this.cacheService.set(cacheKey, updatedTemplate, 600);

      // 清除模板列表缓存
      await this.cacheService.deletePattern('templates:*');

      this.logger.info('Template updated successfully', { templateId });

      return BusinessResult.success(updatedTemplate);
    } catch (error) {
      this.logger.error('Failed to update template', error as Error, { templateId });
      return BusinessResult.failure('TEMPLATE_UPDATE_FAILED', '更新模板失败', error);
    }
  }

  async deleteRatioTemplate(templateId: string): Promise<BusinessResult<void>> {
    try {
      await this.apiService.deleteRatioTemplate(templateId);

      // 清除缓存
      await this.cacheService.delete(`template:${templateId}`);
      await this.cacheService.deletePattern('templates:*');

      this.logger.info('Template deleted successfully', { templateId });

      return BusinessResult.success(undefined);
    } catch (error) {
      this.logger.error('Failed to delete template', error as Error, { templateId });
      return BusinessResult.failure('TEMPLATE_DELETE_FAILED', '删除模板失败', error);
    }
  }

  async saveCalculationResult(
    result: Omit<CalculationResultModel, 'id'>
  ): Promise<BusinessResult<CalculationResultModel>> {
    try {
      const savedResult = await this.apiService.saveCalculationResult(result);

      this.logger.info('Calculation result saved', {
        ratioId: result.ratioId,
        type: result.calculationType,
      });

      return BusinessResult.success(savedResult);
    } catch (error) {
      this.logger.error('Failed to save calculation result', error as Error, {
        ratioId: result.ratioId,
      });
      return BusinessResult.failure('CALCULATION_SAVE_FAILED', '保存计算结果失败', error);
    }
  }

  async getCalculationHistory(ratioId: string): Promise<CalculationResultModel[]> {
    try {
      return await this.apiService.getCalculationHistory(ratioId);
    } catch (error) {
      this.logger.error('Failed to get calculation history', error as Error, { ratioId });
      throw error;
    }
  }

  async saveOptimizationResult(
    result: Omit<OptimizationResultModel, 'id'>
  ): Promise<BusinessResult<OptimizationResultModel>> {
    try {
      const savedResult = await this.apiService.saveOptimizationResult(result);

      this.logger.info('Optimization result saved', {
        originalRatioId: result.originalRatioId,
      });

      return BusinessResult.success(savedResult);
    } catch (error) {
      this.logger.error('Failed to save optimization result', error as Error, {
        originalRatioId: result.originalRatioId,
      });
      return BusinessResult.failure('OPTIMIZATION_SAVE_FAILED', '保存优化结果失败', error);
    }
  }

  async getOptimizationHistory(ratioId: string): Promise<OptimizationResultModel[]> {
    try {
      return await this.apiService.getOptimizationHistory(ratioId);
    } catch (error) {
      this.logger.error('Failed to get optimization history', error as Error, { ratioId });
      throw error;
    }
  }

  async validateRatio(ratio: RatioModel): Promise<RatioValidationResult> {
    try {
      return await this.apiService.validateRatio(ratio);
    } catch (error) {
      this.logger.error('Failed to validate ratio', error as Error, {
        ratioId: ratio.id,
      });
      throw error;
    }
  }

  async searchRatios(criteria: RatioSearchCriteria): Promise<RatioModel[]> {
    try {
      return await this.apiService.searchRatios(criteria);
    } catch (error) {
      this.logger.error('Failed to search ratios', error as Error, { criteria });
      throw error;
    }
  }

  async getRatiosByStrengthGrade(strengthGrade: string): Promise<RatioModel[]> {
    try {
      const cacheKey = `ratios:strength:${strengthGrade}`;
      const cached = await this.cacheService.get<RatioModel[]>(cacheKey);
      if (cached) {
        return cached;
      }

      const ratios = await this.apiService.getRatiosByStrengthGrade(strengthGrade);
      await this.cacheService.set(cacheKey, ratios, 300);

      return ratios;
    } catch (error) {
      this.logger.error('Failed to get ratios by strength grade', error as Error, {
        strengthGrade,
      });
      throw error;
    }
  }

  async getRatiosByMixingStation(stationId: string): Promise<RatioModel[]> {
    try {
      const cacheKey = `ratios:station:${stationId}`;
      const cached = await this.cacheService.get<RatioModel[]>(cacheKey);
      if (cached) {
        return cached;
      }

      const ratios = await this.apiService.getRatiosByMixingStation(stationId);
      await this.cacheService.set(cacheKey, ratios, 300);

      return ratios;
    } catch (error) {
      this.logger.error('Failed to get ratios by mixing station', error as Error, { stationId });
      throw error;
    }
  }

  async getRatioStatistics(filters?: StatisticsFilters): Promise<RatioStatistics> {
    try {
      const cacheKey = `statistics:${JSON.stringify(filters || {})}`;
      const cached = await this.cacheService.get<RatioStatistics>(cacheKey);
      if (cached) {
        return cached;
      }

      const statistics = await this.apiService.getRatioStatistics(filters);
      await this.cacheService.set(cacheKey, statistics, 600); // 10分钟缓存

      return statistics;
    } catch (error) {
      this.logger.error('Failed to get ratio statistics', error as Error, { filters });
      throw error;
    }
  }
}

// 依赖接口
interface IRatioApiService {
  getRatioByTaskId(taskId: string): Promise<RatioModel | null>;
  getRatio(ratioId: string): Promise<RatioModel | null>;
  saveRatio(ratio: RatioModel): Promise<RatioModel>;
  updateRatio(ratioId: string, updates: Partial<RatioModel>): Promise<RatioModel>;
  deleteRatio(ratioId: string): Promise<void>;
  getRatioHistory(taskId: string): Promise<RatioHistoryModel[]>;
  addHistoryRecord(record: Omit<RatioHistoryModel, 'id'>): Promise<RatioHistoryModel>;
  getRatioTemplates(filters?: TemplateFilters): Promise<RatioTemplateModel[]>;
  getRatioTemplate(templateId: string): Promise<RatioTemplateModel | null>;
  saveRatioTemplate(template: Omit<RatioTemplateModel, 'id'>): Promise<RatioTemplateModel>;
  updateRatioTemplate(
    templateId: string,
    updates: Partial<RatioTemplateModel>
  ): Promise<RatioTemplateModel>;
  deleteRatioTemplate(templateId: string): Promise<void>;
  saveCalculationResult(
    result: Omit<CalculationResultModel, 'id'>
  ): Promise<CalculationResultModel>;
  getCalculationHistory(ratioId: string): Promise<CalculationResultModel[]>;
  saveOptimizationResult(
    result: Omit<OptimizationResultModel, 'id'>
  ): Promise<OptimizationResultModel>;
  getOptimizationHistory(ratioId: string): Promise<OptimizationResultModel[]>;
  validateRatio(ratio: RatioModel): Promise<RatioValidationResult>;
  searchRatios(criteria: RatioSearchCriteria): Promise<RatioModel[]>;
  getRatiosByStrengthGrade(strengthGrade: string): Promise<RatioModel[]>;
  getRatiosByMixingStation(stationId: string): Promise<RatioModel[]>;
  getRatioStatistics(filters?: StatisticsFilters): Promise<RatioStatistics>;
}

interface ICacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  deletePattern(pattern: string): Promise<void>;
}

interface ILogger {
  debug(message: string, meta?: any): void;
  info(message: string, meta?: any): void;
  error(message: string, error: Error, meta?: any): void;
}
