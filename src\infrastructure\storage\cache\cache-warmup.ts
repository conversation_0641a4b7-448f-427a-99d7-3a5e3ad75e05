/**
 * 缓存预热工具
 * 在应用启动时预热常用的计算缓存
 */

import { computationCache } from './computation-cache';
import { globalCache } from './performance-cache';

// ==================== 预热配置 ====================

interface WarmupConfig {
  enabled: boolean;
  delay: number; // 延迟启动时间（毫秒）
  batchSize: number; // 批处理大小
  timeout: number; // 超时时间
}

const DEFAULT_WARMUP_CONFIG: WarmupConfig = {
  enabled: true,
  delay: 2000, // 2秒后开始预热
  batchSize: 5, // 每批5个
  timeout: 30000, // 30秒超时
};

// ==================== 预热任务定义 ====================

interface WarmupTask {
  key: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  computeFn: () => any | Promise<any>;
  dependencies?: string[];
}

// 常用的预热任务
const WARMUP_TASKS: WarmupTask[] = [
  // 任务过滤相关
  {
    key: 'task-filter-empty',
    description: '空过滤条件的任务列表',
    priority: 'high',
    computeFn: () => ({
      filteredTasks: [],
      totalCount: 0,
      filterTime: Date.now(),
    }),
  },

  // 车辆分组相关
  {
    key: 'vehicle-group-empty',
    description: '空车辆列表分组',
    priority: 'high',
    computeFn: () => new Map(),
  },

  // 配比计算相关
  {
    key: 'ratio-calculation-c30',
    description: 'C30混凝土配比计算',
    priority: 'medium',
    computeFn: () => ({
      cement: 350,
      water: 175,
      sand: 650,
      gravel: 1200,
      totalWeight: 2375,
      waterCementRatio: 0.5,
      strengthPrediction: 30,
      calculatedAt: new Date().toISOString(),
    }),
  },

  {
    key: 'ratio-calculation-c25',
    description: 'C25混凝土配比计算',
    priority: 'medium',
    computeFn: () => ({
      cement: 320,
      water: 170,
      sand: 680,
      gravel: 1230,
      totalWeight: 2400,
      waterCementRatio: 0.53,
      strengthPrediction: 25,
      calculatedAt: new Date().toISOString(),
    }),
  },

  // 成本计算相关
  {
    key: 'cost-calculation-standard',
    description: '标准配比成本计算',
    priority: 'low',
    computeFn: () => ({
      materialCosts: {
        cement: 157.5, // 350kg * 0.45
        water: 0.525, // 175kg * 0.003
        sand: 52, // 650kg * 0.08
        gravel: 72, // 1200kg * 0.06
      },
      totalCost: 282.025,
      costPerCubicMeter: 282.025,
    }),
  },

  // 统计计算相关
  {
    key: 'task-stats-empty',
    description: '空任务列表统计',
    priority: 'low',
    computeFn: () => ({
      total: 0,
      byStatus: {},
      byPlant: {},
      totalVolume: 0,
      avgVolume: 0,
      completionRate: 0,
    }),
  },

  // 排序相关
  {
    key: 'data-sort-empty',
    description: '空数据排序',
    priority: 'low',
    computeFn: () => [],
  },
];

// ==================== 预热管理器 ====================

export class CacheWarmupManager {
  private config: WarmupConfig;
  private isWarming = false;
  private warmupPromise: Promise<void> | null = null;

  constructor(config: Partial<WarmupConfig> = {}) {
    this.config = { ...DEFAULT_WARMUP_CONFIG, ...config };
  }

  /**
   * 启动缓存预热
   */
  async startWarmup(): Promise<void> {
    if (!this.config.enabled || this.isWarming) {
      return;
    }

    if (this.warmupPromise) {
      return this.warmupPromise;
    }

    this.warmupPromise = this.executeWarmup();
    return this.warmupPromise;
  }

  /**
   * 执行预热过程
   */
  private async executeWarmup(): Promise<void> {
    this.isWarming = true;

    try {
      console.log('🔥 Starting cache warmup...');

      // 延迟启动，避免影响初始加载
      await this.delay(this.config.delay);

      // 按优先级分组任务
      const tasksByPriority = this.groupTasksByPriority();

      // 按优先级顺序执行预热
      for (const priority of ['high', 'medium', 'low'] as const) {
        const tasks = tasksByPriority[priority] || [];
        if (tasks.length > 0) {
          console.log(`🔥 Warming up ${priority} priority tasks (${tasks.length} tasks)...`);
          await this.warmupTaskBatch(tasks);
        }
      }

      console.log('✅ Cache warmup completed successfully');
    } catch (error) {
      console.error('❌ Cache warmup failed:', error);
    } finally {
      this.isWarming = false;
      this.warmupPromise = null;
    }
  }

  /**
   * 按优先级分组任务
   */
  private groupTasksByPriority(): Record<string, WarmupTask[]> {
    const groups: Record<string, WarmupTask[]> = {
      high: [],
      medium: [],
      low: [],
    };

    WARMUP_TASKS.forEach(task => {
      const group = groups[task.priority];
      if (group) {
        group.push(task);
      }
    });

    return groups;
  }

  /**
   * 批量预热任务
   */
  private async warmupTaskBatch(tasks: WarmupTask[]): Promise<void> {
    const batches = this.chunkArray(tasks, this.config.batchSize);

    for (const batch of batches) {
      const promises = batch.map(task => this.warmupSingleTask(task));

      try {
        await Promise.allSettled(promises);
      } catch (error) {
        console.warn('Some warmup tasks failed:', error);
      }

      // 批次间短暂延迟，避免阻塞主线程
      await this.delay(100);
    }
  }

  /**
   * 预热单个任务
   */
  private async warmupSingleTask(task: WarmupTask): Promise<void> {
    try {
      const startTime = performance.now();

      await computationCache.computeWithCache(
        task.key,
        task.computeFn,
        { ttl: 30 * 60 * 1000 } // 30分钟缓存
      );

      const duration = performance.now() - startTime;
      console.log(`  ✓ ${task.description} (${duration.toFixed(1)}ms)`);
    } catch (error) {
      console.warn(`  ✗ Failed to warmup ${task.key}:`, error);
    }
  }

  /**
   * 数组分块
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取预热状态
   */
  getWarmupStatus() {
    return {
      isWarming: this.isWarming,
      config: this.config,
      totalTasks: WARMUP_TASKS.length,
      tasksByPriority: this.groupTasksByPriority(),
    };
  }
}

// ==================== 全局预热管理器 ====================

export const globalWarmupManager = new CacheWarmupManager({
  enabled: true,
  delay: 3000, // 3秒延迟
  batchSize: 3,
  timeout: 60000, // 1分钟超时
});

// ==================== 自动预热 Hook ====================

/**
 * 自动启动缓存预热的Hook
 */
export function useAutoWarmup(enabled = true) {
  if (typeof window === 'undefined') return; // SSR环境跳过

  if (enabled) {
    // 在空闲时启动预热
    if ('requestIdleCallback' in window) {
      requestIdleCallback(
        () => {
          globalWarmupManager.startWarmup();
        },
        { timeout: 5000 }
      );
    } else {
      // 降级到setTimeout
      setTimeout(() => {
        globalWarmupManager.startWarmup();
      }, 5000);
    }
  }
}

// ==================== 手动预热函数 ====================

/**
 * 手动触发特定类型的预热
 */
export async function warmupSpecificCache(
  type: 'tasks' | 'vehicles' | 'ratio' | 'all'
): Promise<void> {
  const taskFilters: Record<string, (task: WarmupTask) => boolean> = {
    tasks: task => task.key.includes('task'),
    vehicles: task => task.key.includes('vehicle'),
    ratio: task => task.key.includes('ratio') || task.key.includes('cost'),
    all: () => true,
  };

  const filter = taskFilters[type];
  if (!filter) {
    console.warn(`Unknown warmup type: ${type}`);
    return;
  }

  const filteredTasks = WARMUP_TASKS.filter(filter);

  console.log(`🔥 Manual warmup for ${type} (${filteredTasks.length} tasks)...`);

  const manager = new CacheWarmupManager({ enabled: true, delay: 0 });

  for (const task of filteredTasks) {
    await manager['warmupSingleTask'](task);
  }

  console.log(`✅ Manual warmup for ${type} completed`);
}

// ==================== 导出 ====================

export { WARMUP_TASKS, type WarmupTask, type WarmupConfig };
