import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

import type { DeliveryOrderStatus, Vehicle } from '@/core/types';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
//随机生成车牌号码
export interface VariousCarNumbers {
  [key: string]: string[];
}
export function createCarNumber() {
  //随机生成车牌号
  // 有效地区代码映射（示例数据）
  const validCombinations: VariousCarNumbers = {
    京: ['A', 'B', 'C', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q'],
    沪: ['A', 'B', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N'],
    粤: [
      'A',
      'B',
      'C',
      'D',
      'E',
      'F',
      'G',
      'H',
      'J',
      'K',
      'L',
      'M',
      'N',
      'P',
      'Q',
      'R',
      'S',
      'T',
      'U',
      'V',
      'W',
      'X',
      'Y',
    ],
    // 其他省份可继续补充...
    晋: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M'],
  };

  const provinces: string[] = Object.keys(validCombinations);
  const province = provinces[Math.floor(Math.random() * provinces.length)];
  if (!province) return '京A·12345'; // 默认值
  const cityOptions = validCombinations[province];
  if (!cityOptions) return '京A·12345'; // 默认值
  const city = cityOptions[Math.floor(Math.random() * cityOptions.length)];

  // 生成5位字符（最后一位可能是字母）
  let tail = '';
  for (let i = 0; i < 5; i++) {
    const pool = i === 4 ? '0123456789ABCDEFGHJKLMNQPRSTUVWXYZ' : '0123456789';
    tail += pool[Math.floor(Math.random() * pool.length)];
  }

  return `${province}${city}·${tail}`;
}

export const generateRandomDate = (start: Date, end: Date): string => {
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  return date.toISOString().split('T')[0] ?? date.toISOString().substring(0, 10);
};

export const generateRandomTime = (): string => {
  const hours = String(Math.floor(Math.random() * 24)).padStart(2, '0');
  const minutes = String(Math.floor(Math.random() * 60)).padStart(2, '0');
  return `${hours}:${minutes}`;
};

export const getProductionStatusColor = (status?: Vehicle['productionStatus']): string => {
  switch (status) {
    case 'queued':
      return 'bg-gray-400';
    case 'producing':
      return 'bg-yellow-500';
    case 'produced':
      return 'bg-blue-500';
    case 'weighed':
      return 'bg-purple-500';
    case 'ticketed':
      return 'bg-green-500';
    case 'shipped':
      return 'bg-teal-500';
    default:
      return 'bg-transparent'; // Or a default neutral color like 'bg-gray-300'
  }
};

export const getProductionStatusTooltip = (status?: Vehicle['productionStatus']): string => {
  switch (status) {
    case 'queued':
      return '排队中';
    case 'producing':
      return '生产中';
    case 'produced':
      return '生产完成';
    case 'weighed':
      return '已过磅';
    case 'ticketed':
      return '已打票';
    case 'shipped':
      return '已出厂';
    default:
      return '未知状态';
  }
};

export const getDeliveryOrderStatusIndicatorColor = (status?: DeliveryOrderStatus) => {
  switch (status) {
    case 'newlyDispatched':
      return 'bg-sky-500';
    case 'inProduction':
      return 'bg-amber-500';
    case 'weighed':
      return 'bg-purple-500';
    case 'shipped':
      return 'bg-green-500';
    default:
      return 'bg-slate-400';
  }
};
