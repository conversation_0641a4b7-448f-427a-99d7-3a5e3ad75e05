/**
 * 配比推荐弹框组件
 * 展示预设的配比推荐，用户可以选择并应用到配比设计面板
 */

import React, { useState, useEffect } from 'react';
import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Label } from '@/shared/components/label';
import { Separator } from '@/shared/components/separator';
import { Tabs, TabsList, TabsTrigger } from '@/shared/components/tabs';
import {
  Star,
  TrendingUp,
  Shield,
  DollarSign,
  Wrench,
  CheckCircle,
  ArrowRight,
  Sparkles,
} from 'lucide-react';
import {
  ratioRecommendationService,
  type RatioRecommendation,
} from '@/features/ratio-management/services/ratio/ratioRecommendationService';
import type { UnifiedRatioMaterial, RatioCalculationParams } from '@/core/types/ratio';

interface RatioRecommendationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (params: RatioCalculationParams, materials: UnifiedRatioMaterial[]) => void;
}

export function RatioRecommendationModal({
  isOpen,
  onClose,
  onApply,
}: RatioRecommendationModalProps) {
  const [recommendations, setRecommendations] = useState<RatioRecommendation[]>([]);
  const [selectedRecommendation, setSelectedRecommendation] = useState<RatioRecommendation | null>(
    null
  );
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    if (isOpen) {
      // 加载所有推荐配比
      const allRecommendations = ratioRecommendationService.getAllRecommendations();
      setRecommendations(allRecommendations);
      setSelectedRecommendation(null);
    }
  }, [isOpen]);

  // 根据强度等级过滤推荐
  const getFilteredRecommendations = () => {
    if (activeTab === 'all') {
      return recommendations;
    }
    return recommendations.filter(r => r.strengthGrade === activeTab);
  };

  // 获取性能指标颜色
  const getPerformanceColor = (score: number) => {
    if (score >= 8) return 'text-green-600';
    if (score >= 6) return 'text-yellow-600';
    return 'text-red-600';
  };

  // 获取性能指标图标
  const getPerformanceIcon = (type: string) => {
    switch (type) {
      case 'strength':
        return <TrendingUp className='w-4 h-4' />;
      case 'workability':
        return <Wrench className='w-4 h-4' />;
      case 'durability':
        return <Shield className='w-4 h-4' />;
      case 'economy':
        return <DollarSign className='w-4 h-4' />;
      default:
        return <Star className='w-4 h-4' />;
    }
  };

  // 应用推荐配比
  const handleApplyRecommendation = () => {
    if (!selectedRecommendation) return;

    onApply(selectedRecommendation.calculationParams, selectedRecommendation.materials);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-6xl max-h-[90vh] overflow-hidden'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Sparkles className='w-5 h-5 text-purple-500' />
            AI配比推荐
          </DialogTitle>
        </DialogHeader>

        <div className='flex gap-6 h-[70vh]'>
          {/* 左侧：推荐列表 */}
          <div className='flex-1 flex flex-col'>
            {/* 强度等级筛选 */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className='mb-4'>
              <TabsList className='grid w-full grid-cols-6'>
                <TabsTrigger value='all'>全部</TabsTrigger>
                <TabsTrigger value='C15'>C15</TabsTrigger>
                <TabsTrigger value='C25'>C25</TabsTrigger>
                <TabsTrigger value='C30'>C30</TabsTrigger>
                <TabsTrigger value='C35'>C35</TabsTrigger>
                <TabsTrigger value='C40'>C40</TabsTrigger>
              </TabsList>
            </Tabs>

            {/* 推荐列表 */}
            <div className='flex-1 overflow-y-auto space-y-3'>
              {getFilteredRecommendations().map(recommendation => (
                <Card
                  key={recommendation.id}
                  className={`cursor-pointer transition-all duration-200 ${
                    selectedRecommendation?.id === recommendation.id
                      ? 'ring-2 ring-purple-500 shadow-lg'
                      : 'hover:shadow-md'
                  }`}
                  onClick={() => setSelectedRecommendation(recommendation)}
                >
                  <CardHeader className='pb-3'>
                    <div className='flex items-start justify-between'>
                      <div>
                        <CardTitle className='text-lg'>{recommendation.name}</CardTitle>
                        <p className='text-sm text-muted-foreground mt-1'>
                          {recommendation.description}
                        </p>
                      </div>
                      <div className='flex flex-col items-end gap-1'>
                        <Badge variant='outline' className='bg-blue-50'>
                          {recommendation.strengthGrade}
                        </Badge>
                        <span className='text-sm font-medium text-green-600'>
                          ¥{recommendation.estimatedCost}/m³
                        </span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className='pt-0'>
                    {/* 性能指标 */}
                    <div className='grid grid-cols-4 gap-3 mb-3'>
                      <div className='text-center'>
                        <div className='flex items-center justify-center gap-1 mb-1'>
                          {getPerformanceIcon('strength')}
                          <span className='text-xs text-muted-foreground'>强度</span>
                        </div>
                        <span
                          className={`text-sm font-medium ${getPerformanceColor(recommendation.performance.strength)}`}
                        >
                          {recommendation.performance.strength}/10
                        </span>
                      </div>
                      <div className='text-center'>
                        <div className='flex items-center justify-center gap-1 mb-1'>
                          {getPerformanceIcon('workability')}
                          <span className='text-xs text-muted-foreground'>工作性</span>
                        </div>
                        <span
                          className={`text-sm font-medium ${getPerformanceColor(recommendation.performance.workability)}`}
                        >
                          {recommendation.performance.workability}/10
                        </span>
                      </div>
                      <div className='text-center'>
                        <div className='flex items-center justify-center gap-1 mb-1'>
                          {getPerformanceIcon('durability')}
                          <span className='text-xs text-muted-foreground'>耐久性</span>
                        </div>
                        <span
                          className={`text-sm font-medium ${getPerformanceColor(recommendation.performance.durability)}`}
                        >
                          {recommendation.performance.durability}/10
                        </span>
                      </div>
                      <div className='text-center'>
                        <div className='flex items-center justify-center gap-1 mb-1'>
                          {getPerformanceIcon('economy')}
                          <span className='text-xs text-muted-foreground'>经济性</span>
                        </div>
                        <span
                          className={`text-sm font-medium ${getPerformanceColor(recommendation.performance.economy)}`}
                        >
                          {recommendation.performance.economy}/10
                        </span>
                      </div>
                    </div>

                    {/* 特点标签 */}
                    <div className='flex flex-wrap gap-1'>
                      {recommendation.features.slice(0, 3).map((feature, index) => (
                        <Badge key={index} variant='secondary' className='text-xs'>
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* 右侧：详细信息 */}
          <div className='w-80 flex flex-col'>
            {selectedRecommendation ? (
              <>
                <Card className='flex-1 overflow-hidden'>
                  <CardHeader>
                    <CardTitle className='flex items-center gap-2'>
                      <CheckCircle className='w-5 h-5 text-green-500' />
                      配比详情
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='overflow-y-auto'>
                    {/* 基本信息 */}
                    <div className='space-y-3 mb-4'>
                      <div>
                        <Label className='text-sm font-medium'>强度等级</Label>
                        <p className='text-sm text-muted-foreground'>
                          {selectedRecommendation.strengthGrade}
                        </p>
                      </div>
                      <div>
                        <Label className='text-sm font-medium'>目标强度</Label>
                        <p className='text-sm text-muted-foreground'>
                          {selectedRecommendation.targetStrength} MPa
                        </p>
                      </div>
                      <div>
                        <Label className='text-sm font-medium'>估算成本</Label>
                        <p className='text-sm text-muted-foreground'>
                          ¥{selectedRecommendation.estimatedCost}/m³
                        </p>
                      </div>
                    </div>

                    <Separator className='my-4' />

                    {/* 主要参数 */}
                    <div className='space-y-3 mb-4'>
                      <h4 className='font-medium'>主要参数</h4>
                      <div className='grid grid-cols-2 gap-2 text-sm'>
                        <div>
                          <span className='text-muted-foreground'>水胶比:</span>
                          <span className='ml-1 font-medium'>
                            {selectedRecommendation.calculationParams.waterCementRatio}
                          </span>
                        </div>
                        <div>
                          <span className='text-muted-foreground'>砂率:</span>
                          <span className='ml-1 font-medium'>
                            {selectedRecommendation.calculationParams.sandRatio}%
                          </span>
                        </div>
                        <div>
                          <span className='text-muted-foreground'>水泥:</span>
                          <span className='ml-1 font-medium'>
                            {selectedRecommendation.calculationParams.cementContent}kg
                          </span>
                        </div>
                        <div>
                          <span className='text-muted-foreground'>用水量:</span>
                          <span className='ml-1 font-medium'>
                            {selectedRecommendation.calculationParams.waterContent}kg
                          </span>
                        </div>
                      </div>
                    </div>

                    <Separator className='my-4' />

                    {/* 材料组成 */}
                    <div className='space-y-3 mb-4'>
                      <h4 className='font-medium'>材料组成</h4>
                      <div className='space-y-2'>
                        {selectedRecommendation.materials.map(material => (
                          <div key={material.id} className='flex justify-between text-sm'>
                            <span className='text-muted-foreground'>{material.name}:</span>
                            <span className='font-medium'>
                              {material.designValue.toFixed(1)} {material.unit}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator className='my-4' />

                    {/* 适用场景 */}
                    <div className='space-y-3 mb-4'>
                      <h4 className='font-medium'>适用场景</h4>
                      <div className='flex flex-wrap gap-1'>
                        {selectedRecommendation.applications.map((app, index) => (
                          <Badge key={index} variant='outline' className='text-xs'>
                            {app}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* 特点 */}
                    <div className='space-y-3'>
                      <h4 className='font-medium'>配比特点</h4>
                      <div className='flex flex-wrap gap-1'>
                        {selectedRecommendation.features.map((feature, index) => (
                          <Badge key={index} variant='secondary' className='text-xs'>
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 应用按钮 */}
                <div className='mt-4 flex gap-2'>
                  <Button variant='outline' onClick={onClose} className='flex-1'>
                    取消
                  </Button>
                  <Button onClick={handleApplyRecommendation} className='flex-1'>
                    <ArrowRight className='w-4 h-4 mr-2' />
                    应用配比
                  </Button>
                </div>
              </>
            ) : (
              <Card className='flex-1 flex items-center justify-center'>
                <div className='text-center text-muted-foreground'>
                  <Sparkles className='w-12 h-12 mx-auto mb-3 opacity-50' />
                  <p>请选择一个配比推荐</p>
                  <p className='text-sm'>查看详细信息并应用到设计面板</p>
                </div>
              </Card>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
