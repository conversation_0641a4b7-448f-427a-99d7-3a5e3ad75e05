name: 测试流水线

on:
  push:
    branches: [main, master, develop]
  pull_request:
    branches: [main, master, develop]

jobs:
  # 单元测试和集成测试
  unit-tests:
    name: 单元测试
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: 检查依赖同步
        run: |
          if ! npm ci; then
            echo "⚠️ package-lock.json 与 package.json 不同步，尝试重新安装..."
            npm install
          fi

      - name: 类型检查
        run: npm run typecheck

      - name: 代码格式检查
        run: npm run format:check

      - name: ESLint检查
        run: npm run lint

      - name: 运行单元测试
        run: npm run test:ci

      - name: 上传测试覆盖率
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

      - name: 上传测试结果
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-${{ matrix.node-version }}
          path: |
            coverage/
            test-results/

  # 性能测试
  performance-tests:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: unit-tests

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: 安装依赖
        run: npm ci || npm install

      - name: 运行性能测试
        run: npm run test:performance

      - name: 上传性能测试结果
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-test-results
          path: test-results/

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: 安装依赖
        run: npm ci || npm install

      - name: 运行 npm audit
        run: npm audit --audit-level moderate --registry=https://registry.npmjs.org/

      - name: 运行 Snyk 安全扫描
        uses: snyk/actions/node@master
        continue-on-error: true
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest

    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: 安装依赖
        run: npm ci || npm install

      - name: 运行测试生成覆盖率
        run: npm run test:coverage

      - name: SonarCloud 扫描
        uses: SonarSource/sonarcloud-github-action@master
        continue-on-error: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # 构建验证
  build-verification:
    name: 构建验证
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: 安装依赖
        run: npm ci || npm install

      - name: 构建应用
        run: npm run build

      - name: 验证构建产物
        run: |
          ls -la .next/
          test -f .next/BUILD_ID

      - name: 包体积分析
        run: npm run analyze

      - name: 上传构建产物
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts-${{ matrix.node-version }}
          path: |
            .next/
            out/

  # 测试报告汇总
  test-summary:
    name: 测试报告汇总
    runs-on: ubuntu-latest
    needs: [unit-tests, performance-tests]
    if: always()

    steps:
      - name: 下载所有测试结果
        uses: actions/download-artifact@v4

      - name: 生成测试报告
        run: |
          echo "# 测试报告汇总" > test-summary.md
          echo "" >> test-summary.md
          echo "## 单元测试结果" >> test-summary.md
          echo "- Node.js 20.x: ${{ needs.unit-tests.result }}" >> test-summary.md
          echo "" >> test-summary.md
          echo "## 性能测试结果" >> test-summary.md
          echo "- 状态: ${{ needs.performance-tests.result }}" >> test-summary.md

      - name: 发布测试报告
        uses: actions/upload-artifact@v4
        with:
          name: test-summary
          path: test-summary.md

  # 通知
  notify:
    name: 通知
    runs-on: ubuntu-latest
    needs: [unit-tests, performance-tests, security-scan, code-quality, build-verification]
    if: always()

    steps:
      - name: 发送成功通知
        if: ${{ needs.unit-tests.result == 'success'  }}
        run: |
          echo "✅ 所有测试通过！"

      - name: 发送失败通知
        if: ${{ needs.unit-tests.result == 'failure' }}
        run: |
          echo "❌ 测试失败，请检查测试结果"
          exit 1
