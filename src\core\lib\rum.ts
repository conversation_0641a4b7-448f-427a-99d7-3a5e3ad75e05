/**
 * 真实用户监控 (Real User Monitoring) 系统
 * 收集和分析真实用户的性能数据和行为数据
 */
import { performanceLogger } from './logger';

// RUM 数据类型定义
export interface RUMMetrics {
  // 页面性能指标
  pageLoad: {
    fcp: number; // First Contentful Paint
    lcp: number; // Largest Contentful Paint
    fid: number; // First Input Delay
    cls: number; // Cumulative Layout Shift
    ttfb: number; // Time to First Byte
    domContentLoaded: number;
    loadComplete: number;
  };

  // 用户行为指标
  userBehavior: {
    sessionId: string;
    userId?: string;
    pageViews: number;
    clickCount: number;
    scrollDepth: number;
    timeOnPage: number;
    bounceRate: boolean;
  };

  // 技术指标
  technical: {
    userAgent: string;
    viewport: { width: number; height: number };
    connection: string;
    deviceMemory?: number;
    hardwareConcurrency?: number;
  };

  // 错误信息
  errors: {
    jsErrors: Array<{
      message: string;
      stack?: string;
      timestamp: number;
      url: string;
      line: number;
      column: number;
    }>;
    resourceErrors: Array<{
      url: string;
      type: string;
      timestamp: number;
    }>;
  };
}

export interface RUMConfig {
  enabled: boolean;
  sampleRate: number; // 采样率 0-1
  endpoint?: string; // 数据上报端点
  bufferSize: number; // 缓冲区大小
  flushInterval: number; // 上报间隔(ms)
  enableUserBehavior: boolean;
  enablePerformance: boolean;
  enableErrors: boolean;
}

class RUMCollector {
  private config: RUMConfig;
  private metrics: Partial<RUMMetrics> = {};
  private buffer: any[] = [];
  private sessionId: string;
  private startTime: number;
  private observers: PerformanceObserver[] = [];
  private flushTimer?: NodeJS.Timeout;

  constructor(config: Partial<RUMConfig> = {}) {
    this.config = {
      enabled: process.env.NODE_ENV === 'production',
      sampleRate: 0.1, // 10% 采样率
      bufferSize: 100,
      flushInterval: 30000, // 30秒
      enableUserBehavior: true,
      enablePerformance: true,
      enableErrors: true,
      ...config,
    };

    this.sessionId = this.generateSessionId();
    this.startTime = Date.now();

    if (this.config.enabled && this.shouldSample()) {
      this.initialize();
    }
  }

  private generateSessionId(): string {
    return `rum_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private shouldSample(): boolean {
    return Math.random() < this.config.sampleRate;
  }

  private initialize(): void {
    if (typeof window === 'undefined') return;

    this.initializeMetrics();

    if (this.config.enablePerformance) {
      this.collectPerformanceMetrics();
    }

    if (this.config.enableUserBehavior) {
      this.collectUserBehavior();
    }

    if (this.config.enableErrors) {
      this.collectErrors();
    }

    // 定期上报数据
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);

    // 页面卸载时上报数据
    window.addEventListener('beforeunload', () => {
      this.flush(true);
    });
  }

  private initializeMetrics(): void {
    this.metrics = {
      pageLoad: {
        fcp: 0,
        lcp: 0,
        fid: 0,
        cls: 0,
        ttfb: 0,
        domContentLoaded: 0,
        loadComplete: 0,
      },
      userBehavior: {
        sessionId: this.sessionId,
        pageViews: 1,
        clickCount: 0,
        scrollDepth: 0,
        timeOnPage: 0,
        bounceRate: false,
      },
      technical: {
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        connection: (navigator as any).connection?.effectiveType || 'unknown',
        deviceMemory: (navigator as any).deviceMemory,
        hardwareConcurrency: navigator.hardwareConcurrency,
      },
      errors: {
        jsErrors: [],
        resourceErrors: [],
      },
    };
  }

  private collectPerformanceMetrics(): void {
    // 收集 Web Vitals
    this.observeWebVitals();

    // 收集导航时间
    this.collectNavigationTiming();

    // 收集资源时间
    this.collectResourceTiming();
  }

  private observeWebVitals(): void {
    // FCP (First Contentful Paint)
    const fcpObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry && this.metrics.pageLoad) {
        this.metrics.pageLoad.fcp = fcpEntry.startTime;
      }
    });
    fcpObserver.observe({ entryTypes: ['paint'] });
    this.observers.push(fcpObserver);

    // LCP (Largest Contentful Paint)
    const lcpObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (lastEntry && this.metrics.pageLoad) {
        this.metrics.pageLoad.lcp = lastEntry.startTime;
      }
    });
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    this.observers.push(lcpObserver);

    // FID (First Input Delay)
    const fidObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (this.metrics.pageLoad) {
          this.metrics.pageLoad.fid = entry.processingStart - entry.startTime;
        }
      });
    });
    fidObserver.observe({ entryTypes: ['first-input'] });
    this.observers.push(fidObserver);

    // CLS (Cumulative Layout Shift)
    let clsValue = 0;
    const clsObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          if (this.metrics.pageLoad) {
            this.metrics.pageLoad.cls = clsValue;
          }
        }
      });
    });
    clsObserver.observe({ entryTypes: ['layout-shift'] });
    this.observers.push(clsObserver);
  }

  private collectNavigationTiming(): void {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType(
        'navigation'
      )[0] as PerformanceNavigationTiming;
      if (navigation && this.metrics.pageLoad) {
        this.metrics.pageLoad.ttfb = navigation.responseStart - navigation.requestStart;
        this.metrics.pageLoad.domContentLoaded =
          navigation.domContentLoadedEventEnd - navigation.fetchStart;
        this.metrics.pageLoad.loadComplete = navigation.loadEventEnd - navigation.fetchStart;
      }
    });
  }

  private collectResourceTiming(): void {
    const resourceObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        // 记录慢资源
        if (entry.duration > 1000) {
          // 超过1秒的资源
          this.addToBuffer({
            type: 'slow-resource',
            url: entry.name,
            duration: entry.duration,
            timestamp: Date.now(),
          });
        }
      });
    });
    resourceObserver.observe({ entryTypes: ['resource'] });
    this.observers.push(resourceObserver);
  }

  private collectUserBehavior(): void {
    let clickCount = 0;
    let maxScrollDepth = 0;

    // 点击事件
    document.addEventListener('click', () => {
      clickCount++;
      if (this.metrics.userBehavior) {
        this.metrics.userBehavior.clickCount = clickCount;
      }
    });

    // 滚动深度
    const updateScrollDepth = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      const scrollDepth = Math.round(((scrollTop + windowHeight) / documentHeight) * 100);

      if (scrollDepth > maxScrollDepth) {
        maxScrollDepth = scrollDepth;
        if (this.metrics.userBehavior) {
          this.metrics.userBehavior.scrollDepth = maxScrollDepth;
        }
      }
    };

    window.addEventListener('scroll', updateScrollDepth, { passive: true });

    // 页面停留时间
    setInterval(() => {
      if (this.metrics.userBehavior) {
        this.metrics.userBehavior.timeOnPage = Date.now() - this.startTime;
      }
    }, 1000);
  }

  private collectErrors(): void {
    // JavaScript 错误
    window.addEventListener('error', event => {
      if (this.metrics.errors) {
        this.metrics.errors.jsErrors.push({
          message: event.message,
          stack: event.error?.stack,
          timestamp: Date.now(),
          url: event.filename,
          line: event.lineno,
          column: event.colno,
        });
      }
    });

    // Promise 拒绝错误
    window.addEventListener('unhandledrejection', event => {
      if (this.metrics.errors) {
        this.metrics.errors.jsErrors.push({
          message: `Unhandled Promise Rejection: ${event.reason}`,
          timestamp: Date.now(),
          url: window.location.href,
          line: 0,
          column: 0,
        });
      }
    });

    // 资源加载错误
    window.addEventListener(
      'error',
      event => {
        if (event.target !== window && this.metrics.errors) {
          this.metrics.errors.resourceErrors.push({
            url: (event.target as any)?.src || (event.target as any)?.href || 'unknown',
            type: (event.target as any)?.tagName || 'unknown',
            timestamp: Date.now(),
          });
        }
      },
      true
    );
  }

  private addToBuffer(data: any): void {
    this.buffer.push({
      ...data,
      sessionId: this.sessionId,
      timestamp: Date.now(),
    });

    if (this.buffer.length >= this.config.bufferSize) {
      this.flush();
    }
  }

  private flush(immediate = false): void {
    if (this.buffer.length === 0 && !immediate) return;

    const data = {
      sessionId: this.sessionId,
      metrics: this.metrics,
      events: [...this.buffer],
      timestamp: Date.now(),
    };

    // 记录到日志系统
    performanceLogger.info('RUM data collected', {
      sessionId: this.sessionId,
      metricsCount: Object.keys(this.metrics).length,
      eventsCount: this.buffer.length,
    });

    // 如果配置了端点，发送到服务器
    if (this.config.endpoint) {
      this.sendToServer(data, immediate);
    }

    // 清空缓冲区
    this.buffer = [];
  }

  private sendToServer(data: any, immediate: boolean): void {
    const payload = JSON.stringify(data);

    if (immediate && navigator.sendBeacon) {
      // 页面卸载时使用 sendBeacon
      navigator.sendBeacon(this.config.endpoint!, payload);
    } else {
      // 正常情况使用 fetch
      fetch(this.config.endpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: payload,
        keepalive: true,
      }).catch(error => {
        performanceLogger.error('Failed to send RUM data', error);
      });
    }
  }

  // 手动记录自定义事件
  public recordEvent(eventName: string, data: any): void {
    this.addToBuffer({
      type: 'custom-event',
      name: eventName,
      data,
    });
  }

  // 获取当前指标
  public getMetrics(): Partial<RUMMetrics> {
    return { ...this.metrics };
  }

  // 清理资源
  public destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];

    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    this.flush(true);
  }
}

// 全局 RUM 实例
export const rum = new RUMCollector({
  enabled: process.env.NODE_ENV === 'production',
  sampleRate: 0.1,
  endpoint: process.env['NEXT_PUBLIC_RUM_ENDPOINT'],
});

// 便捷方法
export const recordUserAction = (action: string, data?: any) => {
  rum.recordEvent('user-action', { action, ...data });
};

export const recordPerformanceMark = (name: string) => {
  if (typeof window !== 'undefined' && window.performance) {
    performance.mark(name);
    rum.recordEvent('performance-mark', { name, timestamp: Date.now() });
  }
};

export const recordCustomMetric = (name: string, value: number, unit?: string) => {
  rum.recordEvent('custom-metric', { name, value, unit });
};
