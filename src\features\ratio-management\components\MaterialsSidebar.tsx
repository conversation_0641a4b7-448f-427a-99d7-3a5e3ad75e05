/**
 * 物料侧边栏组件
 * 显示各站点的物料信息
 */

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/table';
import { Badge } from '@/shared/components/badge';
import { mockMaterials } from '@/infrastructure/api/mock/mock-data';
import type { MaterialsSidebarProps } from '@/core/types/ratio';

/**
 * 物料侧边栏组件
 */
export const MaterialsSidebar = React.memo<MaterialsSidebarProps>(function MaterialsSidebar() {
  // 按站点分组的物料
  const groupedMaterials = useMemo(() => {
    return mockMaterials.reduce(
      (acc, mat) => {
        const key = (mat as any).station || '未知站点';
        if (!acc[key]) acc[key] = [];
        acc[key].push(mat);
        return acc;
      },
      {} as Record<string, any[]>
    );
  }, []);

  // 按类别分组的物料
  const materialsByCategory = useMemo(() => {
    return mockMaterials.reduce(
      (acc, mat) => {
        const category = (mat as any).category || 'other';
        if (!acc[category]) acc[category] = [];
        acc[category].push(mat);
        return acc;
      },
      {} as Record<string, any[]>
    );
  }, []);

  // 获取类别显示名称
  const getCategoryDisplayName = (category: string) => {
    const categoryNames: Record<string, string> = {
      cementitious: '胶凝材料',
      aggregate: '骨料',
      admixture: '外加剂',
      water: '水',
      other: '其他',
    };
    return categoryNames[category] || category;
  };

  // 获取材料状态颜色
  const getMaterialStatusColor = (material: any) => {
    if (material.stock && material.stock < 100) {
      return 'destructive'; // 库存不足
    }
    if (material.quality === 'excellent') {
      return 'default'; // 质量优良
    }
    return 'secondary'; // 普通状态
  };

  return (
    <Card className='h-full overflow-y-auto'>
      <CardHeader className='p-1 sticky top-0 bg-background z-10 border-b'>
        <CardTitle className='text-sm'>物料仓</CardTitle>
      </CardHeader>

      <CardContent className='p-1'>
        {/* 按站点显示 */}
        <div className='mb-4'>
          <h4 className='font-semibold text-xs p-1 bg-muted/50 rounded mb-2'>按站点分组</h4>
          {Object.entries(groupedMaterials).map(([station, items]) => (
            <div key={station} className='mb-3'>
              <h5 className='font-medium text-xs p-1 bg-blue-50 rounded text-blue-700 mb-1'>
                {station} ({items.length}种)
              </h5>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className='w-[80px] h-6 text-xs'>物料</TableHead>
                    <TableHead className='text-xs h-6'>规格</TableHead>
                    <TableHead className='text-xs h-6'>仓名</TableHead>
                    <TableHead className='text-xs h-6'>状态</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {items.map(item => (
                    <TableRow key={item.id} className='h-6 text-xs'>
                      <TableCell className='font-medium'>{item.materialType}</TableCell>
                      <TableCell title={item.spec}>
                        {item.spec.length > 10 ? `${item.spec.substring(0, 10)}...` : item.spec}
                      </TableCell>
                      <TableCell>{item.storageBin}</TableCell>
                      <TableCell>
                        <Badge variant={getMaterialStatusColor(item)} className='text-xs px-1 py-0'>
                          {item.stock && item.stock < 100 ? '库存低' : '正常'}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ))}
        </div>

        {/* 按类别显示 */}
        <div>
          <h4 className='font-semibold text-xs p-1 bg-muted/50 rounded mb-2'>按类别分组</h4>
          {Object.entries(materialsByCategory).map(([category, items]) => (
            <div key={category} className='mb-3'>
              <h5 className='font-medium text-xs p-1 bg-green-50 rounded text-green-700 mb-1'>
                {getCategoryDisplayName(category)} ({items.length}种)
              </h5>
              <div className='grid grid-cols-1 gap-1'>
                {items.map(item => (
                  <div
                    key={item.id}
                    className='p-2 border rounded-sm hover:bg-muted/50 cursor-pointer transition-colors'
                    title={`${item.materialType} - ${item.spec}`}
                  >
                    <div className='flex justify-between items-start'>
                      <div className='flex-1 min-w-0'>
                        <div className='font-medium text-xs truncate'>{item.materialType}</div>
                        <div className='text-xs text-muted-foreground truncate'>{item.spec}</div>
                        <div className='text-xs text-muted-foreground'>{item.storageBin}</div>
                      </div>
                      <Badge
                        variant={getMaterialStatusColor(item)}
                        className='text-xs px-1 py-0 ml-1'
                      >
                        {item.stock && item.stock < 100 ? '低' : '正常'}
                      </Badge>
                    </div>

                    {/* 额外信息 */}
                    {(item.density || item.cost) && (
                      <div className='mt-1 pt-1 border-t border-muted/30'>
                        <div className='flex justify-between text-xs text-muted-foreground'>
                          {item.density && <span>密度: {item.density}kg/m³</span>}
                          {item.cost && <span>价格: ¥{item.cost}/t</span>}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* 统计信息 */}
        <div className='mt-4 p-2 bg-muted/30 rounded'>
          <h5 className='font-medium text-xs mb-2'>统计信息</h5>
          <div className='grid grid-cols-2 gap-2 text-xs'>
            <div>
              <span className='text-muted-foreground'>总材料数:</span>
              <span className='font-medium ml-1'>{mockMaterials.length}</span>
            </div>
            <div>
              <span className='text-muted-foreground'>站点数:</span>
              <span className='font-medium ml-1'>{Object.keys(groupedMaterials).length}</span>
            </div>
            <div>
              <span className='text-muted-foreground'>类别数:</span>
              <span className='font-medium ml-1'>{Object.keys(materialsByCategory).length}</span>
            </div>
            <div>
              <span className='text-muted-foreground'>库存低:</span>
              <span className='font-medium ml-1 text-red-600'>
                {mockMaterials.filter(m => m.stock && m.stock < 100).length}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});
