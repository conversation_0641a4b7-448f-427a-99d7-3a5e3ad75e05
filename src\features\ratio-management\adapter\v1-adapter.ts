/**
 * V1版本适配器
 * 将核心配比模块适配到V1版本的接口
 */

import { useMemo } from 'react';
import type { UseRatioCoreOptions } from '@/features/ratio-management/hooks/ratio-core/useRatioCore';
import { useRatioCore } from '@/features/ratio-management/hooks/ratio-core/useRatioCore';

import {
  convertV1MaterialToCore,
  convertCoreToV1Material,
  convertCalculationParamsToCore,
  convertCoreCalculationParamsToVersion,
} from '@/core/utils/ratio-core/ratioDataConverter';

import type { LegacyRatioMaterial } from '@/core/types/ratio';

/**
 * V1版本的useRatioManagement Hook接口
 */
export interface UseRatioManagementOptions {
  taskId: string;
  autoLoad?: boolean;
}

export interface UseRatioManagementReturn {
  // 核心数据 - V1格式
  currentRatio: any;
  materials: LegacyRatioMaterial[];
  calculationParams: any;
  calculationResults: any;

  // 状态
  isLoading: boolean;
  isCalculating: boolean;
  isSaving: boolean;
  error: string | null;
  isDirty: boolean;

  // 数据操作
  loadRatio: (taskId?: string) => Promise<void>;
  saveRatio: () => Promise<void>;
  clearRatio: () => void;

  // 材料操作
  addMaterial: (material: LegacyRatioMaterial) => void;
  removeMaterial: (materialId: string) => void;
  updateMaterial: (materialId: string, updates: Partial<LegacyRatioMaterial>) => void;

  // 计算操作
  updateCalculationParams: (params: any) => void;
  calculate: () => Promise<void>;

  // 工具方法
  canSave: () => boolean;
  canCalculate: () => boolean;
  hasUnsavedChanges: () => boolean;
}

/**
 * V1版本适配器Hook
 * 将核心配比模块适配为V1版本的useRatioManagement接口
 */
export function useRatioManagement(options: UseRatioManagementOptions): UseRatioManagementReturn {
  const { taskId, autoLoad = true } = options;

  // 使用核心Hook
  const coreOptions: UseRatioCoreOptions = {
    taskId,
    version: 'v1',
    autoLoad,
    autoSave: false, // V1版本默认不自动保存
    useMockData: true,
    apiEndpoint: '/api/ratio-v1',
    cacheEnabled: true,
    cacheTTL: 5 * 60 * 1000,
  };

  const core = useRatioCore(coreOptions);

  // ==================== 数据转换 ====================

  // 将核心材料转换为V1格式
  const materials = useMemo(() => {
    return core.materials.map(material => convertCoreToV1Material(material));
  }, [core.materials]);

  // 将核心计算参数转换为V1格式
  const calculationParams = useMemo(() => {
    if (!core.calculationParams) return null;
    return convertCoreCalculationParamsToVersion(core.calculationParams, 'v1');
  }, [core.calculationParams]);

  // 将核心计算结果转换为V1格式
  const calculationResults = useMemo(() => {
    if (!core.calculationResults) return null;

    // V1版本的简化结果格式
    return {
      totalWeight: core.calculationResults.totalWeight,
      materials: core.calculationResults.materials,
      qualityScore: core.calculationResults.qualityScore,
      strengthPrediction: core.calculationResults.strengthPrediction,
      warnings: core.calculationResults.warnings,
      calculationTime: core.calculationResults.calculationTime,
      timestamp: core.calculationResults.timestamp,
    };
  }, [core.calculationResults]);

  // 当前配比数据（V1格式）
  const currentRatio = useMemo(() => {
    if (!core.currentRatio) return null;

    return {
      id: core.currentRatio.id,
      taskId: core.currentRatio.taskId,
      name: core.currentRatio.name,
      description: core.currentRatio.description,
      materials,
      calculationParams,
      calculationResults,
      version: 'v1',
      createdAt: core.currentRatio.createdAt,
      updatedAt: core.currentRatio.updatedAt,
      status: core.currentRatio.status,
    };
  }, [core.currentRatio, materials, calculationParams, calculationResults]);

  // ==================== 材料操作适配 ====================

  const addMaterial = (material: LegacyRatioMaterial) => {
    const coreMaterial = convertV1MaterialToCore(material);
    core.addMaterial(coreMaterial);
  };

  const removeMaterial = (materialId: string) => {
    core.removeMaterial(materialId);
  };

  const updateMaterial = (materialId: string, updates: Partial<LegacyRatioMaterial>) => {
    // 将V1格式的更新转换为核心格式
    const coreUpdates: any = {};

    // 映射 LegacyRatioMaterial 的实际属性
    if (updates.theoryAmount !== undefined) coreUpdates.designValue = updates.theoryAmount;
    if (updates.actualAmount !== undefined) coreUpdates.actualValue = updates.actualAmount;
    if (updates.designValue !== undefined) coreUpdates.designValue = updates.designValue;

    // 其他属性需要从扩展接口或默认值获取
    // 这些属性在 LegacyRatioMaterial 中不存在，需要特殊处理
    const extendedUpdates = updates as any;
    if (extendedUpdates.unit !== undefined) coreUpdates.unit = extendedUpdates.unit;
    if (extendedUpdates.density !== undefined) coreUpdates.density = extendedUpdates.density;
    if (extendedUpdates.specificGravity !== undefined)
      coreUpdates.specificGravity = extendedUpdates.specificGravity;
    if (extendedUpdates.grade !== undefined) coreUpdates.grade = extendedUpdates.grade;
    if (extendedUpdates.supplier !== undefined) coreUpdates.supplier = extendedUpdates.supplier;
    if (extendedUpdates.ratio !== undefined) coreUpdates.ratio = extendedUpdates.ratio;
    if (extendedUpdates.percentage !== undefined)
      coreUpdates.percentage = extendedUpdates.percentage;

    core.updateMaterial(materialId, coreUpdates);
  };

  // ==================== 计算操作适配 ====================

  const updateCalculationParams = (params: any) => {
    const coreParams = convertCalculationParamsToCore(params, 'v1');
    core.updateCalculationParams(coreParams);
  };

  const calculate = async () => {
    await core.calculate();
  };

  // ==================== 返回适配后的接口 ====================

  return {
    // 核心数据 - V1格式
    currentRatio,
    materials,
    calculationParams,
    calculationResults,

    // 状态
    isLoading: core.isLoading,
    isCalculating: core.isCalculating,
    isSaving: core.isSaving,
    error: core.error,
    isDirty: core.isDirty,

    // 数据操作
    loadRatio: (taskId?: string) => core.loadRatio(taskId || ''),
    saveRatio: core.saveRatio,
    clearRatio: core.clearRatio,

    // 材料操作
    addMaterial,
    removeMaterial,
    updateMaterial,

    // 计算操作
    updateCalculationParams,
    calculate,

    // 工具方法
    canSave: core.canSave,
    canCalculate: core.canCalculate,
    hasUnsavedChanges: core.hasUnsavedChanges,
  };
}
