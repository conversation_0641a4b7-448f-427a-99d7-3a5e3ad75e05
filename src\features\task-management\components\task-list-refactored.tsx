// src/components/sections/task-list/task-list-refactored.tsx
'use client';

/**
 * 重构后的任务列表组件 - 使用分层架构
 *
 * 架构说明：
 * - 使用容器组件模式，将业务逻辑分离到Hook中
 * - UI组件只负责展示和用户交互
 * - 业务逻辑和数据操作通过专门的Hook封装
 * - 模态框管理通过专门的管理器组件处理
 */

import { TaskListContainer } from './TaskListContainer';

export interface TaskListProps {
  productionLineCount: number;
  /** 是否使用悬浮头部 */
  useFloatingHeader?: boolean;
  /** 悬浮头部初始位置 */
  floatingHeaderPosition?: { x: number; y: number };
  /** 悬浮头部位置变化回调 */
  onFloatingHeaderPositionChange?: (position: { x: number; y: number }) => void;
}

/**
 * 重构后的任务列表主组件
 * 现在只是一个简单的容器，所有业务逻辑都已分离到专门的Hook和组件中
 */
export function TaskList(props: TaskListProps = { productionLineCount: 4 }) {
  return <TaskListContainer {...props} />;
}
