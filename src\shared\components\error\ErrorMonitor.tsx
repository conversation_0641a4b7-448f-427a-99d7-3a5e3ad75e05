/**
 * 错误监控组件
 * 提供实时错误统计、趋势分析和错误报告功能
 */

'use client';

import React, { useState } from 'react';
import {
  AlertTriangle,
  BarChart3,
  Bug,
  Download,
  Filter,
  RefreshCw,
  TrendingUp,
  Wifi,
  WifiOff,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/card';
import { Progress } from '@/shared/components/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/tabs';
import { useErrorStats } from '@/shared/hooks/useErrorHandler';
import { ErrorCategory, ErrorSeverity } from '@/core/types/error';
import { safeDownloadFile } from '@/core/utils/dom-safe-operations';

interface ErrorMonitorProps {
  className?: string;
  showDetails?: boolean;
}

export function ErrorMonitor({ className, showDetails = true }: ErrorMonitorProps) {
  const { stats, clearStats, refreshStats } = useErrorStats();
  const [selectedCategory, setSelectedCategory] = useState<ErrorCategory | 'all'>('all');
  const [selectedSeverity, setSelectedSeverity] = useState<ErrorSeverity | 'all'>('all');

  const filteredErrors = stats.recentErrors.filter(error => {
    const categoryMatch = selectedCategory === 'all' || error.category === selectedCategory;
    const severityMatch = selectedSeverity === 'all' || error.severity === selectedSeverity;
    return categoryMatch && severityMatch;
  });

  const getSeverityColor = (severity: ErrorSeverity) => {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 'bg-blue-100 text-blue-800';
      case ErrorSeverity.MEDIUM:
        return 'bg-yellow-100 text-yellow-800';
      case ErrorSeverity.HIGH:
        return 'bg-orange-100 text-orange-800';
      case ErrorSeverity.CRITICAL:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: ErrorCategory) => {
    switch (category) {
      case ErrorCategory.NETWORK:
        return <Wifi className='w-4 h-4' />;
      case ErrorCategory.API:
        return <Bug className='w-4 h-4' />;
      case ErrorCategory.COMPONENT:
        return <AlertTriangle className='w-4 h-4' />;
      default:
        return <AlertTriangle className='w-4 h-4' />;
    }
  };

  const exportErrorReport = () => {
    const report = {
      timestamp: new Date().toISOString(),
      stats,
      recentErrors: filteredErrors,
      filters: {
        category: selectedCategory,
        severity: selectedSeverity,
      },
    };

    // 使用安全的下载函数
    safeDownloadFile(
      JSON.stringify(report, null, 2),
      `error-report-${new Date().toISOString().split('T')[0]}.json`,
      'application/json'
    );
  };

  const getHealthScore = () => {
    const totalErrors = stats.totalErrors;
    if (totalErrors === 0) return 100;

    const criticalWeight = (stats.errorsBySeverity[ErrorSeverity.CRITICAL] || 0) * 4;
    const highWeight = (stats.errorsBySeverity[ErrorSeverity.HIGH] || 0) * 3;
    const mediumWeight = (stats.errorsBySeverity[ErrorSeverity.MEDIUM] || 0) * 2;
    const lowWeight = (stats.errorsBySeverity[ErrorSeverity.LOW] || 0) * 1;

    const weightedErrors = criticalWeight + highWeight + mediumWeight + lowWeight;
    const maxPossibleWeight = totalErrors * 4;

    return Math.max(0, Math.round(100 - (weightedErrors / maxPossibleWeight) * 100));
  };

  const healthScore = getHealthScore();

  return (
    <div className={className}>
      <Tabs defaultValue='overview' className='w-full'>
        <TabsList className='grid w-full grid-cols-3'>
          <TabsTrigger value='overview'>概览</TabsTrigger>
          <TabsTrigger value='details'>详情</TabsTrigger>
          <TabsTrigger value='trends'>趋势</TabsTrigger>
        </TabsList>

        <TabsContent value='overview' className='space-y-4'>
          {/* 健康评分 */}
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>系统健康评分</CardTitle>
              <TrendingUp className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='flex items-center space-x-4'>
                <div className='flex-1'>
                  <Progress value={healthScore} className='w-full' />
                </div>
                <div className='text-2xl font-bold'>{healthScore}%</div>
              </div>
              <p className='text-xs text-muted-foreground mt-2'>基于错误频率和严重程度计算</p>
            </CardContent>
          </Card>

          {/* 错误统计卡片 */}
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>总错误数</CardTitle>
                <AlertTriangle className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{stats.totalErrors}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>严重错误</CardTitle>
                <AlertTriangle className='h-4 w-4 text-red-500' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-red-600'>
                  {(stats.errorsBySeverity[ErrorSeverity.CRITICAL] || 0) +
                    (stats.errorsBySeverity[ErrorSeverity.HIGH] || 0)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>网络错误</CardTitle>
                <WifiOff className='h-4 w-4 text-orange-500' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-orange-600'>
                  {stats.errorsByCategory[ErrorCategory.NETWORK] || 0}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>API错误</CardTitle>
                <Bug className='h-4 w-4 text-blue-500' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-blue-600'>
                  {stats.errorsByCategory[ErrorCategory.API] || 0}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='details' className='space-y-4'>
          {/* 过滤器 */}
          <div className='flex flex-wrap gap-4 items-center'>
            <div className='flex items-center gap-2'>
              <Filter className='h-4 w-4' />
              <span className='text-sm font-medium'>过滤器:</span>
            </div>

            <Select
              value={selectedCategory}
              onValueChange={value => setSelectedCategory(value as ErrorCategory | 'all')}
            >
              <SelectTrigger className='w-[150px]'>
                <SelectValue placeholder='选择类别' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>所有类别</SelectItem>
                <SelectItem value={ErrorCategory.NETWORK}>网络</SelectItem>
                <SelectItem value={ErrorCategory.API}>API</SelectItem>
                <SelectItem value={ErrorCategory.COMPONENT}>组件</SelectItem>
                <SelectItem value={ErrorCategory.VALIDATION}>验证</SelectItem>
                <SelectItem value={ErrorCategory.BUSINESS}>业务</SelectItem>
                <SelectItem value={ErrorCategory.SYSTEM}>系统</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={selectedSeverity}
              onValueChange={value => setSelectedSeverity(value as ErrorSeverity | 'all')}
            >
              <SelectTrigger className='w-[150px]'>
                <SelectValue placeholder='选择严重程度' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>所有级别</SelectItem>
                <SelectItem value={ErrorSeverity.LOW}>低</SelectItem>
                <SelectItem value={ErrorSeverity.MEDIUM}>中</SelectItem>
                <SelectItem value={ErrorSeverity.HIGH}>高</SelectItem>
                <SelectItem value={ErrorSeverity.CRITICAL}>严重</SelectItem>
              </SelectContent>
            </Select>

            <div className='flex gap-2 ml-auto'>
              <Button variant='outline' size='sm' onClick={refreshStats}>
                <RefreshCw className='h-4 w-4 mr-2' />
                刷新
              </Button>
              <Button variant='outline' size='sm' onClick={exportErrorReport}>
                <Download className='h-4 w-4 mr-2' />
                导出
              </Button>
            </div>
          </div>

          {/* 错误列表 */}
          <Card>
            <CardHeader>
              <CardTitle>最近错误 ({filteredErrors.length})</CardTitle>
              <CardDescription>显示最近发生的错误，按时间倒序排列</CardDescription>
            </CardHeader>
            <CardContent>
              {filteredErrors.length === 0 ? (
                <div className='text-center py-8 text-muted-foreground'>
                  <AlertTriangle className='h-8 w-8 mx-auto mb-2 opacity-50' />
                  <p>没有找到匹配的错误记录</p>
                </div>
              ) : (
                <div className='space-y-3'>
                  {filteredErrors.slice(0, 20).map(error => (
                    <div
                      key={error.id}
                      className='flex items-start gap-3 p-3 border rounded-lg hover:bg-muted/50'
                    >
                      <div className='flex-shrink-0 mt-1'>{getCategoryIcon(error.category)}</div>

                      <div className='flex-1 min-w-0'>
                        <div className='flex items-center gap-2 mb-1'>
                          <Badge className={getSeverityColor(error.severity)}>
                            {error.severity}
                          </Badge>
                          <Badge variant='outline'>{error.category}</Badge>
                          <span className='text-xs text-muted-foreground'>
                            {new Date(error.timestamp).toLocaleString()}
                          </span>
                        </div>

                        <p className='text-sm font-medium truncate'>{error.message}</p>
                        <p className='text-xs text-muted-foreground'>
                          错误代码: {error.code} | ID: {error.id}
                        </p>

                        {showDetails && error.context && (
                          <details className='mt-2'>
                            <summary className='text-xs cursor-pointer text-muted-foreground hover:text-foreground'>
                              查看上下文
                            </summary>
                            <pre className='text-xs bg-muted p-2 rounded mt-1 overflow-auto'>
                              {JSON.stringify(error.context, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='trends' className='space-y-4'>
          {/* 错误趋势图 */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <BarChart3 className='h-5 w-5' />
                错误趋势分析
              </CardTitle>
              <CardDescription>显示过去24小时和7天的错误分布情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-6'>
                {/* 24小时趋势 */}
                <div>
                  <h4 className='text-sm font-medium mb-3'>过去24小时</h4>
                  <div className='flex items-end gap-1 h-20'>
                    {stats.errorTrends.hourly.map((count, index) => (
                      <div
                        key={index}
                        className='flex-1 bg-blue-200 rounded-t'
                        style={{
                          height: `${Math.max(4, (count / Math.max(...stats.errorTrends.hourly, 1)) * 100)}%`,
                        }}
                        title={`${index}:00 - ${count} 个错误`}
                      />
                    ))}
                  </div>
                  <div className='flex justify-between text-xs text-muted-foreground mt-1'>
                    <span>0:00</span>
                    <span>12:00</span>
                    <span>23:00</span>
                  </div>
                </div>

                {/* 7天趋势 */}
                <div>
                  <h4 className='text-sm font-medium mb-3'>过去7天</h4>
                  <div className='flex items-end gap-2 h-20'>
                    {stats.errorTrends.daily.map((count, index) => (
                      <div
                        key={index}
                        className='flex-1 bg-orange-200 rounded-t'
                        style={{
                          height: `${Math.max(4, (count / Math.max(...stats.errorTrends.daily, 1)) * 100)}%`,
                        }}
                        title={`${['周日', '周一', '周二', '周三', '周四', '周五', '周六'][index]} - ${count} 个错误`}
                      />
                    ))}
                  </div>
                  <div className='flex justify-between text-xs text-muted-foreground mt-1'>
                    {['周日', '周一', '周二', '周三', '周四', '周五', '周六'].map(day => (
                      <span key={day}>{day}</span>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className='flex justify-end gap-2'>
            <Button variant='outline' onClick={clearStats}>
              清除历史数据
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
