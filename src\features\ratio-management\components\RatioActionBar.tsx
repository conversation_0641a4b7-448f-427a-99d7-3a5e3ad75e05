/**
 * 配比操作栏组件
 * 包含配比页面顶部的操作按钮和选择器
 */

import React from 'react';
import { TestTube, Printer, Container, History, Layers, Settings, RefreshCw } from 'lucide-react';
import { Button } from '@/shared/components/button';
import { Checkbox } from '@/shared/components/checkbox';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { Card } from '@/shared/components/card';
import { mockPlants } from '@/infrastructure/api/mock/mock-data';
import type { RatioActionBarProps } from '@/core/types/ratio';

/**
 * 配比操作栏组件
 */
export const RatioActionBar = React.memo<RatioActionBarProps>(function RatioActionBar({
  onOpenModal,
  taskId,
}) {
  const handleSwitchToModernVersion = () => {
    window.location.href = `/ratio-v2/${taskId}`;
  };

  const handlePrint = () => {
    window.print();
  };

  const handleRefreshSilo = () => {
    // 刷新料仓数据
    console.log('刷新料仓数据');
  };

  const handleOpenModal = (modalName: string) => {
    onOpenModal(modalName);
  };

  return (
    <Card className='flex items-center justify-between p-1 flex-wrap gap-1 flex-shrink-0'>
      {/* 左侧：标题和基本选项 */}
      <div className='flex items-center gap-2 flex-wrap'>
        <h1 className='text-xl font-bold text-primary flex items-center gap-2'>
          <TestTube className='w-6 h-6' />
          <span>砼配比</span>
        </h1>

        <div className='flex items-center space-x-2 pl-4'>
          <Checkbox id='unify-ratio' />
          <Label htmlFor='unify-ratio' className='text-sm whitespace-nowrap'>
            所有站统一配比
          </Label>
        </div>

        <Select defaultValue='plant1'>
          <SelectTrigger className='w-[150px] h-8 text-sm'>
            <SelectValue placeholder='选择搅拌站' />
          </SelectTrigger>
          <SelectContent>
            {mockPlants.map(p => (
              <SelectItem key={p.id} value={p.id}>
                {p.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button variant='outline' size='sm' className='h-7 text-xs'>
          除搅拌站1配比
        </Button>

        {/* 新版本切换按钮 */}
        <Button
          variant='outline'
          size='sm'
          className='h-7 text-xs gap-1 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 text-blue-700 hover:from-blue-100 hover:to-purple-100'
          onClick={handleSwitchToModernVersion}
        >
          <span className='text-xs'>✨</span>
          现代版本
        </Button>
      </div>

      {/* 右侧：操作按钮 */}
      <div className='flex items-center gap-1 flex-wrap'>
        <Button variant='ghost' size='sm' className='gap-1 h-7 text-xs' onClick={handlePrint}>
          <Printer className='w-4 h-4' />
          打印
        </Button>

        <Button
          variant='ghost'
          size='sm'
          className='gap-1 h-7 text-xs'
          onClick={() => onOpenModal('silo')}
        >
          <Container className='w-4 h-4' />
          料仓
        </Button>

        <Button
          variant='ghost'
          size='sm'
          className='gap-1 h-7 text-xs'
          onClick={() => onOpenModal('history')}
        >
          <History className='w-4 h-4' />
          历史
        </Button>

        <Button
          variant='ghost'
          size='sm'
          className='gap-1 h-7 text-xs'
          onClick={() => onOpenModal('mortar')}
        >
          <Layers className='w-4 h-4' />
          砂浆配比
        </Button>

        <Button
          variant='ghost'
          size='sm'
          className='gap-1 h-7 text-xs'
          onClick={() => onOpenModal('settings')}
        >
          <Settings className='w-4 h-4' />
          设置
        </Button>

        <Select>
          <SelectTrigger className='w-[150px] h-8 text-sm'>
            <SelectValue placeholder='选择配比编号' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='c125-00003'>C125-00003</SelectItem>
          </SelectContent>
        </Select>

        <Button variant='ghost' size='sm' className='gap-1 h-7 text-xs' onClick={handleRefreshSilo}>
          <RefreshCw className='w-4 h-4' />
          刷新料仓
        </Button>
      </div>
    </Card>
  );
});
