// src/utils/eventEmitter.ts

type EventCallback = (...args: any[]) => void;

// 创建一个接口来定义事件发射器的方法
interface IEventEmitter {
  on(event: string, callback: EventCallback): void;
  off(event: string, callback: EventCallback): void;
  emit(event: string, ...args: any[]): void;
  removeAllListeners(event?: string): void;
}

class EventEmitter implements IEventEmitter {
  private events: Map<string, EventCallback[]> = new Map();

  on(event: string, callback: EventCallback) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(callback);
  }

  off(event: string, callback: EventCallback) {
    const callbacks = this.events.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(event: string, ...args: any[]) {
    const callbacks = this.events.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(...args));
    }
  }

  removeAllListeners(event?: string) {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }
}

// 创建全局事件发射器实例
export const globalEventEmitter: IEventEmitter = new EventEmitter();

// 定义事件类型
export const EVENTS = {
  SEND_PRODUCTION_INSTRUCTION: 'sendProductionInstruction',
  OPEN_VEHICLE_DISPATCH_MODAL: 'openVehicleDispatchModal',
  OPEN_TASK_PROGRESS_MODAL: 'openTaskProgressModal',
} as const;
