'use client';

import React, { useState } from 'react';

import { But<PERSON> } from '@/shared/components/button';
import { Checkbox } from '@/shared/components/checkbox';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import { ScrollArea } from '@/shared/components/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/tabs';
// 导入统一的配比数据
import {
  mockAdjustmentMaterials,
  mockMaterialDensities,
  mockMortarCoefficients,
  mockUserPermissions,
} from '@/infrastructure/api/mock/ratio-mock-data';
import type {
  AdjustmentMaterial,
  MaterialDensity,
  MortarCoefficient,
  UserPermission,
} from '@/core/types';

interface RatioSettingsModalProps {
  isOpen?: boolean;
  open?: boolean;
  onOpenChangeAction?: (open: boolean) => void;
  onOpenChange?: (open: boolean) => void;
}

// Permissions Tab Component
const PermissionsTab = () => (
  <div className='grid grid-cols-1 md:grid-cols-2 gap-6 mt-4'>
    {/* Left side: Fine-tuning materials */}
    <div className='border p-4 rounded-lg'>
      <h3 className='font-semibold mb-4 text-center'>配比微调料名</h3>
      <ScrollArea className='h-72'>
        <div className='space-y-2 pr-4'>
          <div className='flex items-center gap-4 sticky top-0 bg-background pb-2 border-b'>
            <Label className='flex-1 font-bold'>料名</Label>
            <Label className='w-24 font-bold text-center'>调整范围</Label>
          </div>
          {mockAdjustmentMaterials.map((material: AdjustmentMaterial) => (
            <div key={material.id} className='flex items-center gap-4'>
              <Checkbox id={`mat-${material.id}`} />
              <Label htmlFor={`mat-${material.id}`} className='flex-1'>
                {material.name}
              </Label>
              <Input placeholder='范围' className='w-24 h-8' />
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>

    {/* Right side: User permissions */}
    <div className='border p-4 rounded-lg'>
      <h3 className='font-semibold mb-4 text-center'>用户权限管理</h3>
      <ScrollArea className='h-72'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className='w-[80px]'>用户名</TableHead>
              <TableHead>配比计算书</TableHead>
              <TableHead>配比设计</TableHead>
              <TableHead>配比发送</TableHead>
              <TableHead>配比微调</TableHead>
              <TableHead>配比执行</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mockUserPermissions.map((user: UserPermission) => (
              <TableRow key={user.id}>
                <TableCell className='font-medium'>{user.username}</TableCell>
                {Object.keys(user.permissions).map(key => (
                  <TableCell key={key} className='text-center'>
                    <Checkbox checked={user.permissions[key as keyof typeof user.permissions]} />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </ScrollArea>
    </div>
  </div>
);

// Mortar Settings Tab Component
const MortarSettingsTab: React.FC<{
  coefficients: MortarCoefficient[];
  onUpdate: (id: string, newCoefficient: number) => void;
}> = ({ coefficients, onUpdate }) => (
  <div className='py-4 mt-4'>
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-4'>
      {coefficients.map(material => (
        <div key={material.id} className='grid grid-cols-2 items-center gap-2'>
          <Label htmlFor={`coeff-${material.id}`} className='text-right'>
            {material.name}:
          </Label>
          <Input
            id={`coeff-${material.id}`}
            type='number'
            value={material.coefficient}
            onChange={e => onUpdate(material.id, parseFloat(e.target.value) || 0)}
            className='h-8'
          />
        </div>
      ))}
    </div>
  </div>
);

// Density Settings Tab Component
const DensitySettingsTab: React.FC<{
  densities: MaterialDensity[];
  onUpdate: (id: string, newDensity: number) => void;
}> = ({ densities, onUpdate }) => (
  <div className='py-4 mt-4'>
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-4'>
      {densities.map(material => (
        <div key={material.id} className='grid grid-cols-2 items-center gap-2'>
          <Label htmlFor={`density-${material.id}`} className='text-right'>
            {material.name}默认密度:
          </Label>
          <div className='flex items-center gap-1'>
            <Input
              id={`density-${material.id}`}
              type='number'
              value={material.density}
              onChange={e => onUpdate(material.id, parseFloat(e.target.value) || 0)}
              className='h-8'
            />
            <span className='text-xs text-muted-foreground'>kg/m³</span>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Main Modal Component
export const RatioSettingsModal: React.FC<RatioSettingsModalProps> = ({
  isOpen = false,
  open = false,
  onOpenChangeAction,
  onOpenChange,
}) => {
  const [mortarCoefficients, setMortarCoefficients] =
    useState<MortarCoefficient[]>(mockMortarCoefficients);
  const [materialDensities, setMaterialDensities] =
    useState<MaterialDensity[]>(mockMaterialDensities);

  const handleCoefficientUpdate = (id: string, newCoefficient: number) => {
    setMortarCoefficients(prev =>
      prev.map(mat => (mat.id === id ? { ...mat, coefficient: newCoefficient } : mat))
    );
  };

  const handleDensityUpdate = (id: string, newDensity: number) => {
    setMaterialDensities(prev =>
      prev.map(mat => (mat.id === id ? { ...mat, density: newDensity } : mat))
    );
  };

  // 统一处理open状态和回调
  const modalOpen = isOpen || open;
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChangeAction?.(newOpen);
    onOpenChange?.(newOpen);
  };

  return (
    <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
      <DialogContent className='max-w-5xl'>
        <DialogHeader>
          <DialogTitle>参数设置</DialogTitle>
        </DialogHeader>
        <Tabs defaultValue='permissions' className='w-full'>
          <TabsList className='grid w-full grid-cols-3'>
            <TabsTrigger value='permissions'>配比权限设置</TabsTrigger>
            <TabsTrigger value='mortar'>砂浆生成系数</TabsTrigger>
            <TabsTrigger value='density'>材料换算密度</TabsTrigger>
          </TabsList>

          <TabsContent value='permissions'>
            <PermissionsTab />
          </TabsContent>

          <TabsContent value='mortar'>
            <MortarSettingsTab
              coefficients={mortarCoefficients}
              onUpdate={handleCoefficientUpdate}
            />
          </TabsContent>

          <TabsContent value='density'>
            <DensitySettingsTab densities={materialDensities} onUpdate={handleDensityUpdate} />
          </TabsContent>
        </Tabs>
        <DialogFooter>
          <Button variant='outline' onClick={() => handleOpenChange(false)}>
            取消
          </Button>
          <Button onClick={() => handleOpenChange(false)}>保存</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RatioSettingsModal;
