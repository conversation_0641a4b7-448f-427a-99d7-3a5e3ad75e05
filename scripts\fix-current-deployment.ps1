# TMH任务调度系统 - 修复当前部署问题
# 解决SSH密码认证和PM2启动脚本问题

param(
    [string]$ServerIP = "*************",
    [string]$ServerUser = "administrator",
    [string]$Password = "13834567299Goodbye!",
    [string]$DeployPath = "C:\inetpub\tmh-task-dispatcher",
    [string]$ServiceName = "tmh-task-dispatcher",
    [int]$Port = 9001
)

Write-Host "🔧 TMH任务调度系统 - 修复当前部署" -ForegroundColor Green
Write-Host "🎯 目标: 修复SSH认证和PM2启动问题" -ForegroundColor Cyan
Write-Host ""

# 创建修复后的启动脚本
$fixedStartScript = @"
#!/usr/bin/env node
// TMH任务调度系统启动脚本 - 修复版本
process.env.NODE_ENV = 'production';
process.env.PORT = '$Port';

const { spawn } = require('child_process');
const path = require('path');
const os = require('os');
const fs = require('fs');

console.log('🚀 启动TMH任务调度系统...');
console.log('环境:', process.env.NODE_ENV);
console.log('端口:', process.env.PORT);
console.log('平台:', os.platform());

// Windows平台的Next.js启动方式
let nextCommand, nextArgs;

if (os.platform() === 'win32') {
  // 方法1: 直接使用next的JavaScript入口
  const nextCliPath = path.join(__dirname, 'node_modules', 'next', 'dist', 'bin', 'next');
  if (fs.existsSync(nextCliPath)) {
    console.log('使用Next.js CLI路径:', nextCliPath);
    nextCommand = 'node';
    nextArgs = [nextCliPath, 'start'];
  } else {
    // 方法2: 使用npm start
    console.log('使用npm start启动');
    nextCommand = 'npm.cmd';
    nextArgs = ['start'];
  }
} else {
  // Unix平台
  nextCommand = 'node';
  nextArgs = [path.join(__dirname, 'node_modules', '.bin', 'next'), 'start'];
}

console.log('启动命令:', nextCommand, nextArgs.join(' '));

const child = spawn(nextCommand, nextArgs, {
  stdio: 'inherit',
  env: process.env,
  cwd: __dirname,
  shell: os.platform() === 'win32'
});

child.on('error', (error) => {
  console.error('❌ 启动失败:', error);
  console.error('尝试使用npm start...');
  
  const fallbackChild = spawn('npm', ['start'], {
    stdio: 'inherit',
    env: process.env,
    cwd: __dirname,
    shell: true
  });
  
  fallbackChild.on('error', (fallbackError) => {
    console.error('❌ 所有启动方式都失败:', fallbackError);
    process.exit(1);
  });
});

child.on('exit', (code) => {
  console.log('应用退出，代码:', code);
  process.exit(code);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭...');
  child.kill('SIGTERM');
});
"@

# 创建修复后的PM2配置
$fixedPM2Config = @"
module.exports = {
  apps: [{
    name: '$ServiceName',
    script: './start.js',
    cwd: '.',
    env: {
      NODE_ENV: 'production',
      PORT: $Port
    },
    node_args: '--max-old-space-size=2048',
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '2G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
"@

Write-Host "🔄 开始修复..." -ForegroundColor Blue

# 创建临时文件
$tempDir = "$env:TEMP\tmh-fix-deployment"
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

$startScriptPath = "$tempDir\start.js"
$pm2ConfigPath = "$tempDir\ecosystem.config.js"

Set-Content -Path $startScriptPath -Value $fixedStartScript -Encoding UTF8
Set-Content -Path $pm2ConfigPath -Value $fixedPM2Config -Encoding UTF8

Write-Host "✅ 修复文件已创建" -ForegroundColor Green

# 检查PuTTY工具
$hasPlink = Get-Command plink -ErrorAction SilentlyContinue
$hasPscp = Get-Command pscp -ErrorAction SilentlyContinue

if (-not $hasPlink -and -not $hasPscp) {
    Write-Host "⚠️ 未找到PuTTY工具，尝试使用标准SSH..." -ForegroundColor Yellow
    
    # 使用标准SSH（需要手动输入密码）
    Write-Host "📤 上传修复文件到服务器..." -ForegroundColor Blue
    Write-Host "密码: $Password" -ForegroundColor Yellow
    
    try {
        & scp $startScriptPath "${ServerUser}@${ServerIP}:${DeployPath}/start.js"
        & scp $pm2ConfigPath "${ServerUser}@${ServerIP}:${DeployPath}/ecosystem.config.js"
        Write-Host "✅ 文件上传成功" -ForegroundColor Green
    } catch {
        Write-Host "❌ 文件上传失败，请手动上传" -ForegroundColor Red
        Write-Host "本地文件:" -ForegroundColor Cyan
        Write-Host "  $startScriptPath" -ForegroundColor White
        Write-Host "  $pm2ConfigPath" -ForegroundColor White
        Write-Host "上传到服务器:" -ForegroundColor Cyan
        Write-Host "  ${DeployPath}\start.js" -ForegroundColor White
        Write-Host "  ${DeployPath}\ecosystem.config.js" -ForegroundColor White
        exit 1
    }
} else {
    Write-Host "✅ 找到PuTTY工具，使用自动上传" -ForegroundColor Green
    
    try {
        if ($hasPscp) {
            & pscp -P 22 -l $ServerUser -pw $Password -batch $startScriptPath "${ServerIP}:${DeployPath}/start.js"
            & pscp -P 22 -l $ServerUser -pw $Password -batch $pm2ConfigPath "${ServerIP}:${DeployPath}/ecosystem.config.js"
        } else {
            # 使用plink上传（base64编码方式）
            $startContent = [Convert]::ToBase64String([IO.File]::ReadAllBytes($startScriptPath))
            $pm2Content = [Convert]::ToBase64String([IO.File]::ReadAllBytes($pm2ConfigPath))

            & plink -ssh -P 22 -l $ServerUser -pw $Password -batch $ServerIP "echo '$startContent' | base64 -d > '${DeployPath}/start.js'"
            & plink -ssh -P 22 -l $ServerUser -pw $Password -batch $ServerIP "echo '$pm2Content' | base64 -d > '${DeployPath}/ecosystem.config.js'"
        }
        Write-Host "✅ 文件上传成功" -ForegroundColor Green
    } catch {
        Write-Host "❌ 自动上传失败: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# 重启PM2服务
Write-Host "🔄 重启PM2服务..." -ForegroundColor Blue
try {
    if ($hasPlink) {
        & plink -ssh -P 22 -l $ServerUser -pw $Password -batch $ServerIP "cd /d `"$DeployPath`" && pm2 stop $ServiceName"
        & plink -ssh -P 22 -l $ServerUser -pw $Password -batch $ServerIP "cd /d `"$DeployPath`" && pm2 start ecosystem.config.js"
        & plink -ssh -P 22 -l $ServerUser -pw $Password -batch $ServerIP "pm2 save"
    } else {
        Write-Host "密码: $Password" -ForegroundColor Yellow
        & ssh "${ServerUser}@${ServerIP}" "cd /d `"$DeployPath`" && pm2 stop $ServiceName && pm2 start ecosystem.config.js && pm2 save"
    }
    Write-Host "✅ PM2服务重启成功" -ForegroundColor Green
} catch {
    Write-Host "❌ PM2重启失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 验证服务状态
Write-Host "🔍 验证服务状态..." -ForegroundColor Blue
Start-Sleep -Seconds 5

try {
    if ($hasPlink) {
        $status = & plink -ssh -P 22 -l $ServerUser -pw $Password -batch $ServerIP "pm2 status"
        Write-Host $status -ForegroundColor White
    } else {
        Write-Host "请手动检查服务状态:" -ForegroundColor Yellow
        Write-Host "ssh ${ServerUser}@${ServerIP} `"pm2 status`"" -ForegroundColor White
    }
} catch {
    Write-Host "⚠️ 无法获取服务状态" -ForegroundColor Yellow
}

# 测试HTTP访问
Write-Host "🌐 测试应用访问..." -ForegroundColor Blue
Start-Sleep -Seconds 5

try {
    $response = Invoke-WebRequest -Uri "http://$ServerIP`:$Port" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 应用访问成功！" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 应用返回状态码: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ 应用访问失败，可能需要更多时间启动" -ForegroundColor Yellow
    Write-Host "请稍后访问: http://$ServerIP`:$Port" -ForegroundColor Cyan
}

# 清理临时文件
Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "🎉 修复完成！" -ForegroundColor Green
Write-Host "🌐 访问地址: http://$ServerIP`:$Port" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 如果问题仍然存在，请检查:" -ForegroundColor Yellow
Write-Host "1. Node.js版本是否正确 (需要18+)" -ForegroundColor White
Write-Host "2. PM2是否正确安装" -ForegroundColor White
Write-Host "3. 应用依赖是否完整安装" -ForegroundColor White
