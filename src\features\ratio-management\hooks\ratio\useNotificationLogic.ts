/**
 * 配比通知单应用逻辑Hook
 * 处理配比通知单的应用和相关业务逻辑
 */

import { useCallback } from 'react';
import { useToast } from '@/shared/hooks/use-toast';
import type { NotificationApplyResult } from '@/core/types/ratio-notification';
import type { UnifiedRatioMaterial, RatioCalculationParams } from '@/core/types/ratio';

interface UseNotificationLogicProps {
  selectedMaterials: UnifiedRatioMaterial[];
  removeMaterial: (id: string) => void;
  addMaterial: (material: UnifiedRatioMaterial) => void;
  updateCalculationParams: (params: RatioCalculationParams) => void;
  calculateRatio: () => Promise<void>;
}

/**
 * 配比通知单应用逻辑Hook
 */
export function useNotificationLogic({
  selectedMaterials,
  removeMaterial,
  addMaterial,
  updateCalculationParams,
  calculateRatio,
}: UseNotificationLogicProps) {
  const { toast } = useToast();

  /**
   * 应用通知单处理函数
   */
  const handleApplyNotification = useCallback(
    async (result: NotificationApplyResult) => {
      if (!result.success) {
        toast({
          title: '应用失败',
          description: result.errors?.join(', ') || '应用通知单时发生错误',
          variant: 'destructive',
        });
        return;
      }

      try {
        // 应用计算参数
        if (result.calculationParams) {
          updateCalculationParams(result.calculationParams);
        }

        // 应用材料配比
        if (result.materials) {
          // 先移除现有材料，再添加新材料
          selectedMaterials.forEach(material => {
            removeMaterial(material.id);
          });

          result.materials.forEach(material => {
            addMaterial(material);
          });
        }

        // 触发重新计算
        if (result.calculationParams) {
          await calculateRatio();
        }

        // 显示成功消息和摘要
        const summary = result.summary;
        let summaryText = '通知单应用成功！';

        if (summary) {
          const changes = [];
          if (summary.updatedParams.length > 0) {
            changes.push(`更新参数: ${summary.updatedParams.join(', ')}`);
          }
          if (summary.addedMaterials.length > 0) {
            changes.push(`新增材料: ${summary.addedMaterials.join(', ')}`);
          }
          if (summary.updatedMaterials.length > 0) {
            changes.push(`更新材料: ${summary.updatedMaterials.join(', ')}`);
          }
          if (summary.removedMaterials.length > 0) {
            changes.push(`移除材料: ${summary.removedMaterials.join(', ')}`);
          }

          if (changes.length > 0) {
            summaryText += '\n' + changes.join('\n');
          }
        }

        toast({
          title: '应用成功',
          description: summaryText,
        });

        // 显示警告信息（如果有）
        if (result.warnings && result.warnings.length > 0) {
          setTimeout(() => {
            toast({
              title: '注意事项',
              description: result.warnings!.join('\n'),
              variant: 'default',
            });
          }, 1000);
        }
      } catch (error) {
        console.error('应用通知单时发生错误:', error);
        toast({
          title: '应用失败',
          description: error instanceof Error ? error.message : '应用通知单时发生未知错误',
          variant: 'destructive',
        });
      }
    },
    [selectedMaterials, removeMaterial, addMaterial, updateCalculationParams, calculateRatio, toast]
  );

  /**
   * 验证通知单数据
   */
  const validateNotificationData = useCallback(
    (result: NotificationApplyResult): boolean => {
      if (!result.success) {
        return false;
      }

      // 检查是否有有效的更改
      const hasValidChanges =
        result.calculationParams || (result.materials && result.materials.length > 0);

      if (!hasValidChanges) {
        toast({
          title: '无效通知单',
          description: '通知单中没有有效的配比更改',
          variant: 'destructive',
        });
        return false;
      }

      return true;
    },
    [toast]
  );

  /**
   * 预览通知单更改
   */
  const previewNotificationChanges = useCallback((result: NotificationApplyResult) => {
    if (!result.success || !result.summary) {
      return null;
    }

    const { summary } = result;
    const changes = {
      parameterChanges: summary.updatedParams,
      materialAdditions: summary.addedMaterials,
      materialUpdates: summary.updatedMaterials,
      materialRemovals: summary.removedMaterials,
    };

    return changes;
  }, []);

  return {
    handleApplyNotification,
    validateNotificationData,
    previewNotificationChanges,
  };
}
