'use client';

/**
 * 增强的加载状态组件
 * 提供多种加载状态和错误处理
 */

import React, { useState, useEffect } from 'react';
import { Loader2, AlertCircle, RefreshCw, Wifi, WifiOff } from 'lucide-react';
import { Button } from '@/shared/components/button';
import { Progress } from '@/shared/components/progress';

// 加载状态类型
export type LoadingState = 'idle' | 'loading' | 'success' | 'error' | 'timeout' | 'offline';

// 加载配置接口
export interface LoadingConfig {
  message?: string;
  timeout?: number;
  showProgress?: boolean;
  showRetry?: boolean;
  retryText?: string;
  onRetry?: () => void;
  estimatedTime?: number;
}

// 骨架屏组件
export const SkeletonLoader: React.FC<{
  type?: 'card' | 'list' | 'chart' | 'table' | 'modal';
  count?: number;
  className?: string;
}> = ({ type = 'card', count = 1, className = '' }) => {
  const renderSkeleton = () => {
    switch (type) {
      case 'card':
        return (
          <div className='animate-pulse space-y-4'>
            <div className='h-4 bg-gray-200 rounded w-3/4'></div>
            <div className='space-y-2'>
              <div className='h-3 bg-gray-200 rounded'></div>
              <div className='h-3 bg-gray-200 rounded w-5/6'></div>
            </div>
            <div className='flex space-x-2'>
              <div className='h-8 bg-gray-200 rounded w-16'></div>
              <div className='h-8 bg-gray-200 rounded w-16'></div>
            </div>
          </div>
        );

      case 'list':
        return (
          <div className='animate-pulse space-y-3'>
            {Array.from({ length: count }).map((_, i) => (
              <div key={i} className='flex items-center space-x-3'>
                <div className='w-10 h-10 bg-gray-200 rounded-full'></div>
                <div className='flex-1 space-y-2'>
                  <div className='h-3 bg-gray-200 rounded w-1/2'></div>
                  <div className='h-2 bg-gray-200 rounded w-3/4'></div>
                </div>
              </div>
            ))}
          </div>
        );

      case 'chart':
        return (
          <div className='animate-pulse'>
            <div className='h-4 bg-gray-200 rounded w-1/3 mb-4'></div>
            <div className='flex items-end justify-between space-x-2 h-32'>
              {Array.from({ length: 8 }).map((_, i) => (
                <div
                  key={i}
                  className='bg-gray-200 rounded-t'
                  style={{ height: `${Math.random() * 80 + 20}%`, width: '12%' }}
                ></div>
              ))}
            </div>
            <div className='flex justify-center space-x-4 mt-4'>
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className='flex items-center space-x-2'>
                  <div className='w-3 h-3 bg-gray-200 rounded'></div>
                  <div className='h-3 bg-gray-200 rounded w-12'></div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'table':
        return (
          <div className='animate-pulse'>
            <div className='grid grid-cols-4 gap-4 mb-4'>
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className='h-4 bg-gray-200 rounded'></div>
              ))}
            </div>
            {Array.from({ length: count }).map((_, i) => (
              <div key={i} className='grid grid-cols-4 gap-4 mb-2'>
                {Array.from({ length: 4 }).map((_, j) => (
                  <div key={j} className='h-3 bg-gray-200 rounded'></div>
                ))}
              </div>
            ))}
          </div>
        );

      case 'modal':
        return (
          <div className='animate-pulse space-y-4'>
            <div className='h-6 bg-gray-200 rounded w-1/2'></div>
            <div className='space-y-3'>
              <div className='h-4 bg-gray-200 rounded'></div>
              <div className='h-4 bg-gray-200 rounded w-4/5'></div>
              <div className='h-4 bg-gray-200 rounded w-3/5'></div>
            </div>
            <div className='flex justify-end space-x-2 pt-4'>
              <div className='h-8 bg-gray-200 rounded w-16'></div>
              <div className='h-8 bg-gray-200 rounded w-16'></div>
            </div>
          </div>
        );

      default:
        return (
          <div className='animate-pulse'>
            <div className='h-4 bg-gray-200 rounded w-3/4 mb-2'></div>
            <div className='h-3 bg-gray-200 rounded w-1/2'></div>
          </div>
        );
    }
  };

  return <div className={`p-4 ${className}`}>{renderSkeleton()}</div>;
};

// 智能加载组件
export const SmartLoader: React.FC<{
  state: LoadingState;
  config?: LoadingConfig;
  children?: React.ReactNode;
  className?: string;
}> = ({ state, config = {}, children, className = '' }) => {
  const [progress, setProgress] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isOnline, setIsOnline] = useState(true);

  const {
    message = '正在加载...',
    timeout = 30000,
    showProgress = false,
    showRetry = true,
    retryText = '重试',
    onRetry,
    estimatedTime = 3000,
  } = config;

  // 监听网络状态
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // 进度条和计时器
  useEffect(() => {
    if (state === 'loading') {
      const startTime = Date.now();
      const interval = setInterval(() => {
        const elapsed = Date.now() - startTime;
        setElapsedTime(elapsed);

        if (showProgress) {
          // 基于估计时间计算进度
          const progressValue = Math.min((elapsed / estimatedTime) * 100, 95);
          setProgress(progressValue);
        }
      }, 100);

      return () => clearInterval(interval);
    } else {
      setProgress(0);
      setElapsedTime(0);
      return undefined;
    }
  }, [state, showProgress, estimatedTime]);

  // 渲染不同状态
  const renderContent = () => {
    switch (state) {
      case 'loading':
        return (
          <div className='flex flex-col items-center justify-center p-8 space-y-4'>
            <Loader2 className='w-8 h-8 animate-spin text-blue-500' />
            <div className='text-center'>
              <p className='text-sm font-medium text-gray-900'>{message}</p>
              {showProgress && (
                <div className='mt-2 w-48'>
                  <Progress value={progress} className='h-2' />
                  <p className='text-xs text-gray-500 mt-1'>
                    {(elapsedTime / 1000).toFixed(1)}s / {(estimatedTime / 1000).toFixed(1)}s
                  </p>
                </div>
              )}
              {elapsedTime > 5000 && (
                <p className='text-xs text-gray-400 mt-2'>加载时间较长，请耐心等待...</p>
              )}
            </div>
          </div>
        );

      case 'error':
        return (
          <div className='flex flex-col items-center justify-center p-8 space-y-4'>
            <AlertCircle className='w-8 h-8 text-red-500' />
            <div className='text-center'>
              <p className='text-sm font-medium text-gray-900'>加载失败</p>
              <p className='text-xs text-gray-500 mt-1'>{message || '请检查网络连接或稍后重试'}</p>
              {showRetry && onRetry && (
                <Button onClick={onRetry} variant='outline' size='sm' className='mt-3'>
                  <RefreshCw className='w-4 h-4 mr-1' />
                  {retryText}
                </Button>
              )}
            </div>
          </div>
        );

      case 'timeout':
        return (
          <div className='flex flex-col items-center justify-center p-8 space-y-4'>
            <AlertCircle className='w-8 h-8 text-yellow-500' />
            <div className='text-center'>
              <p className='text-sm font-medium text-gray-900'>加载超时</p>
              <p className='text-xs text-gray-500 mt-1'>加载时间超过 {timeout / 1000} 秒</p>
              {showRetry && onRetry && (
                <Button onClick={onRetry} variant='outline' size='sm' className='mt-3'>
                  <RefreshCw className='w-4 h-4 mr-1' />
                  {retryText}
                </Button>
              )}
            </div>
          </div>
        );

      case 'offline':
        return (
          <div className='flex flex-col items-center justify-center p-8 space-y-4'>
            <WifiOff className='w-8 h-8 text-gray-400' />
            <div className='text-center'>
              <p className='text-sm font-medium text-gray-900'>网络连接断开</p>
              <p className='text-xs text-gray-500 mt-1'>请检查网络连接后重试</p>
              <div className='flex items-center justify-center mt-2 text-xs text-gray-400'>
                {isOnline ? (
                  <>
                    <Wifi className='w-4 h-4 mr-1' />
                    网络已连接
                  </>
                ) : (
                  <>
                    <WifiOff className='w-4 h-4 mr-1' />
                    网络断开
                  </>
                )}
              </div>
            </div>
          </div>
        );

      case 'success':
        return children;

      default:
        return children;
    }
  };

  return <div className={className}>{renderContent()}</div>;
};

// 懒加载包装器组件
export const LazyWrapper: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  errorFallback?: React.ReactNode;
  onLoad?: () => void;
  onError?: (error: Error) => void;
  skeletonType?: 'card' | 'list' | 'chart' | 'table' | 'modal';
}> = ({ children, fallback, errorFallback, onLoad, onError, skeletonType = 'card' }) => {
  const [loadState, setLoadState] = useState<LoadingState>('loading');

  useEffect(() => {
    // 模拟加载完成
    const timer = setTimeout(() => {
      setLoadState('success');
      onLoad?.();
    }, 100);

    return () => clearTimeout(timer);
  }, [onLoad]);

  if (loadState === 'loading') {
    return fallback || <SkeletonLoader type={skeletonType} />;
  }

  if (loadState === 'error') {
    return (
      errorFallback || (
        <SmartLoader
          state='error'
          config={{
            onRetry: () => setLoadState('loading'),
          }}
        />
      )
    );
  }

  return <>{children}</>;
};
