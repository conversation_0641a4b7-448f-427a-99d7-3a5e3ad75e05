/**
 * 提供声音提醒相关的工具函数
 */

import {
  handleAudioError,
  safeExecuteAsync,
} from '../../infrastructure/error-handling/error-handlers';

// 使用Web Audio API播放简单的提示音
export const playBeepSound = (frequency: number = 800, duration: number = 200): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      // 检查浏览器是否支持Web Audio API
      if (!window.AudioContext && !(window as any).webkitAudioContext) {
        reject(new Error('浏览器不支持Web Audio API'));
        return;
      }

      const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
      const audioContext = new AudioContext();

      // 创建振荡器
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      // 连接节点
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      // 设置频率和波形
      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
      oscillator.type = 'sine';

      // 设置音量包络
      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);

      // 播放声音
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration / 1000);

      // 清理资源
      oscillator.onended = () => {
        audioContext
          .close()
          .then(() => resolve())
          .catch(() => resolve());
      };
    } catch (error) {
      reject(error);
    }
  });
};

// 播放提醒声音（简化版，使用错误处理器）
export const playReminderSound = async (
  soundId: string = 'dispatch-alert-sound'
): Promise<void> => {
  return safeExecuteAsync(
    async () => {
      const audio = document.getElementById(soundId) as HTMLAudioElement;

      if (!audio) {
        // 如果找不到音频元素，尝试使用Web Audio API创建简单的提示音
        await playBeepSound();
        return;
      }

      // 尝试播放音频
      try {
        await audio.play();
      } catch (error) {
        // 如果播放失败，回退到提示音
        handleAudioError(error, { soundId, fallback: 'beep' });
        await playBeepSound();
      }
    },
    undefined,
    error => {
      // 自定义错误处理：音频播放失败不应该影响应用运行
      handleAudioError(error, { soundId, action: 'playReminderSound' });
    }
  );
};

// 停止提醒声音
export const stopReminderSound = (soundId: string = 'dispatch-alert-sound'): void => {
  const audio = document.getElementById(soundId) as HTMLAudioElement;
  if (audio) {
    audio.pause();
    audio.currentTime = 0;
  }
};
