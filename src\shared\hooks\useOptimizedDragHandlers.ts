'use client';

import { useCallback, useRef } from 'react';
// 简单的节流和防抖实现，避免外部依赖
const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: { leading?: boolean; trailing?: boolean } = {}
): T => {
  let timeout: NodeJS.Timeout | null = null;
  let previous = 0;
  const { leading = true, trailing = true } = options;

  return ((...args: Parameters<T>) => {
    const now = Date.now();
    if (!previous && !leading) previous = now;
    const remaining = wait - (now - previous);

    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = now;
      return func.apply(null, args);
    } else if (!timeout && trailing) {
      timeout = setTimeout(() => {
        previous = leading ? 0 : Date.now();
        timeout = null;
        return func.apply(null, args);
      }, remaining);
    }
  }) as T;
};

const debounce = <T extends (...args: any[]) => any>(func: T, wait: number): T => {
  let timeout: NodeJS.Timeout | null = null;

  return ((...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(null, args), wait);
  }) as T;
};

interface DragHandlerOptions {
  throttleMs?: number;
  debounceMs?: number;
  enableThrottle?: boolean;
  enableDebounce?: boolean;
}

const DEFAULT_OPTIONS: Required<DragHandlerOptions> = {
  throttleMs: 16, // 60fps
  debounceMs: 100,
  enableThrottle: true,
  enableDebounce: false,
};

export const useOptimizedDragHandlers = (options: DragHandlerOptions = {}) => {
  const finalOptions = { ...DEFAULT_OPTIONS, ...options };
  const dragStateRef = useRef({
    isDragging: false,
    startTime: 0,
    lastUpdateTime: 0,
  });

  // 优化的拖拽开始处理器
  const createOptimizedDragStart = useCallback((handler: (event: any) => void) => {
    return (event: any) => {
      dragStateRef.current.isDragging = true;
      dragStateRef.current.startTime = performance.now();
      dragStateRef.current.lastUpdateTime = performance.now();

      // 立即执行，不需要节流
      handler(event);
    };
  }, []);

  // 优化的拖拽移动处理器
  const createOptimizedDragMove = useCallback(
    (handler: (event: any) => void) => {
      const throttledHandler = finalOptions.enableThrottle
        ? throttle(
            (event: any) => {
              const now = performance.now();
              dragStateRef.current.lastUpdateTime = now;
              handler(event);
            },
            finalOptions.throttleMs,
            { leading: true, trailing: false }
          )
        : handler;

      return throttledHandler;
    },
    [finalOptions.enableThrottle, finalOptions.throttleMs]
  );

  // 优化的拖拽结束处理器
  const createOptimizedDragEnd = useCallback(
    (handler: (event: any) => void) => {
      const debouncedHandler = finalOptions.enableDebounce
        ? debounce((event: any) => {
            dragStateRef.current.isDragging = false;
            handler(event);
          }, finalOptions.debounceMs)
        : (event: any) => {
            dragStateRef.current.isDragging = false;
            handler(event);
          };

      return debouncedHandler;
    },
    [finalOptions.enableDebounce, finalOptions.debounceMs]
  );

  // 获取拖拽统计信息
  const getDragStats = useCallback(() => {
    const now = performance.now();
    return {
      isDragging: dragStateRef.current.isDragging,
      duration: now - dragStateRef.current.startTime,
      lastUpdate: now - dragStateRef.current.lastUpdateTime,
    };
  }, []);

  // 清理函数
  const cleanup = useCallback(() => {
    dragStateRef.current = {
      isDragging: false,
      startTime: 0,
      lastUpdateTime: 0,
    };
  }, []);

  return {
    createOptimizedDragStart,
    createOptimizedDragMove,
    createOptimizedDragEnd,
    getDragStats,
    cleanup,
  };
};

// 拖拽性能优化工具函数
export const optimizeDragElement = (element: HTMLElement) => {
  // 启用硬件加速
  element.style.transform = 'translateZ(0)';
  element.style.willChange = 'transform';

  // 启用 CSS 包含
  element.style.contain = 'layout style paint';

  // 优化触摸事件
  element.style.touchAction = 'none';

  // 添加性能优化类
  element.classList.add('drag-optimized');
};

// 清理拖拽元素优化
export const cleanupDragElement = (element: HTMLElement) => {
  element.style.willChange = 'auto';
  element.style.transform = '';
  element.style.contain = '';
  element.style.touchAction = '';
  element.classList.remove('drag-optimized');
};

// 批量 DOM 更新优化
export const batchDOMUpdates = (updates: (() => void)[]) => {
  requestAnimationFrame(() => {
    updates.forEach(update => update());
  });
};

// 拖拽防抖工具
export const createDragDebouncer = (delay: number = 16) => {
  let timeoutId: NodeJS.Timeout | null = null;

  return (callback: () => void) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      callback();
      timeoutId = null;
    }, delay);
  };
};
