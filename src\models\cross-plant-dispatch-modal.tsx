'use client';

import React, { useState } from 'react';

import { Button } from '@/shared/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { Label } from '@/shared/components/label';
// Input is imported but not used, consider removing if not planned for future use
// import { Input } from '@/shared/components/input';
import { Textarea } from '@/shared/components/textarea';
import { useToast } from '@/shared/hooks/use-toast';
import type { Plant, Vehicle } from '@/core/types';

interface CrossPlantDispatchModalProps {
  isOpen: boolean;
  onOpenChangeAction: (isOpen: boolean) => void;
  vehicle: Vehicle | null;
  sourcePlant: Plant | null;
  targetPlant: Plant | null;
  onConfirmAction: (vehicleId: string, targetPlantId: string, notes?: string) => void;
}

export function CrossPlantDispatchModal({
  isOpen,
  onOpenChangeAction,
  vehicle,
  sourcePlant,
  targetPlant,
  onConfirmAction,
}: CrossPlantDispatchModalProps) {
  const [notes, setNotes] = useState('');
  const { toast } = useToast();

  if (!vehicle || !sourcePlant || !targetPlant) return null;

  const handleConfirm = () => {
    onConfirmAction(vehicle.id, targetPlant.id, notes);
    toast({
      title: '跨站调度已提交',
      description: `车辆 ${vehicle.vehicleNumber} 从 ${sourcePlant.name} 调度到 ${targetPlant.name}. 备注: ${notes || '无'}`,
    });
    onOpenChangeAction(false);
    setNotes('');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChangeAction}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>车辆跨站调度确认</DialogTitle>
          <DialogDescription>
            请确认将车辆从 {sourcePlant.name} 调度到 {targetPlant.name}。
          </DialogDescription>
        </DialogHeader>
        <div className='grid gap-4 py-4 text-sm'>
          <div className='flex justify-between'>
            <span className='text-muted-foreground'>调度车辆:</span>
            <span>
              {vehicle.vehicleNumber} (ID: {vehicle.id})
            </span>
          </div>
          <div className='flex justify-between'>
            <span className='text-muted-foreground'>原属搅拌站:</span>
            <span>{sourcePlant.name}</span>
          </div>
          <div className='flex justify-between'>
            <span className='text-muted-foreground'>目标搅拌站:</span>
            <span>{targetPlant.name}</span>
          </div>
          <div className='grid w-full gap-1.5'>
            <Label htmlFor='cross-dispatch-notes'>调度备注 (可选)</Label>
            <Textarea
              placeholder='输入调度备注...'
              id='cross-dispatch-notes'
              value={notes}
              onChange={e => setNotes(e.target.value)}
              className='min-h-[60px]'
            />
          </div>
        </div>
        <DialogFooter>
          <Button type='button' variant='outline' onClick={() => onOpenChangeAction(false)}>
            取消
          </Button>
          <Button type='button' onClick={handleConfirm}>
            确认调度
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
