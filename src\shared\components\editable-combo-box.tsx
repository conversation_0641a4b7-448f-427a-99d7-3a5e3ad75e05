'use client';

import React, { useState } from 'react';

import { Plus, Trash2, X } from 'lucide-react';

import { Button } from '@/shared/components/button';
import { Input } from '@/shared/components/input';

// 可编辑下拉选择组件 - 单一控件，既可输入也可下拉选择
interface EditableComboBoxProps {
  value: string;
  onValueChange: (value: string) => void;
  options: string[];
  onAddOption?: (option: string) => void;
  onRemoveOption?: (option: string) => void;
  placeholder?: string;
  className?: string;
  // 新增配置选项
  allowAdd?: boolean; // 是否允许添加选项，默认 true
  allowRemove?: boolean; // 是否允许删除选项，默认 true
  addFromInput?: boolean; // 是否允许从输入框直接添加，默认 true
}

export function EditableComboBox({
  value,
  onValueChange,
  options,
  onAddOption,
  onRemoveOption,
  placeholder,
  className,
  allowAdd = true,
  allowRemove = true,
  addFromInput = true,
}: EditableComboBoxProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [newOption, setNewOption] = useState('');
  const containerRef = React.useRef<HTMLDivElement>(null);

  // 点击外部关闭下拉框
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setIsAdding(false);
        setNewOption('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleAddOption = () => {
    if (newOption.trim() && onAddOption && allowAdd) {
      onAddOption(newOption.trim());
      setNewOption('');
      setIsAdding(false);
    }
  };

  // 处理输入框失焦时自动添加选项
  const handleInputBlur = () => {
    if (
      addFromInput &&
      allowAdd &&
      value.trim() &&
      !options.includes(value.trim()) &&
      onAddOption
    ) {
      onAddOption(value.trim());
    }
  };

  // 处理输入框按键事件
  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (
      e.key === 'Enter' &&
      addFromInput &&
      allowAdd &&
      value.trim() &&
      !options.includes(value.trim()) &&
      onAddOption
    ) {
      e.preventDefault();
      onAddOption(value.trim());
    }
  };

  const handleSelectOption = (option: string) => {
    onValueChange(option);
    setIsOpen(false);
  };

  return (
    <div ref={containerRef} className='relative flex-1'>
      <div className='flex items-center'>
        <Input
          value={value}
          onChange={e => onValueChange(e.target.value)}
          onBlur={handleInputBlur}
          onKeyDown={handleInputKeyDown}
          placeholder={placeholder}
          className={`${className} pr-8 border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors duration-200`}
        />
        <Button
          variant='ghost'
          size='sm'
          onClick={() => setIsOpen(!isOpen)}
          className='absolute right-1 h-4 w-4 p-0 hover:bg-gray-100'
        >
          <svg className='w-3 h-3' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
            <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M19 9l-7 7-7-7' />
          </svg>
        </Button>
      </div>

      {isOpen && (
        <div className='absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-slate-200 rounded-lg shadow-lg max-h-48 overflow-y-auto'>
          {/* 显示添加提示 */}
          {addFromInput &&
            allowAdd &&
            value.trim() &&
            !options.includes(value.trim()) &&
            onAddOption && (
              <div className='px-3 py-2 bg-blue-50 border-b border-blue-200 text-xs text-blue-700'>
                <div className='flex items-center gap-1'>
                  <Plus className='w-3 h-3' />
                  <span>按回车键添加 "{value.trim()}" 到选项列表</span>
                </div>
              </div>
            )}

          {options.map(option => (
            <div
              key={option}
              className='flex items-center justify-between px-3 py-2 hover:bg-blue-50 transition-colors duration-150 border-b border-slate-100 last:border-b-0'
            >
              <button
                onClick={() => handleSelectOption(option)}
                className='flex-1 text-left text-xs text-slate-700 hover:text-blue-700 transition-colors duration-150'
              >
                {option}
              </button>
              {onRemoveOption && allowRemove && (
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={e => {
                    e.stopPropagation();
                    onRemoveOption(option);
                  }}
                  className='h-4 w-4 p-0 ml-2 hover:bg-red-100'
                  title='删除选项'
                >
                  <Trash2 className='w-3 h-3 text-red-500' />
                </Button>
              )}
            </div>
          ))}

          {isAdding ? (
            <div className='flex items-center gap-1 p-2 border-t'>
              <Input
                value={newOption}
                onChange={e => setNewOption(e.target.value)}
                className='h-6 text-xs flex-1'
                placeholder='输入新选项'
                onKeyDown={e => {
                  if (e.key === 'Enter') {
                    handleAddOption();
                  } else if (e.key === 'Escape') {
                    setIsAdding(false);
                    setNewOption('');
                  }
                }}
                autoFocus
              />
              <Button
                onClick={handleAddOption}
                size='sm'
                className='h-6 px-2'
                disabled={!newOption.trim()}
              >
                <Plus className='w-3 h-3' />
              </Button>
              <Button
                onClick={() => {
                  setIsAdding(false);
                  setNewOption('');
                }}
                variant='ghost'
                size='sm'
                className='h-6 px-2'
              >
                <X className='w-3 h-3' />
              </Button>
            </div>
          ) : (
            onAddOption &&
            allowAdd && (
              <button
                onClick={() => setIsAdding(true)}
                className='w-full px-3 py-2 text-left text-xs hover:bg-green-50 hover:text-green-700 border-t border-gray-200 flex items-center transition-colors duration-150'
                title='添加新选项'
              >
                <Plus className='w-3 h-3 mr-1' />
                添加选项
              </button>
            )
          )}
        </div>
      )}
    </div>
  );
}
