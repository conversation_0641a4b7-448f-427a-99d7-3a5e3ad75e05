/**
 * 配比反向计算API路由
 * POST /api/ratio/reverse-calculate
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type {
  ReverseCalculateRequest,
  ReverseCalculateResponse,
  ApiErrorResponse,
} from '@/core/types/ratio-api';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ReverseCalculateResponse | ApiErrorResponse>
) {
  // 只允许POST请求
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: '只允许POST请求',
      },
      timestamp: new Date().toISOString(),
    });
  }

  try {
    const request: ReverseCalculateRequest = req.body;

    // 基本参数验证
    if (!request.taskId || !request.materials || Object.keys(request.materials).length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: '缺少必要参数：任务ID或目标材料用量',
        },
        timestamp: new Date().toISOString(),
      });
    }

    // 执行反向计算
    const calculationResults = performReverseCalculation(request);

    const response: ReverseCalculateResponse = {
      success: true,
      data: calculationResults as any,
      message: '反向计算完成',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('反向计算API错误:', error);

    res.status(500).json({
      success: false,
      error: {
        code: 'CALCULATION_ERROR',
        message: '反向计算失败',
        details: error instanceof Error ? error.message : '未知错误',
      },
      timestamp: new Date().toISOString(),
    });
  }
}

/**
 * 执行反向计算逻辑
 */
function performReverseCalculation(request: ReverseCalculateRequest) {
  const targetMaterials = request.materials || {};
  const constraints = (request as any).constraints || {};

  // 获取材料用量 - 从材料数组中提取
  const materialsArray = Array.isArray(targetMaterials) ? targetMaterials : [];
  const cement =
    materialsArray.find((m: any) => m.category === 'cement' || m.name?.includes('水泥'))
      ?.actualAmount || 0;
  const water =
    materialsArray.find((m: any) => m.category === 'water' || m.name?.includes('水'))
      ?.actualAmount || 0;
  const sand =
    materialsArray.find((m: any) => m.category === 'fine-aggregate' || m.name?.includes('砂'))
      ?.actualAmount || 0;
  const gravel =
    materialsArray.find((m: any) => m.category === 'coarse-aggregate' || m.name?.includes('石'))
      ?.actualAmount || 0;
  const admixture =
    materialsArray.find((m: any) => m.category === 'admixture' || m.name?.includes('外加剂'))
      ?.actualAmount || 0;

  // 计算基础参数
  const totalWeight = cement + water + sand + gravel + admixture;
  const waterCementRatio = cement > 0 ? water / cement : 0;
  const sandRatio = sand + gravel > 0 ? (sand / (sand + gravel)) * 100 : 0;

  // 强度预测（基于水胶比的简化公式）
  let strengthPrediction = 30; // 默认C30
  if (waterCementRatio > 0) {
    // 使用Abrams定律的简化版本
    strengthPrediction = Math.max(15, Math.min(80, 65 - (waterCementRatio - 0.3) * 100));
  }

  // 根据约束条件调整参数
  const calculationParams = {
    density: totalWeight,
    waterCementRatio: Math.round(waterCementRatio * 100) / 100,
    sandRatio: Math.round(sandRatio * 10) / 10,
    strengthGrade: Math.round(strengthPrediction),
    slump: constraints?.targetSlump || calculateSlump(waterCementRatio, admixture),
    airContent: constraints?.targetAirContent || 4.5,
    temperature: constraints?.temperature || 20,
  };

  // 质量评估
  const qualityScore = calculateQualityScore(calculationParams, targetMaterials);

  // 生成警告和建议
  const warnings = generateReverseWarnings(calculationParams, targetMaterials);
  const suggestions = generateReverseSuggestions(calculationParams, targetMaterials);

  return {
    calculationParams,
    qualityAnalysis: {
      qualityScore,
      strengthPrediction,
      durabilityRating: calculateDurabilityRating(calculationParams),
      workabilityRating: calculateWorkabilityRating(calculationParams),
    },
    warnings,
    suggestions,
    adjustments: generateAdjustmentRecommendations(calculationParams, constraints),
    metadata: {
      calculationMethod: 'reverse',
      calculatedAt: new Date().toISOString(),
      version: '1.0',
      confidence: calculateConfidence(targetMaterials),
    },
  };
}

/**
 * 计算坍落度
 */
function calculateSlump(waterCementRatio: number, admixture: number): number {
  let baseSlump = 80;

  // 水胶比影响
  baseSlump += (waterCementRatio - 0.4) * 200;

  // 外加剂影响
  if (admixture > 0) {
    baseSlump += 40; // 减水剂增加坍落度
  }

  return Math.max(50, Math.min(200, Math.round(baseSlump)));
}

/**
 * 计算质量评分
 */
function calculateQualityScore(params: any, materials: any): number {
  let score = 100;

  // 水胶比评估
  if (params.waterCementRatio > 0.6) {
    score -= 20;
  } else if (params.waterCementRatio < 0.3) {
    score -= 10;
  }

  // 砂率评估
  if (params.sandRatio < 25 || params.sandRatio > 45) {
    score -= 10;
  }

  // 材料比例评估
  const totalAggregate = (materials.sand || 0) + (materials.gravel || 0);
  const cementAggregateRatio = totalAggregate > 0 ? (materials.cement || 0) / totalAggregate : 0;

  if (cementAggregateRatio < 0.15 || cementAggregateRatio > 0.25) {
    score -= 15;
  }

  return Math.max(0, Math.min(100, Math.round(score)));
}

/**
 * 计算耐久性评级
 */
function calculateDurabilityRating(params: any): string {
  if (params.waterCementRatio <= 0.4) return '优秀';
  if (params.waterCementRatio <= 0.5) return '良好';
  if (params.waterCementRatio <= 0.6) return '一般';
  return '较差';
}

/**
 * 计算工作性评级
 */
function calculateWorkabilityRating(params: any): string {
  if (params.slump >= 120 && params.slump <= 160) return '优秀';
  if (params.slump >= 80 && params.slump <= 180) return '良好';
  if (params.slump >= 50 && params.slump <= 200) return '一般';
  return '较差';
}

/**
 * 生成反向计算警告
 */
function generateReverseWarnings(params: any, materials: any): string[] {
  const warnings: string[] = [];

  if (params.waterCementRatio > 0.6) {
    warnings.push('水胶比过高，可能影响强度和耐久性');
  }

  if (params.waterCementRatio < 0.3) {
    warnings.push('水胶比过低，可能影响工作性');
  }

  if (params.sandRatio < 25) {
    warnings.push('砂率偏低，可能影响和易性');
  }

  if (params.sandRatio > 45) {
    warnings.push('砂率偏高，可能增加用水量');
  }

  if (materials.cement && materials.cement < 250) {
    warnings.push('水泥用量偏低，请检查强度要求');
  }

  if (materials.cement && materials.cement > 500) {
    warnings.push('水泥用量偏高，可能增加收缩');
  }

  return warnings;
}

/**
 * 生成反向计算建议
 */
function generateReverseSuggestions(params: any, materials: any): string[] {
  const suggestions: string[] = [];

  suggestions.push('建议进行试配验证反算结果');

  if (params.waterCementRatio > 0.5) {
    suggestions.push('考虑增加水泥用量或减少用水量');
  }

  if (params.qualityScore < 80) {
    suggestions.push('当前配比质量评分较低，建议优化材料比例');
  }

  if (params.slump < 80) {
    suggestions.push('坍落度偏低，考虑增加用水量或外加剂');
  }

  if (params.slump > 180) {
    suggestions.push('坍落度偏高，考虑减少用水量');
  }

  return suggestions;
}

/**
 * 生成调整建议
 */
function generateAdjustmentRecommendations(params: any, constraints: any) {
  const adjustments: any[] = [];

  if (constraints?.targetStrength && params.strengthGrade < constraints.targetStrength) {
    adjustments.push({
      parameter: 'cement',
      action: 'increase',
      amount: Math.ceil((constraints.targetStrength - params.strengthGrade) * 5),
      reason: '提高强度等级',
    });
  }

  if (constraints?.targetSlump && Math.abs(params.slump - constraints.targetSlump) > 20) {
    const action = params.slump < constraints.targetSlump ? 'increase' : 'decrease';
    adjustments.push({
      parameter: 'water',
      action,
      amount: Math.abs(params.slump - constraints.targetSlump) * 0.5,
      reason: '调整坍落度',
    });
  }

  return adjustments;
}

/**
 * 计算置信度
 */
function calculateConfidence(materials: any): number {
  let confidence = 0.9;

  // 检查材料完整性
  const requiredMaterials = ['cement', 'water', 'sand', 'gravel'];
  const providedMaterials = requiredMaterials.filter(material => materials[material] > 0);

  confidence *= providedMaterials.length / requiredMaterials.length;

  return Math.round(confidence * 100) / 100;
}
