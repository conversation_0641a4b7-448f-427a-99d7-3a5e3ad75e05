/**
 * immer-helpers.ts
 * 提供了对 immer 库的封装和类型安全的辅助函数。
 * 这样可以集中管理不可变状态更新的逻辑，并提供更强的类型推断。
 *
 * 主要导出：
 * - produce: 从 immer 库直接导出的核心函数。
 * - Draft: 从 immer 库直接导出的类型，用于在更新函数中修改状态。
 * - createSafeProducer: 一个高阶函数，用于创建类型安全的 produce 包装器，
 *   确保更新函数只接收正确的状态和参数。
 */
import { type Draft, produce } from 'immer';

/**
 * 重新导出 immer 的核心成员，以便在整个应用中统一导入来源。
 */
export { produce, type Draft };

/**
 * 创建一个类型安全的 produce 包装器。
 * 这是一个高阶函数，它接收一个更新函数，并返回一个新的函数，
 * 该函数在内部使用 produce 来安全地更新状态。
 *
 * @template S - 状态的类型。
 * @template A - 更新函数接收的额外参数的类型（元组形式）。
 *
 * @param {(draft: Draft<S>, ...args: A) => void} updater - 一个函数，
 *   它接收一个可变的草稿状态（draft）和任意数量的额外参数，并直接修改草稿。
 *
 * @returns {(state: S, ...args: A) => S} - 一个新的纯函数，
 *   它接收当前状态和与更新函数相同的额外参数，并返回一个新的、更新后的不可变状态。
 *
 * @example
 * // 定义一个更新函数
 * const updateUserAge = (draft, newAge: number) => {
 *   draft.age = newAge;
 * };
 *
 * // 创建一个安全的 producer
 * const safeUpdateUserAge = createSafeProducer(updateUserAge);
 *
 * // 使用 producer
 * const currentState = { name: 'Alice', age: 30 };
 * const newState = safeUpdateUserAge(currentState, 31);
 * // newState is { name: 'Alice', age: 31 }
 * // currentState remains unchanged
 */
export function createSafeProducer<S, A extends any[]>(
  updater: (draft: Draft<S>, ...args: A) => void
) {
  return function (state: S, ...args: A): S {
    return produce(state, draft => {
      updater(draft as Draft<S>, ...args);
    });
  };
}
