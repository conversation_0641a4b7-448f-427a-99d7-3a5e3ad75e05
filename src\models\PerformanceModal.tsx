'use client';

import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';

interface PerformanceModalProps {
  open?: boolean;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onOpenChangeAction?: (open: boolean) => void;
}

const PerformanceModal: React.FC<PerformanceModalProps> = ({
  open = false,
  isOpen = false,
  onOpenChange,
  onOpenChangeAction,
}) => {
  // 统一处理open状态和回调
  const modalOpen = (open || isOpen) ?? false;
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange?.(newOpen);
    onOpenChangeAction?.(newOpen);
  };

  return (
    <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>性能监控</DialogTitle>
        </DialogHeader>
        <div className='p-4'>
          <p>性能监控功能正在开发中...</p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PerformanceModal;
