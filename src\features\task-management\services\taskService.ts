import type { Task } from '@/core/types';

/**
 * Calculates the progress of a single task based on completed and required volume.
 * @param task - The task to calculate progress for.
 * @returns The progress percentage (0-100).
 */
export const calculateTaskProgress = (task: Task): number => {
  if (
    !task ||
    typeof task.requiredVolume !== 'number' ||
    typeof task.completedVolume !== 'number' ||
    task.requiredVolume <= 0
  ) {
    return 0;
  }
  const progress = (task.completedVolume / task.requiredVolume) * 100;
  return Math.max(0, Math.min(Math.round(progress), 100)); // Ensure progress is between 0 and 100
};

// Add other task-related business logic functions here in the future...
// e.g., filterTasks, sortTasks, etc.
// Note: filterTasks logic is currently in taskFilteringService.ts
