/**
 * 配比推荐服务
 * 预先生成不同强度等级的配比推荐
 */

import type { UnifiedRatioMaterial, RatioCalculationParams } from '@/core/types/ratio';

// 辅助函数：创建完整的材料对象
function createMaterial(base: Partial<UnifiedRatioMaterial>): UnifiedRatioMaterial {
  return {
    actualValue: base.actualAmount || 0,
    specificGravity: base.density || 2.6,
    grade: base.specification || '',
    ratio: 1.0,
    percentage: 0,
    ...base,
  } as UnifiedRatioMaterial;
}

export interface RatioRecommendation {
  id: string;
  name: string;
  description: string;
  strengthGrade: string; // C15, C20, C25, C30, C35, C40, C50
  targetStrength: number; // MPa

  // 配比参数
  calculationParams: RatioCalculationParams;

  // 材料配比
  materials: UnifiedRatioMaterial[];

  // 性能指标
  performance: {
    strength: number; // 预测强度 MPa
    workability: number; // 工作性评分 1-10
    durability: number; // 耐久性评分 1-10
    economy: number; // 经济性评分 1-10
  };

  // 适用场景
  applications: string[];

  // 特点
  features: string[];

  // 成本估算
  estimatedCost: number; // 元/m³
}

class RatioRecommendationService {
  private recommendations: RatioRecommendation[] = [];

  constructor() {
    this.initializeRecommendations();
  }

  /**
   * 初始化预设配比推荐
   */
  private initializeRecommendations() {
    this.recommendations = [
      // C15 配比推荐
      {
        id: 'c15-standard',
        name: 'C15标准配比',
        description: '适用于垫层、基础等非承重结构',
        strengthGrade: 'C15',
        targetStrength: 15,
        calculationParams: {
          targetStrength: 15,
          slump: 160,
          maxAggregateSize: 25,
          exposureClass: 'XC1' as any,
          ambientTemperature: 20,
          relativeHumidity: 60,
          cementTemperature: 20,
          aggregateTemperature: 20,
          selectedMaterials: [],
          cementType: 'P.O 42.5',
          aggregateType: '碎石',
          waterType: '自来水',
          waterCementRatio: 0.65,
          sandRatio: 38,
          cementContent: 280,
          waterContent: 182,
          additiveRatio: 0,
          flyashRatio: 0,
          mineralPowderRatio: 0,
          silicaFumeRatio: 0,
          antifreezeRatio: 0,
          expansionRatio: 0,
          cementAmount: 280,
          waterAmount: 182,
          density: 2350,
          airContent: 4.5,
          strengthGrade: 15,
          ultraFineSandRatio: 0,
          earlyStrengthRatio: 0,
          placementMethod: 'pump' as any,
          finishingRequirement: 'smooth' as any,
          cureConditions: {
            method: '标准养护',
            duration: 28,
            temperature: 20,
            humidity: 95,
          },
          admixtureAmount: 0,
          antifreezeAmount: 0,
          flyAshAmount: 0,
          mineralPowderAmount: 0,
          s105PowderAmount: 0,
          expansionAgentAmount: 0,
          earlyStrengthAgentAmount: 0,
          ultraFineSandAmount: 0,
        },
        materials: [
          createMaterial({
            id: 'c15-cement',
            materialId: 'cement-1',
            name: '水泥',
            specification: 'P.O 42.5',
            category: 'cement' as any,
            unit: 'kg',
            density: 3.1,
            theoreticalAmount: 280,
            actualAmount: 280,
            waterContent: 0,
            stoneContent: 0,
            designValue: 280,
            siloId: 'silo-cement',
            siloName: '水泥料仓',
            cost: 0,
            supplier: '',
          }),
          createMaterial({
            id: 'c15-water',
            materialId: 'water-1',
            name: '水',
            specification: '自来水',
            category: 'water' as any,
            unit: 'kg',
            density: 1.0,
            theoreticalAmount: 182,
            actualAmount: 182,
            waterContent: 0,
            stoneContent: 0,
            designValue: 182,
            siloId: 'silo-water',
            siloName: '水料仓',
            cost: 0,
            supplier: '',
          }),
          createMaterial({
            id: 'c15-sand',
            materialId: 'sand-1',
            name: '砂',
            specification: '中砂',
            category: 'aggregate' as any,
            unit: 'kg',
            density: 2.65,
            theoreticalAmount: 720,
            actualAmount: 720,
            waterContent: 0,
            stoneContent: 0,
            designValue: 720,
            siloId: 'silo-sand',
            siloName: '砂料仓',
            cost: 0,
            supplier: '',
          }),
          createMaterial({
            id: 'c15-stone',
            materialId: 'stone-1',
            name: '石子',
            specification: '碎石 5-25mm',
            category: 'aggregate' as any,
            unit: 'kg',
            density: 2.7,
            theoreticalAmount: 1168,
            actualAmount: 1168,
            waterContent: 0,
            stoneContent: 0,
            designValue: 1168,
            siloId: 'silo-stone',
            siloName: '石子料仓',
            cost: 0,
            supplier: '',
          }),
        ],
        performance: {
          strength: 18,
          workability: 6,
          durability: 5,
          economy: 9,
        },
        applications: ['垫层混凝土', '基础垫层', '道路基层', '非承重结构'],
        features: ['成本低廉', '施工简便', '强度适中'],
        estimatedCost: 180,
      },

      // C25 配比推荐
      {
        id: 'c25-standard',
        name: 'C25标准配比',
        description: '适用于一般民用建筑的梁、板、柱等结构',
        strengthGrade: 'C25',
        targetStrength: 25,
        calculationParams: {
          targetStrength: 25,
          slump: 180,
          maxAggregateSize: 25,
          exposureClass: 'XC2' as any,
          ambientTemperature: 20,
          relativeHumidity: 60,
          cementTemperature: 20,
          aggregateTemperature: 20,
          selectedMaterials: [],
          cementType: 'P.O 42.5',
          aggregateType: '碎石',
          waterType: '自来水',
          waterCementRatio: 0.55,
          sandRatio: 36,
          cementContent: 330,
          waterContent: 182,
          additiveRatio: 0.8,
          flyashRatio: 10,
          mineralPowderRatio: 0,
          silicaFumeRatio: 0,
          antifreezeRatio: 0,
          expansionRatio: 0,
          cementAmount: 330,
          waterAmount: 182,
          density: 2400,
          airContent: 4.0,
          strengthGrade: 25,
          ultraFineSandRatio: 0,
          earlyStrengthRatio: 0,
          placementMethod: 'pump' as any,
          finishingRequirement: 'smooth' as any,
          cureConditions: {
            method: '标准养护',
            duration: 28,
            temperature: 20,
            humidity: 95,
          },
          admixtureAmount: 0,
          antifreezeAmount: 0,
          flyAshAmount: 0,
          mineralPowderAmount: 0,
          s105PowderAmount: 0,
          expansionAgentAmount: 0,
          earlyStrengthAgentAmount: 0,
          ultraFineSandAmount: 0,
        },
        materials: [
          createMaterial({
            id: 'c25-cement',
            materialId: 'cement-1',
            name: '水泥',
            specification: 'P.O 42.5',
            category: 'cement' as any,
            unit: 'kg',
            density: 3.1,
            theoreticalAmount: 330,
            actualAmount: 330,
            waterContent: 0,
            stoneContent: 0,
            designValue: 330,
            siloId: 'silo-cement',
            siloName: '水泥料仓',
            cost: 0,
            supplier: '',
          }),
          createMaterial({
            id: 'c25-water',
            materialId: 'water-1',
            name: '水',
            specification: '自来水',
            category: 'water' as any,
            unit: 'kg',
            density: 1.0,
            theoreticalAmount: 182,
            actualAmount: 182,
            waterContent: 0,
            stoneContent: 0,
            designValue: 182,
            siloId: 'silo-water',
            siloName: '水料仓',
            cost: 0,
            supplier: '',
          }),
          createMaterial({
            id: 'c25-sand',
            materialId: 'sand-1',
            name: '砂',
            specification: '中砂',
            category: 'aggregate' as any,
            unit: 'kg',
            density: 2.65,
            theoreticalAmount: 680,
            actualAmount: 680,
            waterContent: 0,
            stoneContent: 0,
            designValue: 680,
            siloId: 'silo-sand',
            siloName: '砂料仓',
            cost: 0,
            supplier: '',
          }),
          createMaterial({
            id: 'c25-stone',
            materialId: 'stone-1',
            name: '石子',
            specification: '碎石 5-25mm',
            category: 'aggregate' as any,
            unit: 'kg',
            density: 2.7,
            theoreticalAmount: 1208,
            actualAmount: 1208,
            waterContent: 0,
            stoneContent: 0,
            designValue: 1208,
            siloId: 'silo-stone',
            siloName: '石子料仓',
            cost: 0,
            supplier: '',
          }),
          createMaterial({
            id: 'c25-additive',
            materialId: 'additive-1',
            name: '外加剂',
            specification: '减水剂',
            category: 'admixture' as any,
            unit: 'kg',
            density: 1.2,
            theoreticalAmount: 2.6,
            actualAmount: 2.6,
            waterContent: 0,
            stoneContent: 0,
            designValue: 2.6,
            siloId: 'silo-additive',
            siloName: '外加剂料仓',
            cost: 0,
            supplier: '',
          }),
          createMaterial({
            id: 'c25-flyash',
            materialId: 'flyash-1',
            name: '粉煤灰',
            specification: 'I级',
            category: 'supplementary' as any,
            unit: 'kg',
            density: 2.2,
            theoreticalAmount: 33,
            actualAmount: 33,
            waterContent: 0,
            stoneContent: 0,
            designValue: 33,
            siloId: 'silo-flyash',
            siloName: '粉煤灰料仓',
            cost: 0,
            supplier: '',
          }),
        ],
        performance: {
          strength: 28,
          workability: 7,
          durability: 7,
          economy: 8,
        },
        applications: ['住宅建筑', '办公楼', '一般工业建筑', '梁板柱结构'],
        features: ['性价比高', '工作性良好', '适用面广'],
        estimatedCost: 220,
      },
    ];

    // 继续添加其他强度等级的配比...
    this.addHighStrengthRecommendations();
    this.addSpecialRecommendations();
  }

  /**
   * 添加高强度配比推荐
   */
  private addHighStrengthRecommendations() {
    // C30 高性能配比
    this.recommendations.push({
      id: 'c30-high-performance',
      name: 'C30高性能配比',
      description: '适用于重要结构，具有优异的强度和耐久性',
      strengthGrade: 'C30',
      targetStrength: 30,
      calculationParams: {
        targetStrength: 30,
        slump: 180,
        maxAggregateSize: 20,
        exposureClass: 'XC3' as any,
        ambientTemperature: 20,
        relativeHumidity: 60,
        cementTemperature: 20,
        aggregateTemperature: 20,
        selectedMaterials: [],
        cementType: 'P.O 42.5',
        aggregateType: '碎石',
        waterType: '自来水',
        waterCementRatio: 0.45,
        sandRatio: 35,
        cementContent: 400,
        waterContent: 180,
        additiveRatio: 1.2,
        flyashRatio: 15,
        mineralPowderRatio: 10,
        silicaFumeRatio: 0,
        antifreezeRatio: 0,
        expansionRatio: 0,
        cementAmount: 400,
        waterAmount: 180,
        density: 2450,
        airContent: 3.5,
        strengthGrade: 30,
        ultraFineSandRatio: 0,
        earlyStrengthRatio: 0,
        placementMethod: 'pump' as any,
        finishingRequirement: 'smooth' as any,
        cureConditions: {
          method: '标准养护',
          duration: 28,
          temperature: 20,
          humidity: 95,
        },
        admixtureAmount: 0,
        antifreezeAmount: 0,
        flyAshAmount: 0,
        mineralPowderAmount: 0,
        s105PowderAmount: 0,
        expansionAgentAmount: 0,
        earlyStrengthAgentAmount: 0,
        ultraFineSandAmount: 0,
      },
      materials: [
        {
          id: 'c30-cement',
          materialId: 'cement-1',
          name: '水泥',
          specification: 'P.O 42.5',
          category: 'cement' as any,
          unit: 'kg',
          density: 3.1,
          theoreticalAmount: 400,
          actualAmount: 400,
          waterContent: 0,
          stoneContent: 0,
          designValue: 400,
          siloId: 'silo-cement',
          siloName: '水泥料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c30-water',
          materialId: 'water-1',
          name: '水',
          specification: '自来水',
          category: 'water' as any,
          unit: 'kg',
          density: 1.0,
          theoreticalAmount: 180,
          actualAmount: 180,
          waterContent: 0,
          stoneContent: 0,
          designValue: 180,
          siloId: 'silo-water',
          siloName: '水料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c30-sand',
          materialId: 'sand-1',
          name: '砂',
          specification: '中砂',
          category: 'aggregate' as any,
          unit: 'kg',
          density: 2.65,
          theoreticalAmount: 650,
          actualAmount: 650,
          waterContent: 0,
          stoneContent: 0,
          designValue: 650,
          siloId: 'silo-sand',
          siloName: '砂料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c30-stone',
          materialId: 'stone-1',
          name: '石子',
          specification: '碎石 5-20mm',
          category: 'aggregate' as any,
          unit: 'kg',
          density: 2.7,
          theoreticalAmount: 1200,
          actualAmount: 1200,
          waterContent: 0,
          stoneContent: 0,
          designValue: 1200,
          siloId: 'silo-stone',
          siloName: '石子料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c30-additive',
          materialId: 'additive-1',
          name: '外加剂',
          specification: '高效减水剂',
          category: 'admixture' as any,
          unit: 'kg',
          density: 1.2,
          theoreticalAmount: 4.8,
          actualAmount: 4.8,
          waterContent: 0,
          stoneContent: 0,
          designValue: 4.8,
          siloId: 'silo-additive',
          siloName: '外加剂料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c30-flyash',
          materialId: 'flyash-1',
          name: '粉煤灰',
          specification: 'I级',
          category: 'supplementary' as any,
          unit: 'kg',
          density: 2.2,
          theoreticalAmount: 60,
          actualAmount: 60,
          waterContent: 0,
          stoneContent: 0,
          designValue: 60,
          siloId: 'silo-flyash',
          siloName: '粉煤灰料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c30-mineral',
          materialId: 'mineral-1',
          name: '矿粉',
          specification: 'S95',
          category: 'supplementary' as any,
          unit: 'kg',
          density: 2.9,
          theoreticalAmount: 40,
          actualAmount: 40,
          waterContent: 0,
          stoneContent: 0,
          designValue: 40,
          siloId: 'silo-mineral',
          siloName: '矿粉料仓',
          cost: 0,
          supplier: '',
        },
      ],
      performance: {
        strength: 35,
        workability: 8,
        durability: 8,
        economy: 7,
      },
      applications: ['高层建筑', '桥梁工程', '重要结构', '预制构件'],
      features: ['高强度', '高耐久性', '工作性优良', '抗渗性好'],
      estimatedCost: 280,
    });

    // C35 高强配比
    this.recommendations.push({
      id: 'c35-high-strength',
      name: 'C35高强配比',
      description: '适用于高层建筑和重要工程结构',
      strengthGrade: 'C35',
      targetStrength: 35,
      calculationParams: {
        targetStrength: 35,
        slump: 180,
        maxAggregateSize: 20,
        exposureClass: 'XC4' as any,
        ambientTemperature: 20,
        relativeHumidity: 60,
        cementTemperature: 20,
        aggregateTemperature: 20,
        selectedMaterials: [],
        cementType: 'P.O 42.5',
        aggregateType: '碎石',
        waterType: '自来水',
        waterCementRatio: 0.4,
        sandRatio: 34,
        cementContent: 450,
        waterContent: 180,
        additiveRatio: 1.5,
        flyashRatio: 20,
        mineralPowderRatio: 15,
        silicaFumeRatio: 5,
        antifreezeRatio: 0,
        expansionRatio: 0,
        cementAmount: 450,
        waterAmount: 180,
        density: 2480,
        airContent: 3.0,
        strengthGrade: 35,
        ultraFineSandRatio: 0,
        earlyStrengthRatio: 0,
        placementMethod: 'pump' as any,
        finishingRequirement: 'smooth' as any,
        cureConditions: {
          method: '标准养护',
          duration: 28,
          temperature: 20,
          humidity: 95,
        },
        admixtureAmount: 0,
        antifreezeAmount: 0,
        flyAshAmount: 0,
        mineralPowderAmount: 0,
        s105PowderAmount: 0,
        expansionAgentAmount: 0,
        earlyStrengthAgentAmount: 0,
        ultraFineSandAmount: 0,
      },
      materials: [
        {
          id: 'c35-cement',
          materialId: 'cement-1',
          name: '水泥',
          specification: 'P.O 42.5',
          category: 'cement' as any,
          unit: 'kg',
          density: 3.1,
          theoreticalAmount: 450,
          actualAmount: 450,
          waterContent: 0,
          stoneContent: 0,
          designValue: 450,
          siloId: 'silo-cement',
          siloName: '水泥料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c35-water',
          materialId: 'water-1',
          name: '水',
          specification: '自来水',
          category: 'water' as any,
          unit: 'kg',
          density: 1.0,
          theoreticalAmount: 180,
          actualAmount: 180,
          waterContent: 0,
          stoneContent: 0,
          designValue: 180,
          siloId: 'silo-water',
          siloName: '水料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c35-sand',
          materialId: 'sand-1',
          name: '砂',
          specification: '中砂',
          category: 'aggregate' as any,
          unit: 'kg',
          density: 2.65,
          theoreticalAmount: 620,
          actualAmount: 620,
          waterContent: 0,
          stoneContent: 0,
          designValue: 620,
          siloId: 'silo-sand',
          siloName: '砂料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c35-stone',
          materialId: 'stone-1',
          name: '石子',
          specification: '碎石 5-20mm',
          category: 'aggregate' as any,
          unit: 'kg',
          density: 2.7,
          theoreticalAmount: 1200,
          actualAmount: 1200,
          waterContent: 0,
          stoneContent: 0,
          designValue: 1200,
          siloId: 'silo-stone',
          siloName: '石子料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c35-additive',
          materialId: 'additive-1',
          name: '外加剂',
          specification: '高效减水剂',
          category: 'admixture' as any,
          unit: 'kg',
          density: 1.2,
          theoreticalAmount: 6.8,
          actualAmount: 6.8,
          waterContent: 0,
          stoneContent: 0,
          designValue: 6.8,
          siloId: 'silo-additive',
          siloName: '外加剂料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c35-flyash',
          materialId: 'flyash-1',
          name: '粉煤灰',
          specification: 'I级',
          category: 'supplementary' as any,
          unit: 'kg',
          density: 2.2,
          theoreticalAmount: 90,
          actualAmount: 90,
          waterContent: 0,
          stoneContent: 0,
          designValue: 90,
          siloId: 'silo-flyash',
          siloName: '粉煤灰料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c35-mineral',
          materialId: 'mineral-1',
          name: '矿粉',
          specification: 'S95',
          category: 'supplementary' as any,
          unit: 'kg',
          density: 2.9,
          theoreticalAmount: 68,
          actualAmount: 68,
          waterContent: 0,
          stoneContent: 0,
          designValue: 68,
          siloId: 'silo-mineral',
          siloName: '矿粉料仓',
          cost: 0,
          supplier: '',
        },
        {
          id: 'c35-silica',
          materialId: 'silica-1',
          name: '硅灰',
          specification: '微硅粉',
          category: 'supplementary' as any,
          unit: 'kg',
          density: 2.2,
          theoreticalAmount: 23,
          actualAmount: 23,
          waterContent: 0,
          stoneContent: 0,
          designValue: 23,
          siloId: 'silo-silica',
          siloName: '硅灰料仓',
          cost: 0,
          supplier: '',
        },
      ],
      performance: {
        strength: 38,
        workability: 7,
        durability: 9,
        economy: 6,
      },
      applications: ['高层建筑', '桥梁工程', '预应力构件', '海工结构'],
      features: ['高强度', '高耐久性', '抗渗性优异', '长期性能稳定'],
      estimatedCost: 320,
    });

    // C40 超高强配比
    this.recommendations.push({
      id: 'c40-ultra-high',
      name: 'C40超高强配比',
      description: '适用于超高层建筑和特殊工程',
      strengthGrade: 'C40',
      targetStrength: 40,
      calculationParams: {
        targetStrength: 40,
        slump: 200,
        maxAggregateSize: 20,
        exposureClass: 'XC4' as any,
        ambientTemperature: 20,
        relativeHumidity: 60,
        cementTemperature: 20,
        aggregateTemperature: 20,
        selectedMaterials: [],
        cementType: 'P.O 52.5',
        aggregateType: '碎石',
        waterType: '自来水',
        waterCementRatio: 0.35,
        sandRatio: 32,
        cementContent: 500,
        waterContent: 175,
        additiveRatio: 2.0,
        flyashRatio: 15,
        mineralPowderRatio: 20,
        silicaFumeRatio: 8,
        antifreezeRatio: 0,
        expansionRatio: 0,
        cementAmount: 500,
        waterAmount: 175,
        density: 2500,
        airContent: 2.5,
        strengthGrade: 40,
        ultraFineSandRatio: 0,
        earlyStrengthRatio: 0,
        placementMethod: 'pump' as any,
        finishingRequirement: 'smooth' as any,
        cureConditions: {
          method: '标准养护',
          duration: 28,
          temperature: 20,
          humidity: 95,
        },
        admixtureAmount: 0,
        antifreezeAmount: 0,
        flyAshAmount: 0,
        mineralPowderAmount: 0,
        s105PowderAmount: 0,
        expansionAgentAmount: 0,
        earlyStrengthAgentAmount: 0,
        ultraFineSandAmount: 0,
      },
      materials: [], // 简化，实际应用中需要完整材料列表
      performance: {
        strength: 42,
        workability: 8,
        durability: 9,
        economy: 5,
      },
      applications: ['超高层建筑', '大跨度结构', '核电工程', '海洋平台'],
      features: ['超高强度', '优异耐久性', '低渗透性', '高模量'],
      estimatedCost: 380,
    });
  }

  /**
   * 添加特殊配比推荐
   */
  private addSpecialRecommendations() {
    // 经济型配比
    this.recommendations.push({
      id: 'c20-economy',
      name: 'C20经济配比',
      description: '成本优化的经济型配比，适用于一般工程',
      strengthGrade: 'C20',
      targetStrength: 20,
      calculationParams: {
        targetStrength: 20,
        slump: 160,
        maxAggregateSize: 25,
        exposureClass: 'XC1' as any,
        ambientTemperature: 20,
        relativeHumidity: 60,
        cementTemperature: 20,
        aggregateTemperature: 20,
        selectedMaterials: [],
        cementType: 'P.C 32.5',
        aggregateType: '碎石',
        waterType: '自来水',
        waterCementRatio: 0.6,
        sandRatio: 37,
        cementContent: 300,
        waterContent: 180,
        additiveRatio: 0,
        flyashRatio: 20,
        mineralPowderRatio: 0,
        silicaFumeRatio: 0,
        antifreezeRatio: 0,
        expansionRatio: 0,
        cementAmount: 300,
        waterAmount: 180,
        density: 2380,
        airContent: 4.0,
        strengthGrade: 20,
        ultraFineSandRatio: 0,
        earlyStrengthRatio: 0,
        placementMethod: 'pump' as any,
        finishingRequirement: 'smooth' as any,
        cureConditions: {
          method: '标准养护',
          duration: 28,
          temperature: 20,
          humidity: 95,
        },
        admixtureAmount: 0,
        antifreezeAmount: 0,
        flyAshAmount: 0,
        mineralPowderAmount: 0,
        s105PowderAmount: 0,
        expansionAgentAmount: 0,
        earlyStrengthAgentAmount: 0,
        ultraFineSandAmount: 0,
      },
      materials: [], // 简化
      performance: {
        strength: 22,
        workability: 7,
        durability: 6,
        economy: 10,
      },
      applications: ['住宅建筑', '一般工业建筑', '道路工程', '基础工程'],
      features: ['成本最优', '施工便利', '性能稳定', '适用性广'],
      estimatedCost: 160,
    });
  }

  /**
   * 获取所有配比推荐
   */
  getAllRecommendations(): RatioRecommendation[] {
    return this.recommendations;
  }

  /**
   * 根据强度等级获取推荐
   */
  getRecommendationsByStrength(strengthGrade: string): RatioRecommendation[] {
    return this.recommendations.filter(r => r.strengthGrade === strengthGrade);
  }

  /**
   * 根据ID获取推荐
   */
  getRecommendationById(id: string): RatioRecommendation | undefined {
    return this.recommendations.find(r => r.id === id);
  }

  /**
   * 根据应用场景获取推荐
   */
  getRecommendationsByApplication(application: string): RatioRecommendation[] {
    return this.recommendations.filter(r => r.applications.some(app => app.includes(application)));
  }
}

export const ratioRecommendationService = new RatioRecommendationService();
