'use client';

// src/hooks/useTankTruckDispatchModal.ts
import { useCallback, useState } from 'react';

import type { Task, Vehicle } from '@/core/types';

import type { TankTruckDispatchData } from '@/models/tank-truck-dispatch-modal';

interface UseTankTruckDispatchModalReturn {
  isOpen: boolean;
  task: Task | null;
  vehicle: Vehicle | null;
  lineId: string | null;
  openModal: (task: Task, vehicle: Vehicle, lineId: string) => void;
  closeModal: () => void;
  handleConfirm: (dispatchData: TankTruckDispatchData) => Promise<void>;
}

interface UseTankTruckDispatchModalProps {
  onDispatchConfirm: (
    vehicle: Vehicle,
    task: Task,
    lineId: string,
    dispatchData: TankTruckDispatchData
  ) => Promise<void>;
}

/**
 * 罐车发车单模态框管理Hook
 */
export function useTankTruckDispatchModal({
  onDispatchConfirm,
}: UseTankTruckDispatchModalProps): UseTankTruckDispatchModalReturn {
  const [isOpen, setIsOpen] = useState(false);
  const [task, setTask] = useState<Task | null>(null);
  const [vehicle, setVehicle] = useState<Vehicle | null>(null);
  const [lineId, setLineId] = useState<string | null>(null);

  const openModal = useCallback(
    (task: Task, vehicle: Vehicle, lineId: string) => {
      console.log('🚛 useTankTruckDispatchModal.openModal called:', {
        task: task.taskNumber,
        vehicle: vehicle.vehicleNumber,
        lineId,
        currentIsOpen: isOpen,
      });

      setTask(task);
      setVehicle(vehicle);
      setLineId(lineId);
      setIsOpen(true);

      console.log('✅ Tank truck dispatch modal state updated, isOpen should be true');
    },
    [isOpen]
  );

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setTask(null);
    setVehicle(null);
    setLineId(null);
  }, []);

  const handleConfirm = useCallback(
    async (dispatchData: TankTruckDispatchData) => {
      if (!vehicle || !task || !lineId) {
        console.error('Missing required data for dispatch confirmation');
        return;
      }

      try {
        await onDispatchConfirm(vehicle, task, lineId, dispatchData);
        closeModal();
      } catch (error) {
        console.error('Failed to confirm dispatch:', error);
        // 不关闭模态框，让用户重试
      }
    },
    [vehicle, task, lineId, onDispatchConfirm, closeModal]
  );

  return {
    isOpen,
    task,
    vehicle,
    lineId,
    openModal,
    closeModal,
    handleConfirm,
  };
}
