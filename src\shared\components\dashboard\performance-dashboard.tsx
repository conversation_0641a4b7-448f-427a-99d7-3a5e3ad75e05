'use client';

import React, { useEffect, useMemo, useState } from 'react';

import {
  Activity,
  AlertTriangle,
  BarChart3,
  Clock,
  Eye,
  Lightbulb,
  Monitor,
  RefreshCw,
  TrendingDown,
  Zap,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';

// 时间格式化函数
function formatTime(date: Date): string {
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');
  return `${hour}:${minute}:${second}`;
}
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/card';
import { Progress } from '@/shared/components/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/tabs';
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from '@/shared/components/alert';

import { performanceManager } from '@/shared/services/PerformanceManager';
import { benchmarkService } from '@/shared/services/BenchmarkService';
import {
  ExtendedWebVitals,
  ComponentPerformanceMetrics,
  PerformanceIssue,
  PerformanceRecommendation,
  PerformanceBenchmark,
} from '@/core/types/performance';
import { performanceLogger } from '../../../core/lib/logger';
import { rum } from '../../../core/lib/rum';

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  status: 'good' | 'needs-improvement' | 'poor';
  threshold: { good: number; poor: number };
  description: string;
}

interface SystemMetric {
  name: string;
  value: number;
  max: number;
  unit: string;
  status: 'normal' | 'warning' | 'critical';
}

// 辅助函数：获取性能指标状态
function getVitalStatus(
  value: number,
  threshold: { good: number; poor: number }
): 'good' | 'needs-improvement' | 'poor' {
  if (value <= threshold.good) return 'good';
  if (value <= threshold.poor) return 'needs-improvement';
  return 'poor';
}

// 辅助函数：获取系统状态
function getSystemStatus(metrics: SystemMetric[]): 'normal' | 'warning' | 'critical' {
  const criticalCount = metrics.filter(m => m.status === 'critical').length;
  const warningCount = metrics.filter(m => m.status === 'warning').length;

  if (criticalCount > 0) return 'critical';
  if (warningCount > 0) return 'warning';
  return 'normal';
}

export function PerformanceDashboard() {
  const [webVitals, setWebVitals] = useState<PerformanceMetric[]>([]);
  const [extendedWebVitals, setExtendedWebVitals] = useState<ExtendedWebVitals[]>([]);
  const [componentMetrics, setComponentMetrics] = useState<ComponentPerformanceMetrics[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetric[]>([]);
  const [performanceIssues, setPerformanceIssues] = useState<PerformanceIssue[]>([]);
  const [recommendations, setRecommendations] = useState<PerformanceRecommendation[]>([]);
  const [benchmarks, setBenchmarks] = useState<PerformanceBenchmark[]>([]);
  const [rumData, setRumData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [activeTab, setActiveTab] = useState('overview');

  // 收集增强的性能数据
  const collectEnhancedPerformanceData = () => {
    // 收集扩展的Web Vitals
    const extendedVitals = performanceManager.getWebVitals();
    setExtendedWebVitals(extendedVitals);

    // 收集组件性能指标
    const componentData = performanceManager.getComponentMetrics();
    setComponentMetrics(componentData);

    // 收集性能问题
    const issues = componentData.flatMap(metric => {
      const issues: PerformanceIssue[] = [];

      if (metric.renderTime > 16) {
        issues.push({
          id: `render-${metric.componentName}-${Date.now()}`,
          type: 'warning',
          category: 'rendering',
          title: '渲染时间过长',
          description: `组件 ${metric.componentName} 渲染时间为 ${metric.renderTime.toFixed(2)}ms`,
          impact: '可能导致页面卡顿',
          details: {
            component: metric.componentName,
            metric: 'renderTime',
            currentValue: metric.renderTime,
            expectedValue: 16,
            threshold: 16,
          },
          solutions: ['使用 React.memo', '优化组件逻辑', '减少重渲染'],
          priority: 7,
          timestamp: new Date().toISOString(),
        });
      }

      return issues;
    });
    setPerformanceIssues(issues);

    // 生成优化建议
    const newRecommendations: PerformanceRecommendation[] = [];

    if (issues.length > 0) {
      newRecommendations.push({
        id: `rec-${Date.now()}`,
        title: '优化组件渲染性能',
        description: '检测到多个组件存在渲染性能问题',
        type: 'code',
        expectedImpact: {
          metric: 'renderTime',
          improvement: 30,
          effort: 'medium',
        },
        steps: [
          '使用 React.memo 包装组件',
          '优化 useEffect 依赖数组',
          '使用 useMemo 缓存计算结果',
          '减少不必要的状态更新',
        ],
        resources: {
          documentation: 'https://react.dev/reference/react/memo',
          examples: ['React.memo 使用示例', 'useMemo 优化案例'],
        },
        priority: 8,
      });
    }

    setRecommendations(newRecommendations);

    // 收集基准测试数据
    const benchmarkData = benchmarkService.getAllBenchmarks();
    setBenchmarks(benchmarkData);
  };

  // 收集传统 Web Vitals 数据（保持向后兼容）
  const collectWebVitals = () => {
    const vitals: PerformanceMetric[] = [
      {
        name: 'First Contentful Paint',
        value: 0,
        unit: 'ms',
        status: 'good',
        threshold: { good: 1800, poor: 3000 },
        description: '首次内容绘制时间',
      },
      {
        name: 'Largest Contentful Paint',
        value: 0,
        unit: 'ms',
        status: 'good',
        threshold: { good: 2500, poor: 4000 },
        description: '最大内容绘制时间',
      },
      {
        name: 'First Input Delay',
        value: 0,
        unit: 'ms',
        status: 'good',
        threshold: { good: 100, poor: 300 },
        description: '首次输入延迟',
      },
      {
        name: 'Cumulative Layout Shift',
        value: 0,
        unit: '',
        status: 'good',
        threshold: { good: 0.1, poor: 0.25 },
        description: '累积布局偏移',
      },
    ];

    // 获取实际性能数据
    if (typeof window !== 'undefined') {
      // FCP
      const fcpEntries = performance.getEntriesByName('first-contentful-paint');
      if (fcpEntries.length > 0 && vitals[0] && fcpEntries[0]) {
        vitals[0].value = Math.round(fcpEntries[0].startTime);
        vitals[0].status = getVitalStatus(vitals[0].value, vitals[0].threshold);
      }

      // 使用 PerformanceObserver 获取其他指标
      if ('PerformanceObserver' in window) {
        // LCP
        const lcpObserver = new PerformanceObserver(list => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          if (lastEntry && vitals[1]) {
            vitals[1].value = Math.round(lastEntry.startTime);
            vitals[1].status = getVitalStatus(vitals[1].value, vitals[1].threshold);
            setWebVitals([...vitals]);
          }
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // FID
        const fidObserver = new PerformanceObserver(list => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (vitals[2]) {
              vitals[2].value = Math.round(entry.processingStart - entry.startTime);
              vitals[2].status = getVitalStatus(vitals[2].value, vitals[2].threshold);
              setWebVitals([...vitals]);
            }
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });

        // CLS
        let clsValue = 0;
        const clsObserver = new PerformanceObserver(list => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput && vitals[3]) {
              clsValue += entry.value;
              vitals[3].value = Math.round(clsValue * 1000) / 1000;
              vitals[3].status = getVitalStatus(vitals[3].value, vitals[3].threshold);
              setWebVitals([...vitals]);
            }
          });
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      }
    }

    setWebVitals(vitals);
  };

  // 收集系统指标
  const collectSystemMetrics = () => {
    const metrics: SystemMetric[] = [];

    if (typeof window !== 'undefined') {
      // 内存使用情况
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
        metrics.push({
          name: 'JS Heap Used',
          value: Math.round(memory.usedJSHeapSize / 1024 / 1024),
          max: Math.round(memory.jsHeapSizeLimit / 1024 / 1024),
          unit: 'MB',
          status:
            memoryUsageRatio > 0.8 ? 'critical' : memoryUsageRatio > 0.6 ? 'warning' : 'normal',
        });
      }

      // CPU 核心数
      if ('hardwareConcurrency' in navigator) {
        metrics.push({
          name: 'CPU Cores',
          value: navigator.hardwareConcurrency,
          max: navigator.hardwareConcurrency,
          unit: 'cores',
          status: 'normal',
        });
      }

      // 设备内存
      if ('deviceMemory' in navigator) {
        metrics.push({
          name: 'Device Memory',
          value: (navigator as any).deviceMemory,
          max: (navigator as any).deviceMemory,
          unit: 'GB',
          status: 'normal',
        });
      }

      // 网络连接
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        metrics.push({
          name: 'Network Speed',
          value: connection.downlink || 0,
          max: 100,
          unit: 'Mbps',
          status:
            connection.downlink > 10 ? 'normal' : connection.downlink > 1 ? 'warning' : 'critical',
        });
      }
    }

    setSystemMetrics(metrics);
  };

  // 获取 RUM 数据
  const collectRumData = () => {
    const data = rum.getMetrics();
    setRumData(data);
  };

  // 刷新所有数据
  const refreshData = () => {
    setIsLoading(true);
    collectEnhancedPerformanceData();
    collectWebVitals();
    collectSystemMetrics();
    collectRumData();
    setLastUpdate(new Date());

    setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    performanceLogger.info('Performance dashboard refreshed');
  };

  useEffect(() => {
    refreshData();

    // 定期刷新数据
    const interval = setInterval(refreshData, 30000); // 30秒刷新一次

    return () => clearInterval(interval);
  }, []);

  // 状态颜色映射
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
      case 'normal':
        return 'text-green-600 bg-green-100';
      case 'needs-improvement':
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'poor':
      case 'critical':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 计算总体性能分数
  const overallScore = useMemo(() => {
    if (webVitals.length === 0) return 0;

    const scores = webVitals.map(vital => {
      switch (vital.status) {
        case 'good':
          return 100;
        case 'needs-improvement':
          return 60;
        case 'poor':
          return 20;
        default:
          return 0;
      }
    });

    return Math.round(
      scores.reduce((sum: number, score: number) => sum + score, 0) / scores.length
    );
  }, [webVitals]);

  return (
    <div className='space-y-6'>
      {/* 头部信息 */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>性能监控仪表板</h2>
          <p className='text-muted-foreground'>实时监控应用性能指标和用户体验数据</p>
        </div>
        <div className='flex items-center gap-4'>
          <div className='text-sm text-muted-foreground'>最后更新: {formatTime(lastUpdate)}</div>
          <Button onClick={refreshData} disabled={isLoading} size='sm'>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>

      {/* 总体性能分数 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <BarChart3 className='w-5 h-5' />
            总体性能分数
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex items-center gap-4'>
            <div className='text-4xl font-bold'>
              {overallScore}
              <span className='text-lg text-muted-foreground'>/100</span>
            </div>
            <Progress value={overallScore} className='flex-1' />
            <Badge
              className={getStatusColor(
                overallScore >= 80 ? 'good' : overallScore >= 60 ? 'needs-improvement' : 'poor'
              )}
            >
              {overallScore >= 80 ? '优秀' : overallScore >= 60 ? '良好' : '需要改进'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className='space-y-4'>
        <TabsList>
          <TabsTrigger value='overview'>概览</TabsTrigger>
          <TabsTrigger value='web-vitals'>Web Vitals</TabsTrigger>
          <TabsTrigger value='components'>组件性能</TabsTrigger>
          <TabsTrigger value='issues'>性能问题</TabsTrigger>
          <TabsTrigger value='benchmarks'>基准测试</TabsTrigger>
          <TabsTrigger value='system'>系统指标</TabsTrigger>
          <TabsTrigger value='user-behavior'>用户行为</TabsTrigger>
          <TabsTrigger value='errors'>错误监控</TabsTrigger>
        </TabsList>

        {/* 概览标签页 */}
        <TabsContent value='overview' className='space-y-4'>
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>活跃组件</CardTitle>
                <Monitor className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{componentMetrics.length}</div>
                <p className='text-xs text-muted-foreground'>正在监控的组件数量</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>性能问题</CardTitle>
                <AlertTriangle className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-red-600'>{performanceIssues.length}</div>
                <p className='text-xs text-muted-foreground'>需要关注的性能问题</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>平均渲染时间</CardTitle>
                <Clock className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  {componentMetrics.length > 0
                    ? (
                        componentMetrics.reduce((sum, m) => sum + m.renderTime, 0) /
                        componentMetrics.length
                      ).toFixed(1)
                    : '0'}
                  ms
                </div>
                <p className='text-xs text-muted-foreground'>所有组件平均渲染时间</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>基准测试</CardTitle>
                <BarChart3 className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{benchmarks.length}</div>
                <p className='text-xs text-muted-foreground'>已创建的性能基准</p>
              </CardContent>
            </Card>
          </div>

          {/* 性能问题快速预览 */}
          {performanceIssues.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <AlertTriangle className='h-5 w-5 text-red-500' />
                  性能问题概览
                </CardTitle>
                <CardDescription>
                  检测到 {performanceIssues.length} 个性能问题需要关注
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-2'>
                  {performanceIssues.slice(0, 3).map(issue => (
                    <Alert key={issue.id} className='border-l-4 border-l-red-500'>
                      <AlertTriangle className='h-4 w-4' />
                      <AlertTitle>{issue.title}</AlertTitle>
                      <AlertDescription>{issue.description}</AlertDescription>
                    </Alert>
                  ))}
                  {performanceIssues.length > 3 && (
                    <p className='text-sm text-muted-foreground'>
                      还有 {performanceIssues.length - 3} 个问题，查看"性能问题"标签页了解详情
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 优化建议快速预览 */}
          {recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Lightbulb className='h-5 w-5 text-yellow-500' />
                  优化建议
                </CardTitle>
                <CardDescription>基于当前性能数据生成的优化建议</CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  {recommendations.slice(0, 2).map(rec => (
                    <div key={rec.id} className='border-l-4 border-l-blue-500 pl-4'>
                      <h4 className='font-medium'>{rec.title}</h4>
                      <p className='text-sm text-muted-foreground'>{rec.description}</p>
                      <div className='mt-2 flex items-center gap-2'>
                        <Badge variant='outline' className='text-xs'>
                          预期提升: {rec.expectedImpact.improvement}%
                        </Badge>
                        <Badge variant='outline' className='text-xs'>
                          工作量:{' '}
                          {rec.expectedImpact.effort === 'low'
                            ? '低'
                            : rec.expectedImpact.effort === 'medium'
                              ? '中'
                              : '高'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Web Vitals 标签页 */}
        <TabsContent value='web-vitals' className='space-y-4'>
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
            {webVitals.map((vital, index) => (
              <Card key={index}>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-sm font-medium'>{vital.name}</CardTitle>
                  <Badge className={getStatusColor(vital.status)}>
                    {vital.status === 'good'
                      ? '优秀'
                      : vital.status === 'needs-improvement'
                        ? '良好'
                        : '差'}
                  </Badge>
                </CardHeader>
                <CardContent>
                  <div className='text-2xl font-bold'>
                    {vital.value}
                    <span className='text-sm text-muted-foreground ml-1'>{vital.unit}</span>
                  </div>
                  <p className='text-xs text-muted-foreground'>{vital.description}</p>
                  <div className='mt-2'>
                    <Progress
                      value={Math.min((vital.value / vital.threshold.poor) * 100, 100)}
                      className='h-2'
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* 系统指标标签页 */}
        <TabsContent value='system' className='space-y-4'>
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
            {systemMetrics.map((metric, index) => (
              <Card key={index}>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-sm font-medium'>{metric.name}</CardTitle>
                  <Badge className={getStatusColor(metric.status)}>
                    {metric.status === 'normal'
                      ? '正常'
                      : metric.status === 'warning'
                        ? '警告'
                        : '严重'}
                  </Badge>
                </CardHeader>
                <CardContent>
                  <div className='text-2xl font-bold'>
                    {metric.value}
                    <span className='text-sm text-muted-foreground ml-1'>{metric.unit}</span>
                  </div>
                  <div className='mt-2'>
                    <Progress value={(metric.value / metric.max) * 100} className='h-2' />
                  </div>
                  <p className='text-xs text-muted-foreground mt-1'>
                    最大值: {metric.max} {metric.unit}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* 用户行为标签页 */}
        <TabsContent value='user-behavior' className='space-y-4'>
          {rumData?.userBehavior && (
            <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-sm font-medium'>页面浏览量</CardTitle>
                  <Eye className='h-4 w-4 text-muted-foreground' />
                </CardHeader>
                <CardContent>
                  <div className='text-2xl font-bold'>{rumData.userBehavior.pageViews}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-sm font-medium'>点击次数</CardTitle>
                  <Activity className='h-4 w-4 text-muted-foreground' />
                </CardHeader>
                <CardContent>
                  <div className='text-2xl font-bold'>{rumData.userBehavior.clickCount}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-sm font-medium'>滚动深度</CardTitle>
                  <TrendingDown className='h-4 w-4 text-muted-foreground' />
                </CardHeader>
                <CardContent>
                  <div className='text-2xl font-bold'>{rumData.userBehavior.scrollDepth}%</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-sm font-medium'>停留时间</CardTitle>
                  <Clock className='h-4 w-4 text-muted-foreground' />
                </CardHeader>
                <CardContent>
                  <div className='text-2xl font-bold'>
                    {Math.round(rumData.userBehavior.timeOnPage / 1000)}s
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        {/* 错误监控标签页 */}
        <TabsContent value='errors' className='space-y-4'>
          {rumData?.errors && (
            <div className='grid gap-4 md:grid-cols-2'>
              <Card>
                <CardHeader>
                  <CardTitle>JavaScript 错误</CardTitle>
                  <CardDescription>
                    当前会话中的 JS 错误数量: {rumData.errors.jsErrors.length}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {rumData.errors.jsErrors.length > 0 ? (
                    <div className='space-y-2'>
                      {rumData.errors.jsErrors.slice(0, 3).map((error: any, index: number) => (
                        <div key={index} className='p-2 bg-red-50 rounded text-sm'>
                          <div className='font-medium text-red-800'>{error.message}</div>
                          <div className='text-red-600 text-xs'>
                            {error.url}:{error.line}:{error.column}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className='text-green-600'>无错误记录</div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>资源错误</CardTitle>
                  <CardDescription>
                    资源加载失败数量: {rumData.errors.resourceErrors.length}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {rumData.errors.resourceErrors.length > 0 ? (
                    <div className='space-y-2'>
                      {rumData.errors.resourceErrors
                        .slice(0, 3)
                        .map((error: any, index: number) => (
                          <div key={index} className='p-2 bg-yellow-50 rounded text-sm'>
                            <div className='font-medium text-yellow-800'>{error.type}</div>
                            <div className='text-yellow-600 text-xs'>{error.url}</div>
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className='text-green-600'>无资源错误</div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
