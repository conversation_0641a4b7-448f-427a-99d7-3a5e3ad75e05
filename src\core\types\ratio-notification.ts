/**
 * 配比通知单相关类型定义
 */

import type { RatioCalculationParams, UnifiedRatioMaterial } from './ratio';

/**
 * 配比通知单基本信息
 */
export interface RatioNotificationInfo {
  /** 通知单编号 */
  notificationNumber: string;
  /** 任务编号 */
  taskNumber: string;
  /** 项目名称 */
  projectName: string;
  /** 施工部位 */
  constructionSite: string;
  /** 强度等级 */
  strengthGrade: string;
  /** 发布日期 */
  issueDate: string;
  /** 有效期 */
  validUntil?: string;
  /** 施工单位 */
  constructionUnit?: string;
  /** 设计单位 */
  designUnit?: string;
}

/**
 * 配比通知单技术参数
 */
export interface RatioNotificationParams {
  /** 水胶比 */
  waterCementRatio: number;
  /** 砂率 (%) */
  sandRatio: number;
  /** 用水量 (kg/m³) */
  waterContent: number;
  /** 坍落度 (mm) */
  slump: number;
  /** 抗渗等级 */
  impermeabilityGrade?: string;
  /** 抗冻等级 */
  freezeResistanceGrade?: string;
  /** 氯离子含量限制 */
  chlorideLimit?: number;
  /** 碱含量限制 */
  alkaliLimit?: number;
}

/**
 * 配比通知单材料信息
 */
export interface RatioNotificationMaterial {
  /** 材料名称 */
  materialName: string;
  /** 材料类型 */
  materialType: string;
  /** 规格型号 */
  specification?: string;
  /** 用量 (kg/m³) */
  dosage: number;
  /** 供应商 */
  supplier?: string;
  /** 质量要求 */
  qualityRequirement?: string;
  /** 备注 */
  remarks?: string;
}

/**
 * 配比通知单质量要求
 */
export interface RatioNotificationQuality {
  /** 28天抗压强度 (MPa) */
  compressiveStrength28d: number;
  /** 7天抗压强度 (MPa) */
  compressiveStrength7d?: number;
  /** 弹性模量 (GPa) */
  elasticModulus?: number;
  /** 收缩率限制 */
  shrinkageLimit?: number;
  /** 泌水率限制 */
  bleedingLimit?: number;
  /** 凝结时间要求 */
  settingTimeRequirement?: {
    initialSetting: number; // 初凝时间 (min)
    finalSetting: number; // 终凝时间 (min)
  };
}

/**
 * 配比通知单审批信息
 */
export interface RatioNotificationApproval {
  /** 技术负责人 */
  technicalManager: string;
  /** 审批人 */
  approver: string;
  /** 审批时间 */
  approvalDate: string;
  /** 审批意见 */
  approvalComments?: string;
  /** 审批状态 */
  approvalStatus: 'pending' | 'approved' | 'rejected' | 'expired';
  /** 版本号 */
  version: number;
}

/**
 * 完整的配比通知单
 */
export interface RatioNotification {
  /** 唯一标识 */
  id: string;
  /** 基本信息 */
  info: RatioNotificationInfo;
  /** 技术参数 */
  params: RatioNotificationParams;
  /** 材料配比 */
  materials: RatioNotificationMaterial[];
  /** 质量要求 */
  quality: RatioNotificationQuality;
  /** 审批信息 */
  approval: RatioNotificationApproval;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 备注说明 */
  notes?: string;
}

/**
 * 通知单解析结果
 */
export interface NotificationParseResult {
  /** 解析是否成功 */
  success: boolean;
  /** 解析后的通知单数据 */
  notification?: RatioNotification;
  /** 错误信息 */
  errors?: string[];
  /** 警告信息 */
  warnings?: string[];
  /** 解析的原始数据 */
  rawData?: any;
}

/**
 * 通知单应用结果
 */
export interface NotificationApplyResult {
  /** 应用是否成功 */
  success: boolean;
  /** 应用后的计算参数 */
  calculationParams?: RatioCalculationParams;
  /** 应用后的材料列表 */
  materials?: UnifiedRatioMaterial[];
  /** 错误信息 */
  errors?: string[];
  /** 警告信息 */
  warnings?: string[];
  /** 应用摘要 */
  summary?: {
    updatedParams: string[];
    updatedMaterials: string[];
    addedMaterials: string[];
    removedMaterials: string[];
  };
}

/**
 * 通知单来源类型
 */
export type NotificationSource = 'file' | 'history' | 'manual' | 'template';

/**
 * 通知单选择选项
 */
export interface NotificationSelectOption {
  /** 选项ID */
  id: string;
  /** 显示标题 */
  title: string;
  /** 描述信息 */
  description: string;
  /** 来源类型 */
  source: NotificationSource;
  /** 通知单数据 */
  notification?: RatioNotification;
  /** 文件信息（如果是文件来源） */
  fileInfo?: {
    name: string;
    size: number;
    type: string;
    lastModified: number;
  };
}

/**
 * 支持的文件格式
 */
export type SupportedFileFormat = 'json' | 'xml' | 'txt' | 'csv';

/**
 * 文件解析配置
 */
export interface FileParseConfig {
  /** 文件格式 */
  format: SupportedFileFormat;
  /** 编码格式 */
  encoding?: string;
  /** 字段映射 */
  fieldMapping?: Record<string, string>;
  /** 是否严格模式 */
  strictMode?: boolean;
}
