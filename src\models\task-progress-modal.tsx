'use client';

// 重新导出新版本的任务进度模态框以保持向后兼容性
export {
  TaskProgressModalV2,
  type ProgressPoint,
  type VehicleDispatchRecord,
  type TaskProgressData,
} from './task-progress-modal-v2';

// 安全的别名导出
export { TaskProgressModalV3 as TaskProgressModal } from './task-progress-modal-v3';

// 为了保持向后兼容性，也导出原始接口
export interface LegacyProgressPoint {
  time: string; // 时间点
  volume: number; // 累计方量
  isTarget?: boolean; // 是否为目标点
}

export interface LegacyTaskProgressData {
  projectName: string;
  firstDispatchTime: string;
  lastDispatchTime: string;
  plannedVolume: number;
  completedVolume: number;
  progressData: LegacyProgressPoint[];
}
