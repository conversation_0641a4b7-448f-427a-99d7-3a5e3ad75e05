'use client';

import React, { useState, useCallback, useEffect } from 'react';
import {
  Download,
  Upload,
  Trash2,
  RefreshCw,
  Save,
  Database,
  FileText,
  Settings,
  Palette,
  Grid,
  BarChart3,
} from 'lucide-react';

import { Button } from '@/shared/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/card';
import { Badge } from '@/shared/components/badge';
import { Separator } from '@/shared/components/separator';
import { useToast } from '@/shared/hooks/use-toast';
import {
  PersistenceManager,
  STORAGE_KEYS,
} from '@/infrastructure/storage/persistence/persistenceManager';
import { safeDownloadFile } from '@/core/utils/dom-safe-operations';

// 配置类型映射
const CONFIG_TYPES = {
  TASK_LIST_SETTINGS: { icon: Settings, label: '任务列表设置', color: 'blue' },
  TASK_CARD_CONFIG: { icon: Grid, label: '任务卡片配置', color: 'green' },
  TASK_CARD_LAYOUT: { icon: Grid, label: '卡片布局配置', color: 'green' },
  APP_THEME: { icon: Palette, label: '应用主题', color: 'purple' },
  APP_DENSITY: { icon: BarChart3, label: '界面密度', color: 'orange' },
  TABLE_COLUMN_WIDTHS: { icon: Database, label: '表格列宽', color: 'cyan' },
  TABLE_COLUMN_ORDER: { icon: Database, label: '表格列顺序', color: 'cyan' },
  TABLE_COLUMN_VISIBILITY: { icon: Database, label: '表格列可见性', color: 'cyan' },
  TABLE_COLUMN_STYLES: { icon: Database, label: '表格列样式', color: 'cyan' },
  CARD_GRID_CONFIG: { icon: Grid, label: '卡片网格配置', color: 'green' },
  CARD_VEHICLE_STYLES: { icon: Grid, label: '车辆卡片样式', color: 'green' },
  GROUPING_CONFIG: { icon: BarChart3, label: '分组配置', color: 'yellow' },
  UI_PREFERENCES: { icon: Settings, label: 'UI偏好设置', color: 'gray' },
  PERFORMANCE_SETTINGS: { icon: BarChart3, label: '性能设置', color: 'red' },
} as const;

export function PersistenceManagementPanel() {
  const { toast } = useToast();
  const [storageInfo, setStorageInfo] = useState<ReturnType<
    typeof PersistenceManager.getStorageInfo
  > | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // 客户端初始化
  useEffect(() => {
    setIsClient(true);
    setStorageInfo(PersistenceManager.getStorageInfo());
  }, []);

  // 刷新存储信息
  const refreshStorageInfo = useCallback(() => {
    if (typeof window !== 'undefined') {
      setStorageInfo(PersistenceManager.getStorageInfo());
    }
  }, []);

  // 导出所有配置
  const handleExportAll = useCallback(() => {
    try {
      const config = PersistenceManager.exportAll();
      const dataStr = JSON.stringify(config, null, 2);
      // 使用安全的下载函数
      safeDownloadFile(
        dataStr,
        `tmh-config-${new Date().toISOString().split('T')[0]}.json`,
        'application/json'
      );

      toast({
        title: '导出成功',
        description: '所有配置已导出到文件',
      });
    } catch (error) {
      toast({
        title: '导出失败',
        description: '配置导出过程中发生错误',
        variant: 'destructive',
      });
    }
  }, [toast]);

  // 导入配置
  const handleImportConfig = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = e => {
        try {
          const configData = JSON.parse(e.target?.result as string);
          const success = PersistenceManager.importAll(configData);

          if (success) {
            toast({
              title: '导入成功',
              description: '配置已成功导入，请刷新页面以应用更改',
            });
            refreshStorageInfo();
          } else {
            throw new Error('部分配置导入失败');
          }
        } catch (error) {
          toast({
            title: '导入失败',
            description: '配置文件格式错误或导入过程中发生错误',
            variant: 'destructive',
          });
        }
      };
      reader.readAsText(file);

      // 清空input值，允许重复选择同一文件
      event.target.value = '';
    },
    [toast, refreshStorageInfo]
  );

  // 清除所有配置
  const handleClearAll = useCallback(() => {
    if (!confirm('确定要清除所有配置吗？此操作不可撤销！')) {
      return;
    }

    setIsLoading(true);
    try {
      const success = PersistenceManager.clearAll();
      if (success) {
        toast({
          title: '清除成功',
          description: '所有配置已清除，请刷新页面',
        });
        refreshStorageInfo();
      } else {
        throw new Error('清除失败');
      }
    } catch (error) {
      toast({
        title: '清除失败',
        description: '清除配置过程中发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast, refreshStorageInfo]);

  // 创建备份
  const handleCreateBackup = useCallback(() => {
    try {
      const backupKey = PersistenceManager.createBackup();
      toast({
        title: '备份成功',
        description: `配置备份已创建: ${backupKey}`,
      });
    } catch (error) {
      toast({
        title: '备份失败',
        description: '创建备份过程中发生错误',
        variant: 'destructive',
      });
    }
  }, [toast]);

  // 删除单个配置
  const handleDeleteConfig = useCallback(
    (key: keyof typeof STORAGE_KEYS) => {
      if (!confirm(`确定要删除 ${CONFIG_TYPES[key]?.label} 配置吗？`)) {
        return;
      }

      const success = PersistenceManager.remove(key);
      if (success) {
        toast({
          title: '删除成功',
          description: `${CONFIG_TYPES[key]?.label} 配置已删除`,
        });
        refreshStorageInfo();
      } else {
        toast({
          title: '删除失败',
          description: '删除配置过程中发生错误',
          variant: 'destructive',
        });
      }
    },
    [toast, refreshStorageInfo]
  );

  // 格式化文件大小
  const formatSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 如果还没有加载客户端数据，显示加载状态
  if (!isClient || !storageInfo) {
    return (
      <div className='space-y-6'>
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Database className='w-5 h-5' />
              配置存储概览
            </CardTitle>
            <CardDescription>正在加载配置信息...</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-3 gap-4 mb-4'>
              <div className='text-center'>
                <div className='text-2xl font-bold text-muted-foreground'>--</div>
                <div className='text-sm text-muted-foreground'>已保存配置</div>
              </div>
              <div className='text-center'>
                <div className='text-2xl font-bold text-muted-foreground'>--</div>
                <div className='text-sm text-muted-foreground'>总存储大小</div>
              </div>
              <div className='text-center'>
                <div className='text-2xl font-bold text-muted-foreground'>--</div>
                <div className='text-sm text-muted-foreground'>配置类型</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* 概览信息 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Database className='w-5 h-5' />
            配置存储概览
          </CardTitle>
          <CardDescription>管理应用的所有持久化配置，包括样式、主题、布局等设置</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-3 gap-4 mb-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-primary'>{storageInfo.itemCount}</div>
              <div className='text-sm text-muted-foreground'>已保存配置</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-primary'>
                {formatSize(storageInfo.totalSize)}
              </div>
              <div className='text-sm text-muted-foreground'>总存储大小</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-primary'>
                {Object.keys(STORAGE_KEYS).length}
              </div>
              <div className='text-sm text-muted-foreground'>配置类型</div>
            </div>
          </div>

          <Separator className='my-4' />

          {/* 操作按钮 */}
          <div className='flex flex-wrap gap-2'>
            <Button onClick={refreshStorageInfo} variant='outline' size='sm'>
              <RefreshCw className='w-4 h-4 mr-2' />
              刷新
            </Button>
            <Button onClick={handleExportAll} variant='outline' size='sm'>
              <Download className='w-4 h-4 mr-2' />
              导出所有配置
            </Button>
            <Button variant='outline' size='sm' asChild>
              <label>
                <Upload className='w-4 h-4 mr-2' />
                导入配置
                <input
                  type='file'
                  accept='.json'
                  onChange={handleImportConfig}
                  className='hidden'
                />
              </label>
            </Button>
            <Button onClick={handleCreateBackup} variant='outline' size='sm'>
              <Save className='w-4 h-4 mr-2' />
              创建备份
            </Button>
            <Button onClick={handleClearAll} variant='destructive' size='sm' disabled={isLoading}>
              <Trash2 className='w-4 h-4 mr-2' />
              清除所有配置
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 配置详情 */}
      <Card>
        <CardHeader>
          <CardTitle>配置详情</CardTitle>
          <CardDescription>查看和管理各个配置项的详细信息</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            {storageInfo.items.map(item => {
              const configType = CONFIG_TYPES[item.key as keyof typeof CONFIG_TYPES];
              const Icon = configType?.icon || FileText;

              return (
                <div
                  key={item.key}
                  className='flex items-center justify-between p-3 border rounded-lg'
                >
                  <div className='flex items-center gap-3'>
                    <Icon className='w-5 h-5 text-muted-foreground' />
                    <div>
                      <div className='font-medium'>{configType?.label || item.key}</div>
                      <div className='text-sm text-muted-foreground'>
                        {item.exists ? formatSize(item.size) : '未保存'}
                      </div>
                    </div>
                  </div>
                  <div className='flex items-center gap-2'>
                    {item.exists ? (
                      <>
                        <Badge variant='secondary' className='text-xs'>
                          已保存
                        </Badge>
                        <Button
                          onClick={() => handleDeleteConfig(item.key as keyof typeof STORAGE_KEYS)}
                          variant='ghost'
                          size='sm'
                        >
                          <Trash2 className='w-4 h-4' />
                        </Button>
                      </>
                    ) : (
                      <Badge variant='outline' className='text-xs'>
                        未保存
                      </Badge>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
