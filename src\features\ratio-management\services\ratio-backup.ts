/**
 * 备选配比服务
 * 处理备选配比的存储、检索和管理
 */

import type {
  BackupRatio,
  CreateBackupRatioRequest,
  BackupRatioListItem,
  ApplyBackupRatioResult,
  BackupRatioFilter,
  BackupRatioSortBy,
  BackupRatioSortOrder,
  BackupRatioStats,
} from '@/core/types/ratio-backup';

class RatioBackupService {
  private readonly STORAGE_KEY = 'ratio_backup_list';

  /**
   * 创建备选配比
   */
  async createBackupRatio(request: CreateBackupRatioRequest): Promise<BackupRatio> {
    const now = new Date().toISOString();
    const id = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const backupRatio: BackupRatio = {
      id,
      name: request.name,
      description: request.description,
      taskId: request.taskId,
      materials: request.materials,
      calculationParams: request.calculationParams,
      calculationResults: request.calculationResults,
      createdAt: now,
      updatedAt: now,
      tags: request.tags || [],

      // 快速访问属性
      targetStrength: `C${request.calculationParams.targetStrength}`,
      slump: `${request.calculationParams.slump}±20`,
      waterCementRatio: request.calculationParams.waterCementRatio,
      totalMaterials: request.materials.length,
      qualityScore: request.calculationResults?.qualityScore,
    };

    // 保存到本地存储
    const existingRatios = this.getStoredRatios();
    existingRatios.push(backupRatio);
    this.saveRatios(existingRatios);

    return backupRatio;
  }

  /**
   * 获取备选配比列表
   */
  async getBackupRatios(
    filter?: BackupRatioFilter,
    sortBy: BackupRatioSortBy = 'createdAt',
    sortOrder: BackupRatioSortOrder = 'desc'
  ): Promise<BackupRatioListItem[]> {
    let ratios = this.getStoredRatios();

    // 应用过滤条件
    if (filter) {
      ratios = this.applyFilter(ratios, filter);
    }

    // 排序
    ratios = this.sortRatios(ratios, sortBy, sortOrder);

    // 转换为列表项格式
    return ratios.map(ratio => ({
      id: ratio.id,
      name: ratio.name,
      description: ratio.description,
      targetStrength: ratio.targetStrength,
      slump: ratio.slump,
      waterCementRatio: ratio.waterCementRatio,
      totalMaterials: ratio.totalMaterials || 0,
      qualityScore: ratio.qualityScore,
      createdAt: ratio.createdAt,
      tags: ratio.tags,
    }));
  }

  /**
   * 获取单个备选配比详情
   */
  async getBackupRatio(id: string): Promise<BackupRatio | null> {
    const ratios = this.getStoredRatios();
    return ratios.find(ratio => ratio.id === id) || null;
  }

  /**
   * 应用备选配比
   */
  async applyBackupRatio(id: string): Promise<ApplyBackupRatioResult> {
    try {
      const ratio = await this.getBackupRatio(id);

      if (!ratio) {
        return {
          success: false,
          message: '未找到指定的备选配比',
          errors: ['备选配比不存在或已被删除'],
        };
      }

      // 验证配比数据完整性
      const validationResult = this.validateRatioData(ratio);
      if (!validationResult.isValid) {
        return {
          success: false,
          message: '备选配比数据验证失败',
          errors: validationResult.errors,
        };
      }

      return {
        success: true,
        message: `成功应用备选配比：${ratio.name}`,
        appliedRatio: ratio,
        warnings: validationResult.warnings,
      };
    } catch (error) {
      return {
        success: false,
        message: '应用备选配比时发生错误',
        errors: [error instanceof Error ? error.message : '未知错误'],
      };
    }
  }

  /**
   * 删除备选配比
   */
  async deleteBackupRatio(id: string): Promise<boolean> {
    const ratios = this.getStoredRatios();
    const filteredRatios = ratios.filter(ratio => ratio.id !== id);

    if (filteredRatios.length === ratios.length) {
      return false; // 没有找到要删除的配比
    }

    this.saveRatios(filteredRatios);
    return true;
  }

  /**
   * 更新备选配比
   */
  async updateBackupRatio(id: string, updates: Partial<BackupRatio>): Promise<BackupRatio | null> {
    const ratios = this.getStoredRatios();
    const index = ratios.findIndex(ratio => ratio.id === id);

    if (index === -1) {
      return null;
    }

    const updatedRatio = {
      ...ratios[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    ratios[index] = updatedRatio as any;
    this.saveRatios(ratios);

    return updatedRatio as any;
  }

  /**
   * 获取备选配比统计信息
   */
  async getBackupRatioStats(taskId?: string): Promise<BackupRatioStats> {
    let ratios = this.getStoredRatios();

    if (taskId) {
      ratios = ratios.filter(ratio => ratio.taskId === taskId);
    }

    const byStrength: Record<string, number> = {};
    const byTags: Record<string, number> = {};
    let totalQualityScore = 0;
    let qualityScoreCount = 0;

    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    let recentCount = 0;

    ratios.forEach(ratio => {
      // 按强度统计
      if (ratio.targetStrength) {
        byStrength[ratio.targetStrength] = (byStrength[ratio.targetStrength] || 0) + 1;
      }

      // 按标签统计
      ratio.tags?.forEach(tag => {
        byTags[tag] = (byTags[tag] || 0) + 1;
      });

      // 质量评分统计
      if (ratio.qualityScore) {
        totalQualityScore += ratio.qualityScore;
        qualityScoreCount++;
      }

      // 最近创建统计
      if (new Date(ratio.createdAt) > sevenDaysAgo) {
        recentCount++;
      }
    });

    return {
      total: ratios.length,
      byStrength,
      byTags,
      averageQualityScore: qualityScoreCount > 0 ? totalQualityScore / qualityScoreCount : 0,
      recentCount,
    };
  }

  // 私有方法
  private getStoredRatios(): BackupRatio[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('读取备选配比数据失败:', error);
      return [];
    }
  }

  private saveRatios(ratios: BackupRatio[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(ratios));
    } catch (error) {
      console.error('保存备选配比数据失败:', error);
      throw new Error('保存失败，请检查存储空间');
    }
  }

  private applyFilter(ratios: BackupRatio[], filter: BackupRatioFilter): BackupRatio[] {
    return ratios.filter(ratio => {
      // 任务ID过滤
      if (filter.taskId && ratio.taskId !== filter.taskId) {
        return false;
      }

      // 强度等级过滤
      if (filter.targetStrength && filter.targetStrength.length > 0) {
        if (!ratio.targetStrength || !filter.targetStrength.includes(ratio.targetStrength)) {
          return false;
        }
      }

      // 坍落度范围过滤
      if (filter.slumpRange) {
        const slump = ratio.calculationParams.slump;
        if (slump < filter.slumpRange.min || slump > filter.slumpRange.max) {
          return false;
        }
      }

      // 水胶比范围过滤
      if (filter.waterCementRatioRange && ratio.waterCementRatio) {
        if (
          ratio.waterCementRatio < filter.waterCementRatioRange.min ||
          ratio.waterCementRatio > filter.waterCementRatioRange.max
        ) {
          return false;
        }
      }

      // 标签过滤
      if (filter.tags && filter.tags.length > 0) {
        if (!ratio.tags || !filter.tags.some(tag => ratio.tags!.includes(tag))) {
          return false;
        }
      }

      // 搜索文本过滤
      if (filter.searchText) {
        const searchText = filter.searchText.toLowerCase();
        const searchableText = [
          ratio.name,
          ratio.description,
          ratio.targetStrength,
          ...(ratio.tags || []),
        ]
          .join(' ')
          .toLowerCase();

        if (!searchableText.includes(searchText)) {
          return false;
        }
      }

      return true;
    });
  }

  private sortRatios(
    ratios: BackupRatio[],
    sortBy: BackupRatioSortBy,
    sortOrder: BackupRatioSortOrder
  ): BackupRatio[] {
    return ratios.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'createdAt':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'targetStrength':
          const aStrength = parseInt(a.targetStrength?.replace('C', '') || '0');
          const bStrength = parseInt(b.targetStrength?.replace('C', '') || '0');
          comparison = aStrength - bStrength;
          break;
        case 'qualityScore':
          comparison = (a.qualityScore || 0) - (b.qualityScore || 0);
          break;
        case 'waterCementRatio':
          comparison = (a.waterCementRatio || 0) - (b.waterCementRatio || 0);
          break;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });
  }

  private validateRatioData(ratio: BackupRatio): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 基本数据验证
    if (!ratio.materials || ratio.materials.length === 0) {
      errors.push('配比材料列表为空');
    }

    if (!ratio.calculationParams) {
      errors.push('计算参数缺失');
    }

    // 材料数据验证
    ratio.materials.forEach((material, index) => {
      if (!material.name) {
        errors.push(`第${index + 1}个材料名称缺失`);
      }
      if (material.actualAmount <= 0) {
        warnings.push(`材料"${material.name}"的实际用量为0或负数`);
      }
    });

    // 计算参数验证
    if (ratio.calculationParams) {
      if (
        ratio.calculationParams.waterCementRatio <= 0 ||
        ratio.calculationParams.waterCementRatio > 1
      ) {
        warnings.push('水胶比数值异常');
      }
      if (ratio.calculationParams.slump <= 0 || ratio.calculationParams.slump > 300) {
        warnings.push('坍落度数值异常');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
}

// 导出单例实例
export const ratioBackupService = new RatioBackupService();
export default ratioBackupService;
