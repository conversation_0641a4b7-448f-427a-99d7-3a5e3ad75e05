/**
 * 固定列相关的TypeScript类型定义
 * 提供严格的类型安全和更好的开发体验
 */

/**
 * 固定列位置枚举
 */
export enum StickyColumnPosition {
  LEFT = 'left',
  RIGHT = 'right',
  NONE = 'none',
}

/**
 * z-index层级枚举
 * 统一管理所有层级，确保冲突最小化
 */
export enum ZIndexLevels {
  // 基础层级
  BASE = 0,
  TABLE_CELL = 1, // Non-sticky cells

  // 固定列层级
  STICKY_BODY = 10, // Standard sticky body cell (td)
  // STICKY_BODY_SHADOW_CASTER = 12, // Removed, shadow class will be on STICKY_BODY
  STICKY_HEADER = 20, // Standard sticky header cell (th)
  // STICKY_HEADER_SHADOW_CASTER = 22, // Removed, shadow class will be on STICKY_HEADER

  TABLE_THEAD_STICKY = 30, // For the <thead> element itself to be above tbody
  TABLE_RESIZER_HANDLE = 35, // For column resize handles

  // 弹出层级 (ensure these are higher than table elements)
  DROPDOWN = 1000,
  MODAL_BACKDROP = 1040,
  MODAL = 1050,
  TOOLTIP = 1070,
  TOAST = 1080,
  TABLE_HEADER = 2,
}

/**
 * 阴影配置接口
 */
export interface ShadowConfig {
  /** 水平偏移量 (px) */
  offsetX: number;
  /** 垂直偏移量 (px) */
  offsetY: number;
  /** 模糊半径 (px) */
  blur: number;
  /** 扩散半径 (px) */
  spread: number;
  /** 阴影颜色 */
  color: string;
}

/**
 * 固定列配置接口
 */
export interface StickyColumnConfig {
  /** 列ID */
  columnId: string;
  /** 固定位置 */
  position: StickyColumnPosition;
  /** 排序顺序 */
  order: number;
  /** 是否显示阴影 - DEPRECATED: Shadows are now applied dynamically via JS/CSS classes */
  showShadow?: boolean;
  /** 自定义阴影配置 */
  shadowConfig?: Partial<ShadowConfig>;
  /** 最小宽度 */
  minWidth?: number;
  /** 最大宽度 */
  maxWidth?: number;
  /** 是否可调整大小 */
  resizable?: boolean;
  /** 优先级 for responsive scenarios */
  priority?: 'high' | 'medium' | 'low';
}

/**
 * 固定列样式配置接口
 */
export interface StickyColumnStyles {
  /** 位置样式 */
  position: 'sticky';
  /** 层级 */
  zIndex?: number;
  /** 左偏移 */
  left?: string;
  /** 右偏移 */
  right?: string;
  /** 背景色 */
  backgroundColor?: string;
  /** 宽度 */
  width?: number;
  /** 最小宽度 */
  minWidth?: number;
  /** 最大宽度 */
  maxWidth?: number;
  /** 盒阴影 - DEPRECATED: Shadows applied via CSS classes now */
  boxShadow_DEPRECATED?: string;
}

/**
 * 固定列元数据接口
 */
export interface StickyColumnMeta {
  /** 自定义配置 */
  customDef?: {
    /** 固定位置 */
    fixed?: StickyColumnPosition;
    /** 排序顺序 */
    order?: number;
    /** 密度样式配置 */
    densityStyles?: {
      headerPaddingX?: string;
      headerPaddingY?: string;
      headerHeight?: string;
      headerFontSize?: string;
    };
    /** 获取列背景属性的函数 */
    getColumnBackgroundProps?: (
      columnId: string,
      isHeader: boolean,
      isFixed: boolean
    ) => {
      style: React.CSSProperties;
      className: string;
    };
    isHeader?: boolean; // Added to distinguish if it's a header cell
  };
}

/**
 * 固定列上下文接口
 */
export interface StickyColumnContext {
  /** 左固定列配置 */
  leftFixedColumns: StickyColumnConfig[];
  /** 右固定列配置 */
  rightFixedColumns: StickyColumnConfig[];
  /** 获取列的固定样式 */
  getStickyStyles: (columnId: string, isHeader: boolean) => StickyColumnStyles;
  /** 判断是否为最后一个左固定列 */
  isLastLeftFixed: (columnId: string) => boolean;
  /** 判断是否为第一个右固定列 */
  isFirstRightFixed: (columnId: string) => boolean;
  /** 更新固定列配置 */
  updateStickyConfig: (columnId: string, config: Partial<StickyColumnConfig>) => void;
  /** Screen width for responsive adjustments */
  screenWidth: number;
  /** Flag for mobile device */
  leftColumns: StickyColumnConfig[];
  rightColumns: StickyColumnConfig[];
  shadowConfigs: Record<StickyColumnPosition, ShadowConfig>;
  isMobile: boolean;
  /** Current theme */
  theme: 'light' | 'dark'; // Or your theme type
  /** Function to get actual column width */
  getColumnWidth: (columnId: string) => number;
  /** Utility to calculate left offset */
  calculateLeftOffset: (columnId: string) => number;
  /** Utility to calculate right offset */
  calculateRightOffset: (columnId: string) => number;
  /** Utility to generate class names */
  generateClassNames: (
    columnId: string,
    position: StickyColumnPosition,
    showShadow?: boolean
  ) => string[];
}

/**
 * 固定列工具函数类型
 */
export type StickyColumnUtilsType = {
  // Renamed to avoid conflict if imported directly
  /** 计算左偏移量 */
  calculateLeftOffset: (
    columnId: string,
    columns: StickyColumnConfig[],
    getColumnSize: (id: string) => number
  ) => number;
  /** 计算右偏移量 */
  calculateRightOffset: (
    columnId: string,
    columns: StickyColumnConfig[],
    getColumnSize: (id: string) => number
  ) => number;
  /** 生成阴影CSS */
  generateShadowCSS: (config: ShadowConfig) => string;
  /** 验证固定列配置 */
  validateStickyConfig: (config: StickyColumnConfig) => boolean;
  /** 创建默认阴影配置 */
  createDefaultShadowConfig: (position: StickyColumnPosition) => ShadowConfig;
  /** 合并用户自定义阴影配置 */
  mergeShadowConfig: (
    defaultConfig: ShadowConfig,
    userConfig?: Partial<ShadowConfig>
  ) => ShadowConfig;
  /** 获取响应式阴影配置 */
  getResponsiveShadowConfig: (position: StickyColumnPosition, isMobile?: boolean) => ShadowConfig;
  /** 生成CSS类名 */
  generateClassNames: (
    columnId: string,
    position: StickyColumnPosition,
    showShadow?: boolean
  ) => string[];
  /** 生成固定列样式 */
  generateStickyStyles: (
    config: StickyColumnConfig,
    isHeader: boolean,
    offset: number,
    columnSize: number
  ) => StickyColumnStyles;
  /** 是否最后一个左固定列 */
  isLastLeftFixed: (columnId: string, leftColumns: StickyColumnConfig[]) => boolean;
  /** 是否第一个右固定列 */
  isFirstRightFixed: (columnId: string, rightColumns: StickyColumnConfig[]) => boolean;
};

/**
 * 固定列事件类型
 */
export interface StickyColumnEvents {
  /** 列宽度改变事件 */
  onColumnResize?: (columnId: string, newWidth: number) => void;
  /** 列位置改变事件 */
  onColumnMove?: (columnId: string, newPosition: StickyColumnPosition) => void;
  /** 列配置改变事件 */
  onConfigChange?: (columnId: string, config: StickyColumnConfig) => void;
}

/**
 * 导出所有类型的联合类型，便于批量导入
 */
export type AllStickyColumnTypes = {
  // Renamed to avoid conflict
  StickyColumnPosition: typeof StickyColumnPosition;
  ZIndexLevels: typeof ZIndexLevels;
  ShadowConfig: ShadowConfig;
  StickyColumnConfig: StickyColumnConfig;
  StickyColumnStyles: StickyColumnStyles;
  StickyColumnMeta: StickyColumnMeta;
  StickyColumnContext: StickyColumnContext;
  StickyColumnUtilsType: StickyColumnUtilsType;
  StickyColumnEvents: StickyColumnEvents;
};
