// src/ai/flows/announce-vehicle-arrivals.ts
'use server';

/**
 * @fileOverview Announces vehicle arrival information using a Genkit flow and text-to-speech.
 *
 * - announceVehicleArrival - A function to announce vehicle arrival details.
 * - AnnounceVehicleArrivalInput - The input type for the announceVehicleArrival function.
 * - AnnounceVehicleArrivalOutput - The return type for the announceVehicleArrival function.
 */
import { z } from 'zod';

// src/ai/flows/announce-vehicle-arrivals.ts

const AnnounceVehicleArrivalInputSchema = z.object({
  mixingPlant: z.string().describe('The name of the mixing plant.'),
  vehicleNumber: z.string().describe('The vehicle number.'),
  repeatCount: z.number().describe('The number of times to repeat the announcement.'),
  speechRate: z.number().optional().describe('The speech rate for the announcement (optional).'),
  language: z.string().optional().describe('The language for the announcement (optional).'),
  accent: z.string().optional().describe('The accent for the announcement (optional).'),
});

export type AnnounceVehicleArrivalInput = z.infer<typeof AnnounceVehicleArrivalInputSchema>;

const AnnounceVehicleArrivalOutputSchema = z.object({
  announcement: z.string().describe('The generated announcement message.'),
});

export type AnnounceVehicleArrivalOutput = z.infer<typeof AnnounceVehicleArrivalOutputSchema>;

export async function announceVehicleArrival(
  input: AnnounceVehicleArrivalInput
): Promise<AnnounceVehicleArrivalOutput> {
  try {
    // 动态导入 AI 实例
    const { getAI } = await import('@/features/ai/genkit');
    const ai = await getAI();

    // 创建提示模板
    const announceVehicleArrivalPrompt = ai.definePrompt({
      name: 'announceVehicleArrivalPrompt',
      input: { schema: AnnounceVehicleArrivalInputSchema },
      output: { schema: AnnounceVehicleArrivalOutputSchema },
      prompt: `Announce the arrival of vehicle {{vehicleNumber}} at mixing plant {{mixingPlant}}. Repeat the announcement {{repeatCount}} times.

        Consider using a {{language}} language and a {{accent}} accent. Adjust the speech rate to {{speechRate}}.`,
    });

    // 创建流程
    const announceVehicleArrivalFlow = ai.defineFlow(
      {
        name: 'announceVehicleArrivalFlow',
        inputSchema: AnnounceVehicleArrivalInputSchema,
        outputSchema: AnnounceVehicleArrivalOutputSchema,
      },
      async (flowInput: AnnounceVehicleArrivalInput) => {
        let announcementText = '';
        for (let i = 0; i < flowInput.repeatCount; i++) {
          const { output } = await announceVehicleArrivalPrompt(flowInput);
          announcementText += output!.announcement + ' ';
        }

        // Here, we would ideally integrate with a text-to-speech service to actually
        // make the announcement audible.  This placeholder shows how the announcement
        // text would be generated.

        return { announcement: announcementText };
      }
    );

    return announceVehicleArrivalFlow(input);
  } catch (error) {
    console.error('车辆到达通知失败:', error);

    // 返回备用通知
    const fallbackAnnouncement = `车辆 ${input.vehicleNumber} 已到达 ${input.mixingPlant}。`.repeat(
      input.repeatCount
    );
    return { announcement: fallbackAnnouncement };
  }
}
