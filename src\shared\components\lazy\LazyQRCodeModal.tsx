/**
 * LazyQRCodeModal - QR码模态框的动态导入包装器
 * 只在需要时加载 qrcode 库，减少初始包体积
 */

'use client';

import React, { Suspense, lazy } from 'react';
import { LoadingSpinner } from '@/shared/components/LoadingSpinner';
import type { Task } from '@/core/types';

// 动态导入 QR码模态框组件
const QRCodeModal = lazy(() =>
  import('@/models/qr-code-modal').then(module => ({
    default: module.QRCodeModal,
  }))
);

interface LazyQRCodeModalProps {
  isOpen: boolean;
  onOpenChangeAction: (open: boolean) => void;
  task: Task | null;
}

/**
 * QR码模态框的懒加载包装器
 * 提供加载状态和错误处理
 */
export function LazyQRCodeModal(props: LazyQRCodeModalProps) {
  return (
    <Suspense
      fallback={
        <div className='flex items-center justify-center p-8'>
          <LoadingSpinner />
          <span className='ml-2 text-sm text-muted-foreground'>加载二维码组件...</span>
        </div>
      }
    >
      <QRCodeModal {...props} />
    </Suspense>
  );
}

export default LazyQRCodeModal;
