/**
 * React DnD 适配器实现 V2
 */
'use client';

import React, { useRef, useCallback } from 'react';
import {
  useDrag as useReactDndDragHook,
  useDrop as useReactDndDropHook,
  DndProvider,
} from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import {
  IDragDropAdapter,
  IDragDropProviderFactory,
  UnifiedDragConfig,
  UnifiedDropConfig,
  UnifiedDragResult,
  UnifiedDropResult,
  UnifiedDragItem,
} from './DragDropAdapter';

export class ReactDndAdapter implements IDragDropAdapter {
  readonly library = 'react-dnd' as const;

  useDrag(config: UnifiedDragConfig): UnifiedDragResult {
    const ref = useRef<HTMLElement>(null);

    const [{ isDragging }, drag] = useReactDndDragHook({
      type: config.type,
      item: {
        id: config.id,
        type: config.type,
        data: config.data || {},
      },
      collect: monitor => ({
        isDragging: monitor.isDragging(),
      }),
      canDrag: !config.disabled,
    });

    // React DnD 使用回调 ref
    const dragRef = useCallback(
      (node: HTMLElement | null) => {
        if (ref.current !== node) {
          (ref as React.MutableRefObject<HTMLElement | null>).current = node;
        }
        drag(node);
      },
      [drag]
    );

    return {
      ref, // 返回实际的 ref
      dragRef, // 提供回调 ref
      isDragging,
      attributes: {}, // React DnD 不需要额外属性
      listeners: {}, // React DnD 不需要额外监听器
    };
  }

  useDrop(config: UnifiedDropConfig): UnifiedDropResult {
    const ref = useRef<HTMLElement>(null);

    const [{ isOver, canDrop }, drop] = useReactDndDropHook({
      accept: Array.isArray(config.accept) ? config.accept : [config.accept],
      drop: (item: any) => {
        if (config.onDrop) {
          const unifiedItem: UnifiedDragItem = {
            id: item.id,
            type: item.type,
            data: item.data || item,
          };
          config.onDrop(unifiedItem);
        }
      },
      canDrop: (item: any) => {
        if (config.canDrop) {
          const unifiedItem: UnifiedDragItem = {
            id: item.id,
            type: item.type,
            data: item.data || item,
          };
          return config.canDrop(unifiedItem);
        }
        return true;
      },
      collect: monitor => ({
        isOver: monitor.isOver(),
        canDrop: monitor.canDrop(),
      }),
    });

    // React DnD 使用回调 ref
    const dropRef = useCallback(
      (node: HTMLElement | null) => {
        if (ref.current !== node) {
          (ref as React.MutableRefObject<HTMLElement | null>).current = node;
        }
        drop(node);
      },
      [drop]
    );

    return {
      ref, // 返回实际的 ref
      dropRef, // 提供回调 ref
      isOver,
      canDrop,
    };
  }

  convertItemType(type: string): string {
    // React DnD 类型映射
    const typeMapping: Record<string, string> = {
      'vehicle-card': 'vehicleCardDispatch',
      'task-card': 'taskCard',
      'production-line': 'productionLine',
    };

    return typeMapping[type] || type;
  }

  convertDragData(data: Record<string, any>): any {
    // React DnD 数据格式转换
    return {
      ...data,
      // React DnD 特定的数据结构调整
    };
  }
}

/**
 * React DnD Provider 工厂
 */
export class ReactDndProviderFactory implements IDragDropProviderFactory {
  createProvider(): React.ComponentType<{ children: React.ReactNode }> {
    return ({ children }: { children: React.ReactNode }) => (
      <DndProvider backend={HTML5Backend}>{children}</DndProvider>
    );
  }
}

/**
 * React DnD 专用 Hook 包装器
 */
export function useReactDndDrag(config: UnifiedDragConfig) {
  const adapter = new ReactDndAdapter();
  return adapter.useDrag(config);
}

export function useReactDndDrop(config: UnifiedDropConfig) {
  const adapter = new ReactDndAdapter();
  return adapter.useDrop(config);
}

/**
 * 兼容性工具函数
 */
export function wrapReactDndComponent<T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  dragConfig?: UnifiedDragConfig,
  dropConfig?: UnifiedDropConfig
) {
  const WrappedComponent = React.forwardRef<HTMLElement, T>((props, ref) => {
    const dragResult = dragConfig ? useReactDndDrag(dragConfig) : null;
    const dropResult = dropConfig ? useReactDndDrop(dropConfig) : null;

    const combinedRef = React.useCallback(
      (node: HTMLElement | null) => {
        // 组合多个 ref
        if (dragResult?.dragRef) {
          dragResult.dragRef(node);
        }
        if (dropResult?.dropRef) {
          dropResult.dropRef(node);
        }
        if (typeof ref === 'function') {
          ref(node);
        } else if (ref) {
          ref.current = node;
        }
      },
      [dragResult?.dragRef, dropResult?.dropRef, ref]
    );

    const enhancedProps = {
      ...props,
      ref: combinedRef,
      isDragging: dragResult?.isDragging,
      isOver: dropResult?.isOver,
      canDrop: dropResult?.canDrop,
    } as T;

    return React.createElement(Component, enhancedProps);
  });

  WrappedComponent.displayName = `withReactDnd(${Component.displayName || Component.name})`;
  return WrappedComponent;
}
