import { getApiConfig } from '@/infrastructure/api/client/api';
import { serviceLogger } from '@/core/lib/logger';
import type { DeliveryOrder, Plant, Task, Vehicle } from '@/core/types';

import { DataAdapters } from '../../../shared/services/dataAdapters';
import { httpClient } from '../../../infrastructure/api/client/httpClient';

/**
 * 获取任务列表数据
 *
 * 从API获取任务数据，支持mock和真实API两种模式。
 * 会自动处理数据适配和错误处理，确保返回标准的Task[]格式。
 *
 * @async
 * @function getTasks
 * @since 1.0.0
 *
 * @returns {Promise<Task[]>} 任务列表数据
 * @throws {Error} 当API请求失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const tasks = await getTasks();
 *   console.log('获取到任务:', tasks.length);
 * } catch (error) {
 *   console.error('获取任务失败:', error);
 * }
 * ```
 *
 * @note 根据API配置自动选择mock或真实API
 * @see {@link DataAdapters.adaptTasks} 数据适配器
 * @see {@link getApiConfig} API配置获取
 */
export async function getTasks(): Promise<Task[]> {
  try {
    const config = getApiConfig();
    const response = await httpClient.get<any[]>(config.endpoints.tasks);

    // 如果是mock数据，直接返回（httpClient已处理）
    if (config.baseUrl === '') {
      return response as Task[];
    }

    // 真实API数据需要适配
    return DataAdapters.adaptTasks(response);
  } catch (error) {
    serviceLogger.error('Failed to fetch tasks', error as Error, {
      operation: 'getTasks',
      config: getApiConfig(),
    });
    throw error;
  }
}

/**
 * 获取车辆列表数据
 *
 * 从API获取车辆数据，支持mock和真实API两种模式。
 * 会自动处理数据适配和错误处理，确保返回标准的Vehicle[]格式。
 *
 * @async
 * @function getVehicles
 * @since 1.0.0
 *
 * @returns {Promise<Vehicle[]>} 车辆列表数据
 * @throws {Error} 当API请求失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const vehicles = await getVehicles();
 *   console.log('获取到车辆:', vehicles.length);
 * } catch (error) {
 *   console.error('获取车辆失败:', error);
 * }
 * ```
 *
 * @see {@link DataAdapters.adaptVehicles} 数据适配器
 */
export async function getVehicles(): Promise<Vehicle[]> {
  try {
    const config = getApiConfig();
    const response = await httpClient.get<any[]>(config.endpoints.vehicles);

    // 如果是mock数据，直接返回
    if (config.baseUrl === '') {
      return response as Vehicle[];
    }

    // 真实API数据需要适配
    return DataAdapters.adaptVehicles(response);
  } catch (error) {
    serviceLogger.error('Failed to fetch vehicles', error as Error, {
      operation: 'getVehicles',
      config: getApiConfig(),
    });
    throw error;
  }
}

/**
 * 获取配送单列表数据
 *
 * 从API获取配送单数据，支持mock和真实API两种模式。
 * 会自动处理数据适配和错误处理，确保返回标准的DeliveryOrder[]格式。
 *
 * @async
 * @function getDeliveryOrders
 * @since 1.0.0
 *
 * @returns {Promise<DeliveryOrder[]>} 配送单列表数据
 * @throws {Error} 当API请求失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const orders = await getDeliveryOrders();
 *   console.log('获取到配送单:', orders.length);
 * } catch (error) {
 *   console.error('获取配送单失败:', error);
 * }
 * ```
 *
 * @see {@link DataAdapters.adaptDeliveryOrders} 数据适配器
 */
export async function getDeliveryOrders(): Promise<DeliveryOrder[]> {
  try {
    const config = getApiConfig();
    const response = await httpClient.get<any[]>(config.endpoints.deliveryOrders);

    // 如果是mock数据，直接返回
    if (config.baseUrl === '') {
      return response as DeliveryOrder[];
    }

    // 真实API数据需要适配
    return DataAdapters.adaptDeliveryOrders(response);
  } catch (error) {
    serviceLogger.error('Failed to fetch delivery orders', error as Error, {
      operation: 'getDeliveryOrders',
      config: getApiConfig(),
    });
    throw error;
  }
}

/**
 * 获取搅拌站静态数据
 *
 * 从API获取搅拌站基础配置数据，支持mock和真实API两种模式。
 * 会自动处理数据适配和错误处理，确保返回标准的Plant[]格式。
 *
 * @async
 * @function getStaticPlants
 * @since 1.0.0
 *
 * @returns {Promise<Plant[]>} 搅拌站列表数据
 * @throws {Error} 当API请求失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const plants = await getStaticPlants();
 *   console.log('获取到搅拌站:', plants.length);
 * } catch (error) {
 *   console.error('获取搅拌站失败:', error);
 * }
 * ```
 *
 * @note 此函数获取的是搅拌站的静态配置信息，不包含实时统计数据
 * @see {@link DataAdapters.adaptPlants} 数据适配器
 */
export async function getStaticPlants(): Promise<Plant[]> {
  try {
    const config = getApiConfig();
    const response = await httpClient.get<any[]>(config.endpoints.plants);

    // 如果是mock数据，直接返回
    if (config.baseUrl === '') {
      return response as Plant[];
    }

    // 真实API数据需要适配
    return DataAdapters.adaptPlants(response);
  } catch (error) {
    serviceLogger.error('Failed to fetch plants', error as Error, {
      operation: 'getStaticPlants',
      config: getApiConfig(),
    });
    throw error;
  }
}

/**
 * 车辆调度到任务服务
 *
 * 将指定车辆调度到特定任务和生产线，更新车辆状态为出车中，
 * 并设置相关的任务分配信息。这是车辆调度的核心业务逻辑。
 *
 * @async
 * @function dispatchVehicleToTaskService
 * @since 1.0.0
 *
 * @param {Vehicle[]} currentVehicles - 当前车辆列表
 * @param {string} vehicleId - 要调度的车辆ID
 * @param {string} taskId - 目标任务ID
 * @param {string} productionLineId - 目标生产线ID
 * @returns {Promise<Vehicle | null>} 更新后的车辆对象，如果车辆不存在则返回null
 *
 * @example
 * ```typescript
 * try {
 *   const vehicles = await getVehicles();
 *   const updatedVehicle = await dispatchVehicleToTaskService(
 *     vehicles,
 *     'vehicle-001',
 *     'task-001',
 *     'line-001'
 *   );
 *   if (updatedVehicle) {
 *     console.log('车辆调度成功:', updatedVehicle.vehicleNumber);
 *   }
 * } catch (error) {
 *   console.error('车辆调度失败:', error);
 * }
 * ```
 *
 * @note 此函数会更新车辆状态为'outbound'，生产状态为'queued'
 * @see {@link Vehicle} 车辆接口定义
 * @see {@link Task} 任务接口定义
 */
export async function dispatchVehicleToTaskService(
  currentVehicles: Vehicle[],
  vehicleId: string,
  taskId: string,
  productionLineId: string
): Promise<Vehicle | null> {
  try {
    const config = getApiConfig();

    // 如果是mock模式，使用原有逻辑
    if (config.baseUrl === '') {
      await new Promise(resolve => setTimeout(resolve, 50));
      const vehicleIndex = currentVehicles.findIndex(v => v.id === vehicleId);
      if (vehicleIndex > -1) {
        const vehicle = currentVehicles[vehicleIndex]!; // Non-null assertion since we know index is valid
        const updatedVehicle: Vehicle = {
          id: vehicle.id,
          vehicleNumber: vehicle.vehicleNumber,
          status: 'outbound' as Vehicle['status'],
          type: vehicle.type,
          // Copy optional properties if they exist
          ...(vehicle.isDragging !== undefined && { isDragging: vehicle.isDragging }),
          ...(vehicle.operationalStatus !== undefined && {
            operationalStatus: vehicle.operationalStatus,
          }),
          ...(vehicle.currentTripType !== undefined && {
            currentTripType: vehicle.currentTripType,
          }),
          ...(vehicle.productionStatus !== undefined && {
            productionStatus: 'queued' as Vehicle['productionStatus'],
          }),
          ...(vehicle.allowWeighRoomEdit !== undefined && {
            allowWeighRoomEdit: vehicle.allowWeighRoomEdit,
          }),
          ...(vehicle.deliveryOrderId !== undefined && {
            deliveryOrderId: vehicle.deliveryOrderId,
          }),
          ...(vehicle.lastTripWashedWithPumpWater !== undefined && {
            lastTripWashedWithPumpWater: vehicle.lastTripWashedWithPumpWater,
          }),
          // Set dispatch-specific properties
          assignedTaskId: taskId,
          assignedProductionLineId: productionLineId,
          // Override productionStatus to 'queued' regardless of previous value
          productionStatus: 'queued' as Vehicle['productionStatus'],
        };
        return updatedVehicle;
      }
      serviceLogger.error('Vehicle not found for dispatch', undefined, {
        operation: 'dispatchVehicleToTaskService',
        vehicleId,
        taskId,
        productionLineId,
      });
      return null;
    }

    // 真实API调用
    const response = await httpClient.patch<any>(`/vehicles/${vehicleId}/dispatch`, {
      taskId,
      productionLineId,
    });

    return DataAdapters.adaptVehicle(response);
  } catch (error) {
    serviceLogger.error('Failed to dispatch vehicle', error as Error, {
      operation: 'dispatchVehicleToTaskService',
      vehicleId,
      taskId,
      productionLineId,
    });
    throw error;
  }
}

export async function cancelVehicleDispatchService(
  currentVehicles: Vehicle[],
  vehicleId: string
): Promise<Vehicle | null> {
  await new Promise(resolve => setTimeout(resolve, 50));
  const vehicleIndex = currentVehicles.findIndex(v => v.id === vehicleId);
  if (vehicleIndex > -1) {
    const baseVehicle = currentVehicles[vehicleIndex]!; // Non-null assertion since we know index is valid

    // Create a new vehicle object with required properties preserved and optional properties cleared
    const updatedVehicle: Vehicle = {
      // Required properties
      id: baseVehicle.id,
      vehicleNumber: baseVehicle.vehicleNumber,
      type: baseVehicle.type,
      status: 'pending' as Vehicle['status'], // Reset to pending status

      // Optional properties - only include if they exist and we want to keep them
      ...(baseVehicle.isDragging !== undefined && { isDragging: baseVehicle.isDragging }),
      ...(baseVehicle.operationalStatus !== undefined && {
        operationalStatus: baseVehicle.operationalStatus,
      }),
      ...(baseVehicle.allowWeighRoomEdit !== undefined && {
        allowWeighRoomEdit: baseVehicle.allowWeighRoomEdit,
      }),
      ...(baseVehicle.plantId !== undefined && { plantId: baseVehicle.plantId }),
      ...(baseVehicle.lastTripWashedWithPumpWater !== undefined && {
        lastTripWashedWithPumpWater: baseVehicle.lastTripWashedWithPumpWater,
      }),

      // Explicitly clear dispatch-related optional properties by not including them
      // assignedTaskId, assignedProductionLineId, productionStatus, currentTripType, deliveryOrderId are omitted
    };

    return updatedVehicle;
  }
  serviceLogger.error('Vehicle not found for cancel dispatch', undefined, {
    operation: 'cancelVehicleDispatchService',
    vehicleId,
  });
  return null;
}

export async function reorderVehiclesInListService(
  currentVehicles: Vehicle[],
  draggedVehicleId: string,
  targetVehicleId: string,
  listStatus: Vehicle['status']
): Promise<Vehicle[]> {
  await new Promise(resolve => setTimeout(resolve, 50));
  const vehiclesCopy = [...currentVehicles];
  const listToReorder = vehiclesCopy.filter(v => v.status === listStatus);
  const otherVehicles = vehiclesCopy.filter(v => v.status !== listStatus);

  const draggedItem = listToReorder.find(v => v.id === draggedVehicleId);
  if (!draggedItem) return currentVehicles; // Should not happen

  const remainingItems = listToReorder.filter(v => v.id !== draggedVehicleId);
  const targetIndex = remainingItems.findIndex(v => v.id === targetVehicleId);

  if (targetIndex === -1) {
    // Drop on the list but not on a specific card (e.g. end of list)
    remainingItems.push(draggedItem);
  } else {
    remainingItems.splice(targetIndex, 0, draggedItem);
  }

  return [...otherVehicles, ...remainingItems];
}

export async function confirmCrossPlantDispatchService(
  currentVehicles: Vehicle[],
  vehicleId: string,
  targetPlantId: string,
  notes?: string
): Promise<Vehicle | null> {
  await new Promise(resolve => setTimeout(resolve, 50));
  const vehicleIndex = currentVehicles.findIndex(v => v.id === vehicleId);
  if (vehicleIndex > -1) {
    const vehicle = currentVehicles[vehicleIndex];
    if (!vehicle) {
      serviceLogger.error('Vehicle not found at index', undefined, {
        operation: 'confirmCrossPlantDispatchService',
        vehicleId,
        vehicleIndex,
      });
      return null;
    }

    // Create updated vehicle with proper typing
    const updatedVehicle: Vehicle = {
      id: vehicle.id,
      vehicleNumber: vehicle.vehicleNumber,
      status: 'pending' as Vehicle['status'], // Assuming it becomes pending in the new plant
      type: vehicle.type,
      // Copy optional properties if they exist
      ...(vehicle.isDragging !== undefined && { isDragging: vehicle.isDragging }),
      ...(vehicle.operationalStatus !== undefined && {
        operationalStatus: vehicle.operationalStatus,
      }),
      ...(vehicle.currentTripType !== undefined && { currentTripType: vehicle.currentTripType }),
      ...(vehicle.productionStatus !== undefined && { productionStatus: vehicle.productionStatus }),
      ...(vehicle.allowWeighRoomEdit !== undefined && {
        allowWeighRoomEdit: vehicle.allowWeighRoomEdit,
      }),
      ...(vehicle.deliveryOrderId !== undefined && { deliveryOrderId: vehicle.deliveryOrderId }),
      ...(vehicle.lastTripWashedWithPumpWater !== undefined && {
        lastTripWashedWithPumpWater: vehicle.lastTripWashedWithPumpWater,
      }),
      // Override specific properties for cross-plant dispatch
      plantId: targetPlantId,
      // assignedTaskId and assignedProductionLineId are omitted (reset for new plant)
      // Potentially log notes or handle other cross-plant logic here
    };
    serviceLogger.info('Cross-plant dispatch confirmed', {
      operation: 'confirmCrossPlantDispatchService',
      vehicleId,
      targetPlantId,
      notes: notes || 'N/A',
    });
    return updatedVehicle;
  }
  serviceLogger.error('Vehicle not found for cross-plant dispatch', undefined, {
    operation: 'confirmCrossPlantDispatchService',
    vehicleId,
    targetPlantId,
  });
  return null;
}
