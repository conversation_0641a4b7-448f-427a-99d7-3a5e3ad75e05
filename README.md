# 车辆调度管理系统

## 项目概述
这是一个基于Next.js的车辆调度管理系统，用于管理车辆调度、生产线任务和跨厂区调度操作。

## 技术栈
- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **UI库**: Radix UI + Tailwind CSS
- **状态管理**: Zustand
- **数据管理**: React Query
- **表单处理**: react-hook-form
- **图表**: recharts
- **AI集成**: Genkit AI
- **部署**: Firebase

## 核心功能
1. **车辆调度管理**
   - 车辆到任务的调度
   - 调度取消功能
   - 车辆列表排序

2. **跨厂区调度**
   - 跨厂区调度请求
   - 调度确认流程
   - 调度记录

3. **任务管理**
   - 任务进度跟踪
   - 自动任务更新模拟
   - 生产线分配

4. **用户界面**
   - 可调整面板布局
   - 响应式设计
   - 通知系统

## 项目结构
```
src/
├── ai/            # AI相关功能
├── app/           # Next.js App Router
├── assets/        # 静态资源
├── components/    # 可复用组件
├── constants/     # 常量定义
├── contexts/      # React上下文
├── data/          # 数据模型
├── hooks/         # 自定义Hook
├── lib/           # 工具函数
├── services/      # API服务
├── store/         # Zustand状态
├── styles/        # 全局样式
├── types/         # TypeScript类型
└── utils/         # 实用工具
```

## 开发指南
1. 安装依赖
```bash
npm install
```

2. 开发模式
```bash
npm run dev
```

3. 构建生产版本
```bash
npm run build
```

4. 启动生产服务器
```bash
npm run start
```

## 环境要求
- Node.js 18+
- npm 9+


## 关键接口：
- 获取调度任务
   http://**************:7033/Dispatch?page=1&pageSize=20&sort=1&where=%7B%7D&redisToken=1750668983&u_n=%E7%B3%BB%E7%BB%9F%E7%AE%A1%E7%90%86%E5%91%98&w_n=yulei-admin
- 获取车辆列表
   http://**************:7033/Dispatch/CarList_E?redisToken=1750668983
- 获取今日完成
   http://**************:7033/Dispatch/Fun/getTodaySum?redisToken=1750668983&u_n=%E7%B3%BB%E7%BB%9F%E7%AE%A1%E7%90%86%E5%91%98&w_n=yulei-admin
- 获取发货单详情
   http://**************:7033/Deliver/YZ25-05471B623162010?redisToken=1750669266
- 泵车出车统计
   http://**************:7033/Dispatch/pump/Statics/Today?redisToken=1750669380&u_n=%E7%B3%BB%E7%BB%9F%E7%AE%A1%E7%90%86%E5%91%98&w_n=yulei-admin
- 罐车出车统计
   http://**************:7033/Dispatch/car/Statics/Today?redisToken=1750669385&u_n=%E7%B3%BB%E7%BB%9F%E7%AE%A1%E7%90%86%E5%91%98&w_n=yulei-admin
- 司机统计
   http://**************:7033/Dispatch/driver/Statics/Today?redisToken=1750669649&u_n=%E7%B3%BB%E7%BB%9F%E7%AE%A1%E7%90%86%E5%91%98&w_n=yulei-admin
- 待补充
   车辆调度api
   车辆变更（暂停、停用、重新启用） api
   发货单列表 api
   搅拌站信息 api
   任务详情 api
   车辆详情 api
   任务修改 api
   发货单修改 api
   配比修改 api
   查看配比 api
   任务生产进度 api
   修改任务状态 api
   给任务安排泵车 api
   安排其他车辆出厂 api
   查询任务发车明细 api
   朗读车辆进站信息 api
   系统参数设定 api
   换班api
   查询交接班记录 api
   罐车出车统计 api
   调度工程统计 api
   泵车出车统计api
   发车提醒 设置api
   查询发车提醒消息 api
   发车提醒消息 设置为已读 api
   任务消息获取 api
   任务消息 设置为已读 api

   //调度页面UI配置 提交、下载功能api（重新实现）


