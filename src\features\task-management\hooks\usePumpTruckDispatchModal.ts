'use client';

import { useCallback, useState } from 'react';

import type { Task } from '@/core/types';
import type { PumpTruckDispatchData } from '@/models/pump-truck-dispatch-modal';

interface UsePumpTruckDispatchModalProps {
  onDispatchConfirm: (task: Task, dispatchData: PumpTruckDispatchData) => Promise<void>;
}

interface UsePumpTruckDispatchModalReturn {
  isOpen: boolean;
  task: Task | null;
  openModal: (task: Task) => void;
  closeModal: () => void;
  handleConfirm: (dispatchData: PumpTruckDispatchData) => Promise<void>;
}

/**
 * 泵车发车单模态框管理Hook
 */
export function usePumpTruckDispatchModal({
  onDispatchConfirm,
}: UsePumpTruckDispatchModalProps): UsePumpTruckDispatchModalReturn {
  const [isOpen, setIsOpen] = useState(false);
  const [task, setTask] = useState<Task | null>(null);

  const openModal = useCallback((task: Task) => {
    setTask(task);
    setIsOpen(true);
  }, []);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setTask(null);
  }, []);

  const handleConfirm = useCallback(
    async (dispatchData: PumpTruckDispatchData) => {
      if (!task) {
        console.error('Missing task data for pump truck dispatch confirmation');
        return;
      }

      try {
        await onDispatchConfirm(task, dispatchData);
        closeModal();
      } catch (error) {
        console.error('Failed to confirm pump truck dispatch:', error);
        // 不关闭模态框，让用户重试
      }
    },
    [task, onDispatchConfirm, closeModal]
  );

  return {
    isOpen,
    task,
    openModal,
    closeModal,
    handleConfirm,
  };
}
