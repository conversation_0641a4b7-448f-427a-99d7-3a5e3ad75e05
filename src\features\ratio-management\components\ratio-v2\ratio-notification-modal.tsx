/**
 * 配比通知单选择模态框
 */

import React, { useState, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { Button } from '@/shared/components/button';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/shared/components/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/card';
import { Badge } from '@/shared/components/badge';
import { Alert, AlertDescription } from '@/shared/components/alert';
import { Separator } from '@/shared/components/separator';
import { ScrollArea } from '@/shared/components/scroll-area';
import {
  FileText,
  Upload,
  History,
  CheckCircle,
  AlertTriangle,
  Info,
  Calendar,
  User,
  Building,
} from 'lucide-react';

import type {
  RatioNotification,
  NotificationSelectOption,
  NotificationApplyResult,
} from '@/core/types/ratio-notification';
import { RatioNotificationService } from '@/features/ratio-management/services/ratio-notification';
import { generateNotificationSelectOptions } from '@/infrastructure/api/mock/ratio-notification-mock';

interface RatioNotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (result: NotificationApplyResult) => void;
}

export function RatioNotificationModal({ isOpen, onClose, onApply }: RatioNotificationModalProps) {
  const [selectedNotification, setSelectedNotification] = useState<RatioNotification | null>(null);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [parseResult, setParseResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('history');

  // Mock数据
  const notificationOptions = generateNotificationSelectOptions();

  /**
   * 处理文件上传
   */
  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploadedFile(file);
    setIsLoading(true);

    try {
      const result = await RatioNotificationService.parseFile(file);
      setParseResult(result);

      if (result.success && result.notification) {
        setSelectedNotification(result.notification);
      }
    } catch (error) {
      console.error('文件解析失败:', error);
      setParseResult({
        success: false,
        errors: ['文件解析失败'],
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * 处理历史通知单选择
   */
  const handleHistorySelect = useCallback((option: NotificationSelectOption) => {
    if (option.notification) {
      setSelectedNotification(option.notification);
      setParseResult(null);
      setUploadedFile(null);
    }
  }, []);

  /**
   * 应用选中的通知单
   */
  const handleApply = useCallback(() => {
    if (!selectedNotification) return;

    setIsLoading(true);

    try {
      const result = RatioNotificationService.applyNotification(selectedNotification);
      onApply(result);

      if (result.success) {
        onClose();
      }
    } catch (error) {
      console.error('应用通知单失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedNotification, onApply, onClose]);

  /**
   * 重置状态
   */
  const handleClose = useCallback(() => {
    setSelectedNotification(null);
    setUploadedFile(null);
    setParseResult(null);
    setActiveTab('history');
    onClose();
  }, [onClose]);

  /**
   * 渲染通知单详情
   */
  const renderNotificationDetails = (notification: RatioNotification) => (
    <Card className='mt-4'>
      <CardHeader className='pb-3'>
        <CardTitle className='text-lg flex items-center gap-2'>
          <FileText className='h-5 w-5' />
          {notification.info.notificationNumber}
        </CardTitle>
        <CardDescription>
          {notification.info.projectName} - {notification.info.constructionSite}
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        {/* 基本信息 */}
        <div className='grid grid-cols-2 gap-4 text-sm'>
          <div className='flex items-center gap-2'>
            <Building className='h-4 w-4 text-muted-foreground' />
            <span className='font-medium'>强度等级:</span>
            <Badge variant='outline'>{notification.info.strengthGrade}</Badge>
          </div>
          <div className='flex items-center gap-2'>
            <Calendar className='h-4 w-4 text-muted-foreground' />
            <span className='font-medium'>发布日期:</span>
            <span>{notification.info.issueDate}</span>
          </div>
          <div className='flex items-center gap-2'>
            <User className='h-4 w-4 text-muted-foreground' />
            <span className='font-medium'>审批人:</span>
            <span>{notification.approval.approver}</span>
          </div>
          <div className='flex items-center gap-2'>
            <CheckCircle className='h-4 w-4 text-green-500' />
            <span className='font-medium'>状态:</span>
            <Badge
              variant={
                notification.approval.approvalStatus === 'approved' ? 'default' : 'secondary'
              }
            >
              {notification.approval.approvalStatus === 'approved' ? '已审批' : '待审批'}
            </Badge>
          </div>
        </div>

        <Separator />

        {/* 技术参数 */}
        <div>
          <h4 className='font-medium mb-2'>技术参数</h4>
          <div className='grid grid-cols-2 gap-2 text-sm'>
            <div>水胶比: {notification.params.waterCementRatio}</div>
            <div>砂率: {notification.params.sandRatio}%</div>
            <div>用水量: {notification.params.waterContent} kg/m³</div>
            <div>坍落度: {notification.params.slump} mm</div>
          </div>
        </div>

        <Separator />

        {/* 材料配比 */}
        <div>
          <h4 className='font-medium mb-2'>材料配比</h4>
          <div className='space-y-1 text-sm'>
            {notification.materials.map((material, index) => (
              <div key={index} className='flex justify-between'>
                <span>{material.materialName}</span>
                <span className='font-mono'>{material.dosage} kg/m³</span>
              </div>
            ))}
          </div>
        </div>

        {/* 备注 */}
        {notification.notes && (
          <>
            <Separator />
            <div>
              <h4 className='font-medium mb-2'>备注说明</h4>
              <p className='text-sm text-muted-foreground'>{notification.notes}</p>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='max-w-4xl max-h-[90vh] overflow-hidden'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            应用配比通知单
          </DialogTitle>
          <DialogDescription>
            选择或上传配比通知单，系统将自动解析并应用到当前配比中
          </DialogDescription>
        </DialogHeader>

        <div className='flex-1 overflow-hidden'>
          <Tabs value={activeTab} onValueChange={setActiveTab} className='h-full'>
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger value='history' className='flex items-center gap-2'>
                <History className='h-4 w-4' />
                历史通知单
              </TabsTrigger>
              <TabsTrigger value='upload' className='flex items-center gap-2'>
                <Upload className='h-4 w-4' />
                文件上传
              </TabsTrigger>
            </TabsList>

            <TabsContent value='history' className='mt-4 h-[calc(100%-3rem)] overflow-hidden'>
              <div className='grid grid-cols-2 gap-4 h-full'>
                {/* 左侧：通知单列表 */}
                <div className='space-y-2'>
                  <Label>选择历史通知单</Label>
                  <ScrollArea className='h-[400px] border rounded-md p-2'>
                    <div className='space-y-2'>
                      {notificationOptions.map(option => (
                        <Card
                          key={option.id}
                          className={`cursor-pointer transition-colors hover:bg-muted/50 ${
                            selectedNotification?.id === option.id ? 'ring-2 ring-primary' : ''
                          }`}
                          onClick={() => handleHistorySelect(option)}
                        >
                          <CardContent className='p-3'>
                            <div className='font-medium text-sm'>{option.title}</div>
                            <div className='text-xs text-muted-foreground mt-1'>
                              {option.description}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </ScrollArea>
                </div>

                {/* 右侧：通知单详情 */}
                <div className='overflow-auto'>
                  {selectedNotification ? (
                    renderNotificationDetails(selectedNotification)
                  ) : (
                    <div className='flex items-center justify-center h-full text-muted-foreground'>
                      <div className='text-center'>
                        <FileText className='h-12 w-12 mx-auto mb-2 opacity-50' />
                        <p>请选择一个通知单查看详情</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value='upload' className='mt-4 space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='file-upload'>上传通知单文件</Label>
                <Input
                  id='file-upload'
                  type='file'
                  accept='.json,.xml,.txt,.csv'
                  onChange={handleFileUpload}
                  disabled={isLoading}
                />
                <p className='text-xs text-muted-foreground'>支持 JSON、XML、TXT、CSV 格式文件</p>
              </div>

              {/* 解析结果 */}
              {parseResult && (
                <Alert variant={parseResult.success ? 'default' : 'destructive'}>
                  <div className='flex items-center gap-2'>
                    {parseResult.success ? (
                      <CheckCircle className='h-4 w-4' />
                    ) : (
                      <AlertTriangle className='h-4 w-4' />
                    )}
                    <AlertDescription>
                      {parseResult.success ? '文件解析成功' : '文件解析失败'}
                    </AlertDescription>
                  </div>
                  {parseResult.errors && (
                    <div className='mt-2 text-sm'>
                      {parseResult.errors.map((error: any, index: number) => (
                        <div key={index}>• {error}</div>
                      ))}
                    </div>
                  )}
                </Alert>
              )}

              {/* 上传文件的通知单详情 */}
              {selectedNotification && activeTab === 'upload' && (
                <div className='max-h-[400px] overflow-auto'>
                  {renderNotificationDetails(selectedNotification)}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>

        {/* 底部按钮 */}
        <div className='flex justify-between pt-4 border-t'>
          <Button variant='outline' onClick={handleClose}>
            取消
          </Button>
          <Button
            onClick={handleApply}
            disabled={!selectedNotification || isLoading}
            className='flex items-center gap-2'
          >
            {isLoading ? (
              <>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white' />
                应用中...
              </>
            ) : (
              <>
                <CheckCircle className='h-4 w-4' />
                应用通知单
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
