const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  testEnvironment: 'jsdom',
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/src/__tests__/utils/test-utils.helper.tsx',
    '<rootDir>/src/__tests__/factories/test-data-factory.factory.ts',
    '<rootDir>/src/__tests__/setup/global-mocks.setup.ts',
    '<rootDir>/src/__tests__/jest-dom.d.ts', // 排除类型声明文件
  ],
  moduleNameMapper: {
    // 特定路径映射（优先级高）
    '^@/components/ui/card-minimal$': '<rootDir>/src/shared/components/card-minimal',
    '^@/components/sections/task-list/task-list\\.config$': '<rootDir>/src/features/task-management/components/task-list.config',
    '^@/store/appStore$': '<rootDir>/src/infrastructure/storage/stores/appStore',
    '^@/store/uiStore$': '<rootDir>/src/infrastructure/storage/stores/uiStore',
    '^@/data/mock-data$': '<rootDir>/src/infrastructure/api/mock/mock-data',
    '^@/data/ratio-mock-data$': '<rootDir>/src/infrastructure/api/mock/ratio-mock-data',
    '^@/hooks/use-toast$': '<rootDir>/src/shared/hooks/use-toast',

    // 通用路径映射
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@/hooks/(.*)$': '<rootDir>/src/shared/hooks/$1',
    '^@/components/(.*)$': '<rootDir>/src/shared/components/$1',
    '^@/services/(.*)$': '<rootDir>/src/shared/services/$1',
    '^@/utils/(.*)$': '<rootDir>/src/shared/utils/$1',
    '^@/types/(.*)$': '<rootDir>/src/shared/types/$1',
    '^@/store/(.*)$': '<rootDir>/src/core/store/$1',
    '^lucide-react$': '<rootDir>/node_modules/lucide-react/dist/cjs/lucide-react.js',
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/**/index.{js,jsx,ts,tsx}',
    '!src/**/*.config.{js,jsx,ts,tsx}',
    '!src/**/*.mock.{js,jsx,ts,tsx}',
  ],

  // 覆盖率阈值 - 暂时禁用，待测试完善后重新启用
  // TODO: 逐步添加测试用例，然后重新启用覆盖率检查
  // coverageThreshold: {
  //   global: {
  //     branches: 70,
  //     functions: 70,
  //     lines: 70,
  //     statements: 70,
  //   },
  //   './src/components/': {
  //     branches: 80,
  //     functions: 80,
  //     lines: 80,
  //     statements: 80,
  //   },
  //   './src/services/': {
  //     branches: 75,
  //     functions: 75,
  //     lines: 75,
  //     statements: 75,
  //   },
  //   './src/hooks/': {
  //     branches: 75,
  //     functions: 75,
  //     lines: 75,
  //     statements: 75,
  //   },
  // },
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{test,spec}.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}',
    '!<rootDir>/src/**/*.d.ts', // 排除类型声明文件
  ],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }],
  },
  transformIgnorePatterns: [
    'node_modules/(?!(lucide-react|@radix-ui|react-dnd|dnd-core|@dnd-kit)/)',
  ],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig)
