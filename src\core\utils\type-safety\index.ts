/**
 * 类型安全工具统一导出
 * 提供完整的类型安全解决方案
 */

import { deepMerge } from './strict-type-utils';

// ==================== 运行时验证 ====================
export {
  // 基础验证器类
  BaseValidator,
  StringValidator,
  NumberValidator,
  BooleanValidator,
  ArrayValidator,
  ObjectValidator,
  UnionValidator,
  OptionalValidator,

  // 验证结果类型
  type ValidationResult,
  type ValidationError,
  type ValidationWarning,
  type ValidatorOptions,

  // 工厂函数
  v,

  // 验证函数
  validateData,
  isValidData,
} from './runtime-validator';

// ==================== API类型守卫 ====================
export {
  // API响应类型
  type ApiResponse,
  type PaginatedResponse,

  // 核心实体验证器
  taskValidator,
  vehicleValidator,
  plantValidator,

  // API响应验证器
  createApiResponseValidator,
  createPaginatedResponseValidator,
  taskListResponseValidator,
  taskResponseValidator,
  vehicleListResponseValidator,
  vehicleResponseValidator,
  plantListResponseValidator,
  plantResponseValidator,

  // 类型守卫函数
  isApiResponse,
  isPaginatedResponse,
  isTask,
  isTaskArray,
  isVehicle,
  isVehicleArray,
  isPlant,
  isPlantArray,

  // 安全API调用
  validateApiResponse,
  validatePaginatedResponse,
  safeApiCall,
  ApiValidationError,

  // 开发工具
  debugApiResponse,
} from './api-type-guards';

// ==================== 组件类型守卫 ====================
export {
  // 组件Props类型
  type BaseComponentProps,
  type ClickableProps,
  type FormFieldProps,
  type TaskCardProps,
  type VehicleCardProps,
  type DataTableProps,
  type InputProps,
  type SelectProps,

  // 验证器
  componentValidators,

  // 类型守卫
  componentTypeGuards,
  isValidComponentProps,
  validateComponentProps,

  // 装饰器
  withPropsValidation,

  // 开发工具
  debugComponentProps,
} from './component-type-guards';

// ==================== 严格类型工具 ====================
export {
  // 对象操作
  getObjectKeys,
  getObjectValues,
  getObjectEntries,
  hasProperty,
  getProperty,
  setProperty,
  deepMerge,

  // 数组操作
  filterDefined,
  mapArray,
  findInArray,
  groupBy,
  uniqueArray,

  // JSON操作
  parseJSON,
  stringifyJSON,

  // 异步操作
  safePromise,
  withTimeout,
  retryOperation,

  // 事件处理
  createTypedEventListener,
  createCustomEvent,

  // 存储操作
  typedLocalStorage,
  typedSessionStorage,

  // 类型检查
  isObject,
  isArray,
  isFunction,
  isString,
  isNumber,
  isBoolean,
  isNullish,
  isNotNullish,
  isEmptyString,
  isNonEmptyString,

  // 断言函数
  assert,
  assertType,
} from './strict-type-utils';

// ==================== 类型安全Hooks ====================
export {
  // 状态Hooks
  useTypedState,
  useTypedLocalStorage,
  useTypedSessionStorage,

  // 异步Hooks
  useTypedAsync,
  type AsyncState,

  // 表单Hooks
  useTypedForm,
  type FormField,
  type FormState,
  type FormValidators,

  // 引用Hooks
  useTypedRef,
  useTypedCallbackRef,

  // 记忆化Hooks
  useTypedMemo,
  useTypedCallback,
} from './type-safe-hooks';

// ==================== 类型安全配置 ====================

export interface TypeSafetyConfig {
  // 运行时验证配置
  validation: {
    enabled: boolean;
    strictMode: boolean;
    logErrors: boolean;
    throwOnError: boolean;
  };

  // API验证配置
  api: {
    validateResponses: boolean;
    validateRequests: boolean;
    debugMode: boolean;
  };

  // 组件验证配置
  components: {
    validateProps: boolean;
    debugMode: boolean;
    warnOnInvalidProps: boolean;
  };

  // 开发工具配置
  devTools: {
    enableDebugLogs: boolean;
    enablePerformanceTracking: boolean;
    enableTypeChecking: boolean;
  };
}

export const defaultTypeSafetyConfig: TypeSafetyConfig = {
  validation: {
    enabled: process.env.NODE_ENV === 'development',
    strictMode: true,
    logErrors: true,
    throwOnError: false,
  },
  api: {
    validateResponses: true,
    validateRequests: true,
    debugMode: process.env.NODE_ENV === 'development',
  },
  components: {
    validateProps: process.env.NODE_ENV === 'development',
    debugMode: process.env.NODE_ENV === 'development',
    warnOnInvalidProps: true,
  },
  devTools: {
    enableDebugLogs: process.env.NODE_ENV === 'development',
    enablePerformanceTracking: process.env.NODE_ENV === 'development',
    enableTypeChecking: true,
  },
};

// ==================== 类型安全管理器 ====================

class TypeSafetyManager {
  private config: TypeSafetyConfig;

  constructor(config: TypeSafetyConfig = defaultTypeSafetyConfig) {
    this.config = config;
  }

  updateConfig(updates: Partial<TypeSafetyConfig>): void {
    this.config = deepMerge(this.config, updates) as TypeSafetyConfig;
  }

  getConfig(): TypeSafetyConfig {
    return { ...this.config };
  }

  isValidationEnabled(): boolean {
    return this.config.validation.enabled;
  }

  isApiValidationEnabled(): boolean {
    return this.config.api.validateResponses || this.config.api.validateRequests;
  }

  isComponentValidationEnabled(): boolean {
    return this.config.components.validateProps;
  }

  isDebugMode(): boolean {
    return this.config.devTools.enableDebugLogs;
  }

  logTypeError(error: string, context?: any): void {
    if (this.config.validation.logErrors && this.isDebugMode()) {
      console.error('🔍 类型安全错误:', error, context);
    }
  }

  logTypeWarning(warning: string, context?: any): void {
    if (this.config.components.warnOnInvalidProps && this.isDebugMode()) {
      console.warn('⚠️ 类型安全警告:', warning, context);
    }
  }
}

// 全局类型安全管理器实例
export const typeSafetyManager = new TypeSafetyManager();

// ==================== 便捷函数 ====================

/**
 * 启用严格类型检查模式
 */
export function enableStrictTypeChecking(): void {
  typeSafetyManager.updateConfig({
    validation: {
      enabled: true,
      strictMode: true,
      logErrors: true,
      throwOnError: true,
    },
    api: {
      validateResponses: true,
      validateRequests: true,
      debugMode: true,
    },
    components: {
      validateProps: true,
      debugMode: true,
      warnOnInvalidProps: true,
    },
  });
}

/**
 * 禁用类型检查（生产环境）
 */
export function disableTypeChecking(): void {
  typeSafetyManager.updateConfig({
    validation: {
      enabled: false,
      strictMode: false,
      logErrors: false,
      throwOnError: false,
    },
    api: {
      validateResponses: false,
      validateRequests: false,
      debugMode: false,
    },
    components: {
      validateProps: false,
      debugMode: false,
      warnOnInvalidProps: false,
    },
  });
}

/**
 * 获取类型安全统计信息
 */
export function getTypeSafetyStats(): {
  validationEnabled: boolean;
  apiValidationEnabled: boolean;
  componentValidationEnabled: boolean;
  debugMode: boolean;
} {
  return {
    validationEnabled: typeSafetyManager.isValidationEnabled(),
    apiValidationEnabled: typeSafetyManager.isApiValidationEnabled(),
    componentValidationEnabled: typeSafetyManager.isComponentValidationEnabled(),
    debugMode: typeSafetyManager.isDebugMode(),
  };
}

// ==================== 类型安全初始化 ====================

/**
 * 初始化类型安全系统
 */
export function initializeTypeSafety(config?: Partial<TypeSafetyConfig>): void {
  if (config) {
    typeSafetyManager.updateConfig(config);
  }

  if (typeSafetyManager.isDebugMode()) {
    console.log('🛡️ 类型安全系统已初始化', typeSafetyManager.getConfig());
  }
}

// 自动初始化
if (typeof window !== 'undefined') {
  initializeTypeSafety();
}
