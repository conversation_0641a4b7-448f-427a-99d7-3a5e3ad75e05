/**
 * 配比数据转换工具
 * 确保新旧版本配比页面数据结构一致性
 */

import type {
  LegacyRatioMaterial,
  UnifiedRatioMaterial,
  RatioMaterialConverter,
} from '@/core/types/ratio';
import { MaterialCategory } from '@/core/types/ratio';

/**
 * 材料类型映射
 */
const MATERIAL_TYPE_MAPPING: Record<string, MaterialCategory> = {
  水泥: MaterialCategory.CEMENTITIOUS,
  水: MaterialCategory.WATER,
  砂子: MaterialCategory.AGGREGATE,
  石子: MaterialCategory.AGGREGATE,
  外加剂: MaterialCategory.ADMIXTURE,
  粉煤灰: MaterialCategory.SUPPLEMENTARY,
  矿粉: MaterialCategory.SUPPLEMENTARY,
  膨胀剂: MaterialCategory.SPECIAL_ADDITIVE,
  防冻剂: MaterialCategory.SPECIAL_ADDITIVE,
  早强剂: MaterialCategory.SPECIAL_ADDITIVE,
  超细砂: MaterialCategory.AGGREGATE,
};

/**
 * 反向材料类型映射
 */
const REVERSE_MATERIAL_TYPE_MAPPING: Record<MaterialCategory, string> = {
  [MaterialCategory.CEMENTITIOUS]: '水泥',
  [MaterialCategory.WATER]: '水',
  [MaterialCategory.AGGREGATE]: '砂子', // 默认为砂子，实际使用时需要根据具体材料判断
  [MaterialCategory.ADMIXTURE]: '外加剂',
  [MaterialCategory.SUPPLEMENTARY]: '粉煤灰', // 默认为粉煤灰
  [MaterialCategory.SPECIAL_ADDITIVE]: '膨胀剂', // 默认为膨胀剂
  [MaterialCategory.FIBER]: '纤维',
};

/**
 * 配比材料数据转换器
 */
export const ratioMaterialConverter: RatioMaterialConverter = {
  /**
   * 将旧版材料数据转换为统一格式
   */
  legacyToUnified: (legacy: LegacyRatioMaterial): UnifiedRatioMaterial => {
    const category = MATERIAL_TYPE_MAPPING[legacy.materialType] || MaterialCategory.CEMENTITIOUS;

    return {
      id: legacy.id,
      materialId: legacy.id,
      name: legacy.materialType,
      specification: legacy.spec,
      category,
      unit: getUnitByCategory(category),
      density: getDensityByCategory(category),
      theoreticalAmount: legacy.theoryAmount,
      actualAmount: legacy.actualAmount,
      waterContent: legacy.waterRatio,
      stoneContent: legacy.stoneRatio,
      designValue: legacy.designValue,
      siloId: generateSiloId(legacy.binName),
      siloName: legacy.binName,
      cost: 0, // 默认值，可后续设置
      supplier: '', // 默认值，可后续设置
      // 添加缺失的可选属性
      actualValue: legacy.actualAmount,
      specificGravity: getDensityByCategory(category),
      grade: legacy.spec,
      ratio: 1.0,
      percentage: 0,
    };
  },

  /**
   * 将统一格式材料数据转换为旧版格式
   */
  unifiedToLegacy: (unified: UnifiedRatioMaterial): LegacyRatioMaterial => {
    return {
      id: unified.id,
      materialType: unified.name,
      spec: unified.specification,
      theoryAmount: unified.theoreticalAmount,
      waterRatio: unified.waterContent,
      stoneRatio: unified.stoneContent,
      actualAmount: unified.actualAmount,
      designValue: unified.designValue,
      binName: unified.siloName,
    };
  },
};

/**
 * 根据材料类别获取单位
 */
function getUnitByCategory(category: MaterialCategory): string {
  const unitMapping: Record<MaterialCategory, string> = {
    [MaterialCategory.CEMENTITIOUS]: 'kg',
    [MaterialCategory.WATER]: 'kg',
    [MaterialCategory.AGGREGATE]: 'kg',
    [MaterialCategory.ADMIXTURE]: 'kg',
    [MaterialCategory.SUPPLEMENTARY]: 'kg',
    [MaterialCategory.SPECIAL_ADDITIVE]: 'kg',
    [MaterialCategory.FIBER]: 'kg',
  };

  return unitMapping[category] || 'kg';
}

/**
 * 根据材料类别获取密度
 */
function getDensityByCategory(category: MaterialCategory): number {
  const densityMapping: Record<MaterialCategory, number> = {
    [MaterialCategory.CEMENTITIOUS]: 3.1,
    [MaterialCategory.WATER]: 1.0,
    [MaterialCategory.AGGREGATE]: 2.65,
    [MaterialCategory.ADMIXTURE]: 1.2,
    [MaterialCategory.SUPPLEMENTARY]: 2.2,
    [MaterialCategory.SPECIAL_ADDITIVE]: 2.8,
    [MaterialCategory.FIBER]: 1.5,
  };

  return densityMapping[category] || 2.4;
}

/**
 * 根据料仓名称生成料仓ID
 */
function generateSiloId(binName: string): string {
  // 简单的ID生成逻辑，可根据实际需求调整
  return `silo-${binName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`;
}

/**
 * 批量转换旧版材料数据
 */
export function convertLegacyMaterialsToUnified(
  legacyMaterials: LegacyRatioMaterial[]
): UnifiedRatioMaterial[] {
  return legacyMaterials.map(ratioMaterialConverter.legacyToUnified);
}

/**
 * 批量转换统一格式材料数据为旧版格式
 */
export function convertUnifiedMaterialsToLegacy(
  unifiedMaterials: UnifiedRatioMaterial[]
): LegacyRatioMaterial[] {
  return unifiedMaterials.map(ratioMaterialConverter.unifiedToLegacy);
}

/**
 * 验证材料数据完整性
 */
export function validateMaterialData(material: UnifiedRatioMaterial): boolean {
  return !!(
    material.id &&
    material.name &&
    material.specification &&
    material.category &&
    typeof material.theoreticalAmount === 'number' &&
    typeof material.actualAmount === 'number'
  );
}

/**
 * 计算材料实际用量（考虑含水率和含石率）
 */
export function calculateActualAmount(
  theoreticalAmount: number,
  waterContent: number,
  stoneContent: number
): number {
  // 实际用量 = 理论用量 × (1 + 含水率/100) × (1 + 含石率/100)
  return theoreticalAmount * (1 + waterContent / 100) * (1 + stoneContent / 100);
}

/**
 * 更新材料实际用量
 */
export function updateMaterialActualAmount(material: UnifiedRatioMaterial): UnifiedRatioMaterial {
  return {
    ...material,
    actualAmount: calculateActualAmount(
      material.theoreticalAmount,
      material.waterContent,
      material.stoneContent
    ),
  };
}
