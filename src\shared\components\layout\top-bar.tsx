'use client';

import React, { useCallback, useState } from 'react';
import { useRouter } from 'next/navigation';

import { Bell, Truck } from 'lucide-react';

import { ActionBar } from '@/features/system-settings/components/action-bar';
import { MixingPlantTabs } from '@/features/system-settings/components/mixing-plant-tabs';
import { Button } from '@/shared/components/button';
import { useToast } from '@/shared/hooks/use-toast';
import { useUiStore } from '@/infrastructure/storage/stores/uiStore';
import type { Plant } from '@/core/types';

import { ReminderMessageListClientWrapper } from '@/core/providers/reminder-message-list-client-wrapper';
import { ThemeSelector } from './theme-selector';

const MemoizedMixingPlantTabs = React.memo(MixingPlantTabs);
const MemoizedActionBar = React.memo(ActionBar);

interface TopBarProps {
  plants: Plant[]; // Prop type remains the same
  onInitiateCrossPlantDispatchAction: (vehicleId: string, targetPlantId: string) => void;
}

export function TopBar({ plants, onInitiateCrossPlantDispatchAction }: TopBarProps) {
  const { selectedPlantId, setSelectedPlantId } = useUiStore();
  const { toast } = useToast();
  const router = useRouter();

  return (
    <div className='p-1 border-b bg-card flex flex-col h-full overflow-hidden'>
      <div className='flex items-center justify-between flex-shrink-0 mb-0.5'>
        <MemoizedMixingPlantTabs
          plants={plants}
          selectedPlantId={selectedPlantId}
          onSelectPlantAction={setSelectedPlantId}
          onInitiateCrossPlantDispatchAction={onInitiateCrossPlantDispatchAction}
        />
        <div className='flex items-center space-x-1'>
          <ReminderMessageListClientWrapper />
          <ThemeSelector />

          {/* 车辆调度演示按钮 */}
          {process.env.NODE_ENV === 'development' && (
            <Button
              variant='ghost'
              size='icon'
              onClick={() => router.push('/vehicle-dispatch-demo')}
              title='车辆调度演示'
            >
              <Truck className='h-4 w-4' />
            </Button>
          )}

          {/* 添加测试按钮 */}
          {process.env.NODE_ENV === 'development' && (
            <Button
              variant='ghost'
              size='icon'
              onClick={() => {
                toast({
                  title: '测试提醒',
                  description: '已添加测试提醒消息',
                });
              }}
              className='mx-1'
            >
              <Bell className='h-[1.2rem] w-[1.2rem] text-accent' />
            </Button>
          )}
        </div>
      </div>
      <div className='flex-shrink-0'>
        {/* Pass plants to ActionBar if AnnounceVehicleArrivalModal needs it */}
        <MemoizedActionBar plants={plants} />
      </div>
    </div>
  );
}
