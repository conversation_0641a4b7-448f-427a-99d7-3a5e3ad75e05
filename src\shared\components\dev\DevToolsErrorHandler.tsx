/**
 * DevTools Error Handler Component
 * 客户端组件，用于处理和抑制 React DevTools 相关错误
 */

'use client';

import { useEffect } from 'react';

export function DevToolsErrorHandler() {
  useEffect(() => {
    // 只在开发环境运行
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    // 检查是否为 DevTools 相关错误
    const isDevToolsError = (error: any): boolean => {
      const message = error?.message || error?.toString() || '';

      return (
        message.includes('Cannot add child') ||
        message.includes('parent node was not found in the Store') ||
        message.includes("Failed to execute 'removeChild' on 'Node'") ||
        message.includes('The node to be removed is not a child of this node') ||
        message.includes('DevTools') ||
        message.includes('chrome-extension://') ||
        message.includes('fmkadmapgofadopljbjfkapdkoienihi') // React DevTools extension ID
      );
    };

    // 处理未捕获的错误
    const handleError = (event: ErrorEvent) => {
      if (isDevToolsError(event.error)) {
        event.preventDefault();
        event.stopPropagation();
        console.debug('🛠️ DevTools error suppressed:', event.error?.message);
        return false;
      }
      return true;
    };

    // 处理未捕获的 Promise 拒绝
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (isDevToolsError(event.reason)) {
        event.preventDefault();
        console.debug('🛠️ DevTools promise rejection suppressed:', event.reason?.message);
        return false;
      }
      return true;
    };

    // 重写 console.error 以过滤 DevTools 错误
    const originalConsoleError = console.error;
    const filteredConsoleError = (...args: any[]) => {
      const message = args[0];

      if (isDevToolsError(message)) {
        console.debug('🛠️ DevTools console error suppressed:', message);
        return;
      }

      originalConsoleError.apply(console, args);
    };

    // 添加事件监听器
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    console.error = filteredConsoleError;

    console.log('🛠️ DevTools error handler initialized');

    // 清理函数
    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      console.error = originalConsoleError;
      console.log('🧹 DevTools error handler cleaned up');
    };
  }, []);

  return null;
}
