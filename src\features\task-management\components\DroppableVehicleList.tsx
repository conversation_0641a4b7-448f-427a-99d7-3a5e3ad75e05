'use client';

import React from 'react';

import { useDroppable } from '@dnd-kit/core';
import {
  horizontalListSortingStrategy,
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';

import { useDragDropContext } from '@/shared/components/contexts/DragDropContext';
import { cn } from '@/core/lib/utils';
import type { Task, Vehicle } from '@/core/types';

import { DraggableVehicleCard } from './DraggableVehicleCard';

interface DroppableVehicleListProps {
  droppableId: string;
  vehicles: Vehicle[];
  task?: Task;
  productionLineId?: string;
  vehicleDisplayMode?: 'compact' | 'normal' | 'detailed';
  inTaskVehicleCardStyles?: any;
  productionLineCount?: number;
  density?: 'compact' | 'normal' | 'loose';
  direction?: 'horizontal' | 'vertical';
  className?: string;
  placeholder?: string;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string) => void;
  onOpenContextMenu?: (e: React.MouseEvent, vehicle: Vehicle) => void;
}

export const DroppableVehicleList: React.FC<DroppableVehicleListProps> = ({
  droppableId,
  vehicles,
  task,
  productionLineId,
  vehicleDisplayMode = 'normal',
  inTaskVehicleCardStyles,
  productionLineCount = 1,
  density = 'normal',
  direction = 'horizontal',
  className,
  placeholder = '拖拽车辆到此处',
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenContextMenu,
}) => {
  const { state } = useDragDropContext();

  // 获取容器样式
  const getContainerStyles = () => {
    const baseStyles = 'transition-all duration-200 ease-in-out rounded-lg border-2 border-dashed';

    if (direction === 'vertical') {
      return cn(baseStyles, 'flex flex-col gap-2 p-2 min-h-[100px]');
    }

    return cn(baseStyles, 'flex flex-row gap-2 p-2 min-h-[40px] flex-wrap');
  };

  // 获取拖拽状态样式
  const getDragOverStyles = (isDraggingOver: boolean, draggingFromThisWith: boolean) => {
    if (isDraggingOver) {
      return 'border-blue-400 bg-blue-50/50 shadow-lg';
    }

    if (state.isDragging && !draggingFromThisWith) {
      return 'border-gray-300 bg-gray-50/30';
    }

    return 'border-gray-200 bg-transparent';
  };

  // 获取占位符样式
  const getPlaceholderStyles = () => {
    switch (density) {
      case 'compact':
        return 'text-xs text-gray-400';
      case 'loose':
        return 'text-base text-gray-500';
      default:
        return 'text-sm text-gray-400';
    }
  };

  // 设置拖拽放置数据
  const dropData = {
    type: task ? 'task-drop-zone' : 'vehicle-list-reorder',
    taskId: task?.id,
    productionLineId: productionLineId,
  };

  const { isOver, setNodeRef } = useDroppable({
    id: droppableId,
    data: dropData,
  });

  const vehicleIds = vehicles.map(v => v.id);
  const strategy =
    direction === 'vertical' ? verticalListSortingStrategy : horizontalListSortingStrategy;

  return (
    <div
      ref={setNodeRef}
      className={cn(getContainerStyles(), getDragOverStyles(isOver, false), className)}
    >
      <SortableContext items={vehicleIds} strategy={strategy}>
        {/* 车辆列表 */}
        {vehicles.map((vehicle, index) => (
          <DraggableVehicleCard
            key={vehicle.id}
            vehicle={vehicle}
            index={index}
            task={task}
            productionLineId={productionLineId}
            vehicleDisplayMode={vehicleDisplayMode}
            inTaskVehicleCardStyles={inTaskVehicleCardStyles}
            productionLineCount={productionLineCount}
            density={density}
            onCancelDispatch={onCancelDispatch}
            onOpenStyleEditor={onOpenStyleEditor}
            onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetails}
            onOpenContextMenu={onOpenContextMenu}
          />
        ))}

        {/* 空状态占位符 */}
        {vehicles.length === 0 && !state.isDragging && (
          <div
            className={cn(
              'flex items-center justify-center flex-1 min-h-[40px]',
              getPlaceholderStyles()
            )}
          >
            {placeholder}
          </div>
        )}

        {/* 拖拽时的动态占位符 */}
        {vehicles.length === 0 && state.isDragging && isOver && (
          <div
            className={cn(
              'flex items-center justify-center flex-1 min-h-[40px]',
              'text-blue-600 font-medium animate-pulse'
            )}
          >
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-blue-400 rounded-full animate-bounce' />
              <span>释放以放置车辆</span>
              <div
                className='w-2 h-2 bg-blue-400 rounded-full animate-bounce'
                style={{ animationDelay: '0.1s' }}
              />
            </div>
          </div>
        )}

        {/* 拖拽目标高亮效果 */}
        {state.isDragging &&
          state.targetTaskId === task?.id &&
          state.targetProductionLineId === productionLineId && (
            <div className='absolute inset-0 bg-gradient-to-r from-blue-400/10 to-green-400/10 rounded-lg pointer-events-none animate-pulse' />
          )}
      </SortableContext>
    </div>
  );
};
