/**
 * 组件类型守卫系统
 * 提供React组件Props的类型安全验证
 */

import React from 'react';
import { v, validateData, isValidData, BaseValidator } from './runtime-validator';
import type { ReactNode } from 'react';

// ==================== 通用组件Props类型 ====================

export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
  id?: string;
  'data-testid'?: string;
}

export interface ClickableProps extends BaseComponentProps {
  onClick?: (event: React.MouseEvent) => void;
  disabled?: boolean;
  'aria-label'?: string;
}

export interface FormFieldProps extends BaseComponentProps {
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  helperText?: string;
}

// ==================== 基础组件Props验证器 ====================

export const baseComponentPropsValidator = v.object<BaseComponentProps>({
  className: v.optional(v.string()),
  children: v.optional(v.object({})), // ReactNode类型较复杂，这里简化处理
  id: v.optional(v.string()),
  'data-testid': v.optional(v.string()),
});

export const clickablePropsValidator = v.object<ClickableProps>({
  ...(baseComponentPropsValidator as any).schema,
  onClick: v.optional(v.object({})), // 函数类型简化处理
  disabled: v.optional(v.boolean()),
  'aria-label': v.optional(v.string()),
});

export const formFieldPropsValidator = v.object<FormFieldProps>(
  {
    ...(baseComponentPropsValidator as any).schema,
    name: v.nonEmptyString(),
    label: v.optional(v.string()),
    placeholder: v.optional(v.string()),
    required: v.optional(v.boolean()),
    disabled: v.optional(v.boolean()),
    error: v.optional(v.string()),
    helperText: v.optional(v.string()),
  },
  ['name']
);

// ==================== 业务组件Props验证器 ====================

// TaskCard Props
export interface TaskCardProps extends ClickableProps {
  task: {
    id: string;
    taskNumber: string;
    projectName: string;
    vehicleCount: number;
    completedProgress: number;
    requiredVolume: number;
    completedVolume: number;
  };
  isSelected?: boolean;
  isDragging?: boolean;
  isOverlay?: boolean;
  onSelect?: (taskId: string) => void;
  onDispatch?: (taskId: string) => void;
}

export const taskCardPropsValidator = v.object<TaskCardProps>(
  {
    ...(clickablePropsValidator as any).schema,
    task: v.object({
      id: v.nonEmptyString(),
      taskNumber: v.nonEmptyString(),
      projectName: v.nonEmptyString(),
      vehicleCount: v.positiveInteger(),
      completedProgress: v.number(),
      requiredVolume: v.positiveNumber(),
      completedVolume: v.number(),
    }),
    isSelected: v.optional(v.boolean()),
    isDragging: v.optional(v.boolean()),
    isOverlay: v.optional(v.boolean()),
    onSelect: v.optional(v.object({})), // 函数类型简化
    onDispatch: v.optional(v.object({})), // 函数类型简化
  },
  ['task']
);

// VehicleCard Props
export interface VehicleCardProps extends ClickableProps {
  vehicle: {
    id: string;
    licensePlate: string;
    status: string;
    capacity: number;
    currentLoad: number;
  };
  displayMode?: 'licensePlate' | 'internalId';
  showStatus?: boolean;
  onStatusChange?: (vehicleId: string, status: string) => void;
}

export const vehicleCardPropsValidator = v.object<VehicleCardProps>(
  {
    ...(clickablePropsValidator as any).schema,
    vehicle: v.object({
      id: v.nonEmptyString(),
      licensePlate: v.nonEmptyString(),
      status: v.string(),
      capacity: v.positiveNumber(),
      currentLoad: v.number(),
    }),
    displayMode: v.optional(v.string()),
    showStatus: v.optional(v.boolean()),
    onStatusChange: v.optional(v.object({})), // 函数类型简化
  },
  ['vehicle']
);

// DataTable Props
export interface DataTableProps<T = any> extends BaseComponentProps {
  data: T[];
  columns: Array<{
    key: string;
    title: string;
    width?: number;
    sortable?: boolean;
    filterable?: boolean;
  }>;
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
  };
  onRowClick?: (row: T, index: number) => void;
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  onFilter?: (filters: Record<string, any>) => void;
}

export const dataTablePropsValidator = v.object<DataTableProps>(
  {
    ...(baseComponentPropsValidator as any).schema,
    data: v.array(v.object({})),
    columns: v.array(
      v.object({
        key: v.nonEmptyString(),
        title: v.nonEmptyString(),
        width: v.optional(v.positiveNumber()),
        sortable: v.optional(v.boolean()),
        filterable: v.optional(v.boolean()),
      })
    ),
    loading: v.optional(v.boolean()),
    pagination: v.optional(
      v.object({
        current: v.positiveInteger(),
        pageSize: v.positiveInteger(),
        total: v.positiveInteger(),
      })
    ),
    onRowClick: v.optional(v.object({})), // 函数类型简化
    onSort: v.optional(v.object({})), // 函数类型简化
    onFilter: v.optional(v.object({})), // 函数类型简化
  },
  ['data', 'columns']
);

// ==================== 表单组件Props验证器 ====================

// Input Props
export interface InputProps extends FormFieldProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
  value?: string | number;
  defaultValue?: string | number;
  onChange?: (value: string | number) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  autoComplete?: string;
  autoFocus?: boolean;
}

export const inputPropsValidator = v.object<InputProps>(
  {
    ...(formFieldPropsValidator as any).schema,
    type: v.optional(v.string()),
    value: v.optional(v.union([v.string(), v.number()])),
    defaultValue: v.optional(v.union([v.string(), v.number()])),
    onChange: v.optional(v.object({})), // 函数类型简化
    onBlur: v.optional(v.object({})), // 函数类型简化
    onFocus: v.optional(v.object({})), // 函数类型简化
    maxLength: v.optional(v.positiveInteger()),
    minLength: v.optional(v.positiveInteger()),
    pattern: v.optional(v.string()),
    autoComplete: v.optional(v.string()),
    autoFocus: v.optional(v.boolean()),
  },
  ['name']
);

// Select Props
export interface SelectProps extends FormFieldProps {
  options: Array<{
    value: string | number;
    label: string;
    disabled?: boolean;
  }>;
  value?: string | number;
  defaultValue?: string | number;
  multiple?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  onChange?: (value: string | number | Array<string | number>) => void;
}

export const selectPropsValidator = v.object<SelectProps>(
  {
    ...(formFieldPropsValidator as any).schema,
    options: v.array(
      v.object({
        value: v.union([v.string(), v.number()]),
        label: v.nonEmptyString(),
        disabled: v.optional(v.boolean()),
      })
    ),
    value: v.optional(v.union([v.string(), v.number()])),
    defaultValue: v.optional(v.union([v.string(), v.number()])),
    multiple: v.optional(v.boolean()),
    searchable: v.optional(v.boolean()),
    clearable: v.optional(v.boolean()),
    onChange: v.optional(v.object({})), // 函数类型简化
  },
  ['name', 'options']
);

// ==================== 类型守卫函数 ====================

export function isValidComponentProps<T>(props: unknown, validator: BaseValidator<T>): props is T {
  return isValidData(props, validator);
}

export function validateComponentProps<T>(
  props: unknown,
  validator: BaseValidator<T>,
  componentName: string,
  options: { throwOnError?: boolean } = {}
): T {
  try {
    return validateData(props, validator, options);
  } catch (error) {
    if (error instanceof TypeError) {
      throw new Error(`组件 ${componentName} 的Props验证失败: ${error.message}`);
    }
    throw error;
  }
}

// ==================== 组件Props验证装饰器 ====================

export function withPropsValidation<T>(validator: BaseValidator<T>, componentName: string) {
  return function <P extends T>(Component: React.ComponentType<P>): React.ComponentType<P> {
    const ValidatedComponent = (props: P) => {
      if (process.env.NODE_ENV === 'development') {
        try {
          validateComponentProps(props, validator, componentName);
        } catch (error) {
          console.error(`组件 ${componentName} Props验证失败:`, error);
          // 在开发环境中，我们记录错误但不阻止渲染
        }
      }

      return React.createElement(Component as any, props as any);
    };

    ValidatedComponent.displayName = `withPropsValidation(${componentName})`;
    return ValidatedComponent;
  };
}

// ==================== 开发工具 ====================

export function debugComponentProps<T>(
  props: unknown,
  validator: BaseValidator<T>,
  componentName: string
): void {
  if (process.env.NODE_ENV === 'development') {
    const result = validator.validate(props);

    if (!result.isValid) {
      console.group(`🔍 组件 ${componentName} Props验证失败`);
      console.log('传入的Props:', props);
      console.log('验证错误:', result.errors);
      console.log('验证警告:', result.warnings);
      console.groupEnd();
    } else if (result.warnings.length > 0) {
      console.group(`⚠️ 组件 ${componentName} Props验证警告`);
      console.log('传入的Props:', props);
      console.log('验证警告:', result.warnings);
      console.groupEnd();
    }
  }
}

// ==================== 预定义验证器导出 ====================

export const componentValidators = {
  base: baseComponentPropsValidator,
  clickable: clickablePropsValidator,
  formField: formFieldPropsValidator,
  taskCard: taskCardPropsValidator,
  vehicleCard: vehicleCardPropsValidator,
  dataTable: dataTablePropsValidator,
  input: inputPropsValidator,
  select: selectPropsValidator,
} as const;

// ==================== 类型守卫导出 ====================

export const componentTypeGuards = {
  isTaskCardProps: (props: unknown): props is TaskCardProps =>
    isValidComponentProps(props, taskCardPropsValidator),

  isVehicleCardProps: (props: unknown): props is VehicleCardProps =>
    isValidComponentProps(props, vehicleCardPropsValidator),

  isDataTableProps: (props: unknown): props is DataTableProps =>
    isValidComponentProps(props, dataTablePropsValidator),

  isInputProps: (props: unknown): props is InputProps =>
    isValidComponentProps(props, inputPropsValidator),

  isSelectProps: (props: unknown): props is SelectProps =>
    isValidComponentProps(props, selectPropsValidator),
} as const;
