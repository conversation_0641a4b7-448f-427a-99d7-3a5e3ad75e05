import { useCallback, useEffect, useRef, useState } from 'react';

interface Position {
  x: number;
  y: number;
}

interface UseDraggableFabOptions {
  initialPosition: Position;
  containerRef: React.RefObject<HTMLElement>;
  fabSize: number;
  margin: number;
  storageKey?: string;
}

/**
 * 可拖拽 Fab 按钮的自定义 Hook
 * 支持右键拖拽功能，禁用默认右键菜单
 */
export function useDraggableFab({
  initialPosition,
  containerRef,
  fabSize,
  margin,
  storageKey = 'fab-position',
}: UseDraggableFabOptions) {
  const [position, setPosition] = useState<Position>(initialPosition);
  const [isDragging, setIsDragging] = useState(false);
  const dragStartRef = useRef<{
    startX: number;
    startY: number;
    initialX: number;
    initialY: number;
  } | null>(null);
  const fabRef = useRef<HTMLButtonElement>(null);

  // 从localStorage加载位置
  useEffect(() => {
    const savedPosition = localStorage.getItem(storageKey);
    if (savedPosition) {
      try {
        const parsed = JSON.parse(savedPosition);
        setPosition(parsed);
      } catch (error) {
        console.warn('Failed to parse saved fab position:', error);
      }
    }
  }, [storageKey]);

  // 保存位置到localStorage
  const savePosition = useCallback(
    (newPosition: Position) => {
      localStorage.setItem(storageKey, JSON.stringify(newPosition));
    },
    [storageKey]
  );

  // 限制位置在容器边界内
  const constrainPosition = useCallback(
    (x: number, y: number): Position => {
      if (!containerRef.current) return { x, y };

      const containerRect = containerRef.current.getBoundingClientRect();
      const maxX = containerRect.width - fabSize - margin;
      const maxY = containerRect.height - fabSize - margin;

      return {
        x: Math.max(margin, Math.min(x, maxX)),
        y: Math.max(margin, Math.min(y, maxY)),
      };
    },
    [containerRef, fabSize, margin]
  );

  /**
   * 处理鼠标右键按下事件，开始拖拽
   */
  const handleContextMenu = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault(); // 禁用默认右键菜单
      e.stopPropagation();

      setIsDragging(true);
      dragStartRef.current = {
        startX: e.clientX,
        startY: e.clientY,
        initialX: position.x,
        initialY: position.y,
      };

      document.body.style.cursor = 'grabbing';
      document.body.style.userSelect = 'none';
    },
    [position]
  );

  /**
   * 处理鼠标移动事件，更新拖拽位置
   */
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !dragStartRef.current) return;

      const deltaX = e.clientX - dragStartRef.current.startX;
      const deltaY = e.clientY - dragStartRef.current.startY;

      const newX = dragStartRef.current.initialX + deltaX;
      const newY = dragStartRef.current.initialY + deltaY;

      const constrainedPosition = constrainPosition(newX, newY);
      setPosition(constrainedPosition);
    },
    [isDragging, constrainPosition]
  );

  /**
   * 处理鼠标松开事件，结束拖拽
   */
  const handleMouseUp = useCallback(() => {
    if (!isDragging) return;

    setIsDragging(false);
    dragStartRef.current = null;

    document.body.style.cursor = '';
    document.body.style.userSelect = '';

    // 保存最终位置
    savePosition(position);
  }, [isDragging, position, savePosition]);

  // 添加全局事件监听器
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
    return undefined;
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return {
    position,
    isDragging,
    fabRef,
    handleContextMenu, // 返回右键事件处理器
  };
}
