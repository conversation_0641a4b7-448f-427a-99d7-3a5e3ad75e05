/**
 * Button组件单元测试
 */

import { fireEvent, screen } from '@testing-library/react';
import { Button } from '@/shared/components/button';
import { render } from '../../utils/test-utils.helper';

describe('Button组件', () => {
  describe('基础渲染', () => {
    it('应该正确渲染默认按钮', () => {
      render(<Button>测试按钮</Button>);

      const button = screen.getByRole('button', { name: '测试按钮' });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass('h-10', 'px-4', 'py-2'); // default size
    });

    it('应该正确渲染不同变体', () => {
      const { rerender } = render(<Button variant='default'>默认</Button>);
      expect(screen.getByRole('button')).toHaveClass('bg-primary');

      rerender(<Button variant='destructive'>危险</Button>);
      expect(screen.getByRole('button')).toHaveClass('bg-destructive');

      rerender(<Button variant='outline'>轮廓</Button>);
      expect(screen.getByRole('button')).toHaveClass('border-input');

      rerender(<Button variant='secondary'>次要</Button>);
      expect(screen.getByRole('button')).toHaveClass('bg-secondary');

      rerender(<Button variant='ghost'>幽灵</Button>);
      expect(screen.getByRole('button')).toHaveClass('hover:bg-accent');

      rerender(<Button variant='link'>链接</Button>);
      expect(screen.getByRole('button')).toHaveClass('text-primary', 'underline-offset-4');
    });

    it('应该正确渲染不同尺寸', () => {
      const { rerender } = render(<Button size='default'>默认</Button>);
      expect(screen.getByRole('button')).toHaveClass('h-10', 'px-4', 'py-2');

      rerender(<Button size='sm'>小</Button>);
      expect(screen.getByRole('button')).toHaveClass('h-9', 'px-3');

      rerender(<Button size='lg'>大</Button>);
      expect(screen.getByRole('button')).toHaveClass('h-11', 'px-8');

      rerender(<Button size='icon'>图标</Button>);
      expect(screen.getByRole('button')).toHaveClass('h-10', 'w-10');
    });

    it('应该支持自定义className', () => {
      render(<Button className='custom-class'>自定义</Button>);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-class');
    });

    it('应该支持disabled状态', () => {
      render(<Button disabled>禁用按钮</Button>);

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });
  });

  describe('交互测试', () => {
    it('应该响应点击事件', () => {
      const handleClick = jest.fn();
      render(<Button onClick={handleClick}>点击我</Button>);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('禁用状态下不应该响应点击事件', () => {
      const handleClick = jest.fn();
      render(
        <Button disabled onClick={handleClick}>
          禁用按钮
        </Button>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(handleClick).not.toHaveBeenCalled();
    });

    it('应该支持键盘事件', () => {
      const handleKeyDown = jest.fn();
      render(<Button onKeyDown={handleKeyDown}>键盘测试</Button>);

      const button = screen.getByRole('button');
      fireEvent.keyDown(button, { key: 'Enter' });

      expect(handleKeyDown).toHaveBeenCalledTimes(1);
    });
  });

  describe('可访问性测试', () => {
    it('应该有正确的role属性', () => {
      render(<Button>可访问性测试</Button>);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('应该支持aria-label', () => {
      render(<Button aria-label='关闭对话框'>×</Button>);

      const button = screen.getByLabelText('关闭对话框');
      expect(button).toBeInTheDocument();
    });

    it('应该支持aria-describedby', () => {
      render(
        <>
          <Button aria-describedby='help-text'>帮助按钮</Button>
          <div id='help-text'>这是帮助文本</div>
        </>
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-describedby', 'help-text');
    });
  });

  describe('asChild属性测试', () => {
    it('应该支持asChild渲染为其他元素', () => {
      render(
        <Button asChild>
          <a href='/test'>链接按钮</a>
        </Button>
      );

      const link = screen.getByRole('link');
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', '/test');
      expect(link).toHaveClass('h-10', 'px-4', 'py-2'); // 应该保持Button的样式
    });
  });

  describe('类型安全测试', () => {
    it('应该接受所有标准button属性', () => {
      render(
        <Button type='submit' form='test-form' name='test-button' value='test-value' autoFocus>
          表单按钮
        </Button>
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('type', 'submit');
      expect(button).toHaveAttribute('form', 'test-form');
      expect(button).toHaveAttribute('name', 'test-button');
      expect(button).toHaveAttribute('value', 'test-value');
    });
  });

  describe('边界条件测试', () => {
    it('应该处理空children', () => {
      render(<Button></Button>);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toBeEmptyDOMElement();
    });

    it('应该处理复杂的children结构', () => {
      render(
        <Button>
          <span>图标</span>
          <span>文本</span>
        </Button>
      );

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(screen.getByText('图标')).toBeInTheDocument();
      expect(screen.getByText('文本')).toBeInTheDocument();
    });

    it('应该处理长文本内容', () => {
      const longText =
        '这是一个非常长的按钮文本，用来测试按钮组件是否能够正确处理长文本内容的显示和布局';
      render(<Button>{longText}</Button>);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent(longText);
    });
  });
});
