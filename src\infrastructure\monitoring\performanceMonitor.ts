/**
 * 性能监控工具
 * 用于监控和分析应用性能，验证重构后的性能表现
 */

import React from 'react';

// ==================== 性能指标类型定义 ====================

export interface PerformanceMetrics {
  // 渲染性能
  renderTime: number;
  componentCount: number;
  rerenderCount: number;

  // 内存使用
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };

  // 网络性能
  networkRequests: {
    count: number;
    totalTime: number;
    averageTime: number;
    failedCount: number;
  };

  // 用户交互
  interactionMetrics: {
    firstInputDelay: number;
    largestContentfulPaint: number;
    cumulativeLayoutShift: number;
  };

  // 自定义指标
  customMetrics: Record<string, number>;
}

export interface PerformanceBenchmark {
  name: string;
  timestamp: number;
  metrics: PerformanceMetrics;
  environment: {
    userAgent: string;
    viewport: { width: number; height: number };
    connection: string;
  };
}

// ==================== 性能监控器类 ====================

export class PerformanceMonitor {
  recordRenderTime(componentName: string, duration: number) {
    throw new Error('Method not implemented.');
  }
  getAverageRenderTime(componentName: string): any {
    throw new Error('Method not implemented.');
  }
  getPerformanceReport(): any {
    throw new Error('Method not implemented.');
  }
  clearMetrics() {
    throw new Error('Method not implemented.');
  }
  private static instance: PerformanceMonitor;
  private benchmarks: PerformanceBenchmark[] = [];
  private observers: PerformanceObserver[] = [];
  private startTime: number = 0;
  private renderCount: number = 0;
  private networkRequests: Array<{ start: number; end?: number; success: boolean }> = [];

  private constructor() {
    this.initializeObservers();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * 初始化性能观察器
   */
  private initializeObservers(): void {
    if (typeof window === 'undefined') return;

    try {
      // 观察导航性能
      const navObserver = new PerformanceObserver(list => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.entryType === 'navigation') {
            this.recordNavigationMetrics(entry as PerformanceNavigationTiming);
          }
        });
      });
      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navObserver);

      // 观察资源加载性能
      const resourceObserver = new PerformanceObserver(list => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.entryType === 'resource') {
            this.recordResourceMetrics(entry as PerformanceResourceTiming);
          }
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);

      // 观察用户交互性能
      if ('PerformanceEventTiming' in window) {
        const eventObserver = new PerformanceObserver(list => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            this.recordInteractionMetrics(entry);
          });
        });
        eventObserver.observe({ entryTypes: ['event'] });
        this.observers.push(eventObserver);
      }
    } catch (error) {
      console.warn('Performance observers not supported:', error);
    }
  }

  /**
   * 记录导航性能指标
   */
  private recordNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = {
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      firstPaint: this.getFirstPaint(),
      firstContentfulPaint: this.getFirstContentfulPaint(),
    };

    console.log('Navigation Metrics:', metrics);
  }

  /**
   * 记录资源加载性能指标
   */
  private recordResourceMetrics(entry: PerformanceResourceTiming): void {
    if (entry.name.includes('/api/')) {
      this.networkRequests.push({
        start: entry.startTime,
        end: entry.responseEnd,
        success: entry.responseStatus ? entry.responseStatus < 400 : true,
      });
    }
  }

  /**
   * 记录用户交互性能指标
   */
  private recordInteractionMetrics(entry: PerformanceEntry): void {
    // 记录交互延迟等指标
    console.log('Interaction Metrics:', {
      type: entry.entryType,
      duration: entry.duration,
      startTime: entry.startTime,
    });
  }

  /**
   * 获取First Paint时间
   */
  private getFirstPaint(): number {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : 0;
  }

  /**
   * 获取First Contentful Paint时间
   */
  private getFirstContentfulPaint(): number {
    const paintEntries = performance.getEntriesByType('paint');
    const firstContentfulPaint = paintEntries.find(
      entry => entry.name === 'first-contentful-paint'
    );
    return firstContentfulPaint ? firstContentfulPaint.startTime : 0;
  }

  /**
   * 开始性能测量
   */
  startMeasurement(name: string): void {
    this.startTime = performance.now();
    performance.mark(`${name}-start`);
  }

  /**
   * 结束性能测量
   */
  endMeasurement(name: string): number {
    const endTime = performance.now();
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);

    const duration = endTime - this.startTime;
    console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`);

    return duration;
  }

  /**
   * 记录组件渲染
   */
  recordRender(componentName: string): void {
    this.renderCount++;
    console.log(`Render: ${componentName} (Total renders: ${this.renderCount})`);
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage(): PerformanceMetrics['memoryUsage'] {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
      };
    }

    return { used: 0, total: 0, percentage: 0 };
  }

  /**
   * 获取网络请求指标
   */
  getNetworkMetrics(): PerformanceMetrics['networkRequests'] {
    const completedRequests = this.networkRequests.filter(req => req.end);
    const totalTime = completedRequests.reduce((sum, req) => sum + (req.end! - req.start), 0);
    const failedCount = completedRequests.filter(req => !req.success).length;

    return {
      count: completedRequests.length,
      totalTime,
      averageTime: completedRequests.length > 0 ? totalTime / completedRequests.length : 0,
      failedCount,
    };
  }

  /**
   * 创建性能基准
   */
  createBenchmark(name: string): PerformanceBenchmark {
    const benchmark: PerformanceBenchmark = {
      name,
      timestamp: Date.now(),
      metrics: {
        renderTime: this.endMeasurement(name),
        componentCount: this.renderCount,
        rerenderCount: this.renderCount,
        memoryUsage: this.getMemoryUsage(),
        networkRequests: this.getNetworkMetrics(),
        interactionMetrics: {
          firstInputDelay: 0, // 需要通过其他方式获取
          largestContentfulPaint: 0,
          cumulativeLayoutShift: 0,
        },
        customMetrics: {},
      },
      environment: {
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        connection: this.getConnectionType(),
      },
    };

    this.benchmarks.push(benchmark);
    return benchmark;
  }

  /**
   * 获取连接类型
   */
  private getConnectionType(): string {
    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection;
    return connection ? connection.effectiveType || 'unknown' : 'unknown';
  }

  /**
   * 比较性能基准
   */
  compareBenchmarks(
    baseline: string,
    current: string
  ): {
    improvements: string[];
    regressions: string[];
    summary: Record<
      string,
      { baseline: number; current: number; change: number; changePercent: number }
    >;
  } {
    const baselineBenchmark = this.benchmarks.find(b => b.name === baseline);
    const currentBenchmark = this.benchmarks.find(b => b.name === current);

    if (!baselineBenchmark || !currentBenchmark) {
      throw new Error('Benchmark not found');
    }

    const improvements: string[] = [];
    const regressions: string[] = [];
    const summary: Record<string, any> = {};

    // 比较渲染时间
    const renderTimeChange =
      currentBenchmark.metrics['renderTime'] - baselineBenchmark.metrics['renderTime'];
    const renderTimeChangePercent =
      (renderTimeChange / baselineBenchmark.metrics['renderTime']) * 100;

    summary['renderTime'] = {
      baseline: baselineBenchmark.metrics['renderTime'],
      current: currentBenchmark.metrics['renderTime'],
      change: renderTimeChange,
      changePercent: renderTimeChangePercent,
    };

    if (renderTimeChange < 0) {
      improvements.push(`渲染时间改善 ${Math.abs(renderTimeChangePercent).toFixed(2)}%`);
    } else if (renderTimeChange > 0) {
      regressions.push(`渲染时间回退 ${renderTimeChangePercent.toFixed(2)}%`);
    }

    // 比较内存使用
    const memoryChange =
      currentBenchmark.metrics.memoryUsage.percentage -
      baselineBenchmark.metrics.memoryUsage.percentage;
    summary['memoryUsage'] = {
      baseline: baselineBenchmark.metrics.memoryUsage.percentage,
      current: currentBenchmark.metrics.memoryUsage.percentage,
      change: memoryChange,
      changePercent: memoryChange,
    };

    if (memoryChange < 0) {
      improvements.push(`内存使用减少 ${Math.abs(memoryChange).toFixed(2)}%`);
    } else if (memoryChange > 0) {
      regressions.push(`内存使用增加 ${memoryChange.toFixed(2)}%`);
    }

    return { improvements, regressions, summary };
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const report = [
      '# 性能监控报告',
      '',
      `生成时间: ${new Date().toLocaleString()}`,
      `基准数量: ${this.benchmarks.length}`,
      '',
    ];

    if (this.benchmarks.length >= 2) {
      const latest = this.benchmarks[this.benchmarks.length - 1];
      const previous = this.benchmarks[this.benchmarks.length - 2];

      const comparison =
        previous && latest ? this.compareBenchmarks(previous.name, latest.name) : null;

      report.push('## 性能对比');
      report.push('');

      if (comparison && comparison.improvements.length > 0) {
        report.push('### 🚀 性能改善');
        comparison.improvements.forEach(improvement => {
          report.push(`- ${improvement}`);
        });
        report.push('');
      }

      if (comparison && comparison.regressions.length > 0) {
        report.push('### ⚠️ 性能回退');
        comparison.regressions.forEach(regression => {
          report.push(`- ${regression}`);
        });
        report.push('');
      }

      if (comparison) {
        report.push('### 📊 详细对比');
        Object.entries(comparison.summary).forEach(([metric, data]) => {
          report.push(
            `**${metric}**: ${data.baseline.toFixed(2)} → ${data.current.toFixed(2)} (${data.changePercent > 0 ? '+' : ''}${data.changePercent.toFixed(2)}%)`
          );
        });
      }
    }

    return report.join('\n');
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.benchmarks = [];
    this.networkRequests = [];
  }
}

// ==================== React性能监控Hook ====================

export function usePerformanceMonitor(componentName: string) {
  const monitor = PerformanceMonitor.getInstance();

  React.useEffect(() => {
    monitor.recordRender(componentName);
  });

  const startMeasurement = React.useCallback(
    (name: string) => {
      monitor.startMeasurement(`${componentName}-${name}`);
    },
    [componentName, monitor]
  );

  const endMeasurement = React.useCallback(
    (name: string) => {
      return monitor.endMeasurement(`${componentName}-${name}`);
    },
    [componentName, monitor]
  );

  return {
    startMeasurement,
    endMeasurement,
    createBenchmark: (name: string) => monitor.createBenchmark(`${componentName}-${name}`),
  };
}

// ==================== 性能测试工具 ====================

export const performanceTestUtils = {
  /**
   * 测试组件渲染性能
   */
  async testComponentRender<T>(
    component: React.ComponentType<T>,
    props: T,
    iterations = 100
  ): Promise<{ averageTime: number; minTime: number; maxTime: number }> {
    const times: number[] = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();

      // 这里需要实际的渲染测试，可能需要配合测试环境
      // 暂时使用模拟
      await new Promise(resolve => setTimeout(resolve, Math.random() * 10));

      const endTime = performance.now();
      times.push(endTime - startTime);
    }

    return {
      averageTime: times.reduce((sum, time) => sum + time, 0) / times.length,
      minTime: Math.min(...times),
      maxTime: Math.max(...times),
    };
  },

  /**
   * 测试内存泄漏
   */
  async testMemoryLeak(
    testFunction: () => void,
    iterations = 1000
  ): Promise<{ initialMemory: number; finalMemory: number; leaked: boolean }> {
    const monitor = PerformanceMonitor.getInstance();

    const initialMemory = monitor.getMemoryUsage().used;

    for (let i = 0; i < iterations; i++) {
      testFunction();

      // 每100次迭代强制垃圾回收（如果可用）
      if (i % 100 === 0 && 'gc' in window) {
        (window as any).gc();
      }
    }

    // 等待垃圾回收
    await new Promise(resolve => setTimeout(resolve, 1000));

    const finalMemory = monitor.getMemoryUsage().used;
    const memoryIncrease = finalMemory - initialMemory;
    const leaked = memoryIncrease > initialMemory * 0.1; // 如果内存增长超过10%认为可能有泄漏

    return {
      initialMemory,
      finalMemory,
      leaked,
    };
  },
};

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance();
