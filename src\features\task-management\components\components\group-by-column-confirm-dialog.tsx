// src/components/sections/task-list/components/group-by-column-confirm-dialog.tsx
import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/shared/components/alert-dialog';
import { CustomColumnDefinition } from '@/core/types';

interface GroupByColumnConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  columnDef: CustomColumnDefinition | null;
}

/**
 * 按列分组确认对话框
 *
 * 当用户右键点击列头时，显示此对话框询问是否按该列进行分组
 */
export function GroupByColumnConfirmDialog({
  isOpen,
  onClose,
  onConfirm,
  columnDef,
}: GroupByColumnConfirmDialogProps) {
  if (!columnDef) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <AlertDialogContent className='max-w-md'>
        <AlertDialogHeader>
          <AlertDialogTitle>按列分组确认</AlertDialogTitle>
          <AlertDialogDescription>
            是否按照 <span className='font-medium text-primary'>{columnDef.label}</span>{' '}
            列对任务进行分组？
            <br />
            <br />
            分组后，任务将按照 {columnDef.label} 的值进行分组显示，可以更清晰地查看和管理任务。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm}>确认分组</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
