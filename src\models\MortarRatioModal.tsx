'use client';

import React, { useEffect, useMemo, useState } from 'react';

import { Button } from '@/shared/components/button';
import { Checkbox } from '@/shared/components/checkbox';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import {
  EditableTable,
  type TableRowData,
  type TableColumn,
} from '@/shared/components/editable-table';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
// 导入统一的配比数据
import { mockMaterialNames, mockSpecifications } from '@/infrastructure/api/mock/ratio-mock-data';
import { mockPlants } from '@/infrastructure/api/mock/mock-data';
import { useToast } from '@/shared/hooks/use-toast';

interface MortarRatioModalProps {
  isOpen?: boolean;
  open?: boolean;
  onOpenChangeAction?: (open: boolean) => void;
  onOpenChange?: (open: boolean) => void;
}

// 导入统一的砂浆配比数据
import { mockMortarTableData } from '@/infrastructure/api/mock/ratio-mock-data';

const initialTableData: TableRowData[] = mockMortarTableData;

export const MortarRatioModal: React.FC<MortarRatioModalProps> = ({
  isOpen = false,
  open = false,
  onOpenChangeAction,
  onOpenChange,
}) => {
  const { toast } = useToast();
  const [tableData, setTableData] = useState<TableRowData[]>(initialTableData);
  const [density, setDensity] = useState(1.8);

  const totalWeight = useMemo(() => {
    return (tableData || []).reduce((sum, row) => sum + (row['actualAmount'] || 0), 0);
  }, [tableData]);

  const handleUpdateRow = (id: string, field: keyof TableRowData, value: any) => {
    setTableData(prevData => {
      const newData = prevData.map(row => {
        if (row.id === id) {
          const updatedRow = { ...row, [field]: value };
          if (field === 'theoryAmount' || field === 'waterContent') {
            const theory = field === 'theoryAmount' ? Number(value) : updatedRow['theoryAmount'];
            const water = field === 'waterContent' ? Number(value) : updatedRow['waterContent'];
            updatedRow['actualAmount'] = theory * (1 + (water || 0) / 100);
          }
          return updatedRow;
        }
        return row;
      });
      return newData;
    });
  };

  const handleAddRow = () => {
    const hasEmptyRow = tableData.some(row => !row['materialName']);
    if (hasEmptyRow) {
      toast({
        title: '操作无效',
        description: '请先填写当前空的材料行，再添加新行。',
        variant: 'destructive',
      });
      return;
    }
    const newRow: TableRowData = {
      id: `new_${Date.now()}`,
      materialName: '',
      spec: '',
      theoryAmount: 0,
      waterContent: 0,
      actualAmount: 0,
      designValue: 0,
    };
    setTableData([...tableData, newRow]);
  };

  const handleDeleteRow = (id: string) => {
    setTableData(tableData.filter(row => row.id !== id));
  };

  const columns: TableColumn<TableRowData>[] = [
    {
      key: 'materialName',
      header: '货物名',
      type: 'select',
      options: (mockMaterialNames || []).map(m => m.name),
    },
    {
      key: 'spec',
      header: '规格',
      type: 'select',
      options: (mockSpecifications || []).map(s => s.name),
    },
    { key: 'theoryAmount', header: '理论量', type: 'number' },
    { key: 'waterContent', header: '含水率', type: 'percentage' },
    { key: 'actualAmount', header: '实际量', type: 'readonly' },
    { key: 'designValue', header: '设计量', type: 'number' },
  ];

  // 统一处理open状态和回调
  const modalOpen = isOpen || open;
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChangeAction?.(newOpen);
    onOpenChange?.(newOpen);
  };

  return (
    <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
      <DialogContent className='max-w-4xl'>
        <DialogHeader className='flex flex-row items-center justify-between pr-6'>
          <DialogTitle>砂浆配比</DialogTitle>
          <div className='flex items-center space-x-4'>
            <div className='flex items-center space-x-2'>
              <Checkbox id='unify-ratio' />
              <Label htmlFor='unify-ratio'>所有站统一配比</Label>
            </div>
            <Select defaultValue='plant1'>
              <SelectTrigger className='w-[150px] h-8'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {mockPlants.map(p => (
                  <SelectItem key={p.id} value={p.id}>
                    {p.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant='outline' size='sm'>
              显示罐信息
            </Button>
          </div>
        </DialogHeader>
        <div className='py-2 space-y-2'>
          <div className='flex items-center gap-4 text-sm px-2 py-1 bg-muted rounded-md'>
            <span>
              任务编号: <span className='font-semibold text-primary'>C125-00003</span>
            </span>
            <span>
              强度等级: <span className='font-semibold text-primary'>C15</span>
            </span>
            <div className='flex items-center gap-2 ml-auto'>
              <Label>总重量:</Label>
              <span className='font-bold text-blue-600'>{totalWeight.toFixed(1)}</span>
              <Label>密度:</Label>
              <Input
                type='number'
                value={density}
                onChange={e => setDensity(parseFloat(e.target.value) || 0)}
                className='w-20 h-7 text-center'
              />
              <span>吨/m³</span>
            </div>
            <Button variant='outline' size='sm'>
              选择配比
            </Button>
          </div>
          <div className='max-h-[45vh] overflow-y-auto'>
            <EditableTable
              columns={columns}
              data={tableData}
              onUpdateRowAction={handleUpdateRow}
              onDeleteRowAction={handleDeleteRow}
            />
          </div>
          <Button variant='outline' size='sm' onClick={handleAddRow} className='w-full mt-2'>
            增加材料
          </Button>
        </div>
        <DialogFooter>
          <Button variant='secondary' onClick={() => handleOpenChange(false)}>
            退出
          </Button>
          <Button>保存</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default MortarRatioModal;
