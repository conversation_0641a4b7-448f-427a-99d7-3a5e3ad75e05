/**
 * 集中化的模拟数据生成工具
 * 统一管理所有模拟数据的生成逻辑
 */

import { createCarNumber } from '@/core/lib/utils';
import type { Task, Vehicle, SiloMapping, RatioHistoryEntry } from '@/core/types';

/**
 * 随机数生成工具
 */
export const randomUtils = {
  /**
   * 生成指定范围内的随机整数
   */
  randomInt: (min: number, max: number): number => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },

  /**
   * 生成指定范围内的随机浮点数
   */
  randomFloat: (min: number, max: number, decimals = 2): number => {
    const value = Math.random() * (max - min) + min;
    return Number(value.toFixed(decimals));
  },

  /**
   * 从数组中随机选择一个元素
   */
  randomChoice: <T>(array: T[]): T => {
    if (array.length === 0) {
      throw new Error('Cannot choose from empty array');
    }
    return array[Math.floor(Math.random() * array.length)]!;
  },

  /**
   * 从数组中随机选择多个元素
   */
  randomChoices: <T>(array: T[], count: number): T[] => {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, array.length));
  },

  /**
   * 生成随机布尔值
   */
  randomBoolean: (probability = 0.5): boolean => {
    return Math.random() < probability;
  },

  /**
   * 生成随机日期
   */
  randomDate: (start: Date, end: Date): Date => {
    const startTime = start.getTime();
    const endTime = end.getTime();
    const randomTime = startTime + Math.random() * (endTime - startTime);
    return new Date(randomTime);
  },

  /**
   * 生成随机字符串
   */
  randomString: (
    length: number,
    chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  ): string => {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
};

/**
 * 任务模拟数据生成器
 */
export const taskMockGenerator = {
  /**
   * 生成单个任务
   */
  generateTask: (id?: string): Task => {
    const taskId = id || `task-${randomUtils.randomString(8)}`;
    const now = new Date();
    const supplyDate = randomUtils.randomDate(
      now,
      new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    );

    return {
      id: taskId,
      taskNumber: `T${randomUtils.randomInt(100000, 999999)}`,
      projectName: randomUtils.randomChoice([
        '万科城市花园',
        '恒大绿洲',
        '碧桂园凤凰城',
        '保利香槟国际',
        '融创壹号院',
        '中海国际社区',
      ]),
      customerName: randomUtils.randomChoice([
        '北京建工集团',
        '中建三局',
        '中铁建工',
        '上海建工',
        '广州建筑',
        '深圳建设',
      ]),
      deliveryLocation: randomUtils.randomChoice([
        '北京市朝阳区建国路88号',
        '上海市浦东新区陆家嘴金融区',
        '广州市天河区珠江新城',
        '深圳市南山区科技园',
        '杭州市西湖区文三路',
        '成都市高新区天府大道',
      ]),
      concreteGrade: randomUtils.randomChoice(['C20', 'C25', 'C30', 'C35', 'C40', 'C45']),
      requiredVolume: randomUtils.randomFloat(50, 500),
      completedVolume: randomUtils.randomFloat(0, 300),
      supplyDate: supplyDate.toISOString().split('T')[0],
      supplyTime: `${randomUtils.randomInt(8, 18)}:${randomUtils.randomChoice(['00', '30'])}`,
      dispatchStatus: randomUtils.randomChoice(['New', 'InProgress', 'Completed']),
      vehicleCount: randomUtils.randomInt(1, 5),
      vehicles: [],
      priority: randomUtils.randomChoice(['high', 'medium', 'low']),
      notes: randomUtils.randomBoolean(0.3) ? '特殊要求：需要早强混凝土' : undefined,

      // 添加缺少的必需属性
      plantId: 'plant-001',
      projectAbbreviation: randomUtils.randomChoice([
        '万科',
        '恒大',
        '碧桂园',
        '保利',
        '融创',
        '中海',
      ]),
      constructionUnit: randomUtils.randomChoice(['北京建工', '中建三局', '中铁建工', '上海建工']),
      constructionSite: randomUtils.randomChoice(['A区', 'B区', 'C区', '主楼', '副楼']),
      strength: randomUtils.randomChoice(['C20', 'C25', 'C30', 'C35', 'C40']),
      pouringMethod: randomUtils.randomChoice(['泵送', '塔吊', '汽车泵', '地泵']),
      freezeResistance: randomUtils.randomChoice(['F50', 'F100', 'F150']),
      impermeability: randomUtils.randomChoice(['P6', 'P8', 'P10']),
      pumpTruck: randomUtils.randomChoice(['37米', '42米', '48米', '52米']),
      otherRequirements: randomUtils.randomBoolean(0.3) ? '需要早强剂' : '',
      contactPhone: `138${randomUtils.randomInt(10000000, 99999999)}`,
      publishDate: new Date().toISOString().split('T')[0],
      dispatchFrequencyMinutes: randomUtils.randomChoice([15, 30, 45, 60]),
      lastDispatchTime: '',
      nextScheduledDispatchTime: '',
      isDueForDispatch: false,
      minutesToDispatch: randomUtils.randomInt(0, 60),
      dispatchedVehicles: [],
      completedProgress: randomUtils.randomFloat(0, 100),
      deliveryStatus: randomUtils.randomChoice(['pending', 'in-transit', 'delivered']),
      dispatchReminderMinutes: randomUtils.randomChoice([15, 30, 45]),
      status: randomUtils.randomChoice(['New', 'InProgress', 'Completed']),
    } as any as Task;
  },

  /**
   * 批量生成任务
   */
  generateTasks: (count: number): Task[] => {
    return Array.from({ length: count }, (_, index) =>
      taskMockGenerator.generateTask(`task-${index + 1}`)
    );
  },
};

/**
 * 车辆模拟数据生成器
 */
export const vehicleMockGenerator = {
  /**
   * 生成单个车辆
   */
  generateVehicle: (id?: string): Vehicle => {
    const vehicleId = id || `vehicle-${randomUtils.randomString(8)}`;

    return {
      id: vehicleId,
      type: randomUtils.randomChoice(['Tanker', 'Pump', 'Other']),
      vehicleNumber: `V${randomUtils.randomInt(1000, 9999)}`,
      licensePlate: createCarNumber(),
      capacity: randomUtils.randomChoice([6, 8, 10, 12, 15]),
      capacityUnit: 'm³',
      status: randomUtils.randomChoice(['pending', 'outbound', 'returned']),
      operationalStatus: randomUtils.randomChoice(['normal', 'maintenance', 'repair']),
      driverName: randomUtils.randomChoice([
        '张师傅',
        '李师傅',
        '王师傅',
        '刘师傅',
        '陈师傅',
        '杨师傅',
      ]),
      driverPhone: `138${randomUtils.randomInt(10000000, 99999999)}`,
      assignedTaskId: randomUtils.randomBoolean(0.3)
        ? `task-${randomUtils.randomInt(1, 100)}`
        : undefined,
      currentLocation: randomUtils.randomChoice(['搅拌站', '配送途中', '工地现场', '返回途中']),
      mileage: randomUtils.randomInt(50000, 200000),
      lastMaintenanceDate: randomUtils
        .randomDate(new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), new Date())
        .toISOString()
        .split('T')[0],
      lastMaintenanceMileage: randomUtils.randomInt(40000, 180000),
      totalTrips: randomUtils.randomInt(100, 1000),
      totalWorkingHours: randomUtils.randomFloat(1000, 5000),
      lastActivityTime: randomUtils
        .randomDate(new Date(Date.now() - 24 * 60 * 60 * 1000), new Date())
        .toISOString(),
    } as Vehicle;
  },

  /**
   * 批量生成车辆
   */
  generateVehicles: (count: number): Vehicle[] => {
    return Array.from({ length: count }, (_, index) =>
      vehicleMockGenerator.generateVehicle(`vehicle-${index + 1}`)
    );
  },
};

/**
 * 料仓模拟数据生成器
 */
export const siloMockGenerator = {
  /**
   * 生成料仓映射数据
   */
  generateSiloMappings: (): SiloMapping[] => [
    {
      id: '1',
      siloType: '水泥',
      materialName: '水泥',
      spec: 'P.O 42.5',
      storageBin: '1#水泥仓',
      lastEntry: '2024-01-15 14:30:00',
      enabled: true,
    },
    {
      id: '2',
      siloType: '砂子',
      materialName: '砂子',
      spec: '中砂',
      storageBin: '1#砂仓',
      lastEntry: '2024-01-15 14:30:00',
      enabled: true,
    },
    {
      id: '3',
      siloType: '石子',
      materialName: '石子',
      spec: '5-25mm',
      storageBin: '1#石仓',
      lastEntry: '2024-01-15 14:30:00',
      enabled: true,
    },
    {
      id: '4',
      siloType: '水',
      materialName: '水',
      spec: '饮用水',
      storageBin: '水箱',
      lastEntry: '2024-01-15 14:30:00',
      enabled: true,
    },
    {
      id: '5',
      siloType: '外加剂',
      materialName: '聚羧酸减水剂',
      spec: 'PCE-40%',
      storageBin: '1#外加剂罐',
      lastEntry: '2024-01-15 14:30:00',
      enabled: true,
    },
    // 可以继续添加更多料仓...
  ],

  /**
   * 根据材料类型获取库存量
   */
  getStockAmountByType: (siloType: string): number => {
    const stockMap: Record<string, number> = {
      水泥: 150,
      砂子: 300,
      石子: 400,
      水: 1000,
      外加剂: 20,
      粉煤灰: 100,
      矿粉: 80,
    };
    return stockMap[siloType] || 100;
  },
};

/**
 * 配比历史模拟数据生成器
 */
export const ratioMockGenerator = {
  /**
   * 生成配比历史记录
   */
  generateRatioHistory: (taskId: string): RatioHistoryEntry[] => [
    {
      id: '1',
      taskId,
      editor: '张工程师',
      timestamp: '2024-01-15 14:30:00',
      price: 285.5,
      materials: {
        cement: 372,
        flyAsh: 0,
        mineralPowder: 0,
        sand: 593,
        stone: 1260,
        water: 175,
        admixture: 3.72,
      },
    },
    {
      id: '2',
      taskId,
      editor: '李技术员',
      timestamp: '2024-01-15 16:45:00',
      price: 288.2,
      materials: {
        cement: 375,
        flyAsh: 0,
        mineralPowder: 0,
        sand: 590,
        stone: 1255,
        water: 178,
        admixture: 3.75,
      },
    },
  ],

  /**
   * 生成AI配比生成器的可用材料列表
   */
  generateAvailableMaterials: () => {
    return siloMockGenerator.generateSiloMappings().map(silo => ({
      id: silo.id,
      name: silo.materialName,
      type: silo.siloType,
      specification: silo.spec,
      available: silo.enabled,
      amount: siloMockGenerator.getStockAmountByType(silo.siloType),
    }));
  },
};

/**
 * 综合模拟数据生成器
 */
export const mockDataGenerator = {
  /**
   * 生成完整的模拟数据集
   */
  generateCompleteDataSet: (
    options: {
      taskCount?: number;
      vehicleCount?: number;
      includeRatioData?: boolean;
    } = {}
  ) => {
    const { taskCount = 50, vehicleCount = 20, includeRatioData = true } = options;

    const tasks = taskMockGenerator.generateTasks(taskCount);
    const vehicles = vehicleMockGenerator.generateVehicles(vehicleCount);
    const siloMappings = siloMockGenerator.generateSiloMappings();

    const result: any = {
      tasks,
      vehicles,
      siloMappings,
    };

    if (includeRatioData) {
      result.ratioHistory = tasks
        .map(task => ratioMockGenerator.generateRatioHistory(task.id))
        .flat();
      result.availableMaterials = ratioMockGenerator.generateAvailableMaterials();
    }

    return result;
  },

  // 导出各个生成器
  tasks: taskMockGenerator,
  vehicles: vehicleMockGenerator,
  silos: siloMockGenerator,
  ratios: ratioMockGenerator,
  random: randomUtils,
};
