'use client';

/**
 * 现代化配比计算引擎
 * 基于混凝土配比设计规范和工程实践经验
 */

interface CalculationParams {
  density: number;
  waterRatio: number;
  waterAmount: number;
  sandRatio: number;
  additiveRatio: number;
  flyashRatio: number;
  antifreezeRatio: number;
  mineralPowderRatio?: number;
  s105Ratio?: number;
  expansionRatio?: number;
  earlyStrengthRatio?: number;
  ultraFineSandRatio?: number;
  calculationMethod?: string;
  cementSubstitutionRate?: number;
  flyAshFactor?: number;
  flyAshDensity?: number;
  sandDensity?: number;
  flyAshCementFactor?: number;
  silicaFumeCementFactor?: number;
}

// 导入统一的类型定义
import type { RatioMaterial, CalculationResults, OptimizationAction } from '@/core/types/ratio';

// CalculationResults 类型已迁移到 src/types/ratio.ts

// 本地扩展的优化操作接口
interface LocalOptimizationAction {
  id: string;
  type: 'ratio_adjustment' | 'material_substitution' | 'additive_optimization';
  description: string;
  parameterName?: string;
  materialName?: string;
  currentValue: number;
  suggestedValue: number;
  reason: string;
  impact: string;
  priority: 'high' | 'medium' | 'low';
}

export class ModernCalculationEngine {
  /**
   * 主计算方法 - 正向计算
   */
  calculate(params: CalculationParams, materials: RatioMaterial[]): CalculationResults {
    try {
      // 基础参数验证
      this.validateParams(params);

      // 如果没有材料，使用参数计算理论配比
      const calculatedMaterials =
        materials.length > 0
          ? this.calculateFromMaterials(params, materials)
          : this.calculateMaterialAmounts(params, materials);

      // 计算总重量
      const totalWeight = this.calculateTotalWeight(calculatedMaterials);

      // 强度预测
      const strengthPrediction = this.predictStrength(params, calculatedMaterials);

      // 质量评分
      const qualityAnalysis = this.analyzeQuality(params, calculatedMaterials, strengthPrediction);

      // 环保和成本分析
      const carbonFootprint = this.calculateCarbonFootprint(calculatedMaterials);
      const costEstimate = this.estimateCost(calculatedMaterials);

      // 生成优化建议
      const optimizations = this.generateOptimizations(
        params,
        calculatedMaterials,
        strengthPrediction,
        qualityAnalysis.score
      );

      return {
        totalWeight,
        materials: this.formatMaterialsOutput(calculatedMaterials),
        strengthPrediction,
        qualityScore: qualityAnalysis.score,
        warnings: qualityAnalysis.warnings,
        suggestions: qualityAnalysis.suggestions,
        carbonFootprint,
        costEstimate,
        optimizations: optimizations.map(
          opt =>
            ({
              type: opt.type,
              description: opt.description,
              impact: {
                cost: opt.priority === 'high' ? -50 : opt.priority === 'medium' ? -20 : -10,
              },
              confidence: opt.priority === 'high' ? 0.9 : opt.priority === 'medium' ? 0.7 : 0.5,
            }) as OptimizationAction
        ),
      };
    } catch (error) {
      console.error('计算过程出错:', error);
      // 返回默认结果而不是抛出错误
      return {
        totalWeight: params.density * 1000,
        materials: {},
        strengthPrediction: 25,
        qualityScore: 70,
        warnings: ['计算过程中出现问题，显示默认结果'],
        suggestions: ['请检查输入参数并重新计算'],
        carbonFootprint: 0,
        costEstimate: 0,
      };
    }
  }

  /**
   * 反算方法 - 根据配比反推参数
   */
  reverseCalculate(
    materials: RatioMaterial[],
    targetResults: CalculationResults,
    calculationMethod: string = 'method1'
  ): CalculationParams {
    const totalWeight = targetResults.totalWeight;

    // 提取各材料用量
    const waterAmount = this.extractMaterialAmount(materials, '水') || 180;
    const cementAmount = this.extractMaterialAmount(materials, '水泥') || 400;
    const flyashAmount = this.extractMaterialAmount(materials, '粉煤灰') || 0;
    const mineralPowderAmount = this.extractMaterialAmount(materials, '矿粉') || 0;
    const sandAmount = this.extractMaterialAmount(materials, '砂') || 650;
    const stoneAmount = this.extractMaterialAmount(materials, '石') || 1100;
    const additiveAmount = this.extractMaterialAmount(materials, '外加剂') || 5;
    const s105Amount = this.extractMaterialAmount(materials, 'S105') || 0;
    const expansionAmount = this.extractMaterialAmount(materials, '膨胀剂') || 0;
    const earlyStrengthAmount = this.extractMaterialAmount(materials, '早强剂') || 0;
    const ultraFineSandAmount = this.extractMaterialAmount(materials, '超细砂') || 0;

    // 基础参数计算
    const density = totalWeight / 1000; // 转换为 t/m³
    const totalBinder = cementAmount + flyashAmount + mineralPowderAmount;
    const waterRatio = waterAmount / totalBinder;
    const sandRatio = (sandAmount / (sandAmount + stoneAmount)) * 100;
    const additiveRatio = (additiveAmount / totalBinder) * 100;
    const flyashRatio = (flyashAmount / cementAmount) * 100;
    const mineralPowderRatio = (mineralPowderAmount / cementAmount) * 100;
    const s105Ratio = (s105Amount / totalBinder) * 100;
    const expansionRatio = (expansionAmount / totalBinder) * 100;
    const earlyStrengthRatio = (earlyStrengthAmount / totalBinder) * 100;
    const ultraFineSandRatio = (ultraFineSandAmount / (sandAmount + ultraFineSandAmount)) * 100;

    // 根据计算方法设置特定参数
    let additionalParams: Partial<CalculationParams> = {};

    switch (calculationMethod) {
      case 'method1': // 不考虑粉煤灰超量系数
        additionalParams = {
          calculationMethod: 'method1',
          cementSubstitutionRate: flyashRatio,
        };
        break;

      case 'method2': // 考虑粉煤灰系数
        additionalParams = {
          calculationMethod: 'method2',
          cementSubstitutionRate: flyashRatio,
          flyAshFactor: 0.7, // 粉煤灰活性系数
        };
        break;

      case 'method3': // 考虑粉煤灰系数且体积折算
        additionalParams = {
          calculationMethod: 'method3',
          flyAshDensity: 2.2,
          sandDensity: 2.65,
          flyAshCementFactor: 0.7,
          silicaFumeCementFactor: 2.0,
        };
        break;

      case 'method4': // 超量煤灰
        additionalParams = {
          calculationMethod: 'method4',
          flyAshDensity: 2.2,
          sandDensity: 2.65,
          flyAshCementFactor: 0.8,
          silicaFumeCementFactor: 2.0,
        };
        break;
    }

    return {
      density: Math.round(density * 100) / 100,
      waterRatio: Math.round(waterRatio * 100) / 100,
      waterAmount: Math.round(waterAmount),
      sandRatio: Math.round(sandRatio * 10) / 10,
      additiveRatio: Math.round(additiveRatio * 10) / 10,
      flyashRatio: Math.round(flyashRatio * 10) / 10,
      antifreezeRatio: 0,
      mineralPowderRatio: Math.round(mineralPowderRatio * 10) / 10,
      s105Ratio: Math.round(s105Ratio * 10) / 10,
      expansionRatio: Math.round(expansionRatio * 10) / 10,
      earlyStrengthRatio: Math.round(earlyStrengthRatio * 10) / 10,
      ultraFineSandRatio: Math.round(ultraFineSandRatio * 10) / 10,
      ...additionalParams,
    };
  }

  /**
   * 参数验证
   */
  private validateParams(params: CalculationParams): void {
    const warnings: string[] = [];

    // 使用警告而不是错误，允许计算继续进行
    if (params.density < 2.0 || params.density > 3.0) {
      warnings.push('密度超出推荐范围(2.0-3.0 t/m³)');
    }
    if (params.waterRatio < 0.2 || params.waterRatio > 0.8) {
      warnings.push('水胶比超出推荐范围(0.2-0.8)');
    }
    if (params.waterAmount < 100 || params.waterAmount > 300) {
      warnings.push('用水量超出推荐范围(100-300 kg/m³)');
    }
    if (params.sandRatio < 20 || params.sandRatio > 60) {
      warnings.push('砂率超出推荐范围(20-60%)');
    }

    // 只在控制台输出警告，不阻止计算
    if (warnings.length > 0) {
      console.warn('参数警告:', warnings.join(', '));
    }
  }

  /**
   * 基于现有材料计算
   */
  private calculateFromMaterials(
    params: CalculationParams,
    materials: RatioMaterial[]
  ): Record<string, number> {
    const results: Record<string, number> = {};

    // 从现有材料中提取数据
    materials.forEach(material => {
      // 计算实际用量（考虑含水率等）
      let actualAmount = material.theoreticalAmount;

      // 如果有含水率，调整实际用量
      if (material.waterContent > 0) {
        actualAmount = material.theoreticalAmount * (1 + material.waterContent / 100);
      }

      // 更新材料的实际量和设计值
      material.actualAmount = actualAmount;
      material.designValue = material.theoreticalAmount;

      // 添加到结果中
      results[material.name] = material.theoreticalAmount;
    });

    return results;
  }

  /**
   * 计算各材料用量（理论计算）
   */
  private calculateMaterialAmounts(
    params: CalculationParams,
    materials: RatioMaterial[]
  ): Record<string, number> {
    const results: Record<string, number> = {};

    // 基础计算
    const totalWeight = params.density * 1000; // kg/m³
    const waterAmount = params.waterAmount;

    // 胶凝材料计算
    const totalBinderRatio =
      1 + (params.flyashRatio || 0) / 100 + (params.mineralPowderRatio || 0) / 100;
    const cementAmount = waterAmount / params.waterRatio / totalBinderRatio;
    const flyashAmount = (cementAmount * (params.flyashRatio || 0)) / 100;
    const mineralPowderAmount = (cementAmount * (params.mineralPowderRatio || 0)) / 100;

    // 骨料计算
    const totalBinder = cementAmount + flyashAmount + mineralPowderAmount;
    const aggregateTotal = totalWeight - waterAmount - totalBinder;
    const sandAmount = aggregateTotal * (params.sandRatio / 100);
    const stoneAmount = aggregateTotal - sandAmount;

    // 外加剂计算
    const additiveAmount = totalBinder * (params.additiveRatio / 100);
    const antifreezeAmount = (totalBinder * (params.antifreezeRatio || 0)) / 100;
    const s105Amount = (totalBinder * (params.s105Ratio || 0)) / 100;
    const expansionAmount = (totalBinder * (params.expansionRatio || 0)) / 100;
    const earlyStrengthAmount = (totalBinder * (params.earlyStrengthRatio || 0)) / 100;

    // 填充结果
    results['水'] = waterAmount;
    results['水泥'] = cementAmount;
    if (flyashAmount > 0) results['粉煤灰'] = flyashAmount;
    if (mineralPowderAmount > 0) results['矿粉'] = mineralPowderAmount;
    results['砂'] = sandAmount;
    results['石子'] = stoneAmount;
    if (additiveAmount > 0) results['外加剂'] = additiveAmount;
    if (antifreezeAmount > 0) results['防冻剂'] = antifreezeAmount;
    if (s105Amount > 0) results['S105'] = s105Amount;
    if (expansionAmount > 0) results['膨胀剂'] = expansionAmount;
    if (earlyStrengthAmount > 0) results['早强剂'] = earlyStrengthAmount;

    return results;
  }

  /**
   * 计算总重量
   */
  private calculateTotalWeight(materials: Record<string, number>): number {
    return Object.values(materials).reduce((sum, amount) => sum + amount, 0);
  }

  /**
   * 强度预测 (基于简化的Abrams定律)
   */
  private predictStrength(params: CalculationParams, materials: Record<string, number>): number {
    const waterAmount = materials['水'] || params.waterAmount || 180;
    const cementAmount = materials['水泥'] || 400;
    const flyashAmount = materials['粉煤灰'] || 0;
    const mineralPowderAmount = materials['矿粉'] || 0;

    // 防止除零错误
    const totalBinder = cementAmount + flyashAmount * 0.7 + mineralPowderAmount * 0.8;
    if (totalBinder === 0) {
      return 25; // 返回默认强度
    }

    // 有效水胶比
    const effectiveWaterCementRatio = waterAmount / totalBinder;

    // 强度预测公式 (简化)
    const baseStrength = cementAmount * 0.8 + flyashAmount * 0.5 + mineralPowderAmount * 0.6;
    const strengthPrediction = Math.max((baseStrength / effectiveWaterCementRatio) * 0.15, 10);

    return Math.round(Math.min(strengthPrediction, 80) * 10) / 10; // 限制在合理范围内
  }

  /**
   * 质量分析
   */
  private analyzeQuality(
    params: CalculationParams,
    materials: Record<string, number>,
    strengthPrediction: number
  ): { score: number; warnings: string[]; suggestions: string[] } {
    let score = 100;
    const warnings: string[] = [];
    const suggestions: string[] = [];

    const waterAmount = materials['水'] || 0;
    const cementAmount = materials['水泥'] || 0;
    const flyashAmount = materials['粉煤灰'] || 0;
    const sandAmount = materials['砂'] || 0;
    const stoneAmount = materials['石子'] || 0;
    const additiveAmount = materials['外加剂'] || 0;

    // 水胶比检查
    if (params.waterRatio > 0.6) {
      score -= 20;
      warnings.push('水胶比过高，可能影响强度和耐久性');
      const reduceWater = Math.round((waterAmount - (cementAmount + flyashAmount) * 0.6) * 10) / 10;
      if (reduceWater > 0) {
        suggestions.push(
          `建议减少用水量 ${reduceWater}kg，或增加胶凝材料用量 ${Math.round(reduceWater / 0.6)}kg`
        );
      }
    } else if (params.waterRatio < 0.35) {
      score -= 10;
      warnings.push('水胶比过低，可能影响工作性');
      const increaseWater =
        Math.round(((cementAmount + flyashAmount) * 0.35 - waterAmount) * 10) / 10;
      if (increaseWater > 0) {
        suggestions.push(
          `建议增加用水量 ${increaseWater}kg，或增加外加剂用量 ${Math.round(increaseWater * 0.01)}kg`
        );
      }
    }

    // 砂率检查
    if (params.sandRatio < 30) {
      score -= 10;
      warnings.push('砂率过低，可能影响和易性');
      const increaseSand = Math.round((sandAmount + stoneAmount) * 0.3 - sandAmount);
      const decreaseStone = Math.round(sandAmount + stoneAmount - (sandAmount + increaseSand));
      suggestions.push(`建议增加砂子用量 ${increaseSand}kg，减少石子用量 ${decreaseStone}kg`);
    } else if (params.sandRatio > 45) {
      score -= 10;
      warnings.push('砂率过高，可能影响强度');
      const decreaseSand = Math.round(sandAmount - (sandAmount + stoneAmount) * 0.4);
      const increaseStone = decreaseSand;
      suggestions.push(`建议减少砂子用量 ${decreaseSand}kg，增加石子用量 ${increaseStone}kg`);
    }

    // 外加剂掺量检查
    if (params.additiveRatio < 0.8) {
      score -= 15;
      warnings.push('外加剂掺量过低，可能影响工作性');
      const increaseAdditive =
        Math.round((cementAmount + flyashAmount) * 0.008 - additiveAmount * 10) / 10;
      if (increaseAdditive > 0) {
        suggestions.push(`建议增加外加剂用量 ${increaseAdditive}kg`);
      }
    } else if (params.additiveRatio > 2.5) {
      score -= 15;
      warnings.push('外加剂掺量过高，可能影响凝结时间');
      const decreaseAdditive =
        Math.round((additiveAmount - (cementAmount + flyashAmount) * 0.025) * 10) / 10;
      if (decreaseAdditive > 0) {
        suggestions.push(`建议减少外加剂用量 ${decreaseAdditive}kg`);
      }
    }

    // 粉煤灰掺量检查
    if (params.flyashRatio > 30) {
      score -= 10;
      warnings.push('粉煤灰掺量过高，可能影响早期强度');
      const decreaseFlyash = Math.round((flyashAmount - cementAmount * 0.3) * 10) / 10;
      const increaseCement = decreaseFlyash;
      suggestions.push(`建议减少粉煤灰用量 ${decreaseFlyash}kg，增加水泥用量 ${increaseCement}kg`);
    }

    // 强度预测检查
    const targetStrength = 25; // 假设目标强度
    if (strengthPrediction < targetStrength * 0.9) {
      score -= 25;
      warnings.push('预测强度偏低');
      const strengthDeficit = targetStrength - strengthPrediction;
      const increaseCement = Math.round(strengthDeficit * 8);
      const decreaseWater = Math.round(strengthDeficit * 3);
      suggestions.push(`建议增加水泥用量 ${increaseCement}kg，或减少用水量 ${decreaseWater}kg`);
    } else if (strengthPrediction > targetStrength * 1.2) {
      score -= 10;
      warnings.push('预测强度过高，成本偏高');
      const strengthExcess = strengthPrediction - targetStrength;
      const decreaseCement = Math.round(strengthExcess * 5);
      const increaseFlyash = Math.round(strengthExcess * 3);
      suggestions.push(`建议减少水泥用量 ${decreaseCement}kg，增加粉煤灰用量 ${increaseFlyash}kg`);
    }

    // 工作性建议
    if (params.waterAmount < 160) {
      const increaseWater = 160 - params.waterAmount;
      const increaseAdditive = Math.round(increaseWater * 0.005 * 10) / 10;
      suggestions.push(
        `用水量较低，建议增加用水量 ${increaseWater}kg 或增加外加剂用量 ${increaseAdditive}kg`
      );
    }

    // 经济性建议
    if (params.flyashRatio === 0 && cementAmount > 350) {
      const replaceCement = Math.round(cementAmount * 0.15);
      suggestions.push(
        `建议用 ${replaceCement}kg 粉煤灰替代部分水泥，可降低成本约 ${Math.round(replaceCement * 0.3)}元/m³`
      );
    }

    // 耐久性建议
    if (params.waterRatio > 0.5 && params.flyashRatio < 10) {
      suggestions.push(`建议增加粉煤灰掺量至15-20%，可提高耐久性并降低水化热`);
    }

    return {
      score: Math.max(0, score),
      warnings,
      suggestions,
    };
  }

  /**
   * 碳足迹计算
   */
  private calculateCarbonFootprint(materials: Record<string, number>): number {
    const cementAmount = materials['水泥'] || 0;
    const flyashAmount = materials['粉煤灰'] || 0;

    // 简化的碳足迹计算 (kg CO2/m³)
    const carbonFootprint = (cementAmount * 0.85 + flyashAmount * 0.1) * 0.001;

    return Math.round(carbonFootprint * 100) / 100;
  }

  /**
   * 成本估算
   */
  private estimateCost(materials: Record<string, number>): number {
    // 简化的材料单价 (元/kg)
    const prices = {
      水泥: 0.45,
      粉煤灰: 0.15,
      砂: 0.08,
      石子: 0.06,
      外加剂: 8.5,
      水: 0.005,
      防冻剂: 12.0,
    };

    let totalCost = 0;
    Object.entries(materials).forEach(([material, amount]) => {
      const price = prices[material as keyof typeof prices] || 0;
      totalCost += amount * price;
    });

    return Math.round(totalCost * 100) / 100;
  }

  /**
   * 提取特定材料用量
   */
  private extractMaterialAmount(materials: RatioMaterial[], materialName: string): number {
    const material = materials.find(m => m.name.includes(materialName));
    return material ? material.designValue : 0;
  }

  /**
   * 格式化材料输出
   */
  private formatMaterialsOutput(materials: Record<string, number>): Record<string, number> {
    const formatted: Record<string, number> = {};
    Object.entries(materials).forEach(([name, amount]) => {
      formatted[name] = Math.round(amount * 100) / 100;
    });
    return formatted;
  }

  /**
   * 生成优化建议
   */
  private generateOptimizations(
    params: CalculationParams,
    materials: Record<string, number>,
    strengthPrediction: number,
    qualityScore: number
  ): LocalOptimizationAction[] {
    const optimizations: LocalOptimizationAction[] = [];

    const waterAmount = materials['水'] || params.waterAmount || 180;
    const cementAmount = materials['水泥'] || 400;
    const flyashAmount = materials['粉煤灰'] || 0;
    const mineralPowderAmount = materials['矿粉'] || 0;
    const sandAmount = materials['砂'] || 650;
    const stoneAmount = materials['石子'] || 1100;

    // 1. 水胶比优化
    if (params.waterRatio > 0.6) {
      const targetWaterRatio = 0.55;
      const totalBinder = cementAmount + flyashAmount + mineralPowderAmount;
      const newWaterAmount = totalBinder * targetWaterRatio;

      optimizations.push({
        id: 'water-ratio-1',
        type: 'ratio_adjustment',
        description: `降低水胶比从 ${params.waterRatio.toFixed(2)} 到 ${targetWaterRatio}`,
        parameterName: 'waterRatio',
        currentValue: params.waterRatio,
        suggestedValue: targetWaterRatio,
        reason: '当前水胶比过高，影响强度和耐久性',
        impact: '提高强度约 3-5 MPa，改善耐久性',
        priority: 'high',
      });

      optimizations.push({
        id: 'water-amount-1',
        type: 'ratio_adjustment',
        description: `调整用水量从 ${waterAmount.toFixed(0)}kg 到 ${newWaterAmount.toFixed(0)}kg`,
        parameterName: 'waterAmount',
        currentValue: waterAmount,
        suggestedValue: Math.round(newWaterAmount),
        reason: '配合水胶比调整',
        impact: '保持工作性的同时提高强度',
        priority: 'high',
      });
    } else if (params.waterRatio < 0.35) {
      const targetWaterRatio = 0.38;
      optimizations.push({
        id: 'water-ratio-2',
        type: 'ratio_adjustment',
        description: `提高水胶比从 ${params.waterRatio.toFixed(2)} 到 ${targetWaterRatio}`,
        parameterName: 'waterRatio',
        currentValue: params.waterRatio,
        suggestedValue: targetWaterRatio,
        reason: '当前水胶比过低，可能影响工作性',
        impact: '改善工作性，便于施工',
        priority: 'medium',
      });
    }

    // 2. 砂率优化
    if (params.sandRatio < 30) {
      const targetSandRatio = 35;
      const totalAggregate = sandAmount + stoneAmount;
      const newSandAmount = (totalAggregate * targetSandRatio) / 100;

      optimizations.push({
        id: 'sand-ratio-1',
        type: 'ratio_adjustment',
        description: `提高砂率从 ${params.sandRatio.toFixed(1)}% 到 ${targetSandRatio}%`,
        parameterName: 'sandRatio',
        currentValue: params.sandRatio,
        suggestedValue: targetSandRatio,
        reason: '砂率过低，影响和易性',
        impact: '改善混凝土和易性和可泵性',
        priority: 'medium',
      });
    } else if (params.sandRatio > 45) {
      const targetSandRatio = 40;
      optimizations.push({
        id: 'sand-ratio-2',
        type: 'ratio_adjustment',
        description: `降低砂率从 ${params.sandRatio.toFixed(1)}% 到 ${targetSandRatio}%`,
        parameterName: 'sandRatio',
        currentValue: params.sandRatio,
        suggestedValue: targetSandRatio,
        reason: '砂率过高，可能影响强度',
        impact: '提高强度，降低收缩',
        priority: 'medium',
      });
    }

    // 3. 外加剂优化
    if (params.additiveRatio < 0.8) {
      const targetAdditiveRatio = 1.0;
      optimizations.push({
        id: 'additive-1',
        type: 'additive_optimization',
        description: `增加外加剂掺量从 ${params.additiveRatio.toFixed(1)}% 到 ${targetAdditiveRatio}%`,
        parameterName: 'additiveRatio',
        currentValue: params.additiveRatio,
        suggestedValue: targetAdditiveRatio,
        reason: '外加剂掺量不足，影响工作性',
        impact: '改善工作性，可能降低用水量',
        priority: 'medium',
      });
    } else if (params.additiveRatio > 2.5) {
      const targetAdditiveRatio = 2.0;
      optimizations.push({
        id: 'additive-2',
        type: 'additive_optimization',
        description: `降低外加剂掺量从 ${params.additiveRatio.toFixed(1)}% 到 ${targetAdditiveRatio}%`,
        parameterName: 'additiveRatio',
        currentValue: params.additiveRatio,
        suggestedValue: targetAdditiveRatio,
        reason: '外加剂掺量过高，可能影响凝结时间',
        impact: '避免缓凝，确保正常凝结',
        priority: 'high',
      });
    }

    // 4. 粉煤灰优化
    if (params.flyashRatio === 0 && cementAmount > 350) {
      const targetFlyashRatio = 15;
      optimizations.push({
        id: 'flyash-1',
        type: 'additive_optimization',
        description: `添加粉煤灰掺量 ${targetFlyashRatio}%`,
        parameterName: 'flyashRatio',
        currentValue: params.flyashRatio,
        suggestedValue: targetFlyashRatio,
        reason: '可以降低成本并改善工作性',
        impact: '降低成本约 15-20元/m³，改善工作性',
        priority: 'low',
      });
    } else if (params.flyashRatio > 30) {
      const targetFlyashRatio = 25;
      optimizations.push({
        id: 'flyash-2',
        type: 'additive_optimization',
        description: `降低粉煤灰掺量从 ${params.flyashRatio.toFixed(1)}% 到 ${targetFlyashRatio}%`,
        parameterName: 'flyashRatio',
        currentValue: params.flyashRatio,
        suggestedValue: targetFlyashRatio,
        reason: '粉煤灰掺量过高，影响早期强度',
        impact: '提高早期强度发展',
        priority: 'medium',
      });
    }

    // 5. 强度优化
    const targetStrength = 25; // 假设目标强度
    if (strengthPrediction < targetStrength * 0.9) {
      const strengthDeficit = targetStrength - strengthPrediction;
      const cementIncrease = Math.round(strengthDeficit * 8);

      optimizations.push({
        id: 'strength-1',
        type: 'material_substitution',
        description: `增加水泥用量 ${cementIncrease}kg 以提高强度`,
        materialName: '水泥',
        currentValue: cementAmount,
        suggestedValue: cementAmount + cementIncrease,
        reason: `预测强度 ${strengthPrediction.toFixed(1)}MPa 低于目标强度 ${targetStrength}MPa`,
        impact: `提高强度约 ${strengthDeficit.toFixed(1)}MPa`,
        priority: 'high',
      });
    }

    // 6. 成本优化
    if (qualityScore > 85 && cementAmount > 400) {
      const cementReduction = Math.round(cementAmount * 0.1);
      const flyashIncrease = Math.round(cementReduction * 0.8);

      optimizations.push({
        id: 'cost-1',
        type: 'material_substitution',
        description: `用 ${flyashIncrease}kg 粉煤灰替代 ${cementReduction}kg 水泥`,
        materialName: '水泥',
        currentValue: cementAmount,
        suggestedValue: cementAmount - cementReduction,
        reason: '质量评分较高，可以适当降低成本',
        impact: `降低成本约 ${Math.round(cementReduction * 0.3)}元/m³`,
        priority: 'low',
      });
    }

    return optimizations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * 应用优化建议
   */
  applyOptimizations(
    currentParams: CalculationParams,
    currentMaterials: RatioMaterial[],
    selectedOptimizations: string[]
  ): { params: CalculationParams; materials: RatioMaterial[] } {
    // 简化实现：直接返回当前参数和材料
    // 实际应用中可以根据优化建议调整参数
    let newParams = { ...currentParams };
    let newMaterials = [...currentMaterials];

    // 这里可以根据 selectedOptimizations 应用具体的优化逻辑
    // 目前返回原始数据
    return { params: newParams, materials: newMaterials };
  }
}
