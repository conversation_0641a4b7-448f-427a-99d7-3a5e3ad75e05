/**
 * 配比V1页面UI状态管理
 * 专门为经典V1配比页面设计的简化UI状态管理
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// 配比V1页面UI状态接口
export interface RatioV1UIState {
  // 模态框状态
  activeModal: string | null;

  // 加载状态
  isLoading: boolean;
  isCalculating: boolean;
  isSaving: boolean;

  // 错误状态
  error: string | null;

  // 表单状态
  isDirty: boolean;
  isValid: boolean;

  // 显示状态
  showAdvancedOptions: boolean;
  showCalculationDetails: boolean;
}

// 配比V1页面UI操作接口
export interface RatioV1UIActions {
  // 模态框操作
  setActiveModal: (modal: string | null) => void;
  openModal: (modal: string) => void;
  closeModal: () => void;

  // 加载状态操作
  setLoading: (loading: boolean) => void;
  setCalculating: (calculating: boolean) => void;
  setSaving: (saving: boolean) => void;

  // 错误状态操作
  setError: (error: string | null) => void;
  clearError: () => void;

  // 表单状态操作
  setDirty: (dirty: boolean) => void;
  setValid: (valid: boolean) => void;

  // 显示状态操作
  setShowAdvancedOptions: (show: boolean) => void;
  setShowCalculationDetails: (show: boolean) => void;

  // 重置状态
  reset: () => void;
}

// 初始状态
const initialState: RatioV1UIState = {
  // 模态框状态
  activeModal: null,

  // 加载状态
  isLoading: false,
  isCalculating: false,
  isSaving: false,

  // 错误状态
  error: null,

  // 表单状态
  isDirty: false,
  isValid: true,

  // 显示状态
  showAdvancedOptions: false,
  showCalculationDetails: false,
};

// 创建配比V1页面UI状态Store
export const useRatioV1UIStore = create<RatioV1UIState & RatioV1UIActions>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialState,

      // 模态框操作
      setActiveModal: (modal: string | null) => {
        set(state => {
          state.activeModal = modal;
        });
      },

      openModal: (modal: string) => {
        set(state => {
          state.activeModal = modal;
        });
      },

      closeModal: () => {
        set(state => {
          state.activeModal = null;
        });
      },

      // 加载状态操作
      setLoading: (loading: boolean) => {
        set(state => {
          state.isLoading = loading;
        });
      },

      setCalculating: (calculating: boolean) => {
        set(state => {
          state.isCalculating = calculating;
        });
      },

      setSaving: (saving: boolean) => {
        set(state => {
          state.isSaving = saving;
        });
      },

      // 错误状态操作
      setError: (error: string | null) => {
        set(state => {
          state.error = error;
        });
      },

      clearError: () => {
        set(state => {
          state.error = null;
        });
      },

      // 表单状态操作
      setDirty: (dirty: boolean) => {
        set(state => {
          state.isDirty = dirty;
        });
      },

      setValid: (valid: boolean) => {
        set(state => {
          state.isValid = valid;
        });
      },

      // 显示状态操作
      setShowAdvancedOptions: (show: boolean) => {
        set(state => {
          state.showAdvancedOptions = show;
        });
      },

      setShowCalculationDetails: (show: boolean) => {
        set(state => {
          state.showCalculationDetails = show;
        });
      },

      // 重置状态
      reset: () => {
        set(state => {
          Object.assign(state, initialState);
        });
      },
    }))
  )
);

// 导出选择器
export const ratioV1UISelectors = {
  activeModal: (state: RatioV1UIState & RatioV1UIActions) => state.activeModal,
  isLoading: (state: RatioV1UIState & RatioV1UIActions) => state.isLoading,
  isCalculating: (state: RatioV1UIState & RatioV1UIActions) => state.isCalculating,
  isSaving: (state: RatioV1UIState & RatioV1UIActions) => state.isSaving,
  error: (state: RatioV1UIState & RatioV1UIActions) => state.error,
  isDirty: (state: RatioV1UIState & RatioV1UIActions) => state.isDirty,
  isValid: (state: RatioV1UIState & RatioV1UIActions) => state.isValid,
  showAdvancedOptions: (state: RatioV1UIState & RatioV1UIActions) => state.showAdvancedOptions,
  showCalculationDetails: (state: RatioV1UIState & RatioV1UIActions) =>
    state.showCalculationDetails,
};

// 导出组合选择器
export const ratioV1UIComposedSelectors = {
  // 是否有任何加载状态
  isAnyLoading: (state: RatioV1UIState & RatioV1UIActions) =>
    state.isLoading || state.isCalculating || state.isSaving,

  // 是否可以进行操作
  canOperate: (state: RatioV1UIState & RatioV1UIActions) =>
    !state.isLoading && !state.isCalculating && !state.isSaving,

  // 是否有错误
  hasError: (state: RatioV1UIState & RatioV1UIActions) => !!state.error,

  // 是否有模态框打开
  hasModalOpen: (state: RatioV1UIState & RatioV1UIActions) => !!state.activeModal,

  // 表单状态摘要
  formStatus: (state: RatioV1UIState & RatioV1UIActions) => ({
    isDirty: state.isDirty,
    isValid: state.isValid,
    canSave: state.isDirty && state.isValid && !state.isSaving,
    canSubmit: state.isValid && !state.isCalculating,
  }),
};
