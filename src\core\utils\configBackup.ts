/**
 * 配置备份和恢复工具
 * 提供配置的导入导出、备份恢复功能
 */

import { PersistenceManager } from '../../infrastructure/storage/persistence/persistenceManager';
import { safeDownloadFile } from '@/core/utils/dom-safe-operations';

export interface ConfigBackup {
  version: string;
  timestamp: string;
  userAgent: string;
  configs: Record<string, any>;
  metadata: {
    totalSize: number;
    itemCount: number;
    description?: string;
  };
}

/**
 * 配置备份工具类
 */
export class ConfigBackupManager {
  private static readonly BACKUP_VERSION = '1.0.0';

  /**
   * 创建完整的配置备份
   */
  static createFullBackup(description?: string): ConfigBackup {
    const configs = PersistenceManager.exportAll();
    const storageInfo = PersistenceManager.getStorageInfo();

    return {
      version: this.BACKUP_VERSION,
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Unknown',
      configs,
      metadata: {
        totalSize: storageInfo.totalSize,
        itemCount: storageInfo.itemCount,
        description,
      },
    };
  }

  /**
   * 导出配置到文件
   */
  static exportToFile(backup?: ConfigBackup, filename?: string): void {
    const configBackup = backup || this.createFullBackup();
    const dataStr = JSON.stringify(configBackup, null, 2);
    // 使用安全的下载函数
    safeDownloadFile(
      dataStr,
      filename || `tmh-config-backup-${new Date().toISOString().split('T')[0]}.json`,
      'application/json'
    );
  }

  /**
   * 从文件导入配置
   */
  static importFromFile(): Promise<ConfigBackup> {
    return new Promise((resolve, reject) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';

      input.onchange = event => {
        const file = (event.target as HTMLInputElement).files?.[0];
        if (!file) {
          reject(new Error('未选择文件'));
          return;
        }

        const reader = new FileReader();
        reader.onload = e => {
          try {
            const content = e.target?.result as string;
            const backup = JSON.parse(content) as ConfigBackup;

            if (!this.validateBackup(backup)) {
              reject(new Error('备份文件格式无效'));
              return;
            }

            resolve(backup);
          } catch (error) {
            reject(new Error('文件解析失败'));
          }
        };

        reader.onerror = () => reject(new Error('文件读取失败'));
        reader.readAsText(file);
      };

      input.click();
    });
  }

  /**
   * 恢复配置备份
   */
  static restoreBackup(backup: ConfigBackup): boolean {
    if (!this.validateBackup(backup)) {
      console.error('备份文件格式无效');
      return false;
    }

    try {
      const success = PersistenceManager.importAll(backup.configs);
      if (success) {
        console.log('配置备份恢复成功');
        return true;
      } else {
        console.error('部分配置恢复失败');
        return false;
      }
    } catch (error) {
      console.error('配置恢复失败:', error);
      return false;
    }
  }

  /**
   * 验证备份文件格式
   */
  static validateBackup(backup: any): backup is ConfigBackup {
    return (
      backup &&
      typeof backup === 'object' &&
      typeof backup.version === 'string' &&
      typeof backup.timestamp === 'string' &&
      typeof backup.configs === 'object' &&
      typeof backup.metadata === 'object'
    );
  }

  /**
   * 比较两个备份的差异
   */
  static compareBackups(
    backup1: ConfigBackup,
    backup2: ConfigBackup
  ): {
    added: string[];
    removed: string[];
    modified: string[];
    unchanged: string[];
  } {
    const keys1 = Object.keys(backup1.configs);
    const keys2 = Object.keys(backup2.configs);

    const added = keys2.filter(key => !keys1.includes(key));
    const removed = keys1.filter(key => !keys2.includes(key));
    const common = keys1.filter(key => keys2.includes(key));

    const modified: string[] = [];
    const unchanged: string[] = [];

    common.forEach(key => {
      const value1 = JSON.stringify(backup1.configs[key]);
      const value2 = JSON.stringify(backup2.configs[key]);

      if (value1 === value2) {
        unchanged.push(key);
      } else {
        modified.push(key);
      }
    });

    return { added, removed, modified, unchanged };
  }

  /**
   * 获取备份信息摘要
   */
  static getBackupSummary(backup: ConfigBackup): {
    version: string;
    date: string;
    size: string;
    itemCount: number;
    description?: string;
  } {
    const date = new Date(backup.timestamp).toLocaleString();
    const size = this.formatBytes(backup.metadata.totalSize);

    return {
      version: backup.version,
      date,
      size,
      itemCount: backup.metadata.itemCount,
      description: backup.metadata.description,
    };
  }

  /**
   * 创建增量备份（只包含变更的配置）
   */
  static createIncrementalBackup(baseBackup: ConfigBackup, description?: string): ConfigBackup {
    const currentBackup = this.createFullBackup();
    const diff = this.compareBackups(baseBackup, currentBackup);

    const incrementalConfigs: Record<string, any> = {};

    // 包含新增和修改的配置
    [...diff.added, ...diff.modified].forEach(key => {
      incrementalConfigs[key] = currentBackup.configs[key];
    });

    return {
      version: this.BACKUP_VERSION,
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Unknown',
      configs: incrementalConfigs,
      metadata: {
        totalSize: JSON.stringify(incrementalConfigs).length,
        itemCount: Object.keys(incrementalConfigs).length,
        description: description || `增量备份 (基于 ${baseBackup.timestamp})`,
      },
    };
  }

  /**
   * 合并多个备份
   */
  static mergeBackups(...backups: ConfigBackup[]): ConfigBackup {
    if (backups.length === 0) {
      throw new Error('至少需要一个备份文件');
    }

    const mergedConfigs: Record<string, any> = {};

    // 按时间戳排序，后面的覆盖前面的
    const sortedBackups = backups.sort(
      (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    sortedBackups.forEach(backup => {
      Object.assign(mergedConfigs, backup.configs);
    });

    return {
      version: this.BACKUP_VERSION,
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Unknown',
      configs: mergedConfigs,
      metadata: {
        totalSize: JSON.stringify(mergedConfigs).length,
        itemCount: Object.keys(mergedConfigs).length,
        description: `合并备份 (${backups.length} 个文件)`,
      },
    };
  }

  /**
   * 格式化字节大小
   */
  private static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 自动备份（定期执行）
   */
  static setupAutoBackup(intervalHours: number = 24): () => void {
    const interval = setInterval(
      () => {
        try {
          const backupKey = PersistenceManager.createBackup();
          console.log(`自动备份已创建: ${backupKey}`);
        } catch (error) {
          console.error('自动备份失败:', error);
        }
      },
      intervalHours * 60 * 60 * 1000
    );

    // 返回清理函数
    return () => clearInterval(interval);
  }

  /**
   * 清理旧备份
   */
  static cleanupOldBackups(maxAge: number = 30): number {
    let cleanedCount = 0;
    const cutoffTime = Date.now() - maxAge * 24 * 60 * 60 * 1000;

    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('backup_')) {
          const timestamp = key.split('_')[1];
          if (timestamp && new Date(timestamp).getTime() < cutoffTime) {
            localStorage.removeItem(key);
            cleanedCount++;
          }
        }
      }
    } catch (error) {
      console.error('清理旧备份失败:', error);
    }

    return cleanedCount;
  }
}
