/**
 * 组件接口设计优化工具
 * 分析和优化组件的Props接口设计，提高组件的可复用性和可维护性
 */

// ==================== 接口分析类型定义 ====================

export interface PropDefinition {
  name: string;
  type: string;
  isRequired: boolean;
  isOptional: boolean;
  hasDefaultValue: boolean;
  defaultValue?: any;
  description?: string;
  isCallback?: boolean;
  isComplex?: boolean; // 是否为复杂类型（对象、数组等）
}

export interface ComponentInterface {
  componentName: string;
  filePath: string;
  props: PropDefinition[];
  propsCount: number;
  requiredPropsCount: number;
  optionalPropsCount: number;
  callbackPropsCount: number;
  complexPropsCount: number;
  interfaceComplexity: number; // 0-10 复杂度评分
}

export interface InterfaceAnalysisResult {
  components: ComponentInterface[];
  metrics: InterfaceMetrics;
  issues: InterfaceIssue[];
  suggestions: InterfaceOptimizationSuggestion[];
}

export interface InterfaceMetrics {
  totalComponents: number;
  averagePropsPerComponent: number;
  averageRequiredProps: number;
  averageOptionalProps: number;
  averageCallbackProps: number;
  componentsWithTooManyProps: number;
  componentsWithPoorNaming: number;
  overallComplexityScore: number;
}

export interface InterfaceIssue {
  componentName: string;
  type:
    | 'too-many-props'
    | 'poor-naming'
    | 'missing-defaults'
    | 'complex-props'
    | 'inconsistent-callbacks';
  severity: 'high' | 'medium' | 'low';
  description: string;
  affectedProps: string[];
}

export interface InterfaceOptimizationSuggestion {
  componentName: string;
  type:
    | 'group-props'
    | 'extract-config'
    | 'use-composition'
    | 'simplify-callbacks'
    | 'add-defaults';
  priority: 'high' | 'medium' | 'low';
  description: string;
  implementation: string;
  beforeExample?: string;
  afterExample?: string;
}

// ==================== 组件接口分析器 ====================

export class ComponentInterfaceAnalyzer {
  private components: ComponentInterface[] = [];
  private issues: InterfaceIssue[] = [];
  private suggestions: InterfaceOptimizationSuggestion[] = [];

  /**
   * 添加组件接口信息
   */
  addComponent(component: ComponentInterface): void {
    this.components.push(component);
  }

  /**
   * 分析所有组件接口
   */
  analyze(): InterfaceAnalysisResult {
    this.analyzeIndividualComponents();
    this.generateSuggestions();

    return {
      components: this.components,
      metrics: this.calculateMetrics(),
      issues: this.issues,
      suggestions: this.suggestions,
    };
  }

  /**
   * 分析单个组件
   */
  private analyzeIndividualComponents(): void {
    this.components.forEach(component => {
      this.analyzePropsCount(component);
      this.analyzePropNaming(component);
      this.analyzeDefaultValues(component);
      this.analyzeComplexProps(component);
      this.analyzeCallbackConsistency(component);
    });
  }

  /**
   * 分析Props数量
   */
  private analyzePropsCount(component: ComponentInterface): void {
    if (component.propsCount > 15) {
      this.issues.push({
        componentName: component.componentName,
        type: 'too-many-props',
        severity: 'high',
        description: `组件有 ${component.propsCount} 个Props，建议拆分或分组`,
        affectedProps: component.props.map(p => p.name),
      });
    } else if (component.propsCount > 10) {
      this.issues.push({
        componentName: component.componentName,
        type: 'too-many-props',
        severity: 'medium',
        description: `组件有 ${component.propsCount} 个Props，考虑优化`,
        affectedProps: component.props.map(p => p.name),
      });
    }
  }

  /**
   * 分析Props命名
   */
  private analyzePropNaming(component: ComponentInterface): void {
    const poorlyNamedProps = component.props.filter(prop => {
      // 检查命名规范
      const hasGoodNaming =
        (prop.name.length > 2 && // 不能太短
          /^[a-z][a-zA-Z0-9]*$/.test(prop.name) && // 驼峰命名
          !prop.name.includes('_') && // 不包含下划线
          !prop.name.startsWith('on')) ||
        prop.isCallback; // on开头的应该是回调

      return !hasGoodNaming;
    });

    if (poorlyNamedProps.length > 0) {
      this.issues.push({
        componentName: component.componentName,
        type: 'poor-naming',
        severity: 'medium',
        description: '存在命名不规范的Props',
        affectedProps: poorlyNamedProps.map(p => p.name),
      });
    }
  }

  /**
   * 分析默认值
   */
  private analyzeDefaultValues(component: ComponentInterface): void {
    const optionalPropsWithoutDefaults = component.props.filter(
      prop => prop.isOptional && !prop.hasDefaultValue && !prop.isCallback
    );

    if (optionalPropsWithoutDefaults.length > 0) {
      this.issues.push({
        componentName: component.componentName,
        type: 'missing-defaults',
        severity: 'low',
        description: '可选Props缺少默认值',
        affectedProps: optionalPropsWithoutDefaults.map(p => p.name),
      });
    }
  }

  /**
   * 分析复杂Props
   */
  private analyzeComplexProps(component: ComponentInterface): void {
    if (component.complexPropsCount > 5) {
      this.issues.push({
        componentName: component.componentName,
        type: 'complex-props',
        severity: 'medium',
        description: `有 ${component.complexPropsCount} 个复杂Props，考虑简化`,
        affectedProps: component.props.filter(p => p.isComplex).map(p => p.name),
      });
    }
  }

  /**
   * 分析回调一致性
   */
  private analyzeCallbackConsistency(component: ComponentInterface): void {
    const callbacks = component.props.filter(p => p.isCallback);
    const inconsistentCallbacks = callbacks.filter(callback => {
      // 检查回调命名一致性
      return !callback.name.startsWith('on') && !callback.name.startsWith('handle');
    });

    if (inconsistentCallbacks.length > 0) {
      this.issues.push({
        componentName: component.componentName,
        type: 'inconsistent-callbacks',
        severity: 'low',
        description: '回调函数命名不一致',
        affectedProps: inconsistentCallbacks.map(p => p.name),
      });
    }
  }

  /**
   * 生成优化建议
   */
  private generateSuggestions(): void {
    this.components.forEach(component => {
      this.generatePropsGroupingSuggestions(component);
      this.generateConfigExtractionSuggestions(component);
      this.generateCompositionSuggestions(component);
      this.generateCallbackSimplificationSuggestions(component);
      this.generateDefaultValueSuggestions(component);
    });
  }

  /**
   * 生成Props分组建议
   */
  private generatePropsGroupingSuggestions(component: ComponentInterface): void {
    if (component.propsCount > 10) {
      this.suggestions.push({
        componentName: component.componentName,
        type: 'group-props',
        priority: 'high',
        description: '将相关Props分组到对象中',
        implementation: '创建配置对象来组织相关的Props',
        beforeExample: `interface Props {
  width: number;
  height: number;
  backgroundColor: string;
  borderColor: string;
  borderWidth: number;
  // ... 更多样式相关props
}`,
        afterExample: `interface Props {
  style: {
    width: number;
    height: number;
    backgroundColor: string;
    border: {
      color: string;
      width: number;
    };
  };
  // ... 其他props
}`,
      });
    }
  }

  /**
   * 生成配置提取建议
   */
  private generateConfigExtractionSuggestions(component: ComponentInterface): void {
    const configProps = component.props.filter(prop => prop.hasDefaultValue && !prop.isCallback);

    if (configProps.length > 5) {
      this.suggestions.push({
        componentName: component.componentName,
        type: 'extract-config',
        priority: 'medium',
        description: '提取配置对象',
        implementation: '将多个配置相关的Props合并为一个配置对象',
        beforeExample: `interface Props {
  showHeader: boolean;
  showFooter: boolean;
  enableSearch: boolean;
  enableSort: boolean;
  pageSize: number;
}`,
        afterExample: `interface Props {
  config: {
    showHeader?: boolean;
    showFooter?: boolean;
    enableSearch?: boolean;
    enableSort?: boolean;
    pageSize?: number;
  };
}`,
      });
    }
  }

  /**
   * 生成组合模式建议
   */
  private generateCompositionSuggestions(component: ComponentInterface): void {
    if (component.propsCount > 15) {
      this.suggestions.push({
        componentName: component.componentName,
        type: 'use-composition',
        priority: 'high',
        description: '使用组合模式拆分组件',
        implementation: '将大组件拆分为多个小组件，通过组合使用',
        beforeExample: `<LargeComponent 
  prop1={...} prop2={...} prop3={...}
  // ... 15+ props
/>`,
        afterExample: `<ComponentContainer>
  <ComponentHeader {...headerProps} />
  <ComponentBody {...bodyProps} />
  <ComponentFooter {...footerProps} />
</ComponentContainer>`,
      });
    }
  }

  /**
   * 生成回调简化建议
   */
  private generateCallbackSimplificationSuggestions(component: ComponentInterface): void {
    if (component.callbackPropsCount > 5) {
      this.suggestions.push({
        componentName: component.componentName,
        type: 'simplify-callbacks',
        priority: 'medium',
        description: '简化回调函数',
        implementation: '将多个相关回调合并为一个事件处理器',
        beforeExample: `interface Props {
  onAdd: () => void;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onSelect: (id: string) => void;
  onSort: (field: string) => void;
}`,
        afterExample: `interface Props {
  onAction: (action: 'add' | 'edit' | 'delete' | 'select' | 'sort', payload?: any) => void;
}`,
      });
    }
  }

  /**
   * 生成默认值建议
   */
  private generateDefaultValueSuggestions(component: ComponentInterface): void {
    const propsNeedingDefaults = component.props.filter(
      prop => prop.isOptional && !prop.hasDefaultValue && !prop.isCallback
    );

    if (propsNeedingDefaults.length > 0) {
      this.suggestions.push({
        componentName: component.componentName,
        type: 'add-defaults',
        priority: 'low',
        description: '为可选Props添加默认值',
        implementation: '为所有可选Props提供合理的默认值',
        beforeExample: `interface Props {
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  variant?: 'primary' | 'secondary';
}`,
        afterExample: `interface Props {
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  variant?: 'primary' | 'secondary';
}

  size: 'medium' as const,
  disabled: false,
  variant: 'primary' as const,
};`,
      });
    }
  }

  /**
   * 计算接口指标
   */
  private calculateMetrics(): InterfaceMetrics {
    const totalComponents = this.components.length;

    if (totalComponents === 0) {
      return {
        totalComponents: 0,
        averagePropsPerComponent: 0,
        averageRequiredProps: 0,
        averageOptionalProps: 0,
        averageCallbackProps: 0,
        componentsWithTooManyProps: 0,
        componentsWithPoorNaming: 0,
        overallComplexityScore: 0,
      };
    }

    const totalProps = this.components.reduce((sum, c) => sum + c.propsCount, 0);
    const totalRequiredProps = this.components.reduce((sum, c) => sum + c.requiredPropsCount, 0);
    const totalOptionalProps = this.components.reduce((sum, c) => sum + c.optionalPropsCount, 0);
    const totalCallbackProps = this.components.reduce((sum, c) => sum + c.callbackPropsCount, 0);
    const totalComplexity = this.components.reduce((sum, c) => sum + c.interfaceComplexity, 0);

    return {
      totalComponents,
      averagePropsPerComponent: totalProps / totalComponents,
      averageRequiredProps: totalRequiredProps / totalComponents,
      averageOptionalProps: totalOptionalProps / totalComponents,
      averageCallbackProps: totalCallbackProps / totalComponents,
      componentsWithTooManyProps: this.components.filter(c => c.propsCount > 10).length,
      componentsWithPoorNaming: this.issues.filter(i => i.type === 'poor-naming').length,
      overallComplexityScore: totalComplexity / totalComponents,
    };
  }

  /**
   * 生成接口优化报告
   */
  generateReport(): string {
    const result = this.analyze();
    const metrics = result.metrics;

    const report = [
      '# 组件接口优化报告',
      '',
      '## 总体指标',
      `- 组件总数: ${metrics.totalComponents}`,
      `- 平均Props数量: ${metrics.averagePropsPerComponent.toFixed(2)}`,
      `- 平均必需Props: ${metrics.averageRequiredProps.toFixed(2)}`,
      `- 平均可选Props: ${metrics.averageOptionalProps.toFixed(2)}`,
      `- 平均回调Props: ${metrics.averageCallbackProps.toFixed(2)}`,
      `- Props过多的组件: ${metrics.componentsWithTooManyProps}`,
      `- 命名不规范的组件: ${metrics.componentsWithPoorNaming}`,
      `- 整体复杂度: ${metrics.overallComplexityScore.toFixed(2)}/10`,
      '',
      '## 问题分析',
      '',
    ];

    // 按严重程度分组显示问题
    const highIssues = result.issues.filter(i => i.severity === 'high');
    const mediumIssues = result.issues.filter(i => i.severity === 'medium');
    const lowIssues = result.issues.filter(i => i.severity === 'low');

    if (highIssues.length > 0) {
      report.push('### 🔴 高优先级问题');
      highIssues.forEach(issue => {
        report.push(`- **${issue.componentName}**: ${issue.description}`);
      });
      report.push('');
    }

    if (mediumIssues.length > 0) {
      report.push('### 🟡 中优先级问题');
      mediumIssues.forEach(issue => {
        report.push(`- **${issue.componentName}**: ${issue.description}`);
      });
      report.push('');
    }

    if (lowIssues.length > 0) {
      report.push('### 🟢 低优先级问题');
      lowIssues.forEach(issue => {
        report.push(`- **${issue.componentName}**: ${issue.description}`);
      });
      report.push('');
    }

    // 优化建议
    report.push('## 优化建议');
    const highPrioritySuggestions = result.suggestions.filter(s => s.priority === 'high');

    highPrioritySuggestions.forEach(suggestion => {
      report.push(`### ${suggestion.componentName} - ${suggestion.description}`);
      report.push(`**实现方案**: ${suggestion.implementation}`);
      if (suggestion.beforeExample && suggestion.afterExample) {
        report.push('**优化前**:');
        report.push('```typescript');
        report.push(suggestion.beforeExample);
        report.push('```');
        report.push('**优化后**:');
        report.push('```typescript');
        report.push(suggestion.afterExample);
        report.push('```');
      }
      report.push('');
    });

    return report.join('\n');
  }
}

// ==================== 工具函数 ====================

/**
 * 创建Props定义
 */
export function createPropDefinition(
  name: string,
  type: string,
  isRequired = false,
  hasDefaultValue = false
): PropDefinition {
  return {
    name,
    type,
    isRequired,
    isOptional: !isRequired,
    hasDefaultValue,
    isCallback: name.startsWith('on') || name.startsWith('handle'),
    isComplex: ['object', 'array', 'function'].some(t => type.toLowerCase().includes(t)),
  };
}

/**
 * 计算接口复杂度
 */
export function calculateInterfaceComplexity(props: PropDefinition[]): number {
  if (props.length === 0) return 0;

  const weights = {
    required: 1,
    optional: 0.5,
    callback: 0.8,
    complex: 1.5,
  };

  let complexity = 0;
  props.forEach(prop => {
    if (prop.isRequired) complexity += weights.required;
    if (prop.isOptional) complexity += weights.optional;
    if (prop.isCallback) complexity += weights.callback;
    if (prop.isComplex) complexity += weights.complex;
  });

  return Math.min(10, (complexity / props.length) * 2);
}
