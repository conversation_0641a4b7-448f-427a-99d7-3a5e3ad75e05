/**
 * AI配比生成服务
 * 封装AI配比生成的相关功能
 */

import { httpClient } from '@/infrastructure/api/client/httpClient';
import type { RatioGenerationResult } from '@/features/ai/genkit';

export interface AIRatioGenerationRequest {
  targetStrength: string;
  environment: string;
  costLevel: 'high' | 'medium' | 'low';
  specialRequirements: string[];
  selectedMaterials: string[];
  additionalParams: {
    slump: number;
    durability: string;
    workability: string;
    temperature: number;
  };
  availableMaterials: Array<{
    id: string;
    name: string;
    type: string;
    specification: string;
    available: boolean;
    amount: number;
  }>;
}

export interface AIRatioGenerationResponse {
  success: boolean;
  data?: RatioGenerationResult;
  message: string;
  timestamp: string;
  error?: string;
}

class AIRatioService {
  private readonly baseUrl = '/ratio';

  /**
   * 生成AI配比
   */
  async generateRatio(request: AIRatioGenerationRequest): Promise<AIRatioGenerationResponse> {
    try {
      console.log('发送AI配比生成请求:', {
        targetStrength: request.targetStrength,
        environment: request.environment,
        costLevel: request.costLevel,
      });

      const response = await httpClient.post<AIRatioGenerationResponse>(
        `${this.baseUrl}/ai-generate`,
        request
      );

      if (response.success && response.data) {
        console.log('AI配比生成成功:', {
          ratioName: response.data.ratioName,
          qualityScore: response.data.calculationResults.qualityScore,
        });
      }

      return response;
    } catch (error) {
      console.error('AI配比生成请求失败:', error);

      // 返回错误响应
      return {
        success: false,
        message: 'AI配比生成请求失败',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : '网络错误',
      };
    }
  }

  /**
   * 验证配比生成请求参数
   */
  validateRequest(request: AIRatioGenerationRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查必要参数
    if (!request.targetStrength) {
      errors.push('目标强度等级不能为空');
    }

    if (!request.environment) {
      errors.push('使用环境不能为空');
    }

    if (!request.costLevel) {
      errors.push('成本要求不能为空');
    }

    // 检查材料清单
    if (!request.availableMaterials || request.availableMaterials.length === 0) {
      errors.push('可用材料清单不能为空');
    }

    // 检查选中的材料
    if (!request.selectedMaterials || request.selectedMaterials.length === 0) {
      errors.push('至少需要选择一种材料');
    }

    // 检查附加参数
    if (request.additionalParams) {
      const { slump, temperature } = request.additionalParams;

      if (slump < 50 || slump > 300) {
        errors.push('坍落度应在50-300mm范围内');
      }

      if (temperature < -20 || temperature > 50) {
        errors.push('施工温度应在-20°C到50°C范围内');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 获取默认的配比生成参数
   */
  getDefaultRequest(): Partial<AIRatioGenerationRequest> {
    return {
      targetStrength: 'C30',
      environment: 'normal',
      costLevel: 'medium',
      specialRequirements: [],
      selectedMaterials: [],
      additionalParams: {
        slump: 180,
        durability: 'normal',
        workability: 'good',
        temperature: 20,
      },
      availableMaterials: [],
    };
  }

  /**
   * 格式化配比结果用于显示
   */
  formatRatioResult(result: RatioGenerationResult): {
    summary: string;
    details: Array<{ label: string; value: string; unit?: string }>;
  } {
    const { calculationParams, calculationResults } = result;

    const summary = `${result.ratioName} - 质量评分: ${calculationResults.qualityScore.toFixed(1)}分`;

    const details = [
      { label: '目标强度', value: calculationParams.targetStrength.toString(), unit: 'MPa' },
      { label: '坍落度', value: calculationParams.slump.toString(), unit: 'mm' },
      { label: '水胶比', value: calculationParams.waterCementRatio.toFixed(2) },
      { label: '砂率', value: calculationParams.sandRatio.toFixed(1), unit: '%' },
      { label: '水泥用量', value: calculationParams.cementContent.toString(), unit: 'kg/m³' },
      { label: '用水量', value: calculationParams.waterContent.toString(), unit: 'kg/m³' },
      { label: '总重量', value: calculationResults.totalWeight.toString(), unit: 'kg/m³' },
      { label: '强度预测', value: calculationResults.strengthPrediction.toString(), unit: 'MPa' },
      { label: '成本估算', value: calculationResults.costEstimate.toFixed(2), unit: '元/m³' },
      { label: '碳足迹', value: calculationResults.carbonFootprint.toFixed(2), unit: 'kg CO2/m³' },
    ];

    return { summary, details };
  }
}

// 导出单例实例
export const aiRatioService = new AIRatioService();
export default aiRatioService;
