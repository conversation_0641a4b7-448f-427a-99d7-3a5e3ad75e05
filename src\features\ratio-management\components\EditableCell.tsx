/**
 * 可编辑单元格组件
 * 提供数值输入和增减按钮的组合控件
 */

import React, { useState, useEffect } from 'react';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { Input } from '@/shared/components/input';

/**
 * 可编辑单元格组件Props
 */
export interface EditableCellProps {
  value: number;
  onUpdate: (newValue: number) => void;
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
  disabled?: boolean;
  className?: string;
}

/**
 * 可编辑单元格组件
 */
export const EditableCell = React.memo<EditableCellProps>(function EditableCell({
  value,
  onUpdate,
  min = 0,
  max = Infinity,
  step = 1,
  precision = 2,
  disabled = false,
  className = '',
}) {
  const [localValue, setLocalValue] = useState(value.toString());
  const [isEditing, setIsEditing] = useState(false);

  // 同步外部值变化
  useEffect(() => {
    if (!isEditing) {
      setLocalValue(value.toFixed(precision));
    }
  }, [value, precision, isEditing]);

  // 处理值更新
  const handleUpdate = (val: string) => {
    const num = parseFloat(val);
    if (!isNaN(num)) {
      const clampedValue = Math.max(min, Math.min(max, num));
      onUpdate(clampedValue);
    }
    setIsEditing(false);
  };

  // 处理增加
  const handleIncrement = () => {
    if (disabled) return;
    const newValue = Math.min(max, value + step);
    onUpdate(newValue);
  };

  // 处理减少
  const handleDecrement = () => {
    if (disabled) return;
    const newValue = Math.max(min, value - step);
    onUpdate(newValue);
  };

  // 处理输入框焦点
  const handleFocus = () => {
    setIsEditing(true);
  };

  // 处理输入框失焦
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    handleUpdate(e.target.value);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleUpdate((e.target as HTMLInputElement).value);
    } else if (e.key === 'Escape') {
      setLocalValue(value.toFixed(precision));
      setIsEditing(false);
      (e.target as HTMLInputElement).blur();
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      handleIncrement();
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      handleDecrement();
    }
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <Input
        type='number'
        value={localValue}
        onChange={e => setLocalValue(e.target.value)}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        className='w-16 h-6 p-1 text-center text-xs'
        min={min}
        max={max}
        step={step}
      />

      <div className='flex flex-col'>
        <button
          type='button'
          onClick={handleIncrement}
          disabled={disabled || value >= max}
          className={`
            h-3 w-4 flex items-center justify-center border rounded-t-sm 
            hover:bg-muted transition-colors
            ${disabled || value >= max ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          `}
        >
          <ChevronUp className='h-2 w-2' />
        </button>

        <button
          type='button'
          onClick={handleDecrement}
          disabled={disabled || value <= min}
          className={`
            h-3 w-4 flex items-center justify-center border-t-0 border rounded-b-sm 
            hover:bg-muted transition-colors
            ${disabled || value <= min ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          `}
        >
          <ChevronDown className='h-2 w-2' />
        </button>
      </div>
    </div>
  );
});
