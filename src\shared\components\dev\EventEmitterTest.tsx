'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/shared/components/button';
import { EVENTS, globalEventEmitter } from '@/core/utils/eventEmitter';

export function EventEmitterTest() {
  const [lastEvent, setLastEvent] = useState<string>('');

  useEffect(() => {
    const handleEvent = (taskId: string) => {
      setLastEvent(`Received event for task: ${taskId}`);
      console.log('Event received:', taskId);
    };

    globalEventEmitter.on(EVENTS.SEND_PRODUCTION_INSTRUCTION, handleEvent);

    return () => {
      globalEventEmitter.off(EVENTS.SEND_PRODUCTION_INSTRUCTION, handleEvent);
    };
  }, []);

  const testEvent = () => {
    globalEventEmitter.emit(EVENTS.SEND_PRODUCTION_INSTRUCTION, 'test-task-123');
  };

  return (
    <div className='p-4 border rounded'>
      <h3 className='text-lg font-semibold mb-2'>Event Emitter Test</h3>
      <Button onClick={testEvent} className='mb-2'>
        Test Event
      </Button>
      <p className='text-sm text-muted-foreground'>Last event: {lastEvent || 'None'}</p>
    </div>
  );
}
