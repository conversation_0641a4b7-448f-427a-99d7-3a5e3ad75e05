/**
 * API响应类型守卫系统
 * 提供强类型的API响应验证，确保API数据类型安全
 */

import { v, validateData, isValidData, BaseValidator } from './runtime-validator';
import type { Task, Vehicle, Plant } from '@/core/types';

// ==================== API响应基础类型 ====================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    total?: number;
    page?: number;
    pageSize?: number;
    timestamp?: string;
  };
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  meta: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    timestamp: string;
  };
}

// ==================== 核心实体验证器 ====================

// Task验证器
export const taskValidator = v.object<Task>(
  {
    id: v.nonEmptyString(),
    taskNumber: v.nonEmptyString(),
    projectName: v.nonEmptyString(),
    projectAbbreviation: v.optional(v.string()),
    constructionUnit: v.optional(v.string()),
    constructionSite: v.optional(v.string()),
    strength: v.optional(v.string()),
    pouringMethod: v.optional(v.string()),
    vehicleCount: v.positiveInteger(),
    completedProgress: v.number(),
    requiredVolume: v.positiveNumber(),
    completedVolume: v.number(),
    pumpTruck: v.optional(v.string()),
    otherRequirements: v.optional(v.string()),
    contactPhone: v.optional(v.string()),
    supplyTime: v.optional(v.string()),
    supplyDate: v.optional(v.string()),
    publishDate: v.optional(v.string()),
    dispatchedVehicles: v.array(v.string()),
    productionLines: v.array(v.string()),
    nextScheduledDispatchTime: v.optional(v.string()),
    isDueForDispatch: v.optional(v.boolean()),
    minutesToDispatch: v.optional(v.number()),
    messages: v.optional(
      v.array(
        v.object({
          id: v.nonEmptyString(),
          type: v.string(),
          content: v.nonEmptyString(),
          timestamp: v.string(),
          isRead: v.optional(v.boolean()),
        })
      )
    ),
  },
  [
    'id',
    'taskNumber',
    'projectName',
    'vehicleCount',
    'completedProgress',
    'requiredVolume',
    'completedVolume',
    'dispatchedVehicles',
    'productionLines',
  ]
);

// Vehicle验证器
export const vehicleValidator = v.object<Vehicle>(
  {
    id: v.nonEmptyString(),
    licensePlate: v.nonEmptyString(),
    internalId: v.optional(v.string()),
    status: v.string(),
    capacity: v.positiveNumber(),
    currentLoad: v.number(),
    location: v.optional(v.string()),
    driver: v.optional(
      v.object({
        id: v.nonEmptyString(),
        name: v.nonEmptyString(),
        phone: v.optional(v.string()),
      })
    ),
    lastUpdated: v.optional(v.string()),
    estimatedArrival: v.optional(v.string()),
    taskId: v.optional(v.string()),
  },
  ['id', 'licensePlate', 'status', 'capacity', 'currentLoad']
);

// Plant验证器
export const plantValidator = v.object<Plant>(
  {
    id: v.nonEmptyString(),
    name: v.nonEmptyString(),
    location: v.optional(v.string()),
    capacity: v.positiveNumber(),
    currentProduction: v.number(),
    status: v.string(),
    productionLines: v.array(
      v.object({
        id: v.nonEmptyString(),
        name: v.nonEmptyString(),
        status: v.string(),
        capacity: v.positiveNumber(),
        currentLoad: v.number(),
      })
    ),
  },
  ['id', 'name', 'capacity', 'currentProduction', 'status', 'productionLines']
);

// ==================== API响应验证器 ====================

export function createApiResponseValidator<T>(dataValidator: BaseValidator<T>) {
  return v.object<ApiResponse<T>>(
    {
      success: v.boolean(),
      data: v.optional(dataValidator),
      error: v.optional(
        v.object({
          code: v.nonEmptyString(),
          message: v.nonEmptyString(),
          details: v.optional(v.object({})),
        })
      ),
      meta: v.optional(
        v.object({
          total: v.optional(v.positiveInteger()),
          page: v.optional(v.positiveInteger()),
          pageSize: v.optional(v.positiveInteger()),
          timestamp: v.optional(v.string()),
        })
      ),
    },
    ['success']
  );
}

export function createPaginatedResponseValidator<T>(itemValidator: BaseValidator<T>) {
  return v.object<PaginatedResponse<T>>(
    {
      success: v.boolean(),
      data: v.optional(v.array(itemValidator)),
      error: v.optional(
        v.object({
          code: v.nonEmptyString(),
          message: v.nonEmptyString(),
          details: v.optional(v.object({})),
        })
      ),
      meta: v.object({
        total: v.positiveInteger(),
        page: v.positiveInteger(),
        pageSize: v.positiveInteger(),
        totalPages: v.positiveInteger(),
        hasNext: v.boolean(),
        hasPrev: v.boolean(),
        timestamp: v.string(),
      }),
    },
    ['success', 'meta']
  );
}

// ==================== 预定义验证器 ====================

export const taskListResponseValidator = createPaginatedResponseValidator(taskValidator);
export const taskResponseValidator = createApiResponseValidator(taskValidator);
export const vehicleListResponseValidator = createPaginatedResponseValidator(vehicleValidator);
export const vehicleResponseValidator = createApiResponseValidator(vehicleValidator);
export const plantListResponseValidator = createApiResponseValidator(v.array(plantValidator));
export const plantResponseValidator = createApiResponseValidator(plantValidator);

// ==================== 类型守卫函数 ====================

export function isApiResponse<T>(
  data: unknown,
  dataValidator?: BaseValidator<T>
): data is ApiResponse<T> {
  const validator = dataValidator
    ? createApiResponseValidator(dataValidator)
    : createApiResponseValidator(v.object({}));

  return isValidData(data, validator);
}

export function isPaginatedResponse<T>(
  data: unknown,
  itemValidator: BaseValidator<T>
): data is PaginatedResponse<T> {
  return isValidData(data, createPaginatedResponseValidator(itemValidator));
}

export function isTask(data: unknown): data is Task {
  return isValidData(data, taskValidator);
}

export function isTaskArray(data: unknown): data is Task[] {
  return isValidData(data, v.array(taskValidator));
}

export function isVehicle(data: unknown): data is Vehicle {
  return isValidData(data, vehicleValidator);
}

export function isVehicleArray(data: unknown): data is Vehicle[] {
  return isValidData(data, v.array(vehicleValidator));
}

export function isPlant(data: unknown): data is Plant {
  return isValidData(data, plantValidator);
}

export function isPlantArray(data: unknown): data is Plant[] {
  return isValidData(data, v.array(plantValidator));
}

// ==================== 安全的API响应处理 ====================

export function validateApiResponse<T>(
  response: unknown,
  dataValidator: BaseValidator<T>,
  options: { throwOnError?: boolean } = {}
): ApiResponse<T> {
  const validator = createApiResponseValidator(dataValidator);
  return validateData(response, validator, options);
}

export function validatePaginatedResponse<T>(
  response: unknown,
  itemValidator: BaseValidator<T>,
  options: { throwOnError?: boolean } = {}
): PaginatedResponse<T> {
  const validator = createPaginatedResponseValidator(itemValidator);
  return validateData(response, validator, options);
}

// ==================== API错误处理 ====================

export class ApiValidationError extends Error {
  public readonly validationErrors: Array<{
    path: string;
    message: string;
    expected: string;
    received: string;
  }>;

  constructor(message: string, validationErrors: any[]) {
    super(message);
    this.name = 'ApiValidationError';
    this.validationErrors = validationErrors;
  }

  override toString(): string {
    const errorDetails = this.validationErrors.map(e => `  ${e.path}: ${e.message}`).join('\n');

    return `${this.message}\n验证错误:\n${errorDetails}`;
  }
}

export function safeApiCall<T>(
  apiCall: () => Promise<unknown>,
  dataValidator: BaseValidator<T>
): Promise<ApiResponse<T>> {
  return apiCall()
    .then(response => {
      try {
        return validateApiResponse(response, dataValidator);
      } catch (error) {
        if (error instanceof TypeError) {
          throw new ApiValidationError(
            'API响应格式验证失败',
            [] // 这里可以从error中提取详细信息
          );
        }
        throw error;
      }
    })
    .catch(error => {
      // 网络错误或其他错误的统一处理
      return {
        success: false,
        error: {
          code: 'NETWORK_ERROR',
          message: error.message || '网络请求失败',
          details: error,
        },
      } as ApiResponse<T>;
    });
}

// ==================== 开发工具 ====================

export function debugApiResponse(response: unknown, validator: BaseValidator): void {
  if (process.env.NODE_ENV === 'development') {
    const result = validator.validate(response);

    if (!result.isValid) {
      console.group('🔍 API响应验证失败');
      console.log('原始响应:', response);
      console.log('验证错误:', result.errors);
      console.log('验证警告:', result.warnings);
      console.groupEnd();
    } else if (result.warnings.length > 0) {
      console.group('⚠️ API响应验证警告');
      console.log('原始响应:', response);
      console.log('验证警告:', result.warnings);
      console.groupEnd();
    }
  }
}
