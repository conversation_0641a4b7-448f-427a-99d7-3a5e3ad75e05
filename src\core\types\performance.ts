/**
 * 性能监控系统类型定义
 * 扩展现有的性能监控功能，支持更全面的性能分析
 */

// ==================== 扩展的 Web Vitals 指标 ====================

export interface ExtendedWebVitals {
  // 核心 Web Vitals
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift

  // 新增 Web Vitals
  inp: number; // Interaction to Next Paint (替代FID)
  ttfb: number; // Time to First Byte
  fmp: number; // First Meaningful Paint

  // 自定义性能指标
  tti: number; // Time to Interactive
  tbt: number; // Total Blocking Time
  si: number; // Speed Index

  // 资源加载指标
  domContentLoaded: number;
  loadComplete: number;
  resourceLoadTime: number;

  // 网络指标
  connectionType: string;
  effectiveType: string;
  downlink: number;
  rtt: number;

  // 设备指标
  deviceMemory?: number;
  hardwareConcurrency?: number;

  // 时间戳
  timestamp: number;
  url: string;
}

// ==================== 组件性能指标 ====================

export interface ComponentPerformanceMetrics {
  componentName: string;

  // 渲染性能
  renderTime: number;
  mountTime: number;
  updateTime: number;
  unmountTime: number;

  // 内存使用
  memoryUsage: {
    used: number;
    total: number;
    limit: number;
    delta: number; // 相对于上次测量的变化
  };

  // 渲染统计
  renderCount: number;
  reRenderCount: number;
  wastedRenders: number; // 无效重渲染次数

  // 性能评分
  performanceScore: number; // 0-100

  // 时间戳和上下文
  timestamp: number;
  route: string;
  userAgent: string;

  // 组件层级信息
  depth: number; // 组件嵌套深度
  childrenCount: number; // 子组件数量
  propsSize: number; // props 数据大小
}

// ==================== 性能基准测试 ====================

export interface PerformanceBenchmark {
  id: string;
  name: string;
  description: string;

  // 基准值
  baseline: {
    renderTime: number;
    memoryUsage: number;
    bundleSize: number;
    loadTime: number;
  };

  // 阈值配置
  thresholds: {
    excellent: number;
    good: number;
    needsImprovement: number;
    poor: number;
  };

  // 测试环境
  environment: {
    device: 'desktop' | 'mobile' | 'tablet';
    network: '4g' | '3g' | 'wifi' | 'offline';
    cpu: 'high' | 'medium' | 'low';
  };

  // 创建和更新时间
  createdAt: string;
  updatedAt: string;
}

// ==================== 性能分析报告 ====================

export interface PerformanceAnalysisReport {
  id: string;
  timestamp: string;

  // 总体评分
  overallScore: number; // 0-100

  // 各项指标评分
  scores: {
    webVitals: number;
    componentPerformance: number;
    memoryUsage: number;
    bundleSize: number;
    networkPerformance: number;
  };

  // 性能问题
  issues: PerformanceIssue[];

  // 优化建议
  recommendations: PerformanceRecommendation[];

  // 趋势分析
  trends: {
    period: '1h' | '24h' | '7d' | '30d';
    improvement: number; // 相对于上一周期的改善百分比
    regression: PerformanceRegression[];
  };

  // 对比数据
  comparison: {
    baseline: PerformanceBenchmark;
    current: ExtendedWebVitals & ComponentPerformanceMetrics;
    delta: Record<string, number>;
  };
}

// ==================== 性能问题定义 ====================

export interface PerformanceIssue {
  id: string;
  type: 'critical' | 'warning' | 'info';
  category: 'rendering' | 'memory' | 'network' | 'bundle' | 'component';

  title: string;
  description: string;
  impact: string; // 对用户体验的影响描述

  // 问题详情
  details: {
    component?: string;
    metric: string;
    currentValue: number;
    expectedValue: number;
    threshold: number;
  };

  // 解决方案
  solutions: string[];

  // 优先级
  priority: number; // 1-10

  timestamp: string;
}

// ==================== 性能优化建议 ====================

export interface PerformanceRecommendation {
  id: string;
  title: string;
  description: string;

  // 建议类型
  type: 'code' | 'config' | 'infrastructure' | 'bundle';

  // 预期效果
  expectedImpact: {
    metric: string;
    improvement: number; // 预期改善百分比
    effort: 'low' | 'medium' | 'high'; // 实施难度
  };

  // 实施步骤
  steps: string[];

  // 相关资源
  resources: {
    documentation?: string;
    examples?: string[];
    tools?: string[];
  };

  // 优先级
  priority: number;
}

// ==================== 性能回归检测 ====================

export interface PerformanceRegression {
  metric: string;
  component?: string;

  // 回归数据
  baseline: number;
  current: number;
  regression: number; // 回归百分比

  // 检测信息
  detectedAt: string;
  severity: 'minor' | 'major' | 'critical';

  // 可能原因
  possibleCauses: string[];
}

// ==================== 性能监控配置 ====================

export interface PerformanceMonitoringConfig {
  // 监控开关
  enabled: boolean;

  // 采样配置
  sampling: {
    webVitals: number; // 0-1
    componentMetrics: number;
    memoryTracking: number;
    networkMonitoring: number;
  };

  // 阈值配置
  thresholds: {
    renderTime: number; // ms
    memoryUsage: number; // MB
    bundleSize: number; // KB
    loadTime: number; // ms
  };

  // 报告配置
  reporting: {
    interval: number; // 报告生成间隔(ms)
    retention: number; // 数据保留天数
    endpoint?: string; // 数据上报端点
  };

  // 基准测试配置
  benchmarking: {
    enabled: boolean;
    autoUpdate: boolean; // 自动更新基准
    environments: PerformanceBenchmark['environment'][];
  };

  // 告警配置
  alerts: {
    enabled: boolean;
    channels: ('console' | 'toast' | 'email' | 'webhook')[];
    thresholds: {
      critical: number;
      warning: number;
    };
  };
}

// ==================== 性能数据存储 ====================

export interface PerformanceDataStore {
  // Web Vitals 历史数据
  webVitalsHistory: ExtendedWebVitals[];

  // 组件性能历史
  componentMetricsHistory: ComponentPerformanceMetrics[];

  // 基准测试数据
  benchmarks: PerformanceBenchmark[];

  // 分析报告
  reports: PerformanceAnalysisReport[];

  // 配置
  config: PerformanceMonitoringConfig;

  // 元数据
  metadata: {
    version: string;
    lastUpdated: string;
    dataSize: number;
  };
}

// ==================== Hook 和工具类型 ====================

export interface UsePerformanceMonitorOptions {
  componentName: string;
  enableMemoryTracking?: boolean;
  enableBenchmarking?: boolean;
  sampleRate?: number;
  thresholds?: {
    renderTime?: number;
    memoryUsage?: number;
  };
}

export interface PerformanceMonitorHookReturn {
  // 当前指标
  metrics: ComponentPerformanceMetrics | null;

  // 统计数据
  stats: {
    averageRenderTime: number;
    renderCount: number;
    memoryTrend: 'increasing' | 'stable' | 'decreasing';
    performanceScore: number;
  };

  // 控制方法
  startMeasure: () => void;
  endMeasure: () => void;
  clearMetrics: () => void;

  // 报告生成
  generateReport: () => PerformanceAnalysisReport;

  // 基准测试
  runBenchmark: () => Promise<PerformanceBenchmark>;

  // 问题检测
  detectIssues: () => PerformanceIssue[];
}

// ==================== 事件类型 ====================

export interface PerformanceEvent {
  type: 'metric' | 'issue' | 'recommendation' | 'benchmark';
  timestamp: string;
  data: ExtendedWebVitals | PerformanceIssue | PerformanceRecommendation | PerformanceBenchmark;
  context: {
    route: string;
    userAgent: string;
    sessionId: string;
  };
}

// ==================== 导出类型联合 ====================

export type PerformanceMetricType =
  | 'fcp'
  | 'lcp'
  | 'fid'
  | 'cls'
  | 'inp'
  | 'ttfb'
  | 'fmp'
  | 'tti'
  | 'tbt'
  | 'si'
  | 'renderTime'
  | 'memoryUsage';

export type PerformanceStatus = 'excellent' | 'good' | 'needs-improvement' | 'poor';

export type PerformanceCategory = 'web-vitals' | 'component' | 'memory' | 'network' | 'bundle';
