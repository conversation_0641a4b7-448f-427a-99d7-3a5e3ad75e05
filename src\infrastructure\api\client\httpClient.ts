import { getApiConfig, buildUrl } from '@/infrastructure/api/client/api';
import { errorConverter } from '@/infrastructure/error-handling/errorUtils';
import { errorManager } from '../../error-handling/error/ErrorManager';

// HTTP客户端错误类型
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// 请求配置接口
export interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string>;
  timeout?: number;
}

// HTTP客户端类
class HttpClient {
  private config = getApiConfig();

  // 通用请求方法
  async request<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
    const { method = 'GET', headers = {}, body, params, timeout = this.config.timeout } = config;

    // 构建URL
    const url = buildUrl(endpoint, params);

    // Mock数据拦截 - 如果URL为空，说明是mock模式
    if (url === '') {
      return this.handleMockRequest<T>(endpoint, config);
    }

    // 设置默认headers
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...headers,
    };

    // 构建fetch配置
    const fetchConfig: RequestInit = {
      method,
      headers: defaultHeaders,
      signal: timeout ? AbortSignal.timeout(timeout) : undefined,
    };

    // 添加请求体
    if (body && method !== 'GET') {
      fetchConfig.body = JSON.stringify(body);
    }

    try {
      const response = await fetch(url, fetchConfig);

      if (!response.ok) {
        const responseData = await response.text();
        const apiError = errorConverter.fromApiResponse(response, responseData);

        // 同时抛出旧格式错误以保持向后兼容
        const legacyError = new ApiError(apiError.message, response.status, responseData);

        // 使用新的错误管理器处理
        errorManager.handleError(apiError, {
          method: fetchConfig.method || method,
          url,
        } as any);

        throw legacyError;
      }

      const data = await response.json();
      return data;
    } catch (error) {
      // Graceful fallback for network errors (like 'fetch failed' on server)
      if (
        error instanceof TypeError &&
        (error.message.includes('fetch failed') || error.message.includes('Failed to fetch'))
      ) {
        console.warn(
          `[HttpClient] API fetch to ${url} failed. Falling back to mock data for endpoint: ${endpoint}`
        );

        // 记录网络错误但不阻断流程
        const networkError = errorConverter.fromNetworkError(error);
        errorManager.handleError(networkError, {
          url,
          fallbackUsed: true,
        } as any);

        return this.handleMockRequest<T>(endpoint, config);
      }

      if (error instanceof ApiError) {
        throw error;
      }

      // 处理其他网络错误、超时等
      const networkError = errorConverter.fromNetworkError(error as Error);
      errorManager.handleError(networkError, {
        url,
        method: fetchConfig.method || method,
      } as any);

      throw new ApiError(networkError.message, 0);
    }
  }

  // Mock数据处理
  private async handleMockRequest<T>(endpoint: string, _config: RequestConfig): Promise<T> {
    // 动态导入mock数据，避免在生产环境加载
    const { generateMockTasks, generateMockVehicles, generateMockDeliveryOrders, mockPlants } =
      await import('@/infrastructure/api/mock/mock-data');

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 50));

    // 根据端点返回对应的mock数据
    switch (endpoint) {
      case '/tasks':
        return generateMockTasks() as T;
      case '/vehicles':
        return generateMockVehicles() as T;
      case '/delivery-orders':
        return generateMockDeliveryOrders() as T;
      case '/plants':
        return mockPlants as T;
      case '/ratio/calculate':
        return this.handleMockRatioCalculate(_config) as T;
      case '/ratio/reverse-calculate':
        return this.handleMockRatioReverseCalculate(_config) as T;
      case '/ratio/save':
        return this.handleMockRatioSave(_config) as T;
      case '/ratio/get':
        return this.handleMockRatioGet(_config) as T;
      case '/ratio/history':
        return this.handleMockRatioHistory(_config) as T;
      case '/ratio/ai-generate':
        return this.handleMockAIRatioGenerate(_config) as T;
      default:
        throw new ApiError(`Mock endpoint not found: ${endpoint}`, 404);
    }
  }

  // 便捷方法
  get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET', params });
  }

  post<T>(endpoint: string, body?: any): Promise<T> {
    return this.request<T>(endpoint, { method: 'POST', body });
  }

  put<T>(endpoint: string, body?: any): Promise<T> {
    return this.request<T>(endpoint, { method: 'PUT', body });
  }

  delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  patch<T>(endpoint: string, body?: any): Promise<T> {
    return this.request<T>(endpoint, { method: 'PATCH', body });
  }

  // ==================== 配比API Mock处理方法 ====================

  private async handleMockRatioCalculate(config: RequestConfig): Promise<any> {
    const request = config.body;

    if (!request || !request.materials) {
      throw new ApiError('Invalid request body for ratio calculate', 400);
    }

    const totalWeight = request.materials.reduce(
      (sum: number, material: any) => sum + material.designValue,
      0
    );

    return {
      success: true,
      data: {
        totalWeight,
        materials: request.materials.reduce((acc: any, material: any) => {
          acc[material.name] = material.designValue;
          return acc;
        }, {}),
        strengthPrediction: request.calculationParams?.strengthGrade || 30,
        qualityScore: 85 + Math.random() * 10,
        warnings: [],
        suggestions: ['建议进行试配验证', '注意控制搅拌时间'],
        carbonFootprint: totalWeight * 0.3,
        costEstimate: totalWeight * 0.8,
      },
      message: '配比计算完成',
      timestamp: new Date().toISOString(),
    };
  }

  private async handleMockRatioReverseCalculate(config: RequestConfig): Promise<any> {
    const request = config.body;

    if (!request || !request.materials) {
      throw new ApiError('Invalid request body for ratio reverse calculate', 400);
    }

    // 简单的反算逻辑
    const waterAmount = this.extractMaterialAmount(request.materials, '水') || 180;
    const cementAmount = this.extractMaterialAmount(request.materials, '水泥') || 400;
    const totalBinder = cementAmount;
    const waterRatio = waterAmount / totalBinder;

    return {
      success: true,
      data: {
        strengthGrade: 30,
        slump: 180,
        waterCementRatio: Math.round(waterRatio * 100) / 100,
        sandRatio: 35.0,
        cementContent: Math.round(cementAmount),
        waterContent: Math.round(waterAmount),
        additiveRatio: 2.0,
        flyashRatio: 15.0,
        mineralPowderRatio: 0,
        antifreezeRatio: 0,
        expansionRatio: 0,
        ultraFineSandRatio: 0,
        earlyStrengthRatio: 0,
        density: 2.4,
        airContent: 4.5,
        chlorideContent: 0.1,
        alkaliContent: 3.0,
      },
      message: '配比反算完成',
      timestamp: new Date().toISOString(),
    };
  }

  private async handleMockRatioSave(_config: RequestConfig): Promise<any> {
    return {
      success: true,
      data: {
        ratioId: `ratio-${Date.now()}`,
        version: 1,
      },
      message: '配比保存成功',
      timestamp: new Date().toISOString(),
    };
  }

  private async handleMockRatioGet(_config: RequestConfig): Promise<any> {
    return {
      success: true,
      data: {
        taskId: 'task-001',
        ratioId: `ratio-${Date.now()}`,
        version: 1,
        calculationParams: {},
        materials: [],
        calculationResults: {},
        ratioName: '标准C30配比',
        description: 'Mock配比数据',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      message: '配比获取成功',
      timestamp: new Date().toISOString(),
    };
  }

  private async handleMockRatioHistory(_config: RequestConfig): Promise<any> {
    return {
      success: true,
      data: [
        {
          ratioId: `ratio-${Date.now()}-1`,
          version: 1,
          ratioName: '初始配比',
          description: '首次创建的配比',
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          createdBy: '系统管理员',
          operation: 'create',
          changes: ['创建配比'],
        },
      ],
      message: '配比历史获取成功',
      timestamp: new Date().toISOString(),
    };
  }

  private async handleMockAIRatioGenerate(config: RequestConfig): Promise<any> {
    const request = config.body;

    if (!request || !request.targetStrength || !request.environment || !request.costLevel) {
      throw new ApiError('Invalid request body for AI ratio generate', 400);
    }

    if (!request.availableMaterials || request.availableMaterials.length === 0) {
      throw new ApiError('Available materials list is required', 400);
    }

    // 模拟AI生成延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // 检查是否在服务器环境中运行
    if (typeof window === 'undefined') {
      // 服务器端：使用实际的AI配比生成逻辑
      try {
        const { generateConcreteRatio } = await import('@/features/ai/genkit');

        const genkitRequest = {
          targetStrength: request.targetStrength,
          environment: request.environment,
          costLevel: request.costLevel,
          specialRequirements: request.specialRequirements || [],
          selectedMaterials: request.selectedMaterials || [],
          additionalParams: {
            slump: request.additionalParams?.slump || 180,
            durability: request.additionalParams?.durability || 'normal',
            workability: request.additionalParams?.workability || 'good',
            temperature: request.additionalParams?.temperature || 20,
          },
          availableMaterials: request.availableMaterials,
        };

        const result = await generateConcreteRatio(genkitRequest);

        return {
          success: true,
          data: result,
          message: 'AI配比生成成功',
          timestamp: new Date().toISOString(),
        };
      } catch (error) {
        console.error('AI配比生成失败，使用备用方案:', error);

        // 如果AI生成失败，返回备用配比
        return this.generateFallbackRatioResponse(request);
      }
    } else {
      // 客户端：直接返回备用配比（避免导入Node.js模块）
      console.warn('客户端环境，使用备用配比生成');
      return this.generateFallbackRatioResponse(request);
    }
  }

  private generateFallbackRatioResponse(request: any): any {
    const materials = this.generateFallbackMaterials(request);
    const totalWeight = materials.reduce((sum, material) => sum + material.theoreticalAmount, 0);

    // 提取强度数值
    const strengthMatch = request.targetStrength.match(/\d+/);
    const targetStrengthValue = strengthMatch ? parseInt(strengthMatch[0]) : 30;

    return {
      success: true,
      data: {
        ratioName: `${request.targetStrength}备用配比方案`,
        description: '基于工程经验的标准配比，建议进行试配验证',
        calculationParams: {
          targetStrength: targetStrengthValue,
          slump: request.additionalParams?.slump || 180,
          maxAggregateSize: 25,
          exposureClass: request.environment === 'marine' ? 'XS2' : 'XC1',
          ambientTemperature: request.additionalParams?.temperature || 20,
          relativeHumidity: 65,
          cementTemperature: 20,
          aggregateTemperature: 20,
          waterCementRatio: this.calculateWaterCementRatio(targetStrengthValue),
          sandRatio: 35.0,
          cementContent: materials.find(m => m.name === '水泥')?.theoreticalAmount || 400,
          waterContent: materials.find(m => m.name === '水')?.theoreticalAmount || 185,
          additiveRatio: 1.5,
          flyashRatio: request.costLevel === 'low' ? 20 : 15,
          mineralPowderRatio: request.costLevel === 'high' ? 15 : 10,
        },
        materials: materials,
        calculationResults: {
          totalWeight: totalWeight,
          strengthPrediction: targetStrengthValue,
          qualityScore: 85,
          costEstimate: totalWeight * 0.8,
          carbonFootprint: totalWeight * 0.3,
          warnings: ['这是基于工程经验的备用配比，建议进行试配验证'],
          suggestions: ['建议进行试配验证', '注意控制搅拌时间', '确保材料质量稳定'],
        },
        metadata: {
          generatedBy: 'fallback',
          generatedAt: new Date().toISOString(),
          version: '1.0',
          standards: ['GB50010-2010', 'JGJ55-2011'],
        },
      },
      message: 'AI配比生成成功（备用方案）',
      timestamp: new Date().toISOString(),
    };
  }

  private calculateWaterCementRatio(targetStrength: number): number {
    // 基于强度等级计算合理的水胶比
    if (targetStrength <= 20) return 0.65;
    if (targetStrength <= 25) return 0.6;
    if (targetStrength <= 30) return 0.55;
    if (targetStrength <= 35) return 0.5;
    if (targetStrength <= 40) return 0.45;
    if (targetStrength <= 45) return 0.4;
    if (targetStrength <= 50) return 0.35;
    return 0.3;
  }

  private generateFallbackMaterials(request: any): any[] {
    // 基础材料用量（kg/m³）
    const strengthMap: Record<string, { cement: number; water: number }> = {
      C15: { cement: 280, water: 185 },
      C20: { cement: 330, water: 185 },
      C25: { cement: 370, water: 185 },
      C30: { cement: 420, water: 185 },
      C35: { cement: 460, water: 185 },
      C40: { cement: 500, water: 185 },
      C45: { cement: 540, water: 180 },
      C50: { cement: 580, water: 180 },
    };

    const baseRatio = strengthMap[request.targetStrength] || strengthMap['C30'];
    const cementContent = baseRatio?.cement || 350;
    const waterContent = baseRatio?.water || 175;

    // 计算骨料用量
    const cementVolume = cementContent / 3100;
    const waterVolume = waterContent / 1000;
    const airVolume = 0.045;
    const totalAggregateVolume = 1 - cementVolume - waterVolume - airVolume;
    const sandRatio = 35; // 砂率35%

    const sandVolume = (totalAggregateVolume * sandRatio) / 100;
    const gravelVolume = totalAggregateVolume - sandVolume;
    const sandAmount = Math.round(sandVolume * 2650);
    const gravelAmount = Math.round(gravelVolume * 2700);

    // 外加剂和掺合料用量
    const additiveAmount = Math.round(cementContent * 0.015 * 10) / 10; // 1.5%掺量
    const flyashRatio = request.costLevel === 'low' ? 20 : 15;
    const mineralPowderRatio = request.costLevel === 'high' ? 15 : 10;
    const flyashAmount = Math.round((cementContent * flyashRatio) / 100);
    const mineralPowderAmount = Math.round((cementContent * mineralPowderRatio) / 100);

    const materials = [
      {
        id: 'cement_1',
        materialId: 'cement_po425',
        name: '水泥',
        specification: 'P.O 42.5',
        category: 'CEMENT',
        unit: 'kg',
        density: 3100,
        theoreticalAmount: cementContent,
        actualAmount: cementContent,
        waterContent: 0,
        stoneContent: 0,
        designValue: cementContent,
        siloId: 'silo_cement_1',
        siloName: '1#水泥仓',
        cost: cementContent * 0.45,
        supplier: '海螺水泥',
      },
      {
        id: 'water_1',
        materialId: 'water_drinking',
        name: '水',
        specification: '饮用水',
        category: 'WATER',
        unit: 'kg',
        density: 1000,
        theoreticalAmount: waterContent,
        actualAmount: waterContent,
        waterContent: waterContent,
        stoneContent: 0,
        designValue: waterContent,
        siloId: 'silo_water_1',
        siloName: '1#水罐',
        cost: waterContent * 0.005,
        supplier: '自来水公司',
      },
      {
        id: 'sand_1',
        materialId: 'sand_manufactured',
        name: '砂子',
        specification: '机制砂',
        category: 'AGGREGATE',
        unit: 'kg',
        density: 2650,
        theoreticalAmount: sandAmount,
        actualAmount: sandAmount,
        waterContent: 0,
        stoneContent: 0,
        designValue: sandAmount,
        siloId: 'silo_sand_1',
        siloName: '1#砂仓',
        cost: sandAmount * 0.08,
        supplier: '本地砂场',
      },
      {
        id: 'gravel_1',
        materialId: 'gravel_5_25',
        name: '石子',
        specification: '5-25mm',
        category: 'AGGREGATE',
        unit: 'kg',
        density: 2700,
        theoreticalAmount: gravelAmount,
        actualAmount: gravelAmount,
        waterContent: 0,
        stoneContent: 0,
        designValue: gravelAmount,
        siloId: 'silo_gravel_1',
        siloName: '1#石仓',
        cost: gravelAmount * 0.06,
        supplier: '本地石场',
      },
    ];

    // 添加外加剂
    if (additiveAmount > 0) {
      materials.push({
        id: 'additive_1',
        materialId: 'superplasticizer_pce',
        name: '聚羧酸减水剂',
        specification: 'PCE-40%',
        category: 'ADMIXTURE',
        unit: 'kg',
        density: 1050,
        theoreticalAmount: additiveAmount,
        actualAmount: additiveAmount,
        waterContent: 0,
        stoneContent: 0,
        designValue: additiveAmount,
        siloId: 'silo_additive_1',
        siloName: '1#外加剂罐',
        cost: additiveAmount * 8.5,
        supplier: '建业化工',
      });
    }

    // 添加粉煤灰
    if (flyashAmount > 0) {
      materials.push({
        id: 'flyash_1',
        materialId: 'fly_ash_grade_1',
        name: '粉煤灰',
        specification: 'I级',
        category: 'SUPPLEMENTARY',
        unit: 'kg',
        density: 2200,
        theoreticalAmount: flyashAmount,
        actualAmount: flyashAmount,
        waterContent: 0,
        stoneContent: 0,
        designValue: flyashAmount,
        siloId: 'silo_flyash_1',
        siloName: '1#粉煤灰仓',
        cost: flyashAmount * 0.15,
        supplier: '华能电厂',
      });
    }

    // 添加矿粉
    if (mineralPowderAmount > 0) {
      materials.push({
        id: 'mineral_powder_1',
        materialId: 'slag_powder_s95',
        name: '矿粉',
        specification: 'S95',
        category: 'SUPPLEMENTARY',
        unit: 'kg',
        density: 2900,
        theoreticalAmount: mineralPowderAmount,
        actualAmount: mineralPowderAmount,
        waterContent: 0,
        stoneContent: 0,
        designValue: mineralPowderAmount,
        siloId: 'silo_mineral_1',
        siloName: '1#矿粉仓',
        cost: mineralPowderAmount * 0.25,
        supplier: '首钢矿业',
      });
    }

    return materials;
  }

  private extractMaterialAmount(materials: any[], materialName: string): number {
    const material = materials.find((m: any) => m.name?.includes(materialName));
    return material?.designValue || 0;
  }
}

// 导出单例实例
export const httpClient = new HttpClient();
