# 类型定义系统迁移指南

## 📋 问题总结

经过全面梳理，发现站点中的类型定义系统存在以下问题：

### 1. 🔄 重复定义问题
- **ExposureClass 枚举**：在 4 个文件中重复定义
- **MaterialCategory 枚举**：在 3 个文件中重复定义  
- **PlacementMethod 枚举**：在 2 个文件中重复定义
- **其他枚举**：多个枚举存在重复定义

### 2. ❌ 缺少字段问题
- **RatioCalculationParams 接口**：缺少必需的外加剂用量字段
- **Vehicle 接口**：字段定义不完整，与实际使用不匹配
- **Hook 返回类型**：与接口定义不一致

### 3. 🔢 常量硬编码问题
- 大量魔法数字散布在代码中
- 硬编码字符串缺少统一管理
- 缺少系统级常量定义

### 4. 🚫 缺少类型定义问题
- 许多组件使用 `any` 类型
- Hook 缺少明确的返回类型定义
- 工具函数缺少类型约束

### 5. ⚠️ 类型不匹配问题
- Hook 实际返回与接口定义不符
- 组件属性类型不一致
- 枚举值与实际使用不匹配

## 🎯 解决方案

### 新的类型系统架构

```
src/types/
├── core-enums.ts          # 核心枚举定义（统一版本）
├── core-interfaces.ts     # 核心接口定义（完整版本）
├── utility-types.ts       # 工具类型定义
├── hook-types.ts          # Hook 类型定义
├── unified-types.ts       # 统一导出入口
└── legacy/                # 旧类型文件（逐步废弃）

src/constants/
└── system-constants.ts    # 系统常量定义
```

### 核心改进

1. **统一枚举管理** (`core-enums.ts`)
   - 所有枚举集中定义
   - 提供显示名称映射
   - 包含验证和工具函数

2. **完整接口定义** (`core-interfaces.ts`)
   - 统一的 `UnifiedVehicle`, `UnifiedTask`, `UnifiedPlant` 接口
   - 完整的 `RatioCalculationParams` 接口（包含所有必需字段）
   - 提供默认值常量

3. **工具类型系统** (`utility-types.ts`)
   - 通用工具类型（`Optional`, `DeepPartial` 等）
   - 组件属性类型
   - 状态管理类型
   - 类型守卫函数

4. **Hook 类型规范** (`hook-types.ts`)
   - 所有自定义 Hook 的返回类型定义
   - 统一的 Hook 配置类型
   - 类型安全的 Hook 接口

5. **系统常量管理** (`system-constants.ts`)
   - 数值常量（范围、默认值）
   - 字符串常量（错误消息、状态文本）
   - 配置常量（API 端点、存储键名）

## 🚀 迁移步骤

### 第一阶段：基础设施建设 ✅
- [x] 创建新的类型文件结构
- [x] 定义核心枚举和接口
- [x] 建立常量管理系统
- [x] 创建工具类型库

### 第二阶段：逐步迁移
1. **更新 Hook 定义**
   ```typescript
   // 旧方式
   export function useRatioCalculation(): any {
     // ...
   }
   
   // 新方式
   export function useRatioCalculation(): UseRatioCalculationReturn {
     // ...
   }
   ```

2. **更新组件类型**
   ```typescript
   // 旧方式
   interface Props {
     vehicle: any;
     onSelect: (vehicle: any) => void;
   }
   
   // 新方式
   interface Props {
     vehicle: UnifiedVehicle;
     onSelect: (vehicle: UnifiedVehicle) => void;
   }
   ```

3. **替换硬编码常量**
   ```typescript
   // 旧方式
   if (strength < 10 || strength > 100) {
     throw new Error('强度值无效');
   }
   
   // 新方式
   import { RATIO_CONSTANTS, ERROR_MESSAGES } from '@/constants/system-constants';
   
   if (strength < RATIO_CONSTANTS.MIN_STRENGTH || strength > RATIO_CONSTANTS.MAX_STRENGTH) {
     throw new Error(ERROR_MESSAGES.INVALID_STRENGTH);
   }
   ```

### 第三阶段：清理和优化
1. 删除重复的类型定义
2. 移除 `any` 类型的使用
3. 统一导入路径
4. 添加类型验证

## 📖 使用指南

### 导入新类型系统

```typescript
// 统一导入入口
import type { 
  Vehicle, 
  Task, 
  RatioParams,
  RatioCalculationHook 
} from '@/types/unified-types';

import { 
  VehicleStatus, 
  MaterialCategory,
  RATIO_CONSTANTS,
  ERROR_MESSAGES 
} from '@/types/unified-types';
```

### Hook 类型使用

```typescript
// Hook 定义
export function useRatioCalculation(): UseRatioCalculationReturn {
  // 实现...
  return {
    calculationParams,
    reverseParams,
    proportions,
    isCalculating,
    error,
    calculationMethod,
    setCalculationParams,
    setReverseParam,
    setCalculationMethod,
    calculate,
    reverseCalculate,
    reset,
    validateParams,
    predictStrength,
  };
}

// Hook 使用
const ratioCalculation: UseRatioCalculationReturn = useRatioCalculation();
```

### 组件类型使用

```typescript
interface VehicleCardProps extends BaseComponentProps, ClickableProps {
  vehicle: UnifiedVehicle;
  selected?: boolean;
  onSelect?: (vehicle: UnifiedVehicle) => void;
  onDispatch?: (vehicle: UnifiedVehicle, task: UnifiedTask) => void;
}

const VehicleCard: React.FC<VehicleCardProps> = ({ 
  vehicle, 
  selected, 
  onSelect,
  ...props 
}) => {
  // 实现...
};
```

### 常量使用

```typescript
// 验证配比参数
function validateRatioParams(params: RatioCalculationParams): ValidationResult {
  const errors: string[] = [];
  
  if (params.targetStrength < RATIO_CONSTANTS.MIN_STRENGTH) {
    errors.push(ERROR_MESSAGES.INVALID_STRENGTH);
  }
  
  if (params.slump < RATIO_CONSTANTS.MIN_SLUMP) {
    errors.push(ERROR_MESSAGES.INVALID_SLUMP);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings: [],
  };
}
```

### 类型验证

```typescript
// 使用类型守卫
function processVehicle(data: unknown) {
  if (isValidVehicle(data)) {
    // data 现在是 UnifiedVehicle 类型
    console.log(data.vehicleNumber);
  }
}

// 使用枚举验证
function setVehicleStatus(vehicle: UnifiedVehicle, status: string) {
  if (isValidEnumValue(VehicleStatus, status)) {
    vehicle.status = status; // 类型安全
  }
}
```

## 🔧 工具和辅助

### VSCode 配置
在 `.vscode/settings.json` 中添加：
```json
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

### ESLint 规则
```javascript
// .eslintrc.js
module.exports = {
  rules: {
    '@typescript-eslint/no-explicit-any': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-unused-vars': 'error',
  }
};
```

## 📊 迁移进度跟踪

### 已完成 ✅
- [x] 核心类型系统架构设计
- [x] 枚举统一定义
- [x] 接口完整定义
- [x] 常量系统建立
- [x] 工具类型库创建

### 进行中 🔄
- [ ] Hook 类型迁移
- [ ] 组件类型更新
- [ ] 硬编码常量替换

### 待完成 📋
- [ ] 旧类型文件清理
- [ ] 导入路径统一
- [ ] 类型验证添加
- [ ] 文档完善

## 🎉 预期收益

1. **类型安全性提升**：减少运行时错误
2. **开发效率提高**：更好的 IDE 支持和自动补全
3. **代码可维护性增强**：统一的类型定义和常量管理
4. **团队协作改善**：明确的类型约定和接口定义
5. **重构风险降低**：类型系统保证重构安全性
