'use client';

/**
 * Service Worker 注册组件
 * 负责注册和管理 Service Worker，提升缓存性能
 */

import { useEffect, useState } from 'react';

interface ServiceWorkerStats {
  isSupported: boolean;
  isRegistered: boolean;
  isActive: boolean;
  cacheStats?: Record<string, { count: number; size: number }>;
}

export default function ServiceWorkerRegistration() {
  const [stats, setStats] = useState<ServiceWorkerStats>({
    isSupported: false,
    isRegistered: false,
    isActive: false,
  });

  useEffect(() => {
    // 检查 Service Worker 支持
    if (!('serviceWorker' in navigator)) {
      console.warn('Service Worker not supported');
      return;
    }

    setStats(prev => ({ ...prev, isSupported: true }));

    // 注册 Service Worker
    registerServiceWorker();

    // 监听 Service Worker 状态变化
    navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange);

    return () => {
      navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
    };
  }, []);

  const registerServiceWorker = async () => {
    try {
      console.log('Registering Service Worker...');

      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
      });

      console.log('Service Worker registered:', registration);
      setStats(prev => ({ ...prev, isRegistered: true }));

      // 监听安装和激活事件
      if (registration.installing) {
        console.log('Service Worker installing...');
        registration.installing.addEventListener('statechange', handleStateChange);
      }

      if (registration.waiting) {
        console.log('Service Worker waiting...');
        registration.waiting.addEventListener('statechange', handleStateChange);
      }

      if (registration.active) {
        console.log('Service Worker active');
        setStats(prev => ({ ...prev, isActive: true }));

        // 获取缓存统计信息
        getCacheStats();
      }

      // 监听更新
      registration.addEventListener('updatefound', () => {
        console.log('Service Worker update found');
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('New Service Worker available');
              // 可以在这里提示用户刷新页面
            }
          });
        }
      });
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  };

  const handleStateChange = (event: Event) => {
    const worker = event.target as ServiceWorker;
    console.log('Service Worker state changed:', worker.state);

    if (worker.state === 'activated') {
      setStats(prev => ({ ...prev, isActive: true }));
      getCacheStats();
    }
  };

  const handleControllerChange = () => {
    console.log('Service Worker controller changed');
    window.location.reload();
  };

  const getCacheStats = async () => {
    if (!navigator.serviceWorker.controller) return;

    try {
      const messageChannel = new MessageChannel();

      const statsPromise = new Promise<Record<string, { count: number; size: number }>>(resolve => {
        messageChannel.port1.onmessage = event => {
          if (event.data.type === 'CACHE_STATS') {
            resolve(event.data.payload);
          }
        };
      });

      navigator.serviceWorker.controller.postMessage({ type: 'GET_CACHE_STATS' }, [
        messageChannel.port2,
      ]);

      const cacheStats = await statsPromise;
      setStats(prev => ({ ...prev, cacheStats }));

      console.log('Cache stats:', cacheStats);
    } catch (error) {
      console.warn('Failed to get cache stats:', error);
    }
  };

  // 开发环境下显示 Service Worker 状态
  // if (process.env.NODE_ENV === 'development') {
  //   return (
  //     <div className="fixed bottom-2 left-2 z-50 text-xs bg-black/80 text-white p-2 rounded max-w-xs">
  //       <div className="font-mono">
  //         <div>SW: {stats.isSupported ? '✓' : '✗'} Supported</div>
  //         <div>Reg: {stats.isRegistered ? '✓' : '✗'} Registered</div>
  //         <div>Active: {stats.isActive ? '✓' : '✗'} Active</div>
  //         {stats.cacheStats && (
  //           <div className="mt-1">
  //             Cache: {Object.keys(stats.cacheStats).length} stores
  //           </div>
  //         )}
  //       </div>
  //     </div>
  //   );
  // }

  return null;
}

// ==================== 缓存管理工具函数 ====================

/**
 * 清理缓存
 */
export const clearServiceWorkerCache = async (cacheName?: string): Promise<void> => {
  if (!navigator.serviceWorker.controller) {
    throw new Error('Service Worker not active');
  }

  const messageChannel = new MessageChannel();

  return new Promise((resolve, reject) => {
    messageChannel.port1.onmessage = event => {
      if (event.data.type === 'CACHE_CLEARED') {
        resolve();
      } else {
        reject(new Error('Failed to clear cache'));
      }
    };

    navigator.serviceWorker.controller!.postMessage(
      { type: 'CLEAR_CACHE', payload: { cacheName } },
      [messageChannel.port2]
    );
  });
};

/**
 * 获取缓存统计信息
 */
export const getServiceWorkerCacheStats = async (): Promise<
  Record<string, { count: number; size: number }>
> => {
  if (!navigator.serviceWorker.controller) {
    throw new Error('Service Worker not active');
  }

  const messageChannel = new MessageChannel();

  return new Promise((resolve, reject) => {
    messageChannel.port1.onmessage = event => {
      if (event.data.type === 'CACHE_STATS') {
        resolve(event.data.payload);
      } else {
        reject(new Error('Failed to get cache stats'));
      }
    };

    navigator.serviceWorker.controller!.postMessage({ type: 'GET_CACHE_STATS' }, [
      messageChannel.port2,
    ]);
  });
};

/**
 * 强制更新 Service Worker
 */
export const updateServiceWorker = async (): Promise<void> => {
  if (!('serviceWorker' in navigator)) {
    throw new Error('Service Worker not supported');
  }

  const registration = await navigator.serviceWorker.getRegistration();
  if (!registration) {
    throw new Error('Service Worker not registered');
  }

  await registration.update();

  if (registration.waiting) {
    registration.waiting.postMessage({ type: 'SKIP_WAITING' });
  }
};

/**
 * 预缓存关键资源
 */
export const precacheResources = async (urls: string[]): Promise<void> => {
  if (!('caches' in window)) {
    throw new Error('Cache API not supported');
  }

  const cache = await caches.open('tmh-precache-v1');
  await cache.addAll(urls);

  console.log('Precached resources:', urls);
};
