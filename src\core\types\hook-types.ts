/**
 * Hook 类型定义
 * 统一管理所有自定义Hook的类型定义
 */

import {
  RatioCalculationParams,
  UnifiedRatioMaterial,
  UnifiedVehicle,
  UnifiedTask,
} from './core-interfaces';
import {
  AsyncState,
  FormState,
  SelectionState,
  PaginationProps,
  SortConfig,
  FilterConfig,
} from './utility-types';

// ==================== 配比相关Hook类型 ====================

/**
 * 配比计算Hook返回类型
 */
export interface UseRatioCalculationReturn {
  // 状态
  calculationParams: RatioCalculationParams;
  reverseParams: ReverseCalculationParams;
  proportions: RatioProportions;
  isCalculating: boolean;
  error: string | null;
  calculationMethod: CalculationMethod;

  // 操作方法
  setCalculationParams: (params: Partial<RatioCalculationParams>) => void;
  setReverseParam: <K extends keyof ReverseCalculationParams>(
    key: K,
    value: ReverseCalculationParams[K]
  ) => void;
  setCalculationMethod: (method: CalculationMethod) => void;
  calculate: () => void;
  reverseCalculate: () => void;
  reset: () => void;

  // 工具方法
  validateParams: (params: Partial<RatioCalculationParams>) => ValidationResult;
  predictStrength: (params: RatioCalculationParams) => number;
}

/**
 * 配比表单Hook返回类型
 */
export interface UseRatioFormReturn {
  // 表单状态
  form: any; // React Hook Form 实例
  formState: FormState<RatioCalculationParams>;

  // 操作方法
  handleSubmit: (
    onSubmit: (data: RatioCalculationParams) => void
  ) => (e?: React.BaseSyntheticEvent) => void;
  reset: (values?: Partial<RatioCalculationParams>) => void;
  setValue: <K extends keyof RatioCalculationParams>(
    name: K,
    value: RatioCalculationParams[K]
  ) => void;
  getValue: <K extends keyof RatioCalculationParams>(name: K) => RatioCalculationParams[K];
  watch: (names?: keyof RatioCalculationParams | (keyof RatioCalculationParams)[]) => any;
  trigger: (
    names?: keyof RatioCalculationParams | (keyof RatioCalculationParams)[]
  ) => Promise<boolean>;

  // 验证方法
  validateField: (name: keyof RatioCalculationParams) => Promise<boolean>;
  validateForm: () => Promise<boolean>;

  // 状态查询
  isDirty: boolean;
  isValid: boolean;
  isSubmitting: boolean;
  errors: Partial<Record<keyof RatioCalculationParams, string>>;
}

/**
 * 配比材料管理Hook返回类型
 */
export interface UseRatioMaterialsReturn {
  // 状态
  materials: UnifiedRatioMaterial[];
  selectedMaterials: UnifiedRatioMaterial[];
  isLoading: boolean;
  error: string | null;

  // 操作方法
  addMaterial: (material: Omit<UnifiedRatioMaterial, 'id'>) => void;
  removeMaterial: (id: string) => void;
  updateMaterial: (id: string, updates: Partial<UnifiedRatioMaterial>) => void;
  selectMaterial: (id: string) => void;
  deselectMaterial: (id: string) => void;
  clearSelection: () => void;

  // 查询方法
  getMaterialById: (id: string) => UnifiedRatioMaterial | undefined;
  getMaterialsByCategory: (category: string) => UnifiedRatioMaterial[];

  // 计算方法
  calculateTotalAmount: () => number;
  calculateTotalCost: () => number;
}

/**
 * 统一配比管理Hook返回类型
 */
export interface UseUnifiedRatioManagementReturn {
  // 配比计算
  calculation: UseRatioCalculationReturn;

  // 表单管理
  form: UseRatioFormReturn;

  // 材料管理
  materials: UseRatioMaterialsReturn;

  // 统一操作
  saveRatio: (name: string) => Promise<void>;
  loadRatio: (id: string) => Promise<void>;
  clearRatio: () => void;
  exportRatio: () => void;

  // 状态
  isSaving: boolean;
  isLoading: boolean;
  error: string | null;
}

// ==================== 车辆相关Hook类型 ====================

/**
 * 车辆管理Hook返回类型
 */
export interface UseVehicleManagementReturn {
  // 状态
  vehicles: UnifiedVehicle[];
  selectedVehicles: UnifiedVehicle[];
  asyncState: AsyncState<UnifiedVehicle[]>;

  // 操作方法
  dispatchVehicle: (vehicleId: string, taskId: string) => Promise<void>;
  returnVehicle: (vehicleId: string) => Promise<void>;
  updateVehicleStatus: (vehicleId: string, status: string) => Promise<void>;

  // 查询方法
  getVehicleById: (id: string) => UnifiedVehicle | undefined;
  getAvailableVehicles: () => UnifiedVehicle[];
  getVehiclesByStatus: (status: string) => UnifiedVehicle[];

  // 刷新方法
  refreshVehicles: () => Promise<void>;
}

// ==================== 任务相关Hook类型 ====================

/**
 * 任务管理Hook返回类型
 */
export interface UseTaskManagementReturn {
  // 状态
  tasks: UnifiedTask[];
  selectedTasks: UnifiedTask[];
  asyncState: AsyncState<UnifiedTask[]>;

  // 操作方法
  createTask: (task: Omit<UnifiedTask, 'id'>) => Promise<void>;
  updateTask: (id: string, updates: Partial<UnifiedTask>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
  completeTask: (id: string) => Promise<void>;
  cancelTask: (id: string) => Promise<void>;

  // 查询方法
  getTaskById: (id: string) => UnifiedTask | undefined;
  getTasksByStatus: (status: string) => UnifiedTask[];
  getDueTasks: () => UnifiedTask[];

  // 刷新方法
  refreshTasks: () => Promise<void>;
}

// ==================== 表格相关Hook类型 ====================

/**
 * 表格Hook返回类型
 */
export interface UseTableReturn<T = any> {
  // 数据状态
  data: T[];
  filteredData: T[];
  paginatedData: T[];

  // 分页状态
  pagination: PaginationProps;

  // 排序状态
  sorting: SortConfig<T> | null;

  // 过滤状态
  filters: FilterConfig<T>[];

  // 选择状态
  selection: SelectionState<T>;

  // 操作方法
  setPagination: (pagination: Partial<PaginationProps>) => void;
  setSorting: (sorting: SortConfig<T> | null) => void;
  addFilter: (filter: FilterConfig<T>) => void;
  removeFilter: (field: keyof T) => void;
  clearFilters: () => void;

  // 选择方法
  selectItem: (item: T) => void;
  deselectItem: (item: T) => void;
  selectAll: () => void;
  deselectAll: () => void;
  toggleSelection: (item: T) => void;

  // 查询方法
  isSelected: (item: T) => boolean;
  getSelectedItems: () => T[];
}

// ==================== 表单相关Hook类型 ====================

/**
 * 表单验证Hook返回类型
 */
export interface UseFormValidationReturn<T = any> {
  // 验证状态
  errors: Partial<Record<keyof T, string>>;
  isValid: boolean;
  isValidating: boolean;

  // 验证方法
  validate: (values: T) => Promise<boolean>;
  validateField: (field: keyof T, value: any) => Promise<boolean>;
  clearErrors: () => void;
  setError: (field: keyof T, message: string) => void;

  // 验证规则
  addRule: (field: keyof T, rule: ValidationRule) => void;
  removeRule: (field: keyof T, ruleName: string) => void;
}

// ==================== 支持类型 ====================

/**
 * 反算参数类型
 */
export interface ReverseCalculationParams {
  targetVolume: number;
  availableMaterials: string[];
  constraints: {
    maxCementContent?: number;
    minWaterCementRatio?: number;
    maxWaterCementRatio?: number;
  };
}

/**
 * 配比比例结果类型
 */
export interface RatioProportions {
  cement: number;
  water: number;
  sand: number;
  stone: number;
  admixture: number;
  flyAsh: number;
  mineralPowder: number;
  totalWeight: number;
  volume: number;
}

/**
 * 计算方法类型
 */
export type CalculationMethod = 'absolute_volume' | 'weight_method' | 'empirical';

/**
 * 验证结果类型
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 验证规则类型
 */
export interface ValidationRule {
  name: string;
  validator: (value: any) => boolean | string;
  message?: string;
  required?: boolean;
}

// ==================== Hook配置类型 ====================

/**
 * 异步Hook配置类型
 */
export interface AsyncHookConfig {
  immediate?: boolean; // 是否立即执行
  retryCount?: number; // 重试次数
  retryDelay?: number; // 重试延迟（毫秒）
  cacheKey?: string; // 缓存键
  cacheTTL?: number; // 缓存生存时间（毫秒）
}

/**
 * 表格Hook配置类型
 */
export interface TableHookConfig<T = any> {
  initialPageSize?: number;
  initialSorting?: SortConfig<T>;
  initialFilters?: FilterConfig<T>[];
  enableSelection?: boolean;
  enableMultiSelection?: boolean;
  rowKey?: keyof T | ((record: T) => string);
}

/**
 * 表单Hook配置类型
 */
export interface FormHookConfig<T = any> {
  initialValues?: Partial<T>;
  validationMode?: 'onChange' | 'onBlur' | 'onSubmit';
  reValidateMode?: 'onChange' | 'onBlur' | 'onSubmit';
  shouldFocusError?: boolean;
  shouldUnregister?: boolean;
}

// ==================== 类型导出 ====================

export type {
  // 基础类型重导出
  RatioCalculationParams,
  UnifiedRatioMaterial,
  UnifiedVehicle,
  UnifiedTask,

  // 工具类型重导出
  AsyncState,
  FormState,
  SelectionState,
  PaginationProps,
  SortConfig,
  FilterConfig,
};
