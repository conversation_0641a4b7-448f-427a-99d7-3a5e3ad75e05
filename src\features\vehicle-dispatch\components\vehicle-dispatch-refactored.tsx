// src/components/sections/vehicle-dispatch-refactored.tsx
'use client';

/**
 * 重构后的车辆调度组件 - 使用分层架构
 *
 * 架构说明：
 * - 使用容器组件模式，将业务逻辑分离到Hook中
 * - UI组件只负责展示和用户交互
 * - 业务逻辑和数据操作通过专门的Hook封装
 * - 拖拽逻辑通过专门的Hook处理
 */
import { VehicleDispatchContainer } from './VehicleDispatchContainer';

/**
 * 重构后的车辆调度主组件
 * 现在只是一个简单的容器，所有业务逻辑都已分离到专门的Hook和组件中
 */
export function VehicleDispatch() {
  return <VehicleDispatchContainer />;
}
