#!/usr/bin/env node

/**
 * 修复样式失效问题
 * 检查和修复CSS导入路径、Tailwind配置等
 */

const fs = require('fs');
const path = require('path');

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

/**
 * 检查样式文件
 */
function checkStyleFiles() {
  console.log('🎨 检查样式文件...\n');

  const styleFiles = [
    'src/shared/styles/sticky-columns.css',
    'src/shared/styles/task-card-performance.css',
    'src/shared/styles/globals.css',
    'src/app/globals.css',
  ];

  const missingFiles = [];

  styleFiles.forEach(file => {
    if (checkFileExists(file)) {
      console.log(`✅ ${file} - 存在`);
    } else {
      console.log(`❌ ${file} - 缺失`);
      missingFiles.push(file);
    }
  });

  return missingFiles;
}

/**
 * 检查Tailwind配置
 */
function checkTailwindConfig() {
  console.log('\n🎯 检查Tailwind配置...\n');

  const configPath = 'tailwind.config.ts';
  if (!checkFileExists(configPath)) {
    console.log('❌ tailwind.config.ts 不存在');
    return false;
  }

  const content = fs.readFileSync(configPath, 'utf8');

  // 检查content路径
  const requiredPaths = [
    './src/features/**/*.{js,ts,jsx,tsx,mdx}',
    './src/shared/**/*.{js,ts,jsx,tsx,mdx}',
    './src/core/**/*.{js,ts,jsx,tsx,mdx}',
  ];

  let allPathsPresent = true;
  requiredPaths.forEach(requiredPath => {
    if (content.includes(requiredPath)) {
      console.log(`✅ ${requiredPath} - 已配置`);
    } else {
      console.log(`❌ ${requiredPath} - 缺失`);
      allPathsPresent = false;
    }
  });

  return allPathsPresent;
}

/**
 * 检查TypeScript路径映射
 */
function checkTSConfig() {
  console.log('\n📝 检查TypeScript配置...\n');

  const configPath = 'tsconfig.json';
  if (!checkFileExists(configPath)) {
    console.log('❌ tsconfig.json 不存在');
    return false;
  }

  const content = fs.readFileSync(configPath, 'utf8');

  // 检查路径映射
  const requiredPaths = ['@/features/*', '@/shared/*', '@/core/*', '@/infrastructure/*'];

  let allPathsPresent = true;
  requiredPaths.forEach(requiredPath => {
    if (content.includes(`"${requiredPath}"`)) {
      console.log(`✅ ${requiredPath} - 已配置`);
    } else {
      console.log(`❌ ${requiredPath} - 缺失`);
      allPathsPresent = false;
    }
  });

  return allPathsPresent;
}

/**
 * 检查关键组件
 */
function checkKeyComponents() {
  console.log('\n🧩 检查关键组件...\n');

  const keyComponents = [
    'src/shared/components/card.tsx',
    'src/shared/components/button.tsx',
    'src/shared/components/badge.tsx',
    'src/shared/components/toaster.tsx',
    'src/core/lib/utils.ts',
    'src/core/contexts/theme-provider.tsx',
  ];

  const missingComponents = [];

  keyComponents.forEach(component => {
    if (checkFileExists(component)) {
      console.log(`✅ ${component} - 存在`);
    } else {
      console.log(`❌ ${component} - 缺失`);
      missingComponents.push(component);
    }
  });

  return missingComponents;
}

/**
 * 生成修复建议
 */
function generateFixSuggestions(missingFiles, missingComponents) {
  console.log('\n🔧 修复建议:\n');

  if (missingFiles.length > 0) {
    console.log('📁 缺失的样式文件:');
    missingFiles.forEach(file => {
      console.log(`   - 需要创建或移动: ${file}`);
    });
    console.log('');
  }

  if (missingComponents.length > 0) {
    console.log('🧩 缺失的组件:');
    missingComponents.forEach(component => {
      console.log(`   - 需要创建或移动: ${component}`);
    });
    console.log('');
  }

  console.log('🚀 快速修复步骤:');
  console.log('1. 运行: npm run dev 检查控制台错误');
  console.log('2. 确保所有样式文件已移动到 src/shared/styles/');
  console.log('3. 确保所有UI组件已移动到 src/shared/components/');
  console.log('4. 检查 globals.css 中的 @import 路径');
  console.log('5. 重启开发服务器');
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 开始样式诊断...\n');

  const missingFiles = checkStyleFiles();
  const tailwindOK = checkTailwindConfig();
  const tsconfigOK = checkTSConfig();
  const missingComponents = checkKeyComponents();

  console.log('\n📊 诊断结果:');
  console.log(`   样式文件: ${missingFiles.length === 0 ? '✅ 正常' : '❌ 有问题'}`);
  console.log(`   Tailwind配置: ${tailwindOK ? '✅ 正常' : '❌ 有问题'}`);
  console.log(`   TypeScript配置: ${tsconfigOK ? '✅ 正常' : '❌ 有问题'}`);
  console.log(`   关键组件: ${missingComponents.length === 0 ? '✅ 正常' : '❌ 有问题'}`);

  if (missingFiles.length > 0 || missingComponents.length > 0 || !tailwindOK || !tsconfigOK) {
    generateFixSuggestions(missingFiles, missingComponents);
  } else {
    console.log('\n🎉 所有检查都通过了！样式应该正常工作。');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { checkStyleFiles, checkTailwindConfig, checkTSConfig };
