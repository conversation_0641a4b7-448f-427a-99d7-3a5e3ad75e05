/**
 * 统一持久化管理器
 * 负责管理所有应用配置的持久化，包括样式、主题、布局等
 */

import type { TaskCardConfig } from '@/core/types/taskCardConfig';
import type { TaskListStoredSettings } from '@/core/types';
import type { Theme, DensityMode } from '@/shared/components/contexts/theme-provider';

// 存储键名常量
export const STORAGE_KEYS = {
  // 任务列表相关
  TASK_LIST_SETTINGS: 'taskListSettings_v3.3',
  TASK_CARD_CONFIG: 'taskCardConfig_v1.0',
  TASK_CARD_LAYOUT: 'taskCardLayoutConfig',

  // 主题相关
  APP_THEME: 'app-theme',
  APP_DENSITY: 'app-density',

  // 表格相关
  TABLE_COLUMN_WIDTHS: 'tableColumnWidths_v1.0',
  TABLE_COLUMN_ORDER: 'tableColumnOrder_v1.0',
  TABLE_COLUMN_VISIBILITY: 'tableColumnVisibility_v1.0',
  TABLE_COLUMN_STYLES: 'tableColumnStyles_v1.0',

  // 卡片相关
  CARD_GRID_CONFIG: 'cardGridConfig_v1.0',
  CARD_VEHICLE_STYLES: 'cardVehicleStyles_v1.0',

  // 分组相关
  GROUPING_CONFIG: 'groupingConfig_v1.0',

  // 其他配置
  UI_PREFERENCES: 'uiPreferences_v1.0',
  PERFORMANCE_SETTINGS: 'performanceSettings_v1.0',
} as const;

// 配置接口
export interface PersistenceConfig {
  // 任务列表设置
  taskListSettings?: TaskListStoredSettings;

  // 任务卡片配置
  taskCardConfig?: TaskCardConfig;

  // 主题配置
  theme?: Theme;
  density?: DensityMode;

  // 表格配置
  tableConfig?: {
    columnWidths: Record<string, number>;
    columnOrder: string[];
    columnVisibility: Record<string, boolean>;
    columnStyles: Record<string, any>;
  };

  // 卡片配置
  cardConfig?: {
    gridConfig: any;
    vehicleStyles: any;
  };

  // 分组配置
  groupingConfig?: any;

  // UI偏好
  uiPreferences?: {
    floatingHeaderPosition?: { x: number; y: number };
    sidebarCollapsed?: boolean;
    vehicleDispatchPosition?: { x: number; y: number };
  };

  // 性能设置
  performanceSettings?: {
    enableVirtualization?: boolean;
    maxRenderItems?: number;
    debounceDelay?: number;
  };
}

/**
 * 统一持久化管理器类
 */
export class PersistenceManager {
  /**
   * 保存配置到 localStorage
   */
  static save<T>(key: keyof typeof STORAGE_KEYS, data: T): boolean {
    if (typeof window === 'undefined') {
      console.warn('服务器端无法保存配置');
      return false;
    }

    try {
      const storageKey = STORAGE_KEYS[key];
      const serializedData = JSON.stringify(data);

      // 在设置新值之前获取旧值
      const oldValue = localStorage.getItem(storageKey);

      // 设置新值
      localStorage.setItem(storageKey, serializedData);

      // 强制触发storage事件，确保其他标签页也能收到更新
      window.dispatchEvent(
        new StorageEvent('storage', {
          key: storageKey,
          newValue: serializedData,
          oldValue: oldValue,
          storageArea: localStorage,
        })
      );

      console.log(`✅ 配置已保存: ${key} (${serializedData.length} 字符)`);
      return true;
    } catch (error) {
      console.error(`❌ 配置保存失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 从 localStorage 加载配置
   */
  static load<T>(key: keyof typeof STORAGE_KEYS, defaultValue: T): T {
    if (typeof window === 'undefined') {
      console.log(`📝 服务器端使用默认配置: ${key}`);
      return defaultValue;
    }

    try {
      const storageKey = STORAGE_KEYS[key];
      const serializedData = localStorage.getItem(storageKey);

      if (serializedData === null) {
        console.log(`📝 使用默认配置: ${key}`);
        return defaultValue;
      }

      const parsedData = JSON.parse(serializedData);
      console.log(`✅ 配置已加载: ${key} (${serializedData.length} 字符)`);
      return parsedData;
    } catch (error) {
      console.error(`❌ 配置加载失败: ${key}`, error);
      return defaultValue;
    }
  }

  /**
   * 删除配置
   */
  static remove(key: keyof typeof STORAGE_KEYS): boolean {
    try {
      const storageKey = STORAGE_KEYS[key];
      localStorage.removeItem(storageKey);
      console.log(`🗑️ 配置已删除: ${key}`);
      return true;
    } catch (error) {
      console.error(`❌ 配置删除失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 检查配置是否存在
   */
  static exists(key: keyof typeof STORAGE_KEYS): boolean {
    if (typeof window === 'undefined') {
      return false;
    }

    try {
      const storageKey = STORAGE_KEYS[key];
      return localStorage.getItem(storageKey) !== null;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取配置大小（字节）
   */
  static getSize(key: keyof typeof STORAGE_KEYS): number {
    if (typeof window === 'undefined') {
      return 0;
    }

    try {
      const storageKey = STORAGE_KEYS[key];
      const data = localStorage.getItem(storageKey);
      return data ? new Blob([data]).size : 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 批量保存配置
   */
  static saveBatch(configs: Partial<Record<keyof typeof STORAGE_KEYS, any>>): boolean {
    let allSuccess = true;

    Object.entries(configs).forEach(([key, value]) => {
      const success = this.save(key as keyof typeof STORAGE_KEYS, value);
      if (!success) allSuccess = false;
    });

    return allSuccess;
  }

  /**
   * 批量加载配置
   */
  static loadBatch<T extends Partial<Record<keyof typeof STORAGE_KEYS, any>>>(
    keys: (keyof typeof STORAGE_KEYS)[],
    defaults: T
  ): T {
    const result = { ...defaults };

    keys.forEach(key => {
      if (defaults[key] !== undefined) {
        result[key] = this.load(key, defaults[key]);
      }
    });

    return result;
  }

  /**
   * 导出所有配置
   */
  static exportAll(): PersistenceConfig {
    const config: PersistenceConfig = {};

    Object.keys(STORAGE_KEYS).forEach(key => {
      const storageKey = STORAGE_KEYS[key as keyof typeof STORAGE_KEYS];
      const data = localStorage.getItem(storageKey);
      if (data) {
        try {
          (config as any)[key] = JSON.parse(data);
        } catch (error) {
          console.warn(`导出配置失败: ${key}`, error);
        }
      }
    });

    return config;
  }

  /**
   * 导入所有配置
   */
  static importAll(config: PersistenceConfig): boolean {
    let allSuccess = true;

    Object.entries(config).forEach(([key, value]) => {
      if (value !== undefined && key in STORAGE_KEYS) {
        const success = this.save(key as keyof typeof STORAGE_KEYS, value);
        if (!success) allSuccess = false;
      }
    });

    return allSuccess;
  }

  /**
   * 清除所有配置
   */
  static clearAll(): boolean {
    let allSuccess = true;

    Object.values(STORAGE_KEYS).forEach(storageKey => {
      try {
        localStorage.removeItem(storageKey);
      } catch (error) {
        console.error(`清除配置失败: ${storageKey}`, error);
        allSuccess = false;
      }
    });

    if (allSuccess) {
      console.log('🧹 所有配置已清除');
    }

    return allSuccess;
  }

  /**
   * 获取存储使用情况
   */
  static getStorageInfo(): {
    totalSize: number;
    itemCount: number;
    items: Array<{ key: string; size: number; exists: boolean }>;
  } {
    if (typeof window === 'undefined') {
      // 服务器端返回空数据
      const items = Object.keys(STORAGE_KEYS).map(key => ({
        key,
        size: 0,
        exists: false,
      }));

      return { totalSize: 0, itemCount: 0, items };
    }

    const items = Object.entries(STORAGE_KEYS).map(([key, storageKey]) => ({
      key,
      size: this.getSize(key as keyof typeof STORAGE_KEYS),
      exists: this.exists(key as keyof typeof STORAGE_KEYS),
    }));

    const totalSize = items.reduce((sum, item) => sum + item.size, 0);
    const itemCount = items.filter(item => item.exists).length;

    return { totalSize, itemCount, items };
  }

  /**
   * 创建配置备份
   */
  static createBackup(): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupKey = `backup_${timestamp}`;
    const allConfig = this.exportAll();

    try {
      localStorage.setItem(backupKey, JSON.stringify(allConfig));
      console.log(`💾 配置备份已创建: ${backupKey}`);
      return backupKey;
    } catch (error) {
      console.error('创建备份失败:', error);
      throw error;
    }
  }

  /**
   * 恢复配置备份
   */
  static restoreBackup(backupKey: string): boolean {
    try {
      const backupData = localStorage.getItem(backupKey);
      if (!backupData) {
        throw new Error('备份不存在');
      }

      const config = JSON.parse(backupData);
      const success = this.importAll(config);

      if (success) {
        console.log(`🔄 配置备份已恢复: ${backupKey}`);
      }

      return success;
    } catch (error) {
      console.error('恢复备份失败:', error);
      return false;
    }
  }
}
