/**
 * 通用模态框管理Hook
 * 提供统一的模态框状态管理功能
 */

import { useState, useCallback } from 'react';

/**
 * 模态框状态类型
 */
export type ModalState<T extends Record<string, boolean>> = T;

/**
 * 模态框管理Hook
 */
export function useModalManager<T extends Record<string, boolean>>(initialState: T) {
  const [modals, setModals] = useState<T>(initialState);

  /**
   * 打开指定模态框
   */
  const openModal = useCallback((modalName: keyof T) => {
    setModals(prev => ({ ...prev, [modalName]: true }));
  }, []);

  /**
   * 关闭指定模态框
   */
  const closeModal = useCallback((modalName: keyof T) => {
    setModals(prev => ({ ...prev, [modalName]: false }));
  }, []);

  /**
   * 切换指定模态框状态
   */
  const toggleModal = useCallback((modalName: keyof T) => {
    setModals(prev => ({ ...prev, [modalName]: !prev[modalName] }));
  }, []);

  /**
   * 关闭所有模态框
   */
  const closeAllModals = useCallback(() => {
    const closedState = Object.keys(modals).reduce((acc, key) => {
      acc[key as keyof T] = false as T[keyof T];
      return acc;
    }, {} as T);
    setModals(closedState);
  }, [modals]);

  /**
   * 批量设置模态框状态
   */
  const setModalStates = useCallback((states: Partial<T>) => {
    setModals(prev => ({ ...prev, ...states }));
  }, []);

  /**
   * 检查是否有模态框打开
   */
  const hasOpenModal = useCallback(() => {
    return Object.values(modals).some(Boolean);
  }, [modals]);

  /**
   * 获取打开的模态框列表
   */
  const getOpenModals = useCallback(() => {
    return Object.entries(modals)
      .filter(([, isOpen]) => isOpen)
      .map(([name]) => name);
  }, [modals]);

  return {
    modals,
    openModal,
    closeModal,
    toggleModal,
    closeAllModals,
    setModalStates,
    hasOpenModal,
    getOpenModals,
  };
}
