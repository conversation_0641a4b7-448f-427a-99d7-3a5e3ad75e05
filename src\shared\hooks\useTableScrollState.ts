// src/shared/hooks/useTableScrollState.ts
'use client';

import { useCallback, useEffect, useState } from 'react';

interface ScrollState {
  isAtLeft: boolean;
  isAtRight: boolean;
  scrollLeft: number;
  scrollWidth: number;
  clientWidth: number;
}

/**
 * Hook to track table scroll state
 * 用于跟踪表格滚动状态，特别是用于控制固定列的阴影显示
 */
export function useTableScrollState(containerRef: React.RefObject<HTMLElement>) {
  const [scrollState, setScrollState] = useState<ScrollState>({
    isAtLeft: true,
    isAtRight: false,
    scrollLeft: 0,
    scrollWidth: 0,
    clientWidth: 0,
  });

  const updateScrollState = useCallback(() => {
    const container = containerRef.current;
    if (!container) return;

    const { scrollLeft, scrollWidth, clientWidth } = container;
    const isAtLeft = scrollLeft <= 1; // 允许1px的误差
    const isAtRight = scrollLeft >= scrollWidth - clientWidth - 1; // 允许1px的误差

    setScrollState({
      isAtLeft,
      isAtRight,
      scrollLeft,
      scrollWidth,
      clientWidth,
    });
  }, [containerRef]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 初始化状态
    updateScrollState();

    // 监听滚动事件
    const handleScroll = () => {
      updateScrollState();
    };

    // 监听尺寸变化
    const resizeObserver = new ResizeObserver(() => {
      updateScrollState();
    });

    container.addEventListener('scroll', handleScroll, { passive: true });
    resizeObserver.observe(container);

    return () => {
      container.removeEventListener('scroll', handleScroll);
      resizeObserver.disconnect();
    };
  }, [containerRef, updateScrollState]);

  return scrollState;
}
