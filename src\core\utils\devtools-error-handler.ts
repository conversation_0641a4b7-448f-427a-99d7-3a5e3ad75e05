/**
 * DevTools Error Handler
 * 处理和抑制开发环境中的 React DevTools 相关错误
 */

/**
 * 检查是否为 DevTools 相关错误
 */
function isDevToolsError(error: any): boolean {
  const message = error?.message || error?.toString() || '';

  return (
    message.includes('Cannot add child') ||
    message.includes('parent node was not found in the Store') ||
    message.includes('DevTools') ||
    message.includes('chrome-extension://') ||
    message.includes('fmkadmapgofadopljbjfkapdkoienihi') // React DevTools extension ID
  );
}

/**
 * 全局错误处理器
 */
export function setupDevToolsErrorHandler() {
  // 只在开发环境启用
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  // 处理未捕获的错误
  window.addEventListener('error', event => {
    if (isDevToolsError(event.error)) {
      event.preventDefault();
      event.stopPropagation();
      console.debug('DevTools error suppressed:', event.error?.message);
      return false;
    }
    return true;
  });

  // 处理未捕获的 Promise 拒绝
  window.addEventListener('unhandledrejection', event => {
    if (isDevToolsError(event.reason)) {
      event.preventDefault();
      console.debug('DevTools promise rejection suppressed:', event.reason?.message);
      return false;
    }
    return true;
  });

  // 重写 console.error 以过滤 DevTools 错误
  const originalConsoleError = console.error;
  console.error = (...args) => {
    const message = args[0];

    if (isDevToolsError(message)) {
      console.debug('DevTools console error suppressed:', message);
      return;
    }

    originalConsoleError.apply(console, args);
  };

  console.log('🛠️ DevTools error handler initialized');
}

/**
 * React 错误边界的错误过滤器
 */
export function shouldSuppressError(error: Error): boolean {
  return isDevToolsError(error);
}

/**
 * 清理错误处理器
 */
export function cleanupDevToolsErrorHandler() {
  // 这里可以添加清理逻辑，如果需要的话
  console.log('🧹 DevTools error handler cleaned up');
}
