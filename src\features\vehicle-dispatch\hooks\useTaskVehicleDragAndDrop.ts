'use client';

// src/hooks/useTaskVehicleDragAndDrop.ts
import { useCallback, useState } from 'react';

import { useToast } from '@/shared/hooks/use-toast';
import { useAppStore } from '@/infrastructure/storage/stores/appStore';

export function useTaskVehicleDragAndDrop() {
  const { toast } = useToast();
  const [dragOverTaskId, setDragOverTaskId] = useState<string | null>(null);
  const [dragOverProductionLineId, setDragOverProductionLineId] = useState<string | null>(null);
  const [draggedVehicleId, setDraggedVehicleId] = useState<string | null>(null);
  const [dropTargetId, setDropTargetId] = useState<string | null>(null);

  // 从 appStore 获取数据和方法
  const vehicles = useAppStore(state => state.vehicles);
  const reorderVehiclesInList = useAppStore(state => state.reorderVehiclesInList);

  const handleDragStart = useCallback((e: React.DragEvent<HTMLElement>, vehicleId: string) => {
    e.dataTransfer.setData('text/plain', vehicleId);
    e.dataTransfer.effectAllowed = 'move';
    setDraggedVehicleId(vehicleId);

    // 添加拖动样式
    const element = e.currentTarget;
    element.classList.add('vehicle-card-dragging');
  }, []);

  const handleDragEnd = useCallback((e: React.DragEvent<HTMLElement>) => {
    e.preventDefault();
    const element = e.currentTarget;
    element.classList.remove('vehicle-card-dragging');

    // 清理状态
    setDraggedVehicleId(null);
    setDropTargetId(null);
    setDragOverTaskId(null);
    setDragOverProductionLineId(null);

    // 移除所有让位效果
    document.querySelectorAll('.vehicle-card-spacing').forEach(el => {
      el.classList.remove('vehicle-card-spacing');
    });

    // 移除所有虚线框
    document.querySelectorAll('.vehicle-card-drag-over').forEach(el => {
      el.classList.remove('vehicle-card-drag-over');
    });
  }, []);

  const handleDragOver = useCallback(
    (e: React.DragEvent<HTMLElement>) => {
      e.preventDefault();
      const target = e.target as HTMLElement;
      const vehicleCard = target.closest('.vehicle-card');

      if (vehicleCard && draggedVehicleId) {
        const targetVehicleId = vehicleCard.getAttribute('data-vehicle-id');
        if (targetVehicleId && targetVehicleId !== draggedVehicleId) {
          // 移除之前的所有虚线框和让位效果
          document.querySelectorAll('.vehicle-card-drag-over').forEach(el => {
            el.classList.remove('vehicle-card-drag-over');
          });
          document.querySelectorAll('.vehicle-card-spacing').forEach(el => {
            el.classList.remove('vehicle-card-spacing');
          });

          // 添加新的虚线框
          vehicleCard.classList.add('vehicle-card-drag-over');

          // 获取拖动方向并添加让位效果
          const draggedCard = document.querySelector(`[data-vehicle-id="${draggedVehicleId}"]`);
          if (draggedCard) {
            const draggedRect = draggedCard.getBoundingClientRect();
            const targetRect = vehicleCard.getBoundingClientRect();
            const isMovingDown = draggedRect.top < targetRect.top;

            if (isMovingDown) {
              vehicleCard.nextElementSibling?.classList.add('vehicle-card-spacing');
            } else {
              vehicleCard.classList.add('vehicle-card-spacing');
            }
          }

          setDropTargetId(targetVehicleId);
        }
      }
    },
    [draggedVehicleId]
  );

  const handleDrop = useCallback(
    async (e: React.DragEvent<HTMLElement>) => {
      e.preventDefault();
      const vehicleId = e.dataTransfer.getData('text/plain');

      if (dropTargetId && vehicleId !== dropTargetId) {
        const draggedVehicle = vehicles.find(v => v.id === vehicleId);
        if (draggedVehicle) {
          try {
            // 添加拖拽完成的回弹动画
            const targetCard = document.querySelector(`[data-vehicle-id="${dropTargetId}"]`);
            if (targetCard) {
              targetCard.classList.add('vehicle-card-drop-complete');
              setTimeout(() => {
                targetCard.classList.remove('vehicle-card-drop-complete');
              }, 300);
            }
          } catch (error) {
            toast({
              title: '排序失败',
              description: '无法完成车辆排序，请重试',
              variant: 'destructive',
            });
          }
        }
      }

      // 清理所有拖拽相关的样式
      document.querySelectorAll('.vehicle-card-drag-over, .vehicle-card-spacing').forEach(el => {
        el.classList.remove('vehicle-card-drag-over', 'vehicle-card-spacing');
      });

      setDropTargetId(null);
    },
    [dropTargetId, vehicles, reorderVehiclesInList, toast]
  );

  return {
    dragOverTaskId,
    dragOverProductionLineId,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDrop,
    setDragOverTaskId,
    setDragOverProductionLineId,
  };
}
