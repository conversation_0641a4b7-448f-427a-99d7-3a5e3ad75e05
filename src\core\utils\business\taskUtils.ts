/**
 * 任务相关业务逻辑工具函数
 * 集中管理任务处理的通用逻辑
 */

import type { Task, Vehicle } from '@/core/types';

/**
 * 验证车辆是否可以被调度到任务
 */
export function canDispatchVehicleToTask(vehicle: Vehicle, task: Task): boolean {
  // 车辆必须是可用状态且未被分配
  if (vehicle.assignedTaskId && vehicle.assignedTaskId !== task.id) {
    return false;
  }

  // 车辆状态检查
  if (!['pending', 'returned'].includes(vehicle.status)) {
    return false;
  }

  // 车辆运营状态检查
  if (vehicle.operationalStatus && vehicle.operationalStatus !== 'normal') {
    return false;
  }

  // 任务状态检查
  if (!['InProgress', 'Pending'].includes(task.dispatchStatus)) {
    return false;
  }

  // 检查任务是否还需要车辆
  const maxVehicles = task.vehicleCount || 1;
  const currentVehicles = task.vehicles?.length || 0;
  if (currentVehicles >= maxVehicles) {
    return false;
  }

  return true;
}

/**
 * 验证车辆是否可以被调度（通用检查）
 */
export function canDispatchVehicle(vehicle: Vehicle): boolean {
  return (
    ((!vehicle.assignedTaskId && vehicle.status === 'pending') || vehicle.status === 'returned') &&
    (!vehicle.operationalStatus || vehicle.operationalStatus === 'normal')
  );
}

/**
 * 获取任务的可调度车辆数量
 */
export function getAvailableVehicleCount(task: Task): number {
  const maxVehicles = task.vehicleCount || 1;
  const currentVehicles = task.vehicles?.length || 0;
  return Math.max(0, maxVehicles - currentVehicles);
}

/**
 * 获取任务的完成进度（百分比）
 */
export function getTaskProgress(task: Task): number {
  if (task.requiredVolume <= 0) return 0;
  return Math.min(100, (task.completedVolume / task.requiredVolume) * 100);
}

/**
 * 检查任务是否逾期
 */
export function isTaskOverdue(task: Task): boolean {
  if (!task.supplyDate || !task.supplyTime) return false;

  const supplyDateTime = new Date(`${task.supplyDate} ${task.supplyTime}`);
  const now = new Date();

  return now > supplyDateTime && task.dispatchStatus !== 'Completed';
}

/**
 * 获取任务的优先级
 */
export function getTaskPriority(task: Task): 'high' | 'medium' | 'low' {
  if (isTaskOverdue(task)) return 'high';
  if (task.dispatchStatus === 'InProgress') return 'medium';
  return 'low';
}

/**
 * 获取任务的状态颜色
 */
export function getTaskStatusColor(task: Task): string {
  switch (task.dispatchStatus) {
    case 'Completed':
      return 'green';
    case 'InProgress':
      return 'blue';
    case 'Paused':
      return 'orange';
    case 'Cancelled':
      return 'red';
    default:
      return 'gray';
  }
}

/**
 * 获取任务的剩余时间（分钟）
 */
export function getTaskRemainingTime(task: Task): number | null {
  if (!task.supplyDate || !task.supplyTime) return null;

  const supplyDateTime = new Date(`${task.supplyDate} ${task.supplyTime}`);
  const now = new Date();
  const diffMs = supplyDateTime.getTime() - now.getTime();

  return Math.floor(diffMs / (1000 * 60)); // 转换为分钟
}

/**
 * 格式化任务剩余时间显示
 */
export function formatTaskRemainingTime(task: Task): string {
  const remainingMinutes = getTaskRemainingTime(task);

  if (remainingMinutes === null) return '未设置';
  if (remainingMinutes < 0) return '已逾期';

  const hours = Math.floor(remainingMinutes / 60);
  const minutes = remainingMinutes % 60;

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  }
  return `${minutes}分钟`;
}

/**
 * 检查任务是否需要紧急处理
 */
export function isTaskUrgent(task: Task): boolean {
  const remainingMinutes = getTaskRemainingTime(task);
  if (remainingMinutes === null) return false;

  // 剩余时间少于30分钟或已逾期
  return remainingMinutes <= 30;
}

/**
 * 获取任务的配送地址简称
 */
export function getTaskLocationShort(task: Task): string {
  if (!task.constructionSite) return '未知地址';

  // 提取地址中的关键信息
  const location = task.constructionSite;
  const parts = location.split(/[，,、]/);

  // 返回最后一个部分作为简称
  return parts[parts.length - 1]?.trim() || location;
}

/**
 * 计算任务的预计完成时间
 */
export function getEstimatedCompletionTime(task: Task): Date | null {
  if (!task.supplyDate || !task.supplyTime) return null;

  const startTime = new Date(`${task.supplyDate} ${task.supplyTime}`);
  const progress = getTaskProgress(task);

  if (progress === 0) return null;

  // 基于当前进度估算完成时间
  const elapsedTime = Date.now() - startTime.getTime();
  const totalEstimatedTime = (elapsedTime / progress) * 100;

  return new Date(startTime.getTime() + totalEstimatedTime);
}

/**
 * 获取任务的风险等级
 */
export function getTaskRiskLevel(task: Task): 'low' | 'medium' | 'high' {
  const isOverdue = isTaskOverdue(task);
  const isUrgent = isTaskUrgent(task);
  const progress = getTaskProgress(task);
  const availableVehicles = getAvailableVehicleCount(task);

  // 高风险：逾期或紧急且进度缓慢
  if (isOverdue || (isUrgent && progress < 50)) {
    return 'high';
  }

  // 中风险：紧急或缺少车辆
  if (isUrgent || availableVehicles === 0) {
    return 'medium';
  }

  return 'low';
}

/**
 * 生成任务摘要信息
 */
export function generateTaskSummary(task: Task): {
  status: string;
  progress: number;
  priority: string;
  riskLevel: string;
  remainingTime: string;
  availableVehicles: number;
} {
  return {
    status: task.dispatchStatus,
    progress: getTaskProgress(task),
    priority: getTaskPriority(task),
    riskLevel: getTaskRiskLevel(task),
    remainingTime: formatTaskRemainingTime(task),
    availableVehicles: getAvailableVehicleCount(task),
  };
}

/**
 * 任务排序比较函数
 */
export const taskComparators = {
  byPriority: (a: Task, b: Task): number => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    const aPriority = priorityOrder[getTaskPriority(a)];
    const bPriority = priorityOrder[getTaskPriority(b)];
    return bPriority - aPriority;
  },

  byProgress: (a: Task, b: Task): number => {
    return getTaskProgress(a) - getTaskProgress(b);
  },

  byRemainingTime: (a: Task, b: Task): number => {
    const aTime = getTaskRemainingTime(a) ?? Infinity;
    const bTime = getTaskRemainingTime(b) ?? Infinity;
    return aTime - bTime;
  },

  byRiskLevel: (a: Task, b: Task): number => {
    const riskOrder = { high: 3, medium: 2, low: 1 };
    const aRisk = riskOrder[getTaskRiskLevel(a)];
    const bRisk = riskOrder[getTaskRiskLevel(b)];
    return bRisk - aRisk;
  },
};

/**
 * 任务过滤器
 */
export const taskFilters = {
  overdue: (task: Task): boolean => isTaskOverdue(task),
  urgent: (task: Task): boolean => isTaskUrgent(task),
  highRisk: (task: Task): boolean => getTaskRiskLevel(task) === 'high',
  needsVehicles: (task: Task): boolean => getAvailableVehicleCount(task) > 0,
  inProgress: (task: Task): boolean => task.dispatchStatus === 'InProgress',
  completed: (task: Task): boolean => task.dispatchStatus === 'Completed',
  lowProgress: (task: Task): boolean => getTaskProgress(task) < 30,
};
