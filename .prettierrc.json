{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": false, "plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrder": ["^react$", "^react/(.*)$", "^next/(.*)$", "<THIRD_PARTY_MODULES>", "^@/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "importOrderCaseInsensitive": true}