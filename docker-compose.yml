version: '3.8'

services:
  tmh-app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=https://api.tmh-dispatch.com
      - NEXT_PUBLIC_USE_MOCK_DATA=false
      - NEXT_PUBLIC_APP_NAME=TMH任务调度系统
      - NEXT_PUBLIC_APP_VERSION=1.0.0
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - tmh-network

  # 可选：添加 Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - tmh-app
    restart: unless-stopped
    networks:
      - tmh-network

networks:
  tmh-network:
    driver: bridge

volumes:
  logs:
