/**
 * 核心接口类型定义
 * 统一管理所有系统中的核心接口，确保类型一致性
 */

import {
  MaterialCategory,
  MaterialType,
  ExposureClass,
  PlacementMethod,
  TaskDispatchStatus,
  VehicleStatus,
  VehicleOperationalStatus,
  VehicleProductionStatus,
  Priority,
  AvailabilityStatus,
} from './core-enums';

// ==================== 基础类型 ====================

/**
 * 通用ID类型
 */
export type ID = string;

/**
 * 时间戳类型
 */
export type Timestamp = string;

/**
 * 日期字符串类型 (YYYY-MM-DD)
 */
export type DateString = string;

/**
 * 时间字符串类型 (HH:mm)
 */
export type TimeString = string;

/**
 * 电话号码类型
 */
export type PhoneNumber = string;

/**
 * 车牌号类型
 */
export type LicensePlate = string;

// ==================== 配比相关接口 ====================

/**
 * 配比计算参数接口（完整版本）
 */
export interface RatioCalculationParams {
  // 基本参数
  targetStrength: number; // 目标强度 MPa
  slump: number; // 坍落度 mm
  maxAggregateSize: number; // 最大骨料粒径 mm
  exposureClass: ExposureClass; // 暴露等级

  // 环境参数
  ambientTemperature: number; // 环境温度 °C
  relativeHumidity: number; // 相对湿度 %
  cementTemperature: number; // 水泥温度 °C
  aggregateTemperature: number; // 骨料温度 °C

  // 材料选择
  selectedMaterials: string[]; // 选中的材料ID列表
  cementType: string; // 水泥类型
  aggregateType: string; // 骨料类型
  waterType: string; // 水类型

  // 配比参数
  waterCementRatio: number; // 水胶比
  sandRatio: number; // 砂率 %
  cementContent: number; // 水泥用量 kg/m³
  waterContent: number; // 用水量 kg/m³

  // 外加剂参数（比例）
  additiveRatio: number; // 外加剂掺量 %
  flyashRatio: number; // 粉煤灰掺量 %
  mineralPowderRatio: number; // 矿粉掺量 %
  silicaFumeRatio: number; // 硅灰掺量 %
  antifreezeRatio: number; // 防冻剂掺量 %
  expansionRatio: number; // 膨胀剂掺量 %

  // 数量参数
  cementAmount: number; // 水泥用量 kg/m³
  waterAmount: number; // 用水量 kg/m³
  density: number; // 表观密度 kg/m³
  airContent: number; // 含气量 %
  strengthGrade: number; // 强度等级
  ultraFineSandRatio: number; // 特细砂掺量 %
  earlyStrengthRatio: number; // 早强剂掺量 %

  // 施工参数
  placementMethod: PlacementMethod; // 浇筑方法
  finishingRequirement: FinishingRequirement; // 表面处理要求
  cureConditions: {
    method: string; // 养护方法
    duration: number; // 养护时间 (天)
    temperature: number; // 养护温度 (°C)
    humidity: number; // 养护湿度 (%)
  };

  // 外加剂用量（具体数值）- 必需字段
  admixtureAmount: number; // 外加剂用量 kg/m³
  antifreezeAmount: number; // 防冻剂用量 kg/m³
  flyAshAmount: number; // 粉煤灰用量 kg/m³
  mineralPowderAmount: number; // 矿粉用量 kg/m³
  s105PowderAmount: number; // S105粉用量 kg/m³
  expansionAgentAmount: number; // 膨胀剂用量 kg/m³
  earlyStrengthAgentAmount: number; // 早强剂用量 kg/m³
  ultraFineSandAmount: number; // 特细砂用量 kg/m³
}

/**
 * 表面处理要求枚举
 */
export enum FinishingRequirement {
  ROUGH = 'rough', // 粗糙
  SMOOTH = 'smooth', // 光滑
  TEXTURED = 'textured', // 有纹理
  POLISHED = 'polished', // 抛光
}

/**
 * 统一的材料接口
 */
export interface UnifiedRatioMaterial {
  // 基本信息
  id: string;
  materialId: string;
  name: string;
  specification: string;
  category: MaterialCategory;
  unit: string;
  density: number;

  // 用量信息
  theoreticalAmount: number; // 理论用量
  actualAmount: number; // 实际用量
  waterContent: number; // 含水率 %
  stoneContent: number; // 含石率 %
  designValue: number; // 设计值

  // 仓储信息
  siloId: string;
  siloName: string;

  // 成本信息
  cost: number;
  supplier: string;

  // 可选属性（用于兼容性）
  actualValue?: number;
  specificGravity?: number;
  grade?: string;
  ratio?: number;
  percentage?: number;
}

// ==================== 车辆相关接口 ====================

/**
 * 统一的车辆接口
 */
export interface UnifiedVehicle {
  // 基本信息
  id: ID;
  vehicleNumber: string;
  licensePlate?: LicensePlate;
  plantId: ID;

  // 状态信息
  status: VehicleStatus;
  operationalStatus?: VehicleOperationalStatus;
  productionStatus?: VehicleProductionStatus;

  // 分配信息
  assignedTaskId?: ID;
  assignedProductionLineId?: ID;
  currentTaskId?: ID; // 兼容字段

  // 驾驶员信息
  driver?: string;
  driverName?: string; // 兼容字段
  driverPhone?: PhoneNumber;

  // 车辆规格
  capacity: number;
  capacityUnit?: string;
  type?: string;
  model?: string;
  year?: number;

  // 位置和时间
  currentLocation?: string;
  location?: string; // 兼容字段
  lastDispatchTime?: Timestamp;
  estimatedReturnTime?: Timestamp;
  lastActivityTime?: Timestamp;

  // 拖拽状态
  isDragging?: boolean;
}

// ==================== 任务相关接口 ====================

/**
 * 统一的任务接口
 */
export interface UnifiedTask {
  // 基本信息
  id: ID;
  plantId: ID;
  taskNumber: string;
  projectName: string;

  // 混凝土规格
  strength: string;
  volume: number;
  slump?: number;
  freezeResistance?: string;
  impermeability?: string;

  // 状态信息
  status: TaskDispatchStatus;
  dispatchStatus: TaskDispatchStatus;
  priority: Priority;

  // 时间信息
  scheduledTime: Timestamp;
  actualStartTime?: Timestamp;
  actualEndTime?: Timestamp;
  estimatedDuration: number;

  // 调度信息
  dispatchFrequencyMinutes: number;
  lastDispatchTime?: Timestamp;
  nextScheduledDispatchTime?: Timestamp;
  isDueForDispatch: boolean;
  minutesToDispatch: number;

  // 车辆和设备
  dispatchedVehicles: ID[];
  vehicles?: UnifiedVehicle[];
  pumpTruck?: string;

  // 联系信息
  contactPhone?: PhoneNumber;
  contactPerson?: string;

  // 其他信息
  otherRequirements?: string;
  notes?: string;
  subTheme?: string;
  dispatchNote?: string;
  isTicketed?: boolean;

  // 消息和提醒
  messages?: TaskMessage[];
  reminders?: ReminderMessage[];

  // 元数据
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
  createdBy?: string;
  updatedBy?: string;
}

/**
 * 任务消息接口
 */
export interface TaskMessage {
  id: ID;
  taskId: ID;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  content: string;
  isRead: boolean;
  createdAt: Timestamp;
  createdBy: string;
}

/**
 * 提醒消息接口
 */
export interface ReminderMessage {
  id: ID;
  taskId: ID;
  type: 'dispatch' | 'quality' | 'schedule' | 'maintenance';
  message: string;
  triggerTime: Timestamp;
  isActive: boolean;
  isDismissed: boolean;
}

// ==================== 工厂相关接口 ====================

/**
 * 统一的工厂接口
 */
export interface UnifiedPlant {
  // 基本信息
  id: ID;
  name: string;
  code: string;
  address: string;

  // 生产能力
  productionLineCount: number;
  dailyCapacity: number;
  maxConcurrentTasks: number;

  // 状态信息
  isActive: boolean;
  operationalStatus: 'normal' | 'maintenance' | 'offline';

  // 统计信息
  stats: {
    completedTasks: number;
    totalTasks: number;
    todayProduction: number;
    monthlyProduction: number;
  };

  // 联系信息
  contactPhone?: PhoneNumber;
  contactPerson?: string;
  email?: string;

  // 元数据
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// ==================== 默认值常量 ====================

/**
 * 默认配比计算参数
 */
export const DEFAULT_RATIO_CALCULATION_PARAMS: RatioCalculationParams = {
  // 基本参数
  targetStrength: 30,
  slump: 180,
  maxAggregateSize: 25,
  exposureClass: ExposureClass.XC1,

  // 环境参数
  ambientTemperature: 20,
  relativeHumidity: 60,
  cementTemperature: 20,
  aggregateTemperature: 20,

  // 材料选择
  selectedMaterials: [],
  cementType: 'P.O 42.5',
  aggregateType: '碎石',
  waterType: '自来水',

  // 配比参数
  waterCementRatio: 0.45,
  sandRatio: 35,
  cementContent: 400,
  waterContent: 180,

  // 外加剂参数
  additiveRatio: 1.2,
  flyashRatio: 15,
  mineralPowderRatio: 10,
  silicaFumeRatio: 0,
  antifreezeRatio: 0,
  expansionRatio: 0,

  // 数量参数
  cementAmount: 400,
  waterAmount: 180,
  density: 2400,
  airContent: 4.5,
  strengthGrade: 30,
  ultraFineSandRatio: 0,
  earlyStrengthRatio: 0,

  // 施工参数
  placementMethod: PlacementMethod.PUMP,
  finishingRequirement: FinishingRequirement.SMOOTH,
  cureConditions: {
    method: '自然养护',
    duration: 28,
    temperature: 20,
    humidity: 95,
  },

  // 外加剂用量（具体数值）
  admixtureAmount: 4.8,
  antifreezeAmount: 0,
  flyAshAmount: 60,
  mineralPowderAmount: 40,
  s105PowderAmount: 0,
  expansionAgentAmount: 0,
  earlyStrengthAgentAmount: 0,
  ultraFineSandAmount: 0,
};
