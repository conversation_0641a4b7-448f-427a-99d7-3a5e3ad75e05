# TMH车辆调度系统架构
- TMH车辆调度系统采用Next.js 15 App Router + TypeScript架构，基于领域驱动设计(DDD)原则，实现清晰的分层架构：表现层(components)、业务逻辑层(services)、数据访问层(store)、基础设施层(lib/utils)。

# 组件设计原则
- 项目遵循严格的组件设计原则：单一职责原则(SRP)通过细粒度组件拆分实现，如TaskList拆分为TaskGroup、VehicleCard、DispatchButton等子组件；组件采用函数式设计模式，使用React.forwardRef、React.memo等优化性能。
- 组件分层架构遵循原子设计原则：atoms(基础UI组件)、molecules(复合组件)、organisms(业务组件)、templates(布局模板)、pages(页面组件)，每层职责明确，依赖关系单向向下。

# 状态管理
- 状态管理采用分层模块化设计：UI状态(uiStore)与业务状态(appStore)严格分离，使用Zustand的createWithEqualityFn确保性能优化，配合React Query实现数据获取、缓存和同步的统一管理。
- 项目采用Context Provider模式管理全局状态：ThemeProvider管理主题和密度设置，DndContextProvider管理拖拽状态，QueryProvider管理数据请求缓存，每个Provider职责单一且可独立配置。

# 编码规范
- 编码规范遵循TypeScript严格类型安全：所有核心实体(Task、Vehicle、Plant)类型定义集中在src/types目录，使用interface而非type，避免any类型，组件props和Hook返回值都有明确类型定义。

# 服务层设计
- 服务层设计采用适配器模式：dataService作为数据访问统一入口，httpClient提供HTTP请求封装，DataAdapters负责API数据转换，支持mock和真实API的无缝切换。

# Hook设计
- Hook设计模式遵循单一职责：useCurrentPlantInfo专注工厂信息获取，useTaskRowHighlight专注任务行高亮，useIsMobile专注响应式检测，每个Hook都有明确的输入输出类型定义和错误处理机制。

# 性能优化
- 性能优化策略包括：使用React.memo和useMemo防止不必要重渲染，Zustand的shallow比较优化状态订阅，虚拟滚动处理大数据列表，动态导入(dynamic import)实现代码分割和懒加载。

# 后续开发优先级
- TMH项目后续开发优先级：1.添加Prettier代码格式化 2.优化包体积分析 3.集成真实用户监控(RUM) 4.实现性能指标仪表板 5.新版配比页面添加更多物料类型支持 6.增强计算算法精度 7.增加AI智能配比推荐功能