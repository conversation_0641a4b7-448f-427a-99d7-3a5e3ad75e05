'use client';
/**
 * 车辆卡片拖拽逻辑Hook
 * 处理车辆卡片拖拽和排序逻辑
 */

import { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import type { Identifier } from 'dnd-core';
import { ItemTypes } from '@/core/constants/dndItemTypes';
import type { Vehicle } from '@/core/types';

interface UseVehicleCardDragProps {
  vehicle: Vehicle;
  index: number;
  listType: 'pending' | 'returned';
  globalDispatchActive: boolean;
  onVisualMove: (draggedId: string, hoverId: string, statusList: 'pending' | 'returned') => void;
  onCommitReorder: (statusList: 'pending' | 'returned') => void;
}

/**
 * 车辆卡片拖拽逻辑Hook
 */
export function useVehicleCardDrag({
  vehicle,
  index,
  listType,
  globalDispatchActive,
  onVisualMove,
  onCommitReorder,
}: UseVehicleCardDragProps) {
  const ref = useRef<HTMLDivElement>(null);

  // 拖拽源配置
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.VEHICLE_CARD_DISPATCH,
    item: { vehicle, index, statusList: listType, type: ItemTypes.VEHICLE_CARD_DISPATCH },
    canDrag: () => {
      const canDrag =
        globalDispatchActive &&
        (vehicle.status === 'pending' || vehicle.status === 'returned') &&
        (vehicle.operationalStatus === 'normal' || !vehicle.operationalStatus);

      // 调试信息
      if (!canDrag) {
        console.log('🚫 车辆无法拖拽:', {
          vehicleId: vehicle.id,
          vehicleNumber: vehicle.vehicleNumber,
          globalDispatchActive,
          status: vehicle.status,
          operationalStatus: vehicle.operationalStatus,
          canDrag,
        });
      }

      return canDrag;
    },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
    end: (item, monitor) => {
      if (!monitor.didDrop() && item.statusList === listType) {
        onCommitReorder(listType);
      }
    },
  });

  // 拖拽目标配置
  const [, drop] = useDrop<
    { vehicle: Vehicle; index: number; statusList: 'pending' | 'returned'; type: string },
    void,
    { handlerId: Identifier | null }
  >({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    canDrop: item => item.statusList === listType,
    hover: (item, monitor) => {
      if (!ref.current) return;
      if (item.statusList !== listType) return;

      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;

      const hoverBoundingRect = ref.current.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;

      onVisualMove(item.vehicle.id, vehicle.id, listType);
      item.index = hoverIndex;
    },
  });

  // 连接拖拽源和目标
  drag(drop(ref));

  return {
    ref,
    isDragging,
  };
}
