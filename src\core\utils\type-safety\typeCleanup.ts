/**
 * 类型清理工具
 * 提供工具函数来识别和清理代码中的any类型使用
 */

// ==================== 类型安全工具 ====================

/**
 * 安全的类型断言，提供运行时检查
 */
export function safeTypeAssertion<T>(
  value: unknown,
  typeGuard: (value: unknown) => value is T,
  errorMessage?: string
): T {
  if (typeGuard(value)) {
    return value;
  }
  throw new Error(errorMessage || `Type assertion failed for value: ${JSON.stringify(value)}`);
}

/**
 * 安全的对象属性访问
 */
export function safePropertyAccess<T, K extends keyof T>(
  obj: T | null | undefined,
  key: K
): T[K] | undefined {
  return obj?.[key];
}

/**
 * 安全的数组访问
 */
export function safeArrayAccess<T>(array: T[] | null | undefined, index: number): T | undefined {
  return array?.[index];
}

/**
 * 类型安全的对象合并
 */
export function safeMerge<T extends Record<string, any>, U extends Record<string, any>>(
  target: T,
  source: U
): T & U {
  return { ...target, ...source };
}

// ==================== 通用类型守卫 ====================

/**
 * 检查值是否为字符串
 */
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

/**
 * 检查值是否为非空字符串
 */
export function isNonEmptyString(value: unknown): value is string {
  return isString(value) && value.trim().length > 0;
}

/**
 * 检查值是否为数字
 */
export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

/**
 * 检查值是否为正数
 */
export function isPositiveNumber(value: unknown): value is number {
  return isNumber(value) && value > 0;
}

/**
 * 检查值是否为整数
 */
export function isInteger(value: unknown): value is number {
  return isNumber(value) && Number.isInteger(value);
}

/**
 * 检查值是否为布尔值
 */
export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

/**
 * 检查值是否为数组
 */
export function isArray<T>(value: unknown): value is T[] {
  return Array.isArray(value);
}

/**
 * 检查值是否为对象（非null，非数组）
 */
export function isObject(value: unknown): value is Record<string, unknown> {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
}

/**
 * 检查值是否为函数
 */
export function isFunction(value: unknown): value is Function {
  return typeof value === 'function';
}

/**
 * 检查值是否为null或undefined
 */
export function isNullish(value: unknown): value is null | undefined {
  return value === null || value === undefined;
}

/**
 * 检查值是否不为null或undefined
 */
export function isNotNullish<T>(value: T | null | undefined): value is T {
  return !isNullish(value);
}

// ==================== 复合类型守卫 ====================

/**
 * 检查对象是否具有指定的属性
 */
export function hasProperty<K extends string>(obj: unknown, key: K): obj is Record<K, unknown> {
  return isObject(obj) && key in obj;
}

/**
 * 检查对象是否具有指定类型的属性
 */
export function hasTypedProperty<K extends string, T>(
  obj: unknown,
  key: K,
  typeGuard: (value: unknown) => value is T
): obj is Record<K, T> {
  return hasProperty(obj, key) && typeGuard(obj[key]);
}

/**
 * 检查数组中的所有元素是否都符合指定类型
 */
export function isArrayOf<T>(
  value: unknown,
  typeGuard: (item: unknown) => item is T
): value is T[] {
  return isArray(value) && value.every(typeGuard);
}

/**
 * 检查值是否为指定枚举的成员
 */
export function isEnumValue<T extends Record<string, string | number>>(
  enumObject: T,
  value: unknown
): value is T[keyof T] {
  return Object.values(enumObject).includes(value as T[keyof T]);
}

// ==================== 类型转换工具 ====================

/**
 * 安全的字符串转换
 */
export function toSafeString(value: unknown, defaultValue = ''): string {
  if (isString(value)) return value;
  if (isNumber(value)) return value.toString();
  if (isBoolean(value)) return value.toString();
  return defaultValue;
}

/**
 * 安全的数字转换
 */
export function toSafeNumber(value: unknown, defaultValue = 0): number {
  if (isNumber(value)) return value;
  if (isString(value)) {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? defaultValue : parsed;
  }
  return defaultValue;
}

/**
 * 安全的布尔值转换
 */
export function toSafeBoolean(value: unknown, defaultValue = false): boolean {
  if (isBoolean(value)) return value;
  if (isString(value)) {
    const lower = value.toLowerCase();
    if (['true', '1', 'yes', 'on'].includes(lower)) return true;
    if (['false', '0', 'no', 'off'].includes(lower)) return false;
  }
  if (isNumber(value)) return value !== 0;
  return defaultValue;
}

/**
 * 安全的数组转换
 */
export function toSafeArray<T>(value: unknown): T[] {
  if (isArray<T>(value)) return value;
  if (isNotNullish(value)) return [value as T];
  return [];
}

// ==================== 类型验证工具 ====================

/**
 * 验证对象结构
 */
export function validateObjectStructure<T extends Record<string, unknown>>(
  obj: unknown,
  schema: {
    [K in keyof T]: (value: unknown) => value is T[K];
  }
): obj is T {
  if (!isObject(obj)) return false;

  for (const [key, validator] of Object.entries(schema)) {
    if (!validator(obj[key])) {
      return false;
    }
  }

  return true;
}

/**
 * 创建对象验证器
 */
export function createObjectValidator<T extends Record<string, unknown>>(schema: {
  [K in keyof T]: (value: unknown) => value is T[K];
}) {
  return (obj: unknown): obj is T => validateObjectStructure(obj, schema);
}

// ==================== 错误处理工具 ====================

/**
 * 类型错误类
 */
export class TypeValidationError extends Error {
  constructor(
    message: string,
    public readonly value: unknown,
    public readonly expectedType: string
  ) {
    super(message);
    this.name = 'TypeValidationError';
  }
}

/**
 * 抛出类型验证错误
 */
export function throwTypeError(value: unknown, expectedType: string, context?: string): never {
  const contextStr = context ? ` in ${context}` : '';
  throw new TypeValidationError(
    `Expected ${expectedType}${contextStr}, but received ${typeof value}`,
    value,
    expectedType
  );
}

/**
 * 安全的类型验证，失败时抛出错误
 */
export function assertType<T>(
  value: unknown,
  typeGuard: (value: unknown) => value is T,
  expectedType: string,
  context?: string
): asserts value is T {
  if (!typeGuard(value)) {
    throwTypeError(value, expectedType, context);
  }
}

// ==================== 开发工具 ====================

/**
 * 在开发环境中记录类型信息
 */
export function logTypeInfo(value: unknown, label?: string): void {
  if (process.env.NODE_ENV === 'development') {
    const prefix = label ? `[${label}]` : '[Type Info]';
    console.log(`${prefix} Type: ${typeof value}`, value);
  }
}

/**
 * 检查运行时类型与期望类型是否匹配
 */
export function checkTypeMatch<T>(
  value: unknown,
  typeGuard: (value: unknown) => value is T,
  label?: string
): boolean {
  const matches = typeGuard(value);
  if (process.env.NODE_ENV === 'development' && !matches) {
    console.warn(`Type mismatch${label ? ` for ${label}` : ''}:`, value);
  }
  return matches;
}

// ==================== 导出类型清理报告 ====================

/**
 * 生成类型使用报告
 */
export interface TypeUsageReport {
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  successRate: number;
  errors: Array<{
    value: unknown;
    expectedType: string;
    actualType: string;
    context?: string;
  }>;
}

/**
 * 类型检查统计器
 */
export class TypeChecker {
  private stats = {
    total: 0,
    passed: 0,
    failed: 0,
    errors: [] as TypeUsageReport['errors'],
  };

  check<T>(
    value: unknown,
    typeGuard: (value: unknown) => value is T,
    expectedType: string,
    context?: string
  ): value is T {
    this.stats.total++;
    const passed = typeGuard(value);

    if (passed) {
      this.stats.passed++;
    } else {
      this.stats.failed++;
      this.stats.errors.push({
        value,
        expectedType,
        actualType: typeof value,
        context,
      });
    }

    return passed;
  }

  getReport(): TypeUsageReport {
    return {
      totalChecks: this.stats.total,
      passedChecks: this.stats.passed,
      failedChecks: this.stats.failed,
      successRate: this.stats.total > 0 ? this.stats.passed / this.stats.total : 0,
      errors: [...this.stats.errors],
    };
  }

  reset(): void {
    this.stats = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: [],
    };
  }
}

// 全局类型检查器实例
export const globalTypeChecker = new TypeChecker();
