'use client';

import React from 'react';

import { Badge } from '@/shared/components/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/dialog';
import { ScrollArea } from '@/shared/components/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/table';
import type { RatioHistoryEntry, Task } from '@/core/types';

// 日期时间格式化函数
function formatDateTime(timestamp: string | number): string {
  try {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}`;
  } catch {
    return String(timestamp);
  }
}

interface RatioHistoryModalProps {
  isOpen?: boolean;
  open?: boolean;
  onOpenChangeAction?: (open: boolean) => void;
  onOpenChange?: (open: boolean) => void;
  task?: Task | null;
  history?: RatioHistoryEntry[];
}

export const RatioHistoryModal: React.FC<RatioHistoryModalProps> = ({
  isOpen = false,
  open = false,
  onOpenChangeAction,
  onOpenChange,
  task = null,
  history = [],
}) => {
  if (!task) return null;

  // 统一处理open状态和回调
  const modalOpen = isOpen || open;
  const handleOpenChange = (newOpen: boolean) => {
    onOpenChangeAction?.(newOpen);
    onOpenChange?.(newOpen);
  };

  return (
    <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
      <DialogContent className='max-w-4xl max-h-[80vh] flex flex-col'>
        <DialogHeader>
          <DialogTitle>配比修改历史 - {task.taskNumber}</DialogTitle>
          <DialogDescription>查看任务 "{task.projectName}" 的所有配比修改记录。</DialogDescription>
        </DialogHeader>
        <div className='grid grid-cols-4 gap-2 text-xs text-muted-foreground border-b pb-2 mb-2'>
          <div>
            <span className='font-medium'>工程名称:</span> {task.projectName}
          </div>
          <div>
            <span className='font-medium'>客户名称:</span> {task.customerName || 'N/A'}
          </div>
          <div>
            <span className='font-medium'>强度等级:</span>{' '}
            <Badge variant='secondary'>{task.strength}</Badge>
          </div>
          <div>
            <span className='font-medium'>施工部位:</span> {task.constructionSite}
          </div>
        </div>
        <ScrollArea className='flex-1'>
          <Table>
            <TableHeader className='sticky top-0 bg-background'>
              <TableRow>
                <TableHead className='w-[100px]'>修改人</TableHead>
                <TableHead className='w-[150px]'>修改时间</TableHead>
                <TableHead>价格</TableHead>
                <TableHead>水泥</TableHead>
                <TableHead>粉煤灰</TableHead>
                <TableHead>矿粉</TableHead>
                <TableHead>砂子</TableHead>
                <TableHead>石子</TableHead>
                <TableHead>水</TableHead>
                <TableHead>外加剂</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {history.map(entry => (
                <TableRow key={entry.id}>
                  <TableCell>{entry.editor}</TableCell>
                  <TableCell>{formatDateTime(entry.timestamp)}</TableCell>
                  <TableCell>{entry.price.toFixed(2)}</TableCell>
                  <TableCell>{entry.materials.cement}</TableCell>
                  <TableCell>{entry.materials.flyAsh}</TableCell>
                  <TableCell>{entry.materials.mineralPowder}</TableCell>
                  <TableCell>{entry.materials.sand}</TableCell>
                  <TableCell>{entry.materials.stone}</TableCell>
                  <TableCell>{entry.materials.water}</TableCell>
                  <TableCell>{entry.materials.admixture}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default RatioHistoryModal;
