'use client';

/**
 * 懒加载图表管理器
 * 实现图表组件的按需加载，优化性能
 */

import React, { Suspense, lazy, useState, useEffect } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { SkeletonLoader, SmartLoader } from '@/shared/components/enhanced-loading';
import {
  unifiedPerformanceMonitor,
  useUnifiedPerformanceMonitor,
} from '@/infrastructure/monitoring/unified-performance-monitor';

// ==================== 图表组件懒加载定义 ====================

// 调度相关图表 - 实际组件
const TaskProgressChartComponent = lazy(() => import('./TaskProgressChart'));

// TaskProgressChart 包装器，提供模拟数据
const TaskProgressChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => {
      // 生成模拟的进度数据
      const mockData = Array.from({ length: 10 }, (_, i) => {
        const date = new Date(Date.now() - (9 - i) * 24 * 60 * 60 * 1000);
        const dateStr = date.toISOString().split('T')[0];
        return {
          date: dateStr,
          day: dateStr, // 添加 day 字段作为 key
          volume: Math.floor(Math.random() * 50) + 20,
          dailyVolume: Math.floor(Math.random() * 30) + 10, // 添加日产量字段
          progress: Math.min(100, i * 12 + Math.random() * 10),
        };
      });

      // 生成模拟的车辆记录
      const mockVehicleRecords = Array.from({ length: 15 }, (_, i) => ({
        id: `vehicle-${i + 1}`,
        dispatchTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        volume: Math.floor(Math.random() * 10) + 5,
        status: 'completed',
      }));

      return React.createElement(TaskProgressChartComponent, {
        ...props,
        data: mockData,
        vehicleRecords: mockVehicleRecords,
        plannedVolume: 500,
        completedVolume: 320,
      });
    },
  })
);
const VehicleDensityChart = lazy(() => import('./VehicleDensityChart'));
const DispatchEfficiencyChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => (
      <div className='bg-gray-50 rounded-lg border border-gray-200 p-4 text-center text-gray-500'>
        <div className='text-lg mb-2'>📊</div>
        <div>调度效率图表</div>
        <div className='text-xs mt-1'>开发中...</div>
      </div>
    ),
  })
);
const TaskTimelineChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => (
      <div className='bg-gray-50 rounded-lg border border-gray-200 p-4 text-center text-gray-500'>
        <div className='text-lg mb-2'>📅</div>
        <div>任务时间线图表</div>
        <div className='text-xs mt-1'>开发中...</div>
      </div>
    ),
  })
);
const VehicleUtilizationChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => (
      <div className='bg-gray-50 rounded-lg border border-gray-200 p-4 text-center text-gray-500'>
        <div className='text-lg mb-2'>🚛</div>
        <div>车辆利用率图表</div>
        <div className='text-xs mt-1'>开发中...</div>
      </div>
    ),
  })
);

// 配比相关图表 - 部分实际组件
const RatioTrendChart = lazy(() => import('./RatioTrendChart'));
const MaterialUsageChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => (
      <div className='bg-gray-50 rounded-lg border border-gray-200 p-4 text-center text-gray-500'>
        <div className='text-lg mb-2'>🧱</div>
        <div>材料用量图表</div>
        <div className='text-xs mt-1'>开发中...</div>
      </div>
    ),
  })
);
const QualityAnalysisChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => (
      <div className='bg-gray-50 rounded-lg border border-gray-200 p-4 text-center text-gray-500'>
        <div className='text-lg mb-2'>🔬</div>
        <div>质量分析图表</div>
        <div className='text-xs mt-1'>开发中...</div>
      </div>
    ),
  })
);
const CostAnalysisChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => (
      <div className='bg-gray-50 rounded-lg border border-gray-200 p-4 text-center text-gray-500'>
        <div className='text-lg mb-2'>💰</div>
        <div>成本分析图表</div>
        <div className='text-xs mt-1'>开发中...</div>
      </div>
    ),
  })
);
const StrengthPredictionChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => (
      <div className='bg-gray-50 rounded-lg border border-gray-200 p-4 text-center text-gray-500'>
        <div className='text-lg mb-2'>💪</div>
        <div>强度预测图表</div>
        <div className='text-xs mt-1'>开发中...</div>
      </div>
    ),
  })
);

// 通用图表 - 暂时创建占位符组件
const LineChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => <div>LineChart 占位符 {JSON.stringify(props)}</div>,
  })
);
const BarChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => <div>BarChart 占位符 {JSON.stringify(props)}</div>,
  })
);
const PieChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => <div>PieChart 占位符 {JSON.stringify(props)}</div>,
  })
);
const ScatterChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => <div>ScatterChart 占位符 {JSON.stringify(props)}</div>,
  })
);
const HeatmapChart = lazy(() =>
  Promise.resolve({
    default: (props: any) => <div>HeatmapChart 占位符 {JSON.stringify(props)}</div>,
  })
);

// ==================== 图表类型定义 ====================

export type ChartType =
  // 调度相关
  | 'taskProgress'
  | 'vehicleDensity'
  | 'dispatchEfficiency'
  | 'taskTimeline'
  | 'vehicleUtilization'
  // 配比相关
  | 'ratioTrend'
  | 'materialUsage'
  | 'qualityAnalysis'
  | 'costAnalysis'
  | 'strengthPrediction'
  // 通用图表
  | 'line'
  | 'bar'
  | 'pie'
  | 'scatter'
  | 'heatmap';

export interface ChartConfig {
  type: ChartType;
  data?: any;
  options?: any;
  width?: number;
  height?: number;
  className?: string;
}

export interface LazyChartProps extends ChartConfig {
  fallback?: React.ReactNode;
  errorFallback?: React.ReactNode;
  preload?: boolean;
  onLoad?: () => void;
  onError?: (error: Error) => void;
  taskId?: string;
  ratioId?: string;
}

// ==================== 图表组件映射 ====================

const CHART_COMPONENTS = {
  // 调度相关
  taskProgress: TaskProgressChart,
  vehicleDensity: VehicleDensityChart,
  dispatchEfficiency: DispatchEfficiencyChart,
  taskTimeline: TaskTimelineChart,
  vehicleUtilization: VehicleUtilizationChart,

  // 配比相关
  ratioTrend: RatioTrendChart,
  materialUsage: MaterialUsageChart,
  qualityAnalysis: QualityAnalysisChart,
  costAnalysis: CostAnalysisChart,
  strengthPrediction: StrengthPredictionChart,

  // 通用图表
  line: LineChart,
  bar: BarChart,
  pie: PieChart,
  scatter: ScatterChart,
  heatmap: HeatmapChart,
} as const;

// ==================== 加载状态组件 ====================

const ChartLoadingSkeleton: React.FC<{
  type: ChartType;
  width?: number;
  height?: number;
  className?: string;
}> = ({ type, width = 400, height = 300, className = '' }) => (
  <div
    className={`bg-white rounded-lg border border-gray-200 ${className}`}
    style={{ width, height }}
  >
    <div className='p-4 h-full'>
      <div className='h-4 bg-gray-200 rounded w-1/3 mb-4 animate-pulse'></div>
      <SkeletonLoader type='chart' className='h-full' />
      <div className='text-xs text-gray-500 mt-2 text-center'>正在加载 {type} 图表...</div>
    </div>
  </div>
);

// ==================== 错误回退组件 ====================

const ChartErrorFallback: React.FC<{
  error: Error;
  resetErrorBoundary: () => void;
  chartType: ChartType;
  width?: number;
  height?: number;
  className?: string;
}> = ({ error, resetErrorBoundary, chartType, width = 400, height = 300, className = '' }) => (
  <div
    className={`bg-red-50 border border-red-200 rounded-lg flex flex-col items-center justify-center p-4 ${className}`}
    style={{ width, height }}
  >
    <div className='text-red-600 text-center'>
      <div className='text-lg mb-2'>📊</div>
      <h3 className='font-semibold text-sm'>图表加载失败</h3>
      <p className='text-xs mt-1 text-red-500'>无法加载 {chartType} 图表</p>
    </div>

    <div className='bg-red-100 p-2 rounded text-xs text-red-700 mt-3 max-w-full overflow-hidden'>
      <div className='truncate'>错误: {error.message}</div>
    </div>

    <button
      onClick={resetErrorBoundary}
      className='mt-3 px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600'
    >
      重试
    </button>
  </div>
);

// ==================== 主要组件 ====================

export const LazyChart: React.FC<LazyChartProps> = ({
  type,
  data,
  options,
  width,
  height,
  className,
  fallback,
  errorFallback,
  preload = false,
  onLoad,
  onError,
  taskId,
  ratioId,
}) => {
  const [isVisible, setIsVisible] = useState(preload);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [loadError, setLoadError] = useState<Error | null>(null);
  const [loadStartTime, setLoadStartTime] = useState<number>(preload ? performance.now() : 0);
  const { recordCustomMetric } = useUnifiedPerformanceMonitor({
    componentName: 'LazyChartManager',
  });

  // 交叉观察器，用于检测图表是否进入视口
  useEffect(() => {
    if (preload || hasLoaded) return;

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setLoadStartTime(performance.now()); // 记录开始加载时间
            setIsVisible(true);
            observer.disconnect();
          }
        });
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById(`lazy-chart-${type}`);
    if (element) {
      observer.observe(element);
    }

    return () => observer.disconnect();
  }, [type, preload, hasLoaded]);

  // 处理加载完成
  const handleLoad = () => {
    const loadTime = loadStartTime > 0 ? performance.now() - loadStartTime : 0;
    setHasLoaded(true);
    if (loadTime > 0) {
      recordCustomMetric(`chart-${type}-load-time`, loadTime);
      recordCustomMetric(`chart-${type}-load-success`, 1);
    }
    onLoad?.();
  };

  // 处理加载错误
  const handleError = (error: Error) => {
    const loadTime = loadStartTime > 0 ? performance.now() - loadStartTime : 0;
    console.error(`Chart loading error (${type}):`, error);
    setLoadError(error);
    if (loadTime > 0) {
      recordCustomMetric(`chart-${type}-load-time`, loadTime);
      recordCustomMetric(`chart-${type}-load-error`, 1);
    }
    onError?.(error);
  };

  const ChartComponent = CHART_COMPONENTS[type];

  if (!ChartComponent) {
    console.error(`Unknown chart type: ${type}`);
    return (
      <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${className}`}>
        <div className='text-yellow-800 text-center'>
          <div className='text-lg mb-2'>⚠️</div>
          <div className='text-sm'>未知的图表类型: {type}</div>
        </div>
      </div>
    );
  }

  // 如果还未进入视口且未预加载，显示占位符
  if (!isVisible) {
    return (
      <div
        id={`lazy-chart-${type}`}
        className={`bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <div className='text-gray-500 text-sm text-center'>
          <div className='text-2xl mb-2'>📊</div>
          <div>滚动到此处加载图表</div>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary
      FallbackComponent={props =>
        errorFallback || (
          <ChartErrorFallback
            {...props}
            chartType={type}
            width={width}
            height={height}
            className={className}
          />
        )
      }
      onError={handleError}
    >
      <Suspense
        fallback={
          fallback || (
            <ChartLoadingSkeleton type={type} width={width} height={height} className={className} />
          )
        }
      >
        <ChartComponent
          data={data}
          options={options}
          width={width}
          height={height}
          className={className}
          onLoad={handleLoad}
          taskId={taskId}
          ratioId={ratioId}
        />
      </Suspense>
    </ErrorBoundary>
  );
};

// ==================== 图表预加载Hook ====================

export const useChartPreloader = () => {
  const [preloadedCharts, setPreloadedCharts] = useState<Set<ChartType>>(new Set());

  const preloadChart = async (type: ChartType) => {
    if (preloadedCharts.has(type)) return;

    try {
      // 由于我们使用的是占位符组件，直接标记为已预加载
      setPreloadedCharts(prev => new Set(prev).add(type));
      console.log(`Chart ${type} marked as preloaded`);
    } catch (error) {
      console.error(`Failed to preload chart ${type}:`, error);
    }
  };

  const preloadChartsByCategory = async (category: 'dispatch' | 'ratio' | 'common') => {
    const chartsByCategory = {
      dispatch: ['taskProgress', 'vehicleDensity', 'dispatchEfficiency'] as ChartType[],
      ratio: ['ratioTrend', 'materialUsage', 'qualityAnalysis'] as ChartType[],
      common: ['line', 'bar', 'pie'] as ChartType[],
    };

    const charts = chartsByCategory[category] || [];
    await Promise.all(charts.map(preloadChart));
  };

  return {
    preloadChart,
    preloadChartsByCategory,
    preloadedCharts: Array.from(preloadedCharts),
  };
};

// ==================== 便捷组件 ====================

export const TaskProgressChartLazy: React.FC<Omit<LazyChartProps, 'type'>> = props => (
  <LazyChart type='taskProgress' {...props} />
);

export const VehicleDensityChartLazy: React.FC<Omit<LazyChartProps, 'type'>> = props => (
  <LazyChart type='vehicleDensity' {...props} />
);

export const RatioTrendChartLazy: React.FC<Omit<LazyChartProps, 'type'>> = props => (
  <LazyChart type='ratioTrend' {...props} />
);

export const MaterialUsageChartLazy: React.FC<Omit<LazyChartProps, 'type'>> = props => (
  <LazyChart type='materialUsage' {...props} />
);
