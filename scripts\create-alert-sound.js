#!/usr/bin/env node

/**
 * 创建简单的提示音文件
 * 使用 data URL 方式创建一个简单的音频文件
 */

const fs = require('fs');
const path = require('path');

console.log('🔊 创建提示音文件...\n');

// 创建一个简单的 WAV 文件数据
function createSimpleWav(frequency = 800, duration = 0.5, sampleRate = 44100) {
  const numSamples = Math.floor(sampleRate * duration);
  const buffer = Buffer.alloc(44 + numSamples * 2);

  // WAV 文件头
  buffer.write('RIFF', 0);
  buffer.writeUInt32LE(36 + numSamples * 2, 4);
  buffer.write('WAVE', 8);
  buffer.write('fmt ', 12);
  buffer.writeUInt32LE(16, 16);
  buffer.writeUInt16LE(1, 20);
  buffer.writeUInt16LE(1, 22);
  buffer.writeUInt32LE(sampleRate, 24);
  buffer.writeUInt32LE(sampleRate * 2, 28);
  buffer.writeUInt16LE(2, 32);
  buffer.writeUInt16LE(16, 34);
  buffer.write('data', 36);
  buffer.writeUInt32LE(numSamples * 2, 40);

  // 生成正弦波数据
  for (let i = 0; i < numSamples; i++) {
    const sample = Math.sin((2 * Math.PI * frequency * i) / sampleRate);
    const amplitude = Math.floor(sample * 0x7fff * 0.3); // 30% 音量
    buffer.writeInt16LE(amplitude, 44 + i * 2);
  }

  return buffer;
}

// 创建音频文件
try {
  const soundsDir = path.join(process.cwd(), 'public', 'sounds');

  // 确保目录存在
  if (!fs.existsSync(soundsDir)) {
    fs.mkdirSync(soundsDir, { recursive: true });
    console.log('✅ 创建 sounds 目录');
  }

  // 生成 WAV 文件
  const wavData = createSimpleWav(800, 0.3, 44100);
  const wavPath = path.join(soundsDir, 'alert.wav');
  fs.writeFileSync(wavPath, wavData);
  console.log('✅ 创建 alert.wav 文件');

  // 创建一个简单的 HTML 文件用于测试
  const testHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>音频测试</title>
</head>
<body>
    <h1>音频文件测试</h1>
    <audio controls>
        <source src="/sounds/alert.wav" type="audio/wav">
        <source src="/sounds/alert.mp3" type="audio/mpeg">
        您的浏览器不支持音频播放。
    </audio>
    <br><br>
    <button onclick="playSound()">播放提示音</button>
    
    <script>
        function playSound() {
            const audio = document.querySelector('audio');
            audio.play().catch(error => {
                console.error('播放失败:', error);
                alert('播放失败: ' + error.message);
            });
        }
    </script>
</body>
</html>
  `;

  const testPath = path.join(process.cwd(), 'public', 'audio-test.html');
  fs.writeFileSync(testPath, testHtml.trim());
  console.log('✅ 创建测试页面: /audio-test.html');

  console.log('\n🎉 音频文件创建完成！');
  console.log('\n📝 使用说明:');
  console.log('1. 启动开发服务器: npm run dev');
  console.log('2. 访问测试页面: http://localhost:3000/audio-test.html');
  console.log('3. 点击播放按钮测试音频');
} catch (error) {
  console.error('❌ 创建音频文件失败:', error.message);
  process.exit(1);
}
