/**
 * 性能管理器 - 统一的性能监控和分析系统
 * 提供全面的性能数据收集、分析和优化建议
 */

import {
  ExtendedWebVitals,
  ComponentPerformanceMetrics,
  PerformanceBenchmark,
  PerformanceAnalysisReport,
  PerformanceIssue,
  PerformanceRecommendation,
  PerformanceMonitoringConfig,
  PerformanceDataStore,
  PerformanceRegression,
  PerformanceEvent,
} from '@/core/types/performance';
import { performanceLogger } from '../../core/lib/logger';

class PerformanceManager {
  private static instance: PerformanceManager;
  private config: PerformanceMonitoringConfig;
  private dataStore: PerformanceDataStore;
  private observers: PerformanceObserver[] = [];
  private eventListeners: ((event: PerformanceEvent) => void)[] = [];
  private reportTimer?: NodeJS.Timeout;

  constructor(config?: Partial<PerformanceMonitoringConfig>) {
    this.config = {
      enabled: true,
      sampling: {
        webVitals: 1.0,
        componentMetrics: 0.1,
        memoryTracking: 0.05,
        networkMonitoring: 1.0,
      },
      thresholds: {
        renderTime: 16, // 60fps
        memoryUsage: 100, // 100MB
        bundleSize: 1024, // 1MB
        loadTime: 3000, // 3s
      },
      reporting: {
        interval: 300000, // 5分钟
        retention: 7, // 7天
      },
      benchmarking: {
        enabled: true,
        autoUpdate: false,
        environments: [
          { device: 'desktop', network: 'wifi', cpu: 'high' },
          { device: 'mobile', network: '4g', cpu: 'medium' },
        ],
      },
      alerts: {
        enabled: true,
        channels: ['console', 'toast'],
        thresholds: {
          critical: 20,
          warning: 50,
        },
      },
      ...config,
    };

    this.dataStore = {
      webVitalsHistory: [],
      componentMetricsHistory: [],
      benchmarks: [],
      reports: [],
      config: this.config,
      metadata: {
        version: '1.0.0',
        lastUpdated: new Date().toISOString(),
        dataSize: 0,
      },
    };

    if (this.config.enabled && typeof window !== 'undefined') {
      this.initialize();
    }
  }

  static getInstance(config?: Partial<PerformanceMonitoringConfig>): PerformanceManager {
    if (!PerformanceManager.instance) {
      PerformanceManager.instance = new PerformanceManager(config);
    }
    return PerformanceManager.instance;
  }

  // ==================== 初始化和配置 ====================

  private initialize(): void {
    this.setupWebVitalsCollection();
    this.setupPerformanceObservers();
    this.startReporting();

    performanceLogger.info('Performance Manager initialized', {
      config: this.config,
      timestamp: new Date().toISOString(),
    });
  }

  updateConfig(newConfig: Partial<PerformanceMonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.dataStore.config = this.config;
    this.dataStore.metadata.lastUpdated = new Date().toISOString();

    performanceLogger.info('Performance Manager config updated', { config: this.config });
  }

  // ==================== Web Vitals 收集 ====================

  private setupWebVitalsCollection(): void {
    if (Math.random() > this.config.sampling.webVitals) return;

    // FCP (First Contentful Paint)
    const fcpObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        this.recordWebVital('fcp', fcpEntry.startTime);
      }
    });
    fcpObserver.observe({ entryTypes: ['paint'] });
    this.observers.push(fcpObserver);

    // LCP (Largest Contentful Paint)
    const lcpObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (lastEntry) {
        this.recordWebVital('lcp', lastEntry.startTime);
      }
    });
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    this.observers.push(lcpObserver);

    // FID (First Input Delay) / INP (Interaction to Next Paint)
    const fidObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        const delay = entry.processingStart - entry.startTime;
        this.recordWebVital('fid', delay);

        // 如果支持INP，也记录INP
        if (entry.duration) {
          this.recordWebVital('inp', entry.duration);
        }
      });
    });
    fidObserver.observe({ entryTypes: ['first-input'] });
    this.observers.push(fidObserver);

    // CLS (Cumulative Layout Shift)
    let clsValue = 0;
    const clsObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          this.recordWebVital('cls', clsValue);
        }
      });
    });
    clsObserver.observe({ entryTypes: ['layout-shift'] });
    this.observers.push(clsObserver);

    // Navigation Timing
    this.collectNavigationTiming();
  }

  private collectNavigationTiming(): void {
    if (!window.performance || !window.performance.timing) return;

    const timing = window.performance.timing;

    // TTFB (Time to First Byte)
    const ttfb = timing.responseStart - timing.navigationStart;
    this.recordWebVital('ttfb', ttfb);

    // DOM Content Loaded
    const domContentLoaded = timing.domContentLoadedEventEnd - timing.navigationStart;
    this.recordWebVital('domContentLoaded', domContentLoaded);

    // Load Complete
    const loadComplete = timing.loadEventEnd - timing.navigationStart;
    this.recordWebVital('loadComplete', loadComplete);

    // TTI (Time to Interactive) - 简化计算
    const tti = timing.domInteractive - timing.navigationStart;
    this.recordWebVital('tti', tti);
  }

  private recordWebVital(metric: string, value: number): void {
    const webVital: Partial<ExtendedWebVitals> = {
      [metric]: value,
      timestamp: Date.now(),
      url: window.location.href,
    };

    // 获取网络信息
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      webVital.connectionType = connection.type || 'unknown';
      webVital.effectiveType = connection.effectiveType || 'unknown';
      webVital.downlink = connection.downlink || 0;
      webVital.rtt = connection.rtt || 0;
    }

    // 获取设备信息
    if ('deviceMemory' in navigator) {
      webVital.deviceMemory = (navigator as any).deviceMemory;
    }
    if ('hardwareConcurrency' in navigator) {
      webVital.hardwareConcurrency = navigator.hardwareConcurrency;
    }

    // 更新或创建完整的Web Vitals记录
    this.updateWebVitalsRecord(webVital as ExtendedWebVitals);

    // 触发事件
    this.emitEvent({
      type: 'metric',
      timestamp: new Date().toISOString(),
      data: webVital as ExtendedWebVitals,
      context: {
        route: window.location.pathname,
        userAgent: navigator.userAgent,
        sessionId: this.getSessionId(),
      },
    });
  }

  private updateWebVitalsRecord(partial: Partial<ExtendedWebVitals>): void {
    const existing = this.dataStore.webVitalsHistory.find(
      record => Math.abs(record.timestamp - partial.timestamp!) < 1000
    );

    if (existing) {
      Object.assign(existing, partial);
    } else {
      const newRecord: ExtendedWebVitals = {
        fcp: 0,
        lcp: 0,
        fid: 0,
        cls: 0,
        inp: 0,
        ttfb: 0,
        fmp: 0,
        tti: 0,
        tbt: 0,
        si: 0,
        domContentLoaded: 0,
        loadComplete: 0,
        resourceLoadTime: 0,
        connectionType: 'unknown',
        effectiveType: 'unknown',
        downlink: 0,
        rtt: 0,
        timestamp: Date.now(),
        url: window.location.href,
        ...partial,
      };

      this.dataStore.webVitalsHistory.push(newRecord);

      // 保持数据量在合理范围内
      if (this.dataStore.webVitalsHistory.length > 1000) {
        this.dataStore.webVitalsHistory = this.dataStore.webVitalsHistory.slice(-500);
      }
    }
  }

  // ==================== 组件性能监控 ====================

  recordComponentMetrics(metrics: ComponentPerformanceMetrics): void {
    if (Math.random() > this.config.sampling.componentMetrics) return;

    // 计算性能评分
    const performanceScore = this.calculateComponentPerformanceScore(metrics);
    metrics.performanceScore = performanceScore;

    this.dataStore.componentMetricsHistory.push(metrics);

    // 检测性能问题
    const issues = this.detectComponentIssues(metrics);
    issues.forEach(issue => this.handlePerformanceIssue(issue));

    // 保持数据量在合理范围内
    if (this.dataStore.componentMetricsHistory.length > 5000) {
      this.dataStore.componentMetricsHistory = this.dataStore.componentMetricsHistory.slice(-2500);
    }

    performanceLogger.debug('Component metrics recorded', {
      componentName: metrics.componentName,
      renderTime: metrics.renderTime,
      performanceScore,
    });
  }

  private calculateComponentPerformanceScore(metrics: ComponentPerformanceMetrics): number {
    let score = 100;

    // 渲染时间评分 (权重: 40%)
    if (metrics.renderTime > this.config.thresholds.renderTime) {
      const penalty = Math.min(
        40,
        (metrics.renderTime / this.config.thresholds.renderTime - 1) * 20
      );
      score -= penalty;
    }

    // 内存使用评分 (权重: 30%)
    if (metrics.memoryUsage.used > this.config.thresholds.memoryUsage) {
      const penalty = Math.min(
        30,
        (metrics.memoryUsage.used / this.config.thresholds.memoryUsage - 1) * 15
      );
      score -= penalty;
    }

    // 重渲染评分 (权重: 20%)
    if (metrics.wastedRenders > 0) {
      const penalty = Math.min(20, metrics.wastedRenders * 2);
      score -= penalty;
    }

    // 组件复杂度评分 (权重: 10%)
    if (metrics.depth > 10 || metrics.childrenCount > 50) {
      score -= 10;
    }

    return Math.max(0, Math.round(score));
  }

  private detectComponentIssues(metrics: ComponentPerformanceMetrics): PerformanceIssue[] {
    const issues: PerformanceIssue[] = [];

    // 渲染时间过长
    if (metrics.renderTime > this.config.thresholds.renderTime * 2) {
      issues.push({
        id: `render-time-${Date.now()}`,
        type: 'critical',
        category: 'rendering',
        title: '组件渲染时间过长',
        description: `组件 ${metrics.componentName} 渲染时间为 ${metrics.renderTime.toFixed(2)}ms，超过阈值`,
        impact: '可能导致页面卡顿，影响用户体验',
        details: {
          component: metrics.componentName,
          metric: 'renderTime',
          currentValue: metrics.renderTime,
          expectedValue: this.config.thresholds.renderTime,
          threshold: this.config.thresholds.renderTime * 2,
        },
        solutions: [
          '使用 React.memo 优化组件',
          '检查是否有不必要的重渲染',
          '考虑使用虚拟滚动',
          '优化组件内部逻辑',
        ],
        priority: 9,
        timestamp: new Date().toISOString(),
      });
    }

    // 内存使用过高
    if (metrics.memoryUsage.used > this.config.thresholds.memoryUsage) {
      issues.push({
        id: `memory-usage-${Date.now()}`,
        type: 'warning',
        category: 'memory',
        title: '组件内存使用过高',
        description: `组件 ${metrics.componentName} 内存使用为 ${metrics.memoryUsage.used}MB`,
        impact: '可能导致内存泄漏，影响应用性能',
        details: {
          component: metrics.componentName,
          metric: 'memoryUsage',
          currentValue: metrics.memoryUsage.used,
          expectedValue: this.config.thresholds.memoryUsage,
          threshold: this.config.thresholds.memoryUsage,
        },
        solutions: [
          '检查是否有内存泄漏',
          '清理未使用的事件监听器',
          '优化数据结构',
          '使用 WeakMap 和 WeakSet',
        ],
        priority: 7,
        timestamp: new Date().toISOString(),
      });
    }

    // 无效重渲染
    if (metrics.wastedRenders > 5) {
      issues.push({
        id: `wasted-renders-${Date.now()}`,
        type: 'warning',
        category: 'rendering',
        title: '组件存在无效重渲染',
        description: `组件 ${metrics.componentName} 存在 ${metrics.wastedRenders} 次无效重渲染`,
        impact: '浪费计算资源，影响性能',
        details: {
          component: metrics.componentName,
          metric: 'wastedRenders',
          currentValue: metrics.wastedRenders,
          expectedValue: 0,
          threshold: 5,
        },
        solutions: [
          '使用 React.memo 或 useMemo',
          '检查 props 和 state 的变化',
          '优化依赖数组',
          '使用 useCallback 优化函数',
        ],
        priority: 6,
        timestamp: new Date().toISOString(),
      });
    }

    return issues;
  }

  // ==================== 工具方法 ====================

  private setupPerformanceObservers(): void {
    // 资源加载监控
    const resourceObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (entry.duration > 1000) {
          // 超过1秒的资源加载
          performanceLogger.warn('Slow resource loading detected', {
            name: entry.name,
            duration: entry.duration,
            size: entry.transferSize,
          });
        }
      });
    });
    resourceObserver.observe({ entryTypes: ['resource'] });
    this.observers.push(resourceObserver);
  }

  private startReporting(): void {
    if (this.config.reporting.interval > 0) {
      this.reportTimer = setInterval(() => {
        this.generatePerformanceReport();
      }, this.config.reporting.interval);
    }
  }

  private generatePerformanceReport(): PerformanceAnalysisReport {
    // 实现性能报告生成逻辑
    const report: PerformanceAnalysisReport = {
      id: `report-${Date.now()}`,
      timestamp: new Date().toISOString(),
      overallScore: 85, // 临时值，需要实际计算
      scores: {
        webVitals: 90,
        componentPerformance: 85,
        memoryUsage: 80,
        bundleSize: 85,
        networkPerformance: 90,
      },
      issues: [],
      recommendations: [],
      trends: {
        period: '24h',
        improvement: 5,
        regression: [],
      },
      comparison: {
        baseline: {} as PerformanceBenchmark,
        current: {} as ExtendedWebVitals & ComponentPerformanceMetrics,
        delta: {},
      },
    };

    this.dataStore.reports.push(report);
    return report;
  }

  private handlePerformanceIssue(issue: PerformanceIssue): void {
    if (this.config.alerts.enabled) {
      if (issue.type === 'critical' && issue.priority >= this.config.alerts.thresholds.critical) {
        this.sendAlert(issue);
      } else if (
        issue.type === 'warning' &&
        issue.priority >= this.config.alerts.thresholds.warning
      ) {
        this.sendAlert(issue);
      }
    }

    this.emitEvent({
      type: 'issue',
      timestamp: new Date().toISOString(),
      data: issue,
      context: {
        route: window.location.pathname,
        userAgent: navigator.userAgent,
        sessionId: this.getSessionId(),
      },
    });
  }

  private sendAlert(issue: PerformanceIssue): void {
    this.config.alerts.channels.forEach(channel => {
      switch (channel) {
        case 'console':
          console.warn(`[Performance Alert] ${issue.title}`, issue);
          break;
        case 'toast':
          // 集成 toast 通知系统
          break;
      }
    });
  }

  private emitEvent(event: PerformanceEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        performanceLogger.error('Error in performance event listener', error as Error);
      }
    });
  }

  private getSessionId(): string {
    return (
      sessionStorage.getItem('performance-session-id') ||
      (() => {
        const id = `perf-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        sessionStorage.setItem('performance-session-id', id);
        return id;
      })()
    );
  }

  // ==================== 公共 API ====================

  addEventListener(listener: (event: PerformanceEvent) => void): () => void {
    this.eventListeners.push(listener);
    return () => {
      const index = this.eventListeners.indexOf(listener);
      if (index > -1) {
        this.eventListeners.splice(index, 1);
      }
    };
  }

  getWebVitals(): ExtendedWebVitals[] {
    return this.dataStore.webVitalsHistory;
  }

  getComponentMetrics(): ComponentPerformanceMetrics[] {
    return this.dataStore.componentMetricsHistory;
  }

  getLatestReport(): PerformanceAnalysisReport | null {
    return this.dataStore.reports[this.dataStore.reports.length - 1] || null;
  }

  clearData(): void {
    this.dataStore.webVitalsHistory = [];
    this.dataStore.componentMetricsHistory = [];
    this.dataStore.reports = [];
    this.dataStore.metadata.lastUpdated = new Date().toISOString();

    performanceLogger.info('Performance data cleared');
  }

  destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];

    if (this.reportTimer) {
      clearInterval(this.reportTimer);
    }

    this.eventListeners = [];

    performanceLogger.info('Performance Manager destroyed');
  }
}

// 全局实例
export const performanceManager = PerformanceManager.getInstance();

// 便捷方法
export const recordComponentPerformance = (metrics: ComponentPerformanceMetrics) => {
  performanceManager.recordComponentMetrics(metrics);
};

export const getPerformanceReport = () => {
  return performanceManager.getLatestReport();
};

export const subscribeToPerformanceEvents = (listener: (event: PerformanceEvent) => void) => {
  return performanceManager.addEventListener(listener);
};
