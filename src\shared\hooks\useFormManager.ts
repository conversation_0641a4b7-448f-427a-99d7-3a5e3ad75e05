/**
 * 通用表单管理Hook
 * 提供统一的表单状态管理、验证和提交功能
 */

import { useState, useCallback, useMemo } from 'react';

interface ValidationRule<T> {
  validate: (value: T) => boolean;
  message: string;
}

interface FieldConfig<T> {
  initialValue: T;
  rules?: ValidationRule<T>[];
  required?: boolean;
}

type FormConfig<T> = {
  [K in keyof T]: FieldConfig<T[K]>;
};

type FormErrors<T> = {
  [K in keyof T]?: string;
};

/**
 * 通用表单管理Hook
 */
export function useFormManager<T extends Record<string, any>>(config: FormConfig<T>) {
  // 初始化表单值
  const initialValues = useMemo(() => {
    return Object.keys(config).reduce((acc, key) => {
      acc[key as keyof T] = config[key as keyof T].initialValue;
      return acc;
    }, {} as T);
  }, [config]);

  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<FormErrors<T>>({});
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);
  const [isDirty, setIsDirty] = useState(false);

  /**
   * 验证单个字段
   */
  const validateField = useCallback(
    (fieldName: keyof T, value: T[keyof T]): string | undefined => {
      const fieldConfig = config[fieldName];
      if (!fieldConfig) return undefined;

      // 必填验证
      if (fieldConfig.required && (value === undefined || value === null || value === '')) {
        return '此字段为必填项';
      }

      // 自定义规则验证
      if (fieldConfig.rules) {
        for (const rule of fieldConfig.rules) {
          if (!rule.validate(value)) {
            return rule.message;
          }
        }
      }

      return undefined;
    },
    [config]
  );

  /**
   * 验证所有字段
   */
  const validateForm = useCallback((): FormErrors<T> => {
    const newErrors: FormErrors<T> = {};

    Object.keys(config).forEach(key => {
      const fieldName = key as keyof T;
      const error = validateField(fieldName, values[fieldName]);
      if (error) {
        newErrors[fieldName] = error;
      }
    });

    return newErrors;
  }, [config, values, validateField]);

  /**
   * 设置字段值
   */
  const setValue = useCallback(
    (fieldName: keyof T, value: T[keyof T]) => {
      setValues(prev => ({ ...prev, [fieldName]: value }));
      setIsDirty(true);

      // 实时验证
      const error = validateField(fieldName, value);
      setErrors(prev => ({
        ...prev,
        [fieldName]: error,
      }));
    },
    [validateField]
  );

  /**
   * 设置多个字段值
   */
  const setMultipleValues = useCallback(
    (newValues: Partial<T>) => {
      setValues(prev => ({ ...prev, ...newValues }));
      setIsDirty(true);

      // 验证更新的字段
      const newErrors: FormErrors<T> = { ...errors };
      Object.keys(newValues).forEach(key => {
        const fieldName = key as keyof T;
        const error = validateField(fieldName, newValues[fieldName]!);
        newErrors[fieldName] = error;
      });
      setErrors(newErrors);
    },
    [errors, validateField]
  );

  /**
   * 标记字段为已触摸
   */
  const setFieldTouched = useCallback((fieldName: keyof T, isTouched = true) => {
    setTouched(prev => ({ ...prev, [fieldName]: isTouched }));
  }, []);

  /**
   * 重置表单
   */
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({} as Record<keyof T, boolean>);
    setIsDirty(false);
  }, [initialValues]);

  /**
   * 提交表单
   */
  const submitForm = useCallback(
    async (onSubmit: (values: T) => Promise<void> | void): Promise<boolean> => {
      // 标记所有字段为已触摸
      const allTouched = Object.keys(config).reduce(
        (acc, key) => {
          acc[key as keyof T] = true;
          return acc;
        },
        {} as Record<keyof T, boolean>
      );
      setTouched(allTouched);

      // 验证表单
      const formErrors = validateForm();
      setErrors(formErrors);

      // 检查是否有错误
      const hasErrors = Object.values(formErrors).some(error => error !== undefined);
      if (hasErrors) {
        return false;
      }

      try {
        await onSubmit(values);
        return true;
      } catch (error) {
        console.error('表单提交失败:', error);
        return false;
      }
    },
    [config, validateForm, values]
  );

  /**
   * 检查表单是否有效
   */
  const isValid = useMemo(() => {
    const formErrors = validateForm();
    return Object.values(formErrors).every(error => error === undefined);
  }, [validateForm]);

  /**
   * 检查字段是否有错误且已触摸
   */
  const getFieldError = useCallback(
    (fieldName: keyof T): string | undefined => {
      return touched[fieldName] ? errors[fieldName] : undefined;
    },
    [touched, errors]
  );

  return {
    values,
    errors,
    touched,
    isDirty,
    isValid,
    setValue,
    setValues: setMultipleValues,
    setFieldTouched,
    resetForm,
    submitForm,
    validateField,
    validateForm,
    getFieldError,
  };
}
