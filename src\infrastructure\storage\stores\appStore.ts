import { produce } from 'immer';
import { createWithEqualityFn } from 'zustand/traditional';

import { storeLogger } from '@/core/lib/logger';
import {
  cancelVehicleDispatchService,
  // reorderVehiclesInListService is now handled by updating store based on new local order
  confirmCrossPlantDispatchService,
  dispatchVehicleToTaskService,
  getDeliveryOrders as fetchDataServiceDeliveryOrders,
  getTasks as fetchDataServiceTasks,
  getVehicles as fetchDataServiceVehicles,
} from '@/shared/services/dataService';
import type {
  DeliveryOrder,
  ReminderMessage,
  ReminderWorkerInput,
  ReminderWorkerOutput,
  Task,
  Vehicle,
} from '@/core/types';

// Removed: import { arrayMove } from '@dnd-kit/sortable'; // No longer needed

const REMINDER_WINDOW_MINUTES = 5;
const GLOBAL_TIMER_INTERVAL_MS = 5000;

interface WorkerApiType {
  postMessage: (message: ReminderWorkerInput) => void;
  then: (onResolve: (message: ReminderWorkerOutput) => void) => void;
  terminate: () => void;
  isTerminated?: boolean; // 添加终止状态标记
}

// 使用WeakMap管理Worker引用，避免内存泄漏
let workerCleanupCallbacks: (() => void)[] = [];

interface AppState {
  selectedPlantId: any;
  tasks: Task[];
  vehicles: Vehicle[];
  deliveryOrders: DeliveryOrder[];

  fetchInitialData: () => Promise<void>;
  setMockTasks: (tasks: Task[]) => void;
  updateTask: (taskId: string, updates: Partial<Task>) => void;

  dispatchVehicleToTask: (
    vehicleId: string,
    taskId: string,
    productionLineId?: string
  ) => Promise<Vehicle | null>;
  cancelVehicleDispatch: (vehicleId: string) => Promise<boolean>;
  // Updated signature: takes the status list type and the new array of ordered IDs for that list
  reorderVehiclesInList: (statusList: 'pending' | 'returned', newOrderedIds: string[]) => void;
  confirmCrossPlantDispatch: (
    vehicleId: string,
    targetPlantId: string,
    notes?: string
  ) => Promise<void>;

  _workerApi: WorkerApiType | null;
  _globalTimerId: NodeJS.Timeout | null;
  _visibilityChangeHandler: (() => void) | null;
  initializeTaskManager: () => void;
  cleanupTaskManager: () => void;
  triggerWorkerUpdate: () => void;
}

export const useAppStore = createWithEqualityFn<AppState>(
  (set, get) => ({
    tasks: [],
    vehicles: [],
    deliveryOrders: [],
    selectedPlantId: undefined,
    _workerApi: null,
    _globalTimerId: null,
    _visibilityChangeHandler: null,

    fetchInitialData: async () => {
      try {
        const [tasksData, vehiclesData, deliveryOrdersData] = await Promise.all([
          fetchDataServiceTasks(),
          fetchDataServiceVehicles(),
          fetchDataServiceDeliveryOrders(),
        ]);

        set({
          tasks: tasksData.map(t => {
            // 确保发车提醒相关字段正确初始化
            const now = new Date();
            const dispatchFrequency = t.dispatchFrequencyMinutes || 30;

            // 如果任务正在进行且没有下次发车时间，则计算一个
            let nextScheduledTime = t.nextScheduledDispatchTime;
            if (t.dispatchStatus === 'InProgress' && !nextScheduledTime) {
              if (t.lastDispatchTime) {
                // 基于上次发车时间计算
                const lastTime = new Date(t.lastDispatchTime);
                nextScheduledTime = new Date(
                  lastTime.getTime() + dispatchFrequency * 60 * 1000
                ).toISOString();
              } else {
                // 基于当前时间计算
                nextScheduledTime = new Date(
                  now.getTime() + dispatchFrequency * 60 * 1000
                ).toISOString();
              }
            }

            return {
              ...t,
              isDueForDispatch: false,
              nextScheduledDispatchTime: nextScheduledTime,
              dispatchFrequencyMinutes: dispatchFrequency,
            };
          }),
          vehicles: vehiclesData,
          deliveryOrders: deliveryOrdersData,
        });
      } catch (error) {
        storeLogger.error('Failed to fetch initial data', error as Error, {
          operation: 'fetchInitialData',
          dataTypes: ['tasks', 'vehicles', 'deliveryOrders'],
        });
        set({
          tasks: [],
          vehicles: [],
          deliveryOrders: [],
        });
      }
    },

    setMockTasks: (tasks: Task[]) => {
      set({ tasks });
      setTimeout(() => {
        get().triggerWorkerUpdate();
      }, 100);
    },

    updateTask: (taskId: string, updates: Partial<Task>) => {
      set(
        produce((draft: AppState) => {
          const taskIndex = draft.tasks.findIndex(t => t.id === taskId);
          if (taskIndex !== -1 && draft.tasks[taskIndex]) {
            Object.assign(draft.tasks[taskIndex], updates);
          }
        })
      );
      get().triggerWorkerUpdate();
    },

    initializeTaskManager: () => {
      // 清理现有的定时器和事件监听器
      const state = get();
      if (state._globalTimerId) {
        clearInterval(state._globalTimerId as NodeJS.Timeout);
        set({ _globalTimerId: null });
      }

      // 使用标准的setInterval而不是递归调度，避免内存泄漏
      const timerId = setInterval(() => {
        // 检查store是否仍然存在，避免在组件卸载后继续执行
        const currentState = get();
        if (currentState._globalTimerId === timerId) {
          currentState.triggerWorkerUpdate();
        }
      }, GLOBAL_TIMER_INTERVAL_MS);

      set({ _globalTimerId: timerId });

      // 添加页面可见性变化监听器
      if (typeof document !== 'undefined' && !state._visibilityChangeHandler) {
        const handler = () => {
          const currentState = get();
          if (document.hidden) {
            // 页面隐藏时暂停定时器
            if (currentState._globalTimerId) {
              clearInterval(currentState._globalTimerId as NodeJS.Timeout);
              set({ _globalTimerId: null });
            }
          } else {
            // 页面显示时恢复定时器
            if (!currentState._globalTimerId) {
              const newTimerId = setInterval(() => {
                const state = get();
                if (state._globalTimerId === newTimerId) {
                  state.triggerWorkerUpdate();
                }
              }, GLOBAL_TIMER_INTERVAL_MS);
              set({ _globalTimerId: newTimerId });
              // 立即触发一次更新
              currentState.triggerWorkerUpdate();
            }
          }
        };

        document.addEventListener('visibilitychange', handler);
        set({ _visibilityChangeHandler: handler });
      }

      // 如果页面当前可见，立即触发一次更新
      if (typeof document !== 'undefined' && !document.hidden) {
        get().triggerWorkerUpdate();
      }
    },

    triggerWorkerUpdate: () => {
      const state = get();
      const workerApi = state._workerApi;

      // 检查Worker是否可用且未被终止
      if (workerApi && workerApi.postMessage && !workerApi.isTerminated) {
        try {
          const currentTasks = state.tasks;
          const input: ReminderWorkerInput = {
            tasks: currentTasks,
            currentTime: Date.now(),
            reminderWindowMinutes: REMINDER_WINDOW_MINUTES,
          };
          workerApi.postMessage(input);
        } catch (error) {
          storeLogger.error('Failed to send message to worker', error as Error, {
            operation: 'triggerWorkerUpdate',
            taskCount: state.tasks.length,
          });

          // 如果Worker通信失败，尝试重新初始化
          setTimeout(() => {
            const currentState = get();
            if (currentState._workerApi === workerApi) {
              currentState.cleanupTaskManager();
              currentState.initializeTaskManager();
            }
          }, 1000);
        }
      }
    },

    cleanupTaskManager: () => {
      const state = get();

      // 清理定时器
      if (state._globalTimerId) {
        clearInterval(state._globalTimerId as NodeJS.Timeout);
      }

      // 清理Worker
      const workerApi = state._workerApi;
      if (workerApi && !workerApi.isTerminated) {
        try {
          workerApi.terminate();
          workerApi.isTerminated = true;
        } catch (error) {
          storeLogger.warn('Failed to terminate worker', {
            operation: 'cleanupTaskManager',
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }

      // 清理事件监听器
      if (typeof document !== 'undefined' && state._visibilityChangeHandler) {
        try {
          document.removeEventListener('visibilitychange', state._visibilityChangeHandler);
        } catch (error) {
          storeLogger.warn('Failed to remove visibility change listener', {
            operation: 'cleanupTaskManager',
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }

      // 执行所有注册的清理回调
      workerCleanupCallbacks.forEach(callback => {
        try {
          callback();
        } catch (error) {
          storeLogger.warn('Worker cleanup callback failed', {
            operation: 'cleanupTaskManager',
            error: error instanceof Error ? error.message : String(error),
          });
        }
      });
      workerCleanupCallbacks.length = 0; // 清空回调数组

      // 重置状态
      set({
        _globalTimerId: null,
        _workerApi: null,
        _visibilityChangeHandler: null,
      });
    },

    dispatchVehicleToTask: async (vehicleId, taskId, productionLineId) => {
      const currentVehicles = get().vehicles;
      const effectiveProductionLineId = productionLineId || 'L1';

      const updatedVehicle = await dispatchVehicleToTaskService(
        currentVehicles,
        vehicleId,
        taskId,
        effectiveProductionLineId
      );
      if (updatedVehicle) {
        console.log('📦 AppStore 更新车辆状态:', {
          vehicleId,
          taskId,
          productionLineId: effectiveProductionLineId,
          updatedVehicle: {
            id: updatedVehicle.id,
            vehicleNumber: updatedVehicle.vehicleNumber,
            status: updatedVehicle.status,
            assignedTaskId: updatedVehicle.assignedTaskId,
            assignedProductionLineId: updatedVehicle.assignedProductionLineId,
          },
        });

        set(
          produce((draft: AppState) => {
            const vehicle = draft.vehicles.find((v: { id: string }) => v.id === vehicleId);
            if (vehicle) {
              console.log('🔄 更新车辆前状态:', {
                id: vehicle.id,
                vehicleNumber: vehicle.vehicleNumber,
                status: vehicle.status,
                assignedTaskId: vehicle.assignedTaskId,
                assignedProductionLineId: vehicle.assignedProductionLineId,
              });
              Object.assign(vehicle, updatedVehicle);
              console.log('✅ 更新车辆后状态:', {
                id: vehicle.id,
                vehicleNumber: vehicle.vehicleNumber,
                status: vehicle.status,
                assignedTaskId: vehicle.assignedTaskId,
                assignedProductionLineId: vehicle.assignedProductionLineId,
              });

              // 验证车辆是否正确分配到任务
              console.log('🎯 车辆调度验证:', {
                vehicleId: vehicle.id,
                vehicleNumber: vehicle.vehicleNumber,
                targetTaskId: taskId,
                actualAssignedTaskId: vehicle.assignedTaskId,
                isCorrectlyAssigned: vehicle.assignedTaskId === taskId,
                productionLineId: vehicle.assignedProductionLineId,
              });
            } else {
              console.error('❌ 未找到要更新的车辆:', vehicleId);
            }

            const task = draft.tasks.find((t: { id: string }) => t.id === taskId);
            if (task) {
              const now = Date.now();
              const dispatchFrequencyMinutes = task.dispatchFrequencyMinutes || 30;
              const nextDispatchTime = new Date(now + dispatchFrequencyMinutes * 60 * 1000);

              task.lastDispatchTime = new Date(now).toISOString();
              task.nextScheduledDispatchTime = nextDispatchTime.toISOString();
              task.isDueForDispatch = false;

              // 同时更新任务的调度状态
              if (task.dispatchStatus !== 'InProgress') {
                console.log('📋 更新任务调度状态:', {
                  taskId: task.id,
                  taskNumber: task.taskNumber,
                  oldDispatchStatus: task.dispatchStatus,
                  newDispatchStatus: 'InProgress',
                });
                task.dispatchStatus = 'InProgress';
              }

              console.log('📋 更新任务调度信息:', {
                taskId: task.id,
                taskNumber: task.taskNumber,
                lastDispatchTime: task.lastDispatchTime,
                nextScheduledDispatchTime: task.nextScheduledDispatchTime,
                dispatchStatus: task.dispatchStatus,
              });
            } else {
              console.error('❌ 未找到要更新的任务:', taskId);
            }
          })
        );

        get().triggerWorkerUpdate();
        console.log('🔄 触发 Worker 更新');

        // 验证车辆调度结果
        setTimeout(() => {
          const currentState = get();
          const verifyVehicle = currentState.vehicles.find(v => v.id === vehicleId);
          const verifyTask = currentState.tasks.find(t => t.id === taskId);

          console.log('🔍 车辆调度结果验证:', {
            vehicleFound: !!verifyVehicle,
            taskFound: !!verifyTask,
            vehicleStatus: verifyVehicle?.status,
            vehicleAssignedTaskId: verifyVehicle?.assignedTaskId,
            vehicleAssignedLineId: verifyVehicle?.assignedProductionLineId,
            taskDispatchStatus: verifyTask?.dispatchStatus,
            isCorrectlyAssigned: verifyVehicle?.assignedTaskId === taskId,
            taskInProgress: verifyTask?.dispatchStatus === 'InProgress',
          });

          if (verifyVehicle?.assignedTaskId !== taskId) {
            console.error('❌ 车辆调度验证失败: 车辆未正确分配到任务');
          } else if (verifyTask?.dispatchStatus !== 'InProgress') {
            console.error('❌ 任务状态验证失败: 任务调度状态未更新为InProgress');
          } else {
            console.log('✅ 车辆调度验证成功');
          }
        }, 100);

        // 清理车辆分组缓存，确保UI及时更新
        try {
          // 清理所有车辆相关的缓存
          if (typeof window !== 'undefined' && (window as any).globalCache) {
            const cache = (window as any).globalCache;
            // 清理所有以 'vehicles-by-task-' 开头的缓存项
            const cacheKeys = cache.keys ? cache.keys() : [];
            cacheKeys.forEach((key: string) => {
              if (key.startsWith('vehicles-by-task-')) {
                cache.delete(key);
                console.log('🗑️ 清理车辆缓存:', key);
              }
            });
          }
        } catch (cacheError) {
          console.warn('⚠️ 清理缓存时出错:', cacheError);
        }

        return updatedVehicle;
      }
      return null;
    },

    cancelVehicleDispatch: async vehicleId => {
      const currentVehicles = get().vehicles;
      const updatedVehicle = await cancelVehicleDispatchService(currentVehicles, vehicleId);
      if (updatedVehicle) {
        set(state => ({
          vehicles: state.vehicles.map(v => (v.id === vehicleId ? updatedVehicle : v)),
        }));
        get().triggerWorkerUpdate();
        return true;
      } else {
        storeLogger.error('Failed to cancel vehicle dispatch', undefined, {
          operation: 'cancelVehicleDispatch',
          vehicleId,
          reason: 'Vehicle not found or service failed',
        });
        return false;
      }
    },

    // 优化车辆重新排序
    reorderVehiclesInList: (statusList: 'pending' | 'returned', newOrderedIds: string[]) => {
      set(
        produce((draft: AppState) => {
          // 分离目标状态的车辆和其他车辆
          const vehiclesOfTargetStatus: Vehicle[] = [];
          const otherVehicles: Vehicle[] = [];

          draft.vehicles.forEach((vehicle: Vehicle) => {
            if (vehicle.status === statusList) {
              vehiclesOfTargetStatus.push(vehicle);
            } else {
              otherVehicles.push(vehicle);
            }
          });

          // 按新顺序重新排列
          const reorderedVehicles = newOrderedIds
            .map(id => vehiclesOfTargetStatus.find(v => v.id === id))
            .filter(Boolean) as Vehicle[];

          // 更新车辆数组
          draft.vehicles = [...otherVehicles, ...reorderedVehicles];
        })
      );
    },

    confirmCrossPlantDispatch: async (vehicleId: string, targetPlantId: string, notes?: string) => {
      const currentVehicles = get().vehicles;
      const updatedVehicle = await confirmCrossPlantDispatchService(
        currentVehicles,
        vehicleId,
        targetPlantId,
        notes
      );
      if (updatedVehicle) {
        set(state => ({
          vehicles: state.vehicles.map(v => (v.id === vehicleId ? updatedVehicle : v)),
        }));
        get().triggerWorkerUpdate();
      } else {
        storeLogger.error('Failed to confirm cross-plant dispatch', undefined, {
          operation: 'confirmCrossPlantDispatch',
          vehicleId,
          targetPlantId,
          reason: 'Vehicle not found or service failed',
        });
      }
    },
  }),
  Object.is
);

export const handleWorkerMessage = (workerOutput: ReminderWorkerOutput) => {
  const appStore = useAppStore.getState();
  const tasks = appStore.tasks;
  const { useUiStore } = require('./uiStore');
  const uiStore = useUiStore.getState();

  if (workerOutput.error) {
    storeLogger.error('Worker error received', new Error(workerOutput.error), {
      operation: 'handleWorkerMessage',
      workerOutput,
    });
    return;
  }

  if (workerOutput.recovery) {
    storeLogger.info('Worker recovery message received, attempting to restart', {
      operation: 'handleWorkerMessage',
      action: 'recovery',
    });
    setTimeout(() => {
      appStore.triggerWorkerUpdate();
    }, 1000);
    return;
  }

  if (workerOutput.messages && Array.isArray(workerOutput.messages)) {
    workerOutput.messages.forEach((message: ReminderMessage) => {
      const hasSoundMessages = message.type === 'sound';
      if (hasSoundMessages) {
        uiStore.incrementAlertSoundCounter();
      }
    });
  }

  const { tasksWithUpdates } = workerOutput;
  if (!tasksWithUpdates || tasksWithUpdates.length === 0) return;

  const updatedTasks = [...tasks];
  let shouldPlaySound = false;

  tasksWithUpdates.forEach(update => {
    const taskIndex = updatedTasks.findIndex(t => t.id === update.id);
    if (taskIndex === -1) return;

    const oldTask = updatedTasks[taskIndex];
    const oldIsDueForDispatch = oldTask?.isDueForDispatch;
    const newIsDueForDispatch = update.isDueForDispatch;

    if (oldTask) {
      updatedTasks[taskIndex] = {
        ...oldTask,
        nextScheduledDispatchTime: update.nextScheduledDispatchTime,
        isDueForDispatch: update.isDueForDispatch ?? oldTask.isDueForDispatch,
        minutesToDispatch: update.minutesToDispatch,
      } as any;
    }

    if (!oldIsDueForDispatch && newIsDueForDispatch) {
      shouldPlaySound = true;
    }
  });

  useAppStore.setState({ tasks: updatedTasks });

  if (shouldPlaySound) {
    uiStore.incrementAlertSoundCounter();
  }

  if (workerOutput.stats) {
    storeLogger.debug('Worker processing stats', {
      operation: 'handleWorkerMessage',
      stats: {
        taskCount: workerOutput.stats.taskCount,
        updatedCount: workerOutput.stats.updatedCount,
        processingTimeMs: workerOutput.stats.processingTimeMs,
      },
    });
  }
};
