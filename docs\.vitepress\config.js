// VitePress 配置文件
// 用于生成静态文档站点

export default {
  title: 'TMH车辆调度系统',
  description: '现代化的混凝土生产企业车辆调度管理平台',
  
  // 基础配置
  base: '/docs/',
  lang: 'zh-CN',
  
  // 主题配置
  themeConfig: {
    // 导航栏
    nav: [
      { text: '首页', link: '/' },
      { text: '快速开始', link: '/getting-started/quick-start' },
      { text: '开发指南', link: '/development/coding-standards' },
      { text: 'API文档', link: '/api/overview' },
      { text: 'GitHub', link: 'https://github.com/your-org/tmh-task-dispatcher' }
    ],
    
    // 侧边栏
    sidebar: {
      '/overview/': [
        {
          text: '项目概述',
          items: [
            { text: '项目概述', link: '/overview/project-overview' },
            { text: '系统架构', link: '/overview/system-architecture' },
            { text: '技术栈', link: '/overview/tech-stack' }
          ]
        }
      ],
      
      '/getting-started/': [
        {
          text: '快速开始',
          items: [
            { text: '快速开始', link: '/getting-started/quick-start' },
            { text: '环境搭建', link: '/getting-started/environment-setup' },
            { text: '项目结构', link: '/getting-started/project-structure' }
          ]
        }
      ],
      
      '/development/': [
        {
          text: '开发指南',
          items: [
            { text: '开发规范', link: '/development/coding-standards' },
            { text: '组件开发', link: '/development/component-development' },
            { text: '状态管理', link: '/development/state-management' },
            { text: 'API集成', link: '/development/api-integration' },
            { text: '测试指南', link: '/development/testing-guide' }
          ]
        }
      ],
      
      '/features/': [
        {
          text: '功能模块',
          items: [
            { text: '任务管理', link: '/features/task-management' },
            { text: '车辆调度', link: '/features/vehicle-dispatch' },
            { text: '配比管理', link: '/features/ratio-management' },
            { text: '设置中心', link: '/features/settings' }
          ]
        }
      ],
      
      '/api/': [
        {
          text: 'API文档',
          items: [
            { text: 'API概述', link: '/api/overview' },
            { text: '任务API', link: '/api/task-api' },
            { text: '车辆API', link: '/api/vehicle-api' },
            { text: '配比API', link: '/api/ratio-api' }
          ]
        }
      ],
      
      '/ui-ux/': [
        {
          text: 'UI/UX指南',
          items: [
            { text: '设计系统', link: '/ui-ux/design-system' },
            { text: '主题配置', link: '/ui-ux/theming' },
            { text: '响应式设计', link: '/ui-ux/responsive-design' },
            { text: '用户体验', link: '/ui-ux/user-experience' }
          ]
        }
      ],
      
      '/deployment/': [
        {
          text: '部署运维',
          items: [
            { text: '构建部署', link: '/deployment/build-and-deploy' },
            { text: '环境配置', link: '/deployment/environment-config' },
            { text: '性能优化', link: '/deployment/performance-optimization' },
            { text: '监控告警', link: '/deployment/monitoring' }
          ]
        }
      ],
      
      '/troubleshooting/': [
        {
          text: '故障排除',
          items: [
            { text: '常见问题', link: '/troubleshooting/common-issues' },
            { text: '调试指南', link: '/troubleshooting/debugging-guide' },
            { text: '错误处理', link: '/troubleshooting/error-handling' }
          ]
        }
      ],
      
      '/reference/': [
        {
          text: '参考资料',
          items: [
            { text: '更新日志', link: '/reference/changelog' },
            { text: '贡献指南', link: '/reference/contributing' },
            { text: 'FAQ', link: '/reference/faq' },
            { text: '术语表', link: '/reference/glossary' }
          ]
        }
      ]
    },
    
    // 社交链接
    socialLinks: [
      { icon: 'github', link: 'https://github.com/your-org/tmh-task-dispatcher' }
    ],
    
    // 页脚
    footer: {
      message: 'Released under the MIT License.',
      copyright: 'Copyright © 2024-present TMH Team'
    },
    
    // 搜索
    search: {
      provider: 'local'
    },
    
    // 编辑链接
    editLink: {
      pattern: 'https://github.com/your-org/tmh-task-dispatcher/edit/main/docs/:path',
      text: '在 GitHub 上编辑此页'
    },
    
    // 最后更新时间
    lastUpdated: {
      text: '最后更新',
      formatOptions: {
        dateStyle: 'short',
        timeStyle: 'medium'
      }
    }
  },
  
  // Markdown 配置
  markdown: {
    // 代码块行号
    lineNumbers: true,
    
    // 代码块主题
    theme: {
      light: 'github-light',
      dark: 'github-dark'
    }
  },
  
  // 构建配置
  build: {
    outDir: '../dist/docs'
  }
};
