'use client';

import { useState, useCallback } from 'react';
import type { Vehicle, Task } from '@/core/types';
import type { VehicleTransferData } from '../components/modals/VehicleTransferModal';

interface UseVehicleTransferModalProps {
  onTransferConfirm?: (transferData: VehicleTransferData) => void;
}

interface VehicleTransferModalState {
  isOpen: boolean;
  vehicle: Vehicle | null;
  sourceTask: Task | null;
  targetTask: Task | null;
  targetLineId: string | null;
}

/**
 * 车辆转发模态框管理Hook
 */
export function useVehicleTransferModal({ onTransferConfirm }: UseVehicleTransferModalProps = {}) {
  const [modalState, setModalState] = useState<VehicleTransferModalState>({
    isOpen: false,
    vehicle: null,
    sourceTask: null,
    targetTask: null,
    targetLineId: null,
  });

  // 打开转发模态框
  const openTransferModal = useCallback(
    (vehicle: Vehicle, sourceTask: Task, targetTask: Task, targetLineId: string) => {
      setModalState({
        isOpen: true,
        vehicle,
        sourceTask,
        targetTask,
        targetLineId,
      });
    },
    []
  );

  // 关闭转发模态框
  const closeTransferModal = useCallback(() => {
    setModalState({
      isOpen: false,
      vehicle: null,
      sourceTask: null,
      targetTask: null,
      targetLineId: null,
    });
  }, []);

  // 处理转发确认
  const handleTransferConfirm = useCallback(
    (transferData: VehicleTransferData) => {
      onTransferConfirm?.(transferData);
      closeTransferModal();
    },
    [onTransferConfirm, closeTransferModal]
  );

  // 处理模态框开关状态变化
  const handleOpenChange = useCallback(
    (open: boolean) => {
      if (!open) {
        closeTransferModal();
      }
    },
    [closeTransferModal]
  );

  return {
    // 状态
    isOpen: modalState.isOpen,
    vehicle: modalState.vehicle,
    sourceTask: modalState.sourceTask,
    targetTask: modalState.targetTask,
    targetLineId: modalState.targetLineId,

    // 操作方法
    openTransferModal,
    closeTransferModal,
    handleTransferConfirm,
    handleOpenChange,
  };
}
