'use client';

/**
 * 配比表单管理业务Hook
 * 封装配比表单相关的业务逻辑，包括表单验证、数据同步等
 */

import { useCallback, useEffect } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRatioStoreOrchestrator } from '@/features/ratio-management/store/ratioStoreOrchestrator';
import type { RatioCalculationParams } from '@/core/types/ratio';
import { ExposureClass, PlacementMethod, FinishingRequirement } from '@/core/types';

// 表单验证模式
const calculationParamsSchema = z.object({
  // 基本参数
  targetStrength: z.number().min(10, '目标强度不能小于10MPa').max(100, '目标强度不能大于100MPa'),
  slump: z.number().min(50, '坍落度不能小于50mm').max(250, '坍落度不能大于250mm'),
  maxAggregateSize: z
    .number()
    .min(5, '最大骨料粒径不能小于5mm')
    .max(40, '最大骨料粒径不能大于40mm'),
  exposureClass: z.nativeEnum(ExposureClass),

  // 环境参数
  ambientTemperature: z.number().min(-20, '环境温度不能小于-20°C').max(50, '环境温度不能大于50°C'),
  relativeHumidity: z.number().min(0, '相对湿度不能小于0%').max(100, '相对湿度不能大于100%'),
  cementTemperature: z.number().min(-10, '水泥温度不能小于-10°C').max(60, '水泥温度不能大于60°C'),
  aggregateTemperature: z
    .number()
    .min(-10, '骨料温度不能小于-10°C')
    .max(60, '骨料温度不能大于60°C'),

  // 材料选择
  selectedMaterials: z.array(z.string()),
  cementType: z.string().min(1, '请选择水泥类型'),
  aggregateType: z.string().min(1, '请选择骨料类型'),
  waterType: z.string().min(1, '请选择水类型'),

  // 配比参数
  waterCementRatio: z.number().min(0.2, '水胶比不能小于0.2').max(0.8, '水胶比不能大于0.8'),
  sandRatio: z.number().min(20, '砂率不能小于20%').max(60, '砂率不能大于60%'),
  cementContent: z
    .number()
    .min(200, '水泥用量不能小于200kg/m³')
    .max(600, '水泥用量不能大于600kg/m³'),
  waterContent: z.number().min(100, '用水量不能小于100kg/m³').max(300, '用水量不能大于300kg/m³'),

  // 外加剂参数
  additiveRatio: z.number().min(0, '外加剂掺量不能为负').max(10, '外加剂掺量不能大于10%'),
  flyashRatio: z.number().min(0, '粉煤灰掺量不能为负').max(30, '粉煤灰掺量不能大于30%'),
  mineralPowderRatio: z.number().min(0, '矿粉掺量不能为负').max(30, '矿粉掺量不能大于30%'),
  silicaFumeRatio: z.number().min(0, '硅灰掺量不能为负').max(15, '硅灰掺量不能大于15%'),
  antifreezeRatio: z.number().min(0, '防冻剂掺量不能为负').max(10, '防冻剂掺量不能大于10%'),
  expansionRatio: z.number().min(0, '膨胀剂掺量不能为负').max(10, '膨胀剂掺量不能大于10%'),

  // 数量参数
  cementAmount: z.number().min(0, '水泥用量不能为负'),
  waterAmount: z.number().min(0, '用水量不能为负'),
  density: z.number().min(2000, '密度不能小于2000kg/m³').max(3000, '密度不能大于3000kg/m³'),
  airContent: z.number().min(0, '含气量不能为负').max(10, '含气量不能大于10%'),
  strengthGrade: z.number().min(10, '强度等级不能小于10MPa').max(100, '强度等级不能大于100MPa'),
  ultraFineSandRatio: z.number().min(0, '超细砂掺量不能为负').max(20, '超细砂掺量不能大于20%'),
  earlyStrengthRatio: z.number().min(0, '早强剂掺量不能为负').max(5, '早强剂掺量不能大于5%'),

  // 施工参数
  placementMethod: z.nativeEnum(PlacementMethod),
  finishingRequirement: z.nativeEnum(FinishingRequirement),

  // 养护条件
  cureConditions: z.object({
    method: z.string().min(1, '请选择养护方法'),
    duration: z.number().min(1, '养护时间不能小于1天').max(365, '养护时间不能大于365天'),
    temperature: z.number().min(-10, '养护温度不能小于-10°C').max(60, '养护温度不能大于60°C'),
    humidity: z.number().min(0, '养护湿度不能小于0%').max(100, '养护湿度不能大于100%'),
  }),

  // 外加剂用量（具体数值）
  admixtureAmount: z.number().min(0, '外加剂用量不能为负'),
  antifreezeAmount: z.number().min(0, '防冻剂用量不能为负'),
  flyAshAmount: z.number().min(0, '粉煤灰用量不能为负'),
  mineralPowderAmount: z.number().min(0, '矿粉用量不能为负'),
  s105PowderAmount: z.number().min(0, 'S105用量不能为负'),
  expansionAgentAmount: z.number().min(0, '膨胀剂用量不能为负'),
  earlyStrengthAgentAmount: z.number().min(0, '早强剂用量不能为负'),
  ultraFineSandAmount: z.number().min(0, '超细砂用量不能为负'),
});

/**
 * 配比表单管理Hook返回类型
 */
export interface UseRatioFormReturn {
  // React Hook Form 实例
  form: UseFormReturn<RatioCalculationParams>;

  // 表单状态
  isDirty: boolean;
  isValid: boolean;
  isSubmitting: boolean;
  errors: Record<string, any>;

  // 操作方法
  handleSubmit: (
    onSubmit: (data: RatioCalculationParams) => void | Promise<void>
  ) => (e?: React.BaseSyntheticEvent) => Promise<void>;
  reset: () => void;
  setValue: (name: keyof RatioCalculationParams, value: any) => void;
  getValue: (name: keyof RatioCalculationParams) => any;
  validateField: (name: keyof RatioCalculationParams) => Promise<boolean>;
  validateForm: () => Promise<boolean>;

  // 数据同步
  syncToStore: () => void;
  syncFromStore: () => void;
}

/**
 * 配比表单管理Hook
 * 提供表单管理相关的业务逻辑和状态管理
 */
export function useRatioForm(): UseRatioFormReturn {
  const orchestrator = useRatioStoreOrchestrator();

  // 创建表单实例
  const form = useForm<RatioCalculationParams>({
    resolver: zodResolver(calculationParamsSchema),
    defaultValues: orchestrator.data.calculationParams,
    mode: 'onChange',
  });

  const {
    handleSubmit: rhfHandleSubmit,
    reset: rhfReset,
    setValue: rhfSetValue,
    getValues: rhfGetValues,
    trigger,
    formState: { isDirty, isValid, isSubmitting, errors },
    watch,
  } = form;

  // 同步表单数据到Store
  const syncToStore = useCallback(() => {
    const formData = form.getValues();
    orchestrator.actions.setCalculationParams(formData);
  }, [form, orchestrator.actions]);

  // 从Store同步数据到表单
  const syncFromStore = useCallback(() => {
    rhfReset(orchestrator.data.calculationParams);
  }, [rhfReset, orchestrator.data.calculationParams]);

  // 监听表单变化并同步到Store
  useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      // 只在用户手动输入时同步，避免程序化更新导致的循环
      if (type === 'change' && name) {
        orchestrator.actions.setCalculationParams(value as Partial<RatioCalculationParams>);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, orchestrator.actions]);

  // 监听Store变化并同步到表单（只在初始化时执行一次）
  useEffect(() => {
    rhfReset(orchestrator.data.calculationParams);
  }, [rhfReset]); // 移除 orchestrator.data.calculationParams 依赖，避免循环

  // 包装的提交处理函数
  const handleSubmit = useCallback(
    (onSubmit: (data: RatioCalculationParams) => void | Promise<void>) => {
      return rhfHandleSubmit(async data => {
        try {
          // 同步到Store
          orchestrator.actions.setCalculationParams(data);

          // 执行提交回调
          await onSubmit(data);

          // 标记表单为非脏状态
          orchestrator.stores.ui.setDirty(false);
        } catch (error) {
          console.error('表单提交失败:', error);
          orchestrator.stores.ui.setError(error instanceof Error ? error.message : '提交失败');
        }
      });
    },
    [rhfHandleSubmit, orchestrator.actions, orchestrator.stores.ui]
  );

  // 重置表单
  const reset = useCallback(() => {
    rhfReset(orchestrator.data.calculationParams);
    orchestrator.stores.ui.setDirty(false);
  }, [rhfReset, orchestrator.data.calculationParams, orchestrator.stores.ui]);

  // 设置字段值
  const setValue = useCallback(
    (name: keyof RatioCalculationParams, value: any) => {
      rhfSetValue(name, value, { shouldDirty: true, shouldValidate: true });
    },
    [rhfSetValue]
  );

  // 获取字段值
  const getValue = useCallback(
    (name: keyof RatioCalculationParams) => {
      return rhfGetValues(name);
    },
    [rhfGetValues]
  );

  // 验证单个字段
  const validateField = useCallback(
    async (name: keyof RatioCalculationParams) => {
      return await trigger(name);
    },
    [trigger]
  );

  // 验证整个表单
  const validateForm = useCallback(async () => {
    return await trigger();
  }, [trigger]);

  // 同步表单状态到UI Store
  useEffect(() => {
    orchestrator.stores.ui.setDirty(isDirty);
    orchestrator.stores.ui.setValid(isValid);
  }, [isDirty, isValid, orchestrator.stores.ui]);

  return {
    // React Hook Form 实例
    form,

    // 表单状态
    isDirty,
    isValid,
    isSubmitting,
    errors,

    // 操作方法
    handleSubmit,
    reset,
    setValue,
    getValue,
    validateField,
    validateForm,

    // 数据同步
    syncToStore,
    syncFromStore,
  };
}
