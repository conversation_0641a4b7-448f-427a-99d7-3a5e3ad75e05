/**
 * 配比页面模态框状态管理Hook
 * 将模态框状态管理逻辑从页面组件中分离
 */

import { useState, useCallback } from 'react';

/**
 * 配比页面所有模态框的状态类型
 */
export interface RatioModalsState {
  performance: boolean;
  mortarRatio: boolean;
  history: boolean;
  template: boolean;
  siloManagement: boolean;
  settings: boolean;
  previewRatio: boolean;
  checkStandard: boolean;
  ratioSelection: boolean;
  aiGenerate: boolean;
  ratioRecommendation: boolean;
  ratioNotification: boolean;
  saveBackupRatio: boolean;
  backupRatioSelection: boolean;
}

/**
 * 简化的模态框状态管理（用于V1经典版本）
 */
export interface SimpleModalState {
  activeModal: string | null;
  openModal: (modal: string) => void;
  closeModal: () => void;
  modalData: Record<string, any>;
  setModalData: (modal: string, data: any) => void;
}

/**
 * 模态框状态管理Hook
 */
export function useRatioModals() {
  const [modals, setModals] = useState<RatioModalsState>({
    performance: false,
    mortarRatio: false,
    history: false,
    template: false,
    siloManagement: false,
    settings: false,
    previewRatio: false,
    checkStandard: false,
    ratioSelection: false,
    aiGenerate: false,
    ratioRecommendation: false,
    ratioNotification: false,
    saveBackupRatio: false,
    backupRatioSelection: false,
  });

  /**
   * 打开指定模态框
   */
  const openModal = useCallback((modalName: keyof RatioModalsState) => {
    setModals(prev => ({ ...prev, [modalName]: true }));
  }, []);

  /**
   * 关闭指定模态框
   */
  const closeModal = useCallback((modalName: keyof RatioModalsState) => {
    setModals(prev => ({ ...prev, [modalName]: false }));
  }, []);

  /**
   * 切换指定模态框状态
   */
  const toggleModal = useCallback((modalName: keyof RatioModalsState) => {
    setModals(prev => ({ ...prev, [modalName]: !prev[modalName] }));
  }, []);

  /**
   * 关闭所有模态框
   */
  const closeAllModals = useCallback(() => {
    setModals({
      performance: false,
      mortarRatio: false,
      history: false,
      template: false,
      siloManagement: false,
      settings: false,
      previewRatio: false,
      checkStandard: false,
      ratioSelection: false,
      aiGenerate: false,
      ratioRecommendation: false,
      ratioNotification: false,
      saveBackupRatio: false,
      backupRatioSelection: false,
    });
  }, []);

  /**
   * 批量设置模态框状态
   */
  const setModalStates = useCallback((states: Partial<RatioModalsState>) => {
    setModals(prev => ({ ...prev, ...states }));
  }, []);

  return {
    modals,
    openModal,
    closeModal,
    toggleModal,
    closeAllModals,
    setModalStates,
  };
}

/**
 * 简化的模态框状态管理Hook（用于V1经典版本）
 * 提供与 RatioPageContainer 兼容的接口
 */
export function useSimpleRatioModals(): SimpleModalState {
  const [activeModal, setActiveModal] = useState<string | null>(null);
  const [modalData, setModalDataState] = useState<Record<string, any>>({});

  const openModal = useCallback((modal: string) => {
    setActiveModal(modal);
  }, []);

  const closeModal = useCallback(() => {
    setActiveModal(null);
  }, []);

  const setModalData = useCallback((modal: string, data: any) => {
    setModalDataState(prev => ({ ...prev, [modal]: data }));
  }, []);

  return {
    activeModal,
    openModal,
    closeModal,
    modalData,
    setModalData,
  };
}
