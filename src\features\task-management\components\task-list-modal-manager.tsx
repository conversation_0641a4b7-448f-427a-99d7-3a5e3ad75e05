// src/components/sections/task-list/components/task-list-modal-manager.tsx

import type {
  ColumnTextStyle,
  CustomColumnDefinition,
  InTaskVehicleCardStyle,
  StyleableColumnId,
  Task,
  TaskGroupConfig,
  TaskListStoredSettings,
  Vehicle,
} from '@/core/types';
import { TaskCardConfig } from '@/core/types/taskCardConfig';

import { TaskProgressModal } from '@/models/task-progress-modal';
import {
  VehicleDispatchModal,
  type VehicleDispatchRecord,
  type VehicleDispatchStats,
} from '@/models/vehicle-dispatch-modal';
import { TankTruckDispatchModal } from '@/models/tank-truck-dispatch-modal';
import { RatioHistoryModal } from '@/models/RatioHistoryModal';
import { generateMockRatioHistory } from '@/infrastructure/api/mock/ratio-mock-data';
import { TaskCardConfigModal } from './cards/TaskCardConfigModal';

import { TaskGroupConfigModal } from './modals/task-group-config-modal';
import { TaskListContextMenus } from './task-list-context-menus';
import { TaskListModals } from './task-list-modals';
import { GroupByColumnConfirmDialog } from './components/group-by-column-confirm-dialog';
import { GroupByColumnSelectModal } from './components/group-by-column-select-modal';

interface TaskListModalManagerProps {
  // Tanker Note Modal
  isTankerNoteModalOpen: boolean;
  closeTankerNoteModal: () => void;
  selectedTaskForTankerNote: Task | null;

  // Column Visibility Modal
  isColumnVisibilityModalOpen: boolean;
  closeColumnVisibilityModal: () => void;
  openColumnVisibilityModal: () => void;
  allColumns: CustomColumnDefinition[];
  columnVisibility: Record<string, boolean>;
  handleColumnVisibilityChange: (columnId: string, checked: boolean) => void;
  currentOrder: string[];
  handleColumnOrderChange: (newOrder: string[]) => void;

  // Column Specific Style Modal
  isColumnSpecificStyleModalOpen: boolean;
  closeColumnSpecificStyleModal: () => void;
  editingColumnDef: CustomColumnDefinition | null;
  columnTextStyles: Record<StyleableColumnId, ColumnTextStyle | undefined>;
  columnBackgrounds: Record<string, string>;
  handleColumnTextStyleChange: (
    columnId: StyleableColumnId,
    property: keyof ColumnTextStyle,
    value: string
  ) => void;
  handleColumnBackgroundChange: (columnId: StyleableColumnId, backgroundColor: string) => void;

  // Vehicle Card Styler Modal
  isStyleEditorModalOpen: boolean;
  closeStyleEditorModal: () => void;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  updateSetting: <K extends keyof TaskListStoredSettings>(
    key: K,
    value: TaskListStoredSettings[K]
  ) => void;
  onVehiclesPerRowChange?: (vehiclesPerRow: 2 | 3 | 4 | 5 | 6 | 7 | 8) => void;

  // Group By Column Confirm Dialog
  isGroupByColumnConfirmOpen: boolean;
  groupingColumnDef: CustomColumnDefinition | null;
  closeGroupByColumnConfirm: () => void;
  confirmGroupByColumn: () => void;

  // Group By Column Select Modal
  isGroupByColumnSelectOpen: boolean;
  closeGroupByColumnSelect: () => void;
  confirmGroupByColumnSelect: (columnId: string) => void;
  currentGroupConfig: TaskGroupConfig;

  // Delivery Order Details Modal
  isDeliveryOrderDetailsModalOpen: boolean;
  closeDeliveryOrderDetailsModal: () => void;
  selectedVehicleForDeliveryOrder: Vehicle | null;
  selectedTaskForDeliveryOrder: Task | null;

  // Task Reminder Config Modal
  isReminderConfigModalOpen: boolean;
  closeReminderConfigModal: () => void;
  selectedTaskForReminderConfig: Task | null;

  // QR Code Modal
  isQRCodeModalOpen: boolean;
  closeQRCodeModal: () => void;
  selectedTaskForQRCode: Task | null;

  // Task Abbreviation Modal
  isTaskAbbreviationModalOpen: boolean;
  closeTaskAbbreviationModal: () => void;
  selectedTaskForAbbreviation: Task | null;
  handleSaveAbbreviation: (taskId: string, abbreviation: string) => Promise<void>;

  // Task Card Config Modal
  taskCardConfigModalOpen: boolean;
  setTaskCardConfigModalOpen: (open: boolean) => void;
  taskCardConfig: TaskCardConfig;
  handleTaskCardConfigChange: (config: TaskCardConfig) => void;
  handlePreviewConfigChange: (config: TaskCardConfig) => void;

  // Group Config Modal
  isGroupConfigModalOpen: boolean;
  setIsGroupConfigModalOpen: (open: boolean) => void;
  groupConfig: TaskGroupConfig;
  handleOpenGroupConfig: (config: TaskGroupConfig) => void;

  // Context Menus
  isTaskContextMenuOpen: boolean;
  taskContextMenuPosition: { x: number; y: number } | null;
  contextMenuTaskData: { taskId: string } | null;
  closeTaskContextMenu: () => void;
  openTankerNoteModal: (task: Task) => void;
  openReminderConfigModal: (task: Task) => void;
  openQRCodeModal: (task: Task) => void;
  openTaskAbbreviationModal: (task: Task) => void;
  filteredTasks: Task[];

  // 显示模式相关
  currentDisplayMode: 'table' | 'card';
  onSetDisplayMode: (mode: 'table' | 'card') => void;
  isVehicleCardContextMenuOpen: boolean;
  vehicleCardContextMenuPosition: { x: number; y: number } | null;
  vehicleCardContextMenuContext: { vehicle: Vehicle; task: Task } | null;
  closeVehicleCardContextMenu: () => void;

  // Vehicle Dispatch Modal
  isVehicleDispatchModalOpen: boolean;
  closeVehicleDispatchModal: () => void;
  selectedTaskForVehicleDispatch: Task | null;
  openVehicleDispatchModal: (task: Task) => void;

  // Task Progress Modal
  isTaskProgressModalOpen: boolean;
  closeTaskProgressModal: () => void;
  selectedTaskForProgress: Task | null;
  openTaskProgressModal: (task: Task) => void;

  // Ratio History Modal
  isRatioHistoryModalOpen: boolean;
  closeRatioHistoryModal: () => void;
  selectedTaskForRatioHistory: Task | null;
  openRatioHistoryModal: (task: Task) => void;

  // Tank Truck Dispatch Modal
  tankTruckDispatchModal: {
    isOpen: boolean;
    task: Task | null;
    vehicle: Vehicle | null;
    lineId: string | null;
    openModal: (task: Task, vehicle: Vehicle, lineId: string) => void;
    closeModal: () => void;
    handleConfirm: (dispatchData: any) => Promise<void>;
  };
}

export function TaskListModalManager({
  // Tanker Note Modal
  isTankerNoteModalOpen,
  closeTankerNoteModal,
  selectedTaskForTankerNote,

  // Column Visibility Modal
  isColumnVisibilityModalOpen,
  closeColumnVisibilityModal,
  openColumnVisibilityModal,
  allColumns,
  columnVisibility,
  handleColumnVisibilityChange,
  currentOrder,
  handleColumnOrderChange,

  // Column Specific Style Modal
  isColumnSpecificStyleModalOpen,
  closeColumnSpecificStyleModal,
  editingColumnDef,
  columnTextStyles,
  columnBackgrounds,
  handleColumnTextStyleChange,
  handleColumnBackgroundChange,

  // Vehicle Card Styler Modal
  isStyleEditorModalOpen,
  closeStyleEditorModal,
  inTaskVehicleCardStyles,
  updateSetting,
  onVehiclesPerRowChange,

  // Delivery Order Details Modal
  isDeliveryOrderDetailsModalOpen,
  closeDeliveryOrderDetailsModal,
  selectedVehicleForDeliveryOrder,
  selectedTaskForDeliveryOrder,

  // Task Reminder Config Modal
  isReminderConfigModalOpen,
  closeReminderConfigModal,
  selectedTaskForReminderConfig,

  // QR Code Modal
  isQRCodeModalOpen,
  closeQRCodeModal,
  selectedTaskForQRCode,

  // Task Abbreviation Modal
  isTaskAbbreviationModalOpen,
  closeTaskAbbreviationModal,
  selectedTaskForAbbreviation,
  handleSaveAbbreviation,

  // Task Card Config Modal
  taskCardConfigModalOpen,
  setTaskCardConfigModalOpen,
  taskCardConfig,
  handleTaskCardConfigChange,
  handlePreviewConfigChange,

  // Group Config Modal
  isGroupConfigModalOpen,
  setIsGroupConfigModalOpen,
  groupConfig,
  handleOpenGroupConfig,

  // Context Menus
  isTaskContextMenuOpen,
  taskContextMenuPosition,
  contextMenuTaskData,
  closeTaskContextMenu,
  openTankerNoteModal,
  openReminderConfigModal,
  openQRCodeModal,
  openTaskAbbreviationModal,
  filteredTasks,
  currentDisplayMode,
  onSetDisplayMode,
  isVehicleCardContextMenuOpen,
  vehicleCardContextMenuPosition,
  vehicleCardContextMenuContext,
  closeVehicleCardContextMenu,
  isVehicleDispatchModalOpen,
  closeVehicleDispatchModal,
  selectedTaskForVehicleDispatch,
  openVehicleDispatchModal,
  isTaskProgressModalOpen,
  closeTaskProgressModal,
  selectedTaskForProgress,
  openTaskProgressModal,
  isRatioHistoryModalOpen,
  closeRatioHistoryModal,
  selectedTaskForRatioHistory,
  openRatioHistoryModal,

  // Group By Column Confirm Dialog
  isGroupByColumnConfirmOpen,
  groupingColumnDef,
  closeGroupByColumnConfirm,
  confirmGroupByColumn,

  // Group By Column Select Modal
  isGroupByColumnSelectOpen,
  closeGroupByColumnSelect,
  confirmGroupByColumnSelect,
  currentGroupConfig,

  tankTruckDispatchModal,
}: TaskListModalManagerProps) {
  // 生成模拟车辆出车数据
  const generateMockVehicleDispatchData = (
    task: Task | null
  ): { records: VehicleDispatchRecord[]; stats: VehicleDispatchStats } => {
    if (!task) {
      return {
        records: [],
        stats: {
          totalVehicles: 0,
          production: 0,
          signed: 0,
          actualDelivery: 0,
          averageTime: '0小时0分钟',
        },
      };
    }

    const records: VehicleDispatchRecord[] = [
      {
        id: '1',
        vehicleNumber: '6',
        driver: '王师傅',
        cargo: '砼',
        setting: 12,
        volume: 12,
        departureTime: '2025-05-19 15:20:53',
        returnTime: '2025-04-09 16:00:51',
        duration: '次日 10:31:02',
        returnConcrete: '907小时 10分钟',
        remarks: '',
      },
      {
        id: '2',
        vehicleNumber: '15',
        driver: '郭师傅',
        cargo: '砼',
        setting: 12,
        volume: 12,
        departureTime: '2025-05-19 15:20:53',
        returnTime: '2025-04-09 16:00:51',
        duration: '次日 10:31:02',
        returnConcrete: '907小时 10分钟',
        remarks: '',
      },
    ];

    const stats: VehicleDispatchStats = {
      totalVehicles: 2,
      production: 24,
      signed: 24,
      actualDelivery: 24,
      averageTime: '907小时11分钟/趟',
    };

    return { records, stats };
  };

  const { records: vehicleDispatchRecords, stats: vehicleDispatchStats } =
    generateMockVehicleDispatchData(selectedTaskForVehicleDispatch);

  // 生成模拟任务进度数据
  const generateMockTaskProgressData = (task: Task | null): any => {
    if (!task) {
      return {
        projectName: '',
        contractNumber: '',
        firstDispatchTime: '',
        lastDispatchTime: '',
        plannedVolume: 0,
        completedVolume: 0,
        progressData: [],
        vehicleRecords: [],
      };
    }

    return {
      projectName: task.projectName || '建筑垃圾资源化综合利用项目',
      contractNumber: task.taskNumber || 'T2025040901',
      firstDispatchTime: '2025-04-09 15:59:45',
      lastDispatchTime: '2025-04-09 17:00:45',
      plannedVolume: 35,
      completedVolume: 15.1,
      progressData: [
        {
          day: '1日',
          date: '2025-04-09',
          volume: 0,
          dailyVolume: 0,
          dispatchCount: 0,
          vehicles: [],
        },
        {
          day: '1日',
          date: '2025-04-09',
          volume: 5.2,
          dailyVolume: 5.2,
          dispatchCount: 1,
          vehicles: ['京A12345'],
        },
        {
          day: '1日',
          date: '2025-04-09',
          volume: 8.7,
          dailyVolume: 3.5,
          dispatchCount: 1,
          vehicles: ['京A12346'],
        },
        {
          day: '1日',
          date: '2025-04-09',
          volume: 12.3,
          dailyVolume: 3.6,
          dispatchCount: 1,
          vehicles: ['京A12347'],
        },
        {
          day: '1日',
          date: '2025-04-09',
          volume: 15.1,
          dailyVolume: 2.8,
          dispatchCount: 1,
          vehicles: ['京A12348'],
        },
      ],
      vehicleRecords: [
        {
          id: '1',
          vehicleNumber: '京A12345',
          dispatchTime: '2025-04-09 15:59:45',
          volume: 5.2,
          day: '1日',
        },
        {
          id: '2',
          vehicleNumber: '京A12346',
          dispatchTime: '2025-04-09 16:15:30',
          volume: 3.5,
          day: '1日',
        },
        {
          id: '3',
          vehicleNumber: '京A12347',
          dispatchTime: '2025-04-09 16:30:15',
          volume: 3.6,
          day: '1日',
        },
        {
          id: '4',
          vehicleNumber: '京A12348',
          dispatchTime: '2025-04-09 16:45:00',
          volume: 2.8,
          day: '1日',
        },
      ],
    };
  };

  const taskProgressData = generateMockTaskProgressData(selectedTaskForProgress);

  return (
    <>
      {/* Main Task List Modals */}
      <TaskListModals
        updateSettingAction={updateSetting}
        isTankerNoteModalOpen={isTankerNoteModalOpen}
        closeTankerNoteModalAction={closeTankerNoteModal}
        selectedTaskForTankerNote={selectedTaskForTankerNote}
        isColumnVisibilityModalOpen={isColumnVisibilityModalOpen}
        closeColumnVisibilityModalAction={closeColumnVisibilityModal}
        allColumns={allColumns}
        columnVisibility={columnVisibility}
        handleColumnVisibilityChangeAction={handleColumnVisibilityChange}
        currentOrder={currentOrder}
        handleColumnOrderChangeAction={handleColumnOrderChange}
        isColumnSpecificStyleModalOpen={isColumnSpecificStyleModalOpen}
        closeColumnSpecificStyleModalAction={closeColumnSpecificStyleModal}
        editingColumnDef={editingColumnDef}
        columnTextStyles={columnTextStyles}
        columnBackgrounds={columnBackgrounds}
        handleColumnTextStyleChangeAction={handleColumnTextStyleChange}
        handleColumnBackgroundChangeAction={handleColumnBackgroundChange}
        isStyleEditorModalOpen={isStyleEditorModalOpen}
        closeStyleEditorModalAction={closeStyleEditorModal}
        inTaskVehicleCardStyles={inTaskVehicleCardStyles}
        updateSetting={updateSetting}
        isDeliveryOrderDetailsModalOpen={isDeliveryOrderDetailsModalOpen}
        closeDeliveryOrderDetailsModalAction={closeDeliveryOrderDetailsModal}
        selectedVehicleForDeliveryOrder={selectedVehicleForDeliveryOrder}
        selectedTaskForDeliveryOrder={selectedTaskForDeliveryOrder}
        isReminderConfigModalOpen={isReminderConfigModalOpen}
        selectedTaskForReminderConfig={selectedTaskForReminderConfig}
        closeReminderConfigModalAction={closeReminderConfigModal}
        isQRCodeModalOpen={isQRCodeModalOpen}
        selectedTaskForQRCode={selectedTaskForQRCode}
        closeQRCodeModalAction={closeQRCodeModal}
        isTaskAbbreviationModalOpen={isTaskAbbreviationModalOpen}
        selectedTaskForAbbreviation={selectedTaskForAbbreviation}
        closeTaskAbbreviationModalAction={closeTaskAbbreviationModal}
        handleSaveAbbreviationAction={handleSaveAbbreviation}
        onVehiclesPerRowChangeAction={onVehiclesPerRowChange}
      />

      {/* Task Card Config Modal */}
      <TaskCardConfigModal
        open={taskCardConfigModalOpen}
        onOpenChangeAction={setTaskCardConfigModalOpen}
        config={taskCardConfig}
        onConfigChangeAction={handleTaskCardConfigChange}
        onPreviewConfigChange={handlePreviewConfigChange}
      />

      {/* Group Config Modal */}
      <TaskGroupConfigModal
        isOpen={isGroupConfigModalOpen}
        onClose={() => setIsGroupConfigModalOpen(false)}
        groupConfig={groupConfig}
        onUpdateConfig={config => {
          // 确保config包含所有必需的TaskGroupConfig属性
          if (config.groupBy !== undefined) {
            handleOpenGroupConfig(config as TaskGroupConfig);
          }
        }}
      />

      {/* Context Menus */}
      <TaskListContextMenus
        isTaskContextMenuOpen={isTaskContextMenuOpen}
        taskContextMenuPosition={taskContextMenuPosition}
        contextMenuTaskData={contextMenuTaskData}
        closeTaskContextMenuAction={closeTaskContextMenu}
        openTankerNoteModal={openTankerNoteModal}
        openReminderConfigModal={openReminderConfigModal}
        filteredTasks={filteredTasks}
        isVehicleCardContextMenuOpen={isVehicleCardContextMenuOpen}
        vehicleCardContextMenuPosition={vehicleCardContextMenuPosition}
        vehicleCardContextMenuContext={vehicleCardContextMenuContext}
        closeVehicleCardContextMenuAction={closeVehicleCardContextMenu}
        openDeliveryOrderDetailsModalAction={function (vehicle: Vehicle, task: Task): void {
          throw new Error('Function not implemented.');
        }}
        cancelVehicleDispatchAction={function (vehicleId: string): void {
          throw new Error('Function not implemented.');
        }}
        // 第一组：任务时间和状态管理
        onOpenTimeSettingsModalAction={function (task: Task): void {
          console.log('打开时间设置模态框:', task);
        }}
        onPauseTaskAction={function (task: Task): void {
          console.log('暂停任务:', task);
        }}
        onCompleteTaskAction={function (task: Task): void {
          console.log('完成任务:', task);
        }}
        // 第二组：统计信息查看
        onOpenDispatchDetailsModalAction={openVehicleDispatchModal}
        onOpenTimeStatsModalAction={function (task: Task): void {
          console.log('打开时间统计模态框:', task);
        }}
        // 第三组：任务详情管理
        onOpenTaskDetailModalAction={function (task: Task): void {
          console.log('打开任务详情模态框:', task);
        }}
        onOpenTaskProgressModalAction={openTaskProgressModal}
        // 第四组：配比管理
        onOpenRatioDisplayModalAction={function (task: Task): void {
          console.log('打开配比显示模态框:', task);
          openRatioHistoryModal(task);
        }}
        // 第五组：界面显示设置
        onOpenColumnSettingsModalAction={openColumnVisibilityModal}
        onOpenCardConfigModalAction={function (): void {
          setTaskCardConfigModalOpen(true);
        }}
        onSetDisplayModeAction={onSetDisplayMode}
        onResetToDefaultStyleAction={function (): void {
          console.log('重置为默认样式');
        }}
        // 第六组：扩展功能
        onOpenTaskAbbreviationModalAction={openTaskAbbreviationModal}
        onPublishWeChatMessageAction={function (task: Task): void {
          console.log('发布微信消息:', task);
        }}
        onPrintQRCodeAction={openQRCodeModal}
        currentDisplayMode={currentDisplayMode}
      />

      {/* Vehicle Dispatch Modal */}
      <VehicleDispatchModal
        open={isVehicleDispatchModalOpen}
        onOpenChange={closeVehicleDispatchModal}
        task={selectedTaskForVehicleDispatch}
        dispatchRecords={vehicleDispatchRecords}
        stats={vehicleDispatchStats}
      />

      {/* Task Progress Modal */}
      <TaskProgressModal
        open={isTaskProgressModalOpen}
        onOpenChange={closeTaskProgressModal}
        task={selectedTaskForProgress}
        progressData={taskProgressData}
      />

      {/* Ratio History Modal */}
      <RatioHistoryModal
        isOpen={isRatioHistoryModalOpen}
        onOpenChangeAction={closeRatioHistoryModal}
        task={selectedTaskForRatioHistory}
        history={
          selectedTaskForRatioHistory
            ? generateMockRatioHistory(selectedTaskForRatioHistory.id)
            : []
        }
      />

      {/* Tank Truck Dispatch Modal */}
      <TankTruckDispatchModal
        open={tankTruckDispatchModal.isOpen}
        onOpenChange={open => {
          console.log('🚛 TankTruckDispatchModal onOpenChange:', open);
          tankTruckDispatchModal.closeModal();
        }}
        task={tankTruckDispatchModal.task ?? undefined}
        vehicle={tankTruckDispatchModal.vehicle ?? undefined}
        onConfirm={dispatchData => {
          console.log('🚛 TankTruckDispatchModal onConfirm called:', dispatchData);
          return tankTruckDispatchModal.handleConfirm(dispatchData);
        }}
      />

      {/* Group By Column Confirm Dialog */}
      <GroupByColumnConfirmDialog
        isOpen={isGroupByColumnConfirmOpen}
        onClose={closeGroupByColumnConfirm}
        onConfirm={confirmGroupByColumn}
        columnDef={groupingColumnDef}
      />

      {/* Group By Column Select Modal */}
      <GroupByColumnSelectModal
        isOpen={isGroupByColumnSelectOpen}
        onClose={closeGroupByColumnSelect}
        onConfirm={confirmGroupByColumnSelect}
        groupConfig={currentGroupConfig}
      />
    </>
  );
}
