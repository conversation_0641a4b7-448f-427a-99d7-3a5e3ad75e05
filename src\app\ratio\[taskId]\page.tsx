'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { RatioPageContainer } from '@/features/ratio-management/components';

/**
 * 配比页面
 * 使用重构后的架构，UI/业务/数据分离
 */
export default function RatioPage() {
  const params = useParams();
  const taskId = params?.['taskId'] as string;

  if (!taskId) {
    return (
      <div className='h-screen w-screen bg-muted/20 p-2 flex items-center justify-center'>
        <div className='text-center'>
          <div className='text-red-500 text-lg mb-2'>参数错误</div>
          <p className='text-muted-foreground'>缺少任务ID参数</p>
        </div>
      </div>
    );
  }

  return <RatioPageContainer taskId={taskId} />;
}
