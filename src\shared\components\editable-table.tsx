'use client';

import { X } from 'lucide-react';

import { Button } from '@/shared/components/button';
import { Input } from '@/shared/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/table';
import { cn } from '@/core/lib/utils';

export interface TableRowData {
  id: string;
  [key: string]: any;
}

export interface TableColumn<T extends TableRowData> {
  key: keyof T;
  header: string;
  type: 'text' | 'number' | 'select' | 'readonly' | 'percentage';
  options?: string[];
  className?: string;
}

interface EditableTableProps<T extends TableRowData> {
  columns: TableColumn<T>[];
  data: T[];
  onUpdateRowAction: (id: string, field: keyof T, value: any) => void;
  onDeleteRowAction: (id: string) => void;
}

export function EditableTable<T extends TableRowData>({
  columns,
  data,
  onUpdateRowAction,
  onDeleteRowAction,
}: EditableTableProps<T>) {
  const renderCellContent = (row: T, column: TableColumn<T>) => {
    const value = row[column.key];

    switch (column.type) {
      case 'select':
        return (
          <Select value={value} onValueChange={val => onUpdateRowAction(row.id, column.key, val)}>
            <SelectTrigger className='h-7 text-xs w-full'>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {column.options?.map(opt => (
                <SelectItem key={opt} value={opt}>
                  {opt}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      case 'number':
        return (
          <Input
            type='number'
            value={value || ''}
            onChange={e => onUpdateRowAction(row.id, column.key, parseFloat(e.target.value) || 0)}
            className='h-7 text-xs w-full text-right'
          />
        );
      case 'percentage':
        return (
          <div className='flex items-center'>
            <Input
              type='number'
              value={value || ''}
              onChange={e => onUpdateRowAction(row.id, column.key, parseFloat(e.target.value) || 0)}
              className='h-7 text-xs w-full text-right pr-4'
            />
            <span className='absolute right-2 text-xs text-muted-foreground'>%</span>
          </div>
        );
      case 'readonly':
        return (
          <span className='text-xs px-2'>
            {typeof value === 'number' ? value.toFixed(2) : value}
          </span>
        );
      default:
        return (
          <Input
            type='text'
            value={value || ''}
            onChange={e => onUpdateRowAction(row.id, column.key, e.target.value)}
            className='h-7 text-xs w-full'
          />
        );
    }
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          {columns.map(col => (
            <TableHead key={String(col.key)} className={cn('h-8', col.className)}>
              {col.header}
            </TableHead>
          ))}
          <TableHead className='w-[50px]'></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {data.map(row => (
          <TableRow key={row.id}>
            {columns.map(col => (
              <TableCell key={String(col.key)} className='p-1 relative'>
                {renderCellContent(row, col)}
              </TableCell>
            ))}
            <TableCell className='p-1'>
              <Button
                variant='ghost'
                size='icon'
                onClick={() => onDeleteRowAction(row.id)}
                className='h-7 w-7'
              >
                <X className='h-4 w-4 text-destructive' />
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
